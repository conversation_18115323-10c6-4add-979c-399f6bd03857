package util

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
)

// GenerateUUID 生成一个UUID字符串
func GenerateUUID() string {
	uuid := make([]byte, 16)
	_, err := rand.Read(uuid)
	if err != nil {
		return ""
	}
	// 设置版本 (4) 和变体 (RFC 4122)
	uuid[6] = (uuid[6] & 0x0f) | 0x40
	uuid[8] = (uuid[8] & 0x3f) | 0x80
	return fmt.Sprintf("%x-%x-%x-%x-%x", uuid[0:4], uuid[4:6], uuid[6:8], uuid[8:10], uuid[10:])
}

// GenerateShortID 生成一个短ID（用于分享ID）
func GenerateShortID(length int) string {
	if length <= 0 {
		length = 8
	}
	b := make([]byte, (length+1)/2)
	_, err := rand.Read(b)
	if err != nil {
		return ""
	}
	return hex.EncodeToString(b)[:length]
} 