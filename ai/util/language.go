package util

import (
	"bufio"
	"os"
	"unicode"
)

// JudgeLanguage 判断字符串英文字符的占比
func JudgeLanguage(s string) string {
	englishCharCount := 0
	totalCharCount := 0

	for _, c := range s {
		// 忽略非字母字符
		if unicode.IsLetter(c) {
			if isEnglishChar(c) {
				englishCharCount++
			}
			totalCharCount++
		}
	}

	// 如果没有字母字符，则无法判断，返回默认值（这里假设为"zh"）
	if totalCharCount == 0 {
		return "zh"
	}

	// 判断英文字符是否占总字符数的70%以上
	if float64(englishCharCount)/float64(totalCharCount) >= 0.7 {
		return "en"
	}

	return "zh"
}

// isEnglishChar 检查一个字符是否是英文字符
func isEnglishChar(c rune) bool {
	return (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')
}

// DetectLanguage 通过统计文本中的CJK字符和Latin字符的比例来判断文本语言类型
func DetectLanguage(s string) string {
	var (
		cjkCount   float64
		latinCount float64
	)

	for _, r := range s {
		// 统计CJK字符
		if unicode.Is(unicode.Han, r) || unicode.Is(unicode.Hiragana, r) ||
			unicode.Is(unicode.Katakana, r) || unicode.Is(unicode.Hangul, r) {
			cjkCount++
			continue
		}

		// 统计Latin字符
		if unicode.Is(unicode.Latin, r) {
			latinCount++
		}
	}

	// 如果没有有效字符，返回默认值
	if cjkCount == 0 && latinCount == 0 {
		return "zh"
	}

	// 计算CJK字符的比例
	total := cjkCount + latinCount
	cjkRatio := cjkCount / total

	// 如果CJK字符占比超过40%，判定为中文
	if cjkRatio >= 0.4 {
		return "zh"
	}

	return "en"
}

// DetectLanguageFile 从文本文件读取内容并判断语言类型
func DetectLanguageFile(path string) string {
	file, err := os.Open(path)
	if err != nil {
		return "zh"
	}
	defer file.Close()

	var (
		cjkCount   float64
		latinCount float64
	)

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		for _, r := range line {
			// 统计CJK字符
			if unicode.Is(unicode.Han, r) || unicode.Is(unicode.Hiragana, r) ||
				unicode.Is(unicode.Katakana, r) || unicode.Is(unicode.Hangul, r) {
				cjkCount++
				continue
			}

			// 统计Latin字符
			if unicode.Is(unicode.Latin, r) {
				latinCount++
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return "zh"
	}

	// 如果没有有效字符，返回默认值
	if cjkCount == 0 && latinCount == 0 {
		return "zh"
	}

	// 计算CJK字符的比例
	total := cjkCount + latinCount
	cjkRatio := cjkCount / total

	// 如果CJK字符占比超过40%，判定为中文
	if cjkRatio >= 0.4 {
		return "zh"
	}

	return "en"
}
