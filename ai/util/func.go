package util

import (
	"context"
	"math"
	"time"
)

// RunWithCancel 包装fn执行，可通过ctx取消
func RunWithCancel(ctx context.Context, fn func() error) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	c := make(chan error, 1)
	go func() {
		select {
		case c <- fn():
		case <-ctx.Done():
			return
		}
	}()
	select {
	case <-ctx.Done():
		return ctx.Err()
	case err := <-c:
		return err
	}
}

// Retry 最多重试
func Retry(fn func() error, retry int) error {
	var err error
	for i := 0; i < retry; i++ {
		if err = fn(); err == nil {
			return nil
		}
		time.Sleep(ExponentialBackoff(i))
	}
	return err
}

func ExponentialBackoff(index int) time.Duration {
	seconds := math.Pow(2, float64(index))
	return time.Duration(seconds) * time.Second
}
