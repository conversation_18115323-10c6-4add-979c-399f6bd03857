package util

// MultiplySlice 函数接受一个切片和重复次数，返回一个新的切片
// 其中原始切片的内容被重复了指定的次数。
func MultiplySlice[T any](slice []T, count int) []T {
	if count <= 0 {
		return nil
	}
	if count == 1 {
		return slice
	}
	multiplied := make([]T, len(slice)*count) // 创建一个新的切片，长度为原始切片长度乘以重复次数
	for i := 0; i < count; i++ {
		copy(multiplied[i*len(slice):(i+1)*len(slice)], slice) // 复制原始切片到新切片
	}
	return multiplied
}

// SplitIntoBatches slice分batch
func SplitIntoBatches[T any](data []T, batchSize int) [][]T {
	var batches [][]T
	for batchSize < len(data) {
		data, batches = data[batchSize:], append(batches, data[:batchSize])
	}
	return append(batches, data)
}
