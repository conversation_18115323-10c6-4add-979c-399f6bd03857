package util

import (
	"sort"

	"golang.org/x/exp/constraints"
)

// DiffSlices 比较两个切片，计算增加和减少的元素
func DiffSlices[T constraints.Ordered](oldSlice, newSlice []T) (added, removed []T) {
	oldSet := make(map[T]struct{})
	newSet := make(map[T]struct{})

	// 将 oldSlice 转换为集合
	for _, v := range oldSlice {
		oldSet[v] = struct{}{}
	}

	// 将 newSlice 转换为集合，并计算增加的元素
	for _, v := range newSlice {
		newSet[v] = struct{}{}
		if _, found := oldSet[v]; !found {
			added = append(added, v)
		}
	}

	// 计算减少的元素
	for v := range oldSet {
		if _, found := newSet[v]; !found {
			removed = append(removed, v)
		}
	}

	return added, removed
}

// IntersectSlice 计算两个切片的交集
func IntersectSlice[T constraints.Ordered](slice1, slice2 []T) []T {
	// 确保第一个切片是排序后的
	sort.Slice(slice1, func(i, j int) bool { return slice1[i] < slice1[j] })
	sort.Slice(slice2, func(i, j int) bool { return slice2[i] < slice2[j] })

	// 使用两个指针来遍历两个切片
	i, j := 0, 0
	var result []T
	for i < len(slice1) && j < len(slice2) {
		switch {
		case slice1[i] < slice2[j]:
			i++
		case slice1[i] > slice2[j]:
			j++
		default:
			result = append(result, slice1[i])
			i++
			j++
		}
	}
	return result
}
