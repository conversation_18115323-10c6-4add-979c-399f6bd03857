package util

import (
	"crypto/md5"
	"fmt"
	"io"
	"os"
)

// CalculateMD5String 计算并返回字符串的 MD5 散列值
func CalculateMD5String(s string) string {
	hasher := md5.New()
	hasher.Write([]byte(s))
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

// CalculateMD5File 计算并返回文件的 MD5 散列值
func CalculateMD5File(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hasher := md5.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hasher.Sum(nil)), nil
}
