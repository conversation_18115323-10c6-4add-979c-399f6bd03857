GO := go
TARGET := ai
WORKDIR := ./bin
SMARTDAO := smartdao

-include $(WORKDIR)/etc/.env

.PHONY: build
build:
	$(GO) mod tidy
	$(GO) build -o ${WORKDIR}/${TARGET}

.PHONY: run
run:
	make build && cd ${WORKDIR} && ./${TARGET}

.PHONY: linux
linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 make build

.PHONY: clean
clean:
	rm -rf ${WORKDIR}/${TARGET}
	$(GO) clean -i ./...

.PHONY: dao
dao:
	$(SMARTDAO) -H $(DB_V2_HOST) -p $(DB_V2_PASSWORD) -d $(DB_V2_DBNAME) -b $(table) -m "internal/model" -r "internal/repo_72c2d80c" -e e.coding.net/tencent-ssv/tanlive/services/$(TARGET) && rm -r internal/repo_72c2d80c
