#-
#  table: "table1"
#  relations:
#      -
#        belongsto: "table2"
#        name: "Product"
#        fk: "company_id"
#      -
#        hasone: "table3"
#        name: "CreditCard"
#        fk: "user_name"
#      -
#        hasmany: "table4"
#        name: "CreditCards"
#        fk: "f1"
#      -
#        many2many: "table5"
#        jointable: "table_join"
#        name: "Languages"
-
  table: "t_feedback"
  relations:
    - hasone: "t_assistant"
      name: "Assistant"
      fk: "id"
      ref: "assistant_id"