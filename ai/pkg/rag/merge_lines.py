import re
import sys


def merge_lines(text):
    if not text.strip().startswith('#'):
        text = '\n# ' + text
    if '\\n' in text:
        text = text.replace('\\n', '\n')
    #多换行变双换行，多空格变双空格
    text = re.sub(r'(\s)\1+', r'\1\1', text)
    # 清理中文字符间的空格
    chinese_with_space_pattern = r'([\u4e00-\u9fff])\s+([\u4e00-\u9fff])'
    while re.search(chinese_with_space_pattern, text):
        text = re.sub(chinese_with_space_pattern, r'\1\2', text)

    lines = text.split('\n')
    merged_lines = []
    current_line = ''
    current_prefix = None

    # 遍历每一行文本进行处理
    for line in lines:
        # 跳过空行
        if line.strip().strip('`') == '':
            continue
        # 跳过表格、图片等标题行
        caption = re.match(r'^#+\s(?:Table|Box|Figure)\s\d.*', line)
        if caption:
            continue
        # 跳过单个字母的标题行
        singleletter = re.match(r'^#+\s.$', line)
        if singleletter:
            continue
        # 检查是否以#开头的标题行
        prefix = re.match(r'^(#+)', line)
        if prefix:
            # 获取#号的数量作为标题级别
            prefix = prefix.group(1)
            # 如果与当前标题级别相同,合并内容
            if prefix == current_prefix:
                current_line += line[len(prefix):]
            # 如果标题级别不同,作为新段落
            else:
                if current_line:
                    merged_lines.append(current_line)
                current_line = line
                current_prefix = prefix
        # 非标题行的处理
        else:
            # 检测表格行，不进行合并
            if (re.match(r'^\s*\|.*\|\s*$', line) or  # Markdown表格
                    re.search(r'<\s*table|<\s*tr|<\s*td|<\s*th', line)):  # HTML表格
                if current_line:
                    merged_lines.append(current_line)
                current_line = line
                current_prefix = None
            # 如果当前行不是以标点符号结尾,则与下一行合并
            elif current_line and not re.search(r'[。！？.!?]$', current_line.strip()):
                current_line += ' ' + line
            # 否则作为新段落
            else:
                if current_line:
                    merged_lines.append(current_line)
                current_line = line
                current_prefix = None
    # 处理最后一行
    if current_line:
        merged_lines.append(current_line)
    # 合并所有行并返回
    return '\n'.join(merged_lines)


if __name__ == "__main__":
   input_file_path = '/Users/<USER>/test-big-file.txt'
   output_file_path = '/Users/<USER>/test-python.txt'
   if len(sys.argv) > 2:
        input_file_path = sys.argv[1]
        output_file_path = sys.argv[2]

   with open(input_file_path, 'r', encoding='utf-8') as input_file:
       file_content = input_file.read()

   processed_content = merge_lines(file_content)

   with open(output_file_path, 'w', encoding='utf-8') as output_file:
       output_file.write(processed_content)