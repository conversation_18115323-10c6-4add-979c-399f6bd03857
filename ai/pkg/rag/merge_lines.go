package rag

import (
	"regexp"
	"strings"
	"unicode"
	"unicode/utf8"
)

var (
	spaceTRegex           = regexp.MustCompile(`(\t){2,}`)
	spaceNRegex           = regexp.MustCompile(`(\n){2,}`)
	spaceFRegex           = regexp.MustCompile(`(\f){2,}`)
	spaceRRegex           = regexp.MustCompile(`(\r){2,}`)
	spaceRegex            = regexp.MustCompile(`([ ]){2,}`)
	chineseWithSpaceRegex = regexp.MustCompile(`([\x{4e00}-\x{9fff}])\s+([\x{4e00}-\x{9fff}])`)
	captionRegex          = regexp.MustCompile(`^#+\s(Table|Box|Figure)\s\d.*`)
	singleLetterRegex     = regexp.MustCompile(`^#+\s.$`)
	poundPrefixRegex      = regexp.MustCompile(`^#+`)
	endCharRegex          = regexp.MustCompile(`[。！？.!?]$`)
	mdTableRegex          = regexp.MustCompile(`^\s*\|.*\|\s*$`)
	mdHtmlTableRegex      = regexp.MustCompile(`<\s*table|<\s*tr|<\s*td|<\s*th`)
)

// MergeLines merge_lines函数优化版。直译版请看: MergeLines_LiteralTrans
func MergeLines(text string) string {
	if !startsWithNumberSign(text) {
		text = "\n# " + text
	}

	text = strings.ReplaceAll(text, "\\n", "\n")

	// 多换行变双换行，多空格变双空格
	// text = mergeTextSpace(text) // 实测正则更快
	text = spaceTRegex.ReplaceAllString(text, "$1$1")
	text = spaceNRegex.ReplaceAllString(text, "$1$1")
	text = spaceFRegex.ReplaceAllString(text, "$1$1")
	text = spaceRRegex.ReplaceAllString(text, "$1$1")
	text = spaceRegex.ReplaceAllString(text, "$1$1")

	// 清理中文字符间的空格
	text = clearSpacesBetweenHans(text)

	textBuilder := strings.Builder{}
	lineBuilder := strings.Builder{}

	lines := strings.Split(text, "\n")
	currentPrefix := ""

	// 遍历每一行文本进行处理
	for _, line := range lines {
		// 跳过空行
		if strings.Trim(strings.TrimSpace(line), "`") == "" {
			continue
		}

		// 跳过表格、图片等标题行
		if captionRegex.MatchString(line) {
			continue
		}

		// 跳过单个字母的标题行
		if singleLetterRegex.MatchString(line) {
			continue
		}

		// 检查是否以#开头的标题行
		if prefix := poundPrefixRegex.FindString(line); prefix != "" {
			// 获取#号的数量作为标题级别
			// 如果与当前标题级别相同,合并内容
			if prefix == currentPrefix {
				lineBuilder.WriteString(line[len(prefix):])
			} else {
				// 如果标题级别不同,作为新段落
				if lineBuilder.Len() > 0 {
					if textBuilder.Len() > 0 {
						textBuilder.WriteByte('\n')
					}
					textBuilder.WriteString(lineBuilder.String())
				}
				lineBuilder.Reset()
				lineBuilder.WriteString(line)
				currentPrefix = prefix
			}
		} else {
			// 非标题行的处理
			// 如果是表格内容，不合并
			if mdTableRegex.MatchString(line) || mdHtmlTableRegex.MatchString(line) {
				if lineBuilder.Len() > 0 {
					if textBuilder.Len() > 0 {
						textBuilder.WriteByte('\n')
					}
					textBuilder.WriteString(lineBuilder.String())
				}
				lineBuilder.Reset()
				lineBuilder.WriteString(line)
				currentPrefix = ""
			} else if lineBuilder.Len() > 0 && !endCharRegex.MatchString(strings.TrimSpace(lineBuilder.String())) {
				// 如果当前行不是以标点符号结尾,则与下一行合并
				// if lineBuilder.Len() > 0 && !strings.HasPrefix(lineBuilder.String(), "#") {
				lineBuilder.WriteByte(' ')
				lineBuilder.WriteString(line)
			} else {
				if lineBuilder.Len() > 0 {
					if textBuilder.Len() > 0 {
						textBuilder.WriteByte('\n')
					}
					textBuilder.WriteString(lineBuilder.String())
				}
				lineBuilder.Reset()
				lineBuilder.WriteString(line)
				currentPrefix = ""
			}
		}
	}

	if lineBuilder.Len() > 0 {
		if textBuilder.Len() > 0 {
			textBuilder.WriteByte('\n')
		}
		textBuilder.WriteString(lineBuilder.String())
	}

	return textBuilder.String()
}

func mergeTextSpace(text string) string {
	var (
		r              rune
		size           int
		prevSpace      rune
		prevSpaceCount int
	)

	b := strings.Builder{}
	b.Grow(len(text))

	for {
		r, size = utf8.DecodeRuneInString(text)
		if size == 0 {
			break
		}

		if r == prevSpace {
			prevSpaceCount++
		} else {
			if prevSpace > 0 {
				b.WriteRune(prevSpace)
				// 多换行变双换行，多空格变双空格
				if prevSpaceCount > 1 {
					b.WriteRune(prevSpace)
				}
			}

			if unicode.IsSpace(r) {
				prevSpace = r
				prevSpaceCount = 1
			} else {
				b.WriteRune(r)
				prevSpace = 0
				prevSpaceCount = 0
			}
		}

		text = text[size:]
	}

	return b.String()
}

func clearSpacesBetweenHans(text string) string {
	var (
		r                 rune
		size              int
		isHan             bool
		isHanBeforeSpaces bool
		spaces            []byte
	)

	b := strings.Builder{}
	b.Grow(len(text))

	for {
		r, size = utf8.DecodeRuneInString(text)
		if size == 0 {
			break
		}

		if unicode.IsSpace(r) {
			spaces = utf8.AppendRune(spaces, r)
		} else {
			isHan = 0x4e00 <= r && r <= 0x9fff
			if len(spaces) > 0 {
				if !isHan || !isHanBeforeSpaces {
					b.Write(spaces)
				}
				spaces = spaces[:0]
			}
			isHanBeforeSpaces = isHan
			b.WriteRune(r)
		}

		text = text[size:]
	}

	return b.String()
}

func startsWithNumberSign(text string) bool {
	var (
		r    rune
		size int
	)
	for {
		r, size = utf8.DecodeRuneInString(text)
		if size == 0 {
			return false
		}

		if unicode.IsSpace(r) {
			text = text[size:]
		} else {
			return r == '#'
		}
	}
}

// MergeLines_LiteralTrans python函数merge_lines直译版
func MergeLines_LiteralTrans(text string) string {
	if !strings.HasPrefix(strings.TrimSpace(text), "#") {
		text = "\n# " + text
	}

	text = strings.ReplaceAll(text, "\\n", "\n")

	// 多换行变双换行，多空格变双空格
	text = spaceTRegex.ReplaceAllString(text, "$1$1")
	text = spaceNRegex.ReplaceAllString(text, "$1$1")
	text = spaceFRegex.ReplaceAllString(text, "$1$1")
	text = spaceRRegex.ReplaceAllString(text, "$1$1")
	text = spaceRegex.ReplaceAllString(text, "$1$1")

	// 清理中文字符间的空格
	// 注意：这里和python的正则替换结果不一致，使用clearSpacesBetweenHans函数处理
	// for chineseWithSpaceRegex.MatchString(text) {
	//	text = chineseWithSpaceRegex.ReplaceAllString(text, "$1$2")
	// }
	text = clearSpacesBetweenHans(text)

	lines := strings.Split(text, "\n")
	mergedLines := make([]string, 0, len(lines))
	currentLine := ""
	currentPrefix := ""

	// 遍历每一行文本进行处理
	for _, line := range lines {
		// 跳过空行
		if strings.Trim(strings.TrimSpace(line), "`") == "" {
			continue
		}

		// 跳过表格、图片等标题行
		if captionRegex.MatchString(line) {
			continue
		}

		// 跳过单个字母的标题行
		if singleLetterRegex.MatchString(line) {
			continue
		}

		// 检查是否以#开头的标题行
		if prefix := poundPrefixRegex.FindString(line); prefix != "" {
			// 获取#号的数量作为标题级别
			// 如果与当前标题级别相同,合并内容
			if prefix == currentPrefix {
				currentLine += line[len(prefix):]
			} else {
				// 如果标题级别不同,作为新段落
				if currentLine != "" {
					mergedLines = append(mergedLines, currentLine)
				}
				currentLine = line
				currentPrefix = prefix
			}
		} else {
			// 非标题行的处理
			// 如果当前行不是以标点符号结尾,则与下一行合并
			if currentLine != "" && !endCharRegex.MatchString(strings.TrimSpace(currentLine)) {
				currentLine += " " + line
			} else {
				if currentLine != "" {
					mergedLines = append(mergedLines, currentLine)
				}
				currentLine = line
				currentPrefix = ""
			}
		}
	}

	if currentLine != "" {
		mergedLines = append(mergedLines, currentLine)
	}

	return strings.Join(mergedLines, "\n")
}
