package rag

import (
	"context"
	"encoding/json"

	"e.coding.net/tencent-ssv/tanlive/gokits/xcrypto"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag/common"
)

// NewChunkId 计算chunk_id
func NewChunkId(filename, content string) string {
	return xcrypto.Md5(filename + content)
}

// ReqGetChunksById 查询分段信息请求
type ReqGetChunksById struct {
	// 创建用户id
	UserName string `json:"user_name,omitempty"`
	// 模型名，每个用户空间内唯一
	ModelName string `json:"model_name,omitempty"`
	// 模型id，格式为“用户id-模型id”
	// 如不为空，则用户id和模型名可为空，否则两者不能为空，后台将模型名转拼音拼接实现
	// e.g. roizhao-county_agri_tech_assist
	CollectionName string `json:"collection_name,omitempty"`
	// 要查询segs的ids
	Ids []string `json:"ids"`
	// 该接口仅支持 es
	VdbType string `json:"vdb_type,omitempty"`
	// [仅支持es类型]知识库实例名，用于区分租户，默认为'ldxkujv6', sg节点为‘b2x0ejlp’
	EsIns string `json:"es_ins,omitempty"`
}

// RspGetChunksById 查询分段信息响应
type RspGetChunksById struct {
	common.BaseResponse
	Data struct {
		Data      []*ChunkInfo `json:"data"`
		Info      string       `json:"info"`
		SessionId string       `json:"session_id"`
	} `json:"data"`
}

// ChunkInfo 分段信息
type ChunkInfo struct {
	Id_      string    `json:"_id,omitempty"`
	Index_   string    `json:"_index,omitempty"`
	Content  string    `json:"content"`
	DocName  string    `json:"doc_name"`
	Id       string    `json:"id"`
	Question string    `json:"question,omitempty"`
	UserName string    `json:"user_name,omitempty"`
	Vector   []float64 `json:"vector,omitempty"`
}

// GetChunksById 查询分段信息
func (c *Client) GetChunksById(ctx context.Context, request *ReqGetChunksById) (*RspGetChunksById, error) {
	resp := &RspGetChunksById{}
	req := new(common.Request).WithContext(ctx).WithPath(getPath(c.paths.GetChunksByid, "/get_chunks_byid"))
	param, _ := json.Marshal(request)
	req.WithParams(param)
	err := c.common.Send(req, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ReqUpdateChunksById 请求
type ReqUpdateChunksById struct {
	UserName       string       `json:"user_name,omitempty"`
	ModelName      string       `json:"model_name,omitempty"`
	CollectionName string       `json:"collection_name,omitempty"`
	Chunks         []*ChunkInfo `json:"chunks"`
	VdbType        string       `json:"vdb_type,omitempty"`
	EsIns          string       `json:"es_ins,omitempty"`
}

// RspUpdateChunksById 响应
type RspUpdateChunksById struct {
	common.BaseResponse
	Data struct {
		Info      string `json:"info"`
		SessionId string `json:"session_id"`
	} `json:"data"`
}

// UpdateChunksById 更新collection中对应ids的所有chunks
func (c *Client) UpdateChunksById(ctx context.Context, request *ReqUpdateChunksById) (*RspUpdateChunksById, error) {
	resp := &RspUpdateChunksById{}
	req := new(common.Request).WithContext(ctx).WithPath(getPath(c.paths.UpdateChunksByid, "/update_chunks_byid"))
	param, _ := json.Marshal(request)
	req.WithParams(param)
	err := c.common.Send(req, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
