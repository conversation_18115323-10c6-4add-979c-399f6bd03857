package rag

import "time"

// Options 配置项
type Options struct {
	Host    string
	Schema  string
	Timeout time.Duration
	// Paths 接口路径映射
	Paths Paths
}

// Paths 接口路径
type Paths struct {
	CreateCollection         string
	CreateCollectionAsync    string
	SearchCollection         string
	CollectionUpsertProgress string
	EditCollection           string
	UpdateCollection         string
	GetCollection            string
	DeleteVec                string
	GetChunksByid            string
	UpdateChunksByid         string
	Chat                     string
	ChatStream               string
	SearchCollectionOneshot  string
}

func getPath(path, or string) string {
	if path == "" {
		return or
	}
	return path
}
