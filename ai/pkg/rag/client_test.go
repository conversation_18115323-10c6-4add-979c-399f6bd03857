package rag

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"testing"
)

var client *Client

type stdLogger struct{}

func (l *stdLogger) Printf(ctx context.Context, format string, args ...interface{}) {
	fmt.Printf(format, args...)
}

func TestMain(m *testing.M) {
	client = NewClient(&Options{
		Host:   "***************",
		Schema: "http",
	}).WithLogger(&stdLogger{})
	m.Run()
}

var (
	ctx            = context.Background()
	collectionName = "tsetsjh-api"
)

func TestClient_CreateCollection(t *testing.T) {
	collection, err := client.CreateCollection(ctx, &ReqCreateCollection{
		CollectionName: collectionName,
		Files:          []string{"/Users/<USER>/Desktop/文字文稿1.docx"},
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(collection)
}

func TestClient_SearchCollection(t *testing.T) {
	rsp, err := client.SearchCollection(ctx, &ReqSearchCollection{
		CollectionName: collectionName,
		Text:           "地球十大",
	})
	if err != nil {
		t.Fatal(err)
	}
	for _, v := range rsp.Data.Rows {
		fmt.Println(v)
	}
}

func TestClient_EditCollection(t *testing.T) {
	rsp, err := client.EditCollection(ctx, &ReqEditCollection{
		CollectionName: collectionName,
		Text:           "最好的你",
		IndexContent:   "地球十大美景",
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(rsp)
}

func TestClient_EditQ(t *testing.T) {

}

func TestClient_DeleteQA(t *testing.T) {

}

func TestClient_DeleteVec(t *testing.T) {
	_, err := client.DeleteVecById(ctx, &ReqDeleteVecById{
		CollectionName: collectionName,
		VecIds:         []string{"1"},
	})
	if err != nil {
		t.Fatal(err)
	}
}

func TestRespCreateCollection_Info_MarshalJSON(t *testing.T) {
	stringInfo := &RespCreateCollection_Info{}
	stringInfo.SetString("string info")

	chunksInfo := &RespCreateCollection_Info{}
	chunksInfo.SetChunks([][]string{{"1", "2", "3"}})

	tests := []struct {
		name string
		data any
		want []byte
	}{
		{"string", stringInfo, []byte(`"string info"`)},
		{"chunks", chunksInfo, []byte(`{"chunks":[["1","2","3"]]}`)},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if result, err := json.Marshal(tt.data); err != nil {
				t.Errorf("error = %v", err)
			} else if !bytes.Equal(result, tt.want) {
				t.Errorf("want %v, get %v", string(tt.want), string(result))
			}
		})
	}
}

func TestRespCreateCollection_Info_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		data     []byte
		wantType RespCreateCollection_InfoType
		wantData any
	}{
		{"string", []byte(`"string info"`), RespCreateCollection_InfoType_String, "string info"},
		{"chunks", []byte(`{"chunks":[["1","2","3"]]}`), RespCreateCollection_InfoType_Chunks, [][]string{{"1", "2", "3"}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			info := &RespCreateCollection_Info{}
			if err := json.Unmarshal(tt.data, info); err != nil {
				t.Errorf("error = %v", err)
			} else {
				switch tt.wantType {
				case RespCreateCollection_InfoType_String:
					if !reflect.DeepEqual(info.GetString(), tt.wantData) {
						t.Errorf("wantData %v, get %v", tt.wantData, info.GetString())
					}
				case RespCreateCollection_InfoType_Chunks:
					if !reflect.DeepEqual(info.GetChunks(), tt.wantData) {
						t.Errorf("wantData %v, get %v", tt.wantData, info.GetString())
					}
				}
			}
		})
	}
}
