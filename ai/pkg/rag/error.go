package rag

import (
	"strconv"

	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag/common"
)

const (
	// CreateCollectionStatusFailed 创建collection失败，需要重试
	CreateCollectionStatusFailed = 5000
)

// ErrCreateCollectionFailed 创建collection失败
var ErrCreateCollectionFailed = common.NewError(strconv.Itoa(CreateCollectionStatusFailed), "create collection failed, need retry")

// NewErrCreateCollectionFailed 创建 collection 失败错误
func NewErrCreateCollectionFailed(msg string) *common.Error {
	return common.NewError(strconv.Itoa(CreateCollectionStatusFailed), "create collection failed. "+msg)
}
