package common

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"math"
	"mime/multipart"
	"net"
	"net/http"
	"net/http/httputil"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"github.com/hashicorp/go-uuid"
)

type Logger interface {
	Printf(ctx context.Context, format string, args ...interface{})
}

type MonitorData struct {
	Msg string
	Url string
}

// CommonClient client封装
type CommonClient struct {
	host, schema string
	logger       Logger
	monitor      func(*MonitorData)
}

// NewCommonClient new client
func NewCommonClient(schema, host string, timeout time.Duration) *CommonClient {
	return &CommonClient{
		schema: schema,
		host:   host,
	}
}

func (c *CommonClient) WithLogger(logger Logger) *CommonClient {
	c.logger = logger
	return c
}

func (c *CommonClient) WithMonitor(monitor func(*MonitorData)) *CommonClient {
	c.monitor = monitor
	return c
}

func (c *CommonClient) log(ctx context.Context, format string, args ...interface{}) {
	if c.logger != nil {
		c.logger.Printf(ctx, format, args...)
	}
}

func ExponentialBackoff(index int) time.Duration {
	seconds := math.Pow(2, float64(index))
	return time.Duration(seconds) * time.Second
}

func drainBody(b io.ReadCloser) (r1, r2 io.ReadCloser, err error) {
	if b == nil || b == http.NoBody {
		// No copying needed. Preserve the magic sentinel meaning of NoBody.
		return http.NoBody, http.NoBody, nil
	}
	var buf bytes.Buffer
	if _, err = buf.ReadFrom(b); err != nil {
		return nil, b, err
	}
	if err = b.Close(); err != nil {
		return nil, b, err
	}
	return io.NopCloser(&buf), io.NopCloser(bytes.NewReader(buf.Bytes())), nil
}

func (c *CommonClient) Send(req *Request, rsp Response) error {
	url := c.schema + "://" + c.host + req.path

	var httpRequest *http.Request
	var requestBody io.Reader
	var headers = http.Header(make(map[string][]string))
	if req.formParam == nil {
		payload := string(req.body)
		if req.payload != "" {
			payload = req.payload
		}
		requestBody = strings.NewReader(payload)
		headers.Set("Content-Type", "application/json")
	} else {
		payload := &bytes.Buffer{}
		writer := multipart.NewWriter(payload)
		// 先处理非files字段的参数
		for k, v := range req.formParam {
			if k != "files" {
				err := addFormData(writer, k, v)
				if err != nil {
					return err
				}
			}
		}
		// 最后处理files字段
		if files, ok := req.formParam["files"]; ok {
			err := addFormData(writer, "files", files)
			if err != nil {
				return err
			}
		}
		err := writer.Close()
		if err != nil {
			return err
		}
		requestBody = payload
		headers.Set("Content-Type", writer.FormDataContentType())
		headers.Add("Accept", "*/*")
	}
	c.log(req.ctx, "RagClient Send request url %s, payload: \n%s", url, req.body)
	httpClient := &http.Client{
		Timeout: req.timeout,
	}
	var resp *http.Response
	var respDump []byte
	var reqDump []byte
	var uid string
	// 先默认重试5次
	maxRetry := 5
	if req.maxRetry > 0 {
		maxRetry = req.maxRetry
	}

	i := 0
	start := time.Now()
	var err error
	for i = 0; i < maxRetry; i++ {
		var copyed, body io.ReadCloser
		copyed, body, err = drainBody(io.NopCloser(requestBody))
		if err != nil {
			return err
		}
		requestBody = copyed
		httpRequest, err = http.NewRequest("POST", url, body)
		if err != nil {
			return err
		}
		httpRequest.Header = headers
		if req.ctx != nil {
			httpRequest = httpRequest.WithContext(req.ctx)
		}
		uid, _ = uuid.GenerateUUID()
		httpRequest.Header.Set("User-Agent", "tanlive-"+uid)
		reqDump, _ = httputil.DumpRequestOut(httpRequest, !req.logIgnoreBody)

		resp, err = httpClient.Do(httpRequest)
		if err != nil {
			var netErr net.Error
			if errors.As(err, &netErr) && netErr.Timeout() {
				c.log(req.ctx, "RagClient Send request get err:\n%s\n err:\n%s, uuid:%s", string(reqDump), err.Error(), uid)
				return err
			}
			time.Sleep(ExponentialBackoff(i))
			continue
		}
		if rsp != nil && resp != nil {
			respDump, _ = httputil.DumpResponse(resp, true)
		}
		err = parseHttpResponse(resp, rsp)

		if err != nil && strings.Contains(err.Error(), "ServerError") && (i != maxRetry-1) {
			d := ExponentialBackoff(i)
			c.log(req.ctx, fmt.Sprintf("RagClient retrying (%d/%d) in %f seconds: %s, uuid: %s", i, maxRetry, d.Seconds(), err.Error(), uid))
			time.Sleep(d)
			continue
		}
		break
	}
	elapsed := time.Since(start)
	if len(respDump) > 2500 {
		respDump = respDump[:2500]
	}
	c.log(req.ctx, "RagClient Send response:\n%s\nget response:\n%s, cost(ms):%d, uuid: %s", string(reqDump), string(respDump), elapsed.Milliseconds(), uid)
	if err != nil {
		// 重试最大次数仍失败，告警
		if c.monitor != nil && i >= maxRetry-1 {
			c.monitor(&MonitorData{
				Url: url,
				Msg: fmt.Sprintf("uuid: %s; error: %s", uid, err.Error()),
			})
		}
		c.log(req.ctx, "RagClient Send response get err:\n%s\n err:\n%s, uuid:%s", string(reqDump), err.Error(), uid)
		return err
	}

	return nil
}

func (c *CommonClient) StreamSend(req *Request) (error, io.ReadCloser) {
	url := c.schema + "://" + c.host + req.path
	start := time.Now()
	var err error
	var httpRequest *http.Request
	var requestBody io.Reader

	var uid string

	payload := string(req.body)
	if req.payload != "" {
		payload = req.payload
	}
	requestBody = strings.NewReader(payload)

	uid, _ = uuid.GenerateUUID()
	httpClient := &http.Client{}

	httpRequest, err = http.NewRequest("POST", url, requestBody)
	if err != nil {
		log.WithContext(req.ctx).Errorw("streamRequestMock err", "err", err)
		return err, nil
	}
	httpRequest.Header.Set("User-Agent", "tanlive-"+uid)
	httpRequest.Header.Set("Content-Type", "application/json")
	httpRequest.Header.Set("Cache-Control", "no-cache")
	httpRequest.Header.Set("Connection", "keep-alive")
	httpRequest.Header.Set("X-Accel-Buffering", "no")

	c.log(req.ctx, "RagClient StreamSend request url %s, payload: \n%s", url, payload)
	resp, err := httpClient.Do(httpRequest)
	if err != nil {
		log.WithContext(req.ctx).Errorw("Failed to call DeepSeek API:", "err", err)
		return err, nil
	}
	if resp.Body == nil {
		return errors.New("RagClient StreamSend get err: resp.Body is nil"), nil
	}

	// 复制 resp.Body 用于日志
	var buf bytes.Buffer
	tee := io.TeeReader(resp.Body, &buf)

	// 读取最多 1KB 的 response body
	limitedBytes := make([]byte, 1024)
	n, _ := tee.Read(limitedBytes)

	// 记录日志
	elapsed := time.Since(start)
	c.log(req.ctx, "RagClient StreamSend response url %s, response: \n%s, cost(ms):%d", url, string(limitedBytes[:n]), elapsed)

	// 创建新的 ReadCloser 传递给调用方
	resp.Body = io.NopCloser(io.MultiReader(&buf, resp.Body))

	// 异步记录请求日志
	// go c.sendLog(req, payload, start)
	return nil, resp.Body
}

func addFormData(writer *multipart.Writer, key string, value interface{}) error {
	if f, ok := value.(*os.File); ok {
		part, err := writer.CreateFormFile(key, filepath.Base(f.Name()))
		if err != nil {
			return err
		}
		_, err = io.Copy(part, f)
		if err != nil {
			return err
		}
		return nil
	}
	v, kind := ExtractType(reflect.ValueOf(value))
	switch kind {
	case reflect.Ptr, reflect.Interface, reflect.Invalid:
		return nil
	case reflect.String:
		return writer.WriteField(key, value.(string))
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return writer.WriteField(key, strconv.FormatUint(v.Uint(), 10))
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return writer.WriteField(key, strconv.FormatInt(v.Int(), 10))
	case reflect.Float32:
		return writer.WriteField(key, strconv.FormatFloat(v.Float(), 'f', -1, 32))
	case reflect.Float64:
		return writer.WriteField(key, strconv.FormatFloat(v.Float(), 'f', -1, 64))
	case reflect.Bool:
		return writer.WriteField(key, strconv.FormatBool(v.Bool()))
	case reflect.Slice, reflect.Array:
		for i := 0; i < v.Len(); i++ {
			if err := addFormData(writer, key, v.Index(i).Interface()); err != nil {
				return err
			}
		}

	default:
		panic("unsupported field value data type " + kind.String())
	}
	return nil
}

// ExtractType gets the actual underlying type of field value.
func ExtractType(current reflect.Value) (reflect.Value, reflect.Kind) {
	switch current.Kind() {
	case reflect.Ptr:
		if current.IsNil() {
			return current, reflect.Ptr
		}
		return ExtractType(current.Elem())
	case reflect.Interface:
		if current.IsNil() {
			return current, reflect.Interface
		}
		return ExtractType(current.Elem())
	default:
		return current, current.Kind()
	}
}
