package common

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"
)

var (
	ErrCodeSuccess           = 10000
	ErrCodeInvalidParam      = 90001
	ErrCodeModelNotExist     = 90010
	ErrCodeProgressGetFailed = 90009
	ErrCollectionNotExisted  = 99999
)

// 是否为成功的响应
func isSuccessResponse(code int) bool {
	return 10000 <= code && code < 20000
}

// Error 错误定义
type Error struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}

func (e *Error) Error() string {
	return fmt.Sprintf("code: %s, msg: %s", e.Code, e.Msg)
}

func (e *Error) Is(target error) bool {
	var t *Error
	ok := errors.As(target, &t)
	if !ok {
		return false
	}
	return e.Code == t.Code
}

func NewError(code string, msg string) *Error {
	return &Error{
		Code: code,
		Msg:  msg,
	}
}

// Response 这个接口抽象出从http返回数据抽取 err
type Response interface {
	ParseErrorFromHTTPResponse(body []byte) error
}

// BaseResponse 基础response
type BaseResponse struct {
}

// InfoType 自定义类型，用于处理多种可能的响应数据格式
type InfoType json.RawMessage

func (i *InfoType) UnmarshalJSON(data []byte) error {
	if i == nil {
		return errors.New("rag.InfoType: UnmarshalJSON on nil pointer")
	}
	*i = data
	return nil
}

// String 获取字符串值，如果转换失败则返回空字符串和错误
func (i InfoType) String() (string, error) {
	var result string
	err := json.Unmarshal(i, &result)
	return result, err
}

// StringOr 获取字符串值，如果转换失败则返回默认值
func (i InfoType) StringOr(defaultVal string) string {
	result, err := i.String()
	if err != nil {
		return defaultVal
	}
	return result
}

// Map 将Info解析为map
func (i InfoType) Map() (map[string]interface{}, error) {
	var result map[string]interface{}
	err := json.Unmarshal(i, &result)
	return result, err
}

// MapOr 将Info解析为map，失败则返回默认值
func (i InfoType) MapOr(defaultVal map[string]interface{}) map[string]interface{} {
	result, err := i.Map()
	if err != nil {
		return defaultVal
	}
	return result
}

// Slice 将Info解析为切片
func (i InfoType) Slice() ([]interface{}, error) {
	var result []interface{}
	err := json.Unmarshal(i, &result)
	return result, err
}

// SliceOr 将Info解析为切片，失败则返回默认值
func (i InfoType) SliceOr(defaultVal []interface{}) []interface{} {
	result, err := i.Slice()
	if err != nil {
		return defaultVal
	}
	return result
}

// Int 将Info解析为整数
func (i InfoType) Int() (int, error) {
	var result int
	err := json.Unmarshal(i, &result)
	return result, err
}

// IntOr 将Info解析为整数，失败则返回默认值
func (i InfoType) IntOr(defaultVal int) int {
	result, err := i.Int()
	if err != nil {
		return defaultVal
	}
	return result
}

// Float 将Info解析为浮点数
func (i InfoType) Float() (float64, error) {
	var result float64
	err := json.Unmarshal(i, &result)
	return result, err
}

// FloatOr 将Info解析为浮点数，失败则返回默认值
func (i InfoType) FloatOr(defaultVal float64) float64 {
	result, err := i.Float()
	if err != nil {
		return defaultVal
	}
	return result
}

// Bool 将Info解析为布尔值
func (i InfoType) Bool() (bool, error) {
	var result bool
	err := json.Unmarshal(i, &result)
	return result, err
}

// BoolOr 将Info解析为布尔值，失败则返回默认值
func (i InfoType) BoolOr(defaultVal bool) bool {
	result, err := i.Bool()
	if err != nil {
		return defaultVal
	}
	return result
}

// As 将Info解析为指定类型
func (i InfoType) As(v interface{}) error {
	return json.Unmarshal(i, v)
}

// Raw 获取原始的json.RawMessage
func (i InfoType) Raw() json.RawMessage {
	return json.RawMessage(i)
}

// ErrorResponse 如果返回结果中code判断
type ErrorResponse struct {
	Code int      `json:"code"`
	Info InfoType `json:"info"`
}

// ErrorChatResponse 如果返回结果中code判断
type ErrorChatResponse struct {
	Code int    `json:"code"`
	Info string `json:"info"`
	Gpt  string `json:"gpt"`
}

// ParseErrorFromHTTPResponse 从http响应体抽取业务错误码
func (r *BaseResponse) ParseErrorFromHTTPResponse(body []byte) (err error) {
	var raw json.RawMessage
	if err = json.Unmarshal(body, &raw); err != nil {
		msg := fmt.Sprintf("Fail to parse json content: %s, because: %s", body, err)
		return NewError("ClientError.ParseJsonError", msg)
	}

	var errorResponse ErrorResponse
	if err = json.Unmarshal(raw, &errorResponse); err == nil && errorResponse.Code > 0 && !isSuccessResponse(errorResponse.Code) && errorResponse.Code != ErrCodeModelNotExist {
		// 使用InfoType的方法获取错误信息
		errorMsg := errorResponse.Info.StringOr(string(errorResponse.Info.Raw()))
		return NewError(strconv.Itoa(errorResponse.Code), errorMsg)
	}

	var errorChatResponse ErrorChatResponse
	if err = json.Unmarshal(raw, &errorChatResponse); err == nil && errorChatResponse.Code > 0 && !isSuccessResponse(errorChatResponse.Code) && errorResponse.Code != ErrCodeModelNotExist {
		return NewError(strconv.Itoa(errorChatResponse.Code), errorChatResponse.Gpt)
	}

	return nil
}

type PlainTextResponse struct {
	Text string `json:"text"`
}

func (r *PlainTextResponse) ParseErrorFromHTTPResponse([]byte) (err error) {
	return nil
}

// parseHttpResponse 从http响应解析错误码
// 包括 1: http状态码 2: 业务返回的错误码
func parseHttpResponse(hr *http.Response, resp Response) error {
	defer hr.Body.Close()
	body, err := io.ReadAll(hr.Body)
	if err != nil {
		msg := fmt.Sprintf("Fail to read response body because %s", err)
		return NewError("ClientError.IOError", msg)
	}
	if hr.StatusCode != 200 {
		msg := fmt.Sprintf("Request fail with http status code: %s, with body: %s", hr.Status, body)
		return NewError("ServerError.HttpStatusCodeError", msg)
	}
	err = resp.ParseErrorFromHTTPResponse(body)
	if err != nil {
		return err
	}
	if textResp, ok := resp.(*PlainTextResponse); ok {
		textResp.Text = string(body)
	} else {
		err = json.Unmarshal(body, &resp)
		if err != nil {
			msg := fmt.Sprintf("Fail to parse json content: %s, because: %s", body, err)
			return NewError("ClientError.ParseJsonError", msg)
		}
	}
	return nil
}

// parseHttpResponse 从http响应解析错误码
// 包括 1: http状态码 2: 业务返回的错误码
func parseHttpStreamResponse(hr *http.Response, resp Response) error {
	defer hr.Body.Close()
	body, err := io.ReadAll(hr.Body)
	if err != nil {
		msg := fmt.Sprintf("Fail to read response body because %s", err)
		return NewError("ClientError.IOError", msg)
	}
	if hr.StatusCode != 200 {
		msg := fmt.Sprintf("Request fail with http status code: %s, with body: %s", hr.Status, body)
		return NewError("ServerError.HttpStatusCodeError", msg)
	}
	err = resp.ParseErrorFromHTTPResponse(body)
	if err != nil {
		return err
	}
	return nil
}

// Request 请求
type Request struct {
	ctx           context.Context
	path          string
	body          []byte
	payload       string
	formParam     map[string]interface{}
	timeout       time.Duration
	logIgnoreBody bool
	maxRetry      int
}

func (r *Request) WithMaxRetry(maxRetry int) *Request {
	r.maxRetry = maxRetry
	return r
}

func (r *Request) WithTimeout(timeout time.Duration) *Request {
	r.timeout = timeout
	return r
}

func (r *Request) WithParams(param []byte) *Request {
	r.body = param
	return r
}

func (r *Request) WithPayload(payload string) *Request {
	r.payload = payload
	return r
}

func (r *Request) WithFormParams(param map[string]interface{}) *Request {
	r.formParam = param
	return r
}

func (r *Request) WithContext(ctx context.Context) *Request {
	r.ctx = ctx
	return r
}

func (r *Request) WithPath(path string) *Request {
	r.path = path
	return r
}

func (r *Request) LogIgnoreBody(is bool) *Request {
	r.logIgnoreBody = is
	return r
}
