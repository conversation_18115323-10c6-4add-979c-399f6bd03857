package rag

import (
	"fmt"
	"io"
	"os"
	"os/exec"
	"testing"
	"time"
)

func TestReplaceBetweenChineseSpace(t *testing.T) {
	cases := []struct {
		text string
		want string
	}{
		{"你好 \n 好 的 啊对对对 \t啦啦啦 sss", "你好好的啊对对对啦啦啦 sss"},
		{"你 好 啊 sss", "你好啊 sss"},
	}

	for _, c := range cases {
		for chineseWithSpaceRegex.MatchString(c.text) {
			c.text = chineseWithSpaceRegex.ReplaceAllString(c.text, "$1$2")
		}
		if c.text != c.want {
			t.Errorf("ReplaceBetweenChineseSpace: actual: %s, want: %s\n", c.text, c.want)
		}
	}
}

func TestFindPoundChars(t *testing.T) {
	matches := poundPrefixRegex.FindString("##### 看啦啦啦啦#按时阿萨德啊")
	fmt.Println(matches)
}

func TestMergeLines(t *testing.T) {
	inputPath := "/Users/<USER>/test-big-file.txt"
	outputPath := "/Users/<USER>/test-go.txt"
	comparePath := "/Users/<USER>/test-python.txt"

	inputText, err := readFileToString(inputPath)
	if err != nil {
		t.Fatal(err)
	}

	start := time.Now()
	outputText := MergeLines(inputText)
	d := time.Since(start)
	fmt.Printf("merge lines took %s\n", d)

	if err = os.WriteFile(outputPath, []byte(outputText), 0644); err != nil {
		t.Fatal(err)
	}

	compareText, err := readFileToString(comparePath)
	if err != nil {
		t.Fatal(err)
	}

	if outputText != compareText {
		t.Errorf("failed!!!!\n")
	}
}

func readFileToString(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	text, err := io.ReadAll(file)
	if err != nil {
		return "", err
	}

	return string(text), nil
}

func runWithPy(text string) string {
	// 创建临时输入文件
	tmpInput, err := os.CreateTemp("", "input_*.txt")
	if err != nil {
		fmt.Printf("创建临时输入文件失败: %v\n", err)
		return ""
	}
	defer os.Remove(tmpInput.Name())
	defer tmpInput.Close()

	// 写入输入内容
	if _, err := tmpInput.WriteString(text); err != nil {
		fmt.Printf("写入临时文件失败: %v\n", err)
		return ""
	}
	tmpInput.Close()

	// 创建临时输出文件
	tmpOutput, err := os.CreateTemp("", "output_*.txt")
	if err != nil {
		fmt.Printf("创建临时输出文件失败: %v\n", err)
		return ""
	}
	defer os.Remove(tmpOutput.Name())
	tmpOutput.Close()

	// 执行Python脚本
	cmd := exec.Command("python3", "merge_lines.py", tmpInput.Name(), tmpOutput.Name())
	if output, err := cmd.CombinedOutput(); err != nil {
		fmt.Printf("执行Python脚本失败: %v, 输出: %s\n", err, string(output))
		return ""
	}

	// 读取处理后的输出内容
	result, err := readFileToString(tmpOutput.Name())
	if err != nil {
		fmt.Printf("读取处理结果失败: %v\n", err)
		return ""
	}

	return result
}

func TestMergeLinesTable(t *testing.T) {
	inputPath := "i1.txt"
	outputPath := "o.txt"

	inputText, err := readFileToString(inputPath)
	if err != nil {
		t.Fatal(err)
	}

	start := time.Now()
	outputText := MergeLines(inputText)
	d := time.Since(start)
	fmt.Printf("merge lines took %s\n", d)

	if err = os.WriteFile(outputPath, []byte(outputText), 0644); err != nil {
		t.Fatal(err)
	}

	compareText := runWithPy(inputText)

	if outputText != compareText {
		t.Errorf("failed!!!!\n")
	}
}
