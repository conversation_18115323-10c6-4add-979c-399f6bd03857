package rag

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag/common"
	"github.com/hashicorp/go-uuid"
)

type Client struct {
	common *common.CommonClient
	paths  Paths
}

func NewClient(opt *Options) *Client {
	c := common.NewCommonClient(opt.Schema, opt.Host, opt.Timeout)
	return &Client{
		common: c,
		paths:  opt.Paths,
	}
}

func (c *Client) WithLogger(logger common.Logger) *Client {
	c.common.WithLogger(logger)
	return c
}

func (c *Client) WithMonitor(m func(*common.MonitorData)) *Client {
	c.common.WithMonitor(m)
	return c
}

type ReqCreateCollection struct {
	UserName         string   `json:"user_name,omitempty"`
	ModelName        string   `json:"model_name,omitempty"`
	CollectionName   string   `json:"collection_name,omitempty"`
	Files            []string `json:"files,omitempty"`
	VdbType          string   `json:"vdb_type,omitempty"`
	Lang             string   `json:"lang,omitempty"`
	ReturnIds        bool     `json:"return_ids,omitempty"`
	EsIns            string   `json:"es_ins,omitempty"`
	ReturnChunksOnly bool     `json:"return_chunks_only,omitempty"`
	ChunkSize        int      `json:"chunk_size,omitempty"`
	ChunkOverlapSize int      `json:"chunk_overlap_size,omitempty"`
	ChunkMinSize     int      `json:"chunk_min_size,omitempty"`
	FileName         string   `json:"file_name,omitempty"`
	MergeLines       bool     `json:"merge_lines,omitempty"`
	ChunkSplitter    string   `json:"chunk_splitter,omitempty"`
	FullText         string   `json:"full_text,omitempty"`
	TextChunks       []string `json:"text_chunks,omitempty"`
}

type RespCreateCollection struct {
	common.BaseResponse
	Ids  []string `json:"ids"`
	Info string   `json:"info"`
	Data struct {
		Chunks [][]string `json:"chunks"`
	} `json:"data"`
}

// RespCreateCollection_InfoType info类型
type RespCreateCollection_InfoType int8

const (
	RespCreateCollection_InfoType_Raw RespCreateCollection_InfoType = iota
	RespCreateCollection_InfoType_String
	RespCreateCollection_InfoType_Chunks
)

// RespCreateCollection_Info info
type RespCreateCollection_Info struct {
	typ    RespCreateCollection_InfoType
	raw    json.RawMessage
	str    string
	chunks struct {
		Chunks [][]string `json:"chunks"`
	}
}

func (i *RespCreateCollection_Info) SetRaw(data json.RawMessage) {
	i.raw = data
	i.typ = RespCreateCollection_InfoType_Raw
}

func (i *RespCreateCollection_Info) SetString(text string) {
	i.str = text
	i.typ = RespCreateCollection_InfoType_String
}

func (i *RespCreateCollection_Info) SetChunks(chunks [][]string) {
	i.chunks.Chunks = chunks
	i.typ = RespCreateCollection_InfoType_Chunks
}

func (i *RespCreateCollection_Info) GetType() RespCreateCollection_InfoType {
	return i.typ
}

func (i *RespCreateCollection_Info) GetRaw() json.RawMessage {
	if i.typ == RespCreateCollection_InfoType_Raw {
		return i.raw
	}
	return nil
}

func (i *RespCreateCollection_Info) GetString() string {
	if i.typ == RespCreateCollection_InfoType_String {
		return i.str
	}
	return ""
}

func (i *RespCreateCollection_Info) GetChunks() [][]string {
	if i.typ == RespCreateCollection_InfoType_Chunks {
		return i.chunks.Chunks
	}
	return nil
}

func (i *RespCreateCollection_Info) MarshalJSON() ([]byte, error) {
	if i == nil {
		return []byte("null"), nil
	}
	switch i.typ {
	case RespCreateCollection_InfoType_String:
		return json.Marshal(i.str)
	case RespCreateCollection_InfoType_Chunks:
		return json.Marshal(i.chunks)
	default:
		return i.raw, nil
	}
}

func (i *RespCreateCollection_Info) UnmarshalJSON(data []byte) error {
	switch {
	case bytes.HasPrefix(data, []byte{'"'}):
		i.typ = RespCreateCollection_InfoType_String
		return json.Unmarshal(data, &i.str)
	case bytes.Contains(data, []byte(`"chunks"`)):
		i.typ = RespCreateCollection_InfoType_Chunks
		return json.Unmarshal(data, &i.chunks)
	default:
		i.typ = RespCreateCollection_InfoType_Raw
		i.raw = data
		return nil
	}
}

// CreateCollection 创建collection
func (c *Client) CreateCollection(ctx context.Context, request *ReqCreateCollection) (*RespCreateCollection, error) {
	resp := &RespCreateCollection{}
	req := new(common.Request).WithPath(getPath(c.paths.CreateCollection, "/create_collection"))
	formParams := make(map[string]interface{})
	if request.UserName != "" {
		formParams["user_name"] = request.UserName
	}
	if request.ModelName != "" {
		formParams["model_name"] = request.ModelName
	}
	if request.CollectionName != "" {
		formParams["collection_name"] = request.CollectionName
	}
	if request.VdbType != "" {
		formParams["vdb_type"] = request.VdbType
	}
	if request.Lang != "" {
		formParams["lang"] = request.Lang
	}
	if request.ReturnIds {
		formParams["return_ids"] = true
	}
	if request.EsIns != "" {
		formParams["es_ins"] = request.EsIns
	}
	if request.ReturnChunksOnly {
		formParams["return_chunks_only"] = true
	}
	if request.ChunkSize > 0 {
		formParams["chunk_size"] = request.ChunkSize
	}
	if request.ChunkOverlapSize > 0 {
		formParams["chunk_overlap_size"] = request.ChunkOverlapSize
	}
	if request.ChunkMinSize > 0 {
		formParams["chunk_min_size"] = request.ChunkMinSize
	}
	if request.FileName != "" {
		formParams["file_name"] = request.FileName
	}
	formParams["merge_lines"] = request.MergeLines
	if request.ChunkSplitter != "" {
		formParams["chunk_splitter"] = request.ChunkSplitter
	}
	if request.FullText != "" {
		formParams["text"] = request.FullText
	} else if len(request.TextChunks) > 0 {
		formParams["text"] = request.TextChunks
	}

	var files []*os.File
	for _, v := range request.Files {
		f, err := os.Open(v)
		if err != nil {
			return nil, err
		}
		files = append(files, f)
	}
	formParams["files"] = files
	defer func() {
		for _, f := range files {
			_ = f.Close()
		}
	}()

	req = req.WithContext(ctx).WithFormParams(formParams)
	err := c.common.Send(req, resp)
	// 有时创建成功也会返回10002代码
	cerr := new(common.Error)
	if errors.As(err, &cerr) {
		return resp, nil
	}
	return resp, err
}

type ReqSearchCollection struct {
	UserName       string  `json:"user_name,omitempty"`
	ModelName      string  `json:"model_name,omitempty"`
	CollectionName string  `json:"collection_name,omitempty"`
	Text           string  `json:"text,omitempty"`
	TopN           int     `json:"top_n,omitempty"`
	Threshold      float32 `json:"threshold,omitempty"`
	VdbType        string  `json:"vdb_type,omitempty"`
	From           int     `json:"from_,omitempty"`
	TotalHits      bool    `json:"total_hits,omitempty"`
	Lang           string  `json:"lang,omitempty"`
	EsIns          string  `json:"es_ins,omitempty"`
	TextWeight     float32 `json:"text_weight,omitempty"`
	TextRecallTopN int     `json:"text_recall_top_n,omitempty"`
	// 关键词检索es query
	TextRecallQuery string  `json:"text_recall_query,omitempty"`
	CleanChunks     bool    `json:"clean_chunks"`
	Temperature     float32 `json:"temperature,omitempty"`
	SessionId       string  `json:"session_id,omitempty"`
}

type CollectionRow struct {
	Question  string  `json:"question"`
	Content   string  `json:"content"`
	Score     float32 `json:"score"`
	DocName   string  `json:"doc_name"`
	Id        string  `json:"id"`
	Type      int     `json:"type"`
	IsRelated bool    `json:"is_related"`
}

type CollectionInfo struct {
	TotalHits int `json:"total_hits"`
}

type RespSearchCollection struct {
	common.BaseResponse
	Data struct {
		Rows []*CollectionRow `json:"chunks"`
	} `json:"data"`
	Info *CollectionInfo `json:"info,omitempty"`
}

// SearchCollection 向量搜索
func (c *Client) SearchCollection(ctx context.Context, request *ReqSearchCollection) (*RespSearchCollection, error) {
	resp := &RespSearchCollection{}
	request.TotalHits = true
	req := new(common.Request).WithPath(getPath(c.paths.SearchCollection, "/search_collection")).WithContext(ctx)
	param, _ := json.Marshal(request)
	req.WithParams(param)
	log.WithContext(ctx).Infow("search_collection request", "param", string(param))
	err := c.common.Send(req, resp)
	// TODO: 这里没搜索到相似文档会报错，但是错误码为90010，和其他错误码冲突。暂时通过错误信息判断
	if err != nil && strings.Contains(err.Error(), "No most similar text found") {
		return resp, nil
	}
	return resp, err
}

type ReqEditCollection struct {
	UserName       string  `json:"user_name,omitempty"`
	ModelName      string  `json:"model_name,omitempty"`
	CollectionName string  `json:"collection_name,omitempty"`
	Text           string  `json:"text,omitempty"`
	IndexContent   string  `json:"index_content,omitempty"`
	VdbType        string  `json:"vdb_type,omitempty"`
	Threshold      float32 `json:"threshold,omitempty"`
	DocName        string  `json:"doc_name,omitempty"`
	Lang           string  `json:"lang,omitempty"`
	EsIns          string  `json:"es_ins,omitempty"`
}

type RespEditCollection struct {
	common.BaseResponse
}

// EditCollection 编辑collection
func (c *Client) EditCollection(ctx context.Context, request *ReqEditCollection) (*RespEditCollection, error) {
	resp := &RespEditCollection{}
	req := new(common.Request).WithPath(getPath(c.paths.EditCollection, "/edit_collection")).WithContext(ctx)
	param, _ := json.Marshal(request)
	req.WithParams(param)
	return resp, c.common.Send(req, resp)
}

type ReqUpdateCollection = ReqCreateCollection
type RespUpdateCollection = RespCreateCollection

// UpdateCollection 更新collection
func (c *Client) UpdateCollection(ctx context.Context, request *ReqUpdateCollection) (*RespUpdateCollection, error) {
	resp := &RespUpdateCollection{}
	req := new(common.Request).WithPath(getPath(c.paths.UpdateCollection, "/update_collection")).WithContext(ctx)
	param, _ := json.Marshal(request)
	req.WithParams(param)
	return resp, c.common.Send(req, resp)
}

type RespChatOrSql = common.PlainTextResponse

type ReqChat struct {
	CollectionName string      `json:"collection_name,omitempty"`
	Text           interface{} `json:"text,omitempty"`
	Model          string      `json:"model,omitempty"`
	Threshold      float32     `json:"threshold,omitempty"`
	Security       bool        `json:"security"`
	PromptPrefix   string      `json:"prompt_prefix,omitempty"`
	AgentId        string      `json:"agent_id,omitempty"`
	Engine         string      `json:"engine,omitempty"`
	Stream         bool        `json:"stream"`

	// 搜索相关参数，仅在search=True时有用
	Search          bool    `json:"search,omitempty"` // 是否启用搜索增强，默认为 False，若为 True，则当在数据库中找不到相关问题的文档时，尝试通过联网的方式搜索相关资料，进而提升大模型的回答精确度
	Lang            string  `json:"lang,omitempty"`
	SearchRewrite   bool    `json:"search_rewrite,omitempty"` // 是否开启搜索增强关键词
	EsIns           string  `json:"es_ins,omitempty"`
	TextWeight      float32 `json:"text_weight,omitempty"`
	TopN            int     `json:"top_n,omitempty"`
	JudgePromptType bool    `json:"judge_prompt_type,omitempty"`
	TextRecallTopN  int     `json:"text_recall_top_n,omitempty"`
	Temperature     float32 `json:"temperature,omitempty"`
	CleanChunks     bool    `json:"clean_chunks"`
	// 聊天问题模型
	Model4chat string `json:"model4chat,omitempty"`
	// 复杂问题模型
	Model4complex string `json:"model4complex,omitempty"`
	// 关键词检索es query
	TextRecallQuery string `json:"text_recall_query,omitempty"`
	IsVision        bool   `json:"is_vision,omitempty"`
	SessionId       string `json:"session_id,omitempty"`
}

type RespChat struct {
	common.BaseResponse
	Created     int64    `json:"created"`
	ID          string   `json:"id"`
	Model       string   `json:"model"`
	Version     string   `json:"version"`
	SearchInfo  struct{} `json:"search_info"`
	Processes   struct{} `json:"processes"`
	Enhancement string   `json:"enhancement"`
	Ref         string   `json:"ref"`
	Usage       struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	Data   *RespChatData `json:"data"`
	Code   int           `json:"code"`
	Info   interface{}   `json:"info"`
	GPT    string        `json:"gpt"`
	Object string        `json:"object"`
	// 流式推送参数
	Choices []*ChatStreamChoice `json:"choices"`
	Detail  string              `json:"detail"`
}

type RespChatInfo struct {
	FinalQuery string `json:"final_query"`
}

type Response struct {
	Role             string `json:"role"`
	Content          string `json:"content"`
	ReasoningContent string `json:"reasoning_content"`
}
type RespChatData struct {
	Response         *Response `json:"response"`
	FinalQuery       string    `json:"final_query"`
	FinalSearchQuery string    `json:"final_search_query"`
	GPT              string    `json:"gpt"`
	Judge            string    `json:"judge"`
	Level            int       `json:"level"`
	Score            float64   `json:"score"`
	Label            string    `json:"label"`
	PromptType       string    `json:"prompt_type"`
}

type ChatHistoryMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

type ChatImageUrl struct {
	Url string `json:"url"`
}

type ChatVisionContent struct {
	Text     string        `json:"text,omitempty"`
	Type     string        `json:"type,omitempty"`
	ImageURL *ChatImageUrl `json:"image_url,omitempty"`
}

// ChatOrSql ...
func (c *Client) ChatOrSql(ctx context.Context, param []byte) (*RespChatOrSql, error) {
	resp := &RespChatOrSql{}
	req := new(common.Request).WithPath("/agent/tool/chat_or_sql_tanlive").WithContext(ctx)
	req.WithParams(param)
	return resp, c.common.Send(req, resp)
}

// Chat ...
func (c *Client) Chat(ctx context.Context, param []byte) (*RespChat, error) {
	resp := &RespChat{}
	req := new(common.Request).WithPath(getPath(c.paths.Chat, "/chat")).WithContext(ctx)
	req.WithParams(param).WithMaxRetry(2)
	err := c.common.Send(req, resp)
	if err != nil {
		return resp, err
	}
	if strings.Contains(resp.Detail, "模型不存在") {
		return resp, errors.New("model is not support")
	}

	return resp, nil
}

type ChatStreamChoiceDelta struct {
	Content          string `json:"content"`
	ReasoningContent string `json:"reasoning_content"`
}

type ChatStreamChoice struct {
	Delta        *ChatStreamChoiceDelta `json:"delta"`
	Message      *ChatStreamChoiceDelta `json:"message"`
	FinishReason string                 `json:"finish_reason"`
}

type RespChatStream struct {
	ChatResp *RespChat `json:"chat_resp"`
	Done     bool      `json:"done"`
	Error    error     `json:"error"`
}

// ChatStreamHttp chat_stream非流式请求
func (c *Client) ChatStreamHttp(ctx context.Context, param []byte) (*RespChat, error) {
	resp := &RespChat{}
	var req *common.Request
	if config.GetBoolOr("llm.chat.use_stream_openai", true) {
		req = new(common.Request).WithPath(getPath(c.paths.ChatStream, "/chat_stream")).WithContext(ctx)
	} else {
		req = new(common.Request).WithPath(getPath(c.paths.Chat, "/chat")).WithContext(ctx)
	}

	req.WithParams(param).WithMaxRetry(2)
	err := c.common.Send(req, resp)
	if err != nil {
		return resp, err
	}
	if strings.Contains(resp.Detail, "模型不存在") {
		return resp, errors.New("model is not support")
	}

	return resp, nil
}

func (c *Client) ChatStream(ctx context.Context, param []byte) (io.ReadCloser, error) {
	req := new(common.Request).WithPath(getPath(c.paths.ChatStream, "/chat_stream")).WithContext(ctx)
	req.WithParams(param)
	err, scanner := c.common.StreamSend(req)
	if err != nil {
		return nil, err
	}
	return scanner, nil
}

type ReqDeleteVecById struct {
	UserName       string   `json:"user_name,omitempty"`
	ModelName      string   `json:"model_name,omitempty"`
	CollectionName string   `json:"collection_name,omitempty"`
	VecIds         []string `json:"vec_ids,omitempty"`
	EsIns          string   `json:"es_ins,omitempty"`
}

type RespDeleteVecById struct {
	common.BaseResponse
}

// DeleteVecById 通过id删除向量
func (c *Client) DeleteVecById(ctx context.Context, request *ReqDeleteVecById) (*RespDeleteVecById, error) {
	resp := &RespDeleteVecById{}
	req := new(common.Request).WithPath(getPath(c.paths.DeleteVec, "/delete_vec")).WithContext(ctx)
	param, _ := json.Marshal(request)
	req.WithParams(param)
	return resp, c.common.Send(req, resp)
}

type RespSearchCollectionOneShot struct {
	common.BaseResponse
	Data struct {
		ISay        string           `json:"i_say"`
		ChunkIds    []string         `json:"chunk_ids"`
		Contents    []string         `json:"contents"`
		DocNames    interface{}      `json:"doc_names"`
		RawDocs     []*CollectionRow `json:"raw_docs"`
		RawTextDocs []*CollectionRow `json:"raw_text_docs"`
	} `json:"data"`
}

// SearchCollectionOneShot collection 快照
func (c *Client) SearchCollectionOneShot(ctx context.Context, request *ReqSearchCollection) (*RespSearchCollectionOneShot, error) {
	resp := &RespSearchCollectionOneShot{}
	req := new(common.Request).WithPath(getPath(c.paths.SearchCollectionOneshot, "/search_collection_oneshot")).WithTimeout(time.Duration(config.GetIntOr("llm.chat_timeout", 30000)) * time.Millisecond).WithContext(ctx)
	request.SessionId, _ = uuid.GenerateUUID()
	param, _ := json.Marshal(request)
	req.WithParams(param)
	// payload := strings.ReplaceAll(string(param), `\\\"`, "'")
	// req.WithPayload(payload)
	log.WithContext(ctx).Infow("search_collection_oneshot request", "param", string(param))
	err := c.common.Send(req, resp)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

type ReqCreateCollectionAsync = ReqCreateCollection

type RespCreateCollectionAsync struct {
	common.BaseResponse

	SessionId string `json:"session_id,omitempty"`
}

// CreateCollectionAsync 异步创建collection
func (c *Client) CreateCollectionAsync(ctx context.Context, request *ReqCreateCollectionAsync) (*RespCreateCollectionAsync, error) {
	// 根据请求类型分发到不同的实现
	if len(request.TextChunks) > 0 {
		// 如果有TextChunks，使用JSON方式请求
		return c.CreateCollectionAsyncWithJSON(ctx, request)
	} else {
		// 否则使用form-data方式请求
		return c.CreateCollectionAsyncWithFormData(ctx, request)
	}
}

// CreateCollectionAsyncWithJSON 使用JSON方式发送请求
func (c *Client) CreateCollectionAsyncWithJSON(ctx context.Context, request *ReqCreateCollectionAsync) (*RespCreateCollectionAsync, error) {
	resp := &RespCreateCollectionAsync{}
	req := new(common.Request).WithPath(getPath(c.paths.CreateCollectionAsync, "/create_collection_async"))

	// 将map转为JSON字符串
	// 构建form-data请求参数
	params := make(map[string]interface{})
	if request.UserName != "" {
		params["user_name"] = request.UserName
	}
	if request.ModelName != "" {
		params["model_name"] = request.ModelName
	}
	if request.CollectionName != "" {
		params["collection_name"] = request.CollectionName
	}
	if request.VdbType != "" {
		params["vdb_type"] = request.VdbType
	}
	if request.Lang != "" {
		params["lang"] = request.Lang
	}
	if request.ReturnIds {
		params["return_ids"] = true
	}
	if request.EsIns != "" {
		params["es_ins"] = request.EsIns
	}
	if request.ChunkSize > 0 {
		params["chunk_size"] = request.ChunkSize
	}
	if request.ChunkMinSize > 0 {
		params["chunk_min_size"] = request.ChunkMinSize
	}
	if request.ChunkOverlapSize > 0 {
		params["chunk_overlap_size"] = request.ChunkOverlapSize
	}
	if request.FileName != "" {
		params["file_name"] = request.FileName
	}
	if request.FullText != "" {
		params["text"] = request.FullText
	} else if len(request.TextChunks) > 0 {
		params["text"] = request.TextChunks
	}

	jsonData, _ := json.Marshal(params)
	req = req.WithContext(ctx).WithParams(jsonData)
	err := c.common.Send(req, resp)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

// CreateCollectionAsyncWithFormData 使用form-data方式发送请求
func (c *Client) CreateCollectionAsyncWithFormData(ctx context.Context, request *ReqCreateCollectionAsync) (*RespCreateCollectionAsync, error) {
	resp := &RespCreateCollectionAsync{}
	req := new(common.Request).WithPath(getPath(c.paths.CreateCollectionAsync, "/create_collection_async"))
	formParams := make(map[string]interface{})
	if request.UserName != "" {
		formParams["user_name"] = request.UserName
	}
	if request.ModelName != "" {
		formParams["model_name"] = request.ModelName
	}
	if request.CollectionName != "" {
		formParams["collection_name"] = request.CollectionName
	}
	if request.VdbType != "" {
		formParams["vdb_type"] = request.VdbType
	}
	if request.Lang != "" {
		formParams["lang"] = request.Lang
	}
	if request.ReturnIds {
		formParams["return_ids"] = true
	}
	if request.EsIns != "" {
		formParams["es_ins"] = request.EsIns
	}
	if request.ChunkSize > 0 {
		formParams["chunk_size"] = request.ChunkSize
	}
	if request.ChunkMinSize > 0 {
		formParams["chunk_min_size"] = request.ChunkMinSize
	}
	if request.ChunkOverlapSize > 0 {
		formParams["chunk_overlap_size"] = request.ChunkOverlapSize
	}
	if request.FullText != "" {
		formParams["text"] = request.FullText
	} else if len(request.TextChunks) > 0 {
		formParams["text"] = request.TextChunks
	}
	var files []*os.File
	for _, v := range request.Files {
		f, err := os.Open(v)
		if err != nil {
			return nil, err
		}
		files = append(files, f)
	}
	formParams["files"] = files
	defer func() {
		for _, f := range files {
			_ = f.Close()
		}
	}()

	req = req.WithContext(ctx).WithFormParams(formParams)
	err := c.common.Send(req, resp)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

type ReqCollectionCreateProgress struct {
	SessionId string `json:"session_id,omitempty"`
}

type RespCollectionCreateProgress struct {
	common.BaseResponse

	ProgressString string `json:"progress"`
	Progress       struct {
		CleanProgress  int     `json:"clean_progress"`
		UpsertProgress float64 `json:"upsert_progress"`
		Status         int     `json:"status"`
		ChunkCount     int     `json:"chunk_count"`
		Info           string  `json:"info"`
	}                     // 用于解析ProgressString字符串为结构体
	SegIds string `json:"seg_ids"`
}

// CollectionCreateProgress 向量创建进度
func (c *Client) CollectionCreateProgress(ctx context.Context, request *ReqCollectionCreateProgress) (*RespCollectionCreateProgress, error) {
	resp := &RespCollectionCreateProgress{}
	req := new(common.Request).WithContext(ctx).WithPath(getPath(c.paths.CollectionUpsertProgress, "/collection_upsert_progress"))
	param, _ := json.Marshal(request)
	req.WithParams(param)
	err := c.common.Send(req, resp)
	if err != nil {
		return resp, err
	}
	err = json.Unmarshal([]byte(resp.ProgressString), &resp.Progress)
	if err != nil {
		return resp, fmt.Errorf("fail to unmarshal progress response: %w", err)
	}
	if resp.Progress.Status == CreateCollectionStatusFailed {
		return resp, NewErrCreateCollectionFailed(resp.Progress.Info)
	}
	return resp, nil
}

// ReqGetCollection 请求
type ReqGetCollection struct {
	// 创建用户id
	UserName string `json:"user_name,omitempty"`
	// 模型名，每个用户空间内唯一
	ModelName string `json:"model_name,omitempty"`
	// 模型id，格式为"用户id-模型id"
	// 如不为空，则用户id和模型名可为空，否则两者不能为空，后台将模型名转拼音拼接实现
	// e.g. roizhao-county_agri_tech_assist
	CollectionName string `json:"collection_name,omitempty"`
	// 要下载的文件名
	FileName string `json:"file_name"`
	// 该接口仅支持 es
	VdbType string `json:"vdb_type,omitempty"`
	// [仅支持es类型]知识库实例名，用于区分租户，默认为'ldxkujv6', sg节点为'b2x0ejlp'
	EsIns string `json:"es_ins,omitempty"`
}

// RspGetCollection 响应
type RspGetCollection struct {
	common.BaseResponse
	SessionId string `json:"session_id"`
	Info      struct {
		Ids []string `json:"ids"`
	} `json:"info"`
	Data struct {
		Chunks []*ChunkInfo `json:"chunks"`
	} `json:"data"`
}

// GetCollection 获取collection中对应文档的所有chunks信息
func (c *Client) GetCollection(ctx context.Context, request *ReqGetCollection) (*RspGetCollection, error) {
	resp := &RspGetCollection{}
	req := new(common.Request).WithContext(ctx).WithPath(getPath(c.paths.GetCollection, "/get_collection"))
	param, _ := json.Marshal(request)
	req.WithParams(param)
	err := c.common.Send(req, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
