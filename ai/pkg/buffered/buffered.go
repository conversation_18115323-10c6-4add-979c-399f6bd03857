package buffered

import (
	"container/list"
	"context"
	"errors"
	"sync"
	"time"
)

type <PERSON>[T any] struct {
	mu        sync.Mutex
	queue     *list.List    // 缓存队列
	dataChan  chan T        // 外部写入通道
	startChan chan struct{} // 启动信号
	ctx       context.Context
	cancel    context.CancelFunc
	started   bool // 是否已启动消费
	closeOnce sync.Once
	timeout   <-chan time.Time
	OnClose   func()
}

type ChanOption[T any] func(*<PERSON>[T])

func WithStartChan[T any](ch chan struct{}) ChanOption[T] {
	return func(bc *<PERSON>[T]) {
		bc.mu.Lock()
		defer bc.mu.Unlock()
		if !bc.started {
			bc.startChan = ch
		}
	}
}

func WithTimeout[T any](timeout <-chan time.Time) ChanOption[T] {
	return func(bc *Chan[T]) {
		bc.mu.Lock()
		defer bc.mu.Unlock()
		bc.timeout = timeout
	}
}

func NewChan[T any](opts ...ChanOption[T]) *<PERSON>[T] {
	ctx, cancel := context.WithCancel(context.Background())
	bc := &Chan[T]{
		queue:    list.New(),
		dataChan: make(chan T),
		ctx:      ctx,
		cancel:   cancel,
	}
	for _, opt := range opts {
		opt(bc)
	}
	return bc
}

func (bc *Chan[T]) Send(data T) {
	bc.mu.Lock()
	defer bc.mu.Unlock()

	if !bc.started {
		bc.queue.PushBack(data) // 消费未启动时缓存
	} else {
		select {
		case bc.dataChan <- data: // 直接写入实时通道
		case <-bc.ctx.Done():

		}
	}
}

func (bc *Chan[T]) Start() {
	close(bc.startChan)
}

//func (bc *Chan[T]) SyncConsume(handler func(T)) {
//	go bc.Consume(handler)
//}1

func (bc *Chan[T]) Consume(handler func(T)) error {
	if bc.startChan != nil {
		<-bc.startChan
	}
	bc.mu.Lock()
	bc.started = true

	for elem := bc.queue.Front(); elem != nil; elem = elem.Next() {
		handler(elem.Value.(T))
	}
	bc.queue.Init()
	bc.mu.Unlock()

	closeDataChanOnce := sync.Once{}
	safeCloseDataChan := func() {
		closeDataChanOnce.Do(func() {
			close(bc.dataChan)
		})
	}

	for {
		select {
		case data, ok := <-bc.dataChan:
			if !ok {
				return nil
			}
			handler(data)
		case <-bc.ctx.Done():
			safeCloseDataChan()
		case <-bc.timeout:
			safeCloseDataChan()
			return errors.New("buffered consume timeout")
		}
	}
}

func (bc *Chan[T]) Close() {
	bc.closeOnce.Do(func() {
		bc.mu.Lock()
		defer bc.mu.Unlock()
		bc.cancel()
	})
}
