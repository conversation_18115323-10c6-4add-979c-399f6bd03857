module e.coding.net/tencent-ssv/tanlive/services/ai

go 1.23.4

replace (
	e.coding.net/tencent-ssv/tanlive/services/pkg/boot => ../pkg/boot
	e.coding.net/tencent-ssv/tanlive/services/pkg/metric => ../pkg/metric
	e.coding.net/tencent-ssv/tanlive/services/pkg/ratelimiter => ../pkg/ratelimiter
	e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud => ../pkg/tcloud
	e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/asr => ../pkg/tcloud/asr
	e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/tmt => ../pkg/tcloud/tmt
	e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos => ../pkg/tcloud/xcos
	e.coding.net/tencent-ssv/tanlive/services/pkg/tql => ../pkg/tql
	e.coding.net/tencent-ssv/tanlive/services/pkg/ugc => ../pkg/ugc
	e.coding.net/tencent-ssv/tanlive/services/pkg/validation => ../pkg/validation
	e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin => ../pkg/workweixin
	e.coding.net/tencent-ssv/tanlive/services/pkg/xcharset => ../pkg/xcharset
	e.coding.net/tencent-ssv/tanlive/services/pkg/xormhelper => ../pkg/db/xormhelper
	e.coding.net/tencent-ssv/tanlive/services/proto => ../proto
)

require (
	code.sajari.com/docconv/v2 v2.0.0-pre.4
	e.coding.net/tencent-ssv/tanlive/gokits/config v0.1.0
	e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/event v0.1.0
	e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors v0.1.2
	e.coding.net/tencent-ssv/tanlive/gokits/log v0.1.3
	e.coding.net/tencent-ssv/tanlive/gokits/xcache v0.2.1
	e.coding.net/tencent-ssv/tanlive/gokits/xcrypto v0.1.2
	e.coding.net/tencent-ssv/tanlive/gokits/xorm v0.2.5
	e.coding.net/tencent-ssv/tanlive/gokits/xredis v0.1.0
	e.coding.net/tencent-ssv/tanlive/gokits/xslice v0.0.0-20240709104857-4f9edb854f23
	e.coding.net/tencent-ssv/tanlive/gokits/xstrings v0.1.0
	e.coding.net/tencent-ssv/tanlive/gokits/xsync v0.1.2
	e.coding.net/tencent-ssv/tanlive/services/pkg/boot v0.0.0-00010101000000-000000000000
	e.coding.net/tencent-ssv/tanlive/services/pkg/db/cryptohelper v0.0.0-20250418074249-c9eba25d5fe0
	e.coding.net/tencent-ssv/tanlive/services/pkg/db/xormhelper v0.0.0-20250418074249-c9eba25d5fe0
	e.coding.net/tencent-ssv/tanlive/services/pkg/hashids v0.0.0-20240710030553-abe5bfddfbcc
	e.coding.net/tencent-ssv/tanlive/services/pkg/metric v0.0.0-20250103101119-f5012fb98708
	e.coding.net/tencent-ssv/tanlive/services/pkg/miniprogram v0.0.0-20250418074249-c9eba25d5fe0
	e.coding.net/tencent-ssv/tanlive/services/pkg/ratelimiter v0.0.0-20241210115312-f937413a3a44
	e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/asr v0.0.0-00010101000000-000000000000
	e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/tmt v0.0.0-20241024083400-6781e3aed047
	e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos v0.0.0-20250512090357-2052c1cf4d23
	e.coding.net/tencent-ssv/tanlive/services/pkg/tql v0.0.0-00010101000000-000000000000
	e.coding.net/tencent-ssv/tanlive/services/pkg/validation v0.0.0-20241209094859-15933526ac72
	e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin v0.0.0-20240710030553-abe5bfddfbcc
	e.coding.net/tencent-ssv/tanlive/services/proto v0.0.0-20250102105402-5868d4445d1e
	e.coding.net/tencent-ssv/tanlive/util v0.0.0-20250402065751-c525dcae7c12
	github.com/JohannesKaufmann/html-to-markdown v1.6.0
	github.com/asim/go-micro/v3 v3.7.1
	github.com/chinahtl/tencent-doc-sdk v1.0.3-0.20250604111408-8a293ff9a827
	github.com/go-playground/validator/v10 v10.19.0
	github.com/go-redis/redis_rate/v10 v10.0.1
	github.com/go-redsync/redsync/v4 v4.12.1
	github.com/golang/glog v1.1.2
	github.com/golang/protobuf v1.5.3
	github.com/google/uuid v1.6.0
	github.com/hashicorp/go-uuid v1.0.3
	github.com/hibiken/asynq v0.24.1
	github.com/microcosm-cc/bluemonday v1.0.5
	github.com/pdfcpu/pdfcpu v0.10.2
	github.com/pkg/errors v0.9.1
	github.com/redis/go-redis/v9 v9.7.0
	github.com/spf13/cast v1.7.0
	github.com/stretchr/testify v1.9.0
	github.com/tencentyun/cos-go-sdk-v5 v0.7.65
	go.opentelemetry.io/otel/trace v1.22.0
	golang.org/x/exp v0.0.0-20250218142911-aa4b98e5adaa
	golang.org/x/net v0.35.0
	golang.org/x/sync v0.13.0
	golang.org/x/text v0.24.0
	google.golang.org/protobuf v1.36.6
	gorm.io/datatypes v1.2.1
	gorm.io/driver/sqlite v1.5.0
	gorm.io/gorm v1.25.9
)

require (
	e.coding.net/tencent-ssv/tanlive/gokits/config/source/nacos v0.1.3 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff v0.1.2 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff/web v0.1.2 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/capi v0.1.1 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/hashids v0.1.0 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/mask v0.1.1 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/proto v0.1.3 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/translation v0.1.0 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/validator v0.1.0 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/value v0.1.0 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/xcookie v0.1.0 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/xes v0.2.0 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/xhttp v0.2.2 // indirect
	e.coding.net/tencent-ssv/tanlive/gokits/xsession v0.1.3 // indirect
	e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud v0.0.0-20241218082547-0f8c3c364364 // indirect
	e.coding.net/tencent-ssv/tanlive/services/pkg/ugc v0.0.0-00010101000000-000000000000 // indirect
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/JalfResi/justext v0.0.0-20170829062021-c0282dea7198 // indirect
	github.com/Microsoft/go-winio v0.6.0 // indirect
	github.com/ProtonMail/go-crypto v0.0.0-20210428141323-04723f9f07d7 // indirect
	github.com/PuerkitoBio/goquery v1.9.2 // indirect
	github.com/Shopify/sarama v1.36.0 // indirect
	github.com/acomagu/bufpipe v1.0.3 // indirect
	github.com/advancedlogic/GoOse v0.0.0-20191112112754-e742535969c1 // indirect
	github.com/alibabacloud-go/debug v0.0.0-20190504072949-9472017b5c68 // indirect
	github.com/alibabacloud-go/tea v1.1.17 // indirect
	github.com/alibabacloud-go/tea-utils v1.4.4 // indirect
	github.com/aliyun/alibaba-cloud-sdk-go v1.61.1800 // indirect
	github.com/aliyun/alibabacloud-dkms-gcs-go-sdk v0.2.2 // indirect
	github.com/aliyun/alibabacloud-dkms-transfer-go-sdk v0.1.7 // indirect
	github.com/andybalholm/cascadia v1.3.2 // indirect
	github.com/araddon/dateparse v0.0.0-20200409225146-d820a6159ab1 // indirect
	github.com/asim/go-micro/plugins/broker/kafka/v3 v3.7.0 // indirect
	github.com/asim/go-micro/plugins/client/grpc/v3 v3.7.0 // indirect
	github.com/asim/go-micro/plugins/registry/etcd/v3 v3.7.0 // indirect
	github.com/asim/go-micro/plugins/server/grpc/v3 v3.7.0 // indirect
	github.com/asim/go-micro/plugins/server/http/v3 v3.7.0 // indirect
	github.com/asim/go-micro/plugins/transport/grpc/v3 v3.7.0 // indirect
	github.com/aymerick/douceur v0.2.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bitly/go-simplejson v0.5.0 // indirect
	github.com/bradfitz/gomemcache v0.0.0-20220106215444-fb4bf637b56d // indirect
	github.com/buger/jsonparser v1.1.1 // indirect
	github.com/casbin/casbin/v2 v2.82.0 // indirect
	github.com/casbin/gorm-adapter/v3 v3.20.0 // indirect
	github.com/casbin/govaluate v1.1.0 // indirect
	github.com/casbin/redis-watcher/v2 v2.5.0 // indirect
	github.com/cenkalti/backoff/v4 v4.2.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/chris-ramon/douceur v0.2.0 // indirect
	github.com/clbanning/mxj v1.8.4 // indirect
	github.com/coreos/go-semver v0.3.0 // indirect
	github.com/coreos/go-systemd/v22 v22.3.2 // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.4.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/elastic/elastic-transport-go/v8 v8.5.0 // indirect
	github.com/elastic/go-elasticsearch/v8 v8.13.1 // indirect
	github.com/emirpasic/gods v1.12.0 // indirect
	github.com/fatih/set v0.2.1 // indirect
	github.com/fatih/structs v1.1.0 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/gigawattio/window v0.0.0-20180317192513-0f5467e35573 // indirect
	github.com/glebarez/go-sqlite v1.21.2 // indirect
	github.com/glebarez/sqlite v1.10.0 // indirect
	github.com/go-git/gcfg v1.5.0 // indirect
	github.com/go-git/go-billy/v5 v5.3.1 // indirect
	github.com/go-git/go-git/v5 v5.4.2 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-redis/redis/v8 v8.11.5 // indirect
	github.com/go-resty/resty/v2 v2.3.0 // indirect
	github.com/go-sql-driver/mysql v1.8.1 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-sql/civil v0.0.0-20220223132316-b832511892a9 // indirect
	github.com/golang-sql/sqlexp v0.1.0 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/go-querystring v1.0.0 // indirect
	github.com/gorilla/css v1.0.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.16.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/hhrutter/lzw v1.0.0 // indirect
	github.com/hhrutter/pkcs7 v0.2.0 // indirect
	github.com/hhrutter/tiff v1.0.2 // indirect
	github.com/imdario/mergo v0.3.12 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20231201235250-de7065d80cb9 // indirect
	github.com/jackc/pgx/v5 v5.5.5 // indirect
	github.com/jackc/puddle/v2 v2.2.1 // indirect
	github.com/jaytaylor/html2text v0.0.0-20200412013138-3577fbdbcff7 // indirect
	github.com/jbenet/go-context v0.0.0-20150711004518-d14ea06fba99 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/kevinburke/ssh_config v0.0.0-20201106050909-4977a11b4351 // indirect
	github.com/klauspost/compress v1.17.0 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/levigross/exp-html v0.0.0-20120902181939-8df60c69a8f5 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/mattn/go-sqlite3 v1.14.16 // indirect
	github.com/microsoft/go-mssqldb v0.17.0 // indirect
	github.com/miekg/dns v1.1.43 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/hashstructure v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-httpheader v0.2.1 // indirect
	github.com/nacos-group/nacos-sdk-go/v2 v2.2.5 // indirect
	github.com/nicksnyder/go-i18n/v2 v2.4.0 // indirect
	github.com/nxadm/tail v1.4.8 // indirect
	github.com/olekukonko/tablewriter v0.0.5 // indirect
	github.com/otiai10/gosseract/v2 v2.2.4 // indirect
	github.com/oxtoacart/bpool v0.0.0-20190530202638-03653db5a59c // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pelletier/go-toml/v2 v2.1.0 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_golang v1.19.1 // indirect
	github.com/prometheus/client_model v0.5.0 // indirect
	github.com/prometheus/common v0.48.0 // indirect
	github.com/prometheus/procfs v0.12.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/redis/go-redis/extra/rediscmd/v9 v9.0.5 // indirect
	github.com/redis/go-redis/extra/redisotel/v9 v9.0.5 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/richardlehane/mscfb v1.0.3 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/russross/blackfriday/v2 v2.0.1 // indirect
	github.com/sagikazarmark/locafero v0.4.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/sergi/go-diff v1.3.1 // indirect
	github.com/shurcooL/sanitized_anchor_name v1.0.0 // indirect
	github.com/silenceper/wechat/v2 v2.1.7 // indirect
	github.com/sirupsen/logrus v1.9.2 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/speps/go-hashids/v2 v2.0.1 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.18.2 // indirect
	github.com/ssor/bom v0.0.0-20170718123548-6386211fdfcf // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/asr v1.0.1031 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.1100 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/lke v1.0.1056 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ocr v1.0.930 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tmt v1.0.1073 // indirect
	github.com/tidwall/gjson v1.18.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/tidwall/sjson v1.2.5 // indirect
	github.com/urfave/cli/v2 v2.3.0 // indirect
	github.com/xanzy/ssh-agent v0.3.0 // indirect
	go.etcd.io/etcd/api/v3 v3.5.10 // indirect
	go.etcd.io/etcd/client/pkg/v3 v3.5.10 // indirect
	go.etcd.io/etcd/client/v3 v3.5.10 // indirect
	go.opentelemetry.io/otel v1.22.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.19.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.19.0 // indirect
	go.opentelemetry.io/otel/metric v1.22.0 // indirect
	go.opentelemetry.io/otel/sdk v1.21.0 // indirect
	go.opentelemetry.io/proto/otlp v1.0.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/crypto v0.37.0 // indirect
	golang.org/x/image v0.26.0 // indirect
	golang.org/x/mod v0.23.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/time v0.7.0 // indirect
	golang.org/x/tools v0.30.0 // indirect
	google.golang.org/genproto v0.0.0-20240213162025-012b6fc9bca9 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240221002015-b0ce06bbee7c // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240213162025-012b6fc9bca9 // indirect
	google.golang.org/grpc v1.61.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gopkg.in/warnings.v0 v0.1.2 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.5.6 // indirect
	gorm.io/driver/postgres v1.5.0 // indirect
	gorm.io/driver/sqlserver v1.4.1 // indirect
	gorm.io/plugin/dbresolver v1.5.0 // indirect
	gorm.io/plugin/opentelemetry v0.1.4 // indirect
	modernc.org/libc v1.29.0 // indirect
	modernc.org/mathutil v1.6.0 // indirect
	modernc.org/memory v1.7.2 // indirect
	modernc.org/sqlite v1.27.0 // indirect
	rsc.io/pdf v0.1.1 // indirect
)
