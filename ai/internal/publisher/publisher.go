package publisher

import (
	"context"
	"encoding/json"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	rp "e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
	"github.com/asim/go-micro/v3"
)

var (
	// RagTopicPublisher rag主题发布器
	RagTopicPublisher eventspb.RagTopicPublisher
)

// RegisterPublisher 注册publisher
func RegisterPublisher(svc micro.Service) {
	ragTopic := config.GetStringOr("subscriber.ragTopic", "tanlive.v2.rag")
	RagTopicPublisher = eventspb.NewRagTopicPublisher(ragTopic, svc.Client())
}

// ChatEvent ChatEvent JSON
type ChatEvent struct {
	Event   int32  `json:"event"`
	Content string `json:"content"`
	UserID  uint64 `json:"user_id"`
}

// SendAIEventSource 发送AI事件源
func SendAIEventSource(ctx context.Context, userId uint64, event int32, content string, topic string) error {
	if userId == 0 {
		return fmt.Errorf("userId is 0")
	}

	if len(content) == 0 {
		return fmt.Errorf("data is empty")
	}

	jsonData, err := json.Marshal(ChatEvent{
		Event:   event,
		Content: content,
		UserID:  userId,
	})
	if err != nil {
		return err
	}
	rp.Default.Publish(ctx, topic, string(jsonData))

	return nil
}
