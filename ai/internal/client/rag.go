package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	logger "e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag/common"
)

type RagClientLogger struct{}

func (r RagClientLogger) Printf(ctx context.Context, format string, args ...interface{}) {
	logger.WithContext(ctx).Debugf(format, args...)
}

// GetRagClient 获取增强搜索服务client
func GetRagClient() *rag.Client {
	paths := rag.Paths{}
	if err := config.Unmarshal("llm.paths", &paths); err != nil {
		panic(err)
	}

	c := rag.NewClient(&rag.Options{
		Host:   config.GetString("llm.host2"),
		Schema: config.GetStringOr("llm.schema", "http"),
		Paths:  paths,
		// Timeout: 30 * time.Second,
	})
	c.WithLogger(&RagClientLogger{}).WithMonitor((&monitor{lastAlertTime: make(map[string]time.Time)}).Monitor)
	return c
}

// GetInternalRagClient 海外服务client
func GetInternalRagClient() *rag.Client {
	paths := rag.Paths{}
	if err := config.Unmarshal("llm.paths", &paths); err != nil {
		panic(err)
	}

	c := rag.NewClient(&rag.Options{
		Host:   config.GetString("llm.host_internal"),
		Schema: config.GetStringOr("llm.schema", "http"),
		Paths:  paths,
		// Timeout: 30 * time.Second,
	})
	c.WithLogger(&RagClientLogger{}).WithMonitor((&monitor{lastAlertTime: make(map[string]time.Time)}).Monitor)
	return c
}

// GetPublicRagClient 获取增强搜索服务client（自研云）
func GetPublicRagClient() *rag.Client {
	c := rag.NewClient(&rag.Options{
		Host:   config.GetString("llm.host"),
		Schema: config.GetStringOr("llm.schema", "http"),
		// Timeout: 30 * time.Second,
	})
	c.WithLogger(&RagClientLogger{}).WithMonitor((&monitor{lastAlertTime: make(map[string]time.Time)}).Monitor)
	return c
}

type monitor struct {
	lastAlertTime map[string]time.Time // 用于记录每个URL最近一次告警的时间
	mtx           sync.Mutex
}

// Monitor 执行监控
func (m *monitor) Monitor(data *common.MonitorData) {
	if data == nil {
		return
	}
	env := config.GetStringOr("llm.env", "dev")
	duration, _ := time.ParseDuration(config.GetStringOr("llm.alert_duration", "15m"))
	// 检查是否需要告警
	alter := false
	m.mtx.Lock()
	if lastAlert, exists := m.lastAlertTime[data.Url]; !exists || time.Since(lastAlert) > duration {
		m.lastAlertTime[data.Url] = time.Now() // 更新最近告警时间
		alter = true
	}
	m.mtx.Unlock()

	if alter {
		LLMWeChatSendMsg(fmt.Sprintf("【环境：%s】AI请求接口异常，请检查，url: %s； %s", env, data.Url, data.Msg))
	}
}

// LLMWeChatSendMsg 微信发送信息
var LLMWeChatSendMsg = func(msg string) {
	key := config.GetString("llm.wechat_send_msg_key")
	url := config.GetStringOr("llm.wechat_send_msg_url", "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=")
	message := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]string{
			"content": msg,
		},
	}
	jsonData, _ := json.Marshal(message)
	req, err := http.NewRequest("POST", url+key, bytes.NewBuffer(jsonData))
	if err != nil {
		return
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	_, _ = client.Do(req)
}
