package client

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	reviewpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support"
	"github.com/asim/go-micro/v3"
)

var (
	// ReviewClient review服务
	ReviewClient  reviewpb.ReviewService
	SupportClient support.SupportService
)

// RegisterClient 注册客户端
func RegisterClient(svc micro.Service) {
	reviewService := config.GetStringOr("client.review", "tanlive.v2.review")
	ReviewClient = reviewpb.NewReviewService(reviewService, svc.Client())
	supportService := config.GetStringOr("client.support", "tanlive.v2.support")
	SupportClient = support.NewSupportService(supportService, svc.Client())
}
