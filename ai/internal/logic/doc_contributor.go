package logic

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
)

// ContributorSyncLogic 贡献者同步逻辑
type ContributorSyncLogic struct{}

// DisbandTeam 团队贡献者注销
func (l *ContributorSyncLogic) DisbandTeam(ctx context.Context, teamId uint64, teamName string) error {
	if teamId == 0 {
		return nil
	}

	db := model.NewQuery[model.TDocContributor](ctx).DB()
	err := db.Where("contributor_id = ? and contributor_type = ?", teamId, base.IdentityType_IDENTITY_TYPE_TEAM).
		Select("contributor_id", "contributor_text", "contributor_type").
		Updates(model.TDocContributor{
			ContributorID:   0,
			ContributorText: teamName,
			ContributorType: uint32(base.IdentityType_IDENTITY_TYPE_CUSTOM),
		}).Error
	return err
}

// DisbandDocContributor 注销贡献者
func DisbandDocContributor(ctx context.Context, data *eventspb.SystemDocChanged_Doc) error {
	switch data.UgcType {
	case base.DataType_DATA_TYPE_TEAM:
		// 团队贡献者
		shortName := data.UgcTitle
		if shortName != "" {
			return new(ContributorSyncLogic).DisbandTeam(ctx, data.UgcId, shortName)
		}
	}
	return nil
}

// DisbandDocContributorAsync 异步注销贡献者
func DisbandDocContributorAsync(ctx context.Context, data *eventspb.SystemDocChanged_Doc) {
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		err := DisbandDocContributor(ctx, data)
		if err != nil {
			log.WithContext(ctx).Errorf("disband doc contributor err: %v", err)
		}
		return nil
	}, boot.TraceGo(ctx))
}

// SplitDocsByEditPermission ...
func SplitDocsByEditPermission(ctx context.Context, operator *aipb.Operator,
	docId ...uint64,
) (viewable, editable []uint64, err error) {
	if len(docId) == 0 {
		return
	}
	if operator.Type == base.IdentityType_IDENTITY_TYPE_MGMT {
		return docId, docId, nil
	}

	is, err := IsDocContributorBulk(ctx, docId, operator)
	if err != nil {
		return nil, nil, err
	}
	for i, v := range is {
		if !v {
			viewable = append(viewable, docId[i])
		} else {
			editable = append(editable, docId[i])
		}
	}
	return
}

// IsDocContributorBulk 批量检查当前用户是否是 doc 的贡献值
func IsDocContributorBulk(ctx context.Context, docId []uint64, operator *aipb.Operator) ([]bool, error) {
	r := make([]bool, len(docId))
	var pbRsp aipb.RspListContributor
	err := ListContributor(ctx, &aipb.ReqListContributor{DcoId: docId}, &pbRsp)
	if err != nil {
		return nil, err
	}

	for i, id := range docId {
		cs := pbRsp.ContributorMap[id].GetContributors()
		for _, v := range cs {
			if v.Type == operator.Type && v.Id == operator.Id {
				r[i] = true
			}
		}
	}
	return r, nil
}

// ListContributor 获取贡献者筛选项
func ListContributor(ctx context.Context, req *aipb.ReqListContributor, rsp *aipb.RspListContributor) error {
	// 定义统一的结果结构体
	type ContributorWithAssistants struct {
		DocID           uint64 `gorm:"column:doc_id"`
		ContributorID   uint64 `gorm:"column:contributor_id"`
		ContributorType uint32 `gorm:"column:contributor_type"`
		ContributorText string `gorm:"column:contributor_text"`
		AssistantIDs    string `gorm:"column:assistant_ids"` // GROUP_CONCAT 结果，没有助手时为空字符串
	}

	// 构建基础查询条件
	db := model.NewQuery[model.TDocContributor](ctx).DB()
	if len(req.DcoId) != 0 {
		db.Where("t_doc_contributor.doc_id in ?", req.DcoId)
	}

	// 处理OR组条件
	orGroup := model.NewQuery[model.TDocContributor](ctx).DB()
	if req.GetOr().GetContributor() != nil {
		contributor := req.GetOr().GetContributor()
		subQ := model.NewQuery[model.TDocContributor](ctx).DB().
			Where("contributor_id = ? and contributor_type = ?", contributor.Id, contributor.Type)
		if len(contributor.Text) != 0 {
			subQ = subQ.Or("contributor_text = ? and contributor_type = ?", contributor.Text, contributor.Type)
		}
		subQ.Select("distinct(doc_id)")
		orGroup.Where("t_doc_contributor.doc_id in (?)", subQ)
	}
	if len(req.GetOr().GetScopedAssistantId()) != 0 {
		assistantIds := req.GetOr().GetScopedAssistantId()
		subQ := model.NewQuery[model.TAssistantDoc](ctx).DB().
			Where("assistant_id in ? and state in ?", assistantIds, []aipb.DocState{aipb.DocState_DOC_STATE_ENABLED, aipb.DocState_DOC_STATE_DISABLED}).Select("distinct(doc_id)")
		orGroup.Or("t_doc_contributor.doc_id in (?)", subQ)
	}
	db.Where(orGroup)

	// 处理文档类型过滤
	if req.Type != aipb.ListDocFilterType_LIST_DOC_FILTER_TYPE_UNSPECIFIED {
		subQ := model.NewQuery[model.TDoc](ctx).DB()
		switch req.Type {
		case aipb.ListDocFilterType_LIST_DOC_FILTER_TYPE_QA:
			subQ.Where("data_type = ?", aipb.DocType_DOCTYPE_QA)
		case aipb.ListDocFilterType_LIST_DOC_FILTER_TYPE_TEXTFILE:
			subQ.Where("data_type in ?", []aipb.DocType{aipb.DocType_DOCTYPE_TEXT, aipb.DocType_DOCTYPE_FILE})
			if req.DataSource != 0 {
				subQ.Where("data_source = ?", req.DataSource)
			}
		case aipb.ListDocFilterType_LIST_DOC_FILTER_TYPE_SYSTEM:
			subQ.Where("data_source = ?", aipb.DocDataSource_DOC_DATA_SOURCE_UGC)
		}
		db.Where("t_doc_contributor.doc_id in (?)", subQ.Select("distinct(id)"))
	}

	// 执行查询
	var rows []ContributorWithAssistants
	var err error

	if req.WithAssistantId {
		// 需要助手ID，使用JOIN查询和GROUP_CONCAT
		baseQuery := db.Table("t_doc_contributor").
			Joins("INNER JOIN t_assistant_doc ON t_doc_contributor.doc_id = t_assistant_doc.doc_id").
			Where("t_assistant_doc.state IN ?", []aipb.DocState{aipb.DocState_DOC_STATE_ENABLED, aipb.DocState_DOC_STATE_DISABLED})
		if len(req.GetOr().GetScopedAssistantId()) != 0 {
			baseQuery.Where("t_assistant_doc.assistant_id in ?", req.GetOr().GetScopedAssistantId())
		}

		if len(req.DcoId) == 0 {
			// 不按doc_id过滤
			err = baseQuery.Select("t_doc_contributor.contributor_id, t_doc_contributor.contributor_type, t_doc_contributor.contributor_text, GROUP_CONCAT(DISTINCT t_assistant_doc.assistant_id) as assistant_ids").
				Group("t_doc_contributor.contributor_id, t_doc_contributor.contributor_type, t_doc_contributor.contributor_text").
				Scan(&rows).Error
		} else {
			// 按doc_id过滤
			err = baseQuery.Select("t_doc_contributor.doc_id, t_doc_contributor.contributor_id, t_doc_contributor.contributor_type, t_doc_contributor.contributor_text, GROUP_CONCAT(DISTINCT t_assistant_doc.assistant_id) as assistant_ids").
				Group("t_doc_contributor.doc_id, t_doc_contributor.contributor_id, t_doc_contributor.contributor_type, t_doc_contributor.contributor_text").
				Scan(&rows).Error
		}
	} else {
		// 不需要助手ID，直接查询贡献者信息
		if len(req.DcoId) == 0 {
			// 不按doc_id过滤，返回所有唯一贡献者
			err = db.Select("contributor_id, contributor_type, contributor_text").
				Distinct("contributor_id, contributor_type, contributor_text").
				Scan(&rows).Error
		} else {
			// 按doc_id过滤，返回每个doc_id下的贡献者
			err = db.Select("doc_id, contributor_id, contributor_type, contributor_text").
				Distinct("doc_id, contributor_id, contributor_type, contributor_text").
				Scan(&rows).Error
		}
	}

	if err != nil {
		return err
	}

	// 处理查询结果
	rsp.ContributorMap = make(map[uint64]*aipb.RspListContributorSlice, len(req.DcoId))
	distinctContributor := make(map[string]*aipb.Contributor)

	for _, v := range rows {
		// 解析助手IDs字符串（如果有）
		var assistantIds []uint64
		if req.WithAssistantId && v.AssistantIDs != "" {
			assistantIDsArr := strings.Split(v.AssistantIDs, ",")
			for _, idStr := range assistantIDsArr {
				id, parseErr := strconv.ParseUint(idStr, 10, 64)
				if parseErr == nil {
					assistantIds = append(assistantIds, id)
				}
			}
		}

		// 创建贡献者对象
		c := &aipb.Contributor{
			Type:         base.IdentityType(v.ContributorType),
			Text:         v.ContributorText,
			Id:           v.ContributorID,
			AssistantIds: assistantIds,
		}

		// 处理按docID分组的结果
		if len(req.DcoId) != 0 {
			if rsp.ContributorMap[v.DocID] == nil {
				rsp.ContributorMap[v.DocID] = &aipb.RspListContributorSlice{}
			}
			rsp.ContributorMap[v.DocID].Contributors = append(rsp.ContributorMap[v.DocID].Contributors, c)
		}

		// 保存到唯一贡献者映射中
		dKey := fmt.Sprintf("%d-%d-%s", v.ContributorID, v.ContributorType, v.ContributorText)
		distinctContributor[dKey] = c
	}

	// 将distinctContributor中的贡献者添加到结果中
	for _, v := range distinctContributor {
		rsp.Contributors = append(rsp.Contributors, v)
	}

	return nil
}
