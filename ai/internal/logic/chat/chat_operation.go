package chat

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// CreateChatOperation creates a new chat operation record
func CreateChatOperation(ctx context.Context, operation *model.TChatOperation) error {
	if err := model.NewQuery[model.TChatOperation](ctx).Create(operation); err != nil {
		return err
	}
	return nil
}

// GetChatOperationByHashID retrieves a chat operation by its ID
func GetChatOperationByHashID(ctx context.Context, hashId string) (*model.TChatOperation, error) {
	var operation model.TChatOperation
	if err := model.NewQuery[model.TChatOperation](ctx).DB().Where("hash_id = ?", hashId).First(&operation).Error; err != nil {
		return nil, err
	}
	return &operation, nil
}

// GetChatOperationsByQuestionID retrieves all chat operations for a specific question
func GetChatOperationsByQuestionID(ctx context.Context, questionID uint64) ([]*model.TChatOperation, error) {
	var operations []*model.TChatOperation
	if err := model.NewQuery[model.TChatOperation](ctx).DB().Where("question_id = ?", questionID).Find(&operations).Error; err != nil {
		return nil, err
	}
	return operations, nil
}

func GetChatOperationByQuestionHashTx(tx *gorm.DB, questionID uint64, hashID string) (*model.TChatOperation, error) {
	var operation model.TChatOperation
	if err := tx.Model(&model.TChatMessage{}).Where("question_id = ? AND hash_id = ?", questionID, hashID).First(&operation).Error; err != nil {
		return nil, err
	}
	return &operation, nil
}

func GetChatStopOperation(ctx context.Context, questionID uint64, hashID string) (*model.TChatOperation, error) {
	var operation model.TChatOperation
	var stopOps = []aipb.ChatOperationType{aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_THINK, aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_TEXT}
	if err := model.NewQuery[model.TChatOperation](ctx).DB().Where("message_id = ? and hash_id = ? and operation_type = ?", questionID, hashID, stopOps).First(&operation).Error; err != nil {
		return nil, err
	}
	return &operation, nil
}

// GetChatOperationByQuestionHash retrieves a chat operation by question ID and hash ID
func GetChatOperationByQuestionHash(ctx context.Context, questionID uint64, hashID string) (*model.TChatOperation, error) {
	var operation model.TChatOperation
	if err := model.NewQuery[model.TChatOperation](ctx).DB().Where("question_id = ? and hash_id = ?", questionID, hashID).First(&operation).Error; err != nil {
		return nil, err
	}
	return &operation, nil
}

// CreateOrUpdateOperation1 creates a new chat operation if it doesn't exist
//func CreateOrUpdateOperation1(ctx context.Context, operation *model.TChatOperation) error {
//	if operation.QuestionID == 0 && operation.HashID == "" {
//		return nil
//	}
//
//	// First check if the operation exists
//	_, err := GetChatOperationByQuestionHash(ctx, operation.QuestionID, operation.HashID)
//	if err != nil {
//		// If not found, create a new one
//		return createChatOperation(ctx, operation)
//	}
//
//	// If found, update only the fields that are provided
//	updates := make(map[string]interface{})
//
//	// Only include fields that have non-zero values
//	if operation.OperationType != 0 {
//		updates[model.TChatOperationColumns.OperationType] = operation.OperationType
//	}
//	if operation.StopText != "" {
//		updates[model.TChatOperationColumns.StopText] = operation.StopText
//	}
//	if operation.StopChunkState != 0 {
//		updates[model.TChatOperationColumns.StopChunkState] = operation.StopChunkState
//	}
//	if operation.OperationParams != nil {
//		updates[model.TChatOperationColumns.OperationParams] = operation.OperationParams
//	}
//	if operation.StopThink != "" {
//		updates[model.TChatOperationColumns.StopThink] = operation.StopThink
//	}
//
//	// If no fields to update, return early
//	if len(updates) == 0 {
//		return nil
//	}
//
//	return updateOperationByQuestionHash(ctx, operation.QuestionID, operation.HashID, updates)
//}

func updateOperationByQuestionHash(ctx context.Context, questionID uint64, hashID string, updates map[string]interface{}) error {
	if err := model.NewQuery[model.TChatOperation](ctx).DB().Where("question_id = ? AND hash_id = ?", questionID, hashID).
		Updates(updates).Error; err != nil {
		return err
	}
	return nil
}
