package chat

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/buffered"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag/common"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/metric"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"github.com/hashicorp/go-uuid"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type Request[T any] struct{}

type FetchMetric struct {
	*rag.ReqChat
	Elapsed int64 `json:"elapsed"`
}

var MessageRequestValidType = []int32{int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH), int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION), int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY), int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION)}

type Strategy[T any] struct {
	timeout time.Duration
	fetch   func(ctx context.Context, payload *rag.ReqChat) (T, error)
}

type MessageRequestBuilder[T any] interface {
	initBaseBuilder(msg *ChatMessage)
	getMessage() *ChatMessage
	createPayload() (*rag.ReqChat, error)
	afterFetch(ctx context.Context, rsp T) (*ChatMessage, error)
	setDuration(start time.Time, end time.Time)
}

func FetchStrategy[T any](msgType aipb.ChatMessageType, input *rag.ReqChat, m *ChatMessage) *Strategy[T] {
	switch msgType {
	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH:
		return &Strategy[T]{
			timeout: time.Duration(config.GetIntOr("llm.chat_search_timeout", 30000)),
			fetch: func(ctx context.Context, payload *rag.ReqChat) (T, error) {
				if m.WaitAnswer { // 未开问题分类+需要等待答案
					return ragChatFetch[T](ctx, payload)
				}
				return ragChatStreamFetch[T](ctx, payload, m)
			},
		}
	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION:
		return &Strategy[T]{
			timeout: time.Duration(config.GetIntOr("llm.chat_collection_timeout", 30000)),
			fetch: func(ctx context.Context, payload *rag.ReqChat) (T, error) {
				if m.WaitAnswer { // 未开问题分类+需要等待答案
					return ragChatFetch[T](ctx, payload)
				}
				return ragChatStreamFetch[T](ctx, payload, m)
			},
		}
	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION:
		return &Strategy[T]{
			timeout: time.Duration(config.GetIntOr("llm.chat_vision_timeout", 30000)),
			fetch: func(ctx context.Context, payload *rag.ReqChat) (T, error) {
				if m.WaitAnswer { // 需要等待答案
					return ragChatFetch[T](ctx, payload)
				}
				return ragChatStreamFetch[T](ctx, payload, m)
			},
		}
	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY:
		return &Strategy[T]{
			timeout: time.Duration(config.GetIntOr("llm.chat_sql_query_timeout", 8000)),
			fetch: func(ctx context.Context, payload *rag.ReqChat) (T, error) {
				return ragChatSqlFetch[T](ctx, payload)
			},
		}
	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SUGGESTION:
		return &Strategy[T]{
			timeout: time.Duration(config.GetIntOr("llm.chat_suggestion_timeout", 30000)),
			fetch: func(ctx context.Context, payload *rag.ReqChat) (T, error) {
				return ragChatFetch[T](ctx, payload)
			},
		}
	default:
		return &Strategy[T]{
			timeout: time.Duration(config.GetIntOr("llm.chat_default_timeout", 30000)),
			fetch: func(ctx context.Context, payload *rag.ReqChat) (T, error) {
				return ragChatStreamFetch[T](ctx, payload, m)
			},
		}
	}
}

var metricType = map[aipb.ChatMessageType]string{
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH:     "AIApiChatSearch",
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION: "AIApiChatDoc",
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION:     "AIApiChatVision",
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY:  "AIApiChatSql",
}

func handleStreamResponse(ctx context.Context, body io.ReadCloser) (*buffered.Chan[*rag.RespChatStream], error) {
	rspChan := buffered.NewChan[*rag.RespChatStream]()
	getJson := func(line string) string {
		line = strings.ReplaceAll(line, "\n", "")
		var jsonStr string
		if strings.HasPrefix(line, "data: ") {
			jsonStr = strings.TrimPrefix(line, "data: ")
		} else if strings.HasPrefix(line, "data:") {
			jsonStr = strings.TrimPrefix(line, "data:")
		} else {
			jsonStr = line
		}
		return jsonStr
	}

	var wg sync.WaitGroup
	var firstErrOnce sync.Once
	wg.Add(1)
	firstErrChan := make(chan error, 1) // 专用首次错误通道

	// 安全发送错误到通道的函数
	safeSendError := func(err error) {
		firstErrOnce.Do(func() {
			firstErrChan <- err
		})
	}

	go func() {
		logCount := 0
		var line string
		defer func() {
			log.WithContext(ctx).Infow("StreamResponse defer finished and receive last chunk", "chunk", line)
			log.WithContext(ctx).Infow("StreamResponse scanner.Scan() Received [DONE], closing stream")
			rspChan.Send(&rag.RespChatStream{
				Done: true,
			})
			body.Close()
			rspChan.Close()
		}()
		scanner := bufio.NewScanner(body)
		scanner.Buffer(make([]byte, 1024*1024), 1024*1024)

		var firstBlockProcessed bool
		for scanner.Scan() {
			var resSlice = &rag.RespChatStream{}
			line = scanner.Text()
			if line == "" || strings.TrimSpace(line) == "" {
				continue
			}

			if strings.Contains(line, "[DONE]") {
				log.WithContext(ctx).Infow("StreamResponse scanner.Scan() Received [DONE], closing stream")
				return
			}

			if logCount < 10 {
				log.WithContext(ctx).Infow("scanner.Scan() receive chunk", "chunk", fmt.Sprintf("line: %s", line))
				logCount++
			}

			// 解析chunk
			jsonStr := getJson(line)
			var choicesRes *rag.RespChat
			if err := json.Unmarshal([]byte(jsonStr), &choicesRes); err == nil {
				if !firstBlockProcessed {
					firstBlockProcessed = true
					wg.Done() // 通知主函数首次检查完成
				}
				resSlice.ChatResp = choicesRes
				rspChan.Send(resSlice)
			} else {
				log.WithContext(ctx).Errorw("scanner.Scan() receive err chunk", "chunk", fmt.Sprintf("line: %s", line))
				safeSendError(err)
				return
			}
		}

		if err := scanner.Err(); err != nil {
			log.WithContext(ctx).Errorw("StreamResponse scanner encountered an error", "error", err)
			rspChan.Send(&rag.RespChatStream{Error: err})
			safeSendError(err)
		}
	}()

	// 等待首次检查完成或错误
	select {
	case <-ctx.Done():
		log.WithContext(ctx).Infow("StreamResponse context done", "error", ctx.Err())
		return nil, ctx.Err()
	case firstErr := <-firstErrChan:
		log.WithContext(ctx).Infow("StreamResponse receive firstErr", "error", firstErr)
		return nil, errors.New("scanner.Scan() receive err")
	case <-waitErrCheck(&wg):
		log.WithContext(ctx).Infow("StreamResponse waitErrCheck finished, return rspChan")
		return rspChan, nil
	}
}

func waitErrCheck(wg *sync.WaitGroup) <-chan struct{} {
	ch := make(chan struct{})
	go func() {
		wg.Wait()
		close(ch)
	}()
	return ch
}

// 全局的限流器，防止出现并发
var (
	chatLimiter = struct {
		sync.Mutex
		lastRequestTime time.Time
	}{}
)

func waitForRateLimit() {
	chatLimiter.Lock()
	defer chatLimiter.Unlock()

	elapsed := time.Since(chatLimiter.lastRequestTime)
	if elapsed < 100*time.Millisecond {
		// 如果距离上次请求时间小于100ms，则等待
		time.Sleep(100*time.Millisecond - elapsed)
	}
	chatLimiter.lastRequestTime = time.Now()
}

func ragClient(payload rag.ReqChat) *rag.Client {
	internalModels := config.GetStringSlice("llm.chat.internal_models")
	if slices.Contains(internalModels, payload.Model) || slices.Contains(internalModels, payload.Model4complex) || slices.Contains(internalModels, payload.Model4chat) {
		return client.GetInternalRagClient()
	} else {
		return client.GetRagClient()
	}
}

func ragChatFetch[T any](ctx context.Context, payload *rag.ReqChat) (T, error) {
	waitForRateLimit()
	var zero T
	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), time.Duration(config.GetIntOr("llm.ai_fetch_max_timeout", 60000))*time.Millisecond)
	defer func() {
		cancel()
		payload.Stream = false
	}()

	payloadCopy := *payload
	payloadCopy.Stream = false
	payloadCopy.SessionId, _ = uuid.GenerateUUID()
	req, err := json.Marshal(payloadCopy)
	if err != nil {
		return zero, err
	}

	var resultChan = make(chan T, 1)
	var errChan = make(chan error, 1)

	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		if result, err := ragClient(payloadCopy).ChatStreamHttp(ctx, req); err != nil {
			errChan <- err
		} else {
			log.WithContext(ctx).Infow("ragChatFetch receive result", "result", result)
			if res, ok := any(result).(T); ok {
				resultChan <- res
			} else {
				errChan <- errors.New("ragChatFetch chat response type error")
			}
		}
		return nil
	}, boot.TraceGo(ctx))

	select {
	case <-ctxWithTimeout.Done():
		return zero, errors.New("ragChatFetch chat response timeout")
	case err := <-errChan:
		return zero, err
	case result := <-resultChan:
		if res, ok := any(result).(T); ok {
			return res, err
		}
		return zero, errors.New("fail to get rag chat stream fetch")
	}
}

func ragChatFetch2[T any](ctx context.Context, payload *rag.ReqChat) (T, error) {
	waitForRateLimit()
	var zero T
	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), time.Duration(config.GetIntOr("llm.ai_fetch_max_timeout", 60000))*time.Millisecond)
	defer func() {
		cancel()
		payload.Stream = false
	}()

	payloadCopy := *payload
	payloadCopy.Stream = false
	payloadCopy.SessionId, _ = uuid.GenerateUUID()
	req, err := json.Marshal(payloadCopy)
	if err != nil {
		return zero, err
	}

	var resultChan = make(chan T, 1)
	var errChan = make(chan error, 1)

	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		if result, err := client.GetRagClient().Chat(ctx, req); err != nil {
			errChan <- err
		} else {
			log.WithContext(ctx).Infow("ragChatFetch receive result", "result", result)
			if res, ok := any(result).(T); ok {
				resultChan <- res
			} else {
				errChan <- errors.New("ragChatFetch chat response type error")
			}
		}
		return nil
	}, boot.TraceGo(ctx))

	select {
	case <-ctxWithTimeout.Done():
		return zero, errors.New("ragChatFetch chat response timeout")
	case err := <-errChan:
		return zero, err
	case result := <-resultChan:
		if res, ok := any(result).(T); ok {
			return res, err
		}
		return zero, errors.New("fail to get rag chat stream fetch")
	}
}

func ragChatStreamFetch[T any](ctx context.Context, payload *rag.ReqChat, m *ChatMessage) (T, error) {
	waitForRateLimit()
	var zero T

	maxRetries := config.GetIntOr("llm.chat.rag_stream_retry_max_count", 1)
	retryDelay := time.Duration(config.GetIntOr("llm.chat.rag_stream_retry_delay_ms", 500)) * time.Millisecond
	var lastErr error

	for retryCount := 0; retryCount <= maxRetries; retryCount++ {
		ctxWithTimeout, cancel := context.WithTimeout(context.Background(), time.Duration(config.GetIntOr("llm.ai_fetch_max_timeout", 10000))*time.Millisecond)
		defer func() {
			cancel()
			payload.Stream = true
		}()

		payloadCopy := *payload
		payloadCopy.Stream = true
		payloadCopy.SessionId, _ = uuid.GenerateUUID()

		if retryCount > 0 {
			if payloadCopy.IsVision { // 多模态fallback时替换模型
				fallbackModel := config.GetStringOr("llm.rag_vision_fallback_model", "hunyuan-turbos-vision")
				text, err := getVisionText(m, fallbackModel)
				if err != nil {
					return zero, err
				}
				payloadCopy.Text = text
				payloadCopy.Model = config.GetStringOr("llm.rag_vision_fallback_model", "hunyuan-turbos-vision")
			}
			log.WithContext(ctx).Infow("ragChatStreamFetch retrying", "attempt", retryCount, "lastError", lastErr)
			time.Sleep(retryDelay)
		}

		req, err := json.Marshal(payloadCopy)
		if err != nil {
			return zero, err
		}

		var resultChan = make(chan T, 1)
		var errChan = make(chan error, 1)

		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			body, err := ragClient(payloadCopy).ChatStream(ctx, req)
			if err != nil {
				errChan <- err
				return nil
			}
			if result, err := handleStreamResponse(ctx, body); err != nil {
				errChan <- err
			} else {
				if res, ok := any(result).(T); ok {
					resultChan <- res
				} else {
					errChan <- errors.New("stream response type error")
				}
			}
			return nil
		}, boot.TraceGo(ctx))

		select {
		case <-ctxWithTimeout.Done():
			lastErr = errors.New("ragChatStreamFetch stream response timeout")
			if retryCount == maxRetries {
				return zero, lastErr
			}
			continue
		case err := <-errChan:
			lastErr = err
			// 如果是最后一次重试，返回错误
			if retryCount == maxRetries {
				return zero, fmt.Errorf("ragChatStreamFetch failed after %d retries, last error: %v", maxRetries, err)
			}
			// 否则继续重试
			continue
		case result := <-resultChan:
			if res, ok := any(result).(T); ok {
				return res, nil
			}
			lastErr = errors.New("fail to get rag chat stream fetch")
			if retryCount == maxRetries {
				return zero, lastErr
			}
			continue
		}
	}

	return zero, lastErr
}

type fallbackResult[T any] struct {
	data T
	err  error
	from string
}

func fetchWithFallback[T any](ctx context.Context, payload *rag.ReqChat, m *ChatMessage) (T, error) {
	var zero T
	// 创建结果通道
	resChan := make(chan fallbackResult[T], 2)

	// 这里使用context.Background()，让请求发完记录日志
	wg := xsync.NewGroup(context.Background(), xsync.GroupGoOption(boot.TraceGo(ctx)))

	// 启动普通 HTTP 请求
	wg.SafeGo(func(ctx context.Context) error {
		res, err := ragChatFetch[T](ctx, payload)
		select {
		case resChan <- fallbackResult[T]{data: res, err: err, from: "http"}:
		case <-ctx.Done():
		}
		return nil
	})

	// 启动 SSE 流式请求
	wg.SafeGo(func(ctx context.Context) error {
		res, err := ragChatStreamFetch[T](ctx, payload, m)
		select {
		case resChan <- fallbackResult[T]{data: res, err: err, from: "stream"}:
		case <-ctx.Done():
		}
		return nil
	})

	// 等待第一个成功的结果
	var lastErr error
	for i := 0; i < 2; i++ {
		select {
		case res := <-resChan:
			if res.err == nil {
				log.WithContext(ctx).Infow("fetchWithFallback fetch success", "from", res.from)
				if res.from == "http" {
					payload.Stream = false
				} else {
					payload.Stream = true
				}
				return res.data, nil
			}
			lastErr = res.err
			log.WithContext(ctx).Warnw("fetchWithFallback fetch failed", "from", res.from, "error", res.err, "payload.collectionName", payload.CollectionName, "payload.text", payload.Text)
		case <-ctx.Done():
			return zero, ctx.Err()
		}
	}

	// 如果都失败了，返回最后一个错误
	return zero, fmt.Errorf("fetchWithFallback all fetches failed, last error: %v", lastErr)
}

func ragChatSqlFetch[T any](ctx context.Context, payload *rag.ReqChat) (T, error) {
	var zero T
	req, err := json.Marshal(payload)
	if err != nil {
		return zero, err
	}
	result, err := client.GetPublicRagClient().ChatOrSql(ctx, req)
	if res, ok := any(result).(T); ok {
		return res, err
	}
	return zero, errors.New("fail to get rag chat sql fetch")
}

func NewChatRequest[T any]() *Request[T] {
	r := new(Request[T])
	return r
}

func (r *Request[T]) fetchWithTimeout(ctx context.Context, payload *rag.ReqChat, m *ChatMessage) (T, error) {
	var zero T
	if str, ok := payload.Text.(string); ok {
		if strings.HasPrefix(str, "[") && strings.HasSuffix(str, "]") {
			payload.Text = str[1 : len(str)-1]
		}
	}
	start := time.Now()

	strategy := FetchStrategy[T](m.Type, payload, m)
	timeout := strategy.timeout * time.Millisecond

	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	resultChan := make(chan struct {
		rsp interface{}
		err error
	}, 1)

	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		rsp, err := strategy.fetch(ctx, payload)
		resultChan <- struct {
			rsp interface{}
			err error
		}{rsp: rsp, err: err}
		reportChatMessageMetric(ctx, start, payload, m.Type, m.CreateBy)
		return nil
	}, boot.TraceGo(ctx))

	select {
	case <-ctxWithTimeout.Done():
		return zero, ctxWithTimeout.Err()
	case result := <-resultChan:
		if result.err != nil {
			return zero, result.err
		}
		t, ok := result.rsp.(T)
		if !ok {
			return zero, fmt.Errorf("invalid rag response type: expected %T, got %T", new(T), result.rsp)
		}
		return t, nil
	}

}

func saveLog[T any](ctx context.Context, m *ChatMessage, payload *rag.ReqChat, fetchRsp T, gpt string) {
	var ref, sqlQuery, finalQuery string
	var code uint64
	v := reflect.ValueOf(fetchRsp)
	if v.Kind() == reflect.Ptr && v.IsNil() {
		log.WithContext(ctx).Infow("save log get nil fetchRsp", "msg", m.ChatMessage)
	} else {
		switch rsp := any(fetchRsp).(type) {
		case *rag.RespChat:
			if rsp.Code == common.ErrCodeModelNotExist {
				gpt = beautifyChatResponseText(rsp.GPT)
				if rsp.Info != nil {
					info, ok := rsp.Info.(map[string]interface{})
					if ok {
						finalQuery = info["final_query"].(string)
					}
				}
			}
			ref = beautyChatRefLink(rsp.Ref)
			code = uint64(rsp.Code)
			if finalQuery == "" && rsp.Data != nil {
				finalQuery = rsp.Data.FinalQuery
			}
			if gpt == "" && rsp.Data != nil {
				gpt = beautifyChatResponseText(rsp.Data.GPT)
			}
		case *rag.RespChatOrSql:
			sqlQuery = rsp.Text
		default:
			log.WithContext(ctx).Errorw("invalid rag response type")
		}
	}

	err := saveChatRequestLog(ctx, &model.TChatLog{
		SQLQuery:    sqlQuery,
		Gpt:         gpt,
		Ref:         ref,
		MessageType: int32(m.Type),
		StartTime:   m.StartTime.AsTime(),
		EndTime:     time.Now(),
		Code:        code,
	}, m, payload, finalQuery)
	if err != nil {
		log.WithContext(ctx).Errorw("failed to save request log", "error", err.Error())
	}
}

func DoChatRagRequest(ctx context.Context, reqBuilder MessageRequestBuilder[any], msg *ChatMessage) (*ChatMessage, error) {
	return ragRequest(ctx, NewRequestBuilder(reqBuilder, msg))
}

func ragRequest[T any](ctx context.Context, r MessageRequestBuilder[T]) (*ChatMessage, error) {
	var (
		err       error
		startTime = time.Now()
		rsp       T
	)
	m := r.getMessage()
	m.StartTime = timestamppb.New(startTime)
	payload, err := r.createPayload()
	if err != nil {
		return nil, err
	}

	req := NewChatRequest[T]()
	rsp, err = req.fetchWithTimeout(ctx, payload, m)
	m.fetchResponseTime = time.Now()
	if err != nil {
		m.EndTime = timestamppb.New(time.Now())
		saveLog[T](ctx, m, payload, rsp, m.GetDefaultErrText()) // ai接口请求超时也存一下saveLog
		log.WithContext(ctx).Errorw("doChatRequest failed to execute request", "error", err.Error())
		return nil, err
	}
	r.setDuration(startTime, time.Now())
	if r.afterFetch != nil {
		m, err = r.afterFetch(ctx, rsp)
		if err != nil {
			return m, err
		}
	}
	m.EndTime = timestamppb.New(time.Now())
	return m, nil
}

func getVisionText(m *ChatMessage, model string) (interface{}, error) {
	var content []*rag.ChatVisionContent
	if len(m.Text) > 0 {
		content = append(content, &rag.ChatVisionContent{
			Type: "text",
			Text: m.Text,
		})
	}

	for _, file := range m.requestFiles {
		if file.Text != "" {
			content = append(content, &rag.ChatVisionContent{
				Type: "text",
				Text: file.Text,
			})
			continue
		}
		if strings.Contains(model, "gemini") { // gemini模型直接使用url
			content = append(content, &rag.ChatVisionContent{
				Type:     "image_url",
				ImageURL: &rag.ChatImageUrl{Url: file.URL},
			})
			continue
		}
		if file.ParsedURL != "" {
			content = append(content, &rag.ChatVisionContent{
				Type:     "image_url",
				ImageURL: &rag.ChatImageUrl{Url: file.ParsedURL},
			})
		}
	}
	if len(content) < 2 {
		return nil, errors.New("vision request must have at least one text and one image")
	}

	return []*rag.ChatHistoryMessage{{
		Role:    "user",
		Content: content,
	}}, nil
}

func getHistoryRoundsText(m *ChatMessage) interface{} {
	if m.ReqText != nil {
		return m.ReqText
	}
	if m.GetOptions().noHistoryRounds || len(m.DbMessages) == 0 {
		return m.Text
	}

	history := GetMessageHistory(m, m.Type)
	if history == nil || len(history) == 1 {
		return m.Text
	}
	// 追加最后一个问题
	history = append(history, getLastQuestion(m.Text))
	return history
}

func getLastQuestion(text string) *rag.ChatHistoryMessage {
	return &rag.ChatHistoryMessage{
		Role:    "user",
		Content: text,
	}
}

func beautifyChatResponseText(text string) string {
	isJson := func(in string) bool {
		var js interface{}
		if json.Unmarshal([]byte(in), &js) == nil {
			return true
		}
		return false
	}

	isUnicodeEncoded := func(s string) bool {
		re := regexp.MustCompile(`\\u[0-9a-fA-F]{4}`)
		return re.MatchString(s)
	}

	handleContent := func(content interface{}) string {
		switch c := content.(type) {
		case string:
			if isJson(c) {
				return beautifyChatResponseText(c)
			}
			return c
		default:
			return text
		}
	}

	var msg rag.ChatHistoryMessage
	var msgs []rag.ChatHistoryMessage
	if err := json.Unmarshal([]byte(text), &msgs); err == nil && len(msgs) > 0 {
		return handleContent(msgs[0].Content)
	}

	if err := json.Unmarshal([]byte(text), &msg); err == nil {
		return handleContent(msg.Content)
	}

	if isUnicodeEncoded(text) {
		unquote, err := strconv.Unquote(`"` + text + `"`)
		if err != nil {
			return text
		}
		return unquote
	}

	return text
}

func beautyChatRefLink(originRef string) string {
	ref := strings.ReplaceAll(strings.ReplaceAll(originRef, "http-unsolved\n", ""), "http-unsolved", "")
	refs := strings.Split(ref, "\n")
	if len(refs) > 0 {
		for _, r := range refs {
			re := regexp.MustCompile(`\((.*?)\)`)
			matches := re.FindStringSubmatch(r)
			if len(matches) > 1 {
				// 返回括号内的内容
				r = matches[1]
			}
		}
		return strings.Join(refs, "\n")
	}
	return ref
}

func filterCatchGptRefNumber(text string, maxNum int) (map[int]struct{}, [][]string, string) {
	re := regexp.MustCompile(`\[(\d+(?:,\s?\d+)*)\]`)
	matches := re.FindAllStringSubmatch(text, -1) // [["[1,2,3]", "1,2,3"],["[1]","1"]]
	uniqueNumbers := make(map[int]struct{})       // 用于筛选filename

	for _, match := range matches {
		content := match[1] // "[1,2,3]"
		parts := strings.Split(content, ",")
		for _, part := range parts {
			num, err := strconv.Atoi(strings.TrimSpace(part))
			if err == nil {
				if num > maxNum {
					text = strings.ReplaceAll(text, match[0], "")
				}
				uniqueNumbers[num] = struct{}{}
			} else { // 移除[x]，x非数字
				text = strings.ReplaceAll(text, match[0], "")
			}
		}
	}

	return uniqueNumbers, matches, text
}

// 记录chat request metric
func reportChatMessageMetric(ctx context.Context, start time.Time, payload *rag.ReqChat, messageType aipb.ChatMessageType, createBy uint64) {
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		if createBy > 0 {
			var identity *basepb.Identity
			identity = &basepb.Identity{
				IdentityType: basepb.IdentityType_IDENTITY_TYPE_USER,
				IdentityId:   createBy,
			}
			metric.ContextWithApiIdentity(ctx, identity)
		}

		elapsed := time.Since(start)
		report := &FetchMetric{
			Elapsed: elapsed.Milliseconds(),
			ReqChat: &rag.ReqChat{
				CollectionName: payload.CollectionName,
				Model:          payload.Model,
				Threshold:      payload.Threshold,
				PromptPrefix:   payload.PromptPrefix,
				Engine:         payload.Engine,
				Search:         payload.Search,
				Lang:           payload.Lang,
				SearchRewrite:  payload.SearchRewrite,
				TopN:           payload.TopN,
				TextWeight:     payload.TextWeight,
			},
		}
		metric.Create(ctx, metricType[messageType], report, metric.WithIdentity(ctx))
		return nil
	}, boot.TraceGo(ctx))
}

func GetMessageHistory(m *ChatMessage, messageType aipb.ChatMessageType) []*rag.ChatHistoryMessage {
	messages := m.DbMessages
	var hasClearAnswer bool
	var result []*rag.ChatHistoryMessage

	findValidAnswer := func(questionID uint64) *model.TChatMessage {
		if questionID == 0 {
			return nil
		}
		for _, msg := range messages {
			if msg.Type == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_CLEAR_HISTORY) {
				hasClearAnswer = true
			} else if msg.QuestionID == questionID && slices.Contains(MessageRequestValidType, msg.Type) {
				return msg
			}
		}
		return nil
	}

	for i, msg := range messages {
		if aipb.ChatMessageType(msg.Type) == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER && i < len(messages)-1 {
			var answer = findValidAnswer(msg.ID)
			if hasClearAnswer {
				return nil
			}
			if answer != nil {
				// 处理问题
				qContent := &aipb.ChatMessageContent{}
				aContent := &aipb.ChatMessageContent{}
				if err := json.Unmarshal(msg.Content, &qContent); err != nil {
					return nil
				}
				if err := json.Unmarshal(answer.Content, &aContent); err != nil {
					return nil
				}
				if aContent.Text == "" {
					return nil
				}
				result = append(result, getLastQuestion(qContent.Text))
				// 处理答案
				switch aipb.ChatMessageType(answer.Type) {
				case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY:
					if messageType == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY {
						result = append(result, &rag.ChatHistoryMessage{
							Role:    "assistant",
							Content: aContent.SqlQuery,
						})
					} else {
						var ugcNames []string
						if aContent.Ugcs != nil {
							for _, ugc := range aContent.Ugcs {
								if ugc.Cards != nil {
									for _, card := range ugc.Cards {
										ugcNames = append(ugcNames, card.Name)
									}
								}
							}
						}
						result = append(result, &rag.ChatHistoryMessage{
							Role:    "assistant",
							Content: BeautyHistoryText(fmt.Sprintf("我在碳LIVE上为你找到：%s", strings.Join(ugcNames, "、"))),
						})
					}
				default:
					result = append(result, &rag.ChatHistoryMessage{
						Role:    "assistant",
						Content: BeautyHistoryText(aContent.Text),
					})
				}
			}
		}
	}
	return result
}

// saveChatRequestLog 保存chat request log
func saveChatRequestLog(ctx context.Context, row *model.TChatLog, m *ChatMessage, payload *rag.ReqChat, finalQuery string) error {
	var err error
	var conf *aipb.MessageConfig
	var bytes []byte
	defer func() {
		if err != nil {
			log.WithContext(ctx).Errorw("saveChatRequestLog err", "err", err)
		}
	}()

	if row.MessageType == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY) {
		conf = &aipb.MessageConfig{
			SqlModel:      payload.Model,
			HistoryRounds: m.Assistant.HistoryRounds,
			DefaultText:   m.DefaultText,
			IsStream:      payload.Stream,
		}
	}
	if row.MessageType == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION) {
		conf = &aipb.MessageConfig{
			Threshold:      payload.Threshold,
			DocTopN:        int32(payload.TopN),
			Model:          payload.Model,
			CollectionName: payload.CollectionName,
			HistoryRounds:  m.Assistant.HistoryRounds,
			PromptPrefix:   payload.PromptPrefix,
			FinalQuery:     finalQuery,
			DefaultText:    m.DefaultText,
			TextRecallTopN: int32(payload.TextRecallTopN),
			CleanChunks:    payload.CleanChunks,
			Temperature:    payload.Temperature,
			IsStream:       payload.Stream,
		}
		if m.Assistant.TextRecallTopN > 0 {
			conf.TextRecallQuery = m.Assistant.TextRecallQuery
			conf.TextRecallPattern = m.Assistant.TextRecallPattern
			conf.TextRecallSlop = m.Assistant.TextRecallSlop
		}
	}
	if row.MessageType == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH) {
		conf = &aipb.MessageConfig{
			SearchTopN:       int32(payload.TopN),
			HistoryRounds:    m.Assistant.HistoryRounds,
			PromptPrefix:     payload.PromptPrefix,
			Model:            payload.Model,
			SearchEngine:     payload.Engine,
			FinalQuery:       finalQuery,
			DefaultText:      m.DefaultText,
			FinalSearchQuery: m.FinalSearchQuery,
			Temperature:      payload.Temperature,
			IsStream:         payload.Stream,
		}
	}
	if row.MessageType == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION) {
		conf = &aipb.MessageConfig{
			PromptPrefix: payload.PromptPrefix,
			Model:        payload.Model,
			FinalQuery:   finalQuery,
			DefaultText:  m.DefaultText,
			IsStream:     payload.Stream,
		}
	}

	if m.Assistant.QuestionTypeConfig != nil && m.Assistant.QuestionTypeConfig.Enabled {
		conf.JudgePrompt = m.Assistant.QuestionTypeConfig.Prompt
		conf.ChatModel = payload.Model4chat
		conf.ComplexModel = payload.Model4complex
		switch m.PromptType {
		case AIChatPromptTypeChat:
			conf.Model = m.Assistant.QuestionTypeConfig.ChatModel
		case AIChatPromptTypeSimple:
			conf.Model = m.Assistant.QuestionTypeConfig.SimpleModel
		case AIChatPromptTypeComplex:
			conf.Model = m.Assistant.QuestionTypeConfig.ComplexModel
		}
	}

	switch text := payload.Text.(type) {
	case string:
		conf.FinalQuery = ""
		row.RequestText = text
	default:
		if bytes, err = json.Marshal(text); err != nil {
			return err
		}
		row.RequestText = string(bytes)
	}

	if bytes, err = json.Marshal(conf); err != nil {
		return err
	}
	row.ConfigSnapshot = string(bytes)
	row.TaskIndex = int32(m.TaskIndex)
	row.PromptType = m.PromptType
	row.FetchRespTime = &m.fetchResponseTime

	if m.LogChan != nil {
		m.LogChan.Send(row)
	}

	return nil
}

func BeautyHistoryText(text string) string {
	str := strings.ReplaceAll(text, "\\n", "")
	str = strings.ReplaceAll(str, "\n", "")
	str = strings.ReplaceAll(str, "\\", "")
	return str
}

//func SendChatEventSource(ctx context.Context, userId uint64, event int32, content string, hashId string) error {
//
//	if userId == 0 {
//		return fmt.Errorf("userId is 0")
//	}
//
//	if len(content) == 0 {
//		return fmt.Errorf("data is empty")
//	}
//
//	jsonData, err := json.Marshal(aipb.ChatPublishEvent{
//		Event:   event,
//		Content: content,
//		UserId:  userId,
//		HashId:  hashId,
//	})
//	if err != nil {
//		return err
//	}
//	topic := fmt.Sprintf("%s%d", config.GetStringOr("llm.chat_channel_topic", "tanlive:channel:ai:"), userId)
//
//	redisClient().Publish(ctx, topic, string(jsonData))
//
//	return nil
//}
