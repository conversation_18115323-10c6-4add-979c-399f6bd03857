package chat

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	rp "e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/tmt"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"
)

const (
	AIEnhancementSearch = "search"
	AIEnhancementDoc    = "doc"
)

const (
	AIChatPromptTypeSimple  string = "simple"
	AIChatPromptTypeComplex string = "complex"
	AIChatPromptTypeChat    string = "chat"
)

type CollectionSnapshot struct {
	StartTime   time.Time                    `json:"startTime,omitempty"`
	EndTime     time.Time                    `json:"endTime,omitempty"`
	Items       []*aipb.SearchCollectionItem `json:"items,omitempty"`
	CleanChunks bool                         `json:"cleanChunks,omitempty"`
}

// QueryDocsByCollectionItems 修复collection历史数据
func QueryDocsByCollectionItems(ctx context.Context, items []*aipb.SearchCollectionItem) ([]*aipb.SearchCollectionItem, error) {
	var qaDocs []*model.TDoc
	var fileDocs []*model.TDoc
	var textDocs []*model.TDoc

	var questions []string
	var fileNames []string
	var texts []string

	if items[0].DocId > 0 {
		return items, nil
	}

	if len(items) > 0 {
		if items[0].DocId == 0 { // 处理历史数据
			for _, item := range items {
				if item.DocType == aipb.DocType_DOCTYPE_QA && item.Text != "" {
					questions = append(questions, item.Question)
				} else if item.DocType == aipb.DocType_DOCTYPE_FILE {
					if item.RefName != "" {
						fileNames = append(fileNames, item.RefName)
					} else if item.FileName != "" {
						fileNames = append(fileNames, item.FileName)
					}
				} else if item.DocType == aipb.DocType_DOCTYPE_TEXT {
					texts = append(texts, item.Text)
				}
			}
		}
	}
	db := model.NewQuery[model.TDoc](ctx).DB()
	// Query for QA (dataType=1)
	if len(questions) > 0 {
		if err := db.Where("data_type = ? AND index_text IN ?", aipb.DocType_DOCTYPE_QA, questions).Find(&qaDocs).Error; err != nil {
			return nil, fmt.Errorf("query QA docs: %w", err)
		}
	}

	// Query for Files (dataType=3)
	if len(fileNames) > 0 {
		if err := db.Where("data_type = ? AND file_name IN ?", aipb.DocType_DOCTYPE_FILE, fileNames).Find(&fileDocs).Error; err != nil {
			return nil, fmt.Errorf("query file docs: %w", err)
		}
	}

	// Query for Texts (dataType=2)
	if len(texts) > 0 {
		query := db.Where("data_type = ?", aipb.DocType_DOCTYPE_TEXT)
		for _, text := range texts {
			query = query.Or("index_text LIKE ?", "%"+text+"%")
		}
		if err := query.Find(&textDocs).Error; err != nil {
			return nil, fmt.Errorf("query text docs: %w", err)
		}
	}

	for _, qaDoc := range qaDocs {
		for _, item := range items {
			if item.DocType == aipb.DocType_DOCTYPE_QA && item.Question == qaDoc.IndexText {
				item.DocId = qaDoc.ID
				item.DataSource = qaDoc.DataSource
			}
		}
	}
	for _, fileDoc := range fileDocs {
		for _, item := range items {
			if item.DocType == aipb.DocType_DOCTYPE_FILE {
				if item.RefName != "" && item.RefName == fileDoc.FileName {
					item.DocId = fileDoc.ID
					item.DataSource = fileDoc.DataSource
				} else if item.FileName != "" && item.FileName == fileDoc.FileName {
					item.DocId = fileDoc.ID
					item.DataSource = fileDoc.DataSource
				}
			}
		}
	}
	for _, textDoc := range textDocs {
		for _, item := range items {
			if item.DocType == aipb.DocType_DOCTYPE_TEXT {
				if strings.Contains(textDoc.IndexText, item.Text) {
					item.DocId = textDoc.ID
					item.DataSource = textDoc.DataSource
				}
			}
		}
	}

	return items, nil
}

// ... existing code ...

func CheckUserChat(ctx context.Context, chatId uint64, userId uint64) error {
	if userId == 0 {
		return xerrors.BadRequestError("userId is empty")
	}
	var chat *model.TChat
	if err := model.NewQuery[model.TChat](ctx).DB().Where("id = ? and create_by = ?", chatId, userId).First(&chat).Error; err != nil {
		return err
	}
	return nil
}

func handleHashId(id uint64) string {
	if id == 0 {
		return ""
	}
	encode, err := hashids.DefaultCodec.Encode(id)
	if err != nil {
		return ""
	}
	return encode
}

// DescribeMessageDocs 通过doc name获取文档
// 已经 preload 了参考资料
func DescribeMessageDocs(ctx context.Context, docNames []string) ([]*model.TDoc, error) {
	var orderedDocs []*model.TDoc
	if len(docNames) == 0 {
		return orderedDocs, nil
	}
	var err error
	var td map[string]*model.TDoc
	preloads := []string{"References", "TextReferences", "DocReferences"}
	td, err = logic.GetDocByRagDocNames(ctx, docNames, false, preloads)
	if err != nil {
		return nil, err
	}

	// 按docNames顺序重新排列
	for _, name := range docNames {
		if doc, exists := td[name]; exists {
			orderedDocs = append(orderedDocs, doc)
		}
	}

	return orderedDocs, nil
}

func UpdateMessageTextAndThink(ctx context.Context, m *model.TChatMessage, text string, think string, msgType aipb.ChatMessageType) error {
	// 更新message 回答
	if text != "" {
		content := &aipb.ChatMessageContent{}
		if err := json.Unmarshal(m.Content, &content); err != nil {
			log.WithContext(ctx).Infow("updateMessageText  json.Unmarshal content err")
		}
		content.Text = text

		dbContent, err := json.Marshal(content)
		if err != nil {
			return err
		}
		m.Content = dbContent
		if err = model.NewQuery[model.TChatMessage](ctx).DB().Where("type = ? and id = ?", msgType, m.ID).
			Updates(map[string]interface{}{
				"content": dbContent,
			}).Error; err != nil {
			return err
		}
	}

	// 更新思考过程
	if think != "" {
		updates := model.TChatMessageThink{
			MessageID: m.ID,
			Content:   think,
		}

		if err := model.NewQuery[model.TChatMessageThink](ctx).DB().Where("message_id = ?", m.ID).Assign(map[string]interface{}{"content": think}).FirstOrCreate(&updates).Error; err != nil {
			return err
		}
	}

	// 更新log中的gpt回答
	if err := model.NewQuery[model.TChatLog](ctx).DB().Where("message_type = ? and message_id = ?", msgType, m.ID).
		Updates(map[string]interface{}{
			"gpt": text,
		}).Error; err != nil {
		return err
	}
	return nil
}

// SaveQuestionMessage 创建问题消息
func SaveQuestionMessage(ctx context.Context, message *aipb.ChatMessage,
	cursor, thirdRecordUUID, thirdUserUUID, liveAgent string,
) (*model.TChatMessage, error) {
	if message.Type != aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER &&
		message.Type != aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER &&
		message.Type != aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT {
		return nil, xerrors.InternalServerError("question message type err")
	}
	// 语言识别
	rsp, err := tmt.LanguageDetect(ctx, &tmt.ReqLanguageDetect{Text: message.Text, ProjectId: 0})
	if err != nil {
		log.WithContext(ctx).Errorw("FetchAndHandleAIAnswer.tmtutil.LanguageDetect err", "err", err, "content", message.Text)
		message.Lang = util.JudgeLanguage(message.Text)
	} else {
		message.Lang = rsp.Lang
	}

	// 创建问题
	dbm, err := NewChatMessage(message).SetWorkWeiXinRecordValue(cursor, thirdRecordUUID, thirdUserUUID, liveAgent).Save(ctx)
	if err != nil {
		return nil, err
	}

	// 更新chat assistant
	if _, err = model.NewQuery[model.TChat](ctx).UpdateByKey(message.ChatId, map[string]any{
		model.TChatColumns.AssistantID: message.AssistantId,
	}); err != nil {
		return dbm, err
	}

	// 创建微信记录
	if len(thirdRecordUUID) > 0 {
		row := &model.TChatSendRecord{
			MessageID:  dbm.ID,
			Content:    message.Text,
			ChatID:     message.ChatId,
			Type:       aipb.AiRecordType_AI_RECORD_TYPE_USER,
			State:      int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND), // 2 默认已发送
			UUID:       thirdRecordUUID,
			CreateDate: time.Now(),
			SendDate:   time.Now(),
		}
		if len(message.ImageUrl) > 0 {
			row.Content = message.ImageUrl[0]
		}
		if message.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET {
			row.MessageType = aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER
		}
		// 保存发送记录
		return dbm, model.NewQuery[model.TChatSendRecord](ctx).Create(row)
	}
	return dbm, nil
}

// GetChatTasksAnswer 获取多任务chat回答
func GetChatTasksAnswer(ctx context.Context, assistant *aipb.AssistantDetail, tasks []*ChatTask) ([]*aipb.ChatMessage, error) {
	return getMultipleTasksAnswer(ctx, assistant, tasks)
}

// WxGetChatTasksAnswer 获取微信端chat回答
func WxGetChatTasksAnswer(ctx context.Context, assistant *aipb.AssistantDetail, tasks []*ChatTask) ([]*aipb.ChatMessage, error) {
	for _, task := range tasks {
		task.ReqMessage.WaitAnswer = true
		task.ReqMessage.SetOption(WithoutSuggestions(true))
	}
	return getMultipleTasksAnswer(ctx, assistant, tasks)
}

// getMultipleTasksAnswer ...
func getMultipleTasksAnswer(ctx context.Context, assistant *aipb.AssistantDetail, tasks []*ChatTask) ([]*aipb.ChatMessage, error) {
	resultChan := make(chan *ChatTask)
	var result []*aipb.ChatMessage
	if len(tasks) == 0 {
		return result, nil
	}

	wg := xsync.NewGroup(context.Background(), xsync.GroupGoOption(boot.TraceGo(ctx)))
	for index, task := range tasks {
		t := task
		if t.ReqMessage == nil {
			return nil, errors.New("getMultipleTasksAnswer ReqMessage is nil")
		}
		if len(tasks) > 1 || task.startAnswerIndex > 0 { // 针对多任务做索引标注
			t.ReqMessage.AnswerIndex = task.startAnswerIndex + int32(index+1)
		}
		wg.SafeGo(func(ctx context.Context) error {
			t.ctx = ctx
			r := t.CreatChatTaskChainAnswer(assistant)
			if r != nil {
				resultChan <- r
			}
			return nil
		})
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

	errTask := make(map[string]*ChatTask)
	var saveTasks []*ChatTask
	for t := range resultChan {
		if t.chainHashId == "" { // 没有hashId的回答正常保存
			saveTasks = append(saveTasks, t)
			continue
		}
		// 用hashId表示results是同一个问题的回答，如果两个回答都为err，则在详情页隐藏一个err
		if t.fetchErr != nil && errTask[t.chainHashId] == nil {
			message, err := t.GetRspMessage()
			if err != nil {
				log.WithContext(ctx).Errorw("getMultipleTasksAnswer err", "err", err)
				continue
			}
			message.ShowType = int32(MessageShowTypeDisable)
			errTask[t.chainHashId] = t
		}
		saveTasks = append(saveTasks, t)
	}

	for _, st := range saveTasks {
		msg, err := st.Save()
		if err == nil && msg != nil {
			result = append(result, msg)
		}
	}

	log.WithContext(ctx).Infow("getMultipleTasksAnswer response", "results", result)
	return result, nil
}

// CreateChat 创建会话
func CreateChat(ctx context.Context, req *aipb.ReqCreateUserChat) (*model.TChat, error) {
	if req.AssistantId == 0 {
		return nil, xerrors.ValidationError("assistant_id is required")
	}
	session := &model.TChat{
		CreateBy:    req.CreateBy,
		Title:       req.Title,
		UpdateBy:    req.CreateBy,
		Type:        int32(req.Type),
		AppID:       req.AppId,
		AssistantID: req.AssistantId,
		RegionCode:  req.RegionCode,
	}

	err := model.NewQuery[model.TChat](ctx).Create(session)
	if err != nil {
		return nil, err
	}

	return session, nil
}

// DescribeChatQuestionAnswers 获取消息
func DescribeChatQuestionAnswers(ctx context.Context, chatId, questionId uint64, limit uint32, offset uint32, orderBy string) ([]*aipb.ChatMessage, []*aipb.ChatMessage, int64, error) {
	var questions, answers []*model.TChatMessage
	var pbQuestions, pbAnswers []*aipb.ChatMessage
	var totalCount int64

	// 查询问题消息
	query := model.NewQuery[model.TChatMessage](ctx).DB()
	if limit > 0 {
		query.Limit(int(limit)).Offset(int(offset))
	}

	if questionId > 0 {
		query.Where("id = ?", questionId)
	}

	// 使用 Preload 加载最后一次操作记录
	query.Where("ask_type != ?", aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET).
		Where("chat_id = ?", chatId).
		Where("question_id = ?", 0).
		Preload("Files").
		Preload("LastOperation", func(db *gorm.DB) *gorm.DB {
			subQuery := db.Model(&model.TChatOperation{}).
				Select("message_id, MAX(create_date) as max_date").
				Group("message_id")
			return db.Joins("JOIN (?) AS latest ON t_chat_operation.message_id = latest.message_id AND t_chat_operation.create_date = latest.max_date", subQuery)
		})

	if orderBy != "" {
		query.Order(orderBy)
	}

	err := query.Count(&totalCount).Find(&questions).Error
	if err != nil {
		return pbQuestions, pbAnswers, 0, err
	}

	// 如果没有问题，直接返回
	if len(questions) == 0 {
		return pbQuestions, pbAnswers, 0, nil
	}

	// 收集所有问题ID
	var questionIds []uint64
	for _, q := range questions {
		questionIds = append(questionIds, q.ID)
		pbMsg, err := q.TransformToAiMessage()
		if err != nil {
			return pbQuestions, pbAnswers, 0, err
		}
		pbQuestions = append(pbQuestions, pbMsg)
	}

	// 查询所有问题的回答
	exceptAnswerType := []aipb.ChatMessageType{aipb.ChatMessageType_CHAT_MESSAGE_TYPE_DRAFT}
	err = model.NewQuery[model.TChatMessage](ctx).DB().
		Where("chat_id = ?", chatId).
		Where("question_id IN (?)", questionIds).
		Where("type not in ?", exceptAnswerType).
		Preload("LastOperation", func(db *gorm.DB) *gorm.DB {
			subQuery := db.Model(&model.TChatOperation{}).
				Select("message_id, MAX(create_date) as max_date").
				Group("message_id")
			return db.Joins("JOIN (?) AS latest ON t_chat_operation.message_id = latest.message_id AND t_chat_operation.create_date = latest.max_date", subQuery)
		}).
		Preload("Think").
		Preload("Docs").
		Preload("Suggests").
		Preload("Feedback").
		Preload("Task").
		Order("id DESC").
		Find(&answers).Error

	for _, answer := range answers {
		pbMsg, err := answer.TransformToAiMessage()
		if err != nil {
			return pbQuestions, pbAnswers, 0, nil
		}
		pbAnswers = append(pbAnswers, pbMsg)
	}

	return pbQuestions, pbAnswers, totalCount, nil
}

type DescribeChatHistoryMessagesParams struct {
	AskTypes          []aipb.QuestionAskType
	IgnoreQuestionIds []uint64
}

// DescribeChatHistoryMessages 会话框倒序获取最新的message
func DescribeChatHistoryMessages(ctx context.Context, chatId uint64, limit int32, offset uint32, param *DescribeChatHistoryMessagesParams) ([]*model.TChatMessage, error) {
	questionType := int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER)
	var messages []*model.TChatMessage
	var questionIDs []uint64
	var combinedMessages []*model.TChatMessage

	if limit < 1 {
		return nil, errors.New("limit cannot be less than 1")
	}

	// 倒序查询问题
	query := model.NewQuery[model.TChatMessage](ctx).DB().Select("id")
	if len(param.AskTypes) > 0 {
		query.Where("ask_type in (?)", param.AskTypes)
	}
	if len(param.IgnoreQuestionIds) > 0 {
		query.Where("id not in ?", param.IgnoreQuestionIds)
	}
	query.Where("type = ? and chat_id = ?", questionType, chatId).
		Order("create_date DESC").Limit(int(limit)).Offset(int(offset))

	err := query.Pluck("id", &questionIDs).Error
	if err != nil {
		return nil, err
	}

	query2 := model.NewQuery[model.TChatMessage](ctx).DB()
	err = query2.Where("type = ? AND id IN (?) OR type != ? AND question_id IN (?)",
		questionType, questionIDs, questionType, questionIDs).Order("ID ASC").Find(&messages).Error
	if err != nil {
		return nil, err
	}

	// 创建question_id到最新答案的映射
	answerMap := make(map[uint64]*model.TChatMessage)
	for _, answer := range messages {
		if answer.QuestionID > 0 {
			answerMap[answer.QuestionID] = answer
		}
	}

	for _, q := range messages {
		if q.Type != int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER) {
			continue
		}
		combinedMessages = append(combinedMessages, q) // question
		if answerMap[q.ID] != nil {
			combinedMessages = append(combinedMessages, answerMap[q.ID]) // answer
		}
	}

	return combinedMessages, nil
}

// FilterListChat 过滤chat
func FilterListChat(ctx context.Context, req *aipb.ReqListChat) *xorm.Query[model.TChat] {
	return model.NewQuery[model.TChat](ctx).Select(`id`, `title`, `t_chat.create_date`, `finish_date`, `t_chat.create_by`,
		`t_chat.update_by`, `t_chat.deleted_at`, `type`, `app_id`, `image`, `nickname`, `is_manual`,
		`origin`, `support_type`, `assistant_id`, `region_code`,
		"v_chat_summary.question_cnt AS question_cnt",
		"v_chat_summary.update_date AS update_date",
		"v_chat_summary.doc_hits AS doc_hits",
		"v_chat_summary.avg_duration AS avg_duration",
		"v_chat_summary.rating_scale AS rating_scale",
		"v_chat_summary.reject_job_result AS reject_job_result",
	).Wheres(func(db *gorm.DB) {
		db.Joins("LEFT JOIN v_chat_summary ON t_chat.id = v_chat_summary.chat_id")
		if req.CreateDateRange != nil {
			if req.CreateDateRange.Start != nil {
				db.Where("create_date >= ?", req.CreateDateRange.Start.AsTime().Local())
			}
			if req.CreateDateRange.End != nil {
				db.Where("create_date <= ?", req.CreateDateRange.End.AsTime().Local())
			}
		}

		if req.UpdateDateRange != nil {
			if req.UpdateDateRange.Start != nil {
				db.Where("v_chat_summary.update_date >= ?", req.UpdateDateRange.Start.AsTime().Local())
			}
			if req.UpdateDateRange.End != nil {
				db.Where("v_chat_summary.update_date <= ?", req.UpdateDateRange.End.AsTime().Local())
			}
		}
		if req.Origin != aipb.ChatOrigin_CHAT_ORIGIN_UNSPECIFIED {
			db.Where("origin = ?", req.Origin)
		}
		if len(req.AssistantIds) != 0 {
			db.Where("assistant_id IN (?)", req.AssistantIds)
		}
		if len(req.Ids) != 0 {
			db.Where("id IN (?)", req.Ids)
		}
		if req.Filter == nil {
			return
		}
		if req.Filter.RejectJobResult > 0 {
			db.Where("v_chat_summary.reject_job_result = ?", req.Filter.RejectJobResult)
		}
		if req.Filter.IsManual > 0 {
			db.Where("t_chat.is_manual = ?", req.Filter.IsManual)
		}
		if len(req.Filter.ChatTitles) > 0 {
			var filter []interface{}
			sql := " EXISTS(SELECT 1 FROM t_chat_message WHERE t_chat.id = t_chat_message.chat_id AND ( 1 != 1 "
			for _, title := range req.Filter.ChatTitles {
				sql += " OR JSON_UNQUOTE(JSON_EXTRACT(`content`,'$.text')) LIKE ? "
				filter = append(filter, "%"+xorm.EscapeLikeWildcards(title)+"%")
			}
			sql += "))"
			db.Where(sql, filter...)
		}
		if req.Filter.ChatType != aipb.ChatType_CHAT_TYPE_UNSPECIFIED {
			db.Where("t_chat.type = ?", req.Filter.ChatType)
		}

		if len(req.Filter.Nicknames) > 0 || len(req.Filter.UserIds) > 0 {
			var filter []interface{}
			sql := "1 <> 1 "
			if len(req.Filter.Nicknames) > 0 {
				for _, v := range req.Filter.Nicknames {
					sql += "OR nickname LIKE ? "
					filter = append(filter, "%"+xorm.EscapeLikeWildcards(v)+"%")
				}
			}
			if len(req.Filter.UserIds) > 0 {
				sql += "OR create_by IN (?) "
				filter = append(filter, req.Filter.UserIds)
			}
			db.Where(sql, filter...)
		}
		if len(req.Filter.RegionCodes) > 0 {
			db.Where("t_chat.region_code in ?", req.Filter.RegionCodes)
		}
		db.Scopes(model.WithLabels(aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_CHAT, req.LabelTenant, req.Labels, true))
		db.Scopes(model.OrderByLabel(aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_CHAT, req.OrderByLabel))
	})
}

func MessageDetailConfigAuthItem(ctx context.Context, assistantId uint64) ([]string, error) {
	var extend map[string]interface{}
	var result map[string]interface{}
	err := model.NewConnectionV1Mgmt(ctx).Raw("SELECT `extend` FROM t_op_cms_resource WHERE `cms_key` = ?", "ai_auth").Find(&result).Error
	if err != nil {
		return nil, err
	}
	if result["extend"] != nil {
		err = json.Unmarshal([]byte(result["extend"].(string)), &extend)
		if err != nil {
			return nil, err
		}
	}
	if extend == nil {
		return nil, xerrors.BadRequestError("message detail auth extend not found")
	}
	authAll := extend["auth_all"]
	var authItem []string
	if authAll != nil && len(authAll.([]interface{})) > 0 {
		for _, v := range authAll.([]interface{}) {
			if v.(map[string]interface{})["u_id"] == float64(assistantId) {
				items := v.(map[string]interface{})["auth"].([]interface{})
				if items != nil && len(items) > 0 {
					for _, item := range items {
						authItem = append(authItem, item.(string))
					}
				}
			}
		}
	}
	return authItem, nil
}

func TransformMessageConfigToBffMessageConfig(configSnapshot string, configFields []string, ignoreFields []string) (string, error) {
	if configFields == nil || len(configFields) == 0 {
		return configSnapshot, nil
	}

	var data map[string]interface{}
	err := json.Unmarshal([]byte(configSnapshot), &data)
	if err != nil {
		return "", err
	}
	if data == nil {
		return "", nil
	}
	resultMap := make(map[string]interface{})
	for key, value := range data {
		if xslice.Contains(configFields, key) != -1 && xslice.Contains(ignoreFields, key) == -1 {
			resultMap[key] = value
		}
	}

	if data["clean_chunks"] == nil {
		data["clean_chunks"] = false
	}

	result, err := json.Marshal(resultMap)
	if err != nil {
		return "", nil
	}
	return string(result), nil
}

// SetLastQuestionInfoWechat 获取最后一个问题的信息
func SetLastQuestionInfoWechat(ctx context.Context,
	message *aipb.ChatMessage,
) (uint64, aipb.RspGetAnswerWechat_QuestionType) {
	// 获取最后一个问题
	var rows []*model.TChatMessage
	var err error

	if message.Id == 0 {
		rows, err = model.NewQuery[model.TChatMessage](ctx).Wheres(func(db *gorm.DB) {
			db.Where("chat_id = ?", message.ChatId).
				Where("type = ?", aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER).
				Where("question_id = ?", 0)
			// Where("state in ?", []int{model.Send, model.SendDefault})
			// .Where("live_agent_name = ?", liveAgent)
		}).OrderBy("id", true).Limit(1).Get()
	} else {
		rows, err = model.NewQuery[model.TChatMessage](ctx).GetBy("id = ? ", message.Id)
	}
	if err != nil {
		log.WithContext(ctx).Errorw("GetLastQuestionWechat error", "err", err, "message", message)
		return 0, aipb.RspGetAnswerWechat_QUESTION_TYPE_NORMAL
	}
	if len(rows) == 0 || rows[0] == nil {
		return 0, aipb.RspGetAnswerWechat_QUESTION_TYPE_NORMAL
	}

	lastContent := aipb.ChatMessageContent{}
	if err = json.Unmarshal(rows[0].Content, &lastContent); err != nil {
		log.WithContext(ctx).Errorw("SaveLiveAgentMessage unmarshal last question error",
			"err", err, "content", rows[0].Content)
		return 0, aipb.RspGetAnswerWechat_QUESTION_TYPE_NORMAL
	}

	if message.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT { // 人工客服消息，将人工回答追加到用户之前的提问上
		message.QuestionId = rows[0].ID
	}

	// 通过当前问题判断返回的问题类型
	switch message.AskType {
	case aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION: // 重新回答
		// message.Text = lastContent.Text // 填充上一个问题的信息
		return rows[0].ID, aipb.RspGetAnswerWechat_QUESTION_TYPE_REPETITION

	case aipb.QuestionAskType_QUESTION_ASK_TYPE_CONTINUE: // 继续回答
		message.Text = lastContent.Text // 填充上一个问题的信息
		return rows[0].ID, aipb.RspGetAnswerWechat_QUESTION_TYPE_NORMAL

	case aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL, aipb.QuestionAskType_QUESTION_ASK_TYPE_VOICE:
		if rows[0].AskType != int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL) &&
			rows[0].AskType != int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_VOICE) {
			break
		}
		if lastContent.Text == message.Text { // 连续问了两个一摸一样的问题
			return rows[0].ID, aipb.RspGetAnswerWechat_QUESTION_TYPE_REPETITION
		}

	case aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE, aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE:
		return 0, aipb.RspGetAnswerWechat_QUESTION_TYPE_NORMAL
	}

	if (rows[0].AskType == int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE) || // 上一个问题是文件消息
		rows[0].AskType == int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE)) && // 上一个问题是图片消息
		message.AskType != aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET { // 当前问题不是预设隐藏回答（满意度评价）
		// 如果上一个问题是文件或者图片，则用户当前输入的非满意度的问题会清空处理文件agent缓存
		return 0, aipb.RspGetAnswerWechat_QUESTION_TYPE_CLEAR_FILE_CACHE
	}

	return 0, aipb.RspGetAnswerWechat_QUESTION_TYPE_NORMAL
}

func SendChatEventSourceMgmt(ctx context.Context, userId uint64, event int32, content string, topic string) error {
	if userId == 0 {
		return fmt.Errorf("userId is 0")
	}

	if len(content) == 0 {
		return fmt.Errorf("data is empty")
	}

	jsonData, err := json.Marshal(aipb.ChatPublishEvent{
		Event:   event,
		Content: content,
		UserId:  userId,
	})
	if err != nil {
		return err
	}

	log.WithContext(ctx).Infow("SendChatEventSourceMgmt topic", "topic", topic, "json", string(jsonData))
	rp.Default.Publish(ctx, topic, string(jsonData))

	return nil
}

// DescribeDocReferences 获取doc对应的references
func DescribeDocReferences(ctx context.Context, docs []*model.TDoc) map[uint64]*model.TDoc {
	referenceDocs := make(map[uint64]*model.TDoc)
	var referenceIds []uint64
	var rows []*model.TDoc

	for _, doc := range docs {
		if doc.Ref != nil {
			for _, v := range doc.Ref.QaRef {
				if v.Id != 0 {
					if xslice.Contains(referenceIds, v.Id) == -1 {
						referenceIds = append(referenceIds, v.Id)
					}
				}
			}
		}
	}
	if len(referenceIds) > 0 {
		model.NewQuery[model.TDoc](ctx).DB().Where("id IN (?)", referenceIds).Find(&rows)
		for _, vv := range rows {
			referenceDocs[vv.ID] = vv
		}
	}
	return referenceDocs
}

// GetChatPictureList 返回未处理的图片，倒序排列
func GetChatPictureList(ctx context.Context, chatId, questionId uint64, reverse bool) ([]string, []uint64) {
	results := make([]struct {
		ID       uint64 `gorm:"id"`
		ImageUrl string `gorm:"image_url"`
		AskType  int32  `gorm:"ask_type"`
		State    int32  `gorm:"state"`
	}, 0)
	err := model.NewQuery[model.TChatMessage](ctx).
		Select("id", "JSON_EXTRACT(content,'$.image_url') as image_url", "ask_type", "state").
		Wheres(func(db *gorm.DB) {
			db.Where("ask_type in ?", []aipb.QuestionAskType{
				aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE, aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE,
			})
			db.Where("state = ?", aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND_DEFAULT)
			db.Where("create_date >= ?", time.Now().Add(-time.Hour))
			db.Where("chat_id = ?", chatId)
			db.Where("id < ?", questionId)
			db.Where("question_id = 0")
			db.Order("id desc")
		}).Scan(&results)
	if err != nil {
		log.WithContext(ctx).Errorw("GetChatPictureList err", "chatId", chatId, "err", err)
		return nil, nil
	}
	if len(results) == 0 {
		return nil, nil
	}
	var urls []string
	var fileQuestionIds []uint64
	for _, v := range results {
		if v.AskType != int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE) &&
			v.AskType != int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE) { // 回复之前1h内连续的文件/图片（中间不可间隔文字）
			break
		}
		if len(v.ImageUrl) == 0 || v.State != int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND_DEFAULT) {
			continue
		}
		var images []string
		if err = json.Unmarshal([]byte(v.ImageUrl), &images); err == nil && len(images) > 0 {
			fileQuestionIds = append(fileQuestionIds, v.ID)
			for _, image := range images {
				urls = append(urls, image)
			}
		}
	}
	log.WithContext(ctx).Infow("GetChatPictureList", "chatId", chatId, "urls", urls)
	if reverse {
		slices.Reverse(urls)
	}
	return urls, fileQuestionIds
}

func extractTaskLogs(logsByIndex map[int32][]*model.TChatLog, messageType int32) []*model.TChatLog {
	var taskLogs []*model.TChatLog
	for _, logs := range logsByIndex {
		var mainLog *model.TChatLog
		for _, l := range logs {
			if l.MessageType == messageType {
				mainLog = l
				break
			}
		}
		if mainLog == nil && len(logs) == 1 {
			mainLog = logs[0]
		}
		if mainLog != nil {
			taskLogs = append(taskLogs, mainLog)
		}
	}
	return taskLogs
}

func buildChatTasks(ctx context.Context, taskLogs []*model.TChatLog, answer *model.TChatMessage, hashId string) (*ChatTask, error) {
	var mainTask *ChatTask

	for i, tl := range taskLogs {
		reqText := tl.GetTChatLogRequestText()
		if reqText == nil {
			return nil, errors.New("invalid request text")
		}

		configSnap := tl.GetTChatLogConfig()
		if i == 0 {
			question := NewChatMessage(&aipb.ChatMessage{
				Id:            answer.QuestionID,
				ChatId:        answer.ChatID,
				QuestionId:    answer.QuestionID,
				CreateBy:      answer.CreateBy,
				Type:          aipb.ChatMessageType(tl.MessageType),
				PublishHashId: hashId,
			})
			if configSnap != nil {
				question.DefaultText = configSnap.DefaultText
				question.PromptPrefix = configSnap.PromptPrefix
			}
			question.ReqText = reqText
			mainTask = NewChatTask(ctx, ChatTask{
				FetchType:  determineFetchType(tl.MessageType),
				ReqMessage: question,
			})
		} else {
			mainTask.SetNextTask(&ChatTask{
				FetchType:       determineFetchType(tl.MessageType),
				NoHistoryRounds: true,
				BuildReqMessage: func(rspMessage *ChatMessage) *ChatMessage {
					if rspMessage != nil {
						question := NewChatMessage(&aipb.ChatMessage{
							ChatId:        rspMessage.ChatId,
							Text:          rspMessage.Text,
							QuestionId:    rspMessage.QuestionId,
							CreateBy:      rspMessage.CreateBy,
							AssistantId:   rspMessage.AssistantId,
							Lang:          rspMessage.Lang,
							AskType:       rspMessage.AskType,
							ImageUrl:      rspMessage.ImageUrl,
							PublishHashId: hashId,
						})
						if configSnap != nil {
							question.DefaultText = configSnap.DefaultText
							question.PromptPrefix = configSnap.PromptPrefix
						}
						return question
					}
					return nil
				},
			})
		}
	}
	return mainTask, nil
}

func determineFetchType(messageType int32) aipb.PipelineTaskFetchType {
	if messageType == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION) {
		return aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION
	}
	return aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT
}

// GetResendChatTask 获取重新发送的task
func GetResendChatTask(ctx context.Context, questionId uint64, hashId string) ([]*ChatTask, error) {
	var answers []*model.TChatMessage
	var tasks []*ChatTask
	if questionId == 0 {
		return tasks, nil
	}

	groupLogsByTaskIndex := func(logs []*model.TChatLog) map[int32][]*model.TChatLog {
		grouped := make(map[int32][]*model.TChatLog)
		for _, l := range logs {
			grouped[l.TaskIndex] = append(grouped[l.TaskIndex], l)
		}
		return grouped
	}

	if err := model.NewQuery[model.TChatMessage](ctx).DB().Where("question_id = ? and type in ?", questionId, MessageRequestValidType).Scopes(func(db *gorm.DB) *gorm.DB {
		return db.Preload("MessageLog")
	}).Find(&answers).Error; err != nil {
		return nil, err
	}

	for _, answer := range answers {
		if len(answer.MessageLog) == 0 {
			continue
		}

		taskLogsByIndex := groupLogsByTaskIndex(answer.MessageLog)
		taskLogs := extractTaskLogs(taskLogsByIndex, answer.Type)
		if len(taskLogs) == 0 {
			continue
		}

		mainTask, err := buildChatTasks(ctx, taskLogs, answer, hashId)
		if err != nil {
			log.WithContext(ctx).Errorw("GetResendChatTask buildChatTasks", "err", err)
			return nil, err
		}
		if mainTask != nil {
			log.WithContext(ctx).Infow("GetResendChatTask buildChatTasks", "mainTask req message", mainTask.ReqMessage)
			tasks = append(tasks, mainTask)
		}
	}
	return tasks, nil
}

func GetTextRecallQuery(assistant *aipb.AssistantDetail) string {
	if assistant == nil {
		return ""
	}
	// https://zq99299.github.io/note-book/elasticsearch-senior/depth-search/20-match-recall-precision.html#%E6%B7%B7%E5%90%88%E4%BD%BF%E7%94%A8-match-%E4%B8%8E%E8%BF%91%E4%BC%BC%E5%8C%B9%E9%85%8D
	// 直接用 match_phrase 短语搜索（包括 proximity match），会导致必须所有 term 都在 doc field 中出现
	if assistant.TextRecallQuery == aipb.TextRecallQuery_TEXT_RECALL_QUERY_QA {
		switch assistant.TextRecallPattern {
		case aipb.TextRecallPattern_TEXT_RECALL_PATTERN_MATCH:
			return fmt.Sprintf("{\"multi_match\":{\"query\":\"raw_str\",\"fields\":[\"question\",\"content\"],\"type\":\"best_fields\",\"slop\":%d}}", assistant.TextRecallSlop)
		case aipb.TextRecallPattern_TEXT_RECALL_PATTERN_MATCH_PHRASE:
			return fmt.Sprintf("{\"bool\":{\"must\":[{\"multi_match\":{\"query\":\"raw_str\",\"fields\":[\"content\",\"question\"],\"type\":\"best_fields\"}}],\"should\":[{\"multi_match\":{\"query\":\"raw_str\",\"fields\":[\"content\",\"question\"],\"type\":\"phrase\",\"slop\":%d,\"boost\":2.0}},{\"multi_match\":{\"query\":\"raw_str\",\"fields\":[\"content\",\"question\"],\"type\":\"phrase\",\"slop\":%d,\"boost\":1.0}},{\"multi_match\":{\"query\":\"raw_str\",\"fields\":[\"content\",\"question\"],\"type\":\"phrase\",\"slop\":%d,\"boost\":0.5}}]}}", assistant.TextRecallSlop, assistant.TextRecallSlop+5, assistant.TextRecallSlop+50)
		case aipb.TextRecallPattern_TEXT_RECALL_PATTERN_FUZZY:
			return fmt.Sprintf("{\"multi_match\":{\"query\":\"raw_str\",\"fields\":[\"question\",\"content\"],\"fuzziness\":\"AUTO\"}}")
		}
	} else if assistant.TextRecallQuery == aipb.TextRecallQuery_TEXT_RECALL_QUERY_Q {
		switch assistant.TextRecallPattern {
		case aipb.TextRecallPattern_TEXT_RECALL_PATTERN_MATCH:
			return fmt.Sprintf("{\"match\":{\"question\":\"raw_str\"}}")
		case aipb.TextRecallPattern_TEXT_RECALL_PATTERN_MATCH_PHRASE:
			return fmt.Sprintf("{\"bool\":{\"must\":[{\"match\":{\"question\":\"raw_str\"}}],\"should\":[{\"match_phrase\":{\"question\":{\"query\":\"raw_str\",\"slop\":%d,\"boost\":2.0}}},{\"match_phrase\":{\"question\":{\"query\":\"raw_str\",\"slop\":%d,\"boost\":1.0}}},{\"match_phrase\":{\"question\":{\"query\":\"raw_str\",\"slop\":%d,\"boost\":0.5}}}]}}", assistant.TextRecallSlop, assistant.TextRecallSlop+5, assistant.TextRecallSlop+50)
		case aipb.TextRecallPattern_TEXT_RECALL_PATTERN_FUZZY:
			return fmt.Sprintf("{\"match\":{\"question\":{\"query\":\"raw_str\",\"fuzziness\":\"AUTO\"}}}")
		}
	}
	return ""
}

func TransAgentTaskToChatTask(ctx context.Context, task *aipb.ChatAgentTask, question *aipb.ChatMessage, assistant *aipb.AssistantDetail) *ChatTask {
	var chatTask *ChatTask
	chatTask = &ChatTask{
		ctx:              ctx,
		Id:               task.Id,
		FetchType:        task.FetchType,
		NoHistoryRounds:  true,
		pbTask:           task,
		startAnswerIndex: task.StartAnswerIndex,
		BuildReqMessage: func(msg *ChatMessage) *ChatMessage {
			msg.closeChatOrSql = true
			msg.closeJudgePromptType = true
			pbTask := chatTask.pbTask
			if pbTask != nil {
				msg.PromptPrefix = pbTask.PromptPrefix
				msg.DefaultText = pbTask.DefaultText
				if task.Prompt != "" {
					msg.Text = pbTask.Prompt
				}
			}
			return msg
		},
	}
	if question != nil { // 主任务
		chatTask.ReqMessage = NewChatMessage(question).CloseChatOrSql()
		chatTask.ReqMessage.Task = &aipb.ChatMessageTask{
			PipelineId: task.PipelineId,
			TaskId:     task.Id,
			State:      aipb.PipelineTaskState_CHAT_AGENT_TASK_STATE_FINISHED,
		}
	}
	assistant.SuggestionConfig = task.SuggestionConfig
	if task.VisionModel != "" {
		assistant.VisionModel = task.VisionModel
	}
	return chatTask
}
