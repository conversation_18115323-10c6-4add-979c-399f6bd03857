package chat

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/buffered"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type MessageShowType int32

const (
	// MessageShowTypeNormal 正常
	MessageShowTypeNormal MessageShowType = 1
	// MessageShowTypeDisable 禁用
	MessageShowTypeDisable MessageShowType = 2
	// MessageShowTypeHidden 隐藏
	MessageShowTypeHidden MessageShowType = 3
)

const (
	MessageCMDReadBom        = "读配料表"
	MessageCMDReadBomPicture = "读配料表照片"
	MessageCMDDecipherBom    = "解读配料表"

	MessageCMDReadReport     = "读检测报告"
	MessageCMDDecipherReport = "解读检测报告"
)

var HistoryQuestionType = []aipb.QuestionAskType{
	aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL, aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION, aipb.QuestionAskType_QUESTION_ASK_TYPE_CONTINUE, aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
	aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE, aipb.QuestionAskType_QUESTION_ASK_TYPE_VOICE, aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE}

type ChatMessageOptions struct {
	noSuggestions   bool
	noHistoryRounds bool
}
type ChatMessageOptionFn func(message *ChatMessage)
type ChatMessage struct {
	// rpc 流转字段
	*aipb.ChatMessage
	// 仅在ai服务内部流转字段
	cursor, thirdRecordUUID, thirdUserUUID, liveAgent string
	Assistant                                         chatAssistant
	DbMessages                                        []*model.TChatMessage
	disableClearGptRefMark                            bool
	ReqText                                           interface{} `json:"req_text,omitempty"`
	StatePush                                         bool        `json:"state_push,omitempty"`
	TaskIndex                                         int         `json:"task_index,omitempty"` // task 索引
	DefaultText                                       string      `json:"default_text,omitempty"`
	closeChatOrSql                                    bool
	LogChan                                           *buffered.Chan[*model.TChatLog] // log记录
	closeJudgePromptType                              bool
	fetchResponseTime                                 time.Time
	textChan                                          *buffered.Chan[TextResult]
	suggestChan                                       *buffered.Chan[[]string]
	requestFiles                                      []*model.TChatMessageFile
	mu                                                sync.Mutex
	options                                           *ChatMessageOptions
	// 增加字段务必更新m.Copy()方法
}

func WithoutHistoryRounds(b bool) func(msg *ChatMessage) {
	return func(msg *ChatMessage) {
		msg.mu.Lock()
		defer msg.mu.Unlock()
		msg.options.noHistoryRounds = b
	}
}

func WithoutSuggestions(b bool) func(msg *ChatMessage) {
	return func(msg *ChatMessage) {
		msg.mu.Lock()
		defer msg.mu.Unlock()
		msg.options.noSuggestions = b
	}
}

func (m *ChatMessage) GetOptions() *ChatMessageOptions {
	if m.options == nil {
		m.options = &ChatMessageOptions{}
	}
	return m.options
}

func (m *ChatMessage) SetOption(fn ...ChatMessageOptionFn) {
	if m.options == nil {
		m.options = &ChatMessageOptions{}
	}
	for _, opt := range fn {
		opt(m)
	}
}

func (m *ChatMessage) SetOptions(opts *ChatMessageOptions) {
	m.options = opts
	if m.options == nil {
		m.options = &ChatMessageOptions{}
	}
}

func (m *ChatMessage) checkVisionText() bool {
	if m == nil || m.Text == "" || m.Text == "[]" || strings.Contains(m.Text, "NULL") { // 没有识别到内容
		return false
	}
	return true
}

func (m *ChatMessage) GetEmptyFileDefaultText() string {
	return config.GetStringOr("llm.chat.empty_file_default_text", "文件解析失败，恕我无法回答，请重新上传")
}

func (m *ChatMessage) GetDefaultErrText() string {
	if m.DefaultText != "" {
		return m.DefaultText
	}
	if m.Assistant.AssistantDetail != nil {
		return fmt.Sprintf("%s走神了，请过会儿再试试", m.Assistant.AssistantDetail.NickName)
	} else {
		return "小助手走神了，请过会儿再试试"
	}
}

// getMessageFiles 获取message解析成功的文件
func (m *ChatMessage) getMessageFiles(ctx context.Context) []*model.TChatMessageFile {
	var files []*model.TChatMessageFile
	model.NewQuery[model.TChatMessageFile](ctx).DB().Where("message_id = ?", m.QuestionId).Find(&files)
	return files
}

// fetchAndHandleAIAnswer 请求获取chat ai结果
func fetchAndHandleAIAnswer(ctx context.Context, assistant *aipb.AssistantDetail, question *ChatMessage, fetchType aipb.PipelineTaskFetchType) (*ChatMessage, error) {
	m := question
	log.WithContext(ctx).Infow("ChatMessage fetchAndHandleAIAnswer start", "question", m.ChatMessage, "options", m.GetOptions(), "fetchType", fetchType)
	ctx = context.WithoutCancel(ctx)
	var err error
	var message *ChatMessage
	var netErr net.Error
	m.StartTime = timestamppb.New(time.Now())
	defer func() {
		m.EndTime = timestamppb.New(time.Now())
	}()

	if err = m.validateChatMessageInput(assistant); err != nil {
		m.Text = m.GetDefaultErrText()
		return m, err
	}

	m.setChatMessageAssistant(assistant)
	files := m.getMessageFiles(ctx)

	if fetchType == 0 {
		fetchType = aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT
		if len(files) > 0 {
			fetchType = aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION
		}
	}

	switch fetchType {
	case aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_DEFAULT_ANSWER:
		m.Text = m.GetDefaultErrText()
		m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER
		return m, nil
	case aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT:
		message, err = m.chatRequestByOrder(ctx)
	case aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION:
		var successFiles []*model.TChatMessageFile
		for _, file := range files {
			if file.State == aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS {
				successFiles = append(successFiles, file)
			}
		}

		if len(successFiles) == 0 { // fetch type指定多模态，但没有文件解析成功返回固定文案
			m.Text = m.GetEmptyFileDefaultText()
			m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR
			return m, errors.New("fetch type vision has no files")
		}
		request := NewVisionRequest()
		request.autoPublish = true
		m.requestFiles = successFiles
		message, err = DoChatRagRequest(ctx, request, m.Copy())
	}

	if err != nil || message == nil {
		m.Text = m.GetDefaultErrText()
		if errors.Is(err, context.DeadlineExceeded) || errors.Is(err, context.Canceled) || (errors.As(err, &netErr) && netErr.Timeout()) {
			m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR
		} else {
			m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR
		}
		log.WithContext(ctx).Errorw(fmt.Sprintf("FetchAndHandleAIAnswer err %s, msg %+v", err.Error(), message))
		return m, err
	}

	log.WithContext(ctx).Infow("fetchAndHandleAIAnswer msg", "msg", message)
	// 从AI回答中获得的结果更新msg
	m.Type = message.Type
	m.Ugcs = message.Ugcs
	m.SqlQuery = message.SqlQuery
	m.Text = message.Text
	m.DocNames = message.DocNames
	m.Link = message.Link
	m.FinalQuery = message.FinalQuery
	m.ImageUrl = message.ImageUrl
	m.RefFileNames = message.RefFileNames
	m.DocMatchPattern = message.DocMatchPattern
	m.Think = message.Think
	m.PromptType = message.PromptType
	m.WaitAnswer = message.WaitAnswer
	m.textChan = message.textChan
	m.suggestChan = message.suggestChan
	return m, nil
}

func (m *ChatMessage) CopyToPbMessage() *aipb.ChatMessage {
	return proto.Clone(m.ChatMessage).(*aipb.ChatMessage)
}

func (m *ChatMessage) ToRefPushMsg() *aipb.ChatPushMessage {
	return &aipb.ChatPushMessage{}
}

type chatAssistant struct {
	*aipb.AssistantDetail
	IsSearchRewrite, withChatOrSql bool
}

func (m *ChatMessage) setChatMessageAssistant(assistant *aipb.AssistantDetail) *ChatMessage {
	ass := chatAssistant{
		AssistantDetail: assistant,
	}
	if assistant.SearchRewrite == 1 {
		ass.IsSearchRewrite = true
	}
	if assistant.ChatOrSql == 1 {
		ass.withChatOrSql = true
	}
	if m.PromptPrefix == "" {
		m.PromptPrefix = assistant.PromptPrefix
	} else {
		assistant.PromptPrefix = m.PromptPrefix
	}
	m.Assistant = ass
	m.AssistantId = assistant.Id
	return m
}

func (m *ChatMessage) Copy() *ChatMessage {
	newMessage := proto.Clone(m.ChatMessage).(*aipb.ChatMessage)
	return &ChatMessage{
		ChatMessage:            newMessage,
		Assistant:              m.Assistant,
		DbMessages:             m.DbMessages,
		disableClearGptRefMark: m.disableClearGptRefMark,
		ReqText:                m.ReqText,
		TaskIndex:              m.TaskIndex,
		DefaultText:            m.DefaultText,
		cursor:                 m.cursor,
		thirdRecordUUID:        m.thirdRecordUUID,
		thirdUserUUID:          m.thirdUserUUID,
		liveAgent:              m.liveAgent,
		StatePush:              m.StatePush,
		LogChan:                m.LogChan,
		closeChatOrSql:         m.closeChatOrSql,
		closeJudgePromptType:   m.closeJudgePromptType,
		textChan:               m.textChan,
		suggestChan:            m.suggestChan,
		requestFiles:           m.requestFiles,
		options:                m.options,
	}
}

func NewChatMessage(message *aipb.ChatMessage) *ChatMessage {
	newMessage := proto.Clone(message).(*aipb.ChatMessage)
	return &ChatMessage{
		ChatMessage: newMessage,
	}
}

func (m *ChatMessage) SetDisableClearGptRefMark() *ChatMessage {
	m.disableClearGptRefMark = true
	return m
}

func (m *ChatMessage) CloseChatOrSql() *ChatMessage {
	m.closeChatOrSql = true
	return m
}

// SetWorkWeiXinRecordValue 设置微信需要记录的值
func (m *ChatMessage) SetWorkWeiXinRecordValue(cursor, thirdRecordUUID, thirdUserUUID, liveAgent string) *ChatMessage {
	m.cursor = cursor
	m.thirdRecordUUID = thirdRecordUUID
	m.thirdUserUUID = thirdUserUUID
	m.liveAgent = liveAgent
	return m
}

func (m *ChatMessage) SetText(text string) *ChatMessage {
	m.Text = text
	return m
}

// ChangeStateAndPushEvent 推送中间状态event msg
// func (m *ChatMessage) ChangeStateAndPushEvent(ctx context.Context, state int32) error {
//	var err error
//	if _, err = model.NewQuery[model.TChatMessage](ctx).UpdateByKey(m.QuestionId, map[string]any{
//		"state": state}); err != nil {
//		return err
//	}
//	m.State = aipb.ChatMessageState(state)
//	eventMsg := &aipb.EventChatMessage{
//		Id:          m.Id,
//		ChatId:      m.ChatId,
//		Type:        m.Type,
//		Link:        m.Link,
//		QuestionId:  m.QuestionId,
//		CreateDate:  m.CreateDate,
//		Lang:        m.Lang,
//		AssistantId: m.AssistantId,
//		State:       m.State,
//	}
//	hashMsg := EventMessageTransformWithHash(eventMsg, false)
//	r, err := json.Marshal(hashMsg)
//	if err != nil {
//		return err
//	}
//
//	if err = SendChatEventSource(ctx, m.CreateBy, int32(basepb.AIEventSource_AI_CHAT), string(r), m.PublishHashId); err != nil {
//		return err
//	}
//	return nil
// }

func (m *ChatMessage) validateChatMessageInput(assistant *aipb.AssistantDetail) error {
	if assistant.Model == "" {
		return errors.New("model is required")
	}
	if assistant.CollectionName == "" {
		return errors.New("collectionName is required")
	}
	if assistant.Threshold == 0 {
		return errors.New("threshold is required")
	}
	if assistant.ChatOrSql == 1 && assistant.SqlModel == "" {
		return errors.New("sqlModel is required")
	}
	if !assistant.CloseSearch && assistant.SearchEngine == "" {
		return errors.New("searchEngine is required")
	}
	return nil
}

func EventMessageTransformWithHash(m *aipb.EventChatMessage, isOnlySearch bool) *aipb.EventChatHashMessage {
	pbMessage := &aipb.EventChatHashMessage{
		ChatId:           handleHashId(m.ChatId),
		Id:               handleHashId(m.Id),
		CreateDate:       m.CreateDate,
		RatingScale:      m.RatingScale,
		Type:             m.Type,
		QuestionId:       handleHashId(m.QuestionId),
		Ugcs:             make([]*aipb.EventChatHashMessage_EventMessageUgc, 0),
		Text:             m.Text,
		Link:             m.Link,
		State:            int32(m.State),
		Lang:             m.Lang,
		AssistantId:      handleHashId(m.AssistantId),
		ProcessTime:      m.ProcessTime,
		DocFinalQuery:    m.DocFinalQuery,
		SuggestCount:     m.SuggestCount,
		SuggestQuestions: m.SuggestQuestions,
		ShowType:         m.ShowType,
		DocMatchPattern:  m.DocMatchPattern,
		SuggestionMode:   m.SuggestionMode,
		Think:            m.Think,
	}
	if isOnlySearch {
		pbMessage.CustomQuestionId = m.QuestionId
		pbMessage.SqlQuery = m.SqlQuery
	}
	// 处理docs
	for _, v := range m.Docs {
		pbDoc := &aipb.EventChatHashMessage_EventMessageDoc{
			UgcId:       v.UgcId,
			UgcType:     v.UgcType,
			Id:          v.Id,
			RagFilename: v.RagFilename,
			DataType:    v.DataType,
			Reference:   v.Reference,
		}
		for _, contributor := range v.Contributor {
			pbDoc.Contributor = append(pbDoc.Contributor, &aipb.EventChatHashMessage_EventContributor{
				Id:          handleHashId(contributor.Id),
				Text:        contributor.Text,
				Type:        contributor.Type,
				Level:       contributor.Level,
				IsPublished: contributor.IsPublished,
				FullName:    contributor.FullName,
			})
		}
		pbMessage.Docs = append(pbMessage.Docs, pbDoc)
	}
	// 处理ugc
	for _, messageUgc := range m.Ugcs {
		pbUgc := &aipb.EventChatHashMessage_EventMessageUgc{
			UgcType:   messageUgc.UgcType,
			IsUgcLink: messageUgc.IsUgcLink,
		}
		for _, ugc := range messageUgc.Cards {
			pbUgc.Cards = append(pbUgc.Cards, &aipb.EventChatHashMessage_EventMessageUgcCard{
				Id:      handleHashId(ugc.Id),
				Name:    ugc.Name,
				LogoUrl: ugc.LogoUrl,
			})
		}
		var ugcIds []string
		for _, id := range messageUgc.UgcIds {
			ugcIds = append(ugcIds, handleHashId(id))
		}
		pbUgc.UgcIds = ugcIds
		eventFilter := make([]*aipb.EventChatHashMessage_EventMessageFilter, 0, len(messageUgc.Filter))
		for _, item := range messageUgc.Filter {
			eventTag := make([]*aipb.EventChatHashMessage_EventMessageTag, 0, len(item.Tags))
			for _, tag := range item.Tags {
				eventTag = append(eventTag, &aipb.EventChatHashMessage_EventMessageTag{
					Id:           handleHashId(tag.Id),
					TaggableType: tag.TaggableType,
					Name:         tag.Name,
					Type:         tag.Type,
				})
			}
			eventFilter = append(eventFilter, &aipb.EventChatHashMessage_EventMessageFilter{
				Field: item.Field,
				Value: item.Value,
				Tags:  eventTag,
			})
		}
		pbUgc.Filter = eventFilter
		pbMessage.Ugcs = append(pbMessage.Ugcs, pbUgc)
	}
	return pbMessage
}

// TransformToEventPb 转换event source消息
func (m *ChatMessage) TransformToEventPb(ctx context.Context) *aipb.EventChatMessage {
	process := &basepb.TimeRange{
		Start: m.StartTime,
		End:   m.EndTime,
	}
	pbMessage := &aipb.EventChatMessage{
		ChatId:        m.ChatId,
		Id:            m.Id,
		CreateDate:    m.CreateDate,
		RatingScale:   m.RatingScale,
		Type:          m.Type,
		QuestionId:    m.QuestionId,
		Ugcs:          m.Ugcs,
		Text:          m.Text,
		Link:          m.Link,
		SqlQuery:      m.SqlQuery,
		ProcessTime:   process,
		Lang:          m.Lang,
		AssistantId:   m.AssistantId,
		Think:         m.Think,
		ThinkDuration: m.ThinkDuration,
	}

	// 处理docs
	docNames := m.DocNames
	messageDocs := make([]*aipb.ChatMessageDoc, 0, len(docNames))
	if len(docNames) > 0 {
		docIds := make([]uint64, 0, len(docNames))
		docs, err := DescribeMessageDocs(ctx, docNames)
		if err != nil {
			return nil
		}
		// references := DescribeDocReferences(ctx, docs)
		for _, doc := range docs {
			if xstrings.In(doc.RagFilename, docNames) {
				docIds = append(docIds, doc.ID)
				msg := doc.TransformTDocToMessageDoc()
				if msg != nil {
					messageDocs = append(messageDocs, msg)
				}
			}
		}
		db := model.NewQuery[model.TDoc](ctx)
		if err := db.DB().WithContext(ctx).Model(&model.TDoc{}).
			Where("id IN (?)", docIds).
			Update("hit_count", gorm.Expr("hit_count + ?", 1)).Error; err != nil {
			log.Errorw("FetchCreateAIMessage hit_count add err", "err", err)
		}
		pbMessage.Docs = messageDocs
	}

	return pbMessage
}

func (m *ChatMessage) TransformToTMessage() (*model.TChatMessage, error) {
	dbMessage := &model.TChatMessage{
		ChatID:       m.ChatId,
		Type:         int32(m.Type),
		QuestionID:   m.QuestionId,
		CreateBy:     m.CreateBy,
		UpdateBy:     m.CreateBy,
		RejectReason: m.RejectReason,
		RatingScale:  int32(m.RatingScale),
		AssistantID:  m.AssistantId,
		Lang:         m.Lang,
		AskType:      int32(m.AskType),
		State:        int32(m.State),
		ShowType:     m.ShowType,
		MatchPattern: m.DocMatchPattern,
		PromptType:   m.PromptType,
		HashId:       m.PublishHashId,
	}

	// TChatMessageFile
	if len(m.Files) > 0 {
		for _, file := range m.Files {
			dbMessage.Files = append(dbMessage.Files, &model.TChatMessageFile{
				URL:       file.Url,
				ParsedURL: file.ParsedUrl,
				State:     file.State,
			})
		}
	}

	// TChatSuggest
	if len(m.SuggestQuestion) > 0 {
		for _, sug := range m.SuggestQuestion {
			dbMessage.Suggests = append(dbMessage.Suggests, model.TChatSuggest{
				Suggest: sug,
				Mode:    uint32(m.SuggestionMode),
			})
		}
	}

	// TChatMessageCollection
	if m.CollectionSnapshot != nil {
		items, err := json.Marshal(m.CollectionSnapshot.Items)
		if err != nil {
			return nil, err
		}
		start := m.CollectionSnapshot.StartTime.AsTime()
		end := m.CollectionSnapshot.EndTime.AsTime()
		dbMessage.Collections = &model.TChatMessageCollection{
			Content:     string(items),
			EndTime:     &end,
			StartTime:   &start,
			CleanChunks: m.CollectionSnapshot.CleanChunks,
		}
	}

	// TChatMessageTask
	if m.Task != nil {
		dbMessage.Task = &model.TChatMessageTask{
			ChatID:     m.ChatId,
			PipelineID: m.Task.PipelineId,
			TaskID:     m.Task.TaskId,
			State:      int32(m.Task.State),
		}
	}

	// TChatMessageThink
	if m.Think != "" {
		dbMessage.Think = &model.TChatMessageThink{
			Content:  m.Think,
			Duration: uint32(m.ThinkDuration),
		}
	}

	// TChatMessageLog
	if m.Logs != nil {
		for _, messageLog := range m.Logs {
			cl := &model.TChatLog{
				SQLQuery:       messageLog.SqlQuery,
				Enhancement:    messageLog.Enhancement,
				Gpt:            messageLog.Gpt,
				Ref:            messageLog.Ref,
				Code:           messageLog.Code,
				StartTime:      messageLog.StartTime.AsTime(),
				EndTime:        messageLog.EndTime.AsTime(),
				ConfigSnapshot: messageLog.ConfigSnapshot,
				MessageType:    int32(messageLog.Type),
				RequestText:    messageLog.RequestText,
			}
			if messageLog.FetchRespTime != nil {
				t := messageLog.FetchRespTime.AsTime()
				cl.FetchRespTime = &t
			}
			dbMessage.MessageLog = append(dbMessage.MessageLog, cl)
		}
	}

	// TChatMessageDoc
	for _, s := range m.DocNames {
		dbMessage.Docs = append(dbMessage.Docs, model.TChatMessageDoc{
			RagFilename: s,
		})
	}

	if m.LastOperator != nil {
		dbMessage.LastOperation = &model.TChatOperation{
			OperationType:   uint32(m.LastOperator.OperationType),
			StopText:        m.LastOperator.StopText,
			StopThink:       m.LastOperator.StopThink,
			StopChunkState:  m.LastOperator.StopChunkState,
			OperationParams: datatypes.JSON(m.LastOperator.OperationParams),
			HashID:          m.LastOperator.HashId,
		}
	}

	if m.Id > 0 {
		dbMessage.ID = m.Id
		dbMessage.CreateDate = time.Now()
		dbMessage.UpdateDate = time.Now()
	}

	if m.EndTime != nil {
		end := m.EndTime.AsTime()
		dbMessage.EndTime = &end
	}

	if m.StartTime != nil {
		start := m.StartTime.AsTime()
		dbMessage.StartTime = &start
	}

	if dbMessage.AskType == 0 {
		dbMessage.AskType = int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL)
	}

	content := &aipb.ChatMessageContent{
		Text:       m.Text,
		Link:       m.Link,
		SqlQuery:   m.SqlQuery,
		Ugcs:       m.Ugcs,
		ImageUrl:   m.ImageUrl,
		FinalQuery: m.FinalQuery,
	}

	r, err := json.Marshal(content)
	if err != nil {
		return nil, err
	}
	dbMessage.Content = r
	dbMessage.ThirdRecordUUID = m.thirdRecordUUID
	dbMessage.ThirdUserUUID = m.thirdUserUUID
	dbMessage.NextCursor = m.cursor
	dbMessage.LiveAgentName = m.liveAgent

	return dbMessage, nil
}

//func (m *ChatMessage) startConsumeSubscribe(ctx context.Context, sqlRequest *SqlRequest, docRequest *CollectionRequest, searchRequest *SearchRequest) {
//	var sqlMsg, docMsg, searchMsg *ChatMessage
//	sqlReceived, docReceived, searchReceived := false, false, false
//	//if !m.Assistant.withChatOrSql || m.closeChatOrSql {
//	//	sqlReceived = true
//	//}
//	sqlReceived = true
//
//	ctx2, cancel := context.WithTimeout(context.Background(), 60*time.Second)
//	defer func() {
//		cancel()
//		sqlRequest.closeRefChan()
//		docRequest.closeRefChan()
//		searchRequest.closeRefChan()
//	}()
//	ticker := time.NewTicker(50 * time.Millisecond) // 用于定期检查是否跳出循环
//	defer ticker.Stop()
//
//	for {
//		select {
//		case <-ctx2.Done():
//			log.WithContext(ctx).Errorw("startConsume timeout nobody Consume!", "hashId", m.ChatMessage.PublishHashId)
//			return
//		case docMsg = <-docRequest.refChan:
//			if docMsg != nil {
//				log.WithContext(ctx).Infow("startConsume receive docMsg", "docMsg", fmt.Sprintf("%v", docMsg))
//			}
//			docReceived = true
//		case searchMsg = <-searchRequest.refChan:
//			if searchMsg != nil {
//				log.WithContext(ctx).Infow("startConsume receive searchMsg", "searchMsg", fmt.Sprintf("%v", searchMsg))
//			}
//			searchReceived = true
//		case sqlMsg = <-sqlRequest.refChan:
//			log.WithContext(ctx).Infow("startConsume receive sqlMsg", "sqlMsg", fmt.Sprintf("%v", sqlMsg))
//			sqlReceived = true
//		case <-ticker.C:
//			if sqlMsg != nil {
//				return
//			}
//			if sqlReceived && docReceived && (docMsg != nil || m.Assistant.CloseSearch) {
//				docRequest.StartConsume(ctx, true)
//				searchRequest.StartConsume(ctx, false)
//				return
//			}
//			if sqlReceived && docReceived && searchReceived {
//				searchRequest.StartConsume(ctx, true)
//				docRequest.StartConsume(ctx, false)
//				return
//			}
//		}
//	}
//}

func (m *ChatMessage) getCollectionMissReply() string {
	var text string
	if m.Assistant.MissReply != "" {
		text = m.Assistant.MissReply
	} else {
		text = "知识库中没有找到相关知识"
	}
	return text
}

func (m *ChatMessage) getChatDefaultErrMessage() *ChatMessage {
	defaultMsg := m.Copy()
	defaultMsg.Text = m.GetDefaultErrText()
	defaultMsg.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR
	defaultMsg.WaitAnswer = true
	return defaultMsg
}

func (m *ChatMessage) getCollectionDefaultMessage() *ChatMessage {
	defaultMsg := m.Copy()
	defaultMsg.Text = m.getCollectionMissReply()
	defaultMsg.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION
	return defaultMsg
}

// chatRequestByOrder 获取ai请求的message
func (m *ChatMessage) chatRequestByOrder(ctx context.Context) (*ChatMessage, error) {
	var sqlMsg, docMsg *ChatMessage
	var docErr error
	var sqlRequest *SqlRequest

	searchRequest := NewSearchRequest() // 初始化request的start chan
	docRequest := NewCollectionRequest()
	sqlRequest = NewSqlRequest()

	//xsync.SafeGo(context.Background(), func(ctx context.Context) error {
	//	m.startConsumeSubscribe(ctx, sqlRequest, docRequest, searchRequest)
	//	return nil
	//}, boot.TraceGo(ctx))

	if !m.GetOptions().noHistoryRounds && m.ReqText == nil && m.Type != aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION {
		messages, err := DescribeChatHistoryMessages(ctx, m.ChatId, m.Assistant.HistoryRounds-1, 0, &DescribeChatHistoryMessagesParams{AskTypes: HistoryQuestionType, IgnoreQuestionIds: []uint64{m.QuestionId, m.HistoryIgnoreId}})
		if err == nil && len(messages) > 0 {
			m.DbMessages = messages
		}
	}

	wg := xsync.NewGroup(context.Background(), xsync.GroupGoOption(boot.TraceGo(ctx)))
	if m.Assistant.withChatOrSql && !m.closeChatOrSql {
		wg.SafeGo(func(ctx context.Context) error {
			if msg, err := DoChatRagRequest(ctx, sqlRequest, m.Copy()); err != nil {
				log.Errorw("receive handleChatOrSqlRequest err", "err", err)
			} else {
				sqlMsg = msg
			}
			return nil
		})
	}
	wg.SafeGo(func(ctx context.Context) error {
		docMsg, docErr = DoChatRagRequest(ctx, docRequest, m.Copy())
		return nil
	})

	searchMsgChan := make(chan struct {
		msg *ChatMessage
		err error
	})
	if !m.Assistant.CloseSearch {
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			defer close(searchMsgChan)
			searchMsg, searchErr := DoChatRagRequest(ctx, searchRequest, m.Copy())
			searchMsgChan <- struct {
				msg *ChatMessage
				err error
			}{searchMsg, searchErr}
			return nil
		}, boot.TraceGo(ctx))
	} else {
		close(searchMsgChan)
	}

	wg.Wait()
	if sqlMsg != nil {
		return sqlMsg, nil
	}
	if docErr == nil && docMsg != nil { // 1. doc命中返回doc msg
		//docRequest.setRefChan(docMsg)
		docRequest.StartConsume(ctx, true)
		searchRequest.StartConsume(ctx, false)
		return docMsg, nil
	}
	if docErr != nil && m.Assistant.CloseSearch { // 2. 助手关闭互联网强行返回doc msg
		docRequest.StartConsume(ctx, true)
		return m.getCollectionDefaultMessage(), nil
	}
	//docRequest.setRefChan(nil)
	search := <-searchMsgChan
	if search.err != nil {
		return search.msg, search.err
	}
	docRequest.StartConsume(ctx, false)
	searchRequest.StartConsume(ctx, true)
	//searchRequest.setRefChan(search.msg)
	return search.msg, nil
}

// GetMessageMaxUpdateDateByChatID 根据chatID获取对话最后更新时间
func GetMessageMaxUpdateDateByChatID(ctx context.Context, chatIds []uint64) (map[uint64]time.Time, error) {
	var updateDateInfo = make([]*struct {
		ChatId     uint64    `json:"chat_id"`
		UpdateDate time.Time `json:"update_date"`
	}, 0)
	err := model.NewQuery[model.TChatMessage](ctx).Select("chat_id", "max(update_date) as update_date").
		Where("chat_id in ?", chatIds).GroupBy("chat_id", nil).Scan(&updateDateInfo)
	if err != nil {
		return nil, err
	}
	if len(updateDateInfo) == 0 {
		return nil, nil
	}

	result := make(map[uint64]time.Time, len(updateDateInfo))
	for _, info := range updateDateInfo {
		result[info.ChatId] = info.UpdateDate
	}
	return result, nil
}

func UpdateMessage(ctx context.Context, messageId uint64, updateData map[string]interface{}) error {
	var masks []string
	for field := range updateData {
		masks = append(masks, field)
	}

	query := model.NewQuery[model.TChatMessage](ctx)
	_, err := query.Select(masks).UpdateByKey(messageId, updateData)
	if err != nil {
		return err
	}
	return nil
}

func (m *ChatMessage) receiveLogChan(ctx context.Context, dbMsg *model.TChatMessage, logChan *buffered.Chan[*model.TChatLog]) {
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		logChan.OnClose = func() {
			log.WithContext(ctx).Infow("log processing timeout reached, closing logChan")
		}

		logChan.Consume(func(l *model.TChatLog) {
			l.MessageID = dbMsg.ID
			if err := model.NewQuery[model.TChatLog](ctx).Create(l); err != nil {
				log.WithContext(ctx).Errorw("save chat request log failed", "err", err)
			}
		})
		return nil
	}, boot.TraceGo(ctx))
}

func (m *ChatMessage) CreateMessageSuggestion(ctx context.Context, question string, answer string, assistant *aipb.AssistantDetail) []string {
	return CreateModeSuggestion(ctx, question, answer, assistant, m.CreateBy, m.Id)
}

func (m *ChatMessage) receiveTextChan(ctx context.Context, dbMsg *model.TChatMessage, textChan *buffered.Chan[TextResult]) {
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		go func() {
			time.AfterFunc(180*time.Second, func() {
				textChan.Close()
			})
		}()

		handler := func(result TextResult) {
			if result.text != "" {
				log.WithContext(ctx).Infow("textChan receive full text", "text", result.text, "think", result.think)
				if err := UpdateMessageTextAndThink(ctx, dbMsg, result.text, result.think, result.messageType); err != nil {
					log.WithContext(ctx).Errorw("save chat updateMessageText failed", "err", err)
				}
			}
		}
		if err := textChan.Consume(handler); err != nil {
			log.WithContext(ctx).Errorw("save chat updateMessageText failed", "err", err)
		}
		return nil
	}, boot.TraceGo(ctx))
}

func (m *ChatMessage) receiveSuggestChan(ctx context.Context, dbMsg *model.TChatMessage, suggestChan *buffered.Chan[[]string]) {
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		go func() {
			time.AfterFunc(180*time.Second, func() {
				suggestChan.Close()
			})
		}()

		handler := func(result []string) {
			if len(result) > 0 {
				rows := make([]*model.TChatSuggest, 0)
				for _, suggest := range result {
					rows = append(rows, &model.TChatSuggest{
						MessageID: dbMsg.ID,
						Suggest:   suggest,
						Mode:      uint32(m.Assistant.SuggestionConfig.Mode),
					})
				}
				if err := model.NewQuery[model.TChatSuggest](ctx).Insert(rows); err != nil {
					log.WithContext(ctx).Errorw("save chat suggestion failed", "err", err)
				}
			}
		}
		if err := suggestChan.Consume(handler); err != nil {
			log.WithContext(ctx).Errorw("save chat updateMessageText failed", "err", err)
		}
		return nil
	}, boot.TraceGo(ctx))
}

func (m *ChatMessage) checkMessageSaveValid() error {
	if m.AssistantId == 0 {
		return xerrors.ValidationError("assistant_id is required")
	}
	return nil
}

func (m *ChatMessage) Save(ctx context.Context) (*model.TChatMessage, error) {
	var dbm *model.TChatMessage
	var err error

	if err = m.checkMessageSaveValid(); err != nil {
		log.WithContext(ctx).Errorw("[ChatMessage] Save checkMessageSaveValid failed", "err", err)
		return nil, err
	}

	dbm, err = m.TransformToTMessage()
	if err != nil {
		return nil, err
	}
	log.WithContext(ctx).Infow("[ChatMessage] chat message Save() in", "msg", m.ChatMessage, "dbm", dbm)
	dbm.State = int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND)
	if err = model.Transaction(ctx, func(tx *gorm.DB) error {
		var existingMsg model.TChatMessage
		now := time.Now()

		if dbm.QuestionID > 0 {
			// 更新question状态
			if err = tx.Model(&model.TChatMessage{}).Where("id = ?", m.QuestionId).
				Updates(&model.TChatMessage{State: int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_UNSENT), EndTime: &now, HashId: m.PublishHashId}).Error; err != nil {
				return err
			}

			// 更新answer回答内容
			result := tx.Model(&model.TChatMessage{}).Where("hash_id = ? AND question_id = ? AND type = ?", dbm.HashId, dbm.QuestionID, aipb.ChatMessageType_CHAT_MESSAGE_TYPE_DRAFT).First(&existingMsg)
			if result.Error == nil { // 记录存在，更新它
				dbm.ID = existingMsg.ID
				dbm.CreateDate = existingMsg.CreateDate
				dbm.UpdateDate = now
				if err = tx.Save(dbm).Error; err != nil {
					return err
				}
				return nil
			} else if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return result.Error
			}
		}
		// 创建新记录: 1.answer记录不存在 2.问题
		if err = tx.Create(dbm).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}

	// 保存log
	if m.LogChan != nil {
		m.receiveLogChan(ctx, dbm, m.LogChan)
	}

	if m.textChan != nil {
		m.receiveTextChan(ctx, dbm, m.textChan)
	}

	if m.suggestChan != nil {
		m.receiveSuggestChan(ctx, dbm, m.suggestChan)
	}

	m.Id = dbm.ID
	m.State = aipb.ChatMessageState(dbm.State)
	return dbm, nil
}
