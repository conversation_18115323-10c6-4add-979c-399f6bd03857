package chat

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
)

type SqlUtils struct {
	filterResult  []*aipb.ChatMessageContentFilterItem
	ctx           context.Context
	conditionTags []*aipb.MessageTag
	sqlQuery      string
	ugcType       basepb.DataType
}

func NewSqlUtils() *SqlUtils {
	return &SqlUtils{
		filterResult:  []*aipb.ChatMessageContentFilterItem{},
		conditionTags: []*aipb.MessageTag{},
	}
}

func (s *SqlUtils) withContext(ctx context.Context) *SqlUtils {
	s.ctx = ctx
	return s
}

var fieldMappings = map[string][]string{
	"keywords_team":      {"full_name", "brief_intro"},
	"keywords_programs":  {"name", "introduction"},
	"keywords_solutions": {"name", "brief_intro", "economic_benefit", "carbon_benefit", "innovation"},
	"keywords_knowledge": {"name", "introduction"},
}

var programTagTypeMappings = map[string]tagpb.TaggableType{
	"type_tags":                 tagpb.TaggableType_TAGGABLE_TYPE_RESOURCE_TYPE,
	"industry_tags":             tagpb.TaggableType_TAGGABLE_TYPE_RESOURCE_AUDIENCE_INDUSTRY,
	"industry_app_team_tags":    tagpb.TaggableType_TAGGABLE_TYPE_TEAM_INDUSTRY_RECOGNITION,
	"industry_app_product_tags": tagpb.TaggableType_TAGGABLE_TYPE_PRODUCT_INDUSTRY_RECOGNITION,
}

var knowledgeTagTypeMappings = map[string]tagpb.TaggableType{
	"suit_industry_tags": tagpb.TaggableType_TAGGABLE_TYPE_SUIT_INDUSTRY,
	"type_tags":          tagpb.TaggableType_TAGGABLE_ATLAS_TYPE,
}

var solutionsTagTypeMappings = map[string]tagpb.TaggableType{
	"user_oriented_tags":        tagpb.TaggableType_TAGGABLE_TYPE_PRODUCT_TECHNOLOGY_USER_ORIENTED,
	"industry_recognition_tags": tagpb.TaggableType_TAGGABLE_TYPE_PRODUCT_INDUSTRY_RECOGNITION,
	"applicable_industry_tags":  tagpb.TaggableType_TAGGABLE_TYPE_PRODUCT_APPLICABLE_INDUSTRY,
}

var teamTagTypeMappings = map[string]tagpb.TaggableType{
	"type_tags":           tagpb.TaggableType_TAGGABLE_TYPE_TEAM_TYPE,
	"nature_tag":          tagpb.TaggableType_TAGGABLE_TYPE_TEAM_ATTRIBUTES,
	"industry_tags":       tagpb.TaggableType_TAGGABLE_TYPE_TEAM_INDUSTRY,
	"financing_stage_tag": tagpb.TaggableType_TAGGABLE_TYPE_AUDIENCE_TEAM_FINANCING_STAGE,
}
var locationSql = "id IN (SELECT `data_id` FROM `db_tanlive_support`.`v_address_place` WHERE `place_id` IN (SELECT `id` FROM `db_tanlive_support`.`t_place` WHERE `name` %s %s UNION SELECT `place_id` FROM `db_tanlive_support`.`t_place_localization` WHERE `name` %s %s) AND `data_type` = %d AND `data_field` = '%s')"
var taggableSql = "id IN (SELECT taggable.taggable_id FROM db_tanlive_tag.t_taggable taggable,db_tanlive_tag.t_tag tag WHERE tag.id = taggable.tag_id AND taggable.data_type = %d AND tag.name LIKE %s AND taggable.taggable_type = %d)"
var productLocationSwitchSql = "team_id IN (SELECT `data_id` FROM `db_tanlive_support`.`v_address_place` WHERE `place_id` IN (SELECT `id` FROM `db_tanlive_support`.`t_place` WHERE `name` %s %s UNION SELECT `place_id` FROM `db_tanlive_support`.`t_place_localization` WHERE `name` %s %s) AND `data_type` = %d AND `data_field` = '%s')"

// 查询标签
var tagPrefix = "SELECT DISTINCT taggable.`taggable_type`, tag.`id`, tag.`type`, tag.`name`, taggable.`data_type` FROM db_tanlive_tag.t_taggable taggable JOIN db_tanlive_tag.t_tag tag ON tag.id = taggable.tag_id WHERE %s"
var taggablePerSql = "(taggable.data_type = %d AND tag.name LIKE %s AND taggable.taggable_type = %d)"

var teamLocationTypeMappings = map[string]string{
	"location_addresses":       "location",
	"service_region_addresses": "service_region",
}

var programLocationTypeMappings = map[string]string{
	"location":       "location",
	"target_regions": "target_regions",
}

var productLocationTypeMappings = map[string]string{
	"service_region_addresses": "service_region",
}

var tableMappings = map[string]basepb.DataType{
	"v_published_team":    basepb.DataType_DATA_TYPE_TEAM,
	"v_res_planet":        basepb.DataType_DATA_TYPE_RESOURCE,
	"v_atlas":             basepb.DataType_DATA_TYPE_GRAPH,
	"v_published_product": basepb.DataType_DATA_TYPE_PRODUCT,
}

var ugcLogoUrlMap = map[basepb.DataType][]string{
	basepb.DataType_DATA_TYPE_RESOURCE: {"name", "image_url"},
	basepb.DataType_DATA_TYPE_GRAPH:    {"name", "image_url"},
	basepb.DataType_DATA_TYPE_PRODUCT:  {"name", "media"},
	basepb.DataType_DATA_TYPE_TEAM:     {"short_name", "logo_url"},
}

var commonRegex = `(?:\b(\w+)\s)?(?:\b(\w+)\.)?(\w+)\s+(=|LIKE|<=|>=|<|>)\s+('[^']*'|CURRENT_DATE|\d+|\(.*?\))`
var commonReplace = "id > 0"

// executeSQL 执行 SQL 语句
func (s *SqlUtils) executeSQL(ctx context.Context, query string) ([]map[string]interface{}, error) {
	var results []map[string]interface{}
	err := model.NewConnectionV1(ctx).Raw(fmt.Sprintf("%s", query)).Scan(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// withTableType 通过 SQL 表名查询UGC类型
func (s *SqlUtils) withTableType(query string) *SqlUtils {
	tableNameRegex := `(?i)SELECT\s+\*\s+FROM\s+(\w+)`
	re, _ := regexp.Compile(tableNameRegex)
	matches := re.FindStringSubmatch(query)
	if len(matches) > 1 {
		tableName := matches[1]
		if tableType, exists := tableMappings[tableName]; exists {
			s.ugcType = tableType
		}
	}
	return s
}

func (s *SqlUtils) executeTagsSQL(ctx context.Context, sql string) []*aipb.MessageTag {
	var tags []*aipb.MessageTag
	err := model.NewConnection(ctx).Raw(fmt.Sprintf(tagPrefix, sql)).Scan(&tags).Error
	if err != nil {
		return tags
	}
	return tags
}

func (s *SqlUtils) tagMapToSlice(tags map[uint64]*aipb.MessageTag) []*aipb.MessageTag {
	var uniqueTags []*aipb.MessageTag
	for _, tag := range tags {
		uniqueTags = append(uniqueTags, tag)
	}

	return uniqueTags
}

func (s *SqlUtils) spliceUgcTags(ugcId uint64, tags []*aipb.MessageTag) string {
	ugcTags := make([]string, 0, len(tags))
	for _, tag := range tags {
		if tag.TaggableId == ugcId {
			ugcTags = append(ugcTags, tag.Name)
		}
	}
	return strings.Join(ugcTags, ",")
}

// replaceConditions 替换条件
func (s *SqlUtils) replaceConditions(newConditionMap map[string]string, query string) string {
	for key, condition := range newConditionMap {
		query = strings.ReplaceAll(query, key, condition)
	}
	query = s.fixSQLError(query)

	return query
}

// buildTagConditions 通过sql中的tag查询条件查询哪些标签是平台中真实存在的
func (s *SqlUtils) buildTagConditions(conditions []string) {
	var taggableTypeMapping = make(map[string]tagpb.TaggableType)
	var tagSql string
	var ugcType = s.ugcType

	switch ugcType {
	case basepb.DataType_DATA_TYPE_RESOURCE:
		taggableTypeMapping = programTagTypeMappings
	case basepb.DataType_DATA_TYPE_GRAPH:
		taggableTypeMapping = knowledgeTagTypeMappings
	case basepb.DataType_DATA_TYPE_TEAM:
		taggableTypeMapping = teamTagTypeMappings
	case basepb.DataType_DATA_TYPE_PRODUCT:
		taggableTypeMapping = solutionsTagTypeMappings
	}
	for _, condition := range conditions {
		// 匹配 aa.bb (LIKE|=) ('xx'|xx)
		re, _ := regexp.Compile(commonRegex)
		matches := re.FindStringSubmatch(condition)

		if len(matches) > 5 {
			sqlField := matches[3]
			sqlValue := matches[5]
			filterValue := s.handleFilter(sqlValue)
			if s.containsIgnoreKeywords(filterValue) {
				continue
			}
			// 命中tag
			tagType, exists := taggableTypeMapping[sqlField]
			if exists {
				if tagSql == "" {
					tagSql = tagSql + fmt.Sprintf(taggablePerSql, ugcType, sqlValue, tagType)
				} else {
					tagSql = tagSql + fmt.Sprintf(" OR "+taggablePerSql, ugcType, sqlValue, tagType)
				}
			}
		}

	}
	if len(tagSql) > 0 {
		s.conditionTags = s.executeTagsSQL(s.ctx, tagSql)
	}
}

// buildNewConditions 根据提取的条件构造新的查询条件
func (s *SqlUtils) buildNewConditions(conditions []string) map[string]string {
	newConditionMap := make(map[string]string)
	taggableTypeMapping := make(map[string]tagpb.TaggableType)
	locationTypeMapping := make(map[string]string)
	var ugcType = s.ugcType
	var tags = make(map[uint64]*aipb.MessageTag)

	switch ugcType {
	case basepb.DataType_DATA_TYPE_RESOURCE:
		taggableTypeMapping = programTagTypeMappings
		locationTypeMapping = programLocationTypeMappings
	case basepb.DataType_DATA_TYPE_GRAPH:
		taggableTypeMapping = knowledgeTagTypeMappings
	case basepb.DataType_DATA_TYPE_TEAM:
		taggableTypeMapping = teamTagTypeMappings
		locationTypeMapping = teamLocationTypeMappings
	case basepb.DataType_DATA_TYPE_PRODUCT:
		locationTypeMapping = productLocationTypeMappings
		taggableTypeMapping = solutionsTagTypeMappings
	}

	for _, condition := range conditions {
		// 匹配 (WHERE|AND|OR) aa.bb (LIKE|=) ('xx'|xx)
		re, _ := regexp.Compile(commonRegex)
		matches := re.FindStringSubmatch(condition)
		var tagType tagpb.TaggableType
		var isTag bool
		if len(matches) > 5 {
			link := matches[1] + " "
			atlas := matches[2] // t1.keywords_program的t1
			sqlField := matches[3]
			con := matches[4]                       // =|LIKE|<=|>=|<|>
			sqlValue := matches[5]                  // '%低碳%'
			filterValue := s.handleFilter(sqlValue) // 低碳

			if s.containsIgnoreKeywords(filterValue) {
				newConditionMap[condition] = link + commonReplace
				continue
			}

			// 命中keywords
			fields, exists := fieldMappings[sqlField]
			if exists {
				var fieldConditions []string
				for _, field := range fields {
					if atlas != "" {
						fieldConditions = append(fieldConditions, fmt.Sprintf("%s.%s %s %s", atlas, field, con, sqlValue))
					} else {
						fieldConditions = append(fieldConditions, fmt.Sprintf("%s %s %s", field, con, sqlValue))
					}
				}
				newConditionMap[condition] = link + fmt.Sprintf("(%s)", strings.Join(fieldConditions, " OR "))
				sqlField = "search"
			}

			// 命中tag
			tagType, exists = taggableTypeMapping[sqlField]
			if exists {
				tag := s.containsTag(tagType, sqlValue, ugcType) // 存在该标签
				if tag != nil {
					newConditionMap[condition] = link + fmt.Sprintf(taggableSql, ugcType, sqlValue, tagType)
					if !strings.Contains(link, "OR") {
						tags[tag.Id] = tag
					}
				} else {
					newConditionMap[condition] = link + commonReplace
				}
				isTag = true
			}

			// 命中location
			locationFiled, exists := locationTypeMapping[sqlField]
			if exists {
				if ugcType == basepb.DataType_DATA_TYPE_PRODUCT {
					newConditionMap[condition] = link + fmt.Sprintf(productLocationSwitchSql, con, sqlValue, con, sqlValue, basepb.DataType_DATA_TYPE_TEAM, locationFiled)
				} else {
					newConditionMap[condition] = link + fmt.Sprintf(locationSql, con, sqlValue, con, sqlValue, ugcType, locationFiled)
				}
			}

			if !isTag && !strings.Contains(link, "OR") {
				s.filterResult = append(s.filterResult, &aipb.ChatMessageContentFilterItem{
					Field: sqlField, Value: filterValue,
				})
			}
		}
	}
	if len(tags) > 0 {
		if ugcType == basepb.DataType_DATA_TYPE_GRAPH || ugcType == basepb.DataType_DATA_TYPE_RESOURCE {
			s.filterResult = append(s.filterResult, &aipb.ChatMessageContentFilterItem{
				Field: "tags",
				Tags:  s.tagMapToSlice(tags),
			})
		} else {
			for _, tag := range tags {
				s.filterResult = append(s.filterResult, &aipb.ChatMessageContentFilterItem{
					Field: "search", Value: tag.Name,
				})
			}
		}

	}

	return newConditionMap

}

func (s *SqlUtils) handleFilter(sqlValue string) string {
	pattern := `'%([^%']*)%'|'\s*([^']*)\s*'`
	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(sqlValue)
	if len(matches) > 1 {
		if matches[1] != "" {
			return matches[1]
		}
		if matches[2] != "" {
			return matches[2]
		}
		return matches[0]
	}
	return sqlValue
}

// extractKeywordsConditions 提取 SQL 查询中的 keywords_team LIKE 条件
func (s *SqlUtils) extractKeywordsConditions(query string) []string {
	// 匹配 (AND|OR|WHERE) xx.xx (LIKE|=) ('xx'|xx)
	re, _ := regexp.Compile(commonRegex)
	matches := re.FindAllString(query, -1)

	// 去掉逻辑运算符（AND、OR）前缀，并保持条件的原始形式
	var conditions []string
	for _, match := range matches {
		conditions = append(conditions, match)
	}
	return conditions
}

func (s *SqlUtils) containsIgnoreKeywords(target string) bool {
	ignores := []string{"低碳", "碳中和", "减碳", "创新", "climate technology", "carbon neutral", "Climate", "carbon reduction", "emission reduction", "low carbon", "principal_country"}
	ignoreFieldKeywords := config.GetStringSliceOr("llm.ignore_field_keywords", ignores)
	for _, keyword := range ignoreFieldKeywords {
		if target == keyword {
			return true
		}
	}
	return false
}

func (s *SqlUtils) containsTag(tagType tagpb.TaggableType, sqlValue string, ugcType basepb.DataType) *aipb.MessageTag {
	re := regexp.MustCompile(`'%?([^%']+)%?'`)
	match := re.FindStringSubmatch(sqlValue)
	var value = sqlValue
	if len(match) > 1 {
		value = match[1]
	}
	for _, tag := range s.conditionTags {
		if tag.TaggableType == int32(tagType) && strings.Contains(tag.Name, value) && tag.DataType == int32(ugcType) {
			return tag
		}
	}
	return nil
}

func (s *SqlUtils) fixSQLError(query string) string {
	// 匹配 "WHERE OR" 的正则表达式
	re := regexp.MustCompile(`(?i)\bWHERE\s+(OR|AND)\s+`)
	fixedQuery := re.ReplaceAllString(query, "WHERE ")
	strs := strings.Split(fixedQuery, "WHERE")
	if len(strs) > 0 && (strs[len(strs)-1] == " " || strs[len(strs)-1] == "") {
		fixedQuery = strings.ReplaceAll(fixedQuery, "WHERE", "")
	}
	fixedQuery = strings.ReplaceAll(fixedQuery, "OR "+commonReplace, "")
	fixedQuery = strings.ReplaceAll(fixedQuery, ";", "")
	return fixedQuery
}

func splitSQLByUnionAll(sql string) []string {
	var queries []string
	switch {
	case strings.Contains(sql, "UNION ALL"):
		queries = strings.Split(sql, "UNION ALL")
	case strings.Contains(sql, "UNION"):
		queries = strings.Split(sql, "UNION")
	default:
		queries = strings.Split(sql, ";")
	}

	result := make([]string, 0)
	for i := range queries {
		tr := strings.TrimSpace(queries[i])
		if tr != "" {
			result = append(result, tr)
		}
	}

	return result
}

// extractSQL 处理ai返回的sql
func extractSQL(input string) string {
	// 定义正则表达式匹配SQL代码块
	re := regexp.MustCompile("(?s)```sql\\s*(.*?)\\s*```")
	matches := re.FindStringSubmatch(input)
	if len(matches) < 2 {
		return ""
	}
	//sql := removeInvalidSpacesAndNewlines(matches[1])
	sql := strings.ReplaceAll(matches[1], "\\n", " ")
	return sql
}

func sqlPreHandler(sql string) string {
	// 替换 SELECT 和 FROM 之间的内容为 SELECT * FROM
	regex := regexp.MustCompile(`(?i)SELECT\s+.*?\s+FROM`)
	sql = regex.ReplaceAllString(sql, "SELECT * FROM")

	// 替换keywords_programs LIKE '%比赛%'
	sql = strings.ReplaceAll(sql, "keywords_programs LIKE '%比赛%'", "type_tags LIKE '%创新比赛%'")

	return sql
}

func getSqlQueryGroup(originalSql string) []*SqlUtils {
	originalSql = sqlPreHandler(originalSql)
	sqlTexts := splitSQLByUnionAll(originalSql)
	utils := make([]*SqlUtils, 0, len(sqlTexts))
	for _, query := range sqlTexts {
		query = strings.ReplaceAll(query, ";", "")
		s := NewSqlUtils().withTableType(query)
		if s.ugcType == 0 {
			continue
		}
		conditions := s.extractKeywordsConditions(query)
		if len(conditions) > 0 {
			s.buildTagConditions(conditions)
			newConditions := s.buildNewConditions(conditions)
			s.sqlQuery = s.replaceConditions(newConditions, query)
		} else {
			s.sqlQuery = query
		}
		utils = append(utils, s)
	}
	return utils
}

func (s *SqlUtils) backHandler(ctx context.Context, sqlRsp []map[string]interface{}) []map[string]interface{} {
	if len(sqlRsp) > 0 { // 查询结果不为空，不进行后处理
		return sqlRsp
	}
	if strings.Contains(s.sqlQuery, "`data_field` = 'service_region'") {
		newQuery := strings.ReplaceAll(s.sqlQuery, "`data_field` = 'service_region'", "`data_field` = 'location'")
		newSqlRsp, err := s.executeSQL(ctx, newQuery)
		if err != nil {
			return sqlRsp
		}
		return newSqlRsp
	}
	return sqlRsp
}

// handleSqlUgc 处理chat_or_sql查询出的ugc结构
func handleSqlUgc(ctx context.Context, su *SqlUtils) (*aipb.MessageUgc, error) {
	var sqlRsp []map[string]interface{}
	var err error

	if su.sqlQuery == "" || su.ugcType == basepb.DataType_DATA_TYPE_UNSPECIFIED {
		return nil, xerrors.InternalServerError("sql query is empty")
	}
	ugcItem := &aipb.MessageUgc{
		UgcType: su.ugcType,
		Filter:  su.filterResult,
		Cards:   make([]*aipb.MessageUgcCard, 0),
	}
	sqlQuery := su.sqlQuery
	tags := make([]*aipb.MessageTag, 0)

	if len(su.filterResult) == 0 {
		sqlRsp, err = su.executeSQL(ctx, sqlQuery+" ORDER BY last_update_date desc  LIMIT 3")
		ugcItem.IsUgcLink = true
		if err != nil {
			return nil, err
		}
	} else {
		sqlRsp, err = su.executeSQL(ctx, sqlQuery+" ORDER BY last_update_date desc")
		if err != nil {
			return nil, err
		}
	}
	// 后处理
	sqlRsp = su.backHandler(ctx, sqlRsp)

	if len(sqlRsp) == 0 {
		return nil, xerrors.InternalServerError("sql query not found records")
	}

	if len(sqlRsp) > 3 {
		ugcItem.IsUgcLink = true
	}
	var ugcIds []uint64
	for i, item := range sqlRsp {
		ugcIds = append(ugcIds, item["id"].(uint64))
		if i < 3 {
			s2 := ugcLogoUrlMap[ugcItem.UgcType]
			if s2 == nil {
				continue
			}
			card := &aipb.MessageUgcCard{}
			card.Id = item["id"].(uint64)
			s, ok := item[s2[0]].(string)
			if ok {
				card.Name = s
			}
			s, ok = item[s2[1]].(string)
			if ok {
				card.LogoUrl = s
			}
			card.Tags = su.spliceUgcTags(card.Id, tags)
			ugcItem.Cards = append(ugcItem.Cards, card)
		}
	}

	if len(su.filterResult) > 0 {
		ugcItem.UgcIds = ugcIds
	}

	return ugcItem, nil
}
