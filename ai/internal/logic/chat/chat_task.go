package chat

import (
	"context"
	"errors"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/buffered"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/hashicorp/go-uuid"
)

type TextResult struct {
	text        string
	think       string
	messageType aipb.ChatMessageType
}

type ChatRspResult struct {
	msg *ChatMessage
	err error
}

type ChatTask struct {
	Id               uint64
	FetchType        aipb.PipelineTaskFetchType
	ReqMessage       *ChatMessage
	rspMessage       *ChatMessage
	BuildReqMessage  func(msg *ChatMessage) *ChatMessage
	CheckRspValid    func(task *ChatTask) bool
	PreHandler       func() *ChatTask
	NoHistoryRounds  bool
	nextTasks        []*ChatTask
	ctx              context.Context
	fetchErr         error
	chainHashId      string
	pbTask           *aipb.ChatAgentTask
	startAnswerIndex int32
}

func NewChatTask(ctx context.Context, input ChatTask) *ChatTask {
	task := &ChatTask{
		Id:              input.Id,
		FetchType:       input.FetchType,
		ReqMessage:      input.ReqMessage,
		CheckRspValid:   input.CheckRspValid,
		PreHandler:      input.PreHandler,
		NoHistoryRounds: input.NoHistoryRounds,
	}
	task.ctx = ctx
	hashId, _ := uuid.GenerateUUID()
	task.chainHashId = hashId
	return task
}

// PromptPrefix ...
func PromptPrefix(v *ChatMessage, fix string) *ChatMessage {
	v.PromptPrefix = fix
	return v
}

func (t *ChatTask) SetNextTask(task *ChatTask) {
	task.chainHashId = t.chainHashId
	t.ReqMessage.WaitAnswer = true
	t.nextTasks = append(t.nextTasks, task)
}

func (t *ChatTask) Save() (*aipb.ChatMessage, error) {
	if t.rspMessage == nil {
		return nil, errors.New("task RspMessage is nil")
	}
	if dbMsg, err := t.rspMessage.Save(t.ctx); err != nil {
		return nil, errors.New("save task RspMessage error")
	} else {
		t.rspMessage.Id = dbMsg.ID
		return t.rspMessage.ChatMessage, nil
	}
}

func (t *ChatTask) GetRspMessage() (*aipb.ChatMessage, error) {
	if t.rspMessage == nil {
		return nil, errors.New("task RspMessage is nil")
	}
	return t.rspMessage.ChatMessage, nil
}

// CreatChatTaskChainAnswer 创建连续任务答案
func (t *ChatTask) CreatChatTaskChainAnswer(assistant *aipb.AssistantDetail) *ChatTask {
	if t.ctx == nil {
		t.ctx = context.Background()
	}
	ctx := t.ctx

	log.WithContext(ctx).Infow("CreatChatTaskChainAnswer request", "req message", t.ReqMessage)

	if t.ReqMessage == nil {
		log.WithContext(ctx).Errorw("CreatChatTaskChainAnswer task reqMessage is nil")
		return t
	}

	taskChain := t.getTaskChain()
	t.rspMessage, t.fetchErr = getTaskChainResult(ctx, taskChain, assistant)
	if t.rspMessage == nil {
		t.rspMessage = t.getTaskDefaultErrMsg(t.ReqMessage.ReqToRspMessage())
	}
	log.WithContext(ctx).Infow("CreatChatTaskChainAnswer task success", "req message", t.rspMessage, "hashId", t.rspMessage.PublishHashId)
	return t
}

func (t *ChatTask) prepareFetchMessages(index int, rspMsg *ChatMessage) *ChatMessage {
	if t.BuildReqMessage != nil {
		rspMsg = t.BuildReqMessage(rspMsg)
	}
	rspMsg.TaskIndex = index
	rspMsg.SetOption(WithoutHistoryRounds(t.NoHistoryRounds))

	return rspMsg
}

func (t *ChatTask) getTaskChain() []*ChatTask {
	taskChain := []*ChatTask{t}
	if len(t.nextTasks) > 0 {
		taskChain = append(taskChain, t.nextTasks...)
	}
	return taskChain
}

func (t *ChatTask) getTaskDefaultErrMsg(rspMsg *ChatMessage) *ChatMessage {
	defaultMsg := rspMsg.Copy()
	defaultMsg.Text = rspMsg.GetDefaultErrText()
	defaultMsg.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR
	if rspMsg.DefaultText != "" {
		defaultMsg.Text = rspMsg.DefaultText
	}
	defaultMsg.WaitAnswer = true
	return defaultMsg
}

func getTaskChainResult(ctx context.Context, taskChan []*ChatTask, assistant *aipb.AssistantDetail) (*ChatMessage, error) {
	if taskChan == nil || len(taskChan) == 0 {
		return nil, errors.New("taskChan is empty")
	}
	main := taskChan[0]
	var err error

	rspMsg := main.ReqMessage.ReqToRspMessage()
	rspMsg.LogChan = buffered.NewChan[*model.TChatLog](buffered.WithTimeout[*model.TChatLog](time.After(120 * time.Second)))
	for i, task := range taskChan {
		if i > 0 {
			rspMsg = &ChatMessage{ // nextTask.rspMsg继承上一个rspMsg的参数
				Assistant: rspMsg.Assistant,
				LogChan:   rspMsg.LogChan,
				ChatMessage: &aipb.ChatMessage{
					ChatId:          rspMsg.ChatId,
					Text:            rspMsg.Text,
					QuestionId:      rspMsg.QuestionId,
					CreateBy:        rspMsg.CreateBy,
					AssistantId:     rspMsg.AssistantId,
					Lang:            rspMsg.Lang,
					PromptPrefix:    rspMsg.PromptPrefix,
					PublishHashId:   rspMsg.PublishHashId,
					HistoryIgnoreId: rspMsg.HistoryIgnoreId,
					AnswerIndex:     rspMsg.AnswerIndex,
					Task:            rspMsg.Task,
				},
			}
			rspMsg.SetOptions(rspMsg.options)
		}
		rspMsg, err = fetchAndHandleAIAnswer(ctx, assistant, task.prepareFetchMessages(i, rspMsg), task.FetchType)
		log.WithContext(ctx).Infow("getTaskChainResult fetchAndHandleAIAnswer success", "task index", i, "fetchType", task.FetchType, "rspMsg", rspMsg)
		if err != nil {
			log.WithContext(ctx).Errorw("getTaskChainResult get task result fail", "err", err)
			return rspMsg, err
		}
	}
	return rspMsg, nil
}

func (m *ChatMessage) ReqToRspMessage() *ChatMessage {
	rspMsg := m.Copy()
	rspMsg.Id = 0
	rspMsg.QuestionId = m.Id

	return rspMsg
}
