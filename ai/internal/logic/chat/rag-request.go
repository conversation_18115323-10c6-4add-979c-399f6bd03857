package chat

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	rp "e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/buffered"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag/common"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"golang.org/x/sync/errgroup"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type RequestType int32

const (
	RequestTypeSearch     RequestType = 1
	RequestTypeCollection RequestType = 2
	RequestTypeVision     RequestType = 3
	RequestTypeSql        RequestType = 4
)

type BaseRequest struct {
	*ChatMessage
	assistant          chatAssistant
	payload            *rag.ReqChat
	startTime          time.Time
	endTime            time.Time
	publishStream      bool
	startChan          chan struct{}
	suggestPublishChan *buffered.Chan[bool]
	autoPublish        bool
	requestType        RequestType
	refChanOnce        sync.Once
	publishedCount     int
	publishLock        sync.Mutex
	doneChunkOnce      sync.Once
	fullText           string
	fullThink          string

	chatResp *rag.RespChat
}

type RespToMessageTransFunc func(rsp *rag.RespChat, m *ChatMessage) error

func NewRequestBuilder[T any](req MessageRequestBuilder[T], msg *ChatMessage) MessageRequestBuilder[T] {
	req.initBaseBuilder(msg)
	return req
}

func (r *BaseRequest) initBaseBuilder(msg *ChatMessage) {
	r.ChatMessage = msg
	r.assistant = msg.Assistant
}

//func (r *BaseRequest) pushStreamMessageEvent(ctx context.Context, content string, event *aipb.ChatMessage) error {
//	time.Sleep(30 * time.Millisecond)
//	if err = r.pushToRedis(ctx, r.ChatMessage.CreateBy, content, r.ChatMessage.PublishHashId, r.ChatMessage.AnswerIndex); err != nil {
//		return err
//	}
//	return nil
//}

func (r *BaseRequest) pushToRedis(ctx context.Context, pushMsg *aipb.ChatPushMessage) error {
	if r.WaitAnswer {
		return nil // 等待回答的流式请求，不推送chunk
	}
	if pushMsg.HashId == "" {
		return fmt.Errorf("hashId is empty")
	}

	jsonData, err := json.Marshal(pushMsg)
	if err != nil {
		return err
	}
	topic := fmt.Sprintf("%s%s", config.GetStringOr("llm.chat_channel_topic", "tanlive:channel:ai:"), pushMsg.HashId)

	if aiClient := rp.Use("ai"); aiClient != nil {
		// 发布消息
		aiClient.Publish(ctx, topic, string(jsonData))
	}

	if aiNationalClient := rp.Use("ai-internal"); aiNationalClient != nil {
		// 发布消息
		aiNationalClient.Publish(ctx, topic, string(jsonData))
	}

	r.publishLock.Lock()
	if r.publishedCount < 5 { // 打印前五条推送信息
		log.WithContext(ctx).Infow("BaseRequest publish msg success", "type", r.Type, "topic", topic, "data", string(jsonData))
		r.publishedCount++
	}
	r.publishLock.Unlock()

	return nil
}

func (r *BaseRequest) addToPublishChan(event *aipb.ChatPushMessage, publishChan *buffered.Chan[*aipb.ChatPushMessage]) {
	event.AnswerIndex = r.ChatMessage.AnswerIndex
	event.HashId = r.ChatMessage.PublishHashId
	publishChan.Send(event)
}

type CollectionRequest struct {
	BaseRequest
}

func NewCollectionRequest() *CollectionRequest {
	return &CollectionRequest{
		BaseRequest{
			requestType:        RequestTypeCollection,
			startChan:          make(chan struct{}),
			suggestPublishChan: buffered.NewChan[bool](),
		},
	}
}

func (r *BaseRequest) setDuration(start time.Time, end time.Time) {
	r.startTime = start
	r.endTime = end
}

func (r *BaseRequest) StartConsume(ctx context.Context, isPublish bool) *BaseRequest {
	r.publishStream = isPublish
	if r.startChan != nil {
		if r.ChatMessage != nil {
			log.WithContext(ctx).Infow("pkg buffered startConsume", "type", r.ChatMessage.Type, "isPublish", isPublish)
		}
		close(r.startChan)
	}
	if r.suggestPublishChan != nil && isPublish {
		r.suggestPublishChan.Send(true)
		r.suggestPublishChan.Close()
	}
	return r
}

func (r *BaseRequest) createPublishChan(ctx context.Context) *buffered.Chan[*aipb.ChatPushMessage] {
	publishChan := buffered.NewChan[*aipb.ChatPushMessage](buffered.WithStartChan[*aipb.ChatPushMessage](r.startChan))
	if r.WaitAnswer {
		return publishChan // 等待回答的流式请求，不推送chunk
	}
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		if err := publishChan.Consume(func(msg *aipb.ChatPushMessage) {
			if msg != nil && r.publishStream {
				if err := r.pushToRedis(ctx, msg); err != nil {
					log.WithContext(ctx).Errorw("BaseRequest publish msg failed", "type", r.Type, "err", err)
				}
			}
		}); err != nil {
			log.WithContext(ctx).Errorw("BaseRequest publishChan consume failed", "type", r.Type, "err", err)
		}
		return nil
	}, boot.TraceGo(ctx))
	return publishChan
}

//func (r *BaseRequest) closeRefChan() {
//	if r.refChan != nil {
//		r.refChanOnce.Do(func() {
//			close(r.refChan)
//		})
//	}
//}

//func (r *BaseRequest) setRefChan(message *ChatMessage) {
//	if r.refChan != nil {
//		r.refChanOnce.Do(func() {
//			r.refChan <- message
//		})
//	}
//}

type StreamResult struct {
	msg *ChatMessage
	err error
}

func (r *BaseRequest) handleChoiceDelta(delta *rag.ChatStreamChoiceDelta, publishChan *buffered.Chan[*aipb.ChatPushMessage]) {
	var content string
	if content = delta.ReasoningContent; content != "" { // 思考内容
		r.addToPublishChan(&aipb.ChatPushMessage{
			Content: content,
			State:   aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK,
		}, publishChan)
		r.fullThink += content
	} else if content = delta.Content; content != "" { //	回答内容
		if r.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION && (content == "[]" || strings.Contains(content, "NULL")) {
			content = r.ChatMessage.GetDefaultErrText()
			r.addToPublishChan(&aipb.ChatPushMessage{
				Content: content,
				State:   aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK,
				Type:    aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR,
			}, publishChan)
			r.fullText += content
			return
		}
		r.addToPublishChan(&aipb.ChatPushMessage{
			Content: content,
			State:   aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK,
		}, publishChan)
		r.fullText += content
	}
}

// handelAndPublishStreamChunk 处理stream过来的数据
func (r *BaseRequest) handelAndPublishStreamChunk(ctx context.Context, publishChan *buffered.Chan[*aipb.ChatPushMessage], handleStreamRespFn RespToMessageTransFunc, chunk *rag.RespChatStream, resultChan chan StreamResult, syncResult *StreamResult) {
	if chunk == nil {
		log.WithContext(ctx).Errorw("handelAndPublishStreamChunk: chunk is nil")
		return
	}

	if chunk.Error != nil {
		resultChan <- StreamResult{r.ChatMessage, chunk.Error}
		return
	}

	if chunk.ChatResp != nil && (chunk.ChatResp.Code > 0 || chunk.ChatResp.Data != nil) { // 处理 msg chunk
		log.WithContext(ctx).Infow("BaseRequest handelStreamRsp receive chunk.ChatResp", "type", r.ChatMessage.Type, "ChatResp", chunk.ChatResp)
		r.chatResp = chunk.ChatResp
		// 构建并推送ref相关的publish msg
		err := handleStreamRespFn(chunk.ChatResp, r.ChatMessage)
		syncResult.err = err
		syncResult.msg = r.ChatMessage
		resultChan <- StreamResult{r.ChatMessage, err}
		if err != nil {
			r.fullText = r.ChatMessage.GetDefaultErrText()
			if chunk.ChatResp.Code > 0 && chunk.ChatResp.Code == common.ErrCodeModelNotExist { // 处理chunk中返回的err code， code!=success code 返回err
				r.fullText = r.getMissReply()
			}
			//r.setRefChan(nil)
			return
		}

		// 推送参考资料
		eventMsg := r.ChatMessage.CopyToPbMessage()
		eventMsg.State = aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_REF
		eventMsg.Text = ""
		log.WithContext(ctx).Infow("BaseRequest handelStreamRsp publish ref msg", "eventMsg", eventMsg, "saveMsg", r.ChatMessage)
		r.addToPublishChan(&aipb.ChatPushMessage{
			HashMsg: eventMsg,
			State:   eventMsg.State,
			Type:    eventMsg.Type,
		}, publishChan)
		return
	}
	if chunk.Done { // 推送done msg
		r.doneChunkOnce.Do(func() {
			r.addToPublishChan(&aipb.ChatPushMessage{
				State:   aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK_DONE,
				Content: r.fullText,
			}, publishChan)
		})
		return
	}
	if chunk.ChatResp != nil && len(chunk.ChatResp.Choices) > 0 { // 推送chunk msg
		for _, choice := range chunk.ChatResp.Choices {
			if choice.Delta != nil {
				r.handleChoiceDelta(choice.Delta, publishChan)
			} else if choice.Message != nil {
				r.handleChoiceDelta(choice.Message, publishChan)
			}
		}
	}
}

// publishSuggestions  推送建议问题
func (r *BaseRequest) publishSuggestions(ctx context.Context, questionText string, rspMsg *ChatMessage, rspErr error) {
	xsync.SafeGo(ctx, func(ctx context.Context) error {
		if r.ChatMessage.suggestChan == nil || r.ChatMessage.GetOptions().noSuggestions {
			return nil
		}
		var suggests []string
		if rspErr == nil && rspMsg != nil {
			suggests = rspMsg.CreateMessageSuggestion(ctx, questionText, rspMsg.Text, rspMsg.Assistant.AssistantDetail)
		}
		log.WithContext(ctx).Infow("Suggestion chan received", "type", rspMsg.Type)
		defer r.ChatMessage.suggestChan.Close()
		if r.suggestPublishChan != nil {
			err := r.suggestPublishChan.Consume(func(b bool) {
				r.ChatMessage.suggestChan.Send(suggests)
				log.WithContext(ctx).Infow("Suggestion CreateMessageSuggestion success", "type", rspMsg.Type, "suggests", suggests)
				if err := r.pushToRedis(ctx, &aipb.ChatPushMessage{
					Suggests:    suggests,
					State:       aipb.ChatMessageState_CHAT_MESSAGE_STATE_SUGGESTION,
					AnswerIndex: r.ChatMessage.AnswerIndex,
					HashId:      r.PublishHashId,
				}); err != nil {
					log.WithContext(ctx).Errorw("[BaseRequest] afterFetchCommon push message failed", "type", r.Type, "hashId", r.PublishHashId, "err", err)
				}
			})
			if err != nil {
				log.WithContext(ctx).Errorw("r.suggestPublishChan consume err", "err", err)
			}
		}
		return nil
	}, boot.TraceGo(ctx))
}

func (r *BaseRequest) afterFetchCommon(ctx context.Context, chatResp interface{}, m *ChatMessage, handleStreamRespFn RespToMessageTransFunc) (*ChatMessage, error) {
	// 1. chatResp返回的是流式数据
	questionText := m.Text
	stream, ok := chatResp.(*buffered.Chan[*rag.RespChatStream])
	if ok {
		log.WithContext(ctx).Infow("afterFetchCommon handle stream rsp in", "type", r.ChatMessage.Type, "hashId", r.ChatMessage.PublishHashId, "resp type", "buffered.Chan[*rag.RespChatStream]")
		publishChan := r.createPublishChan(ctx)
		m.textChan = buffered.NewChan[TextResult](buffered.WithTimeout[TextResult](time.After(time.Duration(config.GetIntOr("llm.chat.text_chan_timeout", 300)) * time.Second)))
		m.suggestChan = buffered.NewChan[[]string](buffered.WithTimeout[[]string](time.After(time.Duration(config.GetIntOr("llm.chat.suggest_chan_timeout", 300)) * time.Second)))
		var resultChan = make(chan StreamResult, 2)

		wg := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))
		wg.SafeGo(func(ctx context.Context) error {
			defer func() {
				if err := recover(); err != nil {
					m.Text = m.GetDefaultErrText()
					log.WithContext(ctx).Errorw("recovered from panic in handelStreamRsp",
						"error", err,
						"stack", string(debug.Stack()))
				}
				//r.closeRefChan()
				publishChan.Close()
				log.WithContext(ctx).Infow("afterFetchCommon stream receive finished", "type", r.Type, "hashId", r.ChatMessage.PublishHashId, "fullText", r.fullText, "fullThink", r.fullThink)
				close(resultChan)
			}()
			var syncResult = &StreamResult{}
			if err := stream.Consume(func(chunk *rag.RespChatStream) {
				r.handelAndPublishStreamChunk(ctx, publishChan, handleStreamRespFn, chunk, resultChan, syncResult)
			}); err != nil {
				log.WithContext(ctx).Errorw("[BaseRequest] afterFetchCommon respChan consume failed", "type", r.Type, "hashId", r.ChatMessage.PublishHashId, "err", err)
			}
			if syncResult.msg == nil { // stream没有返回正常的rag rsp结构
				m.Text = m.GetDefaultErrText()
				r.addToPublishChan(&aipb.ChatPushMessage{
					Content: m.Text,
					State:   aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK,
					Type:    aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR,
				}, publishChan)
				syncResult.err = errors.New("afterFetchCommon stream rsp is nil")
			} else {
				m.Text = r.fullText
				m.Think = r.fullThink
			}
			log.WithContext(ctx).Infow("after handelAndPublishStreamChunk finished", "type", r.ChatMessage.Type, "result", m)
			m.EndTime = timestamppb.New(time.Now())
			saveLog[*rag.RespChat](ctx, m, r.payload, r.chatResp, r.fullText)
			m.textChan.Send(TextResult{
				text:        m.Text,
				think:       m.Think,
				messageType: m.Type,
			})
			m.textChan.Close()
			r.publishSuggestions(ctx, questionText, m, syncResult.err)
			return nil
		}, boot.TraceGo(ctx))

		if m.WaitAnswer { // m.WaitAnswer=true的情况，search_chart、search_chart_stream强制等待完整回答时
			wg.Wait()
			for {
				select {
				case <-ctx.Done():
					return m, ctx.Err()
				case result, ok := <-resultChan:
					if !ok {
						return m, errors.New("result channel closed")
					}
					return m, result.err
				}
			}
		}
		m.WaitAnswer = false
		for {
			select {
			case <-ctx.Done():
				return m, ctx.Err()
			case result, ok := <-resultChan:
				if !ok {
					return m, errors.New("result channel closed")
				}
				return result.msg, result.err
			}
		}
	}

	// 2. chatResp返回的是原结构数据
	m.WaitAnswer = true
	var gpt string
	chatRsp, ok := chatResp.(*rag.RespChat)
	if ok {
		log.WithContext(ctx).Infow("afterFetchCommon handle http rsp in", "type", r.ChatMessage.Type, "hashId", r.ChatMessage.PublishHashId, "resp type", "*rag.RespChat", "rsp", chatRsp)
		err := handleStreamRespFn(chatRsp, m)
		gpt = m.Text
		m.EndTime = timestamppb.New(time.Now())
		saveLog[*rag.RespChat](ctx, m, r.payload, chatRsp, gpt)
		// 推送建议问题
		m.suggestChan = buffered.NewChan[[]string](buffered.WithTimeout[[]string](time.After(120 * time.Second)))
		r.publishSuggestions(ctx, questionText, m, err)
		return m, err
	}
	m.Text = m.GetDefaultErrText()
	return m, errors.New("afterFetchCommon parse rsp type err")
}

type SearchRequest struct {
	BaseRequest
}

func NewSearchRequest() *SearchRequest {
	return &SearchRequest{
		BaseRequest{
			requestType:        RequestTypeSearch,
			startChan:          make(chan struct{}),
			suggestPublishChan: buffered.NewChan[bool](),
			//refChan:     make(chan *ChatMessage),
		},
	}
}

func (r *SearchRequest) getMessage() *ChatMessage {
	r.ChatMessage.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH
	return r.ChatMessage
}

func (r *SearchRequest) createPayload() (*rag.ReqChat, error) {
	m := r.getMessage()
	m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH
	p := &rag.ReqChat{
		Text:          getHistoryRoundsText(m),
		Model:         m.Assistant.Model,
		Security:      false,
		Search:        true,
		PromptPrefix:  m.PromptPrefix,
		Engine:        m.Assistant.SearchEngine,
		AgentId:       config.GetStringOr("llm.chat_agent_id", "climate_tech_tanlive"),
		SearchRewrite: m.Assistant.IsSearchRewrite,
		TopN:          int(m.Assistant.SearchTopN),
		Temperature:   m.Assistant.Temperature,
	}
	if m.Assistant.QuestionTypeConfig != nil && m.Assistant.QuestionTypeConfig.Enabled {
		p.JudgePromptType = true
		p.Model4complex = m.Assistant.QuestionTypeConfig.ComplexModel
		p.Model4chat = m.Assistant.QuestionTypeConfig.ChatModel
		p.Model = m.Assistant.QuestionTypeConfig.SimpleModel
		if m.Assistant.QuestionTypeConfig.Prompt != "" {
			p.PromptPrefix = m.Assistant.QuestionTypeConfig.Prompt
		}
	}
	r.payload = p
	return p, nil
}

func (r *SearchRequest) ragMsgToChatMessage(rsp *rag.RespChat, m *ChatMessage) error {
	if rsp == nil || (rsp.Code > 0 && rsp.Code != common.ErrCodeSuccess) {
		m.Text = m.GetDefaultErrText()
		return errors.New("CollectionRequest afterFetch receive rsp.msg == nil")
	}
	m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH
	m.StartTime = timestamppb.New(r.startTime)
	m.EndTime = timestamppb.New(r.endTime)
	m = r.handleChatNormalRsp(m, rsp)
	return nil
}

func (r *SearchRequest) afterFetch(ctx context.Context, chatResp interface{}) (*ChatMessage, error) {
	m := r.getMessage()
	return r.afterFetchCommon(ctx, chatResp, m, r.ragMsgToChatMessage)
}

func (r *SearchRequest) handleChatNormalRsp(m *ChatMessage, rsp *rag.RespChat) *ChatMessage {
	if rsp == nil {
		return m
	}
	if rsp.Data != nil {
		m.Text = rsp.Data.GPT
		m.FinalQuery = rsp.Data.FinalQuery
		if rsp.Data.Response != nil {
			m.Think = rsp.Data.Response.ReasoningContent
		}
		m.PromptType = rsp.Data.PromptType
	}
	if rsp.Ref != "" {
		ref := beautyChatRefLink(rsp.Ref)
		m.Link = ref
	}
	return m
}

func (r *CollectionRequest) getMessage() *ChatMessage {
	r.ChatMessage.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION
	return r.ChatMessage
}

func (r *CollectionRequest) createPayload() (*rag.ReqChat, error) {
	m := r.getMessage()
	m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION
	prompt := m.PromptPrefix
	if !m.Assistant.CloseRefMark {
		additionalText := config.GetStringOr("llm.chat_doc_mark_prompt", "(每段回答的结尾用[n]的形式表示这段回答参考的是第几个资料，n对应资料的顺序，n必须为数字)")
		if strings.Contains(prompt, "{}，") {
			prompt = strings.Replace(prompt, "{}，", "{}，"+additionalText+"\n", 1)
		} else if strings.Contains(prompt, "{},") {
			prompt = strings.Replace(prompt, "{},", "{},"+additionalText+"\n", 1)
		} else {
			prompt = m.PromptPrefix + config.GetStringOr("llm.chat_doc_mark_prompt", "(每段回答的结尾用[n]的形式表示这段回答参考的是第几个资料，n对应资料的顺序，n必须为数字)")
		}
	}

	p := &rag.ReqChat{
		CollectionName: m.Assistant.CollectionName,
		Text:           getHistoryRoundsText(m),
		Model:          m.Assistant.Model,
		Security:       false,
		Search:         false,
		Threshold:      m.Assistant.Threshold,
		PromptPrefix:   prompt,
		AgentId:        config.GetStringOr("llm.chat_agent_id", "climate_tech_tanlive"),
		Lang:           m.Assistant.Lang,
		EsIns:          m.Assistant.EsIns,
		TextWeight:     m.Assistant.TextWeight,
		TopN:           int(m.Assistant.DocTopN),
		Temperature:    m.Assistant.Temperature,
		TextRecallTopN: int(m.Assistant.TextRecallTopN),
		CleanChunks:    m.Assistant.CleanChunks,
	}

	if m.Assistant.TextRecallTopN > 0 {
		p.TextRecallQuery = GetTextRecallQuery(m.Assistant.AssistantDetail)
	}

	if m.Assistant.QuestionTypeConfig != nil && m.Assistant.QuestionTypeConfig.Enabled {
		p.JudgePromptType = true
		p.Model4complex = m.Assistant.QuestionTypeConfig.ComplexModel
		p.Model4chat = m.Assistant.QuestionTypeConfig.ChatModel
		p.Model = m.Assistant.QuestionTypeConfig.SimpleModel
		if m.Assistant.QuestionTypeConfig.Prompt != "" {
			p.PromptPrefix = m.Assistant.QuestionTypeConfig.Prompt
		}
	}
	r.payload = p
	return p, nil
}

func (r *BaseRequest) getMissReply() string {
	var text string
	if r.Assistant.MissReply != "" {
		text = r.Assistant.MissReply
	} else {
		text = "知识库中没有找到相关知识"
	}
	return text
}

func (r *CollectionRequest) ragMsgToChatMessage(rsp *rag.RespChat, m *ChatMessage) error {
	if rsp == nil {
		m.Text = m.GetDefaultErrText()
		return errors.New("CollectionRequest afterFetch receive rsp.msg == nil")
	}
	if rsp.Code > 0 && rsp.Code != common.ErrCodeSuccess {
		if rsp.Code == common.ErrCodeModelNotExist {
			m.Text = r.getMissReply()
			m.DocMatchPattern = aipb.DocMatchPattern_DOC_MATCH_PATTERN_MISS_MATCH
			if rsp.Info != nil {
				info, ok := rsp.Info.(map[string]interface{})
				if ok {
					m.FinalQuery = info["final_query"].(string)
				}
			}
		}
		m.Text = m.GetDefaultErrText()
		return errors.New("CollectionRequest afterFetch code invalid")
	}
	m.DocMatchPattern = aipb.DocMatchPattern_DOC_MATCH_PATTERN_LARGE_MODEL_RECALL
	m = r.handleChatNormalRsp(m, rsp) // 必须放在m.Text赋值后
	m.StartTime = timestamppb.New(r.startTime)
	m.EndTime = timestamppb.New(r.endTime)
	return nil
}

func (r *CollectionRequest) afterFetch(ctx context.Context, chatResp interface{}) (*ChatMessage, error) {
	log.WithContext(ctx).Infow("CollectionRequest afterFetch in")
	m := r.getMessage()
	return r.afterFetchCommon(ctx, chatResp, m, r.ragMsgToChatMessage)
}

func (r *CollectionRequest) handleChatNormalRsp(m *ChatMessage, rsp *rag.RespChat) *ChatMessage {
	if rsp == nil {
		return m
	}
	var docNames, refFileNames []string
	if rsp.Ref != "" {
		refFileNames = strings.Split(rsp.Ref, "\n")
	}
	if rsp.Data != nil {
		m.Text = rsp.Data.GPT
		m.PromptType = rsp.Data.PromptType
		m.FinalQuery = rsp.Data.FinalQuery
		if rsp.Data.Response != nil {
			m.Think = rsp.Data.Response.ReasoningContent
		}
	}
	// 更新mark text
	gpt := m.Text
	if m.Assistant.CloseRefMark {
		m.DocNames = refFileNames
	} else { // 通过[n]筛选doc names
		ignores := config.GetStringSliceOr("llm.chat_doc_mark_prompt_ignore", []string{"[n]", "@ref"})
		for _, mark := range ignores {
			gpt = strings.ReplaceAll(gpt, mark, "")
		}
		var markNums map[int]struct{}
		var matches [][]string

		markNums, matches, gpt = filterCatchGptRefNumber(gpt, len(refFileNames))
		if len(markNums) > 0 {
			for i, name := range refFileNames {
				if _, exists := markNums[i+1]; exists {
					docNames = append(docNames, name)
				}
			}
		}
		if !m.disableClearGptRefMark {
			c := gpt // 门户端答案清掉[n]
			for _, match := range matches {
				if len(match) > 0 {
					c = strings.ReplaceAll(c, match[0], "")
				}
			}
			gpt = c
		}
		m.Text = gpt
	}

	m.RefFileNames = refFileNames
	return m
}

type VisionRequest struct {
	BaseRequest
}

func NewVisionRequest() *VisionRequest {
	return &VisionRequest{
		BaseRequest{
			requestType:        RequestTypeVision,
			startChan:          make(chan struct{}),
			suggestPublishChan: buffered.NewChan[bool](),
		},
	}
}

func (r *VisionRequest) ragMsgToChatMessage(rsp *rag.RespChat, m *ChatMessage) error {
	if rsp == nil || (rsp.Code > 0 && rsp.Code != common.ErrCodeSuccess) {
		m.Text = m.GetDefaultErrText()
		return errors.New("VisionRequest afterFetch receive rsp.msg == nil")
	}

	if r.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION && rsp.Data != nil && (rsp.Data.GPT == "[]" || strings.Contains(rsp.Data.GPT, "NULL")) {
		m.Text = m.GetDefaultErrText()
		return errors.New("VisionRequest afterFetch receive rsp.msg is NULL")
	}
	m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION
	m = r.handleChatNormalRsp(m, rsp)
	m.StartTime = timestamppb.New(r.startTime)
	m.EndTime = timestamppb.New(r.endTime)
	return nil
}

func (r *VisionRequest) getMessage() *ChatMessage {
	r.ChatMessage.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION
	return r.ChatMessage
}

func (r *VisionRequest) afterFetch(ctx context.Context, chatResp interface{}) (*ChatMessage, error) {
	m := r.getMessage()
	if r.autoPublish {
		r.StartConsume(ctx, true)
	}
	return r.afterFetchCommon(ctx, chatResp, m, r.ragMsgToChatMessage)
}

func (r *VisionRequest) handleChatNormalRsp(m *ChatMessage, rsp *rag.RespChat) *ChatMessage {
	if rsp == nil {
		return m
	}
	if rsp.Data != nil {
		m.Text = rsp.Data.GPT
		m.FinalQuery = rsp.Data.FinalQuery
		m.FinalSearchQuery = rsp.Data.FinalSearchQuery
		if rsp.Data.Response != nil {
			m.Think = rsp.Data.Response.ReasoningContent
		}
	}
	if rsp.Ref != "" {
		ref := beautyChatRefLink(rsp.Ref)
		m.Link = ref
	}
	return m
}

func (r *VisionRequest) createPayload() (*rag.ReqChat, error) {
	m := r.getMessage()
	m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION
	text, err := getVisionText(m, m.Assistant.VisionModel)
	if err != nil {
		return nil, err
	}
	p := &rag.ReqChat{
		Text:         text,
		Model:        m.Assistant.VisionModel,
		Security:     false,
		PromptPrefix: m.PromptPrefix,
		AgentId:      config.GetStringOr("llm.chat_agent_id", "climate_tech_tanlive"),
		IsVision:     true,
	}
	r.payload = p
	return p, nil
}

type SqlRequest struct {
	BaseRequest
}

func NewSqlRequest() *SqlRequest {
	return &SqlRequest{
		BaseRequest{
			requestType: RequestTypeSql,
			//refChan:     make(chan *ChatMessage),
		},
	}
}

func (r *SqlRequest) getMessage() *ChatMessage {
	r.ChatMessage.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY
	return r.ChatMessage
}

func (r *SqlRequest) createPayload() (*rag.ReqChat, error) {
	m := r.getMessage()
	m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY
	p := &rag.ReqChat{
		Text:     getHistoryRoundsText(m),
		Model:    m.Assistant.SqlModel,
		AgentId:  config.GetStringOr("llm.chat_agent_id", "climate_tech_tanlive"),
		Security: false,
	}
	r.payload = p
	return p, nil
}

func (r *SqlRequest) afterFetch(ctx context.Context, response interface{}) (*ChatMessage, error) {
	m := r.getMessage()
	rsp, ok := response.(*rag.RespChatOrSql)
	if !ok {
		return m, errors.New("SqlRequest AfterFetch parse rsp err")
	}
	originalSql := extractSQL(rsp.Text)
	//defer r.setRefChan(nil)

	if originalSql == "" {
		return nil, errors.New("HandleChatOrSqlRequest err originalSql is invalid")
	}
	//if m.StatePush {
	//	if err := m.ChangeStateAndPushEvent(ctx, int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND_IN_PREPARATION)); err != nil {
	//		return nil, err
	//	}
	//}
	if err := r.handleSqlQueryMessage(ctx, originalSql); err != nil {
		return nil, err
	}
	m.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY
	m.StartTime = timestamppb.New(r.startTime)
	m.EndTime = timestamppb.New(r.endTime)
	//r.setRefChan(m)
	saveLog[*rag.RespChatOrSql](ctx, m, r.payload, rsp, "")
	return m, nil
}

func (r *SqlRequest) handleSqlQueryMessage(ctx context.Context, originalSql string) error {
	m := r.getMessage()
	sqlItems := getSqlQueryGroup(originalSql)
	if len(sqlItems) == 0 {
		return xerrors.NotFoundError("handleSqlQueryMessage getSqlQueryGroup sqlItems is empty")
	}
	ugcs := make([]*aipb.MessageUgc, 0, len(sqlItems))
	var mu sync.Mutex
	g := &errgroup.Group{}
	for _, su := range sqlItems {
		su := su
		g.Go(func() error {
			sqlUgc, err := handleSqlUgc(ctx, su)
			if err != nil {
				return err
			}
			mu.Lock()
			ugcs = append(ugcs, sqlUgc)
			mu.Unlock()
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		return err
	}
	m.Ugcs = ugcs
	m.SqlQuery = originalSql
	return nil
}
