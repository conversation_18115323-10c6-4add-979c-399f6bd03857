package chat

import (
	"context"
	"encoding/json"
	"regexp"
	"strconv"
	"strings"
	"sync"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

type SuggestionLogic struct {
	messageID uint64
	assistant *aipb.AssistantDetail
	createBy  uint64
}

// CreateModeSuggestion 根据不同模式生成建议问题
func CreateModeSuggestion(ctx context.Context, questionText, answerText string, assistant *aipb.AssistantDetail, createBy uint64, messageId uint64) []string {
	l := SuggestionLogic{
		messageID: messageId,
		assistant: assistant,
		createBy:  createBy,
	}
	if assistant.SuggestionConfig == nil {
		return []string{}
	}

	switch assistant.SuggestionConfig.Mode {
	case aipb.AskSuggestionMode_ASK_SUGGESTION_MODE_3, aipb.AskSuggestionMode_ASK_SUGGESTION_MODE_4:
		// 模式3 根据问题在知识库中已命中的知识，生成问题建议(from从0开始搜索suggestCount个collection，用collection生成问题)
		// 模式4 根据问题在知识库中尚未命中但排名靠前的知识，生成问题建议(from从topN开始搜索suggestCount个collection，用collection生成问题)
		var from int
		if assistant.SuggestionConfig.Mode == aipb.AskSuggestionMode_ASK_SUGGESTION_MODE_4 {
			from = int(assistant.DocTopN)
		}
		return l.createCollectionSuggests(ctx, questionText, int(assistant.SuggestionConfig.Count), from)
	case aipb.AskSuggestionMode_ASK_SUGGESTION_MODE_2:
		// 根据QA生成(数量=建议问题*n倍)的建议问题池子，再用search_collection筛选能命中的suggests
		count := assistant.SuggestionConfig.Count
		if assistant.SuggestionConfig.Times > 0 {
			count = assistant.SuggestionConfig.Count * config.GetInt32Or("llm.chat_suggest_multiple", int32(assistant.SuggestionConfig.Times))
		}
		suggests := l.createQaSuggestions(ctx, questionText, answerText, count)
		return l.filterCollectionHitSuggests(ctx, suggests)
	default: // 根据QA生成指定数量的建议问题
		return l.createQaSuggestions(ctx, questionText, answerText, assistant.SuggestionConfig.Count)
	}
}

// filterCollectionHitSuggests 筛选suggests中能命中collection的
func (l *SuggestionLogic) filterCollectionHitSuggests(ctx context.Context, suggests []string) []string {
	var validSuggests []string
	var mu sync.Mutex

	doneChan := make(chan struct{})
	suggestChan := make(chan string, len(suggests))
	wg := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	go func() {
		for suggest := range suggestChan {
			mu.Lock()
			if len(validSuggests) < int(l.assistant.SuggestionConfig.Count) {
				validSuggests = append(validSuggests, suggest)
				if len(validSuggests) >= int(l.assistant.SuggestionConfig.Count) {
					close(doneChan)
				}
			}
			mu.Unlock()
		}
	}()

	for _, suggest := range suggests {
		suggest := suggest
		wg.SafeGo(func(ctx context.Context) error {
			rows := l.searchCollection(ctx, suggest, int(l.assistant.DocTopN), 0)
			select {
			case <-doneChan:
				return nil
			default:
				if len(rows) > 0 {
					select {
					case suggestChan <- suggest:
						log.Infow("filterCollectionHitSuggests suggest sent to chan")
					case <-doneChan:
						return nil
					}
				}
				return nil
			}
		})
	}

	wg.Wait()
	close(suggestChan)

	select {
	case <-doneChan:
		log.Infow("filterCollectionHitSuggests early return due to validSuggests limit", "validSuggests", validSuggests)
	default:
		log.Infow("filterCollectionHitSuggests all tasks completed", "validSuggests", validSuggests)
	}

	return validSuggests
}

func collectionToSuggestText(text string, prompt string) string {
	re, err := regexp.Compile(`\{}`)
	if err != nil {
		return ""
	}
	matches := re.FindAllString(prompt, -1)
	if matches == nil || len(matches) == 0 {
		return ""
	}
	return strings.Replace(prompt, "{}", text, 1)
}

func promptToSuggestText(question, answer string, count int32, prompt string) string {
	if prompt == "" || count == 0 {
		return ""
	}
	re, err := regexp.Compile(`\{}`)
	if err != nil {
		return ""
	}
	matches := re.FindAllString(prompt, -1)
	if matches == nil || len(matches) == 0 {
		return ""
	}
	text := prompt
	if len(matches) == 3 {
		text = strings.Replace(text, "{}", strconv.Itoa(int(count)), 1)
	}
	text = strings.Replace(text, "{}", question, 1)
	text = strings.Replace(text, "{}", answer, 1)
	return text
}

// chatSuggestRequest /chat api request
func (l *SuggestionLogic) chatSuggestRequest(ctx context.Context, maxCount int32, prompt string) []string {
	var (
		gpt      string
		suggests []string
	)
	md := "hunyuan-standard"
	if l.assistant.SuggestionConfig == nil {
		return suggests
	}

	if l.assistant.SuggestionConfig.Model != "" {
		md = l.assistant.SuggestionConfig.Model
	}
	payload := &rag.ReqChat{
		Text:     prompt,
		Model:    md,
		Search:   false,
		AgentId:  config.GetStringOr("llm.chat_agent_id", "climate_tech_tanlive"),
		Lang:     l.assistant.Lang,
		Security: false,
	}
	defer func() {
		l.saveSuggestChatLog(ctx, payload, gpt)
	}()
	m := &ChatMessage{
		ChatMessage: &aipb.ChatMessage{
			Type:     aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SUGGESTION,
			CreateBy: l.createBy,
		},
	}
	rsp, err := NewChatRequest[*rag.RespChat]().fetchWithTimeout(ctx, payload, m)
	if err != nil {
		log.WithContext(ctx).Errorw("chatSuggestRequest fetchWithTimeout err", "err", err)
		return suggests
	}
	gpt = beautifyChatResponseText(rsp.Data.GPT)
	splitter := strings.Split(gpt, "\n")
	for _, str := range splitter {
		re := regexp.MustCompile(`^\d+\.\s*`)
		if re.MatchString(str) {
			suggests = append(suggests, re.ReplaceAllString(str, ""))
		}
		if len(suggests) == int(maxCount) {
			break
		}
	}

	return suggests
}

func (l *SuggestionLogic) createQaSuggestions(ctx context.Context, questionText, answerText string, count int32) []string {
	if l.assistant.SuggestionConfig == nil {
		return []string{}
	}
	prompt := promptToSuggestText(questionText, answerText, count, l.assistant.SuggestionConfig.Prompt)
	return l.chatSuggestRequest(ctx, count, prompt)
}

func (l *SuggestionLogic) searchCollection(ctx context.Context, text string, topN, from int) (rows []*rag.CollectionRow) {
	assistant := l.assistant
	payload := &rag.ReqSearchCollection{
		CollectionName: assistant.CollectionName,
		Text:           text,
		TopN:           topN,
		Threshold:      assistant.Threshold,
		TotalHits:      true,
		Lang:           assistant.Lang,
		From:           from,
	}
	defer func() {
		l.saveSuggestCollectionLog(ctx, payload, rows)
	}()
	res, err := client.GetRagClient().SearchCollection(ctx, payload)
	if err != nil || res == nil || len(res.Data.Rows) == 0 {
		return rows
	}

	return res.Data.Rows
}

func (l *SuggestionLogic) saveSuggestCollectionLog(ctx context.Context, payload *rag.ReqSearchCollection, rows []*rag.CollectionRow) {
	sl := &model.TChatSuggestLog{
		MessageID:   l.messageID,
		RequestType: "collection",
	}
	if p, err := json.Marshal(payload); err != nil {
		return
	} else {
		sl.ConfigSnapshot = string(p)
	}

	if rows != nil && len(rows) > 0 {
		if r, err := json.Marshal(rows); err != nil {
			return
		} else {
			sl.Collections = string(r)
		}
	}

	if err := model.NewQuery[model.TChatSuggestLog](ctx).Create(sl); err != nil {
		log.WithContext(ctx).Errorw("save chat request log failed", "err", err)
	}
}

func (l *SuggestionLogic) saveSuggestChatLog(ctx context.Context, payload *rag.ReqChat, gpt string) {
	if l.messageID == 0 {
		return
	}
	marshal, err := json.Marshal(payload)
	if err != nil {
		return
	}
	sl := &model.TChatSuggestLog{
		ConfigSnapshot: string(marshal),
		Gpt:            gpt,
		MessageID:      l.messageID,
		RequestType:    "chat",
	}
	if err := model.NewQuery[model.TChatSuggestLog](ctx).Create(sl); err != nil {
		log.WithContext(ctx).Errorw("save chat request log failed", "err", err)
	}
}

// createCollectionSuggests 现search collection，再用每个检索结果创建1个问题
func (l *SuggestionLogic) createCollectionSuggests(ctx context.Context, text string, suggestCount, from int) []string {
	var collectionTexts, suggests []string
	var mu sync.Mutex

	rows := l.searchCollection(ctx, text, suggestCount, from)
	if len(rows) == 0 {
		return suggests
	}
	for _, r := range rows {
		collectionTexts = append(collectionTexts, r.Content)
	}
	wg := xsync.NewGroup(context.WithoutCancel(ctx), xsync.GroupGoOption(boot.TraceGo(ctx)))
	for _, t := range collectionTexts {
		t := t
		wg.SafeGo(func(ctx context.Context) error {
			prompt := config.GetStringOr("llm.chat_collection_to_suggest_prompt", "根据以下内容，生成1个问题，不超过25个字，开头不要说“根据”二字：{}\n")
			chatText := collectionToSuggestText(t, prompt)
			if chatText == "" {
				return nil
			}
			sus := l.chatSuggestRequest(ctx, 1, chatText)
			mu.Lock()
			suggests = append(suggests, sus...)
			mu.Unlock()
			return nil
		})
	}
	wg.Wait()
	return suggests
}

// GetSendRecordSuggest ...
func GetSendRecordSuggest(ctx context.Context, messageIds []uint64) (map[uint64][]string,
	map[uint64]aipb.AskSuggestionMode, error) {
	m, err := GetSuggestByMessageIds(ctx, messageIds)
	if err != nil || len(m) == 0 {
		return nil, nil, err
	}

	suggestResult := map[uint64][]string{}
	suggestModel := map[uint64]aipb.AskSuggestionMode{}
	for k, v := range m {
		for _, suggest := range v {
			suggestResult[k] = append(suggestResult[k], suggest.Suggest)
		}
		if len(v) > 0 {
			suggestModel[k] = aipb.AskSuggestionMode(v[0].Mode)
		}
	}
	return suggestResult, suggestModel, nil
}

// GetSuggestByMessageIds ...
func GetSuggestByMessageIds(ctx context.Context, messageIds []uint64) (map[uint64][]*model.TChatSuggest, error) {
	if len(messageIds) == 0 {
		return nil, nil
	}
	suggests, err := model.NewQuery[model.TChatSuggest](ctx).GetBy("message_id in ?", messageIds)
	if err != nil {
		return nil, err
	}
	if len(suggests) == 0 {
		return nil, nil
	}

	m := make(map[uint64][]*model.TChatSuggest)
	for _, suggest := range suggests {
		m[suggest.MessageID] = append(m[suggest.MessageID], suggest)
	}

	return m, nil
}
