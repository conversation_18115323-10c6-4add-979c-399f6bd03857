package chat

import (
	"strings"
	"testing"
)

func replace(text string) []*SqlUtils {
	originalSql := extractSQL(text)
	originalSql = sqlPreHandler(originalSql)
	sqlTexts := splitSQLByUnionAll(originalSql)
	utils := make([]*SqlUtils, 0, len(sqlTexts))
	for _, query := range sqlTexts {
		query = strings.ReplaceAll(query, ";", "")
		s := NewSqlUtils().withTableType(query)
		conditions := s.extractKeywordsConditions(query)
		if len(conditions) > 0 {
			newConditions := s.buildNewConditions(conditions)
			s.sqlQuery = s.replaceConditions(newConditions, query)
		} else {
			s.sqlQuery = query
		}
		utils = append(utils, s)
	}
	return utils
}

func Test_HandleAIsql(m *testing.T) {
	// 示例查询
	originalQuery := "```sql\\nSELECT * FROM v_published_team WHERE location_addresses LIKE '%欧洲%' AND keywords_team LIKE '%新材料%';\\n```\""
	//originalQuery := "jdkjskskjalf sdjalkfjl "
	replace(originalQuery)
}
