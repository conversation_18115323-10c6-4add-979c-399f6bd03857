package chat

import (
	"context"
	"errors"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// WorkWeixinMaxSendSize 企业微信一次性最大可推送消息条数
	WorkWeixinMaxSendSize  = 5
	AiDealQuestionCountKey = "{chat:%d}:ai:deal:questionCount" // AiDealQuestionCountKey 当前正在处理的问题计数
	RecordTypeContinueMenu = 1                                 // 记录需要追加为继续回答菜单
)

//CreateRecordLockKey        = "ai:workWeinxin:createRecordLock:%d"
/*		lockName := fmt.Sprintf(logic.CreateRecordLockKey, req.ChatId)
		dmtx := logic.NewDistributedLock(lockName, redsync.WithExpiry(time.Hour), redsync.WithTries(10))
		dmtx.Lock()
		defer dmtx.Unlock()
*/

// GetRecordSendInfo 判断 是否需要发送，以及是否需要追加 继续回答
func GetRecordSendInfo(ctx context.Context, chatId uint64) (bool, bool) {
	canBeSend, questionNum, interval := GetRecordCanBeSend(ctx, chatId)
	if canBeSend > 1 { // 可发送数量大于1，不需要发送继续回答
		return true, false
	}
	if questionNum > 1 { // 还有其他问题在处理
		if canBeSend == 1 { // 只能发送一条消息了
			return true, true
		}
		return false, interval+1 >= WorkWeixinMaxSendSize // 间隔的数量加上当前要回答的问题 刚好为最大可发送数量，则追加继续回答
	}
	return canBeSend == 1, interval+1 > WorkWeixinMaxSendSize // 没有其他问题要处理了，如果间隔加上当前的问题超过最大可发送数量，则追加继续回答
}

// SetUnSendQuestionCount 设置未被处理的问题数量
func SetUnSendQuestionCount(ctx context.Context, chatId uint64, add bool) {
	key := fmt.Sprintf(AiDealQuestionCountKey, chatId)
	if add {
		result, err := xredis.Default.Incr(ctx, key).Result() // + 1
		log.WithContext(ctx).Debugw("ai SetUnSendQuestionCount add", "result", result,
			"chatId", chatId, "add", add, "err", err)
		return
	}
	log.WithContext(ctx).Debugw("ai SetUnSendQuestionCount Info", "chatId", chatId)
	// 自减并在值为 0 时删除键
	luaScript := `
		local question = redis.call('GET', KEYS[1])  -- 获取键的当前值
		if question then
			question = tonumber(question)  -- 转换为数字
			question = question - 1  -- 自减 1
			if question <= 0 then
				redis.call('DEL', KEYS[1])  -- 如果值为 0，删除该键
			else
				redis.call('SET', KEYS[1], question)  -- 否则更新值
			end
		else
			return "key not exists"  -- 如果键不存在，返回
		end
	`
	result, err := xredis.Default.Eval(ctx, luaScript, []string{key}).Result()
	if !errors.Is(err, redis.Nil) {
		log.WithContext(ctx).Errorw("Ai ReSetUnSendRecordCount Error", "err", err, "result", result)
		return
	}
}

// GetRecordCanBeSend
//
//	@Description: 获取可以发送的数量 及 还有多少个问题待处理 及 离上一个 追加了继续回答的问题的间隔
//	@param ctx
//	@param chatId
//	@return int 可以发送的数量
//	@return int 多少个问题待处理
//	@return int 追加了继续回答的问题的间隔
func GetRecordCanBeSend(ctx context.Context, chatId uint64) (int, int, int) {
	records, err := model.NewQuery[model.TChatSendRecord](ctx).Select("id", "type", "send_type").
		Where("chat_id = ? ", chatId).
		Where("message_type in ? ", []aipb.AiRecordMessageType{
			aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL,
			aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER,
		}).
		Limit(WorkWeixinMaxSendSize).
		OrderBy("id", true).
		Get()
	if err != nil {
		log.WithContext(ctx).Errorw("GetRecordCanBeSend Error", "err", err)
		return 0, 0, 0
	}

	canBeSend := WorkWeixinMaxSendSize

	interval := 0
	found := false

	for i := 0; i < len(records); i++ {
		if records[i].Type == aipb.AiRecordType_AI_RECORD_TYPE_USER { // 判断当前会话用户还能接收多少个问题
			break
		}
		if records[i].SendType == 1 {
			found = true
		}
		if !found { // 这个问题追加了继续回答
			interval++
		}
		canBeSend--
	}

	key := fmt.Sprintf(AiDealQuestionCountKey, chatId)
	questionNum, err := xredis.Default.Get(ctx, key).Int()
	if err != nil {
		log.WithContext(ctx).Errorw("GetRecordCanBeSend Error", "err", err)
	}
	return canBeSend, questionNum, interval
}

// GetCreateSendRecord
//
//	@Description:
//	@param ctx
//	@param req
//	@return []*model.TChatSendRecord 需要入库的数据
//	@return 企业微信能推送的条数
//	@return error
func GetCreateSendRecord(ctx context.Context, req *aipb.ReqCreateSendRecord) ([]*model.TChatSendRecord, int, int, int, error) {
	var normalPieces, otherPieces []*aipb.ReqCreateSendRecord_Piece
	for _, v := range req.Pieces {
		if v.MessageType == aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL ||
			v.MessageType == aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER {
			normalPieces = append(normalPieces, v)
		} else {
			otherPieces = append(otherPieces, v)
		}
	}

	var rows []*model.TChatSendRecord

	if len(otherPieces) > 0 { // 处理重复消息和要隐藏的消息
		now := time.Now()
		for _, v := range otherPieces {
			rows = append(rows, &model.TChatSendRecord{
				MessageID:   req.MessageId,
				ChatID:      req.ChatId,
				Type:        v.Type,
				SendDate:    now,
				MessageType: v.MessageType,
				Content:     v.One + v.Two,
				ExtraInfo:   v.ExtraInfo,
				ExtraID:     v.ExtraId,
			})
		}
	}

	canBeSend, questionNum, interval := GetRecordCanBeSend(ctx, req.ChatId)
	if len(normalPieces) == 0 {
		return rows, len(otherPieces), canBeSend, questionNum, nil
	}

	splitNum := 0 // 如果有消息被拆分了，则需要将判断消息的数量做对应的增加
	for i, v := range normalPieces {
		split := false
		row := &model.TChatSendRecord{
			MessageID: req.MessageId,
			ChatID:    req.ChatId,
			Type:      v.Type,
			ExtraInfo: v.ExtraInfo,
			ExtraID:   v.ExtraId,
		}
		rows = append(rows, row)

		if canBeSend > 0 && (i+1+splitNum == canBeSend || (i+1+splitNum-canBeSend)%WorkWeixinMaxSendSize == 0) || // 当前可推送数量大于0 ，则根据可推送数量来判断是否拆分
			canBeSend == 0 && (i+1+splitNum+interval)%WorkWeixinMaxSendSize == 0 { // 可推送数量为0 ，则根据间隔来计算是否拆分
			row.SendType = RecordTypeContinueMenu // 因为当前这一份消息需要发送为菜单消息，追加“继续回答”

			if len(v.Two) > 0 || len(v.Url) > 0 {
				// == canBeSend 到达本次能发送的上限，如果有message_two，则要拆分这个info
				// (i+1)%maxSendSize 到达5次的上限，此处要拆分为菜单消息
				split = true
			}

		}

		if split { // 能发送的上限，如果有message_two，则要拆分这个info
			rowTwo := &model.TChatSendRecord{
				MessageID: req.MessageId,
				ChatID:    req.ChatId,
				Type:      v.Type,
			}

			if len(v.Url) > 0 { // 当前是图片，要拆分一个继续回答的菜单出来，且这个继续回答的菜单是隐藏的
				row.Type = aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU
				row.MessageType = aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER
				row.ExtraID = ""
				rowTwo.ExtraID = v.ExtraId
				rowTwo.Content = v.Url
			} else {
				row.Content = v.One
				//row.Type = uint32(aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU)
				//row.SendType = RecordTypeContinueMenu
				rowTwo.Content = v.Two
			}
			rows = append(rows, rowTwo)
			splitNum++
		} else if len(v.Url) > 0 {
			row.Content = v.Url
		} else { // 结果需要合并 或者 v.Two 为空
			row.Content = v.One + v.Two
		}
	}

	return rows, len(otherPieces), canBeSend, questionNum, nil
}

// ChatSendRecordInfo ...
type ChatSendRecordInfo struct {
	Id           uint64    `gorm:"primaryKey;column:id" json:"id"`
	MessageId    uint64    `gorm:"column:message_id" json:"messageId,omitempty"`
	MessageType  uint32    `gorm:"column:message_type" json:"messageType,omitempty"`
	Content      string    `gorm:"column:content" json:"text,omitempty"`
	SendDate     time.Time `gorm:"column:send_date" json:"sendDate,omitempty"`
	Type         int32     `gorm:"column:type" json:"type,omitempty"`
	RatingScale  int32     `gorm:"column:rating_scale" json:"ratingScale,omitempty"`
	ShowType     int32     `gorm:"column:show_type" json:"showType,omitempty"`
	RejectReason string    `gorm:"column:reject_reason" json:"rejectReason,omitempty"`
	//SuggestQuestion string               `gorm:"column:suggest_question" json:"suggestQuestion,omitempty"` // 2.8迭代改为从t_chat_suggest 中获取
	AskType      int32                `gorm:"column:ask_type;default:1" json:"askType"`           // 问答枚举 QUESTION_ASK_TYPE  1 正常问答 2 重新回答 3 继续回答 4 预设问答
	MatchPattern aipb.DocMatchPattern `gorm:"column:match_pattern;default:0" json:"matchPattern"` // 匹配模式
}

// ToProto ...
func (m *ChatSendRecordInfo) ToProto(suggestMap map[uint64][]string,
	suggestModelMap map[uint64]aipb.AskSuggestionMode) *aipb.ChatSendRecordInfo {
	result := &aipb.ChatSendRecordInfo{
		Id:                 m.Id,
		MessageId:          m.MessageId,
		MessageType:        aipb.ChatMessageType(m.MessageType),
		Content:            m.Content,
		SendDate:           timestamppb.New(m.SendDate),
		RecordType:         aipb.AiRecordType(m.Type),
		MessageRatingScale: aipb.RatingScale(m.RatingScale),
		ShowType:           m.ShowType,
		RejectReason:       m.RejectReason,
		DocMatchPattern:    m.MatchPattern,
	}
	if m.Type == int32(aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU) {
		result.SuggestQuestions = suggestMap[m.MessageId]
		result.SuggestionMode = suggestModelMap[m.MessageId]
	}
	if m.Type == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER) &&
		(m.AskType == int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE) ||
			m.AskType == int32(aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE)) {
		result.ImageUrl = []string{m.Content} // 文本中是image链接，直接放入ImageUrl
		result.Content = ""
	}
	return result
}

// SendRecordToProto ...
func SendRecordToProto(v *model.TChatSendRecord) *aipb.AiSendResultRecord {
	if v == nil {
		return nil
	}

	return &aipb.AiSendResultRecord{
		Id:      v.ID,
		Uuid:    v.UUID,
		Type:    v.Type,
		Content: v.Content,
	}
}
