package chat

import (
	"context"
	"net/url"
	"os"
	"path"
	"path/filepath"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// FindQAContainsText 查找包含text文本的QA
func FindQAContainsText(ctx context.Context, assistantIds []uint64, text string, topN int) ([]*model.TDoc, error) {
	db := model.NewQuery[model.TDoc](ctx).With("Contributors").Scope(QAContainsTextScope(assistantIds, text)).Limit(topN)
	return db.Get()
}

// QAContainsTextScope 查找包含text文本的QA
// TODO：如何优化，暂时使用LIKE进行文本匹配
func QAContainsTextScope(assistantIds []uint64, text string, isOr ...bool) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(assistantIds) == 0 || len(text) == 0 {
			return db
		}
		subQ := db.Session(&gorm.Session{NewDB: true}).Model(&model.TAssistantDoc{}).
			Where("assistant_id IN (?)", assistantIds).
			Where("state = ?", ai.DocState_DOC_STATE_ENABLED).
			Where("doc_id = t_doc.id").
			Select("1")
		subQ2 := db.Session(&gorm.Session{NewDB: true}).Model(&model.TDocMatchPattern{}).
			Where("doc_id = t_doc.id").
			Where("match_pattern = ?", ai.DocMatchPattern_DOC_MATCH_PATTERN_CONTAINS).
			Select("1")

		orGroup := db.Session(&gorm.Session{NewDB: true}).Model(&model.TDoc{}).
			Where("EXISTS (?)", subQ).
			Where("EXISTS (?)", subQ2).
			Where("data_type = ?", ai.DocType_DOCTYPE_QA).
			Where("index_text LIKE ?", "%"+xorm.EscapeLikeWildcards(text)+"%")

		if len(isOr) > 0 && isOr[0] {
			db.Scopes(func(db *gorm.DB) *gorm.DB {
				return db.Or(orGroup)
			})
		} else {
			db.Scopes(func(db *gorm.DB) *gorm.DB {
				return db.Where(orGroup)
			})
		}

		return db
	}
}

func GetFileToParseFromCos(ctx context.Context, refUrl string) (origin string, dirPath string, err error) {
	const docFilePathPattern = "chat_doc"

	dirPath, err = os.MkdirTemp("./", docFilePathPattern)
	if err != nil {
		return
	}
	originFilePath := path.Join(dirPath, filepath.Base(refUrl))
	// 1. 获取原始文件
	err = new(logic.CosHelper).GetFile(ctx, refUrl, originFilePath)
	if err != nil {
		return
	}

	return originFilePath, dirPath, nil
}

func extractPathFromURL(fullURL string) (string, error) {
	// 解析URL
	u, err := url.Parse(fullURL)
	if err != nil {
		return "", err
	}
	// 去掉开头的斜杠（如果有）
	if len(u.Path) > 0 && u.Path[0] == '/' {
		u.Path = u.Path[1:]
	}
	// 返回路径部分
	return u.Path, nil
}

func ChatMessageFileToParse(ctx context.Context, file *model.TChatMessageFile) (string, error) {
	var localPath, parsedText, dirPath string
	var parseErr error

	defer func() {
		if dirPath != "" {
			os.RemoveAll(dirPath)
		}
	}()

	if file.Type != logic.DocFileTypeAudio && file.Type != logic.DocFileTypeVideo {
		pathUrl, err := extractPathFromURL(file.URL)
		if err != nil {
			return "", err
		}
		localPath, dirPath, parseErr = GetFileToParseFromCos(ctx, pathUrl)
		if parseErr != nil {
			log.WithContext(ctx).Errorw("GetFileToParseFromCos failed", "err", parseErr, "url", file.URL)
			return "", parseErr
		}
	}

	parsedText, parseErr = logic.DoParseDoc(ctx, localPath, file.URL, nil, &model.TDocExtend{})
	if parseErr != nil {
		log.WithContext(ctx).Errorw("DoParseDoc failed", "err", parseErr, "url", file.URL)
		return "", parseErr
	}

	return parsedText, nil
}

// UpdateFileParseState 更新文件解析状态
func UpdateFileParseState(ctx context.Context, fileID uint64, state ai.ChatMessageFileState, text string) error {
	err := model.NewQuery[model.TChatMessageFile](ctx).DB().
		Model(&model.TChatMessageFile{}).
		Where("id = ?", fileID).
		Updates(map[string]interface{}{
			"state": int32(state),
			"text":  text,
		}).Error

	if err != nil {
		log.WithContext(ctx).Errorw("Update TChatMessageFile failed", "err", err, "fileID", fileID)
		return err
	}
	return nil
}
