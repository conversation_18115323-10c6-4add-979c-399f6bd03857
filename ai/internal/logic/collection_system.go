package logic

import (
	"context"
	"errors"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"gorm.io/gorm"
)

var systemDocNotFoundErr = xerrors.NotFoundError(nil).WithLocalization(&xerrors.Localization{
	MessageId: "AiSystemDocNotFound",
})

// CreateSystemDocCopyLogic ...
type CreateSystemDocCopyLogic struct{}

// Create 创建
func (l *CreateSystemDocCopyLogic) Create(
	ctx context.Context, docID uint64, creator *basepb.Identity,
) (uint64, error) {
	source, err := findSystemDoc(ctx, docID, []string{"id", "is_copy"}, "States", "Contributors")
	if err != nil {
		return 0, err
	}
	if source.IsCopy {
		return 0, systemDocNotFoundErr
	}

	const docInsertColumns = "state, contributor, text, index_text, index_text_md5, ref, file_name, " +
		"rag_filename, update_by, update_by_type, update_date, create_by, create_by_type, create_date, " +
		"ugc_type, ugc_id, data_type, show_contributor, is_copy, main_id, ugc_title, is_system, content_state, data_source"
	const docSelectColumns = "state, contributor, text, index_text, index_text_md5, ref, file_name, " +
		"?, ?, ?, ?, ?, ?, ?, " +
		"ugc_type, ugc_id, data_type, show_contributor, ?, id, ugc_title, is_system, ?, data_source"
	const copyDocSql = "INSERT INTO t_doc (" + docInsertColumns + ") " +
		"SELECT " + docSelectColumns + " FROM t_doc WHERE id = ?"

	now := time.Now()
	newRagFileName := GenDocRagFileName()
	copyDocValues := []any{
		newRagFileName, creator.IdentityId, creator.IdentityType, now, creator.IdentityId,
		creator.IdentityType, now, 1, aipb.DocContentState_DOC_CONTENT_STATE_MANUAL, docID,
	}

	var newDocID uint64
	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		// 拷贝文档
		if err := tx.Exec(copyDocSql, copyDocValues...).Error; err != nil {
			return fmt.Errorf("copy system doc: %w", err)
		}

		if err := tx.Raw("SELECT LAST_INSERT_ID() AS id").Pluck("id", &newDocID).Error; err != nil {
			return fmt.Errorf("get new doc id: %w", err)
		}

		// 拷贝助手
		if len(source.States) > 0 {
			states := make([]*model.TAssistantDoc, 0, len(source.States))
			for _, state := range source.States {
				states = append(states, &model.TAssistantDoc{
					DocID:       newDocID,
					AssistantID: state.AssistantID,
					State:       aipb.DocState_DOC_STATE_DISABLED,
					IsShared:    state.IsShared,
				})
			}
			if err := tx.Create(states).Error; err != nil {
				return fmt.Errorf("copy states: %w", err)
			}
		}

		// 拷贝贡献者
		if len(source.Contributors) > 0 {
			contributors := make([]*model.TDocContributor, 0, len(source.Contributors))
			for _, contributor := range source.Contributors {
				contributors = append(contributors, &model.TDocContributor{
					DocID:           newDocID,
					ContributorID:   contributor.ContributorID,
					ContributorText: contributor.ContributorText,
					ContributorType: contributor.ContributorType,
				})
			}
			if err := tx.Create(contributors).Error; err != nil {
				return fmt.Errorf("copy contributors: %w", err)
			}
		}

		return nil
	})
	if err != nil {
		return 0, err
	}

	return newDocID, nil
}

// EnableSystemDocLogic ...
type EnableSystemDocLogic struct{}

// Enable 启用
func (l *EnableSystemDocLogic) Enable(ctx context.Context, docID uint64, operator *basepb.Identity) error {
	mainDoc, target, others, err := findAllSystemDocs(ctx, docID)
	if err != nil {
		return err
	}

	// 不能启用
	if mainDoc.ContentState == aipb.DocContentState_DOC_CONTENT_STATE_UNPUBLISHED ||
		mainDoc.ContentState == aipb.DocContentState_DOC_CONTENT_STATE_DELETED {
		return xerrors.NewCode(errorspb.AiError_AiInvalidDocContentState)
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		if target != nil {
			target.UpdateBy = operator.IdentityId
			target.UpdateByType = operator.IdentityType
			target.UpdateDate = time.Now()
			changes := []string{"update_by", "update_by_type", "update_date"}
			if !target.IsCopy {
				target.ContentState = aipb.DocContentState_DOC_CONTENT_STATE_SYNCING
				changes = append(changes, "content_state")
			}
			if err := tx.Select(changes).Save(target).Error; err != nil {
				return fmt.Errorf("update t_doc: %w", err)
			}
			if err := OnOffDocInBulk(ctx, tx, aipb.DocState_DOC_STATE_ENABLED, []uint64{target.ID}); err != nil {
				return fmt.Errorf("enable doc: %w", err)
			}
		}

		for _, doc := range others {
			if err := OnOffDocInBulk(ctx, tx, aipb.DocState_DOC_STATE_DISABLED, []uint64{doc.ID}); err != nil {
				return fmt.Errorf("disable doc: %w", err)
			}
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// DisableSystemDocLogic ...
type DisableSystemDocLogic struct{}

// Disable 禁用
func (l *DisableSystemDocLogic) Disable(ctx context.Context, docID uint64, operator *basepb.Identity) error {
	mainDoc, target, _, err := findAllSystemDocs(ctx, docID)
	if err != nil {
		return err
	}

	// 不能禁用
	if mainDoc.ContentState == aipb.DocContentState_DOC_CONTENT_STATE_UNPUBLISHED ||
		mainDoc.ContentState == aipb.DocContentState_DOC_CONTENT_STATE_DELETED {
		return xerrors.NewCode(errorspb.AiError_AiInvalidDocContentState)
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		target.UpdateBy = operator.IdentityId
		target.UpdateByType = operator.IdentityType
		target.UpdateDate = time.Now()
		changes := []string{"update_by", "update_by_type", "update_date"}
		if err := tx.Select(changes).Save(target).Error; err != nil {
			return fmt.Errorf("update t_doc: %w", err)
		}
		if err := OnOffDocInBulk(ctx, tx, aipb.DocState_DOC_STATE_DISABLED, []uint64{target.ID}); err != nil {
			return fmt.Errorf("disable doc: %w", err)
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// DeleteSystemDocLogic ...
type DeleteSystemDocLogic struct{}

// Delete 删除
func (l *DeleteSystemDocLogic) Delete(ctx context.Context, docID uint64, operator *basepb.Identity) error {
	target, err := findSystemDoc(ctx, docID, systemDocSelectForAction)
	if err != nil {
		return err
	}
	if !target.IsCopy && target.ContentState != aipb.DocContentState_DOC_CONTENT_STATE_DELETED {
		return xerrors.New(errorspb.AiError_AiInvalidDocContentState, "内容状态非“已删除”的系统文档不可被删除")
	}

	var copies []*model.TDoc
	if !target.IsCopy {
		copies, err = model.NewQuery[model.TDoc](ctx).
			Select(systemDocSelectForAction).Where("main_id = ? and is_copy = ?", target.ID, 1).Get()
		if err != nil {
			return fmt.Errorf("query copies: %w", err)
		}
	}

	deleteIDs := []uint64{target.ID}
	if len(copies) > 0 {
		for _, doc := range copies {
			deleteIDs = append(deleteIDs, doc.ID)
		}
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		target.UpdateBy = operator.IdentityId
		target.UpdateByType = operator.IdentityType
		target.UpdateDate = time.Now()
		changes := []string{"update_by", "update_by_type", "update_date"}
		if err := tx.Select(changes).Save(target).Error; err != nil {
			return fmt.Errorf("update target doc: %w", err)
		}

		if err := DeleteDocInBulk(ctx, tx, deleteIDs, true, nil); err != nil {
			return fmt.Errorf("delete docs: %w", err)
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

var systemDocSelectForAction = []string{
	"id", "is_copy", "main_id", "is_system", "content_state",
}

// 查询系统文档
func findSystemDoc(ctx context.Context, docID uint64, fields []string, relations ...string) (*model.TDoc, error) {
	query := model.NewQuery[model.TDoc](ctx)

	for _, relation := range relations {
		if relation == "Copies" {
			query.With(relation, "is_copy = ?", 1)
		} else {
			query.With(relation)
		}
	}

	doc, err := query.Select(fields).Where("id = ? and data_source = ?", docID, aipb.DocDataSource_DOC_DATA_SOURCE_UGC).Find()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, systemDocNotFoundErr
		}
		return nil, fmt.Errorf("query system doc: %w", err)
	}

	return doc, nil
}

func findAllSystemDocs(ctx context.Context,
	docID uint64,
) (mainDoc, target *model.TDoc, others []*model.TDoc, err error) {
	docs, err := model.NewQuery[model.TDoc](ctx).
		Select(systemDocSelectForAction).
		Where("main_id = (SELECT main_id FROM t_doc WHERE id = ? AND data_source = ?)", docID, aipb.DocDataSource_DOC_DATA_SOURCE_UGC).
		Get()
	if err != nil {
		err = errors.New("query docs: " + err.Error())
		return
	}
	if len(docs) == 0 {
		err = systemDocNotFoundErr
		return
	}

	for _, doc := range docs {
		if doc.MainID == doc.ID {
			mainDoc = doc
		}
		if doc.ID == docID {
			target = doc
		} else {
			others = append(others, doc)
		}
	}

	if target == nil || mainDoc == nil {
		err = systemDocNotFoundErr
		return
	}

	return
}
