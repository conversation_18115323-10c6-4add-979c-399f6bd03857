package logic

import (
	"context"
	"errors"

	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
)

// GetAssistantWithCollection 通过主键id获取助手详情，包括collection信息
func GetAssistantWithCollection(ctx context.Context, id uint64) (*model.TAssistant, error) {
	var assistant model.TAssistant
	err := model.NewQuery[model.TAssistant](ctx).DB().
		Scopes(model.AssistantWithCollection).Where("id = ?", id).Find(&assistant).Error
	if err != nil {
		return nil, err
	}
	return &assistant, nil
}

// GetAssistantsWithCollection 批量通过主键id获取助手详情，包括collection信息
func GetAssistantsWithCollection(ctx context.Context, id ...uint64) ([]*model.TAssistant, error) {
	var assistant []*model.TAssistant
	err := model.NewQuery[model.TAssistant](ctx).DB().
		Scopes(model.AssistantWithCollection).Where("id in ?", id).Find(&assistant).Error
	if err != nil {
		return nil, err
	}
	return assistant, nil
}

// GetChatFinishState 获取对应的会话最大保持时间********分钟
func GetChatFinishState(duration int64) int64 {
	if duration > 2880 || duration < 1 { // 60*48 = 2880 // 48小时的分钟数
		duration = 120 // 默认120分钟
	}
	return duration
}

// GetManagedAssistants 获取用户管理的助手
func GetManagedAssistants(ctx context.Context, operator *ai.Operator) ([]uint64, bool, error) {
	if operator == nil {
		return nil, false, errors.New("operator is nil")
	}
	if operator.Type == base.IdentityType_IDENTITY_TYPE_MGMT {
		return nil, true, nil
	}

	admin := &ai.AssistantAdmin{Id: operator.Id}
	admin.Type = operator.Type
	query, err := ListAssistantApplyFilter(ctx, &ai.ReqListAssistant{
		Admins: []*ai.AssistantAdmin{admin},
	})
	if err != nil {
		return nil, false, err
	}

	var ids []uint64
	return ids, false, query.Pluck("id", &ids)
}

// ListAssistantApplyFilter ...
func ListAssistantApplyFilter(ctx context.Context, req *ai.ReqListAssistant) (*xorm.Query[model.TAssistant], error) {
	query := model.NewQuery[model.TAssistant](ctx)
	if len(req.Ids) != 0 {
		query = query.Where("id in ?", req.Ids)
	}
	if len(req.Admins) != 0 {
		query.Scope(model.AssistantByAdmins(req.Admins...))
	}
	if req.WithCollection {
		query.Scope(model.AssistantWithCollection)
	}
	if len(req.Name) > 0 && req.Language == "en" {
		query = query.Where("name_en like ?", "%"+xorm.EscapeLikeWildcards(req.Name)+"%")
	} else if len(req.Name) > 0 {
		query = query.Where("name like ?", "%"+xorm.EscapeLikeWildcards(req.Name)+"%")
	}

	switch req.Type {
	case ai.ChatType_CHAT_TYPE_WECHAT:
		query = query.Where("channel in (?)", []ai.AssistantChannel{
			ai.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
			ai.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN,
		})
	case ai.ChatType_CHAT_TYPE_WEB:
		query = query.Where("channel = ?", ai.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB)
	case ai.ChatType_CHAT_TYPE_WHATSAPP:
		query = query.Where("channel = ?", ai.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP)
	}

	// 不返回草稿
	query = query.Where("is_draft", 0)
	return query, nil
}
