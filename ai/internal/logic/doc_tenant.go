package logic

import (
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// GenDocTenantScope 生成租户隔离查询
// 用户前台需要传 contributor 和 scopedAssistant，用于租户隔离查询
// 限定在同一个租户（贡献者或者绑定了助手的知识）内
func GenDocTenantScope(contributor *aipb.Contributor, scopedAssistant ...uint64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 租户隔离查询
		subQ := model.NewQuery[model.TDoc](db.Statement.Context).DB()

		// 构建权限过滤条件
		hasCondition := false

		// 处理contributor条件
		if contributor != nil {
			hasCondition = true

			// 构建EXISTS子查询
			contributorQuery := model.NewQuery[model.TDocContributor](db.Statement.Context).DB().
				Select("1").
				Where("t_doc_contributor.doc_id = t_doc.id")

			if contributor.Id != 0 {
				contributorQuery = contributorQuery.
					Where("t_doc_contributor.contributor_id = ? AND t_doc_contributor.contributor_type = ?", contributor.Id, contributor.Type)
			} else if len(contributor.Text) != 0 {
				contributorQuery = contributorQuery.
					Where("t_doc_contributor.contributor_text = ? AND t_doc_contributor.contributor_type = ?", contributor.Text, contributor.Type)
			}

			subQ = subQ.Where("EXISTS (?)", contributorQuery)
		}

		// 处理assistantIds条件
		if len(scopedAssistant) != 0 {
			// 构建EXISTS子查询
			assistantQuery := model.NewQuery[model.TAssistantDoc](db.Statement.Context).DB().
				Select("1").
				Where("t_assistant_doc.doc_id = t_doc.id").
				Where("t_assistant_doc.assistant_id IN ?", scopedAssistant).
				Where("t_assistant_doc.state IN ?", []ai.DocState{
					ai.DocState_DOC_STATE_ENABLED,
					ai.DocState_DOC_STATE_DISABLED,
				})

			if hasCondition {
				subQ = subQ.Or("EXISTS (?)", assistantQuery)
			} else {
				subQ = subQ.Where("EXISTS (?)", assistantQuery)
			}
		}
		return db.Where(subQ)
	}
}
