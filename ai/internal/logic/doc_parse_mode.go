package logic

import (
	"context"
	"errors"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/util/ocr"
)

// DocParserByMode 指定解析模式解析
type DocParserByMode struct {
	parseMode ai.DocParseMode
	result    string
	err       error
}

// PdfModeParse pdf指定解析模式解析
func (p *DocParserByMode) PdfModeParse(ctx context.Context, path string, url string,
	record DocParseProgress) bool {
	switch p.parseMode {
	case ai.DocParseMode_DOC_PARSE_MODE_UNSPECIFIED: // 智能选择
		return false
	case ai.DocParseMode_DOC_PARSE_MODE_SMART: // 大模型
		p.modeParseByLLM(ctx, url)
	case ai.DocParseMode_DOC_PARSE_MODE_FILE: // 文件解析 TIKA解析
		_ = p.modeParseFileByTika(path, false)
	case ai.DocParseMode_DOC_PARSE_MODE_IMAGE: // 图像解析 OCR图像解析
		p.modeParsePdfByOcr(ctx, path, url, record, false)
	case ai.DocParseMode_DOC_PARSE_MODE_TABLE: // 表格解析 OCR表格解析
		p.modeParsePdfTableByOcr(ctx, path, url, record)
	default:
		return false
	}
	return true
}

// PictureModeParse 图片指定解析模式解析
func (p *DocParserByMode) PictureModeParse(ctx context.Context, url string) bool {
	switch p.parseMode {
	case ai.DocParseMode_DOC_PARSE_MODE_UNSPECIFIED: // 智能选择
		return false
	case ai.DocParseMode_DOC_PARSE_MODE_SMART: // 大模型
		p.modeParseByLLM(ctx, url)
	case ai.DocParseMode_DOC_PARSE_MODE_FILE: // 文件解析 TIKA解析
		p.err = errors.New("unsupported parsing mode")
	case ai.DocParseMode_DOC_PARSE_MODE_IMAGE: // 图像解析 OCR图像解析
		p.modeParseImageByOcr(ctx, url)
	case ai.DocParseMode_DOC_PARSE_MODE_TABLE: // 表格解析 OCR表格解析
		p.modeParseTableByOcr(ctx, url)
	default:
		return false
	}
	return true
}

// DefaultModeParse 指定解析模式解析
func (p *DocParserByMode) DefaultModeParse(ctx context.Context, path string, url string) bool {
	switch p.parseMode {
	case ai.DocParseMode_DOC_PARSE_MODE_UNSPECIFIED: // 智能选择
		return false
	case ai.DocParseMode_DOC_PARSE_MODE_SMART: // 大模型
		p.modeParseByLLM(ctx, url)
	case ai.DocParseMode_DOC_PARSE_MODE_FILE: // 文件解析 TIKA解析
		_ = p.modeParseFileByTika(path, false)
	case ai.DocParseMode_DOC_PARSE_MODE_IMAGE, ai.DocParseMode_DOC_PARSE_MODE_TABLE:
		p.err = errors.New("unsupported parsing mode")
	default:
		return false
	}
	return true
}

// IsOnlyFileModeParse ...
func (p *DocParserByMode) IsOnlyFileModeParse() bool {
	if p.parseMode == ai.DocParseMode_DOC_PARSE_MODE_SMART || p.parseMode == ai.DocParseMode_DOC_PARSE_MODE_IMAGE ||
		p.parseMode == ai.DocParseMode_DOC_PARSE_MODE_TABLE {
		p.err = errors.New("unsupported parsing mode")
		return false
	}
	p.parseMode = ai.DocParseMode_DOC_PARSE_MODE_FILE // 文件解析 TIKA解析
	return true
}

// modeParseFileByTika tika 解析文件
func (p *DocParserByMode) modeParseFileByTika(path string, totalPage bool) int {
	p.parseMode = ai.DocParseMode_DOC_PARSE_MODE_FILE
	tika, page, err := ParseFileByTika(path, totalPage)
	p.err = err
	p.result = tika
	return page
}

// modeParsePdfByOcr ocr 解析pdf图像
func (p *DocParserByMode) modeParsePdfByOcr(ctx context.Context, path string, url string,
	record DocParseProgress, useLLM bool) *DocParserByMode {
	text, parseMode, err := GetOcrHelper().OcrPdf(ctx, path, url, record, useLLM, false)
	p.parseMode = parseMode
	p.err = err
	p.result = text
	return p
}

// modeParseImageByOcr ocr 解析图像
func (p *DocParserByMode) modeParseImageByOcr(ctx context.Context, url string) *DocParserByMode {
	text, err := GetOcrHelper().OcrPictureUrl(ctx, url)
	p.parseMode = ai.DocParseMode_DOC_PARSE_MODE_IMAGE
	p.err = err
	p.result = text
	return p
}

// modeParsePdfTableByOcr ocr 解析PDF表格
func (p *DocParserByMode) modeParsePdfTableByOcr(ctx context.Context, path string, url string,
	record DocParseProgress) *DocParserByMode {
	text, _, err := GetOcrHelper().OcrPdf(ctx, path, url, record, false, true)
	p.parseMode = ai.DocParseMode_DOC_PARSE_MODE_TABLE
	//p.parseMode = parseMode
	p.err = err
	p.result = text
	return p
}

// modeParseTableByOcr ocr 解析表格
func (p *DocParserByMode) modeParseTableByOcr(ctx context.Context, url string) *DocParserByMode {
	text, err := ocr.GteInstance().File2TableMarkdown(ctx, "", url, 0)
	p.parseMode = ai.DocParseMode_DOC_PARSE_MODE_TABLE
	p.err = err
	p.result = text
	return p
}

// modeParseByLLM 大模型解析
func (p *DocParserByMode) modeParseByLLM(ctx context.Context, url string) *DocParserByMode {
	p.parseMode = ai.DocParseMode_DOC_PARSE_MODE_SMART
	text, err := GetOcrHelper().ReconstructFileUrlLM(ctx, url)
	p.err = err
	p.result = text
	return p
}

func (p *DocParserByMode) GetModeParseResult() (string, error) {
	return p.result, p.err
}

// GetParseMode 获取解析模式
func (p *DocParserByMode) GetParseMode() ai.DocParseMode {
	if p.parseMode == ai.DocParseMode_DOC_PARSE_MODE_UNSPECIFIED {
		return ai.DocParseMode_DOC_PARSE_MODE_SMART
	}
	return p.parseMode
}
