package logic

import (
	"context"
	"errors"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// 允许评价的消息类型
var ratableChatMessageTypes = map[aipb.ChatMessageType]bool{
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY:        true,
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION:       true,
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH:           true,
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION:           true,
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_CANCEL_ERROR:     true,
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_DRAFT:            true,
	aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER: true,
}

// RateAnswerLogic ...
type RateAnswerLogic struct {
}

// CheckMessage 检查消息
func (l *RateAnswerLogic) CheckMessage(ctx context.Context,
	chatMessageID, updateBy uint64) (*model.TChatMessage, error) {
	message, err := model.NewQuery[model.TChatMessage](ctx).FindByKey(chatMessageID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, xerrors.NotFoundError("message not exists")
		}
		return nil, fmt.Errorf("select t_chat_message: %w", err)
	}
	if !ratableChatMessageTypes[aipb.ChatMessageType(message.Type)] {
		return nil, xerrors.NotFoundError("message not exists")
	}
	if message.CreateBy != updateBy {
		return nil, xerrors.NotFoundError("message not exists")
	}
	return message, nil
}

// Rate 评分
func (l *RateAnswerLogic) Rate(ctx context.Context,
	message *model.TChatMessage, ratingScale aipb.RatingScale, updateBy uint64) error {
	message.RatingScale = int32(ratingScale)
	message.UpdateBy = updateBy
	message.UpdateDate = time.Now()

	err := model.NewQuery[model.TChatMessage](ctx).Select("rating_scale", "update_by", "update_date").Save(message)
	if err != nil {
		return fmt.Errorf("update t_chat_message: %w", err)
	}

	return nil
}
