package assistant

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"gorm.io/gorm"
)

// IsAssistantNameRepeated 检查助手名称重复
func IsAssistantNameRepeated(tx *gorm.DB, name string, exceptID uint64) (bool, error) {
	var count int64

	db := tx.Model(&model.TAssistant{})
	if exceptID > 0 {
		db.Where("id != ?", exceptID)
	}
	db.Where("(name = ? or name_en = ?)", name, name)

	if err := db.Limit(1).Count(&count).Error; err != nil {
		return false, fmt.Errorf("count name: %w", err)
	}
	return count > 0, nil
}

// ValidateRoutePath 校验路由
// 运营端：允许与任意管理员所管理的助手重复
// 用户后台：允许与编辑人所管理的助手重复
func ValidateRoutePath(tx *gorm.DB, routePath string, operator *basepb.Identity, adminPbs []*basepb.Identity, adminModels []*model.TAssistantAdmin) error {
	adminUserIDs, adminTeamIDs := dispatchIdentities(operator, adminPbs, adminModels)

	if routeRepeated, err := IsAssistantRouteRepeated(tx, routePath, adminUserIDs, adminTeamIDs); err != nil {
		return err
	} else if routeRepeated {
		return xerrors.NewCode(errorspb.AiError_AiAssistantRoutePathExisted)
	}

	return nil
}

// IsAssistantRouteRepeated 检查助手路由重复
func IsAssistantRouteRepeated(tx *gorm.DB, routePath string, userIDs, teamIDs []uint64) (bool, error) {
	var count int64

	query := tx.Model(&model.TAssistant{})
	query.Where("website_config->'$.route_path' = ?", routePath)
	model.ApplyAssistantAdminCond(query, userIDs, teamIDs, true)

	if err := query.Limit(1).Count(&count).Error; err != nil {
		return false, fmt.Errorf("count route_path: %w", err)
	}
	return count > 0, nil
}

// ValidateAppId 校验应用ID
// 运营端：允许与任意管理员所管理的助手重复
// 用户后台：允许与编辑人所管理的助手重复
func ValidateAppId(tx *gorm.DB, appId string, operator *basepb.Identity, adminPbs []*basepb.Identity, adminModels []*model.TAssistantAdmin) error {
	adminUserIDs, adminTeamIDs := dispatchIdentities(operator, adminPbs, adminModels)

	if routeRepeated, err := IsAppIdRepeated(tx, appId, adminUserIDs, adminTeamIDs); err != nil {
		return err
	} else if routeRepeated {
		return xerrors.NewCode(errorspb.AiError_AiAssistantAppIdExisted)
	}

	return nil
}

// IsAppIdRepeated 检查应用ID重复
func IsAppIdRepeated(tx *gorm.DB, appId string, userIDs, teamIDs []uint64) (bool, error) {
	var count int64

	query := tx.Model(&model.TAssistant{})
	query.Where("app_id = ?", appId)
	model.ApplyAssistantAdminCond(query, userIDs, teamIDs, true)

	if err := query.Limit(1).Count(&count).Error; err != nil {
		return false, fmt.Errorf("count app_id: %w", err)
	}
	return count > 0, nil
}

func dispatchIdentities(operator *basepb.Identity, adminPbs []*basepb.Identity, adminModels []*model.TAssistantAdmin) (adminUserIDs, adminTeamIDs []uint64) {
	dispatchIdentity := func(identity *basepb.Identity) {
		if identity.IdentityType == basepb.IdentityType_IDENTITY_TYPE_USER {
			adminUserIDs = append(adminUserIDs, identity.IdentityId)
		} else if identity.IdentityType == basepb.IdentityType_IDENTITY_TYPE_TEAM {
			adminTeamIDs = append(adminTeamIDs, identity.IdentityId)
		}
	}

	dispatchAdminModel := func(m *model.TAssistantAdmin) {
		if m.AdminType == basepb.IdentityType_IDENTITY_TYPE_USER {
			adminUserIDs = append(adminUserIDs, m.AdminID)
		} else if m.AdminType == basepb.IdentityType_IDENTITY_TYPE_TEAM {
			adminTeamIDs = append(adminTeamIDs, m.AdminID)
		}
	}

	if operator.IdentityType == basepb.IdentityType_IDENTITY_TYPE_MGMT {
		if len(adminModels) > 0 {
			for _, admin := range adminModels {
				dispatchAdminModel(admin)
			}
		} else {
			for _, admin := range adminPbs {
				dispatchIdentity(admin)
			}
		}
	} else {
		dispatchIdentity(operator)
	}

	return
}

// NewAppId 新建应用ID
func NewAppId(tx *gorm.DB) (string, error) {
	var (
		maxAppId uint64
		minAppId = config.GetUint64Or("assistant.minAppId", 17370120)
	)
	if err := tx.Model(&model.TAssistant{}).
		Select("MAX(app_id) AS max_app_id").Pluck("max_app_id", &maxAppId).Error; err != nil {
		return "", err
	}
	if maxAppId < minAppId {
		maxAppId = minAppId
	}
	appId := strconv.FormatUint(maxAppId+1, 10)
	return appId, nil
}

func formatEnName(name string) string {
	name = strings.TrimSpace(name)

	return replaceSpace(name, " ")
}

var replaceSpaceRegex = regexp.MustCompile(`\s+`)

// 替换空格
func replaceSpace(s, repl string) string {
	return replaceSpaceRegex.ReplaceAllString(s, repl)
}

func isStaffUsernameRepeated(staffs []*aipb.AssistantKefuStaff) error {
	usernames := make(map[string]bool, len(staffs))
	for _, staff := range staffs {
		if usernames[staff.Username] {
			return xerrors.NewCode(errorspb.AiError_AiAssistantKefuStaffUsernameRepeated)
		}
		usernames[staff.Username] = true
	}
	return nil
}
