package assistant

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/db/xormhelper"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
)

// GetLogsLogic ...
type GetLogsLogic struct {
}

// Count 总数
func (l *GetLogsLogic) Count(ctx context.Context, req *aipb.ReqGetAssistantLogs) (uint32, error) {
	query := model.NewQuery[model.TAssistantLog](ctx)

	l.applyFilter(query.DB(), req.Filter)

	count, err := query.Count()
	if err != nil {
		return 0, fmt.Errorf("count assistant logs: %w", err)
	}

	return count, nil
}

// Get 查询
func (l *GetLogsLogic) Get(ctx context.Context, req *aipb.ReqGetAssistantLogs) ([]*model.TAssistantLog, error) {
	query := model.NewQuery[model.TAssistantLog](ctx)

	l.applyFilter(query.DB(), req.Filter)

	logs, err := query.OrderBy("create_date", true).Paging(xormhelper.ToPaginator(req.Page))
	if err != nil {
		return nil, fmt.Errorf("paging assistant logs: %w", err)
	}

	return logs, nil
}

func (l *GetLogsLogic) applyFilter(db *gorm.DB, filter *aipb.ReqGetAssistantLogs_Filter) {
	if filter == nil {
		return
	}

	xormhelper.ApplyIn(db, "id", filter.Id)
	xormhelper.ApplyIn(db, "assistant_id", filter.AssistantId)
	xormhelper.ApplyIn(db, "action", filter.Action)
	xormhelper.ApplyTimeRange(db, "create_date", filter.CreateDate)

	if filter.ExceptMgmt {
		db.Where("create_by->'identity != ?'", basepb.IdentityType_IDENTITY_TYPE_MGMT)
	}
}
