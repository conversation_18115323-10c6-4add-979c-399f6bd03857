package assistant

import (
	"context"
	"fmt"
	"slices"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/db/xormhelper"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"gorm.io/gorm"
)

var orderByAllowedColumns = map[string]bool{
	model.TAssistantColumns.CreateDate: true,
	model.TAssistantColumns.UpdateDate: true,
}

// 星云列表可见字段
var visibleFieldsInNebulaList = []string{
	model.TAssistantColumns.Name,
	model.TAssistantColumns.NameEn,
	model.TAssistantColumns.Nickname,
	model.TAssistantColumns.NicknameEn,
	model.TAssistantColumns.AvatarUrl,
	model.TAssistantColumns.Channel,
	"collection_lang",
}

// 用户后台列表可见字段
var visibleFieldsInConsoleList = []string{
	model.TAssistantColumns.Name,
	model.TAssistantColumns.NameEn,
	model.TAssistantColumns.Nickname,
	model.TAssistantColumns.NicknameEn,
	model.TAssistantColumns.AvatarUrl,
	model.TAssistantColumns.Channel,
	model.TAssistantColumns.Enabled,
	model.TAssistantColumns.FeedbackEnabled,
	model.TAssistantColumns.FieldManageConfig,
}

// 知识库管理可见字段
var visibleFieldsInCollectionManage = []string{
	model.TAssistantColumns.Name,
	model.TAssistantColumns.NameEn,
	model.TAssistantColumns.Nickname,
	model.TAssistantColumns.NicknameEn,
	model.TAssistantColumns.AvatarUrl,
	model.TAssistantColumns.Channel,
	model.TAssistantColumns.Enabled,
	model.TAssistantCollectionColumns.ChunkConfig,
}

// 用户后台详情（必须字段）
var visibleFieldsInConsoleDetail = []string{
	model.TAssistantColumns.Channel,
	model.TAssistantColumns.FieldManageConfig,
}

// 门户端
var visibleFieldsInWeb = []string{
	model.TAssistantColumns.Name,
	model.TAssistantColumns.NameEn,
	model.TAssistantColumns.Nickname,
	model.TAssistantColumns.NicknameEn,
	model.TAssistantColumns.AvatarUrl,
	model.TAssistantColumns.AssistantLang,
	model.TAssistantColumns.SystemLang,
	// model.TAssistantColumns.SystemLanguages,
	model.TAssistantColumns.SearchDebug,
	model.TAssistantColumns.VisibleChainConfig,
	model.TAssistantColumns.Channel,
	model.TAssistantColumns.WebsiteConfig,
	model.TAssistantColumns.MiniprogramConfig,
	model.TAssistantColumns.WelcomeMessageConfig,
	model.TAssistantColumns.PresetQuestionConfig,
	model.TAssistantColumns.RatingScaleReplyConfig,
	model.TAssistantColumns.InteractiveCodeConfig,
	model.TAssistantColumns.FeedbackEnabled,
	model.TAssistantColumns.GraphParseConfig,
	model.TAssistantColumns.SwitchAssistantId,
	model.TAssistantColumns.KefuConfig,
	model.TAssistantColumns.BriefIntro,
	model.TAssistantColumns.DetailIntro,
	model.TAssistantColumns.ShowThink,
	model.TAssistantColumns.QuestionTypeConfig,
	"use_region_code",
	"admins",
}

// GetAssistantsLogic ...
type GetAssistantsLogic struct{}

// Count 总数
func (l *GetAssistantsLogic) Count(ctx context.Context, req *aipb.ReqGetAssistants) (uint32, error) {
	query := model.NewQuery[model.TAssistant](ctx)

	l.applyFilter(query.DB(), req.Filter)

	count, err := query.Count()
	if err != nil {
		return 0, fmt.Errorf("count assistants: %w", err)
	}

	return count, nil
}

// Get 查询
func (l *GetAssistantsLogic) Get(ctx context.Context, req *aipb.ReqGetAssistants) ([]*model.TAssistant, error) {
	query := model.NewQuery[model.TAssistant](ctx)

	l.applyFilter(query.DB(), req.Filter)
	l.applyRelation(query.DB(), req.Relation)
	l.applySceneSort(query.DB(), req.VisibleScene)
	xormhelper.ApplyOrderBy(query, req.OrderBy, orderByAllowedColumns, "db.orderBy.assistant")

	assistants, err := query.Paging(xormhelper.ToPaginator(req.Page))
	if err != nil {
		return nil, fmt.Errorf("paging assistants: %w", err)
	}

	return assistants, nil
}

func (l *GetAssistantsLogic) applySceneSort(query *gorm.DB, scene aipb.ReqGetAssistants_VisibleScene) {
	// 知识星云，AI助手选择collection最后更新过的助手，如果有多个助手的collection最后更新时间一致，则选C端最后对话过的助手。
	if scene == aipb.ReqGetAssistants_VISIBLE_SCENE_IN_NEBULA {
		query.InnerJoins("JOIN t_assistant_collection ON t_assistant.id = t_assistant_collection.assistant_id")
		query.InnerJoins("JOIN t_collection ON t_assistant_collection.collection_id = t_collection.id")
		query.Joins("LEFT JOIN (SELECT assistant_id, max(create_date) as create_date FROM t_chat_message " +
			"GROUP BY assistant_id) as msg " +
			"ON msg.assistant_id = t_assistant.id")
		// query.Select("t_assistant.*")
		query.Order("t_collection.emb_update_date DESC")
		query.Order("msg.create_date DESC")
	}
}

// ApplyVisibility 应用字段可见性
func (l *GetAssistantsLogic) ApplyVisibility(fullAssistants []*aipb.FullAssistant, scene aipb.ReqGetAssistants_VisibleScene) {
	if scene == aipb.ReqGetAssistants_VISIBLE_SCENE_UNSPECIFIED {
		return
	}

	for _, fullAssistant := range fullAssistants {
		rules := l.newVisibleRules(scene, fullAssistant.Assistant.Config)
		config := l.applyVisibility(fullAssistant.Assistant.Config, rules)
		fullAssistant.Assistant.Config = config
	}
}

func (l *GetAssistantsLogic) newVisibleRules(scene aipb.ReqGetAssistants_VisibleScene, config *aipb.AssistantConfig) map[string]bool {
	var fields []string
	switch scene {
	case aipb.ReqGetAssistants_VISIBLE_SCENE_IN_CONSOLE_LIST:
		fields = visibleFieldsInConsoleList
	case aipb.ReqGetAssistants_VISIBLE_SCENE_IN_NEBULA:
		fields = visibleFieldsInNebulaList
	case aipb.ReqGetAssistants_VISIBLE_SCENE_IN_CONSOLE_DETAIL:
		fields = l.getVisibleFieldsInConsoleDetail(config)
	case aipb.ReqGetAssistants_VISIBLE_SCENE_IN_WEB:
		fields = visibleFieldsInWeb
	case aipb.ReqGetAssistants_VISIBLE_SCENE_IN_COLLECTION_MANAGE:
		fields = visibleFieldsInCollectionManage
	}

	rules := make(map[string]bool, len(fields))
	for _, field := range fields {
		rules[field] = true
	}

	return rules
}

func (l *GetAssistantsLogic) getVisibleFieldsInConsoleDetail(config *aipb.AssistantConfig) []string {
	// 默认全部可读
	readable := model.AssistantReadableInConsole
	if len(config.FieldManageConfig.Readable) > 0 {
		readable = config.FieldManageConfig.Readable
	}

	// search_engine包含search_engine、close_search、miss_reply三个字段
	if slices.Contains(readable, model.TAssistantColumns.SearchEngine) {
		readable = append(readable, model.TAssistantColumns.CloseSearch, model.TAssistantColumns.MissReply)
	}

	// 兼容system_languages字段
	if slices.Contains(readable, model.TAssistantColumns.SystemLanguages) {
		readable = append(readable, model.TAssistantColumns.AssistantLang, model.TAssistantColumns.SystemLang)
	}

	fields := make([]string, 0, len(readable)+len(visibleFieldsInConsoleDetail))
	fields = append(fields, readable...)
	fields = append(fields, visibleFieldsInConsoleDetail...)

	return fields
}

func (l *GetAssistantsLogic) applyVisibility(config *aipb.AssistantConfig, rules map[string]bool) *aipb.AssistantConfig {
	// 无可见字段直接返回0值
	if len(rules) == 0 {
		return &aipb.AssistantConfig{}
	}

	filteredConfig := applyProtoMessageVisibility(config, rules)

	switch config.Channel {
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
		aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN:
		filteredConfig.WeixinChannelConfig = applyProtoMessageVisibility(config.WeixinChannelConfig, rules)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB:
		filteredConfig.TanliveWebChannelConfig = applyProtoMessageVisibility(config.TanliveWebChannelConfig, rules)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP:
		filteredConfig.TanliveAppChannelConfig = applyProtoMessageVisibility(config.TanliveAppChannelConfig, rules)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP:
		filteredConfig.WhatsappChannelConfig = applyProtoMessageVisibility(config.WhatsappChannelConfig, rules)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM:
		filteredConfig.MiniprogramChannelConfig = applyProtoMessageVisibility(config.MiniprogramChannelConfig, rules)
	}

	return filteredConfig
}

func applyProtoMessageVisibility[M proto.Message](src M, rules map[string]bool) M {
	dstReflect := src.ProtoReflect().New()
	srcReflect := src.ProtoReflect()
	srcReflect.Range(func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
		if name := fd.Name(); rules[string(name)] {
			dstReflect.Set(fd, v)
		}
		return true
	})
	return dstReflect.Interface().(M)
}

func (l *GetAssistantsLogic) applyFilter(db *gorm.DB, filter *aipb.ReqGetAssistants_Filter) {
	if filter == nil {
		return
	}

	xormhelper.ApplyIn(db, "id", filter.AssistantId)
	xormhelper.ApplyIn(db, "channel", filter.Channel)
	xormhelper.ApplyIn(db, "model", filter.Model)
	xormhelper.ApplyIn(db, "search_engine", filter.SearchEngine)
	xormhelper.ApplyBoolEnum(db, "is_draft", filter.IsDraft)
	xormhelper.ApplyBoolEnum(db, "enabled", filter.Enabled)
	xormhelper.ApplyTimeRange(db, "create_date", filter.CreateDate)

	if filter.AppId != "" {
		db.Where("app_id = ?", filter.AppId)
	}

	if filter.BatchNo != "" {
		db.Where("batch_no = ?", filter.BatchNo)
	}

	if len(filter.RoutePath) > 0 {
		// utf8mb4_general_ci大小写不敏感，对应的索引也是这个字符集编码
		db.Where("(CAST(website_config->>'$.route_path' AS CHAR(255)) COLLATE utf8mb4_general_ci) = ?", filter.RoutePath)
	}

	if filter.AssistantName != "" {
		name := "%" + xorm.EscapeLikeWildcards(filter.AssistantName) + "%"
		db.Where("name like ? OR name_en like ?", name, name)
	}

	// 管理员
	model.ApplyAssistantAdminCond(db, filter.UserId, filter.TeamId, false)

	if len(filter.CollectionLang) > 0 {
		sql := "EXISTS (SELECT 1 FROM t_assistant_collection AS pivot LEFT JOIN t_collection AS collection ON " +
			"pivot.collection_id = collection.id WHERE pivot.assistant_id = t_assistant.id AND collection.lang IN (?))"
		db.Where(sql, filter.CollectionLang)
	}

	if filter.TermsConfirmed > 0 {
		sql := "(SELECT 1 FROM v_assistant_terms AS terms " +
			"WHERE terms.assistant_id = t_assistant.id AND terms.is_agreed = 1)"
		if filter.TermsConfirmed == basepb.BoolEnum_BOOL_ENUM_TRUE {
			db.Where("EXISTS " + sql)
		} else {
			db.Where("NOT EXISTS " + sql)
		}
	}

	model.ApplyAssistantDoc(db, filter.DocId)
}

func (l *GetAssistantsLogic) applyRelation(db *gorm.DB, relation *aipb.ReqGetAssistants_Relation) {
	if relation == nil {
		return
	}

	if relation.Admin {
		db.Preload("Admins")
	}

	if relation.Collection {
		db.Preload("AssistantCollections.Collection")
	}

	if relation.LiveAgent {
		db.Preload("LiveAgents")
	}

	if relation.TermsConfirmation {
		db.Preload("TermsConfirmation")
	}

	if relation.Allowlist {
		db.Preload("Allowlist")
	}
}

// GenerateMiniprogramQrcode 查询的时候异步生成一遍小程序二维码，防止发布的时候创建失败
func (l *GetAssistantsLogic) GenerateMiniprogramQrcode(ctx context.Context, assistants []*model.TAssistant) {
	for _, assistant := range assistants {
		if assistant == nil || assistant.Channel != aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM || assistant.IsDraft {
			continue
		}
		assistantID := assistant.ID
		if assistant.MiniprogramConfig == nil || assistant.MiniprogramConfig.Url == "" {
			xsync.SafeGo(context.Background(), func(ctx context.Context) error {
				if err := new(PublishAssistantLogic).GenMiniprogramQrCode(ctx, assistantID); err != nil {
					log.WithContext(ctx).Errorw("gen assistant miniprogram qrcode", "err", err)
				}
				return nil
			}, boot.TraceGo(ctx))
		}
		if assistant.MiniprogramConfig == nil || assistant.MiniprogramConfig.Schema == "" {
			xsync.SafeGo(context.Background(), func(ctx context.Context) error {
				if err := new(PublishAssistantLogic).GenMiniprogramSchema(ctx, assistantID); err != nil {
					log.WithContext(ctx).Errorw("gen assistant miniprogram schema", "err", err)
				}
				return nil
			}, boot.TraceGo(ctx))
		}
	}
}
