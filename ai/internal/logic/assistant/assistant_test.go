package assistant

import "testing"

func Test_formatEnName(t *testing.T) {
	type args struct {
		name string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"leading space", args{"    pony ma"}, "pony ma"},
		{"tail space", args{"pony ma  "}, "pony ma"},
		{"middle space", args{"pony    ma"}, "pony ma"},
		{"random space", args{"      pony    ma      "}, "pony ma"},
		{"random space utf-8", args{"LD2    改"}, "LD2 改"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := formatEnName(tt.args.name); got != tt.want {
				t.<PERSON>("formatEnName() = %v, want %v", got, tt.want)
			}
		})
	}
}
