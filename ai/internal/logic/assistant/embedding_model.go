package assistant

import (
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// GetEmbeddingModelNames 获取向量化模型名称映射
func GetEmbeddingModelNames() (map[string]string, error) {
	var opts []*aipb.EmbeddingModelOption
	if err := config.Unmarshal("assistant.embeddingModel", &opts); err != nil {
		return nil, fmt.Errorf("unmarshal assistant.embeddingModel: %v", err)
	}

	m := make(map[string]string, len(opts))
	for _, opt := range opts {
		m[opt.Model] = opt.Name
	}

	return m, nil
}
