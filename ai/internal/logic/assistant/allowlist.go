package assistant

import (
	"context"
	"errors"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/db/cryptohelper"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// CheckAllowlistLogic ...
type CheckAllowlistLogic struct {
}

// CheckPhone 检查手机白名单
func (l *CheckAllowlistLogic) CheckPhone(ctx context.Context, assistantID uint64, para *aipb.ReqCheckAssistantAllowlist_PhonePara) (bool, error) {
	assistant, err := l.queryAssistant(ctx, assistantID)
	if err != nil {
		return false, err
	}

	// 助手不存在表示拒绝
	if assistant == nil {
		return false, nil
	}

	// 助手白名单配置未启用表示允许
	if assistant.AllowlistConfig == nil || !assistant.AllowlistConfig.Enabled {
		return true, nil
	}

	// 白名单类型不为手机号表示拒绝
	if assistant.AllowlistConfig.Type != aipb.AssistantAllowlistType_ASSISTANT_ALLOWLIST_TYPE_PHONE {
		return false, nil
	}

	// 手机号加密
	phone, err := cryptohelper.AesEncrypt(para.Phone)
	if err != nil {
		return false, fmt.Errorf("encrypt phone: %w", err)
	}

	// 检查白名单
	cnt, err := model.NewQuery[model.TAssistantAllowlist](ctx).
		Where("assistant_id = ? AND value = ?", assistantID, phone).Count()
	if err != nil {
		return false, fmt.Errorf("query allowlist: %w", err)
	}

	return cnt > 0, nil
}

func (l *CheckAllowlistLogic) queryAssistant(ctx context.Context, assistantID uint64) (*model.TAssistant, error) {
	assistant, err := model.NewQuery[model.TAssistant](ctx).
		Select("id", "enabled", "is_draft", "allowlist_config").
		FindBy("id = ? AND is_draft = ? AND enabled = ?", assistantID, 0, 1)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("query assistant: %w", err)
	}
	return assistant, nil
}
