package assistant

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
)

// DeleteAssistantLogic 删除
type DeleteAssistantLogic struct{}

// BatchDelete 批量删除
func (l *DeleteAssistantLogic) BatchDelete(ctx context.Context, req *aipb.ReqDeleteAssistant) error {
	err := l.validate(req.UpdateBy)
	if err != nil {
		return err
	}

	var assistantIDs []uint64
	switch cond := req.Condition.(type) {
	case *aipb.ReqDeleteAssistant_ByAssistantId:
		assistantIDs = cond.ByAssistantId.AssistantId
	case *aipb.ReqDeleteAssistant_ByBatchNo:
		assistantIDs, err = l.batchNoToAssistantId(ctx, cond.ByBatchNo)
	default:
		err = xerrors.ValidationError("condition is required")
	}
	if err != nil {
		return err
	}

	if err = l.ensureAssistantDeletable(ctx, assistantIDs); err != nil {
		return err
	}

	return model.Transaction(ctx, func(tx *gorm.DB) error {
		return l.deleteTx(tx, assistantIDs, req.UpdateBy)
	})
}

func (l *DeleteAssistantLogic) batchNoToAssistantId(ctx context.Context, batchNo string) ([]uint64, error) {
	assistants, err := model.NewQuery[model.TAssistant](ctx).Select("id", "is_draft").GetBy("batch_no = ?", batchNo)
	if err != nil {
		return nil, fmt.Errorf("query asisstants by batch_no: %w", err)
	}
	if len(assistants) == 0 {
		return nil, errors.New("this batch_no has no valid assistants")
	}

	assistantIDs := make([]uint64, 0, len(assistants))
	for _, assistant := range assistants {
		if !assistant.IsDraft {
			return nil, xerrors.ValidationError("assistant is not draft")
		}
		assistantIDs = append(assistantIDs, assistant.ID)
	}

	return assistantIDs, nil
}

func (l *DeleteAssistantLogic) validate(updateBy *basepb.Identity) error {
	if updateBy.IdentityType != basepb.IdentityType_IDENTITY_TYPE_MGMT {
		return xerrors.ForbiddenError("cannot be deleted by the current operator")
	}

	return nil
}

func (l *DeleteAssistantLogic) ensureAssistantDeletable(ctx context.Context, assistantIDs []uint64) error {
	assistants, err := model.NewQuery[model.TAssistant](ctx).
		Select("id", "is_draft", "enabled").
		GetBy("id in (?)", assistantIDs)
	if err != nil {
		return fmt.Errorf("query assistants: %w", err)
	}

	for _, assistant := range assistants {
		if !assistant.IsDraft && assistant.Enabled {
			return xerrors.ValidationError("cannot delete enabled assistant")
		}
	}

	return nil
}

func (l *DeleteAssistantLogic) deleteTx(tx *gorm.DB, assistantIDs []uint64, deleteBy *basepb.Identity) error {
	if err := l.delete(tx, assistantIDs, deleteBy); err != nil {
		return err
	}
	if err := l.deleteLog(tx, assistantIDs, deleteBy); err != nil {
		return err
	}
	return nil
}

func (l *DeleteAssistantLogic) delete(tx *gorm.DB, assistantIDs []uint64, updateBy *basepb.Identity) error {
	var err error

	// 删除t_assistant_admin
	if err = tx.Delete(&model.TAssistantAdmin{}, "assistant_id in ?", assistantIDs).Error; err != nil {
		return fmt.Errorf("delete t_assistant_admin: %w", err)
	}

	// 删除t_assistant_doc
	if err = tx.Delete(&model.TAssistantDoc{}, "assistant_id in ?", assistantIDs).Error; err != nil {
		return fmt.Errorf("delete t_assistant_doc: %w", err)
	}

	// 删除t_share_receiver
	if err = tx.Delete(&model.TShareReceiver{}, "admin_assistant_id in ?", assistantIDs).Error; err != nil {
		return fmt.Errorf("delete t_share_receiver: %w", err)
	}

	// 删除t_share_sender
	if err = tx.Delete(&model.TShareSender{}, "receiver_id in ? and receiver_type = ?",
		assistantIDs, aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_ASSISTANT).Error; err != nil {
		return fmt.Errorf("delete t_share_sender: %w", err)
	}

	// 删除t_share_receiver_user
	if err = tx.Delete(&model.TShareReceiverUser{}, "admin_assistant_id in ?", assistantIDs).Error; err != nil {
		return fmt.Errorf("delete t_share_receiver_user: %w", err)
	}

	// 删除t_doc_table_oversize
	if err = tx.Delete(&model.TDocTableOversize{}, "assistant_id in ?", assistantIDs).Error; err != nil {
		return fmt.Errorf("delete t_doc_table_oversize: %w", err)
	}

	// 更新删除人
	updateByJson, _ := json.Marshal(updateBy)
	if err = tx.Model(&model.TAssistant{}).
		Where("id in ?", assistantIDs).
		Updates(map[string]any{
			model.TAssistantColumns.UpdateBy:   xstrings.FromBytes(updateByJson),
			model.TAssistantColumns.UpdateDate: time.Now(),
		}).Error; err != nil {
		return fmt.Errorf("update update_by: %w", err)
	}

	// 删除t_assistant
	if err = tx.Delete(&model.TAssistant{}, "id in ?", assistantIDs).Error; err != nil {
		return fmt.Errorf("delete t_assistant: %w", err)
	}

	return nil
}

func (l *DeleteAssistantLogic) deleteLog(tx *gorm.DB, assistantIDs []uint64, updateBy *basepb.Identity) error {
	logs := make([]*model.TAssistantLog, 0, len(assistantIDs))
	for _, assistantID := range assistantIDs {
		log := &model.TAssistantLog{
			AssistantID: assistantID,
			Action:      aipb.AssistantAction_ASSISTANT_ACTION_DELETE,
			CreateBy:    updateBy,
			CreateDate:  time.Now(),
		}
		logs = append(logs, log)
	}
	if err := tx.Create(&logs).Error; err != nil {
		return fmt.Errorf("create assistant delete logs: %w", err)
	}
	return nil
}
