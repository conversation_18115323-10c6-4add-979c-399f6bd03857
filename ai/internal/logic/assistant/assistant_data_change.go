package assistant

import (
	"context"
	"errors"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"

	"gorm.io/gorm"
)

// CalculateAssistantDataChangeRate24H 计算助手24小时内数据变化率
func CalculateAssistantDataChangeRate24H(ctx context.Context, assistantID uint64) (float64, error) {
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)
	return CalculateAssistantChangeRate(ctx, assistantID, startTime, endTime)
}

// CalculateCollectionDataChangeRate24H 计算集合24小时内collection的数据变化率
func CalculateCollectionDataChangeRate24H(ctx context.Context, collectionID uint64) (float64, error) {
	// 通过 colleciton 反查 assistant_id
	var assistantCollection model.TAssistantCollection
	if err := model.NewQuery[model.TAssistantCollection](ctx).DB().
		Where("collection_id = ?", collectionID).
		First(&assistantCollection).Error; err != nil {
		return 0, err
	}
	return CalculateAssistantDataChangeRate24H(ctx, assistantCollection.AssistantID)
}

// CalculateAssistantChangeRate 计算助手在指定时间段内的数据变化率
func CalculateAssistantChangeRate(ctx context.Context, assistantID uint64, startTime, endTime time.Time) (float64, error) {
	// 每次计算时，清理 change 表, 只保留48小时内的数据
	defer func() {
		go func() {
			ctx := context.Background()
			if err := model.NewQuery[model.TAssistantDataChange](ctx).DB().
				Where("assistant_id = ?", assistantID).
				Where("change_time < ?", endTime.Add(-48*time.Hour)).
				Delete(&model.TAssistantDataChange{}).Error; err != nil {
				log.WithContext(ctx).Error("clear assistant data change table failed", err, "assistant_id", assistantID)
			}
		}()
	}()
	// 获取起始时间点最近的快照
	var startSnapshot model.TAssistantDataSnapshot
	if err := model.NewQuery[model.TAssistantDataSnapshot](ctx).DB().
		Where("assistant_id = ? AND snapshot_time <= ?", assistantID, startTime).
		Order("snapshot_time DESC").
		First(&startSnapshot).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果没有找到快照，往后找最近的一个快照
			if err := model.NewQuery[model.TAssistantDataSnapshot](ctx).DB().
				Where("assistant_id = ? AND snapshot_time >= ?", assistantID, startTime).
				Order("snapshot_time ASC").
				First(&startSnapshot).Error; err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					return 0, err
				}
			}
		} else {
			return 0, err
		}
	}

	// 计算时间段内的文档变化数量（去重）
	var docChangeCount int64
	err := model.NewQuery[model.TAssistantDataChange](ctx).DB().
		Where("assistant_id = ? AND change_type = ? AND change_time BETWEEN ? AND ?",
			assistantID, model.ChangeTypeDocOperation, startTime, endTime).
		Distinct("doc_id").
		Count(&docChangeCount).Error
	if err != nil {
		return 0, err
	}

	// 计算时间段内的提问数量
	var questionChangeCount int64
	err = model.NewQuery[model.TAssistantDataChange](ctx).DB().
		Where("assistant_id = ? AND change_type = ? AND change_time BETWEEN ? AND ?",
			assistantID, model.ChangeTypeNewQuestion, startTime, endTime).
		Count(&questionChangeCount).Error
	if err != nil {
		return 0, err
	}

	// 总变化数量
	totalChangeCount := docChangeCount + questionChangeCount

	// 计算变化率
	if startSnapshot.TotalCount == 0 {
		startSnapshot.TotalCount = 1
	}

	changeRate := float64(totalChangeCount) / float64(startSnapshot.TotalCount)
	return changeRate, nil
}
