package assistant

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm/clause"
)

// CreateAllAssistantDataSnapshot 创建所有助手数据快照
func CreateAllAssistantDataSnapshot(ctx context.Context) error {
	// 获取所有助手ID
	var assistants []*model.TAssistant
	if err := model.NewQuery[model.TAssistant](ctx).DB().Select("id").Find(&assistants).Error; err != nil {
		return err
	}

	for _, assistant := range assistants {
		if err := CreateAssistantDataSnapshot(ctx, assistant.ID); err != nil {
			return err
		}
	}

	return nil
}

// CreateAssistantDataSnapshot 创建助手数据快照
func CreateAssistantDataSnapshot(ctx context.Context, assistantID uint64) error {
	// 统计doc数量
	docCount, err := model.NewQuery[model.TAssistantDoc](ctx).
		Where("assistant_id = ? AND state in ?", assistantID, []any{aipb.DocState_DOC_STATE_ENABLED, aipb.DocState_DOC_STATE_DISABLED}).
		Count()
	if err != nil {
		return err
	}

	// 统计提问数量
	questionCount, err := model.NewQuery[model.TChatMessage](ctx).
		Where("assistant_id = ? AND type = ?", assistantID, 1). // 假设type=1表示用户提问
		Count()
	if err != nil {
		return err
	}

	// 创建快照
	snapshot := &model.TAssistantDataSnapshot{
		AssistantID:   assistantID,
		DocCount:      uint64(docCount),
		QuestionCount: uint64(questionCount),
		TotalCount:    uint64(docCount + questionCount),
		SnapshotTime:  time.Now(),
	}

	if err := model.NewQuery[model.TAssistantDataSnapshot](ctx).DB().
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "assistant_id"}},
			UpdateAll: true,
		}).
		Create(snapshot).Error; err != nil {
		return err
	}

	return nil
}
