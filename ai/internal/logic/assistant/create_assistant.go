package assistant

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/validation"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

var createDraftMask = []string{
	model.TAssistantColumns.Name,
	model.TAssistantColumns.NameEn,
	model.TAssistantColumns.Channel,
	"admins",
}

var createDraftMaskMap = map[string]bool{}

func init() {
	for _, mask := range createDraftMask {
		createDraftMaskMap[mask] = true
	}
}

// CreateAssistantLogic 创建助手
type CreateAssistantLogic struct {
}

// BatchCreate 批量创建助手
func (l *CreateAssistantLogic) BatchCreate(ctx context.Context, configs []*aipb.AssistantConfig, createBy *basepb.Identity, isDraft bool) (string, []uint64, error) {
	batchNo := uuid.New().String()
	finished := make(chan struct{})

	var ids []uint64
	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		var err error
		if ids, err = l.batchCreate(tx, finished, batchNo, configs, createBy, isDraft); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return "", nil, err
	}

	close(finished)

	return batchNo, ids, nil
}

func (l *CreateAssistantLogic) batchCreate(tx *gorm.DB, finished chan struct{}, batchNo string, configs []*aipb.AssistantConfig, createBy *basepb.Identity, isDraft bool) ([]uint64, error) {
	var (
		err error
		ids = make([]uint64, 0, len(configs))
	)

	for _, config := range configs {
		if err = l.validate(tx, config, createBy, isDraft); err != nil {
			return nil, err
		}

		assistant, err := l.create(tx, batchNo, config, createBy)
		if err != nil {
			return nil, err
		}

		if !isDraft {
			if err = new(PublishAssistantLogic).publishTx(tx, finished, assistant.ID, createBy); err != nil {
				return nil, err
			}
			assistant.IsDraft = false
		}

		if err = l.createLog(tx, assistant); err != nil {
			return nil, err
		}

		ids = append(ids, assistant.ID)
	}

	return ids, nil
}

// 参数校验
func (l *CreateAssistantLogic) validate(tx *gorm.DB, config *aipb.AssistantConfig, createBy *basepb.Identity, isDraft bool) error {
	if isDraft {
		return l.validateDraft(config)
	}

	// 参数校验
	if err := validation.Validator.Struct(config); err != nil {
		return xerrors.ValidationError(err)
	}

	// 校验staff用户名重复
	if model.IsWeixinChannelConfig(config) && config.WeixinChannelConfig.KefuConfig.Enabled {
		if err := isStaffUsernameRepeated(config.WeixinChannelConfig.KefuConfig.Staffs); err != nil {
			return err
		}
	}

	// 名称校验
	if nameRepeated, err := IsAssistantNameRepeated(tx, config.Name, 0); err != nil {
		return err
	} else if nameRepeated {
		return xerrors.NewCode(errorspb.AiError_AiAssistantNameExisted)
	}
	if nameEnRepeated, err := IsAssistantNameRepeated(tx, config.NameEn, 0); err != nil {
		return err
	} else if nameEnRepeated {
		return xerrors.NewCode(errorspb.AiError_AiAssistantNameEnExisted)
	}

	// 校验路由
	if config.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB {
		if err := ValidateRoutePath(tx, config.TanliveWebChannelConfig.WebsiteConfig.RoutePath, createBy, config.Admins, nil); err != nil {
			return err
		}
	}

	// 应用ID
	if config.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP && config.TanliveAppChannelConfig.AppId != "" {
		if err := ValidateAppId(tx, config.TanliveAppChannelConfig.AppId, createBy, config.Admins, nil); err != nil {
			return err
		}
	}

	return nil
}

func (l *CreateAssistantLogic) validateDraft(config *aipb.AssistantConfig) error {
	if err := validation.Validator.StructFiltered(config, validation.NewAssistantFilterFunc(createDraftMask, 0)); err != nil {
		return xerrors.ValidationError(err)
	}
	return nil
}

// Create 创建
func (l *CreateAssistantLogic) create(tx *gorm.DB, batchNo string, config *aipb.AssistantConfig, createBy *basepb.Identity) (*model.TAssistant, error) {
	now := time.Now()
	assistant := &model.TAssistant{
		CreateBy:      createBy,
		CreateDate:    now,
		UpdateBy:      createBy,
		UpdateDate:    now,
		SearchRewrite: 1,
		BatchNo:       batchNo,
		IsDraft:       true,
	}

	l.fillConfigFields(assistant, config)

	admins := newAdmins(config)
	liveAgents := newLiveAgents(config)
	collection, assistantCollection := newCollection(config)
	allowlist := newAllowlist(config)

	var err error

	// 应用ID
	if assistant.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP && assistant.AppId == "" {
		if assistant.AppId, err = NewAppId(tx); err != nil {
			return nil, err
		}
	}

	// 创建助手
	if err = tx.Create(assistant).Error; err != nil {
		return nil, fmt.Errorf("create assistant: %v", err)
	}

	// 创建admins
	if len(admins) > 0 {
		for _, admin := range admins {
			admin.AssistantID = assistant.ID
		}
		if err = tx.Create(&admins).Error; err != nil {
			return nil, fmt.Errorf("create admins: %v", err)
		}
		assistant.Admins = admins
	}

	// 创建collection
	if collection != nil {
		if err = tx.Create(collection).Error; err != nil {
			return nil, fmt.Errorf("create collection: %v", err)
		}
	}

	// 创建assistant collection
	if assistantCollection != nil {
		assistantCollection.AssistantID = assistant.ID
		assistantCollection.CollectionID = collection.ID
		if err = tx.Create(assistantCollection).Error; err != nil {
			return nil, fmt.Errorf("create assistant collection: %v", err)
		}
		assistantCollection.Collection = collection
		assistant.AssistantCollections = []*model.TAssistantCollection{assistantCollection}
	}

	// 创建人工坐席
	if len(liveAgents) > 0 {
		for _, agent := range liveAgents {
			agent.AssistantID = assistant.ID
		}
		if err = tx.Create(&liveAgents).Error; err != nil {
			return nil, fmt.Errorf("create live agents: %v", err)
		}
		assistant.LiveAgents = liveAgents
	}

	// 白名单
	if len(allowlist) > 0 {
		for _, item := range allowlist {
			item.AssistantID = assistant.ID
		}
		if err = tx.Create(&allowlist).Error; err != nil {
			return nil, fmt.Errorf("create allowlist: %v", err)
		}
		assistant.Allowlist = allowlist
	}

	return assistant, nil
}

func (l *CreateAssistantLogic) createLog(tx *gorm.DB, assistant *model.TAssistant) error {
	newConfig := assistant.ToConfig()
	action := aipb.AssistantAction_ASSISTANT_ACTION_CREATE
	if !assistant.IsDraft {
		action = aipb.AssistantAction_ASSISTANT_ACTION_PUBLISH
	}
	log := &model.TAssistantLog{
		AssistantID: assistant.ID,
		Action:      action,
		Changes: &aipb.AssistantChanges{
			New: newConfig,
		},
		CreateBy:   assistant.CreateBy,
		CreateDate: assistant.CreateDate,
	}
	if err := tx.Create(log).Error; err != nil {
		return fmt.Errorf("create assistant log: %v", err)
	}
	return nil
}

func (l *CreateAssistantLogic) fillConfigFields(assistant *model.TAssistant, config *aipb.AssistantConfig) {
	assistant.SearchDebug = config.SearchDebug
	assistant.VisibleChainConfig = config.VisibleChainConfig
	assistant.FieldManageConfig = config.FieldManageConfig
	assistant.Name = config.Name
	assistant.NameEn = formatEnName(config.NameEn)
	assistant.Channel = config.Channel
	assistant.Enabled = config.Enabled
	assistant.PromptPrefix = config.PromptPrefix
	assistant.Model = config.Model
	assistant.HistoryRounds = config.HistoryRounds
	assistant.CloseSearch = config.CloseSearch
	assistant.SearchEngine = config.SearchEngine
	assistant.SearchTopN = config.SearchTopN
	assistant.ChatOrSqlConfig = config.ChatOrSqlConfig
	assistant.AskSuggestionConfig = config.AskSuggestionConfig
	assistant.TextWeight = config.TextWeight
	assistant.MissReply = config.MissReply
	assistant.BriefIntro = config.BriefIntro
	assistant.DetailIntro = config.DetailIntro
	assistant.ShowThink = config.ShowThink
	assistant.TextRecallTopN = config.TextRecallTopN
	assistant.TextRecallQuery = config.TextRecallQuery
	assistant.TextRecallPattern = config.TextRecallPattern
	assistant.TextRecallSlop = config.TextRecallSlop
	assistant.Temperature = config.Temperature
	assistant.QuestionTypeConfig = config.QuestionTypeConfig
	assistant.ShowInList = config.ShowInList
	assistant.AllowlistConfig = config.AllowlistConfig
	assistant.CleanChunks = config.CleanChunks

	switch config.Channel {
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
		aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN:
		l.fillWeixinChannelConfigFields(assistant, config.WeixinChannelConfig)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB:
		l.fillTanliveWebChannelConfigFields(assistant, config.TanliveWebChannelConfig)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP:
		l.fillTanliveAppChannelConfigFields(assistant, config.TanliveAppChannelConfig)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP:
		l.fillWhatsappChannelConfigFields(assistant, config.WhatsappChannelConfig)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM:
		l.fillMiniprogramChannelConfigFields(assistant, config.MiniprogramChannelConfig)
	}

	if config.Enabled {
		assistant.EnabledAt = sql.NullTime{
			Time:  assistant.CreateDate,
			Valid: true,
		}
	}

	// 问题建议模式默认使用1
	if assistant.AskSuggestionConfig != nil && assistant.AskSuggestionConfig.Mode == 0 {
		assistant.AskSuggestionConfig.Mode = aipb.AskSuggestionMode_ASK_SUGGESTION_MODE_1
	}
}

func (l *CreateAssistantLogic) fillWeixinChannelConfigFields(
	assistant *model.TAssistant, config *aipb.AssistantWeixinChannelConfig) {
	assistant.Nickname = config.Nickname
	assistant.AvatarUrl = config.AvatarUrl
	assistant.SystemLanguages = []string{config.SystemLang}
	assistant.WeixinDevelopConfig = config.WeixinDevelopConfig
	assistant.WelcomeMessageConfig = config.WelcomeMessageConfig
	assistant.PresetQuestionConfig = config.PresetQuestionConfig
	assistant.KefuConfig = config.KefuConfig
	assistant.RatingScaleReplyConfig = config.RatingScaleReplyConfig
	assistant.InteractiveCodeConfig = config.InteractiveCodeConfig
	assistant.ChatIdleDuration = config.ChatIdleDuration
}

func (l *CreateAssistantLogic) fillTanliveWebChannelConfigFields(
	assistant *model.TAssistant, config *aipb.AssistantTanliveWebChannelConfig) {
	assistant.Nickname = config.Nickname
	assistant.NicknameEn = formatEnName(config.NicknameEn)
	assistant.AvatarUrl = config.AvatarUrl
	assistant.SystemLanguages = []string{config.AssistantLang}
	assistant.WebsiteConfig = config.WebsiteConfig
	assistant.WelcomeMessageConfig = config.WelcomeMessageConfig
	assistant.PresetQuestionConfig = config.PresetQuestionConfig
	assistant.RatingScaleReplyConfig = config.RatingScaleReplyConfig
	assistant.InteractiveCodeConfig = config.InteractiveCodeConfig
	assistant.FeedbackEnabled = config.FeedbackEnabled
	assistant.GraphParseConfig = config.GraphParseConfig
	assistant.SwitchAssistantId = config.SwitchAssistantId
	assistant.KefuConfig = config.KefuConfig
}

func (l *CreateAssistantLogic) fillTanliveAppChannelConfigFields(
	assistant *model.TAssistant, config *aipb.AssistantTanliveAppChannelConfig) {
	assistant.Nickname = config.Nickname
	assistant.NicknameEn = formatEnName(config.NicknameEn)
	assistant.AvatarUrl = config.AvatarUrl
	assistant.SystemLanguages = []string{config.AssistantLang}
	assistant.WelcomeMessageConfig = config.WelcomeMessageConfig
	assistant.PresetQuestionConfig = config.PresetQuestionConfig
	assistant.RatingScaleReplyConfig = config.RatingScaleReplyConfig
	assistant.InteractiveCodeConfig = config.InteractiveCodeConfig
	assistant.FeedbackEnabled = config.FeedbackEnabled
	assistant.GraphParseConfig = config.GraphParseConfig
	assistant.KefuConfig = config.KefuConfig
	assistant.AppId = config.AppId
}

func (l *CreateAssistantLogic) fillWhatsappChannelConfigFields(
	assistant *model.TAssistant, config *aipb.AssistantWhatsappChannelConfig) {
	assistant.Nickname = config.WhatsappDevelopConfig.BusinessNumber
	assistant.AvatarUrl = config.AvatarUrl
	assistant.SystemLanguages = []string{config.SystemLang}
	assistant.WhatsappDevelopConfig = config.WhatsappDevelopConfig
	assistant.WelcomeMessageConfig = config.WelcomeMessageConfig
	assistant.PresetQuestionConfig = config.PresetQuestionConfig
	assistant.RatingScaleReplyConfig = config.RatingScaleReplyConfig
	assistant.ChatIdleDuration = config.ChatIdleDuration
}

func (l *CreateAssistantLogic) fillMiniprogramChannelConfigFields(
	assistant *model.TAssistant, config *aipb.AssistantMiniprogramChannelConfig) {
	assistant.Nickname = config.Nickname
	assistant.NicknameEn = formatEnName(config.NicknameEn)
	assistant.AvatarUrl = config.AvatarUrl
	assistant.SystemLanguages = []string{config.AssistantLang}
	assistant.MiniprogramConfig = config.MiniprogramConfig
	assistant.WelcomeMessageConfig = config.WelcomeMessageConfig
	assistant.PresetQuestionConfig = config.PresetQuestionConfig
	assistant.RatingScaleReplyConfig = config.RatingScaleReplyConfig
	assistant.InteractiveCodeConfig = config.InteractiveCodeConfig
	assistant.FeedbackEnabled = config.FeedbackEnabled
	assistant.GraphParseConfig = config.GraphParseConfig
	assistant.KefuConfig = config.KefuConfig

	if assistant.MiniprogramConfig != nil {
		assistant.MiniprogramConfig.Url = ""
		assistant.MiniprogramConfig.Schema = ""
	}
}

func newAdmins(config *aipb.AssistantConfig) []*model.TAssistantAdmin {
	admins := make([]*model.TAssistantAdmin, 0, len(config.Admins))
	for _, admin := range config.Admins {
		admins = append(admins, &model.TAssistantAdmin{
			AdminID:   admin.IdentityId,
			AdminType: admin.IdentityType,
		})
	}
	return admins
}

func newLiveAgents(config *aipb.AssistantConfig) []*model.TChatLiveAgent {
	if config.Channel != aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN &&
		config.Channel != aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN {
		return nil
	}

	if config.WeixinChannelConfig == nil || config.WeixinChannelConfig.KefuConfig == nil {
		return nil
	}

	staffs := config.WeixinChannelConfig.KefuConfig.Staffs
	config.WeixinChannelConfig.KefuConfig.Staffs = nil

	if !config.WeixinChannelConfig.KefuConfig.Enabled {
		return nil
	}

	var liveAgents []*model.TChatLiveAgent
	for index, staff := range staffs {
		if staff.Username == "" {
			continue
		}
		liveAgents = append(liveAgents, &model.TChatLiveAgent{
			Username:   staff.Username,
			Nickname:   staff.Nickname,
			CreateDate: time.Now(),
			Status:     aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_CURRENTLY_SERVING,
			Order:      index,
		})
	}

	return liveAgents
}

func newCollection(config *aipb.AssistantConfig) (*model.TCollection, *model.TAssistantCollection) {
	ragName := newRagName(config.NameEn, config.CollectionLang)
	collection := &model.TCollection{
		Name:    ragName,
		RagName: ragName,
		Lang:    config.CollectionLang,
	}
	assistantCollection := &model.TAssistantCollection{
		Threshold:   config.Threshold,
		DocTopN:     config.DocTopN,
		ChunkConfig: config.ChunkConfig,
	}
	return collection, assistantCollection
}

func newAllowlist(config *aipb.AssistantConfig) []*model.TAssistantAllowlist {
	if config.AllowlistConfig == nil {
		return nil
	}

	allowlist := make([]*model.TAssistantAllowlist, 0, len(config.AllowlistConfig.Phones))

	// 手机
	phoneUnique := make(map[string]bool)
	for _, phone := range config.AllowlistConfig.Phones {
		if phoneUnique[phone] {
			continue
		}
		phoneUnique[phone] = true
		allowlist = append(allowlist, &model.TAssistantAllowlist{
			Type:       aipb.AssistantAllowlistType_ASSISTANT_ALLOWLIST_TYPE_PHONE,
			Value:      phone,
			CreateDate: time.Now(),
		})
	}

	// 移除phones
	config.AllowlistConfig.Phones = nil

	return allowlist
}

var ragNameRandom = xstrings.NewSlowRandomFactory(xstrings.RandomAlphabet("abcdefghijklmnopqrstuvwxyz"))

func newRagName(name, embeddingModel string) string {
	embeddingModel = strings.ToLower(embeddingModel)
	name = replaceSpace(name, "-")
	name = strings.ToLower(name) + "-" + ragNameRandom.Make(4)
	if embeddingModel != "" {
		name = embeddingModel + "_" + name
	}
	return name
}
