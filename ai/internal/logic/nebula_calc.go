package logic

import (
	"context"
	"errors"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/assistant"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BatchCreateNebulaTasksBySplit 用户逐个批量创建星云任务
/*
1. 获取全站用户助手
2. 找出对应助手所归属的个人or团队
3. 助手+用户信息
4. 创建任务
*/
func BatchCreateNebulaTasksBySplit(ctx context.Context, req *aipb.ReqBatchCreateNebulaTasks) error {

	log.WithContext(ctx).Infow("BatchCreateNebulaTasksBySplit start")

	time.Sleep(10 * time.Second)
	batchSize := 1000 // 每次查询的批次大小
	offset := 0
	totalProcessed := 0

	// 用于记录已经处理过的参数组合，避免重复创建
	processedCombinations := make(map[string]bool)

	taskModel := config.GetStringOr("nebula.task_model", "all")

	excludeSet := make(map[uint64]bool)
	for _, id := range config.GetIntSlice("nebula.exclude_ids") {
		excludeSet[uint64(id)] = true
	}

	onlySet := make(map[uint64]bool)
	for _, id := range config.GetIntSlice("nebula.only_ids") {
		onlySet[uint64(id)] = true
	}

	clusteringMethod := config.GetStringOr("nebula.clustering_method", "dbscan")

	for {
		// 1. 从t_assistant_collection表中批量获取assistant_id
		var assistantIDs []uint64
		err := model.NewQuery[model.TAssistantCollection](ctx).
			Select("DISTINCT assistant_id").OrderBy("assistant_id", req.OrderBy).
			Limit(batchSize).
			Offset(offset).
			Scan(&assistantIDs)

		if err != nil {
			log.WithContext(ctx).Errorw("BatchCreateNebulaTasksBySplit query error", "err", err)
			return err
		}

		// 如果没有数据了，退出循环
		if len(assistantIDs) == 0 {
			break
		}

		// 2. 逐个处理每个assistantID
		for _, assistantID := range assistantIDs {

			// 排除模式
			if taskModel == "exclude" && excludeSet[assistantID] {
				log.WithContext(ctx).Infow("BatchCreateNebulaTasksBySplit skip exclude assistantID",
					"assistantID", assistantID)
				continue
			}

			// 只执行模式
			if taskModel == "only" && len(onlySet) > 0 && !onlySet[assistantID] {
				log.WithContext(ctx).Infow("BatchCreateNebulaTasksBySplit skip only assistantID",
					"assistantID", assistantID)
				continue
			}

			assistantAdminInfo, err := findCreateInfoByAssistantID(ctx, assistantID)
			if err != nil {
				log.WithContext(ctx).Errorw("BatchCreateNebulaTasksBySplit find create info error",
					"assistantID", assistantID, "err", err)
				return err
			}

			// 3. 获取创建人信息,助手对应多个用户or团队
			for _, info := range assistantAdminInfo {

				//lang, err := getLangByAssistantID(ctx, assistantID)
				supportedLanguages := config.GetStringSliceOr("nebula.supported_languages", []string{"bge-m3", "zh", "en"})

				for _, lang := range supportedLanguages {
					// 生成唯一组合键
					combinationKey := fmt.Sprintf("%d_%d_%v_%s", assistantID, info.TeamID, info.AsTeam, lang)

					// 检查是否已经处理过这个组合
					if _, exists := processedCombinations[combinationKey]; exists {
						log.WithContext(ctx).Infow("BatchCreateNebulaTasksBySplit skip duplicate combination",
							"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
						continue
					}

					autoCalc := xredis.Default.Get(ctx, "nebula_can_auto_calc").Val()
					if len(autoCalc) > 0 {
						log.WithContext(ctx).Errorw("BatchCreateNebulaTasksBySplit nebula.can_auto_calc is empty")
						return nil
					}

					// 检查是否已经存在任务，如果存在则跳过
					assistantIds, _ := uint64ArrayToJSON([]uint64{assistantID})

					// 1、知识库处理
					query := model.NewQuery[model.TNebulaTask](ctx).
						Where("lang = ?", lang).
						Where("assistant_ids = ?", assistantIds).
						Where("auto_create = ?", model.AutoCreate).
						Where("state in  ? ", []int{model.TaskStateCompleted, model.NebulaTaskStateOK})

					if info.AsTeam {
						query = query.Where("team_id = ?  and admin_type = ? ", info.TeamID, model.AdminTypeTeam)
					} else {
						query = query.Where("user_id = ?  and admin_type = ? ", info.UserID, model.AdminTypeUser)
					}

					count, err := query.Count()
					if err != nil {
						log.WithContext(ctx).Errorw("BatchCreateNebulaTasksBySplit query error", "err", err)
						return err
					}

					if count > 0 {
						log.WithContext(ctx).Infow("BatchCreateNebulaTasksBySplit skip existing task",
							"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
					}

					uuidStr := uuid.New().String()

					reqData := &aipb.ReqCreateNebulaTask{
						AssistantId:      []uint64{assistantID},
						UserId:           info.UserID,
						TeamId:           info.TeamID,
						AsTeam:           info.AsTeam,
						Lang:             lang,
						AutoCreate:       model.AutoCreate,
						LoadEmbedding:    req.LoadEmbedding,
						ClusteringMethod: clusteringMethod,
					}

					// 调用CreateNebulaTask创建任务
					err = CreateNebulaTask(ctx, reqData, uuidStr)
					if err != nil {
						log.WithContext(ctx).Errorw("BatchCreateNebulaTasksBySplit create task error",
							"assistantID", assistantID, "uuid", uuidStr, "err", err, "req", req)

					}

					// 2、提问处理
					queryAssistantIds, _ := uint64ArrayToJSON([]uint64{assistantID})

					query = model.NewQuery[model.TNebulaTask](ctx).
						Where("lang = ?", lang).
						Where("query_assistant_ids = ?", queryAssistantIds).
						Where("auto_create = ?", model.AutoCreate).
						Where("state in  ? ", []int{model.TaskStateCompleted, model.NebulaTaskStateOK})

					if info.AsTeam {
						query = query.Where("team_id = ?  and admin_type = ? ", info.TeamID, model.AdminTypeTeam)
					} else {
						query = query.Where("user_id = ?  and admin_type = ? ", info.UserID, model.AdminTypeUser)
					}

					count, err = query.Count()
					if err != nil {
						log.WithContext(ctx).Errorw("BatchCreateNebulaTasksBySplit query error", "err", err)
						return err
					}

					if count > 0 {
						log.WithContext(ctx).Infow("BatchCreateNebulaTasksBySplit skip existing task",
							"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
						continue
					}

					uuidStr = uuid.New().String()
					reqData = &aipb.ReqCreateNebulaTask{
						QueryAssistantId: []uint64{assistantID},
						UserId:           info.UserID,
						TeamId:           info.TeamID,
						AsTeam:           info.AsTeam,
						Lang:             lang,
						AutoCreate:       model.AutoCreate,
						LoadEmbedding:    req.LoadEmbedding,
						ClusteringMethod: clusteringMethod,
					}

					// 调用CreateNebulaTask创建任务
					err = CreateNebulaTask(ctx, reqData, uuidStr)
					if err != nil {
						log.WithContext(ctx).Errorw("BatchCreateNebulaTasksBySplit create task error",
							"assistantID", assistantID, "uuid", uuidStr, "err", err, "req", req)

					}

					log.WithContext(ctx).Infow("BatchCreateNebulaTasksBySplit created task",
						"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

					// 标记这个组合为已处理
					processedCombinations[combinationKey] = true
					totalProcessed++
				}

			}

		}

		offset += batchSize
	}

	log.WithContext(ctx).Infow("BatchCreateNebulaTasksBySplit completed",
		"total_processed", totalProcessed)
	return nil
}

// BatchCreateNebulaTasks 用户逐个批量创建星云任务
/*
1. 获取全站用户助手
2. 找出对应助手所归属的个人or团队
3. 助手+用户信息+语言
4. 创建任务
*/
func BatchCreateNebulaTasks(ctx context.Context, req *aipb.ReqBatchCreateNebulaTasks) error {

	log.WithContext(ctx).Infow("BatchCreateNebulaTasks start")

	batchSize := 1000 // 每次查询的批次大小
	offset := 0
	totalProcessed := 0

	// 用于记录已经处理过的参数组合，避免重复创建
	processedCombinations := make(map[string]bool)

	taskModel := config.GetStringOr("nebula.task_model", "all")

	excludeSet := make(map[uint64]bool)
	for _, id := range config.GetIntSlice("nebula.exclude_ids") {
		excludeSet[uint64(id)] = true
	}

	clusteringMethod := config.GetStringOr("nebula.clustering_method", "dbscan")

	onlySet := make(map[uint64]bool)
	for _, id := range config.GetIntSlice("nebula.only_ids") {
		onlySet[uint64(id)] = true
	}

	for {
		// 1. 从t_assistant_collection表中批量获取assistant_id
		var assistantIDs []uint64
		err := model.NewQuery[model.TAssistantCollection](ctx).
			Select("DISTINCT assistant_id").OrderBy("assistant_id", req.OrderBy).
			Limit(batchSize).
			Offset(offset).
			Scan(&assistantIDs)

		if err != nil {
			log.WithContext(ctx).Errorw("BatchCreateNebulaTasks query error", "err", err)
			return err
		}

		// 如果没有数据了，退出循环
		if len(assistantIDs) == 0 {
			break
		}

		// 2. 逐个处理每个assistantID
		for _, assistantID := range assistantIDs {

			// 排除模式
			if taskModel == "exclude" && excludeSet[assistantID] {
				log.WithContext(ctx).Infow("BatchCreateNebulaTasks skip exclude assistantID",
					"assistantID", assistantID)
				continue
			}

			// 只执行模式
			if taskModel == "only" && len(onlySet) > 0 && !onlySet[assistantID] {
				log.WithContext(ctx).Infow("BatchCreateNebulaTasks skip only assistantID",
					"assistantID", assistantID)
				continue
			}

			assistantAdminInfo, err := findCreateInfoByAssistantID(ctx, assistantID)
			if err != nil {
				log.WithContext(ctx).Errorw("BatchCreateNebulaTasks find create info error",
					"assistantID", assistantID, "err", err)
				return err
			}

			// 3. 获取创建人信息,助手对应多个用户or团队
			for _, info := range assistantAdminInfo {

				// 获取支持的语言列表
				supportedLanguages := config.GetStringSliceOr("nebula.supported_languages", []string{"bge-m3", "zh", "en"})

				if len(req.Lang) > 0 {
					supportedLanguages = []string{req.Lang}
				}

				for _, lang := range supportedLanguages {

					autoCalc := xredis.Default.Get(ctx, "nebula_can_auto_calc").Val()
					if len(autoCalc) > 0 {
						log.WithContext(ctx).Errorw("BatchCreateNebulaTasks nebula.can_auto_calc is empty")
						return nil
					}

					// 检查是否已经存在任务，如果存在则跳过
					assistantIds, _ := uint64ArrayToJSON([]uint64{assistantID})
					queryAssistantIds, _ := uint64ArrayToJSON([]uint64{assistantID})

					query := model.NewQuery[model.TNebulaTask](ctx).
						Where("lang = ?", lang).
						Where("assistant_ids = ?", assistantIds).
						Where("query_assistant_ids = ?", queryAssistantIds).
						Where("auto_create = ?", model.AutoCreate).
						Where("state in ?", []int{model.TaskStateCompleted, model.NebulaTaskStateOK})

					if info.AsTeam {
						query = query.Where("team_id = ?  and admin_type = ? ", info.TeamID, model.AdminTypeTeam)
					} else {
						query = query.Where("user_id = ?  and admin_type = ? ", info.UserID, model.AdminTypeUser)
					}

					count, err := query.Count()
					if err != nil {
						log.WithContext(ctx).Errorw("BatchCreateNebulaTasks query error", "err", err)
						return err
					}

					if count > 0 {
						log.WithContext(ctx).Infow("BatchCreateNebulaTasks skip existing task",
							"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
						continue
					}

					uuidStr := uuid.New().String()

					// 生成唯一组合键
					combinationKey := fmt.Sprintf("%d_%d_%v_%s", assistantID, info.TeamID, info.AsTeam, lang)

					// 检查是否已经处理过这个组合
					if _, exists := processedCombinations[combinationKey]; exists {
						log.WithContext(ctx).Infow("BatchCreateNebulaTasks skip duplicate combination",
							"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
						continue
					}

					reqData := &aipb.ReqCreateNebulaTask{
						AssistantId:      []uint64{assistantID},
						QueryAssistantId: []uint64{assistantID},
						UserId:           info.UserID,
						TeamId:           info.TeamID,
						AsTeam:           info.AsTeam,
						Lang:             lang,
						AutoCreate:       model.AutoCreate,
						LoadEmbedding:    req.LoadEmbedding,
						ClusteringMethod: clusteringMethod,
					}

					log.WithContext(ctx).Infow("BatchCreateNebulaTasks start AssistantId",
						"assistantID", assistantID, "uuid", uuidStr, "reqData", reqData, "lang", lang)

					// 调用CreateNebulaTask创建任务
					err = CreateNebulaTask(ctx, reqData, uuidStr)
					if err != nil {
						log.WithContext(ctx).Errorw("BatchCreateNebulaTasks create task error",
							"assistantID", assistantID, "uuid", uuidStr, "err", err, "req", req)
						// 可以选择继续处理下一个而不是返回
						continue
					}

					// 标记这个组合为已处理
					processedCombinations[combinationKey] = true

					log.WithContext(ctx).Infow("BatchCreateNebulaTasks created task",
						"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

					totalProcessed++

				}

			}

		}

		offset += batchSize
	}

	log.WithContext(ctx).Infow("BatchCreateNebulaTasks completed",
		"total_processed", totalProcessed)
	return nil
}

func BatchCreateNebulaTasksPara(ctx context.Context, req *aipb.ReqBatchCreateNebulaTasks) {
	uuidStr := uuid.New().String()
	reqdata := &aipb.ReqCreateNebulaTask{
		AssistantId:      req.AssistantId,
		QueryAssistantId: req.QueryAssistantId,
		UserId:           req.UserId,
		TeamId:           req.TeamId,
		AsTeam:           req.AsTeam,
		Lang:             req.Lang,
		AutoCreate:       model.AutoCreate,
		LoadEmbedding:    req.LoadEmbedding,
	}

	log.WithContext(ctx).Infow("BatchCreateNebulaTasksPara start AssistantId",
		"reqdata", reqdata, "uuid", uuidStr, "req", req)

	// 调用CreateNebulaTask创建任务
	err := CreateNebulaTask(ctx, reqdata, uuidStr)
	if err != nil {
		log.WithContext(ctx).Errorw("BatchCreateNebulaTasksPara create task error",
			"err", err, "uuid", uuidStr, "req", req)
		return
	}

	log.WithContext(ctx).Infow("BatchCreateNebulaTasksPara created task success", "uuid", uuidStr, "req", req)
}

// BatchCreateNebulaManyTasks 批量创建星云任务（用户多助手）
/*
1. 获取全站用户助手
2. 找出对应助手所归属的个人or团队
3. 助手+用户信息
4. 创建任务
*/
func BatchCreateNebulaManyTasks(ctx context.Context, req *aipb.ReqBatchCreateNebulaTasks) error {

	log.WithContext(ctx).Infow("BatchCreateNebulaManyTasks start")

	time.Sleep(5 * time.Second)
	batchSize := 1000 // 每次查询的批次大小
	offset := 0
	totalProcessed := 0

	// 用于记录已经处理过的参数组合，避免重复创建
	processedCombinations := make(map[string]bool)

	taskModel := config.GetStringOr("nebula.task_model", "all")

	excludeSet := make(map[uint64]bool)
	for _, id := range config.GetIntSlice("nebula.exclude_ids") {
		excludeSet[uint64(id)] = true
	}

	onlySet := make(map[uint64]bool)
	for _, id := range config.GetIntSlice("nebula.only_ids") {
		onlySet[uint64(id)] = true
	}

	clusteringMethod := config.GetStringOr("nebula.clustering_method", "dbscan")

	for {
		// 1. 从t_assistant_collection表中批量获取assistant_id
		var assistantIDs []uint64
		err := model.NewQuery[model.TAssistantCollection](ctx).
			Select("DISTINCT assistant_id").
			Limit(batchSize).
			Offset(offset).OrderBy("assistant_id", req.OrderBy).
			Scan(&assistantIDs)

		if err != nil {
			log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasks query error", "err", err)
			return err
		}

		// 如果没有数据了，退出循环
		if len(assistantIDs) == 0 {
			break
		}

		// 2. 逐个处理每个assistantID
		for _, assistantID := range assistantIDs {

			// 排除模式
			if taskModel == "exclude" && excludeSet[assistantID] {
				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasks skip exclude assistantID",
					"assistantID", assistantID)
				continue
			}

			// 只执行模式
			if taskModel == "only" && len(onlySet) > 0 && !onlySet[assistantID] {
				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasks skip only assistantID",
					"assistantID", assistantID)
				continue
			}

			assistantAdminInfo, err := findCreateInfoByAssistantID(ctx, assistantID)
			if err != nil {
				log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasks find create info error",
					"assistantID", assistantID, "err", err)
				return err
			}

			// 3. 获取创建人信息,助手对应多个用户or团队
			for _, info := range assistantAdminInfo {

				lang, err := getLangByAssistantID(ctx, assistantID)
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasks get lang error",
						"assistantID", assistantID, "err", err)
					return err
				}

				//生成唯一组合键
				combinationKey := fmt.Sprintf("%d_%v", info.UserID, info.AsTeam)
				// 检查是否已经处理过这个组合
				if _, exists := processedCombinations[combinationKey]; exists {
					log.WithContext(ctx).Infow("BatchCreateNebulaManyTasks skip duplicate combination",
						"userID", info.UserID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
					continue
				}

				allAssistantIDs, err := GetTeamAssistantIDs(ctx, info.TeamID, info.AdminType)
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasks get team assistant error",
						"teamID", info.TeamID, "asTeam", info.AsTeam, "err", err)
					return err
				}

				assistantIds, _ := uint64ArrayToJSON(allAssistantIDs)

				query := model.NewQuery[model.TNebulaTask](ctx).
					Where("lang = ?", lang).
					Where("assistant_ids = ?", assistantIds).
					Where("query_assistant_ids = ?", assistantIds).
					Where("auto_create = ?", model.AutoCreate).
					Where("state in ?", []int{model.TaskStateCompleted, model.NebulaTaskStateOK})

				if info.AsTeam {
					query = query.Where("team_id = ?  and admin_type = ? ", info.TeamID, model.AdminTypeTeam)
				} else {
					query = query.Where("user_id = ?  and admin_type = ? ", info.UserID, model.AdminTypeUser)
				}

				count, err := query.Count()
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasks query error", "err", err)
					return err
				}

				if count > 0 {
					log.WithContext(ctx).Infow("BatchCreateNebulaManyTasks skip existing task",
						"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
					continue
				}

				// 1、团队下多个助手，知识库+提问
				uuidStr := uuid.New().String()
				req := &aipb.ReqCreateNebulaTask{
					AssistantId:      allAssistantIDs,
					QueryAssistantId: allAssistantIDs,
					UserId:           info.UserID,
					TeamId:           info.TeamID,
					AsTeam:           info.AsTeam,
					Lang:             lang,
					AutoCreate:       model.AutoCreate,
					ClusteringMethod: clusteringMethod,
				}

				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasks start AssistantId",
					"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

				// 调用CreateNebulaTask创建任务
				err = CreateNebulaTask(ctx, req, uuidStr)
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasks create task error",
						"assistantID", assistantID, "uuid", uuidStr, "err", err, "req", req)
					// 可以选择继续处理下一个而不是返回
				}

				// 标记这个组合为已处理
				processedCombinations[combinationKey] = true

				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasks created task",
					"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

				totalProcessed++

			}

		}

		offset += batchSize
	}

	log.WithContext(ctx).Infow("BatchCreateNebulaManyTasks completed",
		"total_processed", totalProcessed)
	return nil
}

func BatchCreateNebulaManyTasksBySplit(ctx context.Context, req *aipb.ReqBatchCreateNebulaTasks) error {
	log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit start")

	time.Sleep(15 * time.Second)
	batchSize := 1000 // 每次查询的批次大小
	offset := 0
	totalProcessed := 0

	// 用于记录已经处理过的参数组合，避免重复创建
	processedCombinations := make(map[string]bool)

	taskModel := config.GetStringOr("nebula.task_model", "all")

	excludeSet := make(map[uint64]bool)
	for _, id := range config.GetIntSlice("nebula.exclude_ids") {
		excludeSet[uint64(id)] = true
	}

	onlySet := make(map[uint64]bool)
	for _, id := range config.GetIntSlice("nebula.only_ids") {
		onlySet[uint64(id)] = true
	}

	clusteringMethod := config.GetStringOr("nebula.clustering_method", "dbscan")

	for {
		// 1. 从t_assistant_collection表中批量获取assistant_id
		var assistantIDs []uint64
		err := model.NewQuery[model.TAssistantCollection](ctx).
			Select("DISTINCT assistant_id").
			Limit(batchSize).
			Offset(offset).OrderBy("assistant_id", req.OrderBy).
			Scan(&assistantIDs)

		if err != nil {
			log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasksBySplit query error", "err", err)
			return err
		}

		// 如果没有数据了，退出循环
		if len(assistantIDs) == 0 {
			break
		}

		// 2. 逐个处理每个assistantID
		for _, assistantID := range assistantIDs {

			// 排除模式
			if taskModel == "exclude" && excludeSet[assistantID] {
				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit skip exclude assistantID",
					"assistantID", assistantID)
				continue
			}

			// 只执行模式
			if taskModel == "only" && len(onlySet) > 0 && !onlySet[assistantID] {
				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit skip only assistantID",
					"assistantID", assistantID)
				continue
			}

			assistantAdminInfo, err := findCreateInfoByAssistantID(ctx, assistantID)
			if err != nil {
				log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasksBySplit find create info error",
					"assistantID", assistantID, "err", err)
				return err
			}

			// 3. 获取创建人信息,助手对应多个用户or团队
			for _, info := range assistantAdminInfo {

				lang, err := getLangByAssistantID(ctx, assistantID)
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasksBySplit get lang error",
						"assistantID", assistantID, "err", err)
					return err
				}

				//生成唯一组合键
				combinationKey := fmt.Sprintf("%d_%v", info.UserID, info.AsTeam)
				// 检查是否已经处理过这个组合
				if _, exists := processedCombinations[combinationKey]; exists {
					log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit skip duplicate combination",
						"userID", info.UserID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
					continue
				}

				allAssistantIDs, err := GetTeamAssistantIDs(ctx, info.TeamID, info.AdminType)
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasksBySplit get team assistant error",
						"teamID", info.TeamID, "asTeam", info.AsTeam, "err", err)
					return err
				}

				assistantIds, _ := uint64ArrayToJSON(allAssistantIDs)

				// 1、 知识库
				query := model.NewQuery[model.TNebulaTask](ctx).
					Where("lang = ?", lang).
					Where("assistant_ids = ?", assistantIds).
					Where("auto_create = ?", model.AutoCreate).
					Where("state in ? ", []int{model.TaskStateCompleted, model.NebulaTaskStateOK})

				if info.AsTeam {
					query = query.Where("team_id = ?  and admin_type = ? ", info.TeamID, model.AdminTypeTeam)
				} else {
					query = query.Where("user_id = ?  and admin_type = ? ", info.UserID, model.AdminTypeUser)
				}

				count, err := query.Count()
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasksBySplit query error", "err", err)
					return err
				}

				if count > 0 {
					log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit skip existing task",
						"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
					continue
				}

				uuidStr := uuid.New().String()
				req := &aipb.ReqCreateNebulaTask{
					AssistantId:      allAssistantIDs,
					UserId:           info.UserID,
					TeamId:           info.TeamID,
					AsTeam:           info.AsTeam,
					Lang:             lang,
					AutoCreate:       model.AutoCreate,
					ClusteringMethod: clusteringMethod,
				}

				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit start AssistantId",
					"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

				// 调用CreateNebulaTask创建任务
				err = CreateNebulaTask(ctx, req, uuidStr)
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasksBySplit create task error",
						"assistantID", assistantID, "uuid", uuidStr, "err", err, "req", req)
					// 可以选择继续处理下一个而不是返回
				}

				// 2、提问
				query = model.NewQuery[model.TNebulaTask](ctx).
					Where("lang = ?", lang).
					Where("query_assistant_ids = ?", assistantIds).
					Where("auto_create = ?", model.AutoCreate).
					Where("state in ?", []int{model.TaskStateCompleted, model.NebulaTaskStateOK})

				if info.AsTeam {
					query = query.Where("team_id = ?  and admin_type = ? ", info.TeamID, model.AdminTypeTeam)
				} else {
					query = query.Where("user_id = ?  and admin_type = ? ", info.UserID, model.AdminTypeUser)
				}

				count, err = query.Count()
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasksBySplit query error", "err", err)
					return err
				}

				if count > 0 {
					log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit skip existing task",
						"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
					continue
				}

				uuidStr = uuid.New().String()
				req = &aipb.ReqCreateNebulaTask{
					QueryAssistantId: allAssistantIDs,
					UserId:           info.UserID,
					TeamId:           info.TeamID,
					AsTeam:           info.AsTeam,
					Lang:             lang,
					AutoCreate:       model.AutoCreate,
					ClusteringMethod: clusteringMethod,
				}

				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit start AssistantId",
					"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

				// 调用CreateNebulaTask创建任务
				err = CreateNebulaTask(ctx, req, uuidStr)
				if err != nil {
					log.WithContext(ctx).Errorw("BatchCreateNebulaManyTasksBySplit create task error",
						"assistantID", assistantID, "uuid", uuidStr, "err", err, "req", req)
				}

				// 标记这个组合为已处理
				processedCombinations[combinationKey] = true

				log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit created task",
					"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

				totalProcessed++

			}

		}

		offset += batchSize
	}

	log.WithContext(ctx).Infow("BatchCreateNebulaManyTasksBySplit completed",
		"total_processed", totalProcessed)
	return nil
}

// AssistantAdminInfo 表示助手的管理信息
type AssistantAdminInfo struct {
	AssistantID uint64 `json:"assistant_id"`
	UserID      uint64
	TeamID      uint64
	AdminType   uint32 `json:"admin_type"` // 1-个人, 2-团队
	AsTeam      bool
}

// findCreateInfoByAssistantID 通过助手ID获取创建人情况
func findCreateInfoByAssistantID(ctx context.Context, assistantID uint64) ([]AssistantAdminInfo, error) {
	var results []struct {
		AssistantID uint64 `gorm:"column:assistant_id"`
		AdminID     uint64 `gorm:"column:admin_id"`
		AdminType   uint32 `gorm:"column:admin_type"`
	}

	err := model.NewQuery[model.TAssistantAdmin](ctx).
		Select("assistant_id, admin_id, admin_type").
		Where("assistant_id = ?", assistantID).
		Scan(&results)

	if err != nil {
		log.WithContext(ctx).Errorw("findCreateInfoByAssistantID query err",
			"assistant_id", assistantID, "error", err)
		return nil, err
	}

	adminInfos := make([]AssistantAdminInfo, 0, len(results))
	for _, r := range results {
		adminInfos = append(adminInfos, AssistantAdminInfo{
			AssistantID: r.AssistantID,
			TeamID:      r.AdminID,
			UserID:      r.AdminID,
			AdminType:   r.AdminType,
			AsTeam:      r.AdminType == model.AdminTypeTeam,
		})
	}

	return adminInfos, nil
}

// getLangByAssistantID 通过助手ID获取关联集合的语言（一对一关系）
func getLangByAssistantID(ctx context.Context, assistantID uint64) (string, error) {
	var lang string

	// 执行查询：获取与助手关联的集合的语言
	err := model.NewQuery[model.TCollection](ctx).
		Select("lang").
		Where("id = (SELECT collection_id FROM t_assistant_collection WHERE assistant_id = ? LIMIT 1)", assistantID).
		Scan(&lang)

	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {

		log.WithContext(ctx).Errorw("GetLangByAssistantID query failed",
			"assistant_id", assistantID,
			"error", err)
		return model.ModelTypeBGEM3, err
	}

	if lang == "" {
		log.WithContext(ctx).Infow("Empty lang value for assistant",
			"assistant_id", assistantID)
		return model.ModelTypeBGEM3, nil
	}

	return lang, nil
}

// getCollectionByAssistantID 通过助手ID获取关联集合的rag_name（一对一关系）
func getCollectionByAssistantID(ctx context.Context, assistantID uint64) (string, error) {
	var ragName string

	// 执行查询：获取与助手关联的集合的rag_name
	err := model.NewQuery[model.TCollection](ctx).
		Select("rag_name").
		Where("id = (SELECT collection_id FROM t_assistant_collection WHERE assistant_id = ? LIMIT 1)", assistantID).
		Scan(&ragName)

	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		log.WithContext(ctx).Errorw("getCollectionByAssistantID query failed",
			"assistant_id", assistantID,
			"error", err)
		return "", err
	}

	if ragName == "" {
		log.WithContext(ctx).Infow("Empty rag_name value for assistant",
			"assistant_id", assistantID)
		return "", nil
	}

	return ragName, nil
}

// GetTeamAssistantIDs 获取团队下的非草稿助手ID列表
func GetTeamAssistantIDs(ctx context.Context, teamID uint64, adminType uint32) ([]uint64, error) {
	var assistantIDs []uint64

	// 构建查询: 获取团队下的非草稿助手ID
	err := model.NewQuery[model.TAssistantAdmin](ctx).
		Select("t_assistant_admin.assistant_id").Join("JOIN t_assistant a "+
		"ON t_assistant_admin.assistant_id = a.id").
		Where("a.is_draft = ?", 0).
		Where("t_assistant_admin.admin_id = ?", teamID).
		Where("t_assistant_admin.admin_type = ?", adminType).
		Scan(&assistantIDs)

	if err != nil {
		log.WithContext(ctx).Errorw("GetTeamAssistantIDs query failed",
			"team_id", teamID,
			"error", err)
		return nil, err
	}

	return assistantIDs, nil
}

// NebulaCalcKnowLedge 计算知识库
func NebulaCalcKnowLedge(ctx context.Context) error {
	ctx = context.Background()
	log.WithContext(ctx).Infow("NebulaCalcKnowLedge start")

	batchSize := 1000 // 每次查询的批次大小
	offset := 0
	totalProcessed := 0

	clusteringMethod := config.GetStringOr("nebula.clustering_method", "dbscan")

	// 用于记录已经处理过的参数组合，避免重复创建
	processedCombinations := make(map[string]bool)

	for {
		// 1. 从t_assistant_collection表中批量获取assistant_id
		var assistantIDs []uint64
		err := model.NewQuery[model.TAssistantCollection](ctx).
			Select("DISTINCT assistant_id").OrderBy("assistant_id", false).
			Limit(batchSize).
			Offset(offset).
			Scan(&assistantIDs)

		if err != nil {
			log.WithContext(ctx).Errorw("NebulaCalcKnowLedge query error", "err", err)
			return err
		}

		// 如果没有数据了，退出循环
		if len(assistantIDs) == 0 {
			break
		}

		// 2. 逐个处理每个assistantID
		for _, assistantID := range assistantIDs {

			rate, err := assistant.CalculateAssistantDataChangeRate24H(ctx, assistantID)
			if err != nil {
				log.WithContext(ctx).Errorw("NebulaCalcKnowLedge CalculateAssistantDataChangeRate24H error", "err", err)
				continue
			}

			// 变化率不超过10%不处理
			if rate < config.GetFloat64Or("nebula_calc_knowledge_min_change_rate", 10.0) {
				log.WithContext(ctx).Infow("NebulaCalcKnowLedge rate < 10.0", "rate", rate, "assistantID", assistantID)
				continue
			}

			assistantAdminInfo, err := findCreateInfoByAssistantID(ctx, assistantID)
			if err != nil {
				log.WithContext(ctx).Errorw("NebulaCalcKnowLedge find create info error",
					"assistantID", assistantID, "err", err)
				return err
			}

			// 3. 获取创建人信息,助手对应多个用户or团队
			for _, info := range assistantAdminInfo {

				var langs []string
				// 获取支持的语言列表
				langs = append(langs, model.ModelTypeBGEM3, model.ModelTypeZh, model.ModelTypeEn)

				for _, lang := range langs {

					uuidStr := uuid.New().String()

					// 生成唯一组合键
					combinationKey := fmt.Sprintf("%d_%d_%v_%s", assistantID, info.TeamID, info.AsTeam, lang)

					// 检查是否已经处理过这个组合
					if _, exists := processedCombinations[combinationKey]; exists {
						log.WithContext(ctx).Infow("NebulaCalcKnowLedge skip duplicate combination",
							"assistantID", assistantID, "teamID", info.TeamID, "asTeam", info.AsTeam, "lang", lang)
						continue
					}

					// 标记这个组合为已处理
					processedCombinations[combinationKey] = true

					req := &aipb.ReqCreateNebulaTask{
						AssistantId:      []uint64{assistantID},
						QueryAssistantId: []uint64{assistantID},
						UserId:           info.UserID,
						TeamId:           info.TeamID,
						AsTeam:           info.AsTeam,
						Lang:             lang,
						AutoCreate:       model.AutoCreate,
						ClusteringMethod: clusteringMethod,
					}

					log.WithContext(ctx).Infow("NebulaCalcKnowLedge start AssistantId",
						"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

					// 调用CreateNebulaTask创建任务
					err = CreateNebulaTask(ctx, req, uuidStr)
					if err != nil {
						log.WithContext(ctx).Errorw("NebulaCalcKnowLedge create task error",
							"assistantID", assistantID, "err", err, "req", req)
						// 可以选择继续处理下一个而不是返回
						continue
					}

					log.WithContext(ctx).Infow("NebulaCalcKnowLedge created task",
						"assistantID", assistantID, "uuid", uuidStr, "req", req, "lang", lang)

					totalProcessed++

				}

			}

		}

		offset += batchSize
	}

	log.WithContext(ctx).Infow("NebulaCalcKnowLedge completed",
		"total_processed", totalProcessed)
	return nil

}

// NebulaCalcResetPod 回滚计算pod至空闲状态
func NebulaCalcResetPod(ctx context.Context) error {

	ctx = context.Background()
	log.WithContext(ctx).Infow("NebulaCalcResetPod start")

	// 连续检测次数
	const requiredChecks = 3
	successfulChecks := 0

	for i := 0; i < requiredChecks; i++ {
		count, err := model.NewQuery[model.TNebulaTask](ctx).Where("state = ? and vec_file_size > 1000", model.NebulaTaskStateCreated).Count()
		if err != nil {
			return err
		}

		if count > 0 {
			// 有正在进行的任务，重置成功检查计数
			successfulChecks = 0
			log.WithContext(ctx).Infow("NebulaCalcResetPod found running tasks", "count", count, "checkAttempt", i+1)
			break
		}

		calcingPod := xredis.Default.Get(ctx, "nebula_pod_calcing").Val()
		if calcingPod != "" {
			// 有正在进行的任务，重置成功检查计数
			successfulChecks = 0
			log.WithContext(ctx).Infow("NebulaCalcResetPod found calcingPod running", "pod", calcingPod, "checkAttempt", i+1)
			break
		}

		podSpec := xredis.Default.Get(ctx, "nebula_pod_spec").Val()
		if podSpec == "normal" {
			log.WithContext(ctx).Infow("NebulaCalcResetPod podSpec already normal", "checkAttempt", i+1)
			// 已经是normal状态，不需要更新
			return nil
		}

		successfulChecks++
		log.WithContext(ctx).Infow("NebulaCalcResetPod check passed", "checkAttempt", i+1, "successfulChecks", successfulChecks)

		// 如果不是最后一次检查，等待一段时间再检查
		if i < requiredChecks-1 {
			time.Sleep(1 * time.Second) // 可根据实际情况调整间隔时间
		}
	}

	if successfulChecks < requiredChecks {
		log.WithContext(ctx).Infow("NebulaCalcResetPod conditions not met for all checks", "successfulChecks", successfulChecks)
		return nil
	}

	// 所有检查都通过，执行更新操作
	client := NewClient
	namespace := config.GetStringOr("nebula.namespace", "tanlive-pre")
	deployment := config.GetStringOr("nebula.deployment", "tanlive-rag-calc-vis")

	log.WithContext(ctx).Infow("NebulaCalcResetPod performing update after all checks passed")

	// 更新Pod资源规格
	_, err := client().UpdateResources(UpdateResourcesRequest{
		Namespace:      namespace,
		Deployment:     deployment,
		LimitsCPU:      "1800",
		LimitsMemory:   "2000",
		RequestsCPU:    "1000",
		RequestsMemory: "1000",
	})
	if err != nil {
		return err
	}

	xredis.Default.Set(ctx, "nebula_pod_spec", "normal", 0)
	log.WithContext(ctx).Infow("NebulaCalcResetPod update completed successfully")

	return nil
}

// CreateAssistantCollectionInit 创建助手集合初始化
func CreateAssistantCollectionInit(ctx context.Context) error {

	log.WithContext(ctx).Infow("CreateAssistantCollectionInit start")

	batchSize := 1000 // 每次查询的批次大小
	offset := 0
	totalProcessed := 0

	// 用于记录已经处理过的参数组合，避免重复创建
	processedCombinations := make(map[string]bool)

	for {
		// 1. 从t_assistant_collection表中批量获取assistant_id
		var assistantIDs []uint64
		err := model.NewQuery[model.TAssistantCollection](ctx).
			Select("DISTINCT assistant_id").
			Limit(batchSize).
			Offset(offset).OrderBy("assistant_id", true).
			Scan(&assistantIDs)

		if err != nil {
			log.WithContext(ctx).Errorw("CreateAssistantCollectionInit query error", "err", err)
			return err
		}

		// 如果没有数据了，退出循环
		if len(assistantIDs) == 0 {
			break
		}

		// 2. 逐个处理每个assistantID
		for _, assistantID := range assistantIDs {

			ragName, err := getCollectionByAssistantID(ctx, assistantID)
			if err != nil {
				log.WithContext(ctx).Errorw("CreateAssistantCollectionInit find create info error",
					"assistantID", assistantID, "err", err)
				return err
			}

			lang, err := getLangByAssistantID(ctx, assistantID)
			if err != nil {
				log.WithContext(ctx).Errorw("CreateAssistantCollectionInit find lang error",
					"assistantID", assistantID, "err", err)
				return err
			}

			log.WithContext(ctx).Infow("CreateAssistantCollectionInit find ragName", "assistantID", assistantID, "ragName", ragName)

			client := NewClient
			_, err = client().ProcessElasticsearchIndexCurl(ragName, lang)
			if err != nil {
				log.WithContext(ctx).Errorw("CreateAssistantCollectionInit ProcessElasticsearchIndexCurl error",
					"assistantID", assistantID, "ragName", ragName, "err", err)
				continue
			}

			// 处理过跳过
			if processedCombinations[ragName] {
				continue
			}

			processedCombinations[ragName] = true

			totalProcessed++

		}

		offset += batchSize
	}

	log.WithContext(ctx).Infow("CreateAssistantCollectionInit completed",
		"total_processed", totalProcessed)
	return nil
}

// CreateAssistantCollectionInitPara 创建助手集合初始化
func CreateAssistantCollectionInitPara(ctx context.Context, req *aipb.ReqCreateAssistantCollectionInit) error {

	log.WithContext(ctx).Infow("CreateAssistantCollectionInitPara start", "req", req)

	ragName, err := getCollectionByAssistantID(ctx, req.AssistantId)
	if err != nil {
		log.WithContext(ctx).Errorw("CreateAssistantCollectionInit find create info error",
			"assistantID", req.AssistantId, "err", err)
		return err
	}

	lang, err := getLangByAssistantID(ctx, req.AssistantId)
	if err != nil {
		log.WithContext(ctx).Errorw("CreateAssistantCollectionInit find lang error",
			"assistantID", req.AssistantId, "err", err)
		return err
	}

	log.WithContext(ctx).Infow("CreateAssistantCollectionInit find ragName", "assistantID", req.AssistantId, "ragName", ragName)

	client := NewClient
	_, err = client().ProcessElasticsearchIndexCurl(ragName, lang)
	if err != nil {
		log.WithContext(ctx).Errorw("CreateAssistantCollectionInit ProcessElasticsearchIndexCurl error",
			"assistantID", req.AssistantId, "ragName", ragName, "err", err)
		return err
	}

	log.WithContext(ctx).Infow("CreateAssistantCollectionInit completed")
	return nil
}
