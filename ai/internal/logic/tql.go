package logic

import (
	"fmt"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tql"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// TQLFieldIsLabel 判断是不是标签字段
func TQLFieldIsLabel(field string) (labelId uint64, ok bool) {
	if strings.HasPrefix(field, "label.") {
		label := strings.TrimPrefix(field, "label.")
		id, err := strconv.ParseUint(label, 10, 64)
		if err != nil {
			return 0, false
		}
		return id, true
	}
	return 0, false
}

// 文本文件中tql的数据库字段映射
var textFileTQLFieldMapping = map[string]string{
	"file_name": "file_name",
	"text":      "index_text",
}

// qa中tql的数据库字段映射
var qaTQLFieldMapping = map[string]string{
	"question": "index_text",
	"answer":   "text",
}

const (
	// FiledMatchSqlTypeLike like方式模糊查询
	FiledMatchSqlTypeLike = iota
	// FiledMatchSqlTypeMatch 全文索引方式查询
	FiledMatchSqlTypeMatch
)

// 可以执行模糊搜索的列
type columnToMatch interface {
	genMatchSQL(value string) (cond string, arg string)
}

// 默认的like匹配
type generalLikeColumn struct {
	column string
}

func (c *generalLikeColumn) genMatchSQL(value string) (cond string, arg string) {
	cond = fmt.Sprintf("`%s` like ?", c.column)
	arg = "%" + xorm.EscapeLikeWildcards(value) + "%"
	return
}

// index_text 列，使用match匹配
type tdocIndexTextColumn struct {
}

func NewMatchColumn(column string) columnToMatch {
	switch column {
	case "index_text":
		return &tdocIndexTextColumn{}
	default:
		return &generalLikeColumn{column: column}
	}
}

func (c *tdocIndexTextColumn) genMatchSQL(value string) (cond string, arg string) {
	colum := "index_text"
	cond = fmt.Sprintf("MATCH(%s) AGAINST(? IN BOOLEAN MODE)", colum)
	arg = `"` + value + `"`
	return
}

func genFieldSQL(db *gorm.DB, exp *tql.Condition) {
	field := exp.Field.(*tql.StandardField)

	var sql string
	var args []interface{}
	switch exp.Operator {
	case tql.Match:
		column := NewMatchColumn(field.Name)
		cond, arg := column.genMatchSQL(exp.Value.(*tql.ScalarValue).Value)
		sql = cond
		args = append(args, arg)
	default:
	}

	switch exp.Boolean {
	case tql.And:
		db.Scopes(func(db *gorm.DB) *gorm.DB {
			return db.Where(sql, args...)
		})
	case tql.Or:
		db.Scopes(func(db *gorm.DB) *gorm.DB {
			return db.Or(sql, args...)
		})
		// scope 和 or 好像没法到一组条件里去
		// db.Or(sql, args...)
	default:
		db.Scopes(func(db *gorm.DB) *gorm.DB {
			return db.Where(sql, args...)
		})
	}
	return
}

func genLabelSQL(db *gorm.DB, exp *tql.Condition, labelMeta *LabelFilterMeta) {
	field := exp.Field.(*tql.StandardField)
	labelId, _ := TQLFieldIsLabel(field.Name)

	var labelFilter *aipb.LabelFilter
	switch exp.Operator {
	case tql.Match:
		labelFilter = &aipb.LabelFilter{
			Id:   labelId,
			Like: &aipb.LabelValue{AnyValue: &aipb.LabelValue_TextValue{TextValue: exp.Value.(*tql.ScalarValue).Value}},
			Op:   aipb.LabelFilterOp_LABEL_FILTER_OP_LIKE,
		}
	default:
	}

	switch exp.Boolean {
	case tql.And:
		db.Scopes(model.WithLabels(labelMeta.ObjectType, labelMeta.Tenant, []*aipb.LabelFilter{labelFilter}, true))
	case tql.Or:
		db.Scopes(model.WithLabels(labelMeta.ObjectType, labelMeta.Tenant, []*aipb.LabelFilter{labelFilter}, true, true))
	default:
		db.Scopes(model.WithLabels(labelMeta.ObjectType, labelMeta.Tenant, []*aipb.LabelFilter{labelFilter}, true))
	}
	return
}

func scopeDocTQL(db *gorm.DB, exp interface{}, labelMeta *LabelFilterMeta, fieldMapping map[string]string) *gorm.DB {
	switch exp := exp.(type) {
	case *tql.Condition:
		field := exp.Field.(*tql.StandardField)
		// 数据库字段过滤
		if column, ok := fieldMapping[field.Name]; ok {
			nameBk := field.Name
			field.Name = column
			genFieldSQL(db, exp)
			field.Name = nameBk
		}
		// 标签过滤
		if _, ok := TQLFieldIsLabel(field.Name); ok {
			genLabelSQL(db, exp, labelMeta)
		}
	case *tql.Group:
		subQ := db.Session(&gorm.Session{NewDB: true}).Model(&model.TDoc{})
		for _, v := range exp.Expression {
			scopeDocTQL(subQ, v, labelMeta, fieldMapping)
		}
		switch exp.Boolean {
		case tql.And:
			db.Where(subQ)
		case tql.Or:
			db.Or(subQ)
		default:
			db.Where(subQ)
		}
	}
	return db
}

type LabelFilterMeta struct {
	Tenant     uint64
	ObjectType aipb.CustomLabelObjectType
}

// DocWithTQL doc使用tql查询过滤
func DocWithTQL(expression tql.Expression, labelMeta *LabelFilterMeta, isQa bool) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(expression) == 0 {
			return db
		}
		// 确保已经平铺ALL/ANY函数
		expression = expression.Flatten()

		var mapping map[string]string
		if isQa {
			mapping = qaTQLFieldMapping
		} else {
			mapping = textFileTQLFieldMapping
		}
		subQ := model.NewQuery[model.TDoc](db.Statement.Context).DB()
		for _, v := range expression {
			scopeDocTQL(subQ, v, labelMeta, mapping)
		}
		return db.Where(subQ)
	}
}
