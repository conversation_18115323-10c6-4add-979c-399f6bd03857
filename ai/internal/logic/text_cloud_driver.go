package logic

import (
	"context"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcrypto"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	docClient "github.com/chinahtl/tencent-doc-sdk/client"
	docConfig "github.com/chinahtl/tencent-doc-sdk/config"
	docModel "github.com/chinahtl/tencent-doc-sdk/model"
	docUtil "github.com/chinahtl/tencent-doc-sdk/util"
	"github.com/microcosm-cc/bluemonday"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
)

// Query 查询结构体
type Query struct {
	Type         model.QueryType   // 查询类型
	StartYear    int               // 起始年份
	StartTime    string            // 开始时间
	EndTime      string            // 结束时间
	BaseURL      string            // 基础URL
	UserAgent    string            // User-Agent
	CommonParams map[string]string // 通用查询参数
	Timeout      time.Duration     // 请求超时时间

	AccountID uint64 // 账号ID
	AdminType uint32 // 账号类型
}

// Expert 专家信息结构体
type Expert struct {
	Education   string `json:"education"`   // 学历
	Gender      string `json:"gender"`      // 性别
	City        string `json:"city"`        // 城市
	Position    string `json:"postion"`     // 职称（注意原JSON中拼写为postion）
	Honor       string `json:"honor"`       // 荣誉
	Industry    string `json:"industry"`    // 行业
	UpdateTime  string `json:"update_time"` // 更新时间
	Company     string `json:"company"`     // 公司
	Fax         string `json:"fax"`         // 传真
	Email       string `json:"email"`       // 邮箱
	GreenName   string `json:"greenName"`   // 绿色技术名称
	SubjectName string `json:"subjectName"` // 学科名称
	ExpertGuid  string `json:"expertGuid"`  // 专家GUID
	Summary     string `json:"summary"`     // 简介
	Address     string `json:"address"`     // 地址
	CreateTime  string `json:"create_time"` // 创建时间
	NickName    string `json:"nickName"`    // 昵称
	UpdateUser  string `json:"updateUser"`  // 更新人
	UserName    string `json:"userName"`    // 用户名
	LoginStatus string `json:"loginStatus"` // 登录状态
	UseStatus   string `json:"useStatus"`   // 使用状态
	MobilePhone string `json:"mobilePhone"` // 手机号
	Guid        string `json:"guid"`        // GUID
	CreateUser  string `json:"createUser"`  // 创建人
	GreenCode   string `json:"greenCode"`   // 绿色技术代码
	SubjectCode string `json:"subjectCode"` // 学科代码
	Status      string `json:"status"`      // 状态
}

// Achievement 成果信息结构体
type Achievement struct {
	AchvStage       string `json:"achvStage"`       // 成果阶段（如：成熟应用阶段）
	ShowFrom        string `json:"showFrom"`        // 展示来源
	ApplyIndustry   string `json:"applyIndustry"`   // 应用行业（如：制造业）
	City            string `json:"city"`            // 所在城市（如：上海）
	IsCore          string `json:"isCore"`          // 是否核心库（如：精选库）
	Extra2          string `json:"extra2"`          // 额外字段2
	AchvFrom        string `json:"achvFrom"`        // 成果来源
	Extra3          string `json:"extra3"`          // 额外字段3
	ResearchForm    string `json:"researchForm"`    // 研究形式
	Language        string `json:"language"`        // 语言版本
	Extra1          string `json:"extra1"`          // 额外字段1
	Title           string `json:"title"`           // 成果标题（如：自动控温隔声罩）
	Descripe        string `json:"descripe"`        // 详细描述（HTML格式）
	Extra4          string `json:"extra4"`          // 额外字段4（如：专利类型）
	Extra5          string `json:"extra5"`          // 额外字段5
	Label4          string `json:"label4"`          // 标签4
	Label5          string `json:"label5"`          // 标签5
	RisingProduct   string `json:"risingProduct"`   // 是否新兴产品
	UpdateTime      string `json:"update_time"`     // 更新时间（如：2025-04-13 16:54:57）
	PublishTime     string `json:"publish_time"`    // 发布时间（如：2024-07-02 17:02:23）
	AchvAttribute   string `json:"achvAttribute"`   // 成果属性
	AuthorizeNo     string `json:"authorizeNo"`     // 授权编号（如：G20240022）
	GreenName       string `json:"greenName"`       // 绿色技术名称（如：噪声污染治理）
	SubjectName     string `json:"subjectName"`     // 学科名称（如：噪声与振动控制）
	ScienceType     string `json:"scienceType"`     // 科技类型
	CreateTime      string `json:"create_time"`     // 创建时间
	PublishUserName string `json:"publishUserName"` // 发布人姓名（如：张友春）
	AchvDept        string `json:"achvDept"`        // 成果单位（如：上海汇允环境科技有限公司）
	PublishUser     string `json:"publishUser"`     // 发布人ID
	UpdateUser      string `json:"updateUser"`      // 更新人ID
	Label1          string `json:"label1"`          // 标签1（如：降噪）
	Label2          string `json:"label2"`          // 标签2（如：隔声罩）
	Label3          string `json:"label3"`          // 标签3（如：自动控温）
	AchvType        string `json:"achvType"`        // 成果类型
	CoopWays        string `json:"coopWays"`        // 合作方式（如：技术许可）
	ReadNum         int    `json:"readNum"`         // 阅读量
	IsTop           string `json:"isTop"`           // 是否置顶
	DelStatus       string `json:"delStatus"`       // 删除状态
	Guid            string `json:"guid"`            // 全局唯一标识
	CreateUser      string `json:"createUser"`      // 创建人ID
	GreenCode       string `json:"greenCode"`       // 绿色技术代码（如：101312）
	ImportantGrade  string `json:"importantGrade"`  // 重要程度
}

// NewQueryByYear 创建新的查询实例
func NewQueryByYear(queryType model.QueryType, startYear int) *Query {
	return &Query{
		Type:      queryType,
		StartYear: startYear,
		BaseURL:   config.GetStringOr("doc.source.gtbUrl", "https://www.greentechbank.com/greentech/web/"),
		Timeout:   10 * time.Minute,
	}
}

// NewQuery 创建新的查询实例
func NewQuery(queryType model.QueryType, startTime string, endTime string) *Query {
	return &Query{
		Type:      queryType,
		StartTime: startTime,
		EndTime:   endTime,
		BaseURL:   config.GetStringOr("doc.source.gtbUrl", "https://www.greentechbank.com/greentech/web/"),
		Timeout:   10 * time.Minute,
		UserAgent: config.GetStringOr("doc.source.gtbUserAgent",
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"),
	}
}

// QueryAchievements 查询科技成果
func (q *Query) QueryAchievements() ([]Achievement, error) {
	if q.Type != model.AchievementQuery {
		return nil, fmt.Errorf("查询类型不匹配")
	}

	var allAchievements []Achievement

	// 构造查询URL
	url := fmt.Sprintf("%sachivement/queryJson?startTime=%s&endTime=%s", q.BaseURL, q.StartTime, q.EndTime)

	// 添加通用参数
	url = q.addCommonParams(url)

	achievements, err := q.fetchData(url)
	if err != nil {
		return nil, fmt.Errorf("查询成果数据失败: %v", err)
	}

	// 类型断言转换
	achvSlice, ok := achievements.([]Achievement)
	if !ok {
		return nil, fmt.Errorf("类型转换失败")
	}

	allAchievements = append(allAchievements, achvSlice...)

	return allAchievements, nil
}

// QueryAchievementsByYear 查询科技成果
func (q *Query) QueryAchievementsByYear() ([]Achievement, error) {
	if q.Type != model.AchievementQuery {
		return nil, fmt.Errorf("查询类型不匹配")
	}

	now := time.Now()
	currentYear := now.Year()
	var allAchievements []Achievement

	for year := q.StartYear; year <= currentYear; year++ {
		startTime := fmt.Sprintf("%d-01-01", year)
		endTime := fmt.Sprintf("%d-12-31", year)

		// 构造查询URL
		url := fmt.Sprintf("%sachivement/queryJson?startTime=%s&endTime=%s", q.BaseURL, startTime, endTime)

		// 添加通用参数
		url = q.addCommonParams(url)

		achievements, err := q.fetchData(url)
		if err != nil {
			return nil, fmt.Errorf("查询%d年成果数据失败: %v", year, err)
		}

		// 类型断言转换
		achvSlice, ok := achievements.([]Achievement)
		if !ok {
			return nil, fmt.Errorf("类型转换失败")
		}

		allAchievements = append(allAchievements, achvSlice...)
	}

	return allAchievements, nil
}

// QueryExperts 查询专家信息
func (q *Query) QueryExperts() ([]Expert, error) {
	if q.Type != model.ExpertQuery {
		return nil, fmt.Errorf("查询类型不匹配")
	}

	var experts []Expert

	// 构造查询URL
	url := fmt.Sprintf("%sexpert/queryJson?startTime=%s&endTime=%s", q.BaseURL, q.StartTime, q.EndTime)

	// 添加通用参数
	url = q.addCommonParams(url)

	expert, err := q.fetchData(url)
	if err != nil {
		return nil, fmt.Errorf("查询专家数据失败: %v", err)
	}

	// 类型断言转换
	achvSlice, ok := expert.([]Expert)
	if !ok {
		return nil, fmt.Errorf("类型转换失败")
	}

	experts = append(experts, achvSlice...)

	return experts, nil
}

// QueryExpertsByYear 查询专家信息
func (q *Query) QueryExpertsByYear() ([]Expert, error) {
	if q.Type != model.ExpertQuery {
		return nil, fmt.Errorf("查询类型不匹配")
	}

	now := time.Now()
	currentYear := now.Year()
	var experts []Expert

	for year := q.StartYear; year <= currentYear; year++ {
		startTime := fmt.Sprintf("%d-01-01", year)
		endTime := fmt.Sprintf("%d-12-31", year)

		// 构造查询URL
		url := fmt.Sprintf("%sexpert/queryJson?startTime=%s&endTime=%s", q.BaseURL, startTime, endTime)

		// 添加通用参数
		url = q.addCommonParams(url)

		achievements, err := q.fetchData(url)
		if err != nil {
			return nil, fmt.Errorf("查询%d年专家数据失败: %v", year, err)
		}

		// 类型断言转换
		achvSlice, ok := achievements.([]Expert)
		if !ok {
			return nil, fmt.Errorf("类型转换失败")
		}

		experts = append(experts, achvSlice...)
	}

	return experts, nil
}

func (q *Query) fetchData(url string) (interface{}, error) {
	// 创建HTTP客户端
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	client := &http.Client{
		Timeout:   q.Timeout,
		Transport: tr,
	}

	// 创建请求并添加User-Agent头
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", q.UserAgent)

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	switch q.Type {
	case model.AchievementQuery:
		var achievements []Achievement
		if err := json.Unmarshal(body, &achievements); err != nil {
			return nil, err
		}
		return achievements, nil
	case model.ExpertQuery:
		var experts []Expert
		if err := json.Unmarshal(body, &experts); err != nil {
			return nil, err
		}
		return experts, nil
	default:
		return nil, fmt.Errorf("不支持的查询类型")
	}
}

// addCommonParams 添加通用查询参数
func (q *Query) addCommonParams(baseURL string) string {
	result := baseURL
	for k, v := range q.CommonParams {
		if len(result) > 0 && result[len(result)-1] != '?' {
			result += "&"
		}
		result += fmt.Sprintf("%s=%s", k, v)
	}
	return result
}

// TencentDoc 腾讯文档
type TencentDoc struct {
	FileName       string
	FileId         string
	FileCreateTime int64
	FileModifyTime int64
	DocId          uint64
	CosUrl         string
}

// UpdateExternalSource 更新外部数据
func UpdateExternalSource(ctx context.Context, Id uint64, updateCond *model.TDocExternalSource) error {
	if Id == 0 {
		return errors.New("Id is empty")
	}

	_, err := model.NewQuery[model.TDocExternalSource](ctx).UpdateBy(updateCond, "id = ? ", Id)
	if err != nil {
		return err
	}

	return nil
}

// LoadManagedAssistants 获取管理的助手
func LoadManagedAssistants(ctx context.Context, adminType uint32, accountId uint64) (assistantIds []uint64, err error) {
	identityType := basepb.IdentityType_IDENTITY_TYPE_TEAM
	if adminType == 1 {
		identityType = basepb.IdentityType_IDENTITY_TYPE_USER
	}
	managedAssistants := &aipb.Operator{
		Type: identityType,
		Id:   accountId,
	}

	assistantIds, _, err = GetManagedAssistants(ctx, managedAssistants)
	if err != nil {
		return assistantIds, err
	}

	return assistantIds, nil
}

// AddGTBAchievement 添加GTB数据
/*
   @returns: id 当前数据ID
   @returns: bool 是否更新数据
*/
func AddGTBAchievement(ctx context.Context, achievement Achievement, adminType uint32, accountId uint64) (uint64, bool, error) {
	externalSource, err := model.NewQuery[model.TDocExternalSource](ctx).FindBy("type = ? and file_uuid = ?  ",
		uint32(model.AchievementQuery), achievement.Guid)
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return 0, false, err
	}

	if externalSource != nil && externalSource.DocID > 0 {
		// 同时在doc中不能删除，如果删除则将外部资源数据删除
		oldDoc, err := model.NewQuery[model.TDoc](ctx).Where("id = ? and  deleted_at is null ", externalSource.DocID).Find()
		if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
			return 0, false, err
		}

		// 说明实际doc中已被删除，需要删除外部数据源
		if externalSource != nil && oldDoc == nil {
			_, err := model.NewQuery[model.TDocExternalSource](ctx).DeleteBy("file_uuid = ? ", externalSource.FileUUID)
			if err != nil {
				return 0, false, err
			}

			externalSource = nil
		}
	}

	// 数据存在则对比更新时间
	if externalSource != nil && len(externalSource.FileUUID) > 0 {
		fileUpdateDate, _ := parseTimeWithLocation(achievement.UpdateTime)

		// 时间不相同则更新状态和内容
		if !externalSource.UpdateDate.Equal(*fileUpdateDate) {

			// externalSource.State = uint32(model.Updated)

			jsonData, err := json.Marshal(achievement)
			if err != nil {
				return 0, false, fmt.Errorf("failed to marshal achievement: %v", err)
			}
			externalSource.ContentJSON = string(jsonData)

			if err = model.NewQuery[model.TDocExternalSource](ctx).Where("file_uuid = ?", externalSource.FileUUID).Save(externalSource); err != nil {
				return 0, false, err
			}

			return externalSource.ID, true, nil
		}

		// 无需更新
		return externalSource.ID, true, nil
	}

	source, _ := prepareAchievementData(achievement)
	source.AdminType = adminType
	source.AccountID = accountId

	source.State = uint32(model.Synced)
	if err = model.NewQuery[model.TDocExternalSource](ctx).Create(source); err != nil {
		return 0, false, err
	}

	return source.ID, false, nil
}

// AddGTBExpert 添加GTB数据
func AddGTBExpert(ctx context.Context, expert Expert, adminType uint32, accountId uint64) (uint64, bool, error) {
	externalSource, err := model.NewQuery[model.TDocExternalSource](ctx).FindBy("type = ? and file_uuid = ?  ",
		uint32(model.ExpertQuery), expert.Guid)
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return 0, false, err
	}

	if externalSource != nil && externalSource.DocID > 0 {
		// 同时在doc中不能删除，如果删除则将外部资源数据删除
		oldDoc, err := model.NewQuery[model.TDoc](ctx).Where("id = ? and  deleted_at is null ", externalSource.DocID).Find()
		if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
			return 0, false, err
		}

		// 说明实际doc中已被删除，需要删除外部数据源
		if externalSource != nil && oldDoc == nil {
			_, err := model.NewQuery[model.TDocExternalSource](ctx).DeleteBy("file_uuid = ? ", externalSource.FileUUID)
			if err != nil {
				return 0, false, err
			}

			externalSource = nil
		}
	}

	// 数据存在则对比更新时间
	if externalSource != nil && len(externalSource.FileUUID) > 0 {
		fileUpdateDate, _ := parseTimeWithLocation(expert.UpdateTime)

		// 时间不相同则更新状态和内容
		if !externalSource.UpdateDate.Equal(*fileUpdateDate) {

			// externalSource.State = uint32(model.Updated)

			jsonData, err := json.Marshal(expert)
			if err != nil {
				return 0, false, fmt.Errorf("failed to marshal achievement: %v", err)
			}
			externalSource.ContentJSON = string(jsonData)

			if err = model.NewQuery[model.TDocExternalSource](ctx).Where("file_uuid = ?", externalSource.FileUUID).Save(externalSource); err != nil {
				return 0, false, err
			}

			return externalSource.ID, true, nil
		}

		// 无需更新
		return externalSource.ID, true, nil
	}

	source, _ := prepareExpertData(expert)
	source.AdminType = adminType
	source.AccountID = accountId
	source.State = uint32(model.Synced)

	if err = model.NewQuery[model.TDocExternalSource](ctx).Create(source); err != nil {
		return 0, false, err
	}

	return source.ID, false, nil
}

// prepareAchievementData 准备基础数据
func prepareAchievementData(achievement Achievement) (*model.TDocExternalSource, error) {
	source := &model.TDocExternalSource{
		Type:     uint32(model.AchievementQuery),
		FileUUID: achievement.Guid,
		FileName: achievement.Title,
	}

	var err error
	source.FileCreateDate, err = parseTimeWithLocation(achievement.CreateTime)
	if err != nil {
		return nil, fmt.Errorf("invalid create time: %v", err)
	}

	source.FileUpdateDate, err = parseTimeWithLocation(achievement.UpdateTime)
	if err != nil {
		return nil, fmt.Errorf("invalid update time: %v", err)
	}

	jsonData, err := json.Marshal(achievement)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal achievement: %v", err)
	}
	source.ContentJSON = string(jsonData)

	return source, nil
}

// prepareTencentDocData 准备基础数据
func prepareTencentDocData(tencentDoc *TencentDoc, md5File string, isUpdate bool) (*model.TDocExternalSource, error) {
	source := &model.TDocExternalSource{
		Type:     uint32(model.TencentDocQuery),
		FileUUID: tencentDoc.FileId,
		FileMd5:  md5File,
		FileName: tencentDoc.FileName,
	}

	source.FileCreateDate = UnixToTimePtr(tencentDoc.FileCreateTime)
	source.FileUpdateDate = UnixToTimePtr(tencentDoc.FileModifyTime)

	return source, nil
}

// AsyncUpdateTencentDocFile 异步更新腾讯文档数据
func AsyncUpdateTencentDocFile(ctx context.Context, insertId uint64, localPath string) error {
	wg := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))
	wg.SafeGo(func(ctx context.Context) error {
		// 获取文件信息
		fileInfo, err := os.Stat(localPath)
		if err != nil {
			log.WithContext(ctx).Errorw("AsyncUpdateTencentDocFile os.Stat err", "filePath", localPath)
			return err
		}

		// 计算文件大小(MB)
		fileSizeMB := float64(fileInfo.Size()) / (1024 * 1024)

		// 计算延迟时间
		// 基础延迟3秒
		baseDelay := 3 * time.Second

		// 额外延迟：每增加1MB增加3秒
		additionalMB := math.Max(0, fileSizeMB-1) // 减去1MB的基础部分
		additionalDelay := time.Duration(math.Ceil(additionalMB)) * 3 * time.Second

		// 总延迟
		delaySeconds := baseDelay + additionalDelay

		log.WithContext(ctx).Infow("AsyncUpdateTencentDocFile File processing delay",
			"filePath", localPath,
			"fileSizeMB", fileSizeMB,
			"delaySeconds", delaySeconds.Seconds())

		time.Sleep(delaySeconds)

		externalSource, err := model.NewQuery[model.TDocExternalSource](ctx).Where("id = ? ", insertId).Find()
		if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
			return err
		}

		// 同步中则更新
		if externalSource != nil && externalSource.State == uint32(model.Syncing) {
			// 更新文档状态
			_, err = model.NewQuery[model.TDocExternalSource](ctx).UpdateBy(
				&model.TDocExternalSource{State: uint32(model.Synced)},
				"id = ? ", insertId)
			if err != nil {
				return err
			}
		}

		return nil
	})

	return nil
}

// AddTencentDocFile 添加腾讯文档数据
/*
  @params ctx context.Context
  @param fileId 腾讯文档id
  @param localPath 本地文件路径

  @return uint64 文档id
  @return bool 是否更新
*/
func AddTencentDocFile(ctx context.Context, tencentDoc *TencentDoc, localPath string, req *aipb.ReqImportTencentDoc, userOpenId string) (uint64, bool, error) {
	fileMD5, err := calculateFileMD5(localPath)
	if err != nil {
		return 0, false, err
	}

	// 优先在数据库中查找，如果存在则更新
	externalSource, err := model.NewQuery[model.TDocExternalSource](ctx).
		Scope(model.WithExternalSourceTenant(req.AdminType, req.AccountId, req.UserId)).
		Where("file_uuid = ?", tencentDoc.FileId).Find()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return 0, false, err
	}

	if externalSource != nil && externalSource.DocID > 0 {
		// 同时在doc中不能删除，如果删除则将外部资源数据删除
		oldDoc, err := model.NewQuery[model.TDoc](ctx).Where("id = ? and  deleted_at is null ", externalSource.DocID).Find()
		if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
			return 0, false, err
		}

		// 说明实际doc中已被删除，需要删除外部数据源
		if externalSource != nil && oldDoc == nil {
			_, err := model.NewQuery[model.TDocExternalSource](ctx).DeleteBy("file_uuid = ? ", tencentDoc.FileId)
			if err != nil {
				return 0, false, err
			}

			externalSource = nil
		}

	}

	// 外部数据源 + doc存在
	if externalSource != nil {

		tencentDoc.CosUrl = externalSource.CosURL
		tencentDoc.DocId = externalSource.DocID

		// 文档有更新，最后更新时间不同
		if !TimePtrEqual(externalSource.FileUpdateDate, UnixToTimePtr(tencentDoc.FileModifyTime)) {

			if _, err = model.NewQuery[model.TDocExternalSource](ctx).UpdateBy(map[string]any{
				"file_md5":         fileMD5,
				"state":            uint32(model.Synced),
				"file_update_date": time.Unix(tencentDoc.FileModifyTime, 0).Format("2006-01-02 15:04:05"),
			}, "file_uuid = ?", tencentDoc.FileId); err != nil {
				return 0, false, err
			}

			return externalSource.ID, true, nil
		} else {
			// 文档无更新，id 返回 0
			// return externalSource.ID, true, nil
			return 0, true, nil
		}
	}

	source, err := prepareTencentDocData(tencentDoc, fileMD5, false)

	source.AdminType = req.AdminType
	source.AccountID = req.AccountId
	source.AccountUserID = req.UserId
	source.State = uint32(model.Syncing)

	// userOpenId, _ := xredis.Default.Get(ctx, fmt.Sprintf("tencent_doc_user_%d", req.UserId)).Result()
	source.TdocOpenID = userOpenId

	if err = model.NewQuery[model.TDocExternalSource](ctx).Create(source); err != nil {
		return 0, false, err
	}

	return source.ID, false, nil
}

// prepareExpertData 准备基础数据
func prepareExpertData(expert Expert) (*model.TDocExternalSource, error) {
	source := &model.TDocExternalSource{
		Type:     uint32(model.ExpertQuery),
		FileUUID: expert.Guid,
		FileName: expert.UserName,
	}

	var err error
	source.FileCreateDate, err = parseTimeWithLocation(expert.CreateTime)
	if err != nil {
		return nil, fmt.Errorf("invalid create time: %v", err)
	}

	source.FileUpdateDate, err = parseTimeWithLocation(expert.UpdateTime)
	if err != nil {
		return nil, fmt.Errorf("invalid update time: %v", err)
	}

	jsonData, err := json.Marshal(expert)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal achievement: %v", err)
	}
	source.ContentJSON = string(jsonData)

	return source, nil
}

func parseTimeWithLocation(timeStr string) (*time.Time, error) {
	loc, err := time.LoadLocation("Asia/Shanghai")

	t, err := time.ParseInLocation("2006-01-02 15:04:05", timeStr, loc)
	if err != nil {
		return nil, err
	}
	return &t, nil
}

func FindTaskEndDate(ctx context.Context) time.Time {
	defaultTime := time.Date(2022, time.January, 1, 0, 0, 0, 0, time.UTC)

	sourceTask, err := model.NewQuery[model.TDocExternalSourceTask](ctx).Where("type = ? ", uint32(model.AchievementQuery)).
		OrderBy("id", true).Find()
	if err != nil {
		return defaultTime
	}

	if sourceTask == nil {
		return defaultTime
	}

	return sourceTask.EndDate
}

// FinishTask 完成任务
/*
  @params ctx context.Context
  @param qType 查询类型
  @param state 状态
  @param userId 用户id
  @param uuid 唯一任务标识
*/
func FinishTask(ctx context.Context, qType model.QueryType, state int, userId uint64, uuid string) error {
	// 任务开始
	if state == model.TaskStateProcessing {
		source := &model.TDocExternalSourceTask{
			UUID:    uuid,
			UserID:  userId,
			Type:    uint32(qType),
			EndDate: time.Now(),
		}

		if err := model.NewQuery[model.TDocExternalSourceTask](ctx).Create(source); err != nil {
			return err
		}
	}

	// 任务结束或者失败
	if state == model.TaskStateCompleted || state == model.TaskStateFailed {
		if _, err := model.NewQuery[model.TDocExternalSourceTask](ctx).UpdateBy(&model.TDocExternalSourceTask{State: uint32(state)},
			"uuid = ? ", uuid); err != nil {
			return err
		}
	}

	return nil
}

// ModifyDocTab ModifyDocTab
type ModifyDocTab struct {
	ID        uint64
	Name      string
	AdminType uint32
	AccountId uint64
	Type      uint32
}

// ModifyDocTab 修改文档标签
func (m *ModifyDocTab) ModifyDocTab(ctx context.Context) error {
	docExternalTab, err := model.NewQuery[model.TDocExternalTab](ctx).Where("type = ? and  admin_type = ? and account_id = ? ",
		m.Type, m.AdminType, m.AccountId).Find()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if docExternalTab != nil && docExternalTab.ID > 0 {
		docExternalTab.Name = m.Name
		if err = model.NewQuery[model.TDocExternalTab](ctx).Where("id = ? ", m.ID).Save(docExternalTab); err != nil {
			return err
		}
	} else {
		docExternalTab = &model.TDocExternalTab{
			Name:      m.Name,
			Type:      m.Type,
			AdminType: int32(m.AdminType),
			AccountID: int64(m.AccountId),
		}

		if err = model.NewQuery[model.TDocExternalTab](ctx).Create(docExternalTab); err != nil {
			return err
		}
	}

	return nil
}

// ModifyInternalDocTab 修改文档标签
func ModifyInternalDocTab(ctx context.Context, name string, adminType uint32, accountId uint64, ty uint32) error {
	t := &ModifyDocTab{}
	t.Name = name
	t.AdminType = adminType
	t.AccountId = accountId
	t.Type = ty

	docExternalTab, err := model.NewQuery[model.TDocExternalTab](ctx).Where("type = ? and  admin_type = ? and account_id = ? ",
		t.Type, t.AdminType, t.AccountId).Find()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if docExternalTab != nil && docExternalTab.ID > 0 {
		// 无需更新
	} else {
		docExternalTab = &model.TDocExternalTab{
			Name:      t.Name,
			Type:      t.Type,
			AdminType: int32(t.AdminType),
			AccountID: int64(t.AccountId),
		}

		if err = model.NewQuery[model.TDocExternalTab](ctx).Create(docExternalTab); err != nil {
			return err
		}
	}

	return nil
}

// DescribeDocTabs 查询文档标签
func (m *ModifyDocTab) DescribeDocTabs(ctx context.Context) ([]*model.TDocExternalTab, error) {
	docExternalTab, err := model.NewQuery[model.TDocExternalTab](ctx).Where(" admin_type = ? and account_id = ? ",
		m.AdminType, m.AccountId).Get()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return docExternalTab, err
	}

	return docExternalTab, nil
}

// PrePareAchievementDoc 预处理成果库知识库数据
func PrePareAchievementDoc(req *aipb.ReqCreateGTBText, assistantIds []uint64, achievement Achievement) *aipb.TextFile {
	// 初始化TextFile并设置基本信息
	refUrl := config.GetStringOr("doc.source.gtbAmdUrl", "https://www.greentechbank.com/greentech/web/achv/achivementDetail/"+achievement.Guid)

	textFile := &aipb.TextFile{
		Name:            achievement.Title,
		ShowContributor: 1,
		DownloadAsRef:   aipb.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_NAME,
		DataSource:      aipb.DocDataSource_DOC_DATA_SOURCE_SQL,
		Reference:       []*aipb.DocReference{{Text: refUrl}},
	}

	// 构建文本内容，只包含非空字段
	var textBuilder strings.Builder
	textBuilder.WriteString("# 成果标题: " + achievement.Title + "\n")

	if strings.TrimSpace(achievement.AuthorizeNo) != "" {
		textBuilder.WriteString("# 成果登记号: " + achievement.AuthorizeNo + "\n")
	}
	if strings.TrimSpace(achievement.GreenName) != "" {
		textBuilder.WriteString("# 绿色分类: " + achievement.GreenName + "\n")
	}
	if strings.TrimSpace(achievement.ApplyIndustry) != "" {
		textBuilder.WriteString("# 应用行业: " + achievement.ApplyIndustry + "\n")
	}
	if strings.TrimSpace(achievement.AchvStage) != "" {
		textBuilder.WriteString("# 应用阶段: " + achievement.AchvStage + "\n")
	}
	if strings.TrimSpace(achievement.Descripe) != "" {
		textBuilder.WriteString("# 应用描述: " + bluemonday.StrictPolicy().
			Sanitize(achievement.Descripe) + "\n")
	}
	if strings.TrimSpace(achievement.AchvDept) != "" {
		textBuilder.WriteString("# 成果单位: " + achievement.AchvDept + "\n")
	}
	if strings.TrimSpace(achievement.CoopWays) != "" {
		textBuilder.WriteString("# 合作方式: " + achievement.CoopWays + "\n")
	}
	if strings.TrimSpace(achievement.Extra4) != "" {
		textBuilder.WriteString("# 知识产权: " + achievement.Extra4 + "\n")
	}
	if strings.TrimSpace(achievement.City) != "" {
		textBuilder.WriteString("# 区域: " + achievement.City + "\n")
	}
	if strings.TrimSpace(achievement.UpdateTime) != "" {
		textBuilder.WriteString("# 更新时间: " + achievement.UpdateTime + "\n")
	}

	textFile.Text = textBuilder.String()

	// 根据AdminType确定操作者类型
	identityType := basepb.IdentityType_IDENTITY_TYPE_TEAM
	if req.AdminType == 1 {
		identityType = basepb.IdentityType_IDENTITY_TYPE_USER
	}

	// 设置创建者信息
	textFile.CreateBy = &aipb.Operator{
		Type:   identityType,
		Id:     req.AccountId,
		UserId: req.UserId,
	}

	// 设置贡献者信息
	contributor := &aipb.Contributor{
		Type: identityType,
		Id:   req.AccountId,
	}
	textFile.Contributor = []*aipb.Contributor{contributor}

	// 设置助手
	states := make([]*aipb.DocAssistantState, len(assistantIds))
	for i, id := range assistantIds {
		states[i] = &aipb.DocAssistantState{
			State:       aipb.DocState_DOC_STATE_DISABLED,
			AssistantId: id,
		}
	}
	textFile.States = states

	return textFile
}

// PrePareExpertDoc 预处理专家库知识库数据
func PrePareExpertDoc(req *aipb.ReqCreateGTBText, assistantIds []uint64, expert Expert) *aipb.TextFile {
	// 初始化TextFile并设置基本信息

	refUrl := config.GetStringOr("doc.source.gtbAmdUrl", "https://www.greentechbank.com/greentech/web/expert/detail/"+expert.Guid)

	textFile := &aipb.TextFile{
		Name:            expert.UserName,
		ShowContributor: 1,
		DownloadAsRef:   aipb.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_NAME,
		DataSource:      aipb.DocDataSource_DOC_DATA_SOURCE_SQL,
		Reference:       []*aipb.DocReference{{Text: refUrl}},
	}

	// 构建文本内容，只包含非空字段
	var textBuilder strings.Builder
	textBuilder.WriteString("# 姓名: " + expert.UserName + "\n")

	if strings.TrimSpace(expert.SubjectName) != "" {
		textBuilder.WriteString("# 学科: " + expert.SubjectName + "\n")
	}
	if strings.TrimSpace(expert.Summary) != "" {
		textBuilder.WriteString("# 简介: " + bluemonday.StrictPolicy().Sanitize(expert.Summary) + "\n")
	}
	if strings.TrimSpace(expert.Education) != "" {
		textBuilder.WriteString("# 学历: " + expert.Education + "\n")
	}
	if strings.TrimSpace(expert.GreenName) != "" {
		textBuilder.WriteString("# 绿色领域分类: " + expert.GreenName + "\n")
	}
	if strings.TrimSpace(expert.Industry) != "" {
		textBuilder.WriteString("# 从事行业: " + expert.Industry + "\n")
	}

	textFile.Text = textBuilder.String()

	// 根据AdminType确定操作者类型
	identityType := basepb.IdentityType_IDENTITY_TYPE_TEAM
	if req.AdminType == 1 {
		identityType = basepb.IdentityType_IDENTITY_TYPE_USER
	}

	// 设置创建者信息
	textFile.CreateBy = &aipb.Operator{
		Type: identityType,
		Id:   req.AccountId,
	}

	// 设置贡献者信息
	contributor := &aipb.Contributor{
		Type: identityType,
		Id:   req.AccountId,
	}
	textFile.Contributor = []*aipb.Contributor{contributor}

	// 设置助手
	states := make([]*aipb.DocAssistantState, len(assistantIds))
	for i, id := range assistantIds {
		states[i] = &aipb.DocAssistantState{
			State:       aipb.DocState_DOC_STATE_DISABLED,
			AssistantId: id,
		}
	}
	textFile.States = states

	return textFile
}

// PrePareTencentDoc 预处理腾讯文档数据
func PrePareTencentDoc(req *aipb.ReqImportTencentDoc, assistantIds []uint64, cosUrl string, fileName string) *aipb.TextFile {
	// 初始化TextFile并设置基本信息
	textFile := &aipb.TextFile{
		Name:            fileName,
		ShowContributor: 1,
		Url:             cosUrl,
		DownloadAsRef:   aipb.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_NAME,
		DataSource:      aipb.DocDataSource_DOC_DATA_SOURCE_TCLOUD_DOCUMENT,
	}

	// 根据AdminType确定操作者类型
	identityType := basepb.IdentityType_IDENTITY_TYPE_TEAM
	if req.AdminType == 1 {
		identityType = basepb.IdentityType_IDENTITY_TYPE_USER
	}

	// 设置创建者信息
	textFile.CreateBy = &aipb.Operator{
		Type:   identityType,
		Id:     req.AccountId,
		UserId: req.UserId,
	}
	textFile.UpdateBy = textFile.CreateBy

	// 设置贡献者信息
	contributor := &aipb.Contributor{
		Type: identityType,
		Id:   req.AccountId,
	}
	textFile.Contributor = []*aipb.Contributor{contributor}

	// 设置助手
	states := make([]*aipb.DocAssistantState, len(assistantIds))
	for i, id := range assistantIds {
		states[i] = &aipb.DocAssistantState{
			State:       aipb.DocState_DOC_STATE_DISABLED,
			AssistantId: id,
		}
	}
	textFile.States = states

	return textFile
}

// DocumentConverter  文档interface
type DocumentConverter interface {
	GetURL() string
	GetTitle() string
	GetID() string
	GetType() string
	GetCreateTime() int64
	GetLastModifyTime() int64
	GetOwnerName() string
	GetCreatorName() string
	GetLastBrowseTime() int64
}

type documentAdapter struct {
	doc *docModel.Document
}

func (d *documentAdapter) GetURL() string           { return d.doc.URL }
func (d *documentAdapter) GetTitle() string         { return d.doc.Title }
func (d *documentAdapter) GetID() string            { return d.doc.ID }
func (d *documentAdapter) GetType() string          { return d.doc.Type }
func (d *documentAdapter) GetCreateTime() int64     { return d.doc.CreateTime }
func (d *documentAdapter) GetLastModifyTime() int64 { return d.doc.LastModifyTime }
func (d *documentAdapter) GetOwnerName() string     { return d.doc.OwnerName }
func (d *documentAdapter) GetCreatorName() string   { return d.doc.CreatorName }
func (d *documentAdapter) GetLastBrowseTime() int64 { return d.doc.LastBrowseTime }

type searchDocumentAdapter struct {
	doc *docModel.SearchDocument
}

func (s *searchDocumentAdapter) GetURL() string           { return s.doc.URL }
func (s *searchDocumentAdapter) GetTitle() string         { return s.doc.Title }
func (s *searchDocumentAdapter) GetID() string            { return s.doc.ID }
func (s *searchDocumentAdapter) GetType() string          { return s.doc.Type }
func (s *searchDocumentAdapter) GetCreateTime() int64     { return s.doc.CreateTime }
func (s *searchDocumentAdapter) GetLastModifyTime() int64 { return s.doc.LastModifyTime }
func (s *searchDocumentAdapter) GetOwnerName() string     { return s.doc.OwnerName }
func (s *searchDocumentAdapter) GetCreatorName() string   { return "" }
func (s *searchDocumentAdapter) GetLastBrowseTime() int64 { return 0 }

func ConvertDocToResponse(doc interface{}) (*aipb.TencentDoc, error) {
	var converter DocumentConverter

	switch d := doc.(type) {
	case *docModel.Document:
		converter = &documentAdapter{doc: d}
	case *docModel.SearchDocument:
		converter = &searchDocumentAdapter{doc: d}
	default:
		return nil, fmt.Errorf("unsupported document type: %T", doc)
	}

	fileCreateTime, err := UnixSecondsToRFC3339(converter.GetCreateTime())
	if err != nil {
		return nil, fmt.Errorf("failed to convert create time: %v", err)
	}

	lastModifyTime, err := UnixSecondsToRFC3339(converter.GetLastModifyTime())
	if err != nil {
		return nil, fmt.Errorf("failed to convert modify time: %v", err)
	}

	responseDoc := &aipb.TencentDoc{
		Url:            converter.GetURL(),
		Title:          converter.GetTitle(),
		FileId:         converter.GetID(),
		FileType:       converter.GetType(),
		FileCreateTime: fileCreateTime,
		FileModifyTime: lastModifyTime,
		FileOwnerName:  converter.GetOwnerName(),
	}

	if creator := converter.GetCreatorName(); creator != "" {
		responseDoc.FileCreateUser = creator
	}

	if browseTime := converter.GetLastBrowseTime(); browseTime > 0 {
		browserTime, err := UnixSecondsToRFC3339(browseTime)
		if err != nil {
			return nil, fmt.Errorf("failed to convert browse time: %v", err)
		}
		responseDoc.FileBrowseTime = browserTime
	}

	return responseDoc, nil
}

// LoadMyDocList 加载我的文档列表
func LoadMyDocList(ctx context.Context, adminType uint32, accountId uint64) ([]string, error) {
	var fileUUIDs []string

	externalSources, err := model.NewQuery[model.TDocExternalSource](ctx).Where(" admin_type = ? and account_id = ?  and type = ?",
		adminType, accountId, model.TencentDoc).Get()
	if err != nil {
		return nil, err
	}

	for _, externalSource := range externalSources {
		if externalSource.FileUUID == "" {
			continue
		}
		fileUUIDs = append(fileUUIDs, externalSource.FileUUID)
	}

	return fileUUIDs, nil
}

// AddExternalSourceUser 添加外部文档关联用户
func AddExternalSourceUser(ctx context.Context, user *docModel.UserInfo, token *docModel.Token, userId uint64) error {
	// if err := xredis.Default.Set(ctx, fmt.Sprintf("tencent_doc_user_%d", userId), user.OpenID, time.Hour*24*29).Err(); err != nil {
	// 	return err
	// }

	docExternalTab, err := model.NewQuery[model.TDocExternalSourceUser](ctx).Where("open_id = ? and create_by = ?", user.OpenID, userId).Find()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if docExternalTab != nil && docExternalTab.ID > 0 {
		if _, err = model.NewQuery[model.TDocExternalSourceUser](ctx).UpdateBy(map[string]any{
			"access_token":  token.AccessToken,
			"refresh_token": token.RefreshToken,
			"nick":          user.Nick,
			"avatar":        user.Avatar,
		}, "id = ?", docExternalTab.ID); err != nil {
			return err
		}
		return nil // 已存在，返回空
	}

	source := &model.TDocExternalSourceUser{
		CreateBy:     userId,
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		OpenID:       user.OpenID,
		UnionID:      user.UnionID,
		Nick:         user.Nick,
		Avatar:       user.Avatar,
		Source:       user.Source,
	}

	if err := model.NewQuery[model.TDocExternalSourceUser](ctx).Create(source); err != nil {
		return err
	}

	return nil
}

// DescribeTencentDocTask 获取腾讯文档任务
func DescribeTencentDocTask(ctx context.Context, taskType uint32, userId uint64) (bool, error) {
	// 查询是否有进行中的任务
	tasks, err := model.NewQuery[model.TDocExternalSourceTask](ctx).Where("type = ? and user_id = ? and state = 1", taskType, userId).Count()
	if err != nil {
		return false, err
	}

	if tasks > 0 {
		return true, nil
	}

	return false, nil
}

// UnixSecondsToRFC3339 将秒级Unix时间戳转换为RFC3339格式字符串
/*
@param timestamp: int64类型的秒级Unix时间戳
@param loc: 可选参数，指定时区，默认为UTC

@return: string RFC3339格式的时间字符串
*/
func UnixSecondsToRFC3339(timestamp int64, loc ...*time.Location) (string, error) {
	// 检查时间戳是否有效
	if timestamp < 0 {
		return "", fmt.Errorf("invalid timestamp: %d", timestamp)
	}

	// 设置默认时区为上海（如果未提供 loc 参数）
	location := time.UTC // 默认 UTC
	if len(loc) > 0 && loc[0] != nil {
		location = loc[0] // 使用传入的时区
	} else {
		// 如果未提供 loc，则尝试使用上海时区
		shanghaiLoc, err := time.LoadLocation("Asia/Shanghai")
		if err == nil {
			location = shanghaiLoc
		}
	}

	var t time.Time
	if timestamp > 1e18 {
		// 纳秒级（虽然函数名是Seconds，但为了完整性也处理）
		t = time.Unix(0, timestamp).In(location)
	} else if timestamp > 1e15 {
		// 微秒级
		t = time.Unix(0, timestamp*1e3).In(location)
	} else if timestamp > 1e12 {
		// 毫秒级
		t = time.Unix(0, timestamp*1e6).In(location)
	} else {
		// 秒级
		t = time.Unix(timestamp, 0).In(location)
	}

	// 格式化为RFC3339
	return t.Format(time.RFC3339), nil
}

// UnixToTimePtr 将秒级Unix时间戳转换为*time.Time指针
// 参数:
//
//	timestamp - 秒级Unix时间戳(int64)
//
// 返回:
//
//	*time.Time - 对应的时间指针
func UnixToTimePtr(timestamp int64) *time.Time {
	t := time.Unix(timestamp, 0)
	return &t
}

func calculateFileMD5(localPath string) (string, error) {
	file, err := os.Open(localPath)
	if err != nil {
		return "", fmt.Errorf("无法打开文件: %v", err)
	}
	defer file.Close()

	// 重置文件指针（防御性编程）
	if _, err = file.Seek(0, 0); err != nil {
		return "", fmt.Errorf("重置文件指针失败: %v", err)
	}

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", fmt.Errorf("计算 MD5 时出错: %v", err)
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// TimePtrEqual 检查两个时间指针是否相等
func TimePtrEqual(t1, t2 *time.Time) bool {
	if t1 == nil && t2 == nil {
		return true
	}
	if t1 == nil || t2 == nil {
		return false
	}
	return t1.Equal(*t2)
}

// TencentDocOauthToken 腾讯文档OAuth Token信息,存在 redis中
type TencentDocOauthToken struct {
	docModel.Token
	// 过期绝对时间
	TokenExpiresAt int64 `json:"token_expires_at"`
	// 刷新令牌过期时间
	RefreshTokenExpiresAt int64 `json:"refresh_token_expires_at"`
	// 用户是否主动取消授权（tanlive 侧取消）
	IsCanceled bool `json:"is_canceled"`
	// 最后一次访问时间
	LastAccessTime int64 `json:"last_access_time"`
}

// AuthState 获取授权状态
func (t *TencentDocOauthToken) AuthState() aipb.ExternalSourceUserAuthState {
	if t.AccessToken == "" {
		return aipb.ExternalSourceUserAuthState_EXTERNAL_SOURCE_USER_AUTH_STATE_UNAUTHORIZED
	}
	if t.IsCanceled {
		return aipb.ExternalSourceUserAuthState_EXTERNAL_SOURCE_USER_AUTH_STATE_CANCELED
	}
	if t.RefreshTokenExpiresAt < time.Now().Unix() {
		return aipb.ExternalSourceUserAuthState_EXTERNAL_SOURCE_USER_AUTH_STATE_EXPIRED
	}
	return aipb.ExternalSourceUserAuthState_EXTERNAL_SOURCE_USER_AUTH_STATE_AUTHORIZED
}

var (
	// ErrTencentDocTokenExpired 腾讯文档Token已经过期
	ErrTencentDocTokenExpired = errors.New("tencent doc token expired")
	// ErrTencentDocTokenNotFound 腾讯文档Token未找到
	ErrTencentDocTokenNotFound = errors.New("tencent doc token not found")
	// ErrTencentDocTokenCanceled 腾讯文档Token已取消授权
	ErrTencentDocTokenCanceled = errors.New("tencent doc token canceled")
)

var (
	// TencentDocTokenExpireDuration 腾讯文档Token过期时间,59天
	TencentDocTokenExpireDuration = config.GetDurationOr("llm.collection.external.tencent_doc.token_expire_duration", time.Hour*24*59)
	// TencentDocTokenRefreshDuration 腾讯文档Token刷新时间,364天
	TencentDocTokenRefreshDuration = config.GetDurationOr("llm.collection.external.tencent_doc.token_refresh_duration", time.Hour*24*364)
)

// TencentDocWrapper 腾讯文档Wrapper
type TencentDocWrapper struct{}

// NewTencentDocWrapper 创建腾讯文档Wrapper
func NewTencentDocWrapper() *TencentDocWrapper {
	return &TencentDocWrapper{}
}

// storeTokenToRedis 存储腾讯文档Token到Redis
// 使用HashMapo结构，以用户ID为key，以OAuth用户ID为field，以Token为value
// 可以指定刷新令牌过期时间偏移量
func (w *TencentDocWrapper) storeTokenToRedis(ctx context.Context, userId uint64, token *docModel.Token, refreshTokenExpiresAtOffset ...time.Duration) error {
	if token == nil || len(token.UserID) == 0 {
		return fmt.Errorf("token or token UserID is empty")
	}
	tokenInfo := &TencentDocOauthToken{
		Token: *token,
		// 设置最后访问时间为当前时间
		LastAccessTime: time.Now().Unix(),
	}
	offset := time.Duration(0)
	if len(refreshTokenExpiresAtOffset) > 0 {
		offset = refreshTokenExpiresAtOffset[0]
	}
	// 编码刷新令牌过期绝对时间，强制偏移30秒提前过期
	tokenInfo.RefreshTokenExpiresAt = time.Now().Add(TencentDocTokenRefreshDuration - 30*time.Second).Add(offset).Unix()
	// 编码过期绝对时间，强制偏移30秒提前过期
	tokenInfo.TokenExpiresAt = time.Now().Add(time.Duration(token.ExpiresIn-30) * time.Second).Unix()
	jsonData, err := json.Marshal(tokenInfo)
	if err != nil {
		return fmt.Errorf("failed to marshal token: %v", err)
	}

	encryptedOpenid := xcrypto.Sha256(token.UserID)
	// 使用HashMap存储，key为tencent_doc_tokens_{userId}，field为OAuth用户ID，value为token信息
	err = xredis.Default.HSet(
		ctx,
		fmt.Sprintf("tencent_doc_tokens_%d", userId),
		encryptedOpenid,
		jsonData,
	).Err()
	if err != nil {
		return fmt.Errorf("failed to store token to redis: %v", err)
	}

	err = xredis.Default.Expire(
		ctx,
		fmt.Sprintf("tencent_doc_tokens_%d", userId),
		TencentDocTokenRefreshDuration, // 使用刷新令牌过期时间（时间较长，1 年）
	).Err()
	if err != nil {
		log.WithContext(ctx).Warnw("Failed to set expire time for token", "userId", userId, "err", err)
	}

	return nil
}

// getTokenFromRedis 从Redis中获取特定OAuth用户ID的腾讯文档Token
// 没有 token会返回 ErrTCloudDocTokenNotFound
func (w *TencentDocWrapper) getTokenFromRedis(ctx context.Context, userId uint64, encryptedOpenid string) (TencentDocOauthToken, error) {
	var token TencentDocOauthToken

	if len(encryptedOpenid) == 0 {
		return token, fmt.Errorf("encryptedOpenid is empty")
	}

	tokenStr, err := xredis.Default.HGet(
		ctx,
		fmt.Sprintf("tencent_doc_tokens_%d", userId),
		encryptedOpenid,
	).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return token, ErrTencentDocTokenNotFound
		}
		return token, err
	}

	if tokenStr == "" {
		return token, ErrTencentDocTokenNotFound
	}

	if err := json.Unmarshal([]byte(tokenStr), &token); err != nil {
		return token, fmt.Errorf("failed to unmarshal token: %v", err)
	}

	return token, nil
}

// ListUserTokens 获取用户所有的Token(包括取消授权的)
func (w *TencentDocWrapper) ListUserTokens(ctx context.Context, userId uint64) (map[string]TencentDocOauthToken, error) {
	result := make(map[string]TencentDocOauthToken)

	// 获取HashMap中所有的field-value对
	tokensMap, err := xredis.Default.HGetAll(
		ctx,
		fmt.Sprintf("tencent_doc_tokens_%d", userId),
	).Result()
	if err != nil {
		return result, err
	}

	// 如果没有任何token
	if len(tokensMap) == 0 {
		return result, nil
	}

	// 解析每个token
	for _, tokenStr := range tokensMap {
		var tokenInfo TencentDocOauthToken
		if err := json.Unmarshal([]byte(tokenStr), &tokenInfo); err != nil {
			log.WithContext(ctx).Warnw("Failed to unmarshal token", "userId", userId, "err", err)
			continue
		}
		result[tokenInfo.UserID] = tokenInfo

		// 只返回未取消授权的token
		// if !tokenInfo.IsCanceled {
		// 	result[oauthUserId] = tokenInfo
		// }
	}

	return result, nil
}

// RemoveToken 从Redis中移除指定的Token
func (w *TencentDocWrapper) RemoveToken(ctx context.Context, userId uint64, encryptedOpenid string) error {
	if len(encryptedOpenid) == 0 {
		return fmt.Errorf("encryptedOpenid is empty")
	}

	_, err := xredis.Default.HDel(
		ctx,
		fmt.Sprintf("tencent_doc_tokens_%d", userId),
		encryptedOpenid,
	).Result()

	return err
}

// CancelToken 将Token标记为已取消授权
func (w *TencentDocWrapper) CancelToken(ctx context.Context, userId uint64, encryptedOpenid string) error {
	if len(encryptedOpenid) == 0 {
		return fmt.Errorf("encryptedOpenid is empty")
	}

	token, err := w.getTokenFromRedis(ctx, userId, encryptedOpenid)
	if err != nil {
		// 没有 token直接返回
		if errors.Is(err, ErrTencentDocTokenNotFound) {
			return nil
		}
		return err
	}
	token.IsCanceled = true

	jsonData, err := json.Marshal(token)
	if err != nil {
		return fmt.Errorf("failed to marshal token: %v", err)
	}

	// 更新标记为已取消的token
	err = xredis.Default.HSet(
		ctx,
		fmt.Sprintf("tencent_doc_tokens_%d", userId),
		encryptedOpenid,
		jsonData,
	).Err()

	return err
}

// StoreTokenByCode 使用授权码更新Token
func (w *TencentDocWrapper) StoreTokenByCode(ctx context.Context, code, path string, userId uint64) (*docModel.Token, error) {
	if code == "" {
		return nil, fmt.Errorf("授权码不能为空")
	}

	client, err := w.GetClient(ctx, path)
	if err != nil {
		return nil, err
	}

	token, err := client.ExchangeToken(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange token: %v", err)
	}

	err = w.storeTokenToRedis(ctx, userId, &token.Token)
	if err != nil {
		return nil, err
	}
	return &token.Token, nil
}

// GetToken 获取指定OAuth用户ID的腾讯文档Token
// 如果Token已过期，返回 ErrTencentDocTokenExpired
// 如果Token已取消授权，返回 ErrTencentDocTokenCanceled
// 如果Token不存在，返回 ErrTencentDocTokenNotFound
// 如果Token存在且有效，返回 Token
func (w *TencentDocWrapper) GetToken(ctx context.Context, userId uint64, openid string, encrypted ...bool) (*docModel.Token, error) {
	encryptedOpenid := ""
	if len(encrypted) > 0 && encrypted[0] {
		encryptedOpenid = openid
	} else {
		encryptedOpenid = xcrypto.Sha256(openid)
	}
	oldToken, err := w.getTokenFromRedis(ctx, userId, encryptedOpenid)
	if err != nil {
		return nil, err
	}
	// 延长Token的存储生命周期
	defer func() {
		xredis.Default.Expire(
			ctx,
			fmt.Sprintf("tencent_doc_tokens_%d", userId),
			TencentDocTokenRefreshDuration,
		).Err()
	}()

	if oldToken.IsCanceled {
		return &oldToken.Token, ErrTencentDocTokenCanceled
	}

	// 从Redis中获取到了Token
	if len(oldToken.AccessToken) > 0 {
		// 检查是否已经过期
		if time.Now().Unix() > oldToken.TokenExpiresAt {
			// 检查刷新令牌是否过期
			if time.Now().Unix() > oldToken.RefreshTokenExpiresAt {
				return &oldToken.Token, ErrTencentDocTokenExpired
			}
			// 刷新Token
			newToken, err := w.RefreshToken(ctx, userId, &oldToken)
			if err != nil {
				return nil, err
			}
			return newToken, nil
		}

		// 未过期，直接返回
		return &oldToken.Token, nil
	}

	return nil, ErrTencentDocTokenNotFound
}

// UpdateTokenPriority 更新Token的最后访问时间
// 不再使用ZSet，而是直接更新Token结构中的LastAccessTime字段
func (w *TencentDocWrapper) UpdateTokenPriority(ctx context.Context, userId uint64, encryptedOpenid string) error {
	if len(encryptedOpenid) == 0 {
		return fmt.Errorf("encryptedOpenid is empty")
	}

	// 获取当前Token
	token, err := w.getTokenFromRedis(ctx, userId, encryptedOpenid)
	if err != nil {
		return fmt.Errorf("获取Token失败: %v", err)
	}

	// 更新最后访问时间
	token.LastAccessTime = time.Now().Unix()

	// 序列化并存储回Redis
	jsonData, err := json.Marshal(token)
	if err != nil {
		return fmt.Errorf("序列化Token失败: %v", err)
	}

	// 更新Redis中的Token
	err = xredis.Default.HSet(
		ctx,
		fmt.Sprintf("tencent_doc_tokens_%d", userId),
		encryptedOpenid,
		jsonData,
	).Err()
	if err != nil {
		return fmt.Errorf("更新Token访问时间失败: %v", err)
	}

	log.WithContext(ctx).Debugw("Token访问时间已更新", "userId", userId, "encryptedOpenid", encryptedOpenid)
	return nil
}

// RefreshToken 刷新Token
func (w *TencentDocWrapper) RefreshToken(ctx context.Context, userId uint64, existingToken *TencentDocOauthToken) (*docModel.Token, error) {
	// 如果Token为空，则返回空Token
	if existingToken == nil || len(existingToken.RefreshToken) == 0 {
		return nil, ErrTencentDocTokenNotFound
	}

	// 检查刷新令牌是否已过期
	if time.Now().Unix() > existingToken.RefreshTokenExpiresAt {
		return nil, ErrTencentDocTokenExpired
	}

	client, err := w.GetClient(ctx)
	if err != nil {
		return nil, err
	}

	// 尝试刷新Token
	newToken, err := client.RefreshToken(ctx, existingToken.RefreshToken)
	if err != nil {
		log.WithContext(ctx).Errorw("RefreshToken failed", "err", err, "userId", userId, "token", existingToken)
		return nil, err
	}

	// 刷新成功，存储新Token
	err = w.storeTokenToRedis(ctx, userId, &newToken.Token)
	if err != nil {
		return nil, err
	}

	return &newToken.Token, nil
}

// GetClient 获取腾讯文档客户端（不带Token）
func (w *TencentDocWrapper) GetClient(ctx context.Context, path ...string) (*docClient.Client, error) {
	// 判断是否允许访问该路径
	pathStr := ""
	if len(path) > 0 && xstrings.In(path[0], config.GetStringSlice("tencent.doc.allow_path")) {
		pathStr = path[0]
	}

	// 创建客户端
	client := docClient.NewClient(
		docConfig.WithClientID(config.GetString("tencent.doc.client_id")),
		docConfig.WithClientSecret(config.GetString("tencent.doc.client_secret")),
		docConfig.WithRedirectURI(config.GetString("tencent.doc.redirect_uri")+pathStr),
		docConfig.WithTimeout(time.Second*config.GetDurationOr("tencent.doc.timeout", 300)),
		docConfig.WithRandomState(docUtil.GenerateRandomString(20)),
		docConfig.WithHttpTransport(log.DumpHttpTransport("tencent-doc", nil, true)),
	)

	return client, nil
}

// GetTokendClient 获取带Token的腾讯文档客户端（使用指定的OAuth用户ID）
func (w *TencentDocWrapper) GetTokendClient(ctx context.Context, userId uint64, encryptedOpenid string, path ...string) (*docClient.Client, error) {
	client, err := w.GetClient(ctx, path...)
	if err != nil {
		return nil, err
	}

	token, err := w.GetToken(ctx, userId, encryptedOpenid, true)
	if err != nil {
		return nil, err
	}

	return client.WithToken(token), nil
}

// MigrateOldTokens 迁移旧格式的Token到新格式
func (w *TencentDocWrapper) MigrateOldTokens(ctx context.Context, userId uint64) error {
	// 获取旧格式的Token
	tokenStr, err := xredis.Default.Get(ctx, fmt.Sprintf("tencent_doc_token_%d", userId)).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil // 没有旧Token，无需迁移
		}
		return err
	}

	if tokenStr == "" {
		return nil // 没有旧Token，无需迁移
	}

	var tokenResp docModel.TokenResponse
	if err := json.Unmarshal([]byte(tokenStr), &tokenResp); err != nil {
		return fmt.Errorf("failed to unmarshal old token: %v", err)
	}

	expireTime, err := xredis.Default.TTL(ctx, fmt.Sprintf("tencent_doc_token_%d", userId)).Result()
	if err != nil {
		return err
	}
	// 这里重新计算过期时间,偏移10秒
	tokenResp.Token.ExpiresIn = int(expireTime.Seconds() - 10)
	// 刚存储的Token，过期时间是 29 天，此时剩余 expireTime，所以偏移量是 （29 天 - expireTime）
	refreshTokenExpiresAtOffset := 0 - time.Duration(29*24*60*60-expireTime.Seconds())*time.Second
	// 存储到新格式
	err = w.storeTokenToRedis(ctx, userId, &tokenResp.Token, refreshTokenExpiresAtOffset)
	if err != nil {
		return err
	}

	// 先不删除旧格式的Token
	// _, err = xredis.Default.Del(ctx, fmt.Sprintf("tencent_doc_token_%d", userId)).Result()
	// if err != nil {
	// log.WithContext(ctx).Warnw("Failed to delete old token", "userId", userId, "err", err)
	// }

	// 存储到 t_doc_external_source_user 表
	err = model.NewQuery[model.TDocExternalSourceUser](ctx).Where("open_id = ?", tokenResp.Token.UserID).
		DB().UpdateColumn("create_by", userId).Error
	if err != nil {
		return err
	}

	// 存储到 t_doc_external_source 表的 userid 字段
	err = model.NewQuery[model.TDocExternalSource](ctx).Where("tdoc_open_id = ?", tokenResp.Token.UserID).
		DB().UpdateColumn("account_user_id", userId).Error
	if err != nil {
		return err
	}

	return nil
}

// MigrateAllOldTokens 迁移所有旧格式的Token到新格式
func (w *TencentDocWrapper) MigrateAllOldTokens(ctx context.Context) error {
	keys, err := xredis.Default.Keys(ctx, "tencent_doc_token_*").Result()
	if err != nil {
		return err
	}

	for _, key := range keys {
		userId, err := strconv.ParseUint(strings.TrimPrefix(key, "tencent_doc_token_"), 10, 64)
		if err != nil {
			return err
		}
		err = w.MigrateOldTokens(ctx, userId)
		if err != nil {
			return err
		}
	}

	return nil
}

// ListExternalSourceUser 查询腾讯文档外部用户列表
func ListExternalSourceUser(ctx context.Context, userId uint64) ([]*model.TDocExternalSourceUser, error) {
	users, err := model.NewQuery[model.TDocExternalSourceUser](ctx).Where("create_by = ?", userId).Get()
	if err != nil {
		return nil, err
	}

	return users, nil
}

const (
	// https://docs.qq.com/open/document/app/openapi/v2/file/filter/filter.html
	// 37019 表示Token已过期
	CodeTencentDocApiErrTokenExpired = 37019
)

// 查询哪些腾讯文档账号授权已失效
type authKey struct {
	UserID uint64
	OpenID string
}

type ReimportTencentDocLogic struct {
	ctx    context.Context
	docIds []uint64

	// 可用的腾讯文档Token
	tokensCanUse map[authKey]*docModel.Token
	// 腾讯文档文件ID -> 腾讯文档Token
	doc2Token map[string]*docModel.Token
}

// NewReimportTencentDocLogic 创建重新导入腾讯文档逻辑
func NewReimportTencentDocLogic(ctx context.Context, docIds []uint64) *ReimportTencentDocLogic {
	return &ReimportTencentDocLogic{
		ctx:          ctx,
		docIds:       docIds,
		doc2Token:    make(map[string]*docModel.Token),
		tokensCanUse: make(map[authKey]*docModel.Token),
	}
}

// PreCheck 预检查
// 1. 哪些知识绑定的腾讯文档账号授权已失效
func (l *ReimportTencentDocLogic) PreCheck() (map[uint64]*aipb.ExternalSourceUser, map[string]*docModel.Token, error) {
	docs, err := model.NewQuery[model.TDocExternalSource](l.ctx).Where("doc_id in (?)", l.docIds).Get()
	if err != nil {
		return nil, nil, err
	}
	if len(docs) == 0 {
		return nil, nil, nil
	}
	authInvalidMap := make(map[authKey]*aipb.ExternalSourceUser)
	unqiueAuth := make(map[authKey]struct{})
	for _, doc := range docs {
		unqiueAuth[authKey{UserID: doc.AccountUserID, OpenID: doc.TdocOpenID}] = struct{}{}
	}

	keys := make([][]any, 0)
	for authKey := range unqiueAuth {
		token, err := NewTencentDocWrapper().GetToken(l.ctx, authKey.UserID, authKey.OpenID)
		if err != nil {
			if errors.Is(err, ErrTencentDocTokenNotFound) {
				continue
			}
			if errors.Is(err, ErrTencentDocTokenExpired) {
				authInvalidMap[authKey] = &aipb.ExternalSourceUser{
					UserId:    token.UserID,
					AuthState: aipb.ExternalSourceUserAuthState_EXTERNAL_SOURCE_USER_AUTH_STATE_EXPIRED,
				}
				keys = append(keys, []any{authKey.UserID, authKey.OpenID})
				continue
			}
			if errors.Is(err, ErrTencentDocTokenCanceled) {
				authInvalidMap[authKey] = &aipb.ExternalSourceUser{
					UserId:    token.UserID,
					AuthState: aipb.ExternalSourceUserAuthState_EXTERNAL_SOURCE_USER_AUTH_STATE_CANCELED,
				}
				keys = append(keys, []any{authKey.UserID, authKey.OpenID})
				continue
			}
			return nil, nil, err
		} else {
			l.tokensCanUse[authKey] = token
		}
	}

	// 获取失效token 的用户信息
	ret := make(map[uint64]*aipb.ExternalSourceUser)
	if len(authInvalidMap) != 0 {
		userInfos, err := model.NewQuery[model.TDocExternalSourceUser](l.ctx).Where("(create_by, open_id) in (?)", keys).Get()
		if err != nil {
			return nil, nil, err
		}

		for _, userInfo := range userInfos {
			v := authInvalidMap[authKey{UserID: userInfo.CreateBy, OpenID: userInfo.OpenID}]
			if v != nil {
				v.Nickname = userInfo.Nick
				v.AuthSource = userInfo.Source
				v.Avatar = userInfo.Avatar
			}
		}
	}

	for _, doc := range docs {
		if auth, ok := authInvalidMap[authKey{UserID: doc.AccountUserID, OpenID: doc.TdocOpenID}]; ok {
			ret[doc.DocID] = auth
		} else {
			l.doc2Token[doc.FileUUID] = l.tokensCanUse[authKey{UserID: doc.AccountUserID, OpenID: doc.TdocOpenID}]
		}
	}

	return ret, l.doc2Token, nil
}

var ErrTencentDocFolderNotFound = errors.New("文件夹不存在")

// GetTencentDocFolderFidTimeout 获取腾讯文档文件夹的fid, 如果获取不到，则等待3秒后重试，最多等待timeout时间
func GetTencentDocFolderFidTimeout(ctx context.Context, client *docClient.Client, folderName string, timeout time.Duration) (string, error) {
	timeOut := time.NewTimer(timeout)
	for {
		select {
		case <-timeOut.C:
			return "", ErrTencentDocFolderNotFound
		default:
			folderFid, err := GetTencentDocFolderFid(ctx, client, TencentDocWebClipPath)
			if err != nil {
				if !errors.Is(err, ErrTencentDocFolderNotFound) {
					return "", err
				}
				time.Sleep(time.Second * 3)
			} else {
				return folderFid, nil
			}
		}
	}
}

// GetTencentDocFolderFid 获取腾讯文档文件夹的fid
// folderName 文件夹名称, linux 路径格式。例如: /我的应用文件/网页剪存
// 注意: 该接口是递归的进行目录深度遍历，看能否找到该文件夹，性能较低，请慎用
func GetTencentDocFolderFid(ctx context.Context, client *docClient.Client, folderName string) (string, error) {
	// 递归的进行目录深度遍历，看能否找到该文件夹
	folderName = strings.TrimPrefix(folderName, "/")
	folderName = strings.TrimSuffix(folderName, "/")
	paths := strings.Split(folderName, "/")
	folderFid := ""
	for _, path := range paths {
		offset := 0
	LOOP:
		for {
			param := &docModel.SearchParams{
				SortType:   "create",
				SearchKey:  path,
				SearchType: "title",
				ResultType: "folder",
				Offset:     offset,
				Size:       50,
				FolderID:   folderFid,
			}
			folders, err := client.SearchDocuments(ctx, param)
			if err != nil {
				return "", fmt.Errorf("search documents: %w", err)
			}
			if len(folders.Data.List) == 0 {
				return "", ErrTencentDocFolderNotFound
			}
			for _, folder := range folders.Data.List {
				if folder.Title == path && folder.Type == "folder" {
					folderFid = folder.ID
					break LOOP
				}
			}
			if !folders.Data.HasMore {
				return "", ErrTencentDocFolderNotFound
			}
			offset = folders.Data.Next
		}
	}
	return folderFid, nil
}

// TencentDocWebClipPath 腾讯文档默认的网页剪存目录
const TencentDocWebClipPath = "/我的应用文件/网页剪存"

// ListTencentDocWebClips 获取腾讯文档网页剪存列表,返回 fileId 列表
func ListTencentDocWebClips(ctx context.Context, client *docClient.Client, afterTime time.Time) ([]*docModel.Document, error) {
	// 接口有缓存，最多尝试 30 秒
	folderFid, err := GetTencentDocFolderFidTimeout(ctx, client, TencentDocWebClipPath, time.Second*30)
	if err != nil {
		return nil, err
	}
	start := 0
	files := make([]*docModel.Document, 0)
	for {
		param := &docModel.ListParams{
			FolderID: folderFid,
			ListType: "folder",
			FileType: "doc",
			SortType: "modify",
			Start:    start,
			Limit:    20,
			// IsOwner:  2,
		}
		clips, err := client.ListDocuments(ctx, param)
		if err != nil {
			return nil, fmt.Errorf("list documents: %w", err)
		}
		for _, clip := range clips.Data.List {
			if clip.CreateTime >= afterTime.Unix() {
				files = append(files, clip)
			}
			// 如果遍历到第一个创建时间和更新时间小于 afterTime 的文件，则停止遍历
			if clip.CreateTime < afterTime.Unix() && clip.LastModifyTime < afterTime.Unix() {
				return files, nil
			}
		}
		if clips.Data.Next == 0 || len(clips.Data.List) == 0 {
			break
		}
		start = clips.Data.Next
	}
	return files, nil
}

const (
	tencentDocPathLabelKeyZh = "路径"
	tencentDocPathLabelKeyEn = "Path"
)

const (
	tencentDocPathLabelKeyNextKey = "data_source_state"
)

// GetOrCreateTencentDocPathLable 获取或创建腾讯文档路径标签
// 如果标签不存在，则创建标签
// 如果标签存在，则返回标签ID
func GetOrCreateTencentDocPathLable(ctx context.Context, labelTenantId uint64, lg string) (uint64, error) {
	key := tencentDocPathLabelKeyZh
	if lg == "en" {
		key = tencentDocPathLabelKeyEn
	}
	label, err := model.NewQuery[model.TCustomLabel](ctx).Where("tenant_id = ? and object_type = ? and type = ? and key = ?",
		labelTenantId, aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE, aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, key).
		Find()
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, err
	}
	if err == gorm.ErrRecordNotFound || label == nil || label.ID == 0 {
		label = &model.TCustomLabel{
			TenantID:   labelTenantId,
			ObjectType: aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE,
			Type:       aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT,
			Key:        tencentDocPathLabelKeyZh,
			NextLabel:  tencentDocPathLabelKeyNextKey,
		}
		err = model.NewQuery[model.TCustomLabel](ctx).Create(label)
		if err != nil {
			return 0, err
		}
	}
	return label.ID, nil
}

// UpdateTencentDocPathLabelValue 更新腾讯文档路径标签值
// 如果标签不存在，则创建标签
// 如果标签存在，则更新标签值
func UpdateTencentDocPathLabelValue(ctx context.Context, labelTenantId uint64, lg string, docId uint64, value string) error {
	labelId, err := GetOrCreateTencentDocPathLable(ctx, labelTenantId, lg)
	if err != nil {
		return err
	}
	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		return ReplaceObjectLabels(tx, docId, labelTenantId, aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE, []*model.TObjectLabel{
			{
				LabelID:     labelId,
				LabelType:   aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT,
				ObjectType:  aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE,
				StringValue: value,
			},
		})
	})
	if err != nil {
		return err
	}
	return nil
}

// RefreshAllTencentDocUserInfo 刷新所有有效腾讯文档token对应的用户信息
func RefreshAllTencentDocUserInfo(ctx context.Context) error {
	log.WithContext(ctx).Infow("开始刷新腾讯文档用户信息")

	// 批处理参数
	batchSize := 50 // 每批处理的用户数量
	page := 1       // 当前页码

	// 统计信息
	var total, success, failed, skipped int

	// 创建腾讯文档客户端
	docClient, err := NewTencentDocWrapper().GetClient(ctx)
	if err != nil {
		log.WithContext(ctx).Errorw("获取腾讯文档客户端失败", "error", err)
		return err
	}

	for {
		// 分页查询用户
		users, err := model.NewQuery[model.TDocExternalSourceUser](ctx).
			Limit(batchSize).
			Offset((page-1)*batchSize).
			OrderBy("id", false).
			Get()
		if err != nil {
			log.WithContext(ctx).Errorw("查询腾讯文档用户列表失败", "page", page, "error", err)
			return err
		}

		// 如果没有更多用户，退出循环
		if len(users) == 0 {
			break
		}

		log.WithContext(ctx).Infow("开始处理用户批次", "page", page, "batch_size", len(users))
		total += len(users)

		// 处理当前批次的用户
		batchSuccess, batchFailed, batchSkipped := processUserBatch(ctx, docClient, users)
		success += batchSuccess
		failed += batchFailed
		skipped += batchSkipped

		// 下一页
		page++

		// 批次之间添加短暂延迟，避免请求过于频繁
		time.Sleep(time.Second * 2)
	}

	if total == 0 {
		log.WithContext(ctx).Infow("没有腾讯文档用户需要刷新")
		return nil
	}

	log.WithContext(ctx).Infow("刷新腾讯文档用户信息完成",
		"total", total,
		"success", success,
		"failed", failed,
		"skipped", skipped)

	return nil
}

// processUserBatch 处理一批用户的信息刷新
func processUserBatch(ctx context.Context, docClient *docClient.Client, users []*model.TDocExternalSourceUser) (success, failed, skipped int) {
	for _, user := range users {
		// 获取用户token
		token, err := NewTencentDocWrapper().GetToken(ctx, user.CreateBy, user.OpenID)
		// 如果token不存在或已过期或已取消授权，则跳过
		if err != nil {
			if errors.Is(err, ErrTencentDocTokenNotFound) ||
				errors.Is(err, ErrTencentDocTokenExpired) ||
				errors.Is(err, ErrTencentDocTokenCanceled) {
				log.WithContext(ctx).Infow("用户token无效，跳过刷新",
					"userId", user.CreateBy,
					"openId", user.OpenID,
					"reason", err.Error())
				skipped++
				continue
			}
			log.WithContext(ctx).Errorw("获取用户token失败",
				"userId", user.CreateBy,
				"openId", user.OpenID,
				"error", err)
			failed++
			continue
		}

		// 获取用户信息
		userInfo, err := docClient.WithToken(token).GetUserInfo(ctx)
		if err != nil {
			log.WithContext(ctx).Errorw("获取用户信息失败",
				"userId", user.CreateBy,
				"openId", user.OpenID,
				"error", err)
			failed++
			continue
		}

		// 更新用户信息
		_, err = model.NewQuery[model.TDocExternalSourceUser](ctx).UpdateBy(map[string]any{
			"nick":        userInfo.Nick,
			"avatar":      userInfo.Avatar,
			"update_date": time.Now(),
		}, "id = ?", user.ID)
		if err != nil {
			log.WithContext(ctx).Errorw("更新用户信息失败",
				"userId", user.CreateBy,
				"openId", user.OpenID,
				"error", err)
			failed++
			continue
		}

		success++

		// 避免请求过于频繁
		time.Sleep(time.Millisecond * 200)
	}

	log.WithContext(ctx).Infow("批次处理完成",
		"total", len(users),
		"success", success,
		"failed", failed,
		"skipped", skipped)

	return success, failed, skipped
}
