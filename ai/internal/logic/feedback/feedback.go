package feedback

import (
	"context"
	"errors"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

type feedbackLogic struct {
}

func (l *feedbackLogic) queryAnswer(ctx context.Context, answerID uint64) (*model.TChatMessage, error) {
	message, err := model.NewQuery[model.TChatMessage](ctx).With("Chat").FindByKey(answerID)
	if err != nil {
		return nil, fmt.Errorf("query answer: %w", err)
	}
	if message.Type == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER) {
		return nil, xerrors.ValidationError("the chat message is not a answer")
	}
	return message, nil
}

func (l *feedbackLogic) queryFeedback(ctx context.Context, feedbackID uint64) (*model.TFeedback, error) {
	feedback, err := model.NewQuery[model.TFeedback](ctx).
		FindByKey(feedbackID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("query feedback by key: %w", err)
	}
	return feedback, nil
}

func (l *feedbackLogic) queryFeedbackByAnswerID(ctx context.Context, answerID uint64) (*model.TFeedback, error) {
	feedback, err := model.NewQuery[model.TFeedback](ctx).
		FindBy("answer_id = ?", answerID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("query feedback by answer_id: %w", err)
	}
	return feedback, nil
}
