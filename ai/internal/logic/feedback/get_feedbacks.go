package feedback

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"slices"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var defaultFeedbackOrderBy = map[string]bool{
	"id":               true,
	"create_date":      true,
	"update_date":      true,
	"user_feedback_at": true,
	"op_feedback_at":   true,
	"mgmt_feedback_at": true,
	"handled_at":       true,
}

// GetFeedbacksLogic 查询用户反馈逻辑
type GetFeedbacksLogic struct {
}

// Get ...
func (l *GetFeedbacksLogic) Get(ctx context.Context, req *aipb.ReqGetFeedbacks) ([]*model.TFeedback, error) {
	var (
		feedbacks []*model.TFeedback
		err       error
	)

	query := model.NewQuery[model.TFeedback](ctx).Wheres(func(db *gorm.DB) {
		l.applyFilter(db, req.Filter)
	})
	//query.Scope(model.FeedbacksWithAssistant)

	orderBy := filterOrderBy(req.OrderBy, "feedback.orderBy", defaultFeedbackOrderBy)
	applyOrderBy(query, orderBy)
	l.applyRelations(query.DB(), req.Relation)

	if req.Page == nil {
		feedbacks, err = query.Get()
	} else {
		paginator := xorm.NewPaginator(req.Page.Offset, req.Page.Limit)
		feedbacks, err = query.Paging(paginator)
	}
	if err != nil {
		return nil, fmt.Errorf("query t_feedback: %w", err)
	}

	return feedbacks, nil
}

// Count 计数
func (l *GetFeedbacksLogic) Count(ctx context.Context, req *aipb.ReqGetFeedbacks) (uint32, error) {
	query := model.NewQuery[model.TFeedback](ctx).Wheres(func(db *gorm.DB) {
		l.applyFilter(db, req.Filter)
	})

	totalCount, err := query.Count()
	if err != nil {
		return 0, fmt.Errorf("count t_feedback: %w", err)
	}
	return totalCount, nil
}

func (l *GetFeedbacksLogic) applyFilter(db *gorm.DB, filter *aipb.ReqGetFeedbacks_Filter) {
	if filter == nil {
		return
	}

	if len(filter.FeedbackId) > 0 {
		db.Where("id in (?)", filter.FeedbackId)
	}

	if len(filter.AssistantIds) > 0 {
		db.Where("assistant_id in (?)", filter.AssistantIds)
	}

	if len(filter.RegionCodes) > 0 {
		db.Where("region_code IN (?)", filter.RegionCodes)
	}

	if len(filter.CreateBy) > 0 {
		db.Where(clause.Or(
			clause.Expr{
				SQL:  "create_identity->'$.identity_type' = ? AND create_identity->'$.identity_id' IN (?)",
				Vars: []any{basepb.IdentityType_IDENTITY_TYPE_USER, filter.CreateBy},
			},
			clause.Expr{
				SQL:  "create_identity->'$.identity_type' = ? AND create_identity->'$.extra_id' IN (?)",
				Vars: []any{basepb.IdentityType_IDENTITY_TYPE_TEAM, filter.CreateBy},
			},
		))
	}

	if filter.CreateDateRange != nil {
		if filter.CreateDateRange.Start != nil {
			db.Where("create_date >= ?", filter.CreateDateRange.Start.AsTime().Local())
		}
		if filter.CreateDateRange.End != nil {
			db.Where("create_date <= ?", filter.CreateDateRange.End.AsTime().Local())
		}
	}

	switch filter.ShowScene {
	// 在控制台中仅展示有用户反馈或者运营反馈的数据
	case aipb.ReqGetFeedbacks_SCENE_IN_CONSOLE:
		db.Where("(has_user_feedback = ? or has_op_feedback = ?)", 1, 1)
	}
}

func (l *GetFeedbacksLogic) applyRelations(db *gorm.DB, relation *aipb.ReqGetFeedbacks_Relation) {
	if relation == nil {
		return
	}

	if relation.References {
		db.Preload("References")
	}

	if relation.ExpectedDocs {
		db.Preload("ExpectedDocs", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", aipb.FeedbackDocType_FEEDBACK_DOC_TYPE_OP)
		})
	}

	if relation.ExpectedMgmtDocs {
		db.Preload("ExpectedMgmtDocs", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", aipb.FeedbackDocType_FEEDBACK_DOC_TYPE_MGMT)
		})
	}

	if relation.OriginalQuestion {
		db.Preload("OriginalQuestion")
	}

	if relation.OriginalAnswer {
		db.Preload("OriginalAnswer")
	}
}

// ReadFeedbackLogic 已读用户反馈逻辑
type ReadFeedbackLogic struct {
}

// Check 检查是否可以标记已读
func (l *ReadFeedbackLogic) Check(ctx context.Context, feedbackID uint64) (*model.TFeedback, error) {
	feedback, err := findFeedbackByKey(ctx, feedbackID)
	if err != nil {
		return nil, err
	}
	if feedback == nil {
		return nil, xerrors.NotFoundError("feedback not found")
	}
	if feedback.State != aipb.FeedbackState_FEEDBACK_STATE_UNREAD {
		return nil, xerrors.NewCode(errorspb.AiError_AiFeedbackAlreadyRead)
	}
	return feedback, nil
}

// Read 标记已读（已废弃）
func (l *ReadFeedbackLogic) Read(ctx context.Context, feedback *model.TFeedback,
	readBy uint64, operatorType int32) error {
	now := time.Now()
	columns := map[string]any{
		"state": aipb.FeedbackState_FEEDBACK_STATE_READ,
	}
	switch operatorType {
	case model.FeedbackLogOperatorTypePortal:
		columns["portal_read_by"] = readBy
		columns["portal_read_at"] = now
	case model.FeedbackLogOperatorTypeMgmt:
		columns["mgmt_read_by"] = readBy
		columns["mgmt_read_at"] = now
	}
	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		rowsAffected, err := model.NewQuery[model.TFeedback](ctx).UpdateBy(columns, "id = ? and state = ?",
			feedback.ID, aipb.FeedbackState_FEEDBACK_STATE_UNREAD)
		if err != nil {
			return fmt.Errorf("read feedback: %w", err)
		}

		// 日志
		if rowsAffected > 0 {
			feedbackLog := &model.TFeedbackLog{
				FeedbackID:     feedback.ID,
				FeedbackUserID: feedback.CreateBy,
				Action:         aipb.FeedbackAction_FEEDBACK_ACTION_READ,
				CreateBy:       readBy,
				CreateDate:     now,
				AssistantID:    feedback.AssistantID,
				OperatorType:   operatorType,
			}
			if err := tx.Create(feedbackLog).Error; err != nil {
				return fmt.Errorf("insert read feedback log: %w", err)
			}
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

var feedbackAllowToAcceptStates = []aipb.FeedbackState{
	aipb.FeedbackState_FEEDBACK_STATE_UNREAD,
	aipb.FeedbackState_FEEDBACK_STATE_READ,
}

// AcceptFeedbackLogic 采用用户反馈逻辑
type AcceptFeedbackLogic struct {
	feedbackLogic
}

// Check 检查是否可以标记已读
func (l *AcceptFeedbackLogic) Check(ctx context.Context, feedbackID uint64, operator *basepb.Identity) (*model.TFeedback, error) {
	feedback, err := findFeedbackByKey(ctx, feedbackID)
	if err != nil {
		return nil, err
	}
	if feedback == nil {
		return nil, xerrors.NotFoundError("feedback not found")
	}
	if feedback.State == aipb.FeedbackState_FEEDBACK_STATE_ACCEPTED {
		return nil, xerrors.NewCode(errorspb.AiError_AiFeedbackAlreadyAccepted)
	}

	if operator.IdentityType != basepb.IdentityType_IDENTITY_TYPE_MGMT {
		// 只允助手管理员操作
		if isAdmin, err := model.IsAssistantAdmin(model.NewConnection(ctx),
			feedback.AssistantID, operator); err != nil {
			return nil, err
		} else if !isAdmin {
			return nil, xerrors.ForbiddenError("no permission")
		}
	}

	if !slices.Contains(feedbackAllowToAcceptStates, feedback.State) {
		return nil, xerrors.NewCode(errorspb.AiError_AiFeedbackStateNotAllowedAccept)
	}

	return feedback, nil
}

// Accept 标记采用
func (l *AcceptFeedbackLogic) Accept(ctx context.Context, feedback *model.TFeedback, para *aipb.ReqAcceptFeedback) error {
	feedback.State = aipb.FeedbackState_FEEDBACK_STATE_ACCEPTED
	feedback.HandledBy = para.Operator
	feedback.HandledAt = sql.NullTime{Valid: true, Time: time.Now()}
	feedback.UpdateIdentity = para.Operator
	feedback.UpdateDate = time.Now()
	columns := []string{
		"state", "handled_by", "handled_at", "update_identity", "update_date",
	}

	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		res := tx.Select(columns).Save(feedback)
		if res.Error != nil {
			return fmt.Errorf("accept feedback: %w", res.Error)
		}

		// 日志
		if res.RowsAffected > 0 {
			// 操作日志
			if err := tx.Create(&model.TFeedbackLog{
				AssistantID:    feedback.AssistantID,
				FeedbackID:     feedback.ID,
				Action:         aipb.FeedbackAction_FEEDBACK_ACTION_ACCEPT,
				CreateDate:     feedback.CreateDate,
				CreateIdentity: para.Operator,
			}).Error; err != nil {
				return fmt.Errorf("create feedback log: %v", err)
			}
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func findFeedbackByKey(ctx context.Context, feedbackID uint64) (*model.TFeedback, error) {
	feedback, err := model.NewQuery[model.TFeedback](ctx).FindByKey(feedbackID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("find feedback: %w", err)
	}
	return feedback, nil
}
