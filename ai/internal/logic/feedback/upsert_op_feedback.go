package feedback

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"
)

// UpsertOpFeedbackLogic ...
type UpsertOpFeedbackLogic struct {
	feedbackLogic
}

// Upsert 更新/创建
func (l *UpsertOpFeedbackLogic) Upsert(ctx context.Context, para *aipb.ReqUpsertOpFeedback) (*model.TFeedback, error) {
	if !slices.Contains([]basepb.IdentityType{
		basepb.IdentityType_IDENTITY_TYPE_USER,
		basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}, para.Operator.IdentityType) {
		return nil, xerrors.ForbiddenError("no permission")
	}

	var (
		err      error
		answer   *model.TChatMessage
		feedback *model.TFeedback
	)

	switch cond := para.Cond.(type) {
	case *aipb.ReqUpsertOpFeedback_ByAnswerId:
		answer, err = l.queryAnswer(ctx, cond.ByAnswerId)
		if err != nil {
			return nil, err
		}

		// 只允助手管理员操作
		if !slices.Contains(para.OperatorAssistantId, answer.Chat.AssistantID) {
			return nil, xerrors.ForbiddenError("no permission")
		}

		feedback, err = l.queryFeedbackByAnswerID(ctx, cond.ByAnswerId)
		if err != nil {
			return nil, err
		}
		if feedback == nil {
			feedback = &model.TFeedback{}
		}
	case *aipb.ReqUpsertOpFeedback_ByFeedbackId:
		feedback, err = l.queryFeedback(ctx, cond.ByFeedbackId)
		if err != nil {
			return nil, err
		}
	}

	var isUpdate bool
	if feedback.HasOpFeedback {
		isUpdate = true
	}

	feedback.AnswerRating = para.AnswerRating
	feedback.HitExpectedDoc = para.HitExpectedDoc
	feedback.OpComment = para.OpComment
	feedback.HasOpFeedback = true
	feedback.OpFeedbackBy = para.Operator
	feedback.OpFeedbackAt = sql.NullTime{Valid: true, Time: time.Now()}
	feedback.UpdateIdentity = para.Operator
	feedback.UpdateDate = time.Now()
	columns := []string{
		"answer_rating", "hit_expected_doc", "op_comment", "has_op_feedback",
		"op_feedback_by", "op_feedback_at", "update_identity", "update_date",
	}

	if answer != nil {
		feedback.AssistantID = answer.Chat.AssistantID
		feedback.QuestionID = answer.QuestionID
		feedback.AnswerID = answer.ID
		feedback.RegionCode = answer.Chat.RegionCode
		columns = append(columns, "assistant_id", "question_id", "answer_id", "region_code")
	}

	if feedback.ID == 0 {
		feedback.CreateIdentity = para.Operator
		feedback.CreateDate = time.Now()
		columns = append(columns, "create_identity", "create_date")
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Select(columns).Save(feedback).Error; err != nil {
			return fmt.Errorf("save op feedback: %v", err)
		}

		// 文档
		if err := upsertFeedbackDocs(tx, feedback.ID,
			aipb.FeedbackDocType_FEEDBACK_DOC_TYPE_OP, para.DocId); err != nil {
			return err
		}

		// 操作日志
		action := aipb.FeedbackAction_FEEDBACK_ACTION_CREATE_OP_FEEDBACK
		if isUpdate {
			action = aipb.FeedbackAction_FEEDBACK_ACTION_UPDATE_OP_FEEDBACK
		}
		if err := tx.Create(&model.TFeedbackLog{
			AssistantID:    feedback.AssistantID,
			FeedbackID:     feedback.ID,
			Action:         action,
			CreateDate:     time.Now(),
			CreateIdentity: para.Operator,
		}).Error; err != nil {
			return fmt.Errorf("create feedback log: %v", err)
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return feedback, nil
}
