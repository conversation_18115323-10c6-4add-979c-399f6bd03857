package feedback

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// UpsertMgmtFeedbackLogic ...
type UpsertMgmtFeedbackLogic struct {
	feedbackLogic
}

func (l *UpsertMgmtFeedbackLogic) Upsert(ctx context.Context, para *aipb.ReqUpsertMgmtFeedback) (*model.TFeedback, error) {
	if para.Operator.IdentityType != basepb.IdentityType_IDENTITY_TYPE_MGMT {
		return nil, xerrors.ForbiddenError("no permission")
	}

	var (
		err      error
		answer   *model.TChatMessage
		feedback *model.TFeedback
	)

	switch cond := para.Cond.(type) {
	case *aipb.ReqUpsertMgmtFeedback_ByAnswerId:
		answer, err = l.queryAnswer(ctx, cond.ByAnswerId)
		if err != nil {
			return nil, err
		}

		feedback, err = l.queryFeedbackByAnswerID(ctx, cond.ByAnswerId)
		if err != nil {
			return nil, err
		}
		if feedback == nil {
			feedback = &model.TFeedback{}
		}
	case *aipb.ReqUpsertMgmtFeedback_ByFeedbackId:
		feedback, err = l.queryFeedback(ctx, cond.ByFeedbackId)
		if err != nil {
			return nil, err
		}
	}

	var isUpdate bool
	if feedback.HasMgmtFeedback {
		isUpdate = true
	}

	feedback.AnswerRating = para.AnswerRating
	feedback.MgmtFeedback = para.MgmtFeedback
	feedback.MgmtComment = para.MgmtComment
	feedback.HasMgmtFeedback = true
	feedback.MgmtFeedbackBy = para.Operator
	feedback.MgmtFeedbackAt = sql.NullTime{Valid: true, Time: time.Now()}
	feedback.UpdateIdentity = para.Operator
	feedback.UpdateDate = time.Now()
	columns := []string{
		"answer_rating", "mgmt_feedback", "mgmt_comment", "has_mgmt_feedback",
		"mgmt_feedback_by", "mgmt_feedback_at", "update_identity", "update_date",
	}

	if answer != nil {
		feedback.AssistantID = answer.Chat.AssistantID
		feedback.QuestionID = answer.QuestionID
		feedback.AnswerID = answer.ID
		feedback.RegionCode = answer.Chat.RegionCode
		columns = append(columns, "assistant_id", "question_id", "answer_id","region_code")
	}

	if feedback.ID == 0 {
		feedback.CreateIdentity = para.Operator
		feedback.CreateDate = time.Now()
		columns = append(columns, "create_identity", "create_date")
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Select(columns).Save(feedback).Error; err != nil {
			return fmt.Errorf("upsert mgmt feedback: %v", err)
		}

		// 文档
		if err := upsertFeedbackDocs(tx, feedback.ID,
			aipb.FeedbackDocType_FEEDBACK_DOC_TYPE_MGMT, para.MgmtDocId); err != nil {
			return err
		}

		// 操作日志
		action := aipb.FeedbackAction_FEEDBACK_ACTION_CREATE_MGMT_FEEDBACK
		if isUpdate {
			action = aipb.FeedbackAction_FEEDBACK_ACTION_UPDATE_MGMT_FEEDBACK
		}
		if err := tx.Create(&model.TFeedbackLog{
			AssistantID:    feedback.AssistantID,
			FeedbackID:     feedback.ID,
			Action:         action,
			CreateDate:     time.Now(),
			CreateIdentity: para.Operator,
		}).Error; err != nil {
			return fmt.Errorf("create feedback log: %v", err)
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return feedback, nil
}

func upsertFeedbackDocs(tx *gorm.DB, feedbackID uint64, typ aipb.FeedbackDocType, docIDs []uint64) error {
	docIDs, docs := model.ToFeedbackDocs(feedbackID, typ, docIDs)

	// 先删除
	if len(docIDs) == 0 {
		if err := tx.Delete(&model.TFeedbackDoc{}, "feedback_id = ? AND type = ? AND doc_id NOT IN (?)",
			feedbackID, typ, docIDs).Error; err != nil {
			return fmt.Errorf("delete feedback all docs: %v", err)
		}
	} else {
		if err := tx.Delete(&model.TFeedbackDoc{}, "feedback_id = ? AND type = ? AND doc_id NOT IN (?)",
			feedbackID, typ, docIDs).Error; err != nil {
			return fmt.Errorf("delete feedback all docs: %v", err)
		}
	}

	// 再插入
	if len(docs) > 0 {
		err := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "feedback_id"}, {Name: "type"}, {Name: "doc_id"}},
			DoNothing: true,
		}).Create(docs).Error
		if err != nil {
			return fmt.Errorf("upsert feedback docs: %v", err)
		}
	}

	return nil
}
