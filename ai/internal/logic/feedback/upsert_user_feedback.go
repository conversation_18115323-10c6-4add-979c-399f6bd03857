package feedback

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
)

// UpsertUserFeedbackLogic ...
type UpsertUserFeedbackLogic struct {
	feedbackLogic
}

// Upsert 创建/更新
func (l *UpsertUserFeedbackLogic) Upsert(ctx context.Context, para *aipb.ReqUpsertUserFeedback) (*model.TFeedback, error) {
	if para.Operator.IdentityType != basepb.IdentityType_IDENTITY_TYPE_USER {
		return nil, xerrors.ForbiddenError("no permission")
	}

	switch cond := para.Cond.(type) {
	case *aipb.ReqUpsertUserFeedback_ByAssistantId:
		return l.createByAssistantID(ctx, cond.ByAssistantId, para)
	case *aipb.ReqUpsertUserFeedback_ByAnswerId:
		return l.upsertByAnswerID(ctx, cond.ByAnswerId, para)
	}

	return nil, fmt.Errorf("unsupported cond")
}

func (l *UpsertUserFeedbackLogic) createByAssistantID(ctx context.Context, assistantID uint64, para *aipb.ReqUpsertUserFeedback) (*model.TFeedback, error) {
	feedback := &model.TFeedback{}
	feedback.AssistantID = assistantID
	feedback.Question = para.Question
	feedback.Answer = para.Answer
	feedback.AnswerRating = aipb.FeedbackAnswerRating_FEEDBACK_ANSWER_RATING_BAD
	feedback.HasUserFeedback = true
	feedback.UserFeedbackBy = para.Operator
	feedback.UserFeedbackAt = sql.NullTime{Valid: true, Time: time.Now()}
	feedback.UpdateIdentity = para.Operator
	feedback.UpdateDate = time.Now()
	feedback.CreateIdentity = para.Operator
	feedback.CreateDate = time.Now()
	feedback.RegionCode = para.RegionCode

	references := model.TypedReferencesToFeedbackReferences(para.References, para.Operator.IdentityId)

	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Create(feedback).Error; err != nil {
			return fmt.Errorf("create user feedback by assistant_id: %v", err)
		}

		if err := tx.Model(feedback).Association("References").Unscoped().Replace(references); err != nil {
			return fmt.Errorf("replace feedback expected docs: %v", err)
		}

		// 操作日志
		if err := tx.Create(&model.TFeedbackLog{
			AssistantID:    feedback.AssistantID,
			FeedbackID:     feedback.ID,
			Action:         aipb.FeedbackAction_FEEDBACK_ACTION_CREATE_USER_FEEDBACK,
			CreateDate:     time.Now(),
			CreateIdentity: para.Operator,
		}).Error; err != nil {
			return fmt.Errorf("create feedback log: %v", err)
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return feedback, nil
}

func (l *UpsertUserFeedbackLogic) upsertByAnswerID(ctx context.Context, answerID uint64, para *aipb.ReqUpsertUserFeedback) (*model.TFeedback, error) {
	answer, err := l.queryAnswer(ctx, answerID)
	if err != nil {
		return nil, err
	}

	// 只允许提问人操作
	if answer.CreateBy != para.Operator.IdentityId {
		return nil, xerrors.ForbiddenError("no permission")
	}

	feedback, err := l.queryFeedbackByAnswerID(ctx, answerID)
	if err != nil {
		return nil, err
	}
	if feedback == nil {
		feedback = &model.TFeedback{}
	}

	var isUpdate bool
	if feedback.HasUserFeedback {
		isUpdate = true
	}

	feedback.AssistantID = answer.Chat.AssistantID
	feedback.QuestionID = answer.QuestionID
	feedback.AnswerID = answer.ID
	feedback.Answer = para.Answer
	feedback.AnswerRating = aipb.FeedbackAnswerRating_FEEDBACK_ANSWER_RATING_BAD
	feedback.HasUserFeedback = true
	feedback.UserFeedbackBy = para.Operator
	feedback.UserFeedbackAt = sql.NullTime{Valid: true, Time: time.Now()}
	feedback.UpdateIdentity = para.Operator
	feedback.UpdateDate = time.Now()

	columns := []string{
		"assistant_id", "question_id", "answer_id", "answer", "answer_rating", "has_user_feedback",
		"user_feedback_by", "user_feedback_at", "update_identity", "update_date",
	}

	if feedback.ID == 0 {
		feedback.CreateIdentity = para.Operator
		feedback.CreateDate = time.Now()
		columns = append(columns, "create_identity", "create_date")
	}

	references := model.TypedReferencesToFeedbackReferences(para.References, para.Operator.IdentityId)

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Select(columns).Save(feedback).Error; err != nil {
			return fmt.Errorf("upsert user feedback by question_id: %v", err)
		}

		if err := tx.Model(feedback).Association("References").Unscoped().Replace(references); err != nil {
			return fmt.Errorf("replace feedback expected docs: %v", err)
		}

		// 操作日志
		action := aipb.FeedbackAction_FEEDBACK_ACTION_CREATE_USER_FEEDBACK
		if isUpdate {
			action = aipb.FeedbackAction_FEEDBACK_ACTION_UPDATE_USER_FEEDBACK
		}
		if err := tx.Create(&model.TFeedbackLog{
			AssistantID:    feedback.AssistantID,
			FeedbackID:     feedback.ID,
			Action:         action,
			CreateDate:     time.Now(),
			CreateIdentity: para.Operator,
		}).Error; err != nil {
			return fmt.Errorf("create feedback log: %v", err)
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return feedback, nil
}
