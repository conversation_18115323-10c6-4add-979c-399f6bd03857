package feedback

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
)

var defaultFeedbackLogOrderBy = map[string]bool{
	"id":          true,
	"create_date": true,
}

// GetFeedbackLogsLogic ...
type GetFeedbackLogsLogic struct {
}

// Get 获取
func (l *GetFeedbackLogsLogic) Get(ctx context.Context, req *aipb.ReqGetFeedbackLogs) ([]*model.TFeedbackLog, error) {
	var (
		logs []*model.TFeedbackLog
		err  error
	)

	query := model.NewQuery[model.TFeedbackLog](ctx).Wheres(func(db *gorm.DB) {
		l.applyFilter(db, req.Filter)
	})

	orderBy := filterOrderBy(req.OrderBy, "feedbackLog.orderBy", defaultFeedbackLogOrderBy)
	applyOrderBy(query, orderBy)
	l.applyRelation(query.DB(), req.Relation)

	if req.Page == nil {
		logs, err = query.Get()
	} else {
		paginator := xorm.NewPaginator(req.Page.Offset, req.Page.Limit)
		logs, err = query.Paging(paginator)
	}
	if err != nil {
		return nil, fmt.Errorf("query t_feedback_log: %w", err)
	}

	return logs, nil
}

// Count 计数
func (l *GetFeedbackLogsLogic) Count(ctx context.Context, req *aipb.ReqGetFeedbackLogs) (uint32, error) {
	query := model.NewQuery[model.TFeedbackLog](ctx).Wheres(func(db *gorm.DB) {
		l.applyFilter(db, req.Filter)
	})

	totalCount, err := query.Count()
	if err != nil {
		return 0, fmt.Errorf("count t_feedback_log: %w", err)
	}
	return totalCount, nil
}

func (l *GetFeedbackLogsLogic) applyFilter(db *gorm.DB, filter *aipb.ReqGetFeedbackLogs_Filter) {
	if filter == nil {
		return
	}

	if filter.FeedbackId > 0 {
		db.Where("feedback_id = ?", filter.FeedbackId)
	}

	if len(filter.AssistantId) > 0 {
		db.Where("assistant_id IN ?", filter.AssistantId)
	}

	if filter.CreateIdentity != nil {
		where := "create_identity->'$identity_type' = ? AND create_identity->'$identity_id' = ?"
		args := []any{filter.CreateIdentity.IdentityType, filter.CreateIdentity.IdentityId}

		if filter.CreateIdentity.IdentityType == basepb.IdentityType_IDENTITY_TYPE_TEAM &&
			filter.CreateIdentity.ExtraId > 0 {
			where += " AND create_identity->'$extra_id' = ?"
			args = append(args, filter.CreateIdentity.ExtraId)
		}

		db.Where(where, args...)
	}

	if len(filter.Action) > 0 {
		db.Where("action in (?)", filter.Action)
	}

	if filter.CreateDateRange != nil {
		if filter.CreateDateRange.Start != nil {
			db.Where("create_date >= ?", filter.CreateDateRange.Start.AsTime().Local())
		}
		if filter.CreateDateRange.End != nil {
			db.Where("create_date <= ?", filter.CreateDateRange.End.AsTime().Local())
		}
	}
}

func (l *GetFeedbackLogsLogic) applyRelation(db *gorm.DB, relation *aipb.ReqGetFeedbackLogs_Relation) {
	if relation == nil {
		return
	}

	if relation.Feedback {
		db.Preload("Feedback.OriginalQuestion")
	}
}

func filterOrderBy(orderBy []*basepb.OrderBy, key string, defaultOrderBy map[string]bool) []*basepb.OrderBy {
	allowedOrderBy := getAllowedOrderBy(key, defaultOrderBy)
	filtered := make([]*basepb.OrderBy, 0, len(orderBy))
	set := make(map[string]bool)
	for _, o := range orderBy {
		if !allowedOrderBy[o.Column] || set[o.Column] {
			continue
		}
		filtered = append(filtered, o)
		set[o.Column] = true
	}
	return filtered
}

func getAllowedOrderBy(key string, defaultOrderBy map[string]bool) map[string]bool {
	if !config.Has(key) {
		return defaultOrderBy
	}

	columns := config.GetStringSlice(key)
	allowedOrderBy := make(map[string]bool, len(columns))
	for _, column := range columns {
		allowedOrderBy[column] = true
	}
	return allowedOrderBy
}

func applyOrderBy[Model any](query *xorm.Query[Model], orderBy []*basepb.OrderBy) {
	if len(orderBy) == 0 {
		return
	}

	for _, o := range orderBy {
		query.OrderBy(o.Column, o.Desc)
	}
}
