package logic

import (
	"context"
	"fmt"
	"reflect"
	"slices"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	tablechunk "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk/table"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// DocTipLogic 知识提示
type DocTipLogic struct{}

func NewDocTipLogic() *DocTipLogic {
	return &DocTipLogic{}
}

// FillTextFileAllTipsSummary 填充文本文件的所有知识提示摘要，用于列表显示
func (l *DocTipLogic) FillTextFileAllTipsSummary(ctx context.Context, rows []*model.TDoc, items []*ai.FullTextFile, contributor *ai.Contributor, scopedAssistant ...uint64) error {
	if len(rows) == 0 || len(items) == 0 {
		return nil
	}

	// 1. 填充重复信息
	repeatedMap, err := l.CheckDocRepeatedTip(ctx, rows, contributor, scopedAssistant...)
	if err != nil {
		return fmt.Errorf("检查文档重复失败: %w", err)
	}

	// 将重复信息填充到响应中
	docIndexMap := make(map[uint64]int, len(rows))
	for i, row := range rows {
		docIndexMap[row.ID] = i
	}

	for docID, isRepeated := range repeatedMap {
		if idx, ok := docIndexMap[docID]; ok && idx < len(items) && items[idx].Doc != nil && isRepeated {
			items[idx].Doc.HasRepeated = true
		}
	}

	// 2. 填充表格超长提示
	if err := l.FillOverSizedTablesTip(ctx, rows, items, scopedAssistant); err != nil {
		return fmt.Errorf("填充表格超长提示失败: %w", err)
	}

	return nil
}

// FillQaAllTipsSummary 填充qa的所有知识提示摘要，用于列表显示
func (l *DocTipLogic) FillQaAllTipsSummary(ctx context.Context, rows []*model.TDoc, items []*ai.QA, contributor *ai.Contributor, scopedAssistant ...uint64) error {
	if len(rows) == 0 || len(items) == 0 {
		return nil
	}

	// 1. 填充重复信息
	repeatedMap, err := l.CheckDocRepeatedTip(ctx, rows, contributor, scopedAssistant...)
	if err != nil {
		return fmt.Errorf("检查文档重复失败: %w", err)
	}
	// 将重复信息填充到响应中
	docIndexMap := make(map[uint64]int, len(rows))
	for i, row := range rows {
		docIndexMap[row.ID] = i
	}

	for docID, isRepeated := range repeatedMap {
		if idx, ok := docIndexMap[docID]; ok && idx < len(items) && items[idx] != nil && isRepeated {
			items[idx].HasRepeated = true
		}
	}

	// 2. 填充表格超长提示
	if err := l.FillQaOverSizedQuestionTip(ctx, rows, items, scopedAssistant); err != nil {
		return fmt.Errorf("填充表格超长提示失败: %w", err)
	}

	return nil
}

// FillOverSizedTablesTip 填充表格超长提示
func (l *DocTipLogic) FillOverSizedTablesTip(ctx context.Context, rows []*model.TDoc, items []*ai.FullTextFile, assistantIDs []uint64) error {
	if len(rows) == 0 {
		return nil
	}

	// 批量检查文档的表格头部长度
	docIds := make([]uint64, 0, len(rows))
	docIndexMap := make(map[uint64]int, len(rows))
	for i, v := range rows {
		docIds = append(docIds, v.ID)
		docIndexMap[v.ID] = i
	}

	// 对每个助手进行检查
	docTableLogic := tablechunk.NewDocTableLogic(model.NewConnection(ctx))
	overSizedTablesMap, err := docTableLogic.HasOverSizedTablesBatch(ctx, docIds, assistantIDs)
	if err != nil {
		return fmt.Errorf("HasOverSizedTablesBatch failed: %w", err)
	}

	// 如果任何一个助手下有超长表头，就标记为 true
	for docID, hasOverSized := range overSizedTablesMap {
		if idx, ok := docIndexMap[docID]; ok && hasOverSized {
			if doc := items[idx].Doc; doc != nil {
				doc.HasOverSizedTables = true
			}
		}
	}

	return nil
}

func (l *DocTipLogic) FillQaOverSizedQuestionTip(ctx context.Context, rows []*model.TDoc, items []*ai.QA, assistantIDs []uint64) error {
	if len(rows) == 0 {
		return nil
	}

	docIds := make([]uint64, 0, len(rows))
	docIndexMap := make(map[uint64]int, len(rows))
	for i, v := range rows {
		docIndexMap[v.ID] = i
		docIds = append(docIds, v.ID)
	}

	overSizedMap, err := l.CheckQaOverSizedQuestionTip(ctx, docIds, assistantIDs)
	if err != nil {
		return fmt.Errorf("CheckQaOverSizedQuestionTip failed: %w", err)
	}

	for docID, hasOverSized := range overSizedMap {
		if idx, ok := docIndexMap[docID]; ok && hasOverSized {
			items[idx].QuestionOversize = true
		}
	}

	return nil
}

// CheckQaOverSizedQuestionTip 检查qa的question是否超长
func (l *DocTipLogic) CheckQaOverSizedQuestionTip(ctx context.Context, docIds []uint64, assistantIDs []uint64) (map[uint64]bool, error) {
	if len(docIds) == 0 {
		return nil, nil
	}

	overSizedMap, err := NewQaQuestionOversizeLogic(ctx).HasOverSizedQuestionBatch(ctx, docIds, assistantIDs)
	if err != nil {
		return nil, fmt.Errorf("HasOverSizedTablesBatch failed: %w", err)
	}

	return overSizedMap, nil
}

// CheckDocRepeatedTip 检查文档是否存在重复
func (l *DocTipLogic) CheckDocRepeatedTip(ctx context.Context, rows []*model.TDoc, contributor *ai.Contributor, scopedAssistant ...uint64) (map[uint64]bool, error) {
	if len(rows) == 0 {
		return nil, nil
	}

	// 获取所有文档ID
	docIds := make([]uint64, 0, len(rows))
	docIndexMap := make(map[uint64]int, len(rows))
	for i, v := range rows {
		docIds = append(docIds, v.ID)
		docIndexMap[v.ID] = i
	}
	// 查询重复文档
	var repeatedDocIds []uint64
	// 构建查询条件
	query := model.NewConnection(ctx).Model(&model.TDoc{})
	query = query.Scopes(GenDocTenantScope(contributor, scopedAssistant...))
	query1 := query.WithContext(ctx)

	err := query.Select("DISTINCT t_doc.id").
		InnerJoins("JOIN (?) t2 ON t_doc.index_text_md5 = t2.index_text_md5 AND t_doc.data_type = t2.data_type "+
			" AND t_doc.data_source = t2.data_source AND t_doc.id != t2.id AND t_doc.id IN (?) AND t2.is_copy = 0", query1, docIds).
		Where("t_doc.index_text_md5 != ?", EmptyStringMD5).
		Pluck("t_doc.id", &repeatedDocIds).Error
	if err != nil {
		return nil, fmt.Errorf("查询重复文档失败: %w", err)
	}

	// 创建结果映射
	result := make(map[uint64]bool, len(repeatedDocIds))

	// 标记重复文档
	for _, docID := range repeatedDocIds {
		result[docID] = true
	}

	return result, nil
}

// GetTextFileAllTips 文本文件的所有知识提示
func (l *DocTipLogic) GetTextFileAllTips(ctx context.Context, docId uint64, contributor *ai.Contributor, scopedAssistant ...uint64) (*ai.RspGetTextFileTip, error) {
	tip := &ai.RspGetTextFileTip{}

	tbs, err := l.GetOverSizedTables(ctx, docId, scopedAssistant...)
	if err != nil {
		return nil, err
	}
	tip.TableOverSize = tbs

	parseState, err := l.GetParseState(ctx, docId)
	if err != nil {
		return nil, err
	}
	tip.State = parseState

	repeated, err := l.GetRepeated(ctx, docId, contributor, scopedAssistant...)
	if err != nil {
		return nil, err
	}
	for _, v := range repeated {
		// 只提取文件名即可
		tip.Repeated = append(tip.Repeated, v.FileName)
	}
	return tip, nil
}

// GetQaAllTips QA的所有知识提示
func (l *DocTipLogic) GetQaAllTips(ctx context.Context, docId uint64, contributor *ai.Contributor, scopedAssistant ...uint64) (*ai.RspGetQaTip, error) {
	tip := &ai.RspGetQaTip{}

	// 获取问题超长提示
	qs, err := l.CheckQaOverSizedQuestionTip(ctx, []uint64{docId}, scopedAssistant)
	if err != nil {
		return nil, err
	}
	tip.QuestionOverSize = qs[docId]

	// 获取重复提示
	repeated, err := l.GetRepeated(ctx, docId, contributor, scopedAssistant...)
	if err != nil {
		return nil, err
	}
	for _, v := range repeated {
		// 对于QA，显示重复的问题文本
		tip.Repeated = append(tip.Repeated, v.IndexText)
	}
	return tip, nil
}

// GetOverSizedTables 获取表格表头超长的提示信息
func (l *DocTipLogic) GetOverSizedTables(ctx context.Context, docId uint64, scopedAssistant ...uint64) ([]*ai.TextFileTipTableOverSize, error) {
	tables, err := new(tablechunk.DocTableCheckLogic).GetOverSizedTablesByDocID(ctx, docId, scopedAssistant...)
	if err != nil {
		return nil, err
	}

	// Create assistant map for quick lookup
	assistantMap := make(map[uint64]*model.TAssistant)
	var assistantIds []uint64
	for _, v := range tables {
		assistantIds = append(assistantIds, v.AssistantID)
	}
	// 对assistantIds进行去重，避免重复查询
	slices.Sort(assistantIds)
	assistantIds = slices.Compact(assistantIds)
	gets, err := model.NewQuery[model.TAssistant](ctx).Select("id", "name", "name_en").Where("id in ?", assistantIds).Get()
	if err != nil {
		return nil, err
	}
	for _, v := range gets {
		assistantMap[v.ID] = v
	}
	tips := make([]*ai.TextFileTipTableOverSize, 0, len(tables))
	// Construct tips with assistant names
	for _, table := range tables {
		assistant := assistantMap[table.AssistantID]
		if assistant == nil {
			continue
		}
		tip := &ai.TextFileTipTableOverSize{
			Header:          table.TableHeader,
			AssistantId:     table.AssistantID,
			AssistantName:   assistant.Name,
			AssistantNameEn: assistant.NameEn,
			TableTitle:      table.TableTitle,
		}
		tips = append(tips, tip)
	}

	return tips, nil
}

// GetRepeated 获取内容重复的 doc
// 用户前台需要传 contributor 和 scopedAssistant，用于租户隔离查询
// 查询重复时，只限定在同一个租户（贡献者或者绑定了助手的知识）内
func (l *DocTipLogic) GetRepeated(ctx context.Context, docId uint64, contributor *ai.Contributor, scopedAssistant ...uint64) ([]*model.TDoc, error) {
	// 基础查询
	db := model.NewQuery[model.TDoc](ctx).DB()

	// 获取文档类型
	var doc *model.TDoc
	err := db.Select("data_type", "index_text_md5").Where("id = ?", docId).Scan(&doc).Error
	if err != nil {
		return nil, err
	}
	docType := ai.DocType(doc.DataType)

	// 如果当前文档的MD5是空字符串MD5，直接返回空结果，因为空内容不应该被认为是重复
	if doc.IndexTextMd5 == EmptyStringMD5 {
		return []*model.TDoc{}, nil
	}

	// 查询重复文档
	db = model.NewQuery[model.TDoc](ctx).DB()
	db = db.Where("id <> ?", docId)
	db = db.Where("index_text_md5 = ?", doc.IndexTextMd5)
	db = db.Where("data_type = ?", docType)
	db = db.Scopes(GenDocTenantScope(contributor, scopedAssistant...))

	var repeated []*model.TDoc
	if docType == ai.DocType_DOCTYPE_FILE || docType == ai.DocType_DOCTYPE_TEXT {
		// 文件和文本查询 file_name
		db = db.Select("file_name")
	} else {
		// qa 查询 index_text
		db = db.Select("index_text")
	}
	err = db.Find(&repeated).Error
	if err != nil {
		return nil, err
	}

	return repeated, nil
}

// GetParseState 解析状态
// hint 不为 nil，可以直接从 hint 读取状态字段
func (l *DocTipLogic) GetParseState(ctx context.Context, docId uint64, hint ...*model.TDoc) (ai.DocState, error) {
	if len(hint) != 0 && hint[0] != nil {
		return hint[0].State, nil
	}

	docs, err := model.NewQuery[model.TDoc](ctx).Where("id = ?", docId).Select("state").Get()
	if err != nil {
		return 0, err
	}

	if len(docs) == 0 {
		return 0, gorm.ErrRecordNotFound
	}

	return docs[0].State, nil
}

// QaQuestionOversizeLogic 检查qa的question是否超长
type QaQuestionOversizeLogic struct {
	db *gorm.DB
}

func NewQaQuestionOversizeLogic(ctx context.Context) *QaQuestionOversizeLogic {
	return &QaQuestionOversizeLogic{db: model.NewConnection(ctx)}
}

// HasOverSizedQuestionBatch 检查qa的question是否超长
func (l *QaQuestionOversizeLogic) HasOverSizedQuestionBatch(ctx context.Context, docIDs []uint64, assistantID []uint64) (map[uint64]bool, error) {
	if len(docIDs) == 0 {
		return make(map[uint64]bool), nil
	}

	var tables []model.TDocTableOversize
	err := l.db.Model(&model.TDocTableOversize{}).
		Scopes(func(db *gorm.DB) *gorm.DB {
			if len(assistantID) != 0 {
				db.Where("assistant_id IN (?)", assistantID)
			}
			return db
		}).
		Where("doc_id IN ?  AND is_header_over_sized = ?", docIDs, true).
		Select("DISTINCT doc_id").
		Find(&tables).Error
	if err != nil {
		return nil, errors.Wrap(err, "failed to get doc tables")
	}

	// 构建结果 map
	result := make(map[uint64]bool, len(docIDs))
	// 默认所有文档都没有超长表头
	for _, docID := range docIDs {
		result[docID] = false
	}
	// 更新有超长表头的文档
	for _, table := range tables {
		result[table.DocID] = true
	}

	return result, nil
}

// IsQuestionOverSized 检查qa的question是否超长
// 返回值:
// - bool: 是否超长
// - int: 超长字符串长度
// - int: 超长字符串的token数量
// - int: 超长字符串的maxTokens
func (l *QaQuestionOversizeLogic) IsQuestionOverSized(ctx context.Context, embedModel string, question string) (bool, int, int) {
	if embedModel == "" {
		return false, 0, 0
	}

	isExceed, tokenCount, maxTokens, _ := model.IsExceedEmbeddingTokenLimit(embedModel, question)
	return isExceed, tokenCount, maxTokens
}

// StoreOverSizedQuestionAllAssistant 存储超长问题
func (l *QaQuestionOversizeLogic) StoreOverSizedQuestionAllAssistant(ctx context.Context, docID uint64) error {
	// 从数据库读取文档信息
	var doc model.TDoc
	fields := []string{"id", "index_text"}
	if err := l.db.Model(&model.TDoc{}).Select(fields).First(&doc, docID).Error; err != nil {
		return errors.Wrapf(err, "failed to get doc %d", docID)
	}

	// 获取文档绑定的所有助手
	var assistantDocs []model.TAssistantDoc
	if err := l.db.Model(&model.TAssistantDoc{}).Where("doc_id = ?", docID).Find(&assistantDocs).Error; err != nil {
		return errors.Wrap(err, "failed to get doc assistants")
	}

	assistantIds := make([]uint64, 0, len(assistantDocs))
	for _, assistantDoc := range assistantDocs {
		assistantIds = append(assistantIds, assistantDoc.AssistantID)
	}
	if err := tablechunk.NewDocTableLogic(l.db).ClearDocTableInfo(ctx, docID, assistantIds); err != nil {
		return errors.Wrap(err, "failed to clear doc table info")
	}

	// 如果没有绑定助手，则跳过处理
	if len(assistantDocs) == 0 {
		return nil
	}

	// 对每个绑定的助手处理表格
	for _, assistantDoc := range assistantDocs {
		// 复用 ProcessDocTables 方法，但传入已提取的表格
		if err := l.processOverSizedQuestion(ctx, docID, assistantDoc.AssistantID, doc.IndexText); err != nil {
			return errors.Wrapf(err, "failed to process doc tables for assistant %d", assistantDoc.AssistantID)
		}
	}

	return nil
}

// processOverSizedQuestion 处理超长问题
func (l *QaQuestionOversizeLogic) processOverSizedQuestion(ctx context.Context, docID uint64, assistantID uint64, question string) error {
	embedModel, err := model.GetAssistantEmbeddingModel(ctx, assistantID)
	if err != nil {
		return errors.Wrap(err, "获取嵌入模型失败")
	}

	// 准备批量 upsert 的数据
	tableInfos := make([]*model.TDocTableOversize, 0, 1)
	isExceed, tokenCount, maxTokens := l.IsQuestionOverSized(ctx, embedModel, question)
	if isExceed {
		tableInfos = append(tableInfos, &model.TDocTableOversize{
			DocID:             docID,
			AssistantID:       assistantID,
			TablePosition:     1,
			TableHeader:       question,
			IsHeaderOverSized: isExceed,
			HeaderLength:      tokenCount,
			HeaderMaxLength:   maxTokens,
		})
	}

	// 执行批量 upsert
	if len(tableInfos) > 0 {
		if err := l.db.WithContext(ctx).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "doc_id"}, {Name: "assistant_id"}, {Name: "table_position"}},
			UpdateAll: true,
		}).Create(&tableInfos).Error; err != nil {
			return errors.Wrap(err, "failed to upsert table info")
		}
	}

	// 如果tableInfos为空，则删除doc_table_oversize表中对应的记录
	if len(tableInfos) == 0 {
		if err := l.db.WithContext(ctx).Model(&model.TDocTableOversize{}).
			Where("doc_id = ?", docID).Where("assistant_id = ?", assistantID).
			Delete(nil).Error; err != nil {
			return errors.Wrap(err, "failed to delete table info")
		}
	}

	return nil
}

// StoreOverSizedQuestionAllAssistantBulkAsync 异步存储超长问题
func StoreOverSizedQuestionAllAssistantBulkAsync(ctx context.Context, docIDs []uint64) {
	for _, docID := range docIDs {
		// 每个助手启动一个协程运行
		docID := docID
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			l := NewQaQuestionOversizeLogic(ctx)
			if err := l.StoreOverSizedQuestionAllAssistant(ctx, docID); err != nil {
				log.WithContext(ctx).Errorf("failed to store over sized question for doc %d, err: %v", docID, err)
				return errors.Wrapf(err, "failed to store over sized question for doc %d", docID)
			}
			return nil
		}, boot.TraceGo(ctx))
	}
}

// StoreOverSizedTipAllAssistantBulkAsync 异步存储超长提示，根据数据类型自动区分 qa 或者文本文件
// TODO: 可能需要优化，目前是每个doc启动一个协程运行
func StoreOverSizedTipAllAssistantBulkAsync(ctx context.Context, docIDs []uint64) error {
	// 1. 先获取doc 类型
	if len(docIDs) == 0 {
		return nil
	}

	db := model.NewConnection(ctx)
	rows := make([]*model.TDoc, 0, len(docIDs))
	err := db.Model(&model.TDoc{}).Where("id IN (?)", docIDs).Select("id", "data_type").Find(&rows).Error
	if err != nil {
		return errors.Wrap(err, "failed to get doc types")
	}

	// 2. 根据doc 类型，执行 qa 或者文本逻辑
	for _, doc := range rows {
		// 每个文档启动一个协程运行
		doc := doc
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			switch ai.DocType(doc.DataType) {
			case ai.DocType_DOCTYPE_QA:
				// 处理QA问题超长提示
				l := NewQaQuestionOversizeLogic(ctx)
				if err := l.StoreOverSizedQuestionAllAssistant(ctx, doc.ID); err != nil {
					log.WithContext(ctx).Errorf("failed to store over sized question for doc %d, err: %v", doc.ID, err)
					return errors.Wrapf(err, "failed to store over sized question for doc %d", doc.ID)
				}
			case ai.DocType_DOCTYPE_FILE, ai.DocType_DOCTYPE_TEXT:
				// 处理文本文件表格超长提示
				docTableLogic := tablechunk.NewDocTableLogic(model.NewConnection(ctx))
				if err := docTableLogic.StoreOversizedTablesAllAssistant(ctx, doc.ID); err != nil {
					log.WithContext(ctx).Errorf("failed to store over sized tables for doc %d, err: %v", doc.ID, err)
					return errors.Wrapf(err, "failed to store over sized tables for doc %d", doc.ID)
				}
			}
			return nil
		}, boot.TraceGo(ctx))
	}
	return nil
}

// QAWithTipFilter 处理QA的TipFilter过滤
func (l *DocTipLogic) QAWithTipFilter(tipFilter *ai.ReqListQA_TipFilter, scopedAssistantId []uint64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 处理警告条件过滤
		if tipFilter.GetWarningGroup() != nil {
			warningGroup := tipFilter.GetWarningGroup()
			if warningGroup.GetQuestionOversize() {
				// 使用EXISTS子查询，从doc_table_oversize表中查询超长问题
				questionOverSizeSubQuery := model.NewQuery[model.TDocTableOversize](db.Statement.Context).DB().
					Select("1").
					Where("t_doc_table_oversize.doc_id = t_doc.id").
					Where("is_header_over_sized = ?", true)

				// 如果指定了助手ID，则添加助手ID过滤条件
				if len(scopedAssistantId) > 0 {
					questionOverSizeSubQuery = questionOverSizeSubQuery.Where("assistant_id IN ?", scopedAssistantId)
				}

				// 将具有超长问题的QA记录添加到查询条件中
				db = db.Where("EXISTS (?)", questionOverSizeSubQuery)
			}
		}

		// 未来可以在此处添加其他类型的过滤条件

		return db
	}
}

// TextFileWithTipFilter 处理文本文件的TipFilter过滤
func (l *DocTipLogic) TextFileWithTipFilter(tipFilter *ai.ReqListTextFile_TipFilter, scopedAssistantId []uint64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 处理警告条件过滤
		if tipFilter.GetWarningGroup() != nil {
			warningGroup := tipFilter.GetWarningGroup()
			if warningGroup.GetParseFailed() || warningGroup.GetTableOversize() {
				// 创建一个新的group db用于构建条件组
				group := db.Session(&gorm.Session{NewDB: true})

				// 1. 如果需要过滤解析失败的记录
				if warningGroup.GetParseFailed() {
					group = group.Or("state = ?", ai.DocState_DOC_STATE_PARES_FAILED)
				}

				// 2. 如果需要过滤表格过长的记录
				if warningGroup.GetTableOversize() {
					// 使用EXISTS子查询替代IN子查询
					tableOverSizeSubQuery := model.NewQuery[model.TDocTableOversize](db.Statement.Context).DB().
						Select("1").
						Where("t_doc_table_oversize.doc_id = t_doc.id").
						Where("is_header_over_sized = ?", true)

					// 如果指定了助手ID，则添加助手ID过滤条件
					if len(scopedAssistantId) > 0 {
						tableOverSizeSubQuery = tableOverSizeSubQuery.Where("assistant_id IN ?", scopedAssistantId)
					}

					group = group.Or("EXISTS (?)", tableOverSizeSubQuery)
				}

				// 将条件组添加到主查询中
				db = db.Where(group)
			}
		}

		// 未来可以在此处添加其他类型的过滤条件

		return db
	}
}

// DocWithTipFilter 根据文档类型选择合适的TipFilter处理函数
func (l *DocTipLogic) DocWithTipFilter(ctx context.Context, db *gorm.DB, docType ai.DocType, tipFilter interface{}, scopedAssistantId []uint64) *gorm.DB {
	switch docType {
	case ai.DocType_DOCTYPE_QA:
		// 支持*ai.ReqListQA_TipFilter类型
		if qaFilter, ok := tipFilter.(*ai.ReqListQA_TipFilter); ok {
			return db.Scopes(l.QAWithTipFilter(qaFilter, scopedAssistantId))
		}

		// 支持*ai.ReqListQA类型
		if req, ok := tipFilter.(*ai.ReqListQA); ok {
			tipField := reflect.ValueOf(req).Elem().FieldByName("TipFilter")
			if tipField.IsValid() && !tipField.IsNil() {
				if tipFilterObj, ok := tipField.Interface().(*ai.ReqListQA_TipFilter); ok {
					return db.Scopes(l.QAWithTipFilter(tipFilterObj, scopedAssistantId))
				}
			}
		}

		log.WithContext(ctx).Warnf("TipFilter类型不匹配，无法应用QA过滤条件: %T", tipFilter)
	case ai.DocType_DOCTYPE_TEXT, ai.DocType_DOCTYPE_FILE:
		if textFileFilter, ok := tipFilter.(*ai.ReqListTextFile_TipFilter); ok {
			return db.Scopes(l.TextFileWithTipFilter(textFileFilter, scopedAssistantId))
		}
		log.WithContext(ctx).Warnf("TipFilter类型不匹配，无法应用TextFile过滤条件: %T", tipFilter)
	default:
		log.WithContext(ctx).Warnf("未知的文档类型: %v，无法应用TipFilter", docType)
	}
	return db
}
