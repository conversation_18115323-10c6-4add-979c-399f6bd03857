package logic

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	htmltomarkdown "github.com/JohannesKaufmann/html-to-markdown"
	htmltomarkdownplg "github.com/<PERSON>/html-to-markdown/plugin"
)

// ParseFileByTika 解析文档文本内容
/**
https://tika.apache.org/
https://cwiki.apache.org/confluence/display/TIKA/TikaServer

@param filePath 文件路径
@return string, error
*/
func ParseFileByTika(filePath string, totalPage bool) (string, int, error) {

	// http路由采用容器服务 serviceName
	url := config.GetStringOr("tika.url", "http://apache-tika/tika/form")

	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	file, err := os.Open(filePath)

	defer file.Close()
	part, err := writer.CreateFormFile("upload", filepath.Base(filePath))

	if _, err := io.Copy(part, file); err != nil {
		return "", 0, err
	}
	if err := writer.Close(); err != nil {
		return "", 0, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(http.MethodPost, url, payload)

	if err != nil {
		return "", 0, err
	}
	req.Header.Add("Accept", "*/*")
	req.Header.Add("Connection", "keep-alive")

	req.Header.Set("Content-Type", writer.FormDataContentType())
	res, err := client.Do(req)
	if err != nil {
		return "", 0, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return "", 0, err
	}

	// 检查HTTP状态码
	if res.StatusCode != http.StatusOK {
		return "", 0, fmt.Errorf("unexpected HTTP status code: %d, body %s", res.StatusCode, string(body))
	}

	converter := htmltomarkdown.NewConverter("", true, nil)
	converter.Use(htmltomarkdownplg.GitHubFlavored())
	body = regRemoveHtmlTableEmptyRow.ReplaceAll(body, []byte(""))
	markdown, err := converter.ConvertString(string(body))
	// 处理产生的"�"字符
	markdown = strings.Replace(markdown, "\uFFFD", "", -1)
	if err != nil {
		return "", 0, err
	}

	page := 0
	if totalPage {
		page, err = GetFilePageCountByTika(filePath)
		if err != nil {
			return "", 0, err
		}
	}

	return markdown, page, nil
}

// GetFilePageCountByTika 从 tika 获取页数
func GetFilePageCountByTika(filePath string) (int, error) {
	url := config.GetStringOr("tika.url.meta", "http://apache-tika/tika/meta")

	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return 0, err
	}

	req, err := http.NewRequest("PUT", url, bytes.NewReader(fileData))
	if err != nil {
		return 0, err
	}
	req.Header.Set("Accept", "application/json")
	// req.Header.Set("Content-Type", "application/pdf")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return 0, err
		}

		// PDFResponse 定义 JSON 返回的数据结构
		type PDFResponse struct {
			PDFOCRPageCount string `json:"pdf:ocrPageCount"`
			XMPTPgNPages    string `json:"xmpTPg:NPages"`
		}

		var response PDFResponse
		if err := json.Unmarshal((body), &response); err != nil {
			return 0, fmt.Errorf("parse tika response error: %w", err)
		}

		pageCnt, err := strconv.Atoi(response.XMPTPgNPages)
		if err != nil {
			return 0, fmt.Errorf("parse tika page count error: %w", err)
		}
		return pageCnt, nil
	} else {
		return 0, fmt.Errorf("request tika error: %s", resp.Status)
	}
}

// regRemoveHtmlTableEmptyRow 移除空 html 行
var regRemoveHtmlTableEmptyRow = regexp.MustCompile(`(?i)<tr>\s*(<td\s*/>\s*)+</tr>`)
