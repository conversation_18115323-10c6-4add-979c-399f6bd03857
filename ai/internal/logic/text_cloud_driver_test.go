package logic

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func Test_TextCloudDriver_GetFolderID(t *testing.T) {
	ctx := context.TODO()
	cli, err := NewTencentDocWrapper().GetTokendClient(ctx, 1000002, "878cda13fc25e90bff2e04018fd59c09136ab82bce280dbd215d53e1cae8a3f8")
	if err != nil {
		t.Fatal(err)
	}

	folderID, err := GetTencentDocFolderFid(context.Background(), cli, "/我的应用文件/网页剪存")
	assert.NoError(t, err)
	assert.NotEmpty(t, folderID)

	rsp, err := ListTencentDocWebClips(ctx, cli, time.Time{})
	if err != nil {
		t.Fatal(err)
	}
	for _, v := range rsp {
		fmt.Println(v)
	}
}
