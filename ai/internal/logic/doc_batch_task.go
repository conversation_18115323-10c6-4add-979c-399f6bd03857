package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	taskClient "e.coding.net/tencent-ssv/tanlive/services/ai/internal/tasks/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot/microwrapper"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/hibiken/asynq"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/exp/constraints"
	"golang.org/x/exp/maps"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// DocQueryDataType ...
type DocQueryDataType int32

const (
	AiBatchDocTaskClone   = "ai:batch_clone_doc"
	AiBatchDocTaskDelete  = "ai:batch_delete_doc"
	AiBatchDocTaskOnOff   = "ai:batch_on_off_doc"
	AiBatchDocTaskUpdate  = "ai:batch_update_doc"
	AiBatchDocTaskReparse = "ai:batch_reparse_doc"
	AiBatchDocTaskShare   = "ai:batch_share_doc"

	DocQueryDataTypeQA  DocQueryDataType = 1
	DocQueryDataTypeDoc DocQueryDataType = 2

	AiBatchDocTimeoutHour = 5
)

// GetQueryLimitId 获取query中需要提前处理的id
func GetQueryLimitId(query *model.TDocQuery) (bool, []uint64) {
	limit := config.GetIntOr("llm.collection.patchTaskPreLimit", 50) // 任务执行的前置执行条数
	if len(query.Data) > limit {
		result := query.Data[:limit]
		query.Data = query.Data[limit:]
		return true, result
	}
	return false, query.Data
}

func createQuery(ctx context.Context, dataType DocQueryDataType, data model.DocIds, operator *ai.Operator) (uint64, error) {
	if len(data) == 0 {
		return 0, nil
	}
	row := &model.TDocQuery{
		Data:     data,
		DataType: uint32(dataType),
		CreateBy: operator,
	}
	return row.ID, model.NewQuery[model.TDocQuery](ctx).Create(row)
}

// CreateDocQuery ...
func CreateDocQuery(ctx context.Context, req *ai.ReqListTextFile, operator *ai.Operator) (uint64, uint32, error) {
	var data model.DocIds
	db, _, err := ListTextFileApplyFilter(ctx, req)
	if err != nil {
		return 0, 0, err
	}
	for _, v := range req.OrderBy {
		db.Order(clause.OrderByColumn{Column: clause.Column{Name: v.Column}, Desc: v.Desc})
	}
	if req.OrderByLabel != nil {
		labelObjType := model.DocSource2CustomLabelObjectType(req.DataSource)
		db.Scopes(model.OrderByLabel(labelObjType, req.OrderByLabel))
	}
	if len(req.OrderBy) == 0 && req.OrderByLabel == nil {
		db.Order("update_date desc").Order("id desc")
	}
	if err = db.Pluck("id", &data).Error; err != nil {
		return 0, 0, err
	}
	if len(data) == 0 {
		return 0, 0, nil
	}
	queryId, err := createQuery(ctx, DocQueryDataTypeDoc, data, operator)
	return queryId, uint32(len(data)), err
}

// CreateQAQuery ...
func CreateQAQuery(ctx context.Context, req *ai.ReqListQA, operator *ai.Operator) (uint64, uint32, error) {
	var data model.DocIds
	db, _, err := ListQAApplyFilter(ctx, req)
	if err != nil {
		return 0, 0, err
	}
	for _, v := range req.OrderBy {
		db.Order(clause.OrderByColumn{Column: clause.Column{Name: v.Column}, Desc: v.Desc})
	}
	if req.OrderByLabel != nil {
		db.Scopes(model.OrderByLabel(ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA, req.OrderByLabel))
	}
	if len(req.OrderBy) == 0 && req.OrderByLabel == nil {
		db.Order("update_date desc").Order("id desc")
	}

	if err = db.Pluck("id", &data).Error; err != nil {
		return 0, 0, err
	}
	if len(data) == 0 {
		return 0, 0, nil
	}
	queryId, err := createQuery(ctx, DocQueryDataTypeQA, data, operator)
	return queryId, uint32(len(data)), err
}

// AiBatchOperateDocTaskPayload 批量操作任务的结构
type AiBatchOperateDocTaskPayload struct {
	Task    *model.TDocBatchTask `json:"task"`
	Query   *model.TDocQuery     `json:"query"`
	Ctx     DocParseTaskCtx      `json:"ctx"`
	TraceId string               `json:"trace_id"`
	SpanId  string               `json:"span_id"`

	DisposableIds []uint64 // 一次性执行完成的id
}

func createDocBatchTask(ctx context.Context, taskType ai.DocBatchTaskType, queryId uint64,
	operator *ai.Operator, args datatypes.JSON,
) (*AiBatchOperateDocTaskPayload, error) {
	if operator == nil {
		return nil, fmt.Errorf("operator is nil")
	}

	query, err := model.NewQuery[model.TDocQuery](ctx).FindByKey(queryId)
	if err != nil {
		return nil, err
	}
	if query.CreateBy == nil || query.CreateBy.Id != operator.Id || query.CreateBy.Type != operator.Type ||
		query.CreateBy.UserId != operator.UserId {
		return nil, xerrors.ForbiddenError("not allowed")
	}
	asyncTask, disposableIds := GetQueryLimitId(query)
	if !asyncTask { // 不需要同步执行
		return &AiBatchOperateDocTaskPayload{
			Query:         query,
			DisposableIds: disposableIds,
		}, nil
	}

	task := &model.TDocBatchTask{
		QueryID:    queryId,
		TaskType:   taskType,
		CreateBy:   operator,
		CreateDate: time.Now(),
		Args:       args,
		Status:     model.DocBatchTaskTypeInit,
	}

	taskCtx := DocParseTaskCtx{
		CreateBy:     operator.Id,
		CreateByType: operator.Type,
		CreateAt:     time.Now(),
	}
	load := &AiBatchOperateDocTaskPayload{
		Task:          task,
		Query:         query,
		Ctx:           taskCtx,
		DisposableIds: disposableIds,
	}

	span := trace.SpanFromContext(ctx)
	if span.SpanContext().HasTraceID() {
		load.TraceId = span.SpanContext().TraceID().String()
		load.SpanId = span.SpanContext().SpanID().String()
		task.TraceID = load.TraceId
	}

	return load, model.NewQuery[model.TDocBatchTask](ctx).Create(task)
}

func handleBatchOperate(ctx context.Context, t *asynq.Task, traceName string,
	f func(ctx context.Context, p AiBatchOperateDocTaskPayload) error,
) error {
	var p AiBatchOperateDocTaskPayload
	if err := json.Unmarshal(t.Payload(), &p); err != nil {
		log.WithContext(ctx).Errorw("handleBatchOperate json.Unmarshal failed", "err", err)
		return fmt.Errorf("handleBatchOperate json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
	}

	var span trace.Span
	ctx, span = microwrapper.StartSpanFromContext(ctx, nil, traceName)
	traceId, _ := trace.TraceIDFromHex(p.TraceId)
	spanId, _ := trace.SpanIDFromHex(p.SpanId)
	ctx = trace.ContextWithRemoteSpanContext(ctx, span.SpanContext().WithTraceID(traceId).WithSpanID(spanId))
	defer span.End()
	// spanCtx := trace.SpanContextFromContext(ctx)
	// ctx, span = tracer.Start(trace.ContextWithRemoteSpanContext(ctx, spanCtx), name, opts...)

	// 检查任务状态 避免任务中断之后重新拉起时重复执行
	task, err := model.NewQuery[model.TDocBatchTask](ctx).FindByKey(p.Task.ID)
	if err != nil {
		log.WithContext(ctx).Errorw("handleBatchOperate get task failed", "query_id", p.Task.ID)
		return err
	}
	// 判断任务是否执行完成
	if task == nil || task.Status == model.DocBatchTaskTypeDone || task.Status == model.DocBatchTaskTypeFailed {
		log.WithContext(ctx).Infow("handleBatchOperate has been finished or failed", "taskId", p.Task.ID)
		return nil
	}

	markTask := func(ctx context.Context, id uint64, status model.DocBatchTaskType) {
		_, err = model.NewQuery[model.TDocBatchTask](ctx).UpdateByKey(p.Task.ID,
			map[string]any{model.TDocBatchTaskColumns.Status: status})
		if err != nil {
			log.WithContext(ctx).Errorw("handleBatchOperate update task failed", "query_id", p.Task.ID, "status", status)
		}
	}

	// 标记任务执行中
	markTask(ctx, p.Task.ID, model.DocBatchTaskTypeRunning)

	if err = f(ctx, p); err != nil {
		// 标记任务失败状态
		markTask(ctx, p.Task.ID, model.DocBatchTaskTypeFailed)
		return err
	}

	// 标记任务完成状态
	markTask(ctx, p.Task.ID, model.DocBatchTaskTypeDone)

	return nil
}

// HandleBatchCloneDocTask 处理克隆操作的任务
// CreateCloneBatchTask
func HandleBatchCloneDocTask(ctx context.Context, t *asynq.Task) error {
	return handleBatchOperate(ctx, t, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_CLONE.String(),
		func(ctx context.Context, p AiBatchOperateDocTaskPayload) error {
			var req ai.ReqCloneDocInBulk
			iterator := model.NewDocIdsIterator(p.Query.Data, config.GetIntOr("llm.collection.patchCloneLimit", 0))

			if err := json.Unmarshal(p.Task.Args, &req); err != nil {
				log.WithContext(ctx).Errorw("HandleBatchCloneDocTask json.Unmarshal req failed", "err", err)
				return fmt.Errorf("HandleBatchCloneDocTask json.Unmarshal req failed: %v: %w", err, asynq.SkipRetry)
			}
			for iterator.HasNext() { // 如果某一部分执行失败了需要继续执行
				req.Id = iterator.SleepDefaultNextBatch()
				if err := CloneDocInBulk(ctx, &req, nil); err != nil {
					log.WithContext(ctx).Errorw("HandleBatchCloneDocTask CloneDocInBulk error", "err", err)
				}
			}
			return nil
		})
}

// HandleBatchDeleteDocTask ...
// CreateDeleteBatchTask
func HandleBatchDeleteDocTask(ctx context.Context, t *asynq.Task) error {
	return handleBatchOperate(ctx, t, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_DELETE.String(),
		func(ctx context.Context, p AiBatchOperateDocTaskPayload) error {
			operator := &ai.Operator{Id: p.Ctx.CreateBy, Type: p.Ctx.CreateByType}

			aids, byMgmt, err := GetManagedAssistants(ctx, operator)
			if err != nil {
				log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask GetManagedAssistants Error", "err", err)
				return err
			}

			var req ai.ReqOnOffDocInBulk
			iterator := model.NewDocIdsIterator(p.Query.Data, config.GetIntOr("llm.collection.patchDeleteLimit", 0))

			if err := json.Unmarshal(p.Task.Args, &req); err != nil {
				log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask json.Unmarshal req failed", "err", err)
				return fmt.Errorf("HandleBatchDeleteDocTask json.Unmarshal req failed: %v: %w", err, asynq.SkipRetry)
			}
			for iterator.HasNext() { // 如果某一部分执行失败了需要继续执行
				req.Id = iterator.SleepDefaultNextBatch()
				if byMgmt {
					err = model.Transaction(ctx, func(tx *gorm.DB) error {
						return DeleteDocInBulk(ctx, tx, req.Id, true, nil, req.ScopedAssistantId...)
					})
					if err != nil {
						log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask OnOffDocInBulk error", "err", err)
					}
					continue
				}
				viewable, editable, err := SplitDocsByEditPermission(ctx, operator, req.Id...)
				if err != nil {
					log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask SplitDocsByEditPermission error", "err", err)
					continue
				}
				if len(viewable) > 0 {
					err = model.Transaction(ctx, func(tx *gorm.DB) error {
						return DeleteDocInBulk(ctx, tx, viewable, false, operator, aids...)
					})
					if err != nil {
						log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask OnOffDocInBulk error", "err", err)
					}
				}
				if len(editable) > 0 {
					err = model.Transaction(ctx, func(tx *gorm.DB) error {
						return DeleteDocInBulk(ctx, tx, editable, true, nil)
					})
					if err != nil {
						log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask OnOffDocInBulk error", "err", err)
					}
				}
			}
			return nil
		})
}

// HandleBatchOnOffDocTask ...
// CreateOnOffBatchTask
func HandleBatchOnOffDocTask(ctx context.Context, t *asynq.Task) error {
	return handleBatchOperate(ctx, t, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_ON_OFF.String(),
		func(ctx context.Context, p AiBatchOperateDocTaskPayload) error {
			var req ai.ReqOnOffDocInBulk
			rsp := &ai.RspOnOffDocInBulk{}
			iterator := model.NewDocIdsIterator(p.Query.Data, config.GetIntOr("llm.collection.patchOnOffLimit", 20))

			if err := json.Unmarshal(p.Task.Args, &req); err != nil {
				log.WithContext(ctx).Errorw("HandleBatchOnOffDocTask json.Unmarshal req failed", "err", err)
				return fmt.Errorf("HandleBatchOnOffDocTask json.Unmarshal req failed: %v: %w", err, asynq.SkipRetry)
			}
			var errorIds []uint64
			for iterator.HasNext() {
				req.Id = iterator.SleepLongNextBatch()

				if req.State == ai.DocState_DOC_STATE_ENABLED {
					err := CheckDocBeforeEnable(ctx, &req, rsp)
					if err != nil {
						log.WithContext(ctx).Errorw("HandleBatchOnOffDocTask OnOffDocInBulk CheckDocBeforeEnable error", "err", err)
						continue
					}
					for _, v := range rsp.PreRepeatCollections {
						keys := maps.Keys(v.FileName)
						if len(keys) > 1 {
							errorIds = append(errorIds, keys[1:]...)
						}
					}
					for _, v := range rsp.RepeatCollections {
						keys := maps.Keys(v.FileName)
						if len(keys) > 1 {
							errorIds = append(errorIds, keys[1:]...)
						}
					}
					for _, v := range rsp.ExceedQaContainsMatchLimit {
						errorIds = append(errorIds, v.DocIds...)
					}
				}

				req.Id = DifferenceSet(req.Id, errorIds)
				err := model.Transaction(ctx, func(tx *gorm.DB) error {
					return OnOffDocInBulk(ctx, tx, req.State, req.Id, req.ScopedAssistantId...)
				})
				if err != nil {
					log.WithContext(ctx).Errorw("HandleBatchOnOffDocTask OnOffDocInBulk error", "err", err)
				}
			}
			return nil
		})
}

// HandleBatchUpdateDocTask ...
// CreateUpdateBatchTask
func HandleBatchUpdateDocTask(ctx context.Context, t *asynq.Task) error {
	return handleBatchOperate(ctx, t, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_UPDATE.String(),
		func(ctx context.Context, p AiBatchOperateDocTaskPayload) error {
			operator := &ai.Operator{Id: p.Ctx.CreateBy, Type: p.Ctx.CreateByType}

			_, byMgmt, err := GetManagedAssistants(ctx, operator)
			if err != nil {
				log.WithContext(ctx).Errorw("HandleBatchUpdateDocTask GetManagedAssistants failed", "err", err)
				return err
			}

			var req ai.ReqUpdateDocAttrInBulk
			iterator := model.NewDocIdsIterator(p.Query.Data, config.GetIntOr("llm.collection.patchUpdateLimit", 0))

			if err := json.Unmarshal(p.Task.Args, &req); err != nil {
				log.WithContext(ctx).Errorw("HandleBatchUpdateDocTask json.Unmarshal req failed", "err", err)
				return fmt.Errorf("HandleBatchUpdateDocTask json.Unmarshal req failed: %v: %w", err, asynq.SkipRetry)
			}

			updateNotAssistant := len(req.GetMask().GetPaths()) != 1 || req.GetMask().Paths[0] != "assistant_id"
			for iterator.HasNext() { // 如果某一部分执行失败了需要继续执行
				if updateNotAssistant {
					req.Id = iterator.SleepDefaultNextBatch()
				} else {
					req.Id = iterator.SleepLongNextBatch()
				}
				if byMgmt {
					if err = UpdateDocAttrInBulk(ctx, &req); err != nil {
						log.WithContext(ctx).Errorw("HandleBatchUpdateDocTask UpdateDocAttrInBulk error", "err", err)
					}
					continue
				}
				if updateNotAssistant { // 如果除了assistantId之外还有其他的要更新则要鉴权
					_, editable, err := SplitDocsByEditPermission(ctx, operator, req.Id...)
					if err != nil {
						log.WithContext(ctx).Errorw("HandleBatchUpdateDocTask SplitDocsByEditPermission error", "err", err)
						continue
					}
					req.Id = editable
				}
				if err = UpdateDocAttrInBulk(ctx, &req); err != nil {
					log.WithContext(ctx).Errorw("HandleBatchUpdateDocTask UpdateDocAttrInBulk error", "err", err)
				}
			}
			return nil
		})
}

// HandleBatchReparseDocTask ...
// CreateReparseBatchTask
func HandleBatchReparseDocTask(ctx context.Context, t *asynq.Task) error {
	return handleBatchOperate(ctx, t, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_REPARSE.String(),
		func(ctx context.Context, p AiBatchOperateDocTaskPayload) error {
			var req ai.ReqReparseTextFiles
			iterator := model.NewDocIdsIterator(p.Query.Data, config.GetIntOr("llm.collection.patchReparseLimit", 0))
			operator := &ai.Operator{Id: p.Ctx.CreateBy, Type: p.Ctx.CreateByType}

			_, byMgmt, err := GetManagedAssistants(ctx, operator)
			if err != nil {
				log.WithContext(ctx).Errorw("HandleBatchReparseDocTask GetManagedAssistants Error", "err", err)
				return err
			}

			if err := json.Unmarshal(p.Task.Args, &req); err != nil {
				log.WithContext(ctx).Errorw("HandleBatchReparseDocTask json.Unmarshal req failed", "err", err)
				return fmt.Errorf("HandleBatchReparseDocTask json.Unmarshal req failed: %v: %w", err, asynq.SkipRetry)
			}
			for iterator.HasNext() { // 如果某一部分执行失败了需要继续执行
				req.Ids = iterator.NextBatch()
				if !byMgmt {
					_, editable, err := SplitDocsByEditPermission(ctx, operator, req.Ids...)
					if err != nil {
						log.WithContext(ctx).Errorw("HandleBatchUpdateDocTask SplitDocsByEditPermission error", "err", err)
						continue
					}
					req.Ids = editable
				}
				if err := ReparseDocInBulk(ctx, &req); err != nil {
					log.WithContext(ctx).Errorw("HandleBatchReparseDocTask ReparseTextFiles error", "err", err)
				}
			}
			return nil
		})
}

// HandleBatchShareDocTask ...
// CreateDocShareBatchTask
func HandleBatchShareDocTask(ctx context.Context, t *asynq.Task) error {
	return handleBatchOperate(ctx, t, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_SHARE.String(),
		func(ctx context.Context, p AiBatchOperateDocTaskPayload) error {
			var req ai.ReqCreateDocShare
			iterator := model.NewDocIdsIterator(p.Query.Data, config.GetIntOr("llm.collection.patchShareLimit", 0))
			operator := &ai.Operator{Id: p.Ctx.CreateBy, Type: p.Ctx.CreateByType}

			if err := json.Unmarshal(p.Task.Args, &req); err != nil {
				log.WithContext(ctx).Errorw("HandleBatchShareDocTask json.Unmarshal req failed", "err", err)
				return fmt.Errorf("HandleBatchShareDocTask json.Unmarshal req failed: %v: %w", err, asynq.SkipRetry)
			}
			for iterator.HasNext() { // 如果某一部分执行失败了需要继续执行
				req.DocIds = iterator.SleepLongNextBatch()
				_, editable, err := SplitDocsByEditPermission(ctx, operator, req.DocIds...)
				if err != nil {
					log.WithContext(ctx).Errorw("HandleBatchShareDocTask SplitDocsByEditPermission error", "err", err)
					continue
				}
				// 只处理有编辑权限的文档
				req.DocIds = editable
				if err = DoCreateDocShare(ctx, &req); err != nil {
					log.WithContext(ctx).Errorw("HandleBatchShareDocTask DoCreateDocShare error", "err", err)
				}
			}
			return nil
		})
}

// CreateCloneBatchTask 创建克隆的批量任务(废弃，没有使用场景)
// HandleBatchCloneDocTask
func CreateCloneBatchTask(ctx context.Context, req *ai.ReqCloneDocInBulk) error {
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		return err
	}
	task, err := createDocBatchTask(ctx, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_CLONE,
		req.QueryId, req.Operator, jsonBytes)
	if err != nil {
		return err
	}
	payload, _ := json.Marshal(task)
	return taskClient.SubmitTask(asynq.NewTask(AiBatchDocTaskClone, payload),
		asynq.Timeout(time.Hour*AiBatchDocTimeoutHour), asynq.MaxRetry(0))
}

// CreateDeleteBatchTask 创建删除的批量任务
// HandleBatchDeleteDocTask
func CreateDeleteBatchTask(ctx context.Context, req *ai.ReqDeleteDocInBulk) (bool, error) {
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		return false, err
	}
	task, err := createDocBatchTask(ctx, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_DELETE,
		req.QueryId, req.Operator, jsonBytes)
	if err != nil {
		return false, err
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		viewable, editable, err := SplitDocsByEditPermission(ctx, req.Operator, task.DisposableIds...)
		if err != nil {
			log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask SplitDocsByEditPermission error", "err", err)
			return err
		}
		if len(viewable) > 0 {
			err = model.Transaction(ctx, func(tx *gorm.DB) error {
				return DeleteDocInBulk(ctx, tx, viewable, false, req.Operator, req.ScopedAssistantId...)
			})
			if err != nil {
				log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask OnOffDocInBulk error", "err", err)
				return err
			}
		}
		if len(editable) > 0 {
			err = model.Transaction(ctx, func(tx *gorm.DB) error {
				return DeleteDocInBulk(ctx, tx, editable, true, nil)
			})
			if err != nil {
				log.WithContext(ctx).Errorw("HandleBatchDeleteDocTask OnOffDocInBulk error", "err", err)
				return err
			}
		}
		return nil
	})
	if err != nil || task.Task == nil { // 不需要创建异步任务了直接同步执行
		return false, err
	}

	payload, err := json.Marshal(task)
	if err != nil {
		return false, err
	}
	return true, taskClient.SubmitTask(asynq.NewTask(AiBatchDocTaskDelete, payload),
		asynq.Timeout(time.Hour*AiBatchDocTimeoutHour), asynq.MaxRetry(0))
}

// CreateOnOffBatchTask 创建启用停用的批量任务
// HandleBatchOnOffDocTask
func CreateOnOffBatchTask(ctx context.Context, req *ai.ReqOnOffDocInBulk, rsp *ai.RspOnOffDocInBulk) (bool, error) {
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		return false, err
	}

	task, err := createDocBatchTask(ctx, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_ON_OFF,
		req.QueryId, req.Operator, jsonBytes)
	if err != nil {
		return false, err
	}

	var errorIds []uint64
	req.Id = task.DisposableIds
	// 启用预处理，校验重复
	if req.State == ai.DocState_DOC_STATE_ENABLED {
		err = CheckDocBeforeEnable(ctx, req, rsp)
		if err != nil {
			return false, err
		}
		for _, v := range rsp.PreRepeatCollections {
			keys := maps.Keys(v.FileName)
			if len(keys) > 1 {
				errorIds = append(errorIds, keys[1:]...)
			}
		}
		for _, v := range rsp.RepeatCollections {
			keys := maps.Keys(v.FileName)
			if len(keys) > 1 {
				errorIds = append(errorIds, keys[1:]...)
			}
		}
		for _, v := range rsp.ExceedQaContainsMatchLimit {
			errorIds = append(errorIds, v.DocIds...)
		}
		req.Id = DifferenceSet(req.Id, errorIds)
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		return OnOffDocInBulk(ctx, tx, req.State, req.Id, req.ScopedAssistantId...)
	})
	if err != nil || task.Task == nil { // 不需要创建异步任务了直接同步执行
		return false, err
	}

	payload, err := json.Marshal(task)
	if err != nil {
		return false, err
	}
	return true, taskClient.SubmitTask(asynq.NewTask(AiBatchDocTaskOnOff, payload),
		asynq.Timeout(time.Hour*AiBatchDocTimeoutHour), asynq.MaxRetry(0))
}

// CreateUpdateBatchTask 创建更新的批量任务
// HandleBatchUpdateDocTask
func CreateUpdateBatchTask(ctx context.Context, req *ai.ReqUpdateDocAttrInBulk) (bool, error) {
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		return false, err
	}
	task, err := createDocBatchTask(ctx, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_UPDATE, req.QueryId, req.UpdateBy, jsonBytes)
	if err != nil {
		return false, err
	}

	req.Id = task.DisposableIds
	err = UpdateDocAttrInBulk(ctx, req)
	if err != nil || task.Task == nil { // 不需要创建异步任务了直接同步执行
		return false, err
	}

	payload, err := json.Marshal(task)
	if err != nil {
		return false, err
	}
	return true, taskClient.SubmitTask(asynq.NewTask(AiBatchDocTaskUpdate, payload),
		asynq.Timeout(time.Hour*AiBatchDocTimeoutHour), asynq.MaxRetry(0))
}

// CreateReparseBatchTask 创建重新解析的批量任务
// HandleBatchReparseDocTask
func CreateReparseBatchTask(ctx context.Context, req *ai.ReqReparseTextFiles) (bool, error) {
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		return false, err
	}
	task, err := createDocBatchTask(ctx, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_REPARSE,
		req.QueryId, req.Operator, jsonBytes)
	if err != nil {
		return false, err
	}

	req.Ids = task.DisposableIds
	err = ReparseDocInBulk(ctx, req)
	if err != nil || task.Task == nil { // 不需要创建异步任务了直接同步执行
		return false, err
	}

	payload, err := json.Marshal(task)
	if err != nil {
		return false, err
	}
	return true, taskClient.SubmitTask(asynq.NewTask(AiBatchDocTaskReparse, payload),
		asynq.Timeout(time.Hour*AiBatchDocTimeoutHour), asynq.MaxRetry(0))
}

// CreateDocShareBatchTask 创建重新解析的批量任务
// HandleBatchShareDocTask
func CreateDocShareBatchTask(ctx context.Context, req *ai.ReqCreateDocShare) (bool, error) {
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		return false, err
	}

	task, err := createDocBatchTask(ctx, ai.DocBatchTaskType_DOC_BATCH_TASK_TYPE_SHARE,
		req.QueryId, req.Operator, jsonBytes)
	if err != nil {
		return false, err
	}

	req.DocIds = task.DisposableIds
	err = DoCreateDocShare(ctx, req)
	if err != nil || task.Task == nil { // 不需要创建异步任务了直接同步执行
		return false, err
	}

	payload, err := json.Marshal(task)
	if err != nil {
		return false, err
	}
	return true, taskClient.SubmitTask(asynq.NewTask(AiBatchDocTaskShare, payload),
		asynq.Timeout(time.Hour*AiBatchDocTimeoutHour), asynq.MaxRetry(0))
}

// DifferenceSet arr1 - arr2 差集
func DifferenceSet[T constraints.Ordered](arr1, arr2 []T) []T {
	if len(arr2) == 0 {
		return arr1
	}
	// 用 map 存储 arr2 中的元素，方便快速查找
	exists := make(map[T]struct{})
	for _, v := range arr2 {
		exists[v] = struct{}{}
	}

	// 遍历 arr1，找出不存在于 arr2 中的元素
	var result []T
	for _, v := range arr1 {
		if _, found := exists[v]; !found {
			result = append(result, v)
		}
	}

	return result
}
