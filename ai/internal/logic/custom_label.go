package logic

import (
	"context"
	"fmt"
	"math"
	"slices"
	"strconv"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	pberrors "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// UpdateObjectLabels 更新对象的标签
func UpdateObjectLabels(ctx context.Context, req *aipb.ReqUpdateCustomChatLabels) error {
	if len(req.ObjectId) == 0 || len(req.Labels) == 0 {
		return nil
	}
	rows := make([]*model.TObjectLabel, 0)
	for _, v := range req.ObjectId {
		for _, label := range req.Labels {
			rows = append(rows, (&model.TObjectLabel{}).FromLabelPb(v, req.ObjectType, label))
		}
	}
	mc, noMc := SplitMcLabels(rows)
	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		// 这里把label_type也更新进去，方便之后查询
		// 标签取单个值，直接 upsert
		if len(noMc) != 0 {
			err := tx.Model(&model.TObjectLabel{}).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "object_id"}, {Name: "label_id"}, {Name: "mc_sort"}},
				DoUpdates: clause.AssignmentColumns([]string{"int_value", "uint_value", "float_value", "string_value", "label_type", "deleted_at", "mc_sort"}),
			}).Create(rows).Error
			if err != nil {
				return err
			}
		}

		// 标签可以取多值，先删旧数据，插入新数据
		if len(mc) != 0 {
			deleteSQL := `DELETE FROM t_object_label WHERE (object_id, label_id) IN (%s)`

			// 使用 map 去重
			uniqueLabels := make(map[[2]interface{}]bool)
			for _, label := range mc {
				uniqueLabels[[2]interface{}{label.ObjectID, label.LabelID}] = true
			}

			// 构造参数和占位符
			var values []interface{}
			var placeholders []string
			for key := range uniqueLabels {
				placeholders = append(placeholders, "(?, ?)")
				values = append(values, key[0], key[1])
			}

			// 拼接 SQL
			finalSQL := fmt.Sprintf(deleteSQL, strings.Join(placeholders, ","))

			// 执行原生 SQL
			if err := tx.Exec(finalSQL, values...).Error; err != nil {
				return err
			}

			// for _, label := range mc {
			// 	err := tx.Model(&model.TObjectLabel{}).
			// 		Where("object_id = ?", label.ObjectID).
			// 		Where("label_id = ?", label.LabelID).
			// 		Where("object_type = ?", label.ObjectType).
			// 		Delete(nil).Error
			// 	if err != nil {
			// 		return err
			// 	}
			// }
			model.LabelMcSort(mc)
			err := tx.Model(&model.TObjectLabel{}).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "object_id"}, {Name: "label_id"}, {Name: "mc_sort"}},
				DoUpdates: clause.AssignmentColumns([]string{"int_value", "uint_value", "float_value", "string_value", "label_type", "deleted_at", "mc_sort"}),
			}).Create(rows).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

// ReplaceObjectLabels 替换绑定的所有标签值
func ReplaceObjectLabels(tx *gorm.DB, objectId, tenantId uint64, objType aipb.CustomLabelObjectType, labels []*model.TObjectLabel) error {
	model.LabelMcSort(labels)
	allLabelId := tx.Model(&model.TCustomLabel{}).Where("tenant_id = ?", tenantId).Select("id")
	err := tx.Model(&model.TObjectLabel{}).
		Where("object_id = ?", objectId).
		Where("label_id IN (?)", allLabelId).
		Where("object_type = ?", objType).Delete(nil).Error
	// 插入新标签关系
	if len(labels) != 0 {
		tx.Model(&model.TObjectLabel{}).Clauses(clause.OnConflict{
			DoUpdates: clause.AssignmentColumns([]string{"int_value", "uint_value", "float_value", "string_value", "label_type", "deleted_at"}),
		}).Create(labels)
	}
	return err
}

// SplitMcLabels 按照是否是多选项标签分组
func SplitMcLabels(labels []*model.TObjectLabel) (mc, noMc []*model.TObjectLabel) {
	for _, label := range labels {
		if label.LabelType == aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M {
			mc = append(mc, label)
		} else {
			noMc = append(noMc, label)
		}
	}
	return
}

var (
	// InvalidCustomLabelConvertErr 非法的自定义标签转换
	InvalidCustomLabelConvertErr = xerrors.NewCode(pberrors.AiError_AiInvalidCustomLabelConvert)
)

// LabelConvertLogic 标签类型转换逻辑
type LabelConvertLogic struct {
	ctx    context.Context
	source *model.TCustomLabel
	target *model.TCustomLabel

	dryRun bool
}

// NewLabelConvertLogic new
func NewLabelConvertLogic(ctx context.Context, source *model.TCustomLabel, target *model.TCustomLabel, dryRun bool) *LabelConvertLogic {
	return &LabelConvertLogic{
		ctx:    ctx,
		source: source,
		target: target,
		dryRun: dryRun,
	}
}

// ResetLabelInfo 变更标签的信息
func (l *LabelConvertLogic) ResetLabelInfo() error {
	if l.dryRun {
		return nil
	}

	l.target.ID = l.source.ID
	err := model.NewQuery[model.TCustomLabel](l.ctx).DB().Where("id = ?", l.target.ID).
		Select("value", "type").Updates(l.target).Error
	if err != nil {
		return err
	}
	return nil
}

// ConvertLabelValue 转换标签已绑定的值
func (l *LabelConvertLogic) ConvertLabelValue() (*LabelConvertAffectedRows, error) {
	wrapper := NewLabelValueConverterWrapper(l.ctx, l.source, l.target)
	affected, err := wrapper.Convert(l.dryRun)
	if err != nil {
		return nil, err
	}
	return affected, nil
}

// LabelConvertAffectedRows 标签转换影响行
type LabelConvertAffectedRows struct {
	Reserved int
	Deleted  int
}

type LabelValueConverter interface {
	// Convert 实际数值转换逻辑
	Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool)
}

type LabelValueConverterWrapper struct {
	ctx       context.Context
	source    *model.TCustomLabel
	target    *model.TCustomLabel
	converter LabelValueConverter
}

type LabelTypeConvertTuple struct {
	sourceType aipb.CustomLabelType
	targetType aipb.CustomLabelType
}

var converterMapping = map[LabelTypeConvertTuple]LabelValueConverter{
	// 源格式：字符串
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(text2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(text2emConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      new(text2intConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     new(text2uintConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    new(text2floatConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(text2yearConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  new(text2ymConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: new(text2ymdConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: new(text2datetimeConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     new(text2timeConverter),

	// 源格式: 多选
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(emm2emConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(emm2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      new(emm2intConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     new(emm2uintConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    new(emm2floatConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(emm2yearConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  new(emm2ymConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: new(emm2ymdConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: new(emm2datetimeConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     new(emm2timeConverter),

	// 源格式： 单选
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(em2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(em2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      new(em2intConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     new(em2uintConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    new(em2floatConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(em2YearConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  new(em2YmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: new(em2YmdConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: new(em2DatetimeConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     new(em2timeConverter),

	// 源格式： 整数
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(int2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(int2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(int2emConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     new(int2uintConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    new(int2floatConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(int2yearConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     nil,

	// 源格式：无符号整数
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(uint2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(uint2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(uint2emConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      new(uint2intConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    new(uint2floatConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(uint2yearConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     nil,

	// 源格式：浮点数
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(float2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(float2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(float2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      new(float2intConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     new(float2uintConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(InvalidLabelConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  new(InvalidLabelConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: new(InvalidLabelConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: new(InvalidLabelConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     new(InvalidLabelConverter),

	// 源格式：年
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(year2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(year2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(year2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      new(year2intConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     new(year2uintConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    new(year2floatConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  new(year2ymConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: new(year2ymdConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: new(year2datetimeConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     new(InvalidLabelConverter),

	// 源格式：年月
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(ym2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(ym2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(ym2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(ym2yearConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: new(ym2ymdConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: new(ym2datetimeConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     new(InvalidLabelConverter),

	// 源格式：年月日
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(ymd2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(ymd2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(ymd2emmConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(ymd2yearConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  new(ymd2ymConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME}: new(ymd2datetimeConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     new(InvalidLabelConverter),

	// 源格式：日期时间
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M}:   new(InvalidLabelConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT}:     new(datetime2textConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM}:     new(InvalidLabelConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT}:      nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT}:     nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT}:    nil,
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y}:   new(datetime2yConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM}:  new(datetime2ymConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD}: new(datetime2ymdConverter),
	LabelTypeConvertTuple{sourceType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, targetType: aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME}:     new(datetime2timeConverter),
}

// NewLabelValueConverterWrapper 标签值转换包装器
func NewLabelValueConverterWrapper(ctx context.Context, source *model.TCustomLabel, target *model.TCustomLabel) *LabelValueConverterWrapper {
	wrapper := &LabelValueConverterWrapper{
		ctx:    ctx,
		source: source,
		target: target,
		converter: converterMapping[LabelTypeConvertTuple{
			sourceType: source.Type,
			targetType: target.Type,
		}],
	}
	return wrapper
}

func (l *LabelValueConverterWrapper) scanDB(page, pageSize int) ([]*model.TObjectLabel, error) {
	where := &model.TObjectLabel{
		LabelID: l.source.ID,
	}
	pageRows := make([]*model.TObjectLabel, 0)
	err := model.NewQuery[model.TObjectLabel](l.ctx).
		DB().
		Where(where).
		Limit(pageSize).
		Offset(page * pageSize).
		Find(&pageRows).Error
	if err != nil {
		return nil, err
	}
	return pageRows, nil
}

// Convert 执行标签数据值转换
// 1. 查找
// 2. 判断保留和删除
// 3. 删除
// 4. 保留转换
func (l *LabelValueConverterWrapper) Convert(dryRun bool) (*LabelConvertAffectedRows, error) {
	const pageSize = 500
	affectedRows := &LabelConvertAffectedRows{}
	delObject := make(map[uint64]struct{})
	reserveObject := make(map[uint64]struct{})

	if l.converter == nil {
		return affectedRows, InvalidCustomLabelConvertErr
	}
	if _, ok := l.converter.(*InvalidLabelConverter); ok {
		return affectedRows, InvalidCustomLabelConvertErr
	}

	page := 0

	err := xorm.DB(l.ctx).Transaction(func(tx *gorm.DB) error {
		for {
			// 分页获取数据
			pageRows, err := l.scanDB(page, pageSize)
			if err != nil {
				return err
			}
			if len(pageRows) == 0 {
				break
			}
			page++

			var toDel, toReserved []*model.TObjectLabel
			for _, v := range pageRows {
				if v.LabelType == l.target.Type {
					continue
				}
				// 多选项情况：已经有数值符合要求
				_, has := reserveObject[v.ObjectID]
				ok := false
				var converted *model.TObjectLabel
				if !has {
					converted, ok = l.converter.Convert(v, l.target)
					if ok {
						toReserved = append(toReserved, converted)
						// 该记录在多选时被保留，则不计入删除的行
						reserveObject[v.ObjectID] = struct{}{}
						delete(delObject, v.ObjectID)
						continue
					}
				}
				if has || !ok {
					v.DeletedAt = gorm.DeletedAt{}
					v.DeletedAt.Scan(time.Now())
					toDel = append(toDel, v)
					delObject[v.ObjectID] = struct{}{}
				}
			}

			if dryRun {
				continue
			}

			err = tx.Model(&model.TObjectLabel{}).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "object_id"}, {Name: "label_id"}, {Name: "object_type"}, {Name: "mc_sort"}},
				DoUpdates: clause.AssignmentColumns([]string{"deleted_at"}),
			}).Create(toDel).Error
			if err != nil {
				return err
			}

			columns := []string{"label_type", "mc_sort", "int_value", "uint_value", "float_value", "string_value"}
			err = tx.Model(&model.TObjectLabel{}).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "object_id"}, {Name: "label_id"}, {Name: "object_type"}, {Name: "mc_sort"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(toReserved).Error
			if err != nil {
				return err
			}

			// 如果本页数量少于 pageSize，说明没有更多数据
			if len(pageRows) < pageSize {
				break
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	affectedRows.Deleted = len(delObject)
	affectedRows.Reserved = len(reserveObject)
	return affectedRows, nil
}

const (
	LabelEmmDelimiter = ";"
)

// splitEmmLabelValue 切割多选项的选项值
func splitEmmLabelValue(v string) []string {
	return strings.Split(v, LabelEmmDelimiter)
}

// inEmmLabelValue 判断是否在多选项中
func inEmmLabelValue(v string, emm string) bool {
	s := splitEmmLabelValue(emm)
	if len(s) == 0 {
		return false
	}
	if !slices.Contains(s, v) {
		return false
	}
	return true
}

// InvalidLabelConverter 非法的转换
type InvalidLabelConverter struct {
}

// Convert 永远返回false
func (c *InvalidLabelConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	return old, false
}

// text2emmConverter 文本转多选
type text2emmConverter struct{}

// Convert 只需改变label type
func (c *text2emmConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	if !inEmmLabelValue(old.StringValue, target.Value) {
		return old, false
	}

	back := *old
	new = &back
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M
	return new, true
}

// text2emConverter 文本转单选
type text2emConverter struct{}

// Convert 只需改变label type
func (c *text2emConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	if old.StringValue != target.Value {
		return old, false
	}

	back := *old
	new = &back

	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	return new, true
}

// 文本转整数
type text2intConverter struct{}

func (c *text2intConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToInt64E(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT
	new.IntValue = v
	return new, true
}

// 文本转无符号整数
type text2uintConverter struct{}

func (c *text2uintConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToUint64E(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT
	new.UINtValue = v
	return new, true
}

// 文本转浮点
type text2floatConverter struct{}

func (c *text2floatConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToFloat64E(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT
	new.FloatValue = v
	return new, true
}

const (
	MinYear = 0
	MaxYear = 9999
)

var (
	// tzCST 东八区
	tzCST          = time.FixedZone("CST", 8*60*60)
	dateYmLayout   = "2006-01"
	dateYmdLayout  = "2006-01-02"
	dateTimeLayout = "2006-01-02 15:04"
	timeLayout     = "15:04"
)

// parseYear 解析年份
func parseYear(v any) (int64, error) {
	checkYearRange := func(year int64) bool {
		return year >= MinYear && year <= MaxYear
	}

	var year int64
	var err error

	switch v := v.(type) {
	case string:
		year, err = strconv.ParseInt(v, 10, 64)
		if err != nil {
			return 0, fmt.Errorf("invalid year string: %w", err)
		}
	case int64:
		year = v
	case uint64:
		year = int64(v)
	default:
		return 0, fmt.Errorf("unsupported year type %T", v)
	}

	valid := checkYearRange(year)
	if !valid {
		return 0, fmt.Errorf("invalid year range: %d", year)
	}
	t := time.Date(int(year), 1, 1, 0, 0, 0, 0, tzCST)
	return t.Unix(), nil
}

// parseYm 解析年月
func parseYm(v any) (int64, error) {
	var t time.Time
	var err error

	switch v := v.(type) {
	case string:
		t, err = time.ParseInLocation(dateYmLayout, v, tzCST)
		if err != nil {
			return 0, fmt.Errorf("invalid ym string: %w", err)
		}
	default:
		return 0, fmt.Errorf("unsupported type %T", v)
	}

	return t.Unix(), nil
}

// parseYmd 解析年月日
func parseYmd(v any) (int64, error) {
	var t time.Time
	var err error

	switch v := v.(type) {
	case string:
		t, err = time.ParseInLocation(dateYmdLayout, v, tzCST)
		if err != nil {
			return 0, fmt.Errorf("invalid ymd string: %w", err)
		}
	default:
		return 0, fmt.Errorf("unsupported type %T", v)
	}

	return t.Unix(), nil
}

// parseDateTime 解析年月日 时间
func parseDateTime(v any) (int64, error) {
	var t time.Time
	var err error

	switch v := v.(type) {
	case string:
		t, err = time.ParseInLocation(dateTimeLayout, v, tzCST)
		if err != nil {
			return 0, fmt.Errorf("invalid datetime string: %w", err)
		}
	default:
		return 0, fmt.Errorf("unsupported type %T", v)
	}

	return t.Unix(), nil
}

// parseTime 解析时间,返回的时间戳，以unix起点时间为基准
func parseTime(v any) (int64, error) {
	var t time.Time
	var err error

	switch v := v.(type) {
	case string:
		t, err = time.ParseInLocation(timeLayout, v, tzCST)
		if err != nil {
			return 0, fmt.Errorf("invalid datetime string: %w", err)
		}
	case int64:
		t = time.Unix(v, 0).In(tzCST)
	default:
		return 0, fmt.Errorf("unsupported type %T", v)
	}
	// 以unix时间为基准
	t = time.Date(1970, 1, 1, t.Hour(), t.Minute(), 0, 0, tzCST)
	return t.Unix(), nil
}

func deParseYear(unixSecond int64) (int, error) {
	t, err := cast.ToTimeInDefaultLocationE(unixSecond, tzCST)
	if err != nil {
		return 0, err
	}
	return t.Year(), nil
}

func deParseYm(unixSecond int64) (y int, m int, ymStr string, err error) {
	t, err := cast.ToTimeInDefaultLocationE(unixSecond, tzCST)
	if err != nil {
		return 0, 0, "", err
	}
	return t.Year(), int(t.Month()), t.Format(dateYmLayout), nil
}

func deParseYmd(unixSecond int64) (y int, m int, d int, ymdStr string, err error) {
	t, err := cast.ToTimeInDefaultLocationE(unixSecond, tzCST)
	if err != nil {
		return 0, 0, 0, "", err
	}
	return t.Year(), int(t.Month()), t.Day(), t.Format(dateYmdLayout), nil
}

func deParseDatetime(unixSecond int64) (ymdStr string, err error) {
	t, err := cast.ToTimeInDefaultLocationE(unixSecond, tzCST)
	if err != nil {
		return "", err
	}
	return t.Format(dateTimeLayout), nil
}

// 文本转年
type text2yearConverter struct{}

func (c *text2yearConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseYear(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y
	new.IntValue = v
	return new, true
}

// 文本转年月
type text2ymConverter struct{}

func (c *text2ymConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseYm(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM
	new.IntValue = v
	return new, true
}

// 文本转年月日
type text2ymdConverter struct{}

func (c *text2ymdConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseYmd(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD
	new.IntValue = v
	return new, true
}

// 文本转日期时间
type text2datetimeConverter struct{}

func (c *text2datetimeConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseDateTime(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME
	new.IntValue = v
	return new, true
}

// 文本转日期时间
type text2timeConverter struct{}

func (c *text2timeConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseTime(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME
	new.IntValue = v
	return new, true
}

// 多选转文本
type emm2textConverter struct{}

func (c *emm2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	// 多选只保留一个值，默认选第一个
	if old.McSort != 0 {
		return old, false
	}
	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

// 多选转单选
type emm2emConverter struct{}

func (c *emm2emConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	if old.StringValue != target.Value {
		return old, false
	}
	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	return new, true
}

// 多选转int
type emm2intConverter struct{}

func (c *emm2intConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToInt64E(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT
	return new, true
}

// 多选转uint
type emm2uintConverter struct{}

func (c *emm2uintConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToUint64E(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.UINtValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT
	return new, true
}

// 多选转float
type emm2floatConverter struct{}

func (c *emm2floatConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToFloat64E(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.FloatValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT
	return new, true
}

// 多选转年月
type emm2ymConverter struct{}

func (c *emm2ymConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseYm(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM
	return new, true
}

// 多选转年月日
type emm2ymdConverter struct{}

func (c *emm2ymdConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseYmd(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD
	return new, true
}

// 多选转时间
type emm2timeConverter struct{}

func (c *emm2timeConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseTime(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME
	return new, true
}

// 多选转日期时间
type emm2datetimeConverter struct{}

func (c *emm2datetimeConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseDateTime(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME
	return new, true
}

// 多选转year
type emm2yearConverter struct{}

func (c *emm2yearConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseYear(old.StringValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y
	return new, true
}

// 单选转纯文本
type em2textConverter struct{}

func (c *em2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

// 单选转多选
type em2emmConverter = text2emmConverter

// 单选转多选
type em2intConverter = text2intConverter

// 单选转uint
type em2uintConverter = text2uintConverter

// 单选转float
type em2floatConverter = text2floatConverter

// 单选转年
type em2YearConverter = text2yearConverter

// 单选转年月
type em2YmConverter = text2ymConverter

// 单选转年日
type em2YmdConverter = text2ymdConverter

// 单选日期时间
type em2DatetimeConverter = text2datetimeConverter

// 单选转时间
type em2timeConverter = text2timeConverter

// int转文本
type int2textConverter struct {
}

func (c *int2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	new = old
	new.StringValue = strconv.FormatInt(old.IntValue, 10)
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

// int转多选
type int2emmConverter struct {
}

func (c *int2emmConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	str := strconv.FormatInt(old.IntValue, 10)
	if !inEmmLabelValue(str, target.Value) {
		return old, false
	}

	new = old
	new.StringValue = strconv.FormatInt(old.IntValue, 10)
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M
	return new, true
}

// int转单选
type int2emConverter struct {
}

func (c *int2emConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	str := strconv.FormatInt(old.IntValue, 10)
	if str != target.Value {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	return new, true
}

// int转单选
type int2uintConverter struct {
}

func (c *int2uintConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToUint64E(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.UINtValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT
	return new, true
}

// int转float
type int2floatConverter struct {
}

func (c *int2floatConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToFloat64E(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.FloatValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT
	return new, true
}

// int转年
type int2yearConverter struct {
}

func (c *int2yearConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	year, err := parseYear(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = year
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y
	return new, true
}

// int转年月
// type int2ymConverter struct {
// }
//
// func (c *int2ymConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
// 	ym, err := parseYm(old.IntValue)
// 	if err != nil {
// 		return old, false
// 	}
//
// 	new = old
// 	new.IntValue = ym
// 	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM
// 	return new, true
// }

// uint转文本
type uint2textConverter struct {
}

func (c *uint2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	new = old
	new.StringValue = strconv.FormatUint(old.UINtValue, 10)
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

// uint转多选
type uint2emmConverter struct {
}

func (c *uint2emmConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	str := strconv.FormatUint(old.UINtValue, 10)
	if !inEmmLabelValue(str, target.Value) {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M
	return new, true
}

// uint转单选
type uint2emConverter struct {
}

func (c *uint2emConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	str := strconv.FormatUint(old.UINtValue, 10)
	if str != target.Value {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	return new, true
}

// uint转int
type uint2intConverter struct {
}

func (c *uint2intConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToInt64E(old.UINtValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT
	return new, true
}

// uint转float
type uint2floatConverter struct {
}

func (c *uint2floatConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToFloat64E(old.UINtValue)
	if err != nil {
		return old, false
	}

	new = old
	new.FloatValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT
	return new, true
}

// uint转year
type uint2yearConverter struct {
}

func (c *uint2yearConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseYear(old.UINtValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y
	return new, true
}

// float转文本
type float2textConverter struct {
}

func (c *float2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	new = old
	new.StringValue = strconv.FormatFloat(old.FloatValue, 'f', 2, 64)
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

// float转多选
type float2emmConverter struct {
}

func (c *float2emmConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	str := strconv.FormatFloat(old.FloatValue, 'f', 2, 64)
	if !inEmmLabelValue(str, target.Value) {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M
	return new, true
}

// float转单选
type float2emConverter struct {
}

func (c *float2emConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	str := strconv.FormatFloat(old.FloatValue, 'f', 2, 64)
	if str != target.Value {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	return new, true
}

// float转int
type float2intConverter struct {
}

func (c *float2intConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToInt64E(math.Round(old.FloatValue))
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT
	return new, true
}

// float转uint
type float2uintConverter struct {
}

func (c *float2uintConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := cast.ToUint64E(math.Round(old.FloatValue))
	if err != nil {
		return old, false
	}

	new = old
	new.UINtValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT
	return new, true
}

/************************* 时间互转 *********************/

// anyT2ymConverter 任意时间类型转年
type anyT2yConverter struct {
}

func (c *anyT2yConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y
	return new, true
}

// anyT2ymConverter 任意时间类型转年月
type anyT2ymConverter struct {
}

func (c *anyT2ymConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM
	return new, true
}

// anyT2ymdConverter 任意时间类型转年月日
type anyT2ymdConverter struct {
}

func (c *anyT2ymdConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD
	return new, true
}

// anyT2datetimeConverter 任意时间类型转日期时间
type anyT2datetimeConverter struct {
}

func (c *anyT2datetimeConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	new = old
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME
	return new, true
}

// year转文本
type year2textConverter struct {
}

func (c *year2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	year, err := deParseYear(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.StringValue = strconv.FormatInt(int64(year), 10)
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

// year转单选
type year2emConverter struct {
}

func (c *year2emConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	year, err := deParseYear(old.IntValue)
	if err != nil {
		return old, false
	}
	str := strconv.FormatInt(int64(year), 10)
	if str != target.Value {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	return new, true
}

// year转多选
type year2emmConverter struct {
}

func (c *year2emmConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	year, err := deParseYear(old.IntValue)
	if err != nil {
		return old, false
	}
	str := strconv.FormatInt(int64(year), 10)
	if !inEmmLabelValue(str, target.Value) {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M
	return new, true
}

// year转int
type year2intConverter struct {
}

func (c *year2intConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	year, err := deParseYear(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = int64(year)
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT
	return new, true
}

// year转uint
type year2uintConverter struct {
}

func (c *year2uintConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	year, err := deParseYear(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.UINtValue = uint64(year)
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT
	return new, true
}

// year转float
type year2floatConverter struct {
}

func (c *year2floatConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	year, err := deParseYear(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.FloatValue = float64(year)
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT
	return new, true
}

// year转ym
type year2ymConverter = anyT2ymConverter

// year转ymd
type year2ymdConverter = anyT2ymdConverter

// year转datetime
type year2datetimeConverter = anyT2datetimeConverter

// 年月转文本
type ym2textConverter struct {
}

func (c *ym2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	_, _, str, err := deParseYm(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

// 年月转单选
type ym2emConverter struct {
}

func (c *ym2emConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	_, _, str, err := deParseYm(old.IntValue)
	if err != nil {
		return old, false
	}
	if str != target.Value {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	return new, true
}

// 年月转多选
type ym2emmConverter struct {
}

func (c *ym2emmConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	_, _, str, err := deParseYm(old.IntValue)
	if err != nil {
		return old, false
	}
	if !inEmmLabelValue(str, target.Value) {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M
	return new, true
}

// 年月转年
type ym2yearConverter = anyT2yConverter

// 年月转年日
type ym2ymdConverter = anyT2ymdConverter

// 年月转日期时间
type ym2datetimeConverter = anyT2datetimeConverter

// 年月日转文本
type ymd2textConverter struct {
}

func (c *ymd2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	_, _, _, str, err := deParseYmd(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

// 年月日转单选
type ymd2emConverter struct {
}

func (c *ymd2emConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	_, _, _, str, err := deParseYmd(old.IntValue)
	if err != nil {
		return old, false
	}
	if str != target.Value {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	return new, true
}

// 年月日转多选
type ymd2emmConverter struct {
}

func (c *ymd2emmConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	_, _, _, str, err := deParseYmd(old.IntValue)
	if err != nil {
		return old, false
	}
	if !inEmmLabelValue(str, target.Value) {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M
	return new, true
}

type ymd2yearConverter = anyT2yConverter

type ymd2ymConverter = anyT2ymConverter

type ymd2datetimeConverter = anyT2datetimeConverter

// 日期时间转文本
type datetime2textConverter struct {
}

func (c *datetime2textConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	str, err := deParseDatetime(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.StringValue = str
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	return new, true
}

type datetime2yConverter = anyT2yConverter

type datetime2ymConverter = anyT2ymConverter

type datetime2ymdConverter = anyT2ymdConverter

// 日期时间转时间
type datetime2timeConverter struct {
}

// Convert 设置成1970年1月1日
func (c *datetime2timeConverter) Convert(old *model.TObjectLabel, target *model.TCustomLabel) (new *model.TObjectLabel, reserve bool) {
	v, err := parseTime(old.IntValue)
	if err != nil {
		return old, false
	}

	new = old
	new.IntValue = v
	new.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME
	return new, true
}
