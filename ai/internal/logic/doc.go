package logic

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcache"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	pberrors "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"github.com/google/uuid"
	"github.com/tencentyun/cos-go-sdk-v5"
	"golang.org/x/exp/slices"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	// ErrAiCollectionQuestionExisted question已经存在
	ErrAiCollectionQuestionExisted = xerrors.NewCode(pberrors.AiError_AiCollectionQuestionExisted)
	ErrAiCollectionTextFileExisted = xerrors.NewCode(pberrors.AiError_AiCollectionTextFileExisted)
)

const (
	docFilePathPattern = "doc"
	fileSplitPattern   = "file_split"
	// EmptyStringMD5 空字符串的MD5值
	EmptyStringMD5 = "d41d8cd98f00b204e9800998ecf8427e"
)

// GenDocRagFileName 生成doc在rag存储的文件名称
func GenDocRagFileName() string {
	return uuid.NewString() + ".txt"
}

// GenRagFileName 生成文件的rag存储名（自动识别文件后缀版）
func GenRagFileName(filename string) string {
	name := uuid.NewString()
	if ext := path.Ext(filename); ext != "" {
		return name + ext
	}
	return name
}

// GetCollectionEditThreshold 获取edit collection时的阈值
func GetCollectionEditThreshold() float32 {
	return config.GetFloat32Or("llm.collection.edit_threshold", 0.95)
}

func InsertRagDataInBulk(ctx context.Context, tx *gorm.DB, new []*model.TDoc, scopedAssistants ...uint64) error {
	ids := make([]uint64, 0, len(new))
	for _, v := range new {
		ids = append(ids, v.ID)
	}
	if len(ids) == 0 {
		return nil
	}
	news, err := model.LoadDocWithTx(tx, ids, nil, scopedAssistants...)
	if err != nil {
		return err
	}
	err = SyncDocToDBLogBulk(ctx, tx, nil, news)
	if err != nil {
		log.WithContext(ctx).Errorf("Upsert collection rag data failed: %v, Id: %v", err, ids)
		return err
	}
	return nil
}

// DeleteRagDataInBulk 删除 rag 数据，必须在删除States关联表之前
// scopedAssistants 只删除指定助手的 rag 数据，其余助手 rag 数据不改变
func DeleteRagDataInBulk(ctx context.Context, tx *gorm.DB, docIds []uint64, isDeleteDoc bool, scopedAssistants ...uint64) error {
	// TODO: selects 是否可以只限定id？
	old, err := model.LoadDocWithTx(tx, docIds, nil, scopedAssistants...)
	if err != nil {
		return err
	}
	if len(old) == 0 {
		return nil
	}

	err = SyncDocToDBLogBulk(ctx, tx, old, nil, SyncDocToDBLogBulkOpt{IsDelete: isDeleteDoc})
	if err != nil {
		log.WithContext(ctx).Errorf("Delete collection rag data  failed: %v, Id: %v", err, docIds)
		return err
	}
	return nil
}

// UpdateRagDataHint 更新ragdata时指定优化提示
type UpdateRagDataHint struct {
	// 文本没有变更
	NoIndexTextUpdate bool
}

// UpdateRagDataInBulk 会从数据库读取最新的 doc 配置，自动判断是否需要同步向量数据
func UpdateRagDataInBulk(ctx context.Context, tx *gorm.DB, new []*model.TDoc, old []*model.TDoc, hint UpdateRagDataHint, scopedAssistantId ...uint64) error {
	ids := make([]uint64, 0, len(new))
	for _, v := range new {
		ids = append(ids, v.ID)
	}
	if len(ids) == 0 {
		return nil
	}
	news, err := model.LoadDocWithTx(tx, ids, nil, scopedAssistantId...)
	if err != nil {
		return err
	}
	// 没有index text更新，强制将old的 index text设置和new的一模一样，在特定情况可以减少old的数据库数据读取
	slices.SortFunc(old, func(a, b *model.TDoc) int {
		return int(a.ID - b.ID)
	})
	slices.SortFunc(new, func(a, b *model.TDoc) int {
		return int(a.ID - b.ID)
	})
	if hint.NoIndexTextUpdate {
		for i := range old {
			if old[i] != nil {
				old[i].IndexText = news[i].IndexText
				old[i].Text = news[i].Text
			}
		}
	}

	err = SyncDocToDBLogBulk(ctx, tx, old, news)
	if err != nil {
		log.WithContext(ctx).Errorf("Update collection rag data bulk failed: %v,doc id: %d", err, ids)
		return err
	}
	return nil
}

func UpdateRagData(ctx context.Context, tx *gorm.DB, new *model.TDoc, old *model.TDoc, scopedAssistantId ...uint64) error {
	news, err := model.LoadDocWithTx(tx, []uint64{new.ID}, nil, scopedAssistantId...)
	if err != nil {
		return err
	}
	if len(news) == 0 {
		return nil
	}

	// 只有启用/禁用才会去更新
	if old.State != ai.DocState_DOC_STATE_ENABLED && old.State != ai.DocState_DOC_STATE_DISABLED {
		return nil
	}
	if news[0].State != ai.DocState_DOC_STATE_ENABLED && news[0].State != ai.DocState_DOC_STATE_DISABLED {
		return nil
	}
	err = NewDocSyncLogic(ctx, news[0], old).SyncToDBLog(tx)
	if err != nil {
		log.WithContext(ctx).Errorf("Update collection rag data failed: %v,doc id: %d", err, new.ID)
		return err
	}
	return nil
}

// DeleteDocInBulk 批量删除 doc
func DeleteDocInBulk(ctx context.Context, tx *gorm.DB, ids []uint64, isContributorDelete bool, operator *ai.Operator, scopedAssistantId ...uint64) error {
	if len(ids) == 0 {
		return nil
	}

	var docs []*model.TDoc
	for _, id := range ids {
		docs = append(docs, &model.TDoc{ID: id})
	}
	// 只统计启用/禁用的/删除中，已解绑的不用计算
	cnt := tx.Model(&docs).Where("state in (?)", []ai.DocState{
		ai.DocState_DOC_STATE_ENABLED,
		ai.DocState_DOC_STATE_DISABLED, ai.DocState_DOC_STATE_DELETING,
	}).
		Association("States").Count()
	// 如果没有关联到助手，且是贡献者删除，直接删除该知识，不用同步向量
	if cnt == 0 && isContributorDelete {
		err := tx.Model(&docs).Select(clause.Associations).Omit("Labels").Delete(&docs).Error
		if err != nil {
			return err
		}
		return nil
	}

	var err error
	synced, err := IsDocSynced(ctx, tx, ids, scopedAssistantId...)
	if err != nil {
		return err
	}
	var docState []*model.TAssistantDoc
	err = tx.Model(&model.TAssistantDoc{}).Where("doc_id in ?", ids).Scopes(func(db *gorm.DB) *gorm.DB {
		if len(scopedAssistantId) != 0 {
			return db.Where("assistant_id in ?", scopedAssistantId)
		}
		return db
	}).Scan(&docState).Error
	if err != nil {
		return err
	}

	var docWithMatchPattern []*model.TDoc
	err = tx.Model(&model.TDoc{}).
		Preload("MatchPatterns").
		Select("id", "data_type").
		Where("id in ?", ids).Find(&docWithMatchPattern).Error
	if err != nil {
		return err
	}
	enabledLLM := make(map[uint64]bool)
	for _, doc := range docWithMatchPattern {
		enabledLLM[doc.ID] = DocUsedLMRecall(doc)
	}
	directDeleted := make([]uint64, 0)
	ragToDelete := make([]uint64, 0)

	for docId, ok := range synced {
		// 没有同步完成，需要rag 侧同步完，再删除 doc
		if !ok {
			ragToDelete = append(ragToDelete, docId)
		} else {
			enabled := false
			for _, v := range docState {
				if v.DocID == docId && v.State == ai.DocState_DOC_STATE_ENABLED && enabledLLM[v.DocID] {
					enabled = true
				}
			}
			// 如果有已经启用，需要 rag 侧同步
			if !enabled {
				directDeleted = append(directDeleted, docId)
			} else {
				ragToDelete = append(ragToDelete, docId)
			}
		}
	}

	// 直接删除，无需同步向量
	if len(directDeleted) != 0 {
		if len(scopedAssistantId) == 0 && isContributorDelete {
			toDel := make([]*model.TDoc, 0, len(directDeleted))
			for _, docId := range directDeleted {
				toDel = append(toDel, &model.TDoc{ID: docId})
			}
			err = tx.Model(&model.TDoc{}).Select(clause.Associations).Omit("Labels").Delete(&toDel).Error
			if err != nil {
				return err
			}
		} else {
			err = tx.Model(&model.TAssistantDoc{}).
				Where("doc_id in (?)", directDeleted).
				Scopes(func(d *gorm.DB) *gorm.DB {
					if len(scopedAssistantId) != 0 {
						return d.Where("assistant_id in (?)", scopedAssistantId)
					}
					return d
				}).
				UpdateColumn("state", ai.DocState_DOC_STATE_UNBOUNDED).Error
			if err != nil {
				return err
			}
		}
	}
	// 需要同步向量之后再删除
	if len(ragToDelete) != 0 {
		ids = ragToDelete
		err = DeleteRagDataInBulk(ctx, tx, ids, true, scopedAssistantId...)
		if err != nil {
			return err
		}
		m := tx.Model(&model.TAssistantDoc{}).Where("doc_id in ?", ids)
		if len(scopedAssistantId) != 0 {
			m = m.Where("assistant_id in ?", scopedAssistantId)
		}
		// scopedAssistantId 为空代表直接删除该 doc
		if len(scopedAssistantId) == 0 && isContributorDelete {
			// 1. 助手关联表中设置为删除中
			err = m.WithContext(ctx).Where("state = ?", ai.DocState_DOC_STATE_ENABLED).UpdateColumn("state", ai.DocState_DOC_STATE_DELETING).Error
			if err != nil {
				return err
			}
			err = m.WithContext(ctx).Where("state = ?", ai.DocState_DOC_STATE_DISABLED).UpdateColumn("state", ai.DocState_DOC_STATE_UNBOUNDED).Error
			if err != nil {
				return err
			}
			// 2. doc 表设置为删除中
			err = tx.Model(&docs).Update("state", ai.DocState_DOC_STATE_DELETING).Error
		} else {
			// scopedAssistantId 非空，助手关联表中设置为删除中
			err = m.UpdateColumn("state", ai.DocState_DOC_STATE_DELETING).Error
		}
		if err != nil {
			return err
		}
	}

	// 不是贡献者删除，接收方删除分享关系
	if !isContributorDelete {
		err = tx.Model(&model.TDocShare{}).
			Where("doc_id in (?)", ids).
			Where("share_type = ?", operator.Type).
			Where("target_id = ?", operator.Id).
			Delete(nil).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// OnOffDocInBulk 批量启用/禁用 doc，不会检查重复逻辑
func OnOffDocInBulk(ctx context.Context, tx *gorm.DB, targetState ai.DocState, ids []uint64, scopedAssistantId ...uint64) error {
	if len(ids) == 0 {
		return nil
	}

	// 1. 判断是否doc的状态已经是req.state
	scopedQuery := "state <> ? and doc_id in ?"
	scopedArgs := []interface{}{targetState, ids}
	if len(scopedAssistantId) != 0 {
		scopedQuery = scopedQuery + " and assistant_id in ?"
		scopedArgs = append(scopedArgs, scopedAssistantId)
	}
	//
	// count, err := model.NewQuery[model.TAssistantDoc](ctx).
	// 	Where(scopedQuery, scopedArgs...).Count()
	// if err != nil {
	// 	return err
	// }
	// if count == 0 {
	// 	return nil
	// }

	// 2. 获取doc信息,对于 States 关联读取，只取 scoped assistant 范围内
	var rows []*model.TDoc
	err := tx.Model(&model.TDoc{}).
		Preload("Assistants", model.AssistantWithCollection).
		Preload("States", func(db *gorm.DB) *gorm.DB {
			return db.Where(scopedQuery, scopedArgs...)
		}).
		Preload("MatchPatterns").
		Where("id in ?", ids).Find(&rows).Error
	if err != nil {
		return err
	}
	if len(rows) == 0 {
		return nil
	}

	// 检查状态切换是否合法
	for _, row := range rows {
		for _, v := range row.States {
			err = CanDocStateManualChangeTo(v.State, targetState)
			if err != nil {
				if err, ok := xerrors.IsCode(err, pberrors.AiError_AiCollectionDocStateChangeInvalid); ok {
					err.Message = "当前状态无法启用或停用"
					return err
				}
				return err
			}
			if targetState == ai.DocState_DOC_STATE_ENABLED {
				v.State = targetState
			}
		}
	}

	// 更新 scoped assistant 下的状态为目标状态
	err = tx.Model(&model.TAssistantDoc{}).
		Where(scopedQuery, scopedArgs...).
		Update("state", targetState).Error
	if err != nil {
		return err
	}

	// 过滤只使用了大模型的doc，能加快启用速度
	rowsUsedLLM := make([]*model.TDoc, 0, len(rows))
	for _, row := range rows {
		if DocUsedLMRecall(row) {
			rowsUsedLLM = append(rowsUsedLLM, row)
		}
	}

	switch targetState {
	case ai.DocState_DOC_STATE_ENABLED:
		err = SyncDocToDBLogBulk(ctx, tx, nil, rowsUsedLLM)
	case ai.DocState_DOC_STATE_DISABLED:
		err = SyncDocToDBLogBulk(ctx, tx, rowsUsedLLM, nil)
	}
	if err != nil {
		return err
	}
	return nil
}

type OnDocLogic struct {
	Ctx      context.Context
	RowsToOn []*model.TDoc
	// 限定在ScopedAssistantId这些助手里面的子集启用
	ScopedAssistantId []uint64
	DocType           ai.DocType
}

func (l *OnDocLogic) CheckRepeated() (repeated []*ai.RspOnOffDocInBulk_RepeatCollection, err error) {
	var allMd5Rows []*model.TDoc
	var md5Set []string

	for _, row := range l.RowsToOn {
		if row.IndexTextMd5 != "" && row.IndexTextMd5 != EmptyStringMD5 {
			md5Set = append(md5Set, row.IndexTextMd5)
		}
	}
	db := model.NewQuery[model.TDoc](l.Ctx).DB().
		Preload("Assistants").Model(&model.TDoc{}).
		Preload("States", func(db *gorm.DB) *gorm.DB {
			if len(l.ScopedAssistantId) != 0 {
				db = db.Where("assistant_id in ?", l.ScopedAssistantId)
			}
			db = db.Where("state = ?", ai.DocState_DOC_STATE_ENABLED)
			return db
		}).Where("index_text_md5 in ?", md5Set)
	if l.DocType == ai.DocType_DOCTYPE_QA {
		db = db.Scopes(model.DocWithLLMRecallFilter)
		db = db.Select("ID", "IndexTextMd5", "IndexText")
	} else {
		db = db.Select("ID", "IndexTextMd5", "FileName")
	}
	err = db.Find(&allMd5Rows).Error
	if err != nil {
		return
	}

	for _, row := range l.RowsToOn {
		// 处理重复文件; 查找已经启用的文件md5值， 对比Assistant数据
		var md5rows []*model.TDoc
		for _, v := range allMd5Rows {
			if v.IndexTextMd5 == row.IndexTextMd5 {
				md5rows = append(md5rows, v)
			}
		}
		dataMap := map[uint64]string{}

		for _, r := range row.States {
			find, data := FindMd5rows(r.AssistantID, md5rows)
			if find {
				dataMap[data.ID] = data.FileName
				if l.DocType == ai.DocType_DOCTYPE_QA {
					dataMap[data.ID] = data.IndexText
				}
				repeated = append(repeated, &ai.RspOnOffDocInBulk_RepeatCollection{Id: r.AssistantID, FileName: dataMap})
			}
		}
	}
	return
}

func (l *OnDocLogic) CheckPreRepeat() []*ai.RspOnOffDocInBulk_RepeatCollection {
	md5Map := make(map[string]map[uint64]*ai.RspOnOffDocInBulk_RepeatCollection)

	for _, doc := range l.RowsToOn {
		if _, exists := md5Map[doc.IndexTextMd5]; !exists {
			md5Map[doc.IndexTextMd5] = make(map[uint64]*ai.RspOnOffDocInBulk_RepeatCollection)
		}
		for _, state := range doc.States {
			assistantID := state.AssistantID
			if _, exists := md5Map[doc.IndexTextMd5][assistantID]; !exists {
				md5Map[doc.IndexTextMd5][assistantID] = &ai.RspOnOffDocInBulk_RepeatCollection{
					Id:       assistantID,
					FileName: make(map[uint64]string),
				}
			}
			var docName string
			docName = doc.FileName
			if l.DocType == ai.DocType_DOCTYPE_QA {
				docName = doc.IndexText
			}
			md5Map[doc.IndexTextMd5][assistantID].FileName[doc.ID] = docName
		}
	}

	var repeated []*ai.RspOnOffDocInBulk_RepeatCollection
	for _, assistantMap := range md5Map {
		for _, repeat := range assistantMap {
			if len(repeat.FileName) > 1 {
				repeated = append(repeated, repeat)
			}
		}
	}
	return repeated
}

// CheckQaContainsMatchPatternLimit 检查启用的QA有包含匹配模式是否超了限制
// 默认最多10000个QA能使用包含匹配
func (l *OnDocLogic) CheckQaContainsMatchPatternLimit() (exceed []*ai.RspOnOffDocInBulk_QaContainsMatchCount, err error) {
	if l.DocType != ai.DocType_DOCTYPE_QA {
		return nil, nil
	}

	toOn := make(map[uint64]uint64)
	toOnIds := make(map[uint64][]uint64)
	for _, doc := range l.RowsToOn {
		for _, state := range doc.States {
			toOn[state.AssistantID] += 1
			toOnIds[state.AssistantID] = append(toOnIds[state.AssistantID], doc.ID)
		}
	}
	aids := make([]uint64, 0, len(toOn))
	for id := range toOn {
		aids = append(aids, id)
	}

	existed, err := CountQaContainsMpInAssistant(l.Ctx, aids)
	if err != nil {
		return nil, err
	}

	containsMaxLimit := config.GetUint64Or("llm.collection.qa_contains_max_limit", 10000)
	for k, v := range existed {
		total := v + toOn[k]
		if total > containsMaxLimit {
			exceed = append(exceed, &ai.RspOnOffDocInBulk_QaContainsMatchCount{
				AssistantId: k,
				QaCnt:       v,
				DocIds:      toOnIds[k],
			})
		}
	}
	return
}

// CountQaContainsMpInAssistant 计算助手下匹配模式为包含，且已经启用的QA数量
func CountQaContainsMpInAssistant(ctx context.Context, assistantId []uint64) (map[uint64]uint64, error) {
	// 子查询1：获取指定助手的有效文档关联
	subQ := model.NewQuery[model.TAssistantDoc](ctx).DB().
		Where("assistant_id IN ?", assistantId).
		Where("state = ?", ai.DocState_DOC_STATE_ENABLED).
		// 明确指定字段并设置别名
		Select("doc_id", "assistant_id AS assistant_id")

	// 子查询2：匹配包含模式的文档
	subQ2 := model.NewQuery[model.TDocMatchPattern](ctx).DB().
		Where("doc_id = t_doc.id").
		Where("match_pattern = ?", ai.DocMatchPattern_DOC_MATCH_PATTERN_CONTAINS).
		Select("1")

	// 主查询
	docDB := model.NewQuery[model.TDoc](ctx).DB().
		// 使用 JOIN 代替自动关联，明确指定别名和条件
		Joins("INNER JOIN (?) AS assistant_doc ON t_doc.id = assistant_doc.doc_id", subQ).
		Where("EXISTS (?)", subQ2).
		Where("data_type = ?", ai.DocType_DOCTYPE_QA)

	var result []struct {
		ID  uint64 `gorm:"column:assistant_id"`
		Cnt uint64 `gorm:"column:cnt"`
	}

	err := docDB.
		// 使用明确的别名分组
		Group("assistant_doc.assistant_id").
		Select("COUNT(*) AS cnt", "assistant_doc.assistant_id AS assistant_id").
		Find(&result).Error

	// 转换为 map 返回
	resMap := make(map[uint64]uint64)
	for _, r := range result {
		resMap[r.ID] = r.Cnt
	}
	return resMap, err
}

func GetCollection(ctx context.Context, id uint64) (*model.TCollection, error) {
	row, err := model.NewQuery[model.TCollection](ctx).FindByKey(id)
	if err != nil {
		return row, err
	}
	return row, nil
}

// DocFileManager collection文件管理器
type DocFileManager struct {
	ctx    context.Context
	doc    *model.TDoc
	tmpDir []string
}

// NewDocFileManager ...
func NewDocFileManager(ctx context.Context, doc *model.TDoc) *DocFileManager {
	return &DocFileManager{
		ctx: ctx,
		doc: doc,
	}
}

// FileSize 文件大小
func FileSize(filePath string) (int64, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()
	fi, err := file.Stat()
	if err != nil {
		return 0, err
	}
	return fi.Size(), nil
}

// FileSizeCos 文件大小
func FileSizeCos(path string) (int64, error) {
	cosClient := GetAiDocCosClient()
	rsp, err := cosClient.Object.Head(context.Background(), strings.TrimPrefix(path, "/"), nil)
	if err != nil {
		return 0, err
	}
	sizeString := rsp.Header.Get("Content-Length")
	size, err := strconv.ParseInt(sizeString, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("head object returns an invalid Content-Length: %w", err)
	}
	return size, nil
}

// CalculateMD5Cos 计算cos文件md5
func CalculateMD5Cos(ctx context.Context, path string) (string, error) {
	path = strings.TrimPrefix(path, "/")
	cosClient := GetAiDocCosClient()
	createJobOpt := &cos.FileProcessJobOptions{
		Tag: "FileHashCode",
		Input: &cos.FileProcessInput{
			Object: path,
		},
		Operation: &cos.FileProcessJobOperation{
			FileHashCodeConfig: &cos.FileHashCodeConfig{
				Type:        "MD5",
				AddToHeader: true,
			},
		},
	}
	res, _, err := cosClient.CI.CreateFileProcessJob(ctx, createJobOpt)
	if err != nil {
		return "", fmt.Errorf("create md5 job err: %w", err)
	}
	jobid := res.JobsDetail.JobId

	ctx, cancel := context.WithTimeout(ctx, time.Minute*5)
	defer cancel()
	for {
		select {
		case <-ctx.Done():
			return "", fmt.Errorf("delete job err: %w", ctx.Err())
		default:
			time.Sleep(2 * time.Second)
		}
		res, _, err = cosClient.CI.DescribeFileProcessJob(context.Background(), jobid)
		if err != nil {
			return "", fmt.Errorf("describe md5 job err: %w", err)
		}
		switch res.JobsDetail.State {
		case "Submitted", "Running":
		case "Failed":
			return "", fmt.Errorf("delete job err: %s", res.JobsDetail.Message)
		case "Success":
			md5Str := res.JobsDetail.Operation.FileHashCodeResult.MD5
			return md5Str, nil
		}
	}
}

// UpdateMd5 更新md5
func (l *DocFileManager) UpdateMd5(md5Str string) error {
	err := model.NewQuery[model.TDoc](l.ctx).DB().
		Where("id = ?", l.doc.ID).Omit("update_date").
		UpdateColumn("index_text_md5", md5Str).Error
	if err != nil {
		return err
	}
	return nil
}

// GenMd5ToDB 生成文件的md5
func (l *DocFileManager) GenMd5ToDB() error {
	if l.doc == nil {
		return nil
	}
	localPath, err := os.MkdirTemp("./", docFilePathPattern)
	if err != nil {
		return err
	}
	fileName := uuid.NewString()
	localFilePath := path.Join(localPath, fileName)
	l.tmpDir = append(l.tmpDir, localPath)
	if len(l.doc.Ref.Url) != 0 {
		err = new(CosHelper).GetFile(l.ctx, l.doc.Ref.Url, localFilePath)
		if err != nil {
			return err
		}
		md5, err := util.CalculateMD5File(localFilePath)
		if err != nil {
			return err
		}
		return l.UpdateMd5(md5)
	}
	return nil
}

type DocParseStrategy int

func (s DocParseStrategy) Download() bool {
	if s == DocParseStrategyLocal || s == DocParseStrategyLocalRemote {
		return true
	}
	return false
}

func (s DocParseStrategy) RemoteUrl() bool {
	if s == DocParseStrategyRemote || s == DocParseStrategyLocalRemote {
		return true
	}
	return false
}

const (
	// DocParseStrategyLocal 下载文件至本地解析
	DocParseStrategyLocal DocParseStrategy = iota
	// DocParseStrategyRemote 远程url直接解析
	DocParseStrategyRemote
	// DocParseStrategyLocalRemote 本地和远程url一起
	DocParseStrategyLocalRemote
)

// DocParseStrategy 解析文档的策略
func (l *DocFileManager) DocParseStrategy() DocParseStrategy {
	ft, _ := DocFileTypeBySuffix(l.doc.Ref.Url)
	switch ft {
	// 音频、视频类型，不下载源文件
	case DocFileTypeAudio, DocFileTypeVideo, DocFileTypePicture:
		return DocParseStrategyRemote
	case DocFileTypePdf, DocFileTypeDocx, DocFileTypeCsv, DocFileTypeXlsx, DocFileTypePpt,
		DocFileTypeEpub, DocFileTypeTxt:
		return DocParseStrategyLocalRemote
	default:
		return DocParseStrategyLocal
	}
}

// GetFileToParseFromCos 获取cos文件
// origin -> 源文件
// parsed -> 已解析好的文件
func (l *DocFileManager) GetFileToParseFromCos() (origin, parsed string, err error) {
	if config.GetBoolOr("llm.collection.doc_parse.ignore_parsed_url", false) {
		l.doc.Ref.ParsedUrl = ""
	}
	localPath, err := os.MkdirTemp("./", docFilePathPattern)
	if err != nil {
		return "", "", err
	}
	l.tmpDir = append(l.tmpDir, localPath)
	originFilePath := path.Join(localPath, filepath.Base(l.doc.Ref.Url))
	parsedFilePath := ""
	switch l.doc.DataType {
	case uint32(ai.DocType_DOCTYPE_FILE):
		// 1. 获取原始文件
		err = new(CosHelper).GetFile(l.ctx, l.doc.Ref.Url, originFilePath)
		if err != nil {
			return "", "", err
		}

		// 2. 获取已经解析好的文件
		if len(l.doc.Ref.ParsedUrl) != 0 {
			parsedFilePath = path.Join(localPath, filepath.Base(l.doc.Ref.ParsedUrl))
			err = new(CosHelper).GetFile(l.ctx, l.doc.Ref.ParsedUrl, parsedFilePath)
			if err != nil {
				return "", "", err
			}
		}
	default:
		return "", "", errors.New("DocFileManager not support doc data type")
	}
	return originFilePath, parsedFilePath, nil
}

// 清理文本，删除 Markdown 标题语法，移除无意义字符
func cleanText(text string) string {
	// 1. 删除 Markdown 标题的 `##` 语法
	// 将所有标题中的 `#` 替换为空格或直接去掉
	reMarkdownTitle := regexp.MustCompile(`^\s*#{1,6}\s*`)
	text = reMarkdownTitle.ReplaceAllString(text, "")

	// 2. 删除 URL 链接（例如 http:// 或 https:// 开头的链接）
	reURL := regexp.MustCompile(`https?://[^\s]+`)
	text = reURL.ReplaceAllString(text, "")

	// 3. 删除标点符号和无意义的符号，保留字母、数字、中文字符等
	// 保留字母、数字、汉字，删除其他字符
	rePunctuation := regexp.MustCompile(`[^\w\x{4e00}-\x{9fa5}]+`)
	text = rePunctuation.ReplaceAllString(text, "")

	// 已改为删除所有的空格
	// 4. 删除多余的空白符，确保单一空格分隔
	// text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")

	// 返回清理后的文本
	return text
}

// 计算字符数
func countFileCharacters(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	charCount := 0

	for {
		line, err := reader.ReadString('\n')
		line = cleanText(line)
		charCount += utf8.RuneCountInString(line)
		if err != nil { // 检测文件末尾
			if err.Error() == "EOF" {
				break
			}
			return 0, err
		}
	}

	return charCount, nil
}

// 计算每一页的字符数
func countCharactersPage(filePath string) (int, error) {
	total, err := countFileCharacters(filePath)
	if err != nil {
		return 0, err
	}
	page, err := GetFilePageCountByTika(filePath)
	if err != nil {
		return 0, err
	}
	return total / page, nil
}

// ignoreParsedDocByFront 是否忽略前端解析的内容
func (l *DocFileManager) ignoreParsedDocByFront(path string) (bool, error) {
	// 相等代表该文件为txt文件
	if l.doc.Ref.ParsedUrl == l.doc.Ref.Url {
		return false, nil
	}

	_, err := os.Stat(path)
	if err != nil {
		return false, err
	}
	// 该类型文件不支持解析
	if l.parseSupported() != nil {
		return false, nil
	}
	count, _ := countFileCharacters(path)
	page, _ := GetFilePageCountByTika(path)
	if OcrIfPageRuneTooLess(util.DetectLanguageFile(path), count, page) {
		return true, nil
	}
	return false, nil
}

// GenerateRagFileFromDoc 读取index_text字段，生成文件
func (l *DocFileManager) GenerateRagFileFromDoc() (string, error) {
	wd, _ := os.Getwd()
	localPath, err := os.MkdirTemp(wd, docFilePathPattern)
	if err != nil {
		return "", err
	}
	l.tmpDir = append(l.tmpDir, localPath)
	localFilePath := path.Join(localPath, l.doc.RagFilename)
	f, err := os.Create(localFilePath)
	if err != nil {
		return "", err
	}
	_, err = f.WriteString(l.doc.IndexText)
	if err != nil {
		return "", err
	}
	return localFilePath, nil
}

// Clean 清除临时文件
func (l *DocFileManager) Clean() {
	if len(l.tmpDir) != 0 {
		for _, v := range l.tmpDir {
			os.RemoveAll(v)
		}
	}
}

// parseSupported 判断当前doc的文件类型是否支持解析
func (l *DocFileManager) parseSupported() error {
	ft, suffix := DocFileTypeBySuffix(l.doc.Ref.Url)
	if ft == DocFileTypeUnsupported {
		err := fmt.Errorf("unsupported file type to parse: %s", suffix)
		return err
	}
	return nil
}

func (l *DocFileManager) markDocParseFailed(err error) {
	if err != nil {
		_, dberr := model.NewQuery[model.TDoc](l.ctx).Select("state").UpdateBy(&model.TDoc{State: ai.DocState_DOC_STATE_PARES_FAILED}, "id = ?", l.doc.ID)
		if dberr != nil {
			log.WithContext(l.ctx).Errorf("mark doc parse failed err: %v", err)
		}
		dberr = model.NewQuery[model.TDocParseLog](l.ctx).DB().Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "doc_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"text"}),
		}).Create(&model.TDocParseLog{
			DocID:      l.doc.ID,
			Text:       err.Error(),
			CreateDate: time.Time{},
		}).Error
		if dberr != nil {
			log.WithContext(l.ctx).Errorf("save doc parse log err: %v", dberr)
		}
	}
}

type ParseDocParam struct {
	// force 为true，会忽略之前解析的cache和前端解析的文件
	Force bool
	// IgnoreErr 为true，遇到错误不会记录数据
	IgnoreErr bool
}

// ParseDocToDB 解析文档内容至db
func (l *DocFileManager) ParseDocToDB(param ...ParseDocParam) (err error) {
	var text string
	var originFile, parsedFile string
	var cosUrl string
	var md5Str string
	var fileSize int64
	var doForce bool
	var ignoreErr bool
	cacheDocParseMode := ai.DocParseMode_DOC_PARSE_MODE_UNSPECIFIED
	if len(param) > 0 {
		doForce = param[0].Force
		ignoreErr = param[0].IgnoreErr
	}
	if l.doc.DocExtend == nil {
		log.WithContext(l.ctx).Warnf("ParseDocToDB empty doc extend , doc id: %d", l.doc.ID)
		l.doc.DocExtend = &model.TDocExtend{DocID: l.doc.ID, ParseMode: ai.DocParseMode_DOC_PARSE_MODE_UNSPECIFIED}
	}
	defer func() {
		defer l.ClearOcrParseProgress()
		defer model.SetDocExtend(l.ctx, l.doc.DocExtend, cacheDocParseMode)
		if err != nil {
			log.WithContext(l.ctx).Infof("parse doc failed, docId: %d, error: %v", l.doc.ID, err)
			if !ignoreErr {
				l.markDocParseFailed(err)
			}
		} else {
			if len(text) == 0 {
				log.WithContext(l.ctx).Warnf("ParseDocToDB empty doc text, doc id: %d", l.doc.ID)
			}
			text = RemoveMarkdownImages(text)
			doc := &model.TDoc{
				ID:        l.doc.ID,
				IndexText: rag.MergeLines(text),
				State:     ai.DocState_DOC_STATE_DISABLED,
				FileSize:  fileSize,
			}
			err = model.NewQuery[model.TDoc](l.ctx).DB().Model(doc).
				Select("index_text", "state", "file_size").Omit("update_date").
				Updates(doc).Error
			if err != nil && !ignoreErr {
				l.markDocParseFailed(err)
			}
		}
		if len(md5Str) != 0 {
			err = l.UpdateMd5(md5Str)
			if err != nil {
				log.WithContext(l.ctx).Errorf("parse doc generate doc md5 to database failed: %v", err)
			}
		}
	}()

	strategy := l.DocParseStrategy()
	if strategy.Download() {
		originFile, parsedFile, err = l.GetFileToParseFromCos()
		if err != nil {
			return err
		}
		md5Str, err = util.CalculateMD5File(originFile)
		if err != nil {
			return err
		}
		fileSize, err = FileSize(originFile)
		if err != nil {
			return err
		}
	}
	if strategy.RemoteUrl() {
		cosUrl, err = new(CosHelper).GenCosSignedUrl(l.ctx, l.doc.Ref.Url)
		if err != nil {
			return err
		}
		// 没有下载需要远程计算md5
		if !strategy.Download() {
			md5Str, err = CalculateMD5Cos(l.ctx, l.doc.Ref.Url)
			if err != nil {
				log.WithContext(l.ctx).Errorf("calculate cos file md5 err: %v", err)
				return err
			}
		}
		if fileSize == 0 {
			fileSize, err = FileSizeCos(l.doc.Ref.Url)
			if err != nil {
				return err
			}
		}
	}

	ignoreParsed := true
	if parsedFile != "" {
		ignoreParsed, err = l.ignoreParsedDocByFront(parsedFile)
		if err != nil {
			return err
		}
	}
	if doForce {
		ignoreParsed = true
	}
	if parsedFile != "" && !ignoreParsed && l.doc.DocExtend.ParseMode == ai.DocParseMode_DOC_PARSE_MODE_FILE {
		// 前端解析的内容只有当传递解析模式为文件解析时才用，否则可能出现pdf被前端解析了，传的是图像解析，导致txt作为图像解析时失败
		t := &TxtParser{mode: &DocParserByMode{parseMode: l.doc.DocExtend.ParseMode}}
		text, err = t.Parse(l.ctx, parsedFile, cosUrl, l.RecordParseProgress)
	} else {
		// 检查缓存
		if md5Str != "" && !doForce {
			cacheParseMode, cacheText, err := model.ParseDocFromCache(l.ctx, md5Str)
			if err == nil && cacheParseMode == l.doc.DocExtend.ParseMode { // 如果解析模式和缓存的解析模式一样，则直接返回
				cacheDocParseMode = cacheParseMode
				text = cacheText
				return nil
			}
		}
		text, err = DoParseDoc(l.ctx, originFile, cosUrl, l.RecordParseProgress, l.doc.DocExtend)
		if err == nil {
			_ = model.SetDocParseCache(l.ctx, md5Str, text, l.doc.ID, l.doc.DocExtend.ParseMode)
		}
	}
	return
}

// 仅保留utf8字符
func onlyUtf8(s string) string {
	return s
}

func (l *DocFileManager) getFileParseProgressCache() (string, xcache.Cache) {
	cache := xcache.Default
	key := fmt.Sprintf("ai:doc_file_parse_progress:%d", l.doc.ID)
	return key, cache
}

// RecordParseProgress 记录文档解析进度
// 使用redis存储进度
func (l *DocFileManager) RecordParseProgress(progress float32) {
	key, cache := l.getFileParseProgressCache()
	value := fmt.Sprintf("%.2f", progress)
	err := cache.Set(l.ctx, key, value, time.Hour*24*7)
	if err != nil {
		log.WithContext(l.ctx).Errorf("record file parse progress failed: %v", err)
		return
	}
}

// GetOcrParseProgress 记录ocr进度
func (l *DocFileManager) GetOcrParseProgress() (float32, error) {
	key, cache := l.getFileParseProgressCache()
	get, err := cache.Get(l.ctx, key)
	if err != nil {
		if errors.Is(err, xcache.ErrNotExist) {
			log.WithContext(l.ctx).Errorf("get doc file parse progress failed: %v, docId: %d", err, l.doc.ID)
			return 0, nil
		}
		return 0, err
	}
	f64, _ := strconv.ParseFloat(string(get), 32)
	return float32(f64), nil
}

// ClearOcrParseProgress 清除解析进度记录
func (l *DocFileManager) ClearOcrParseProgress() error {
	key, cache := l.getFileParseProgressCache()
	return cache.Delete(l.ctx, key)
}

// GetDocByRagDocNames 通过rag侧返回的doc name搜索doc信息
func GetDocByRagDocNames(ctx context.Context, docNames []string, forceShowContributor bool, preloads []string, conds ...interface{}) (
	map[string]*model.TDoc, error,
) {
	db := model.NewQuery[model.TDoc](ctx).DB()
	var rows []*model.TDoc
	if len(conds) > 0 {
		db.Where(conds[0], conds[1:]...)
	}
	db.Preload("Contributors").Preload("Assistants", model.AssistantWithCollection)
	for _, v := range preloads {
		db.Preload(v)
	}
	err := db.Find(&rows, "rag_filename in ?", docNames).Error
	if err != nil {
		return nil, err
	}
	m := make(map[string]*model.TDoc)
	for _, v := range rows {
		// 判断是否显示贡献者
		if v.ShowContributor != 1 && !forceShowContributor {
			v.Contributors = nil
		}
		m[v.RagFilename] = v
	}
	return m, nil
}

func SearchCollectionItemsWithDoc(rags []*rag.CollectionRow, docs map[string]*model.TDoc, docToDelete, idsToDelete *[]string) []*ai.SearchCollectionItem {
	var items []*ai.SearchCollectionItem
	for _, v := range rags {
		item := &ai.SearchCollectionItem{
			Score:     v.Score,
			DocName:   v.DocName,
			Id:        v.Id,
			Type:      ai.SearchCollectionType(v.Type),
			IsRelated: v.IsRelated,
		}
		doc := docs[v.DocName]
		if doc != nil {
			switch ai.DocType(doc.DataType) {
			case ai.DocType_DOCTYPE_QA:
				item.Text = doc.Text
				item.Question = doc.IndexText
			case ai.DocType_DOCTYPE_FILE, ai.DocType_DOCTYPE_TEXT:
				item.Text = v.Content // 使用rag侧返回的文本段
				item.RefName = doc.FileName
				item.FileName = doc.FileName
				if doc.Ref != nil {
					item.RefUrl = doc.Ref.Url
					item.Url = doc.Ref.Url
				}
			}
			item.DocId = doc.ID
			item.DataSource = doc.DataSource
			item.Contributor = doc.ContributorToPb()
			item.UpdateBy = &ai.Operator{Type: doc.UpdateByType, Id: doc.UpdateBy}
			item.DocType = ai.DocType(doc.DataType)
			// 只有查到doc的才加入collection列表，防止出现只有评分的空数据
			items = append(items, item)
		} else {
			if !slices.Contains(*docToDelete, v.DocName) {
				*docToDelete = append(*docToDelete, v.DocName)
			}
			if !slices.Contains(*idsToDelete, v.Id) {
				*idsToDelete = append(*idsToDelete, v.Id)
			}
		}
	}
	return items
}

// 重复问题/文件名校验
func checkUniqueHash(ctx context.Context, doc []*model.TDoc, contributor *ai.Contributor,
	scopedAssistant []uint64, dtype []ai.DocType, dataSource ai.DocDataSource,
) ([]uint64, error) {
	dupIds := make([]uint64, len(doc))
	hashs := make([]string, 0, len(doc))
	for _, vv := range doc {
		md5hash := ""
		if vv.DataType == uint32(ai.DocType_DOCTYPE_QA) {
			md5hash = util.CalculateMD5String(vv.IndexText)
		} else {
			md5hash = util.CalculateMD5String(vv.FileName)
		}
		hashs = append(hashs, md5hash)
	}

	db := model.NewQuery[model.TDoc](ctx).DB()

	orGroup := model.NewQuery[model.TDocContributor](ctx).DB()
	if contributor != nil {
		subQ := model.NewQuery[model.TDocContributor](ctx).DB().
			Where("contributor_id = ? and contributor_type = ?", contributor.Id, contributor.Type)
		if len(contributor.Text) != 0 {
			subQ = subQ.Or("contributor_text = ? and contributor_type = ?", contributor.Text, contributor.Type)
		}
		subQ.Select("distinct(doc_id)")
		orGroup.Where("id in (?)", subQ)
	}
	if len(scopedAssistant) != 0 {
		assistantIds := scopedAssistant
		subQ := model.NewQuery[model.TAssistantDoc](ctx).DB().
			Where("assistant_id in ? and state in ?", assistantIds,
				[]ai.DocState{ai.DocState_DOC_STATE_ENABLED, ai.DocState_DOC_STATE_DISABLED}).Select("distinct(doc_id)")
		orGroup.Or("id in (?)", subQ)
	}
	db.Where(orGroup)

	g := errgroup.Group{}
	const batchSize = 50
	batchNum := len(doc)/batchSize + 1
	for i := 0; i < batchNum; i++ {
		start := i * batchSize
		end := start + batchSize
		if end > len(doc) {
			end = len(doc)
		}
		slice := hashs[start:end]
		g.Go(func() error {
			newDB := db.Session(&gorm.Session{})
			if dataSource != 0 {
				newDB.Where("data_source = ?", dataSource)
			}
			var duplicates []*model.TDoc
			err := newDB.Where("unique_hash in (?) and data_type in (?)", slice, dtype).Select("id, unique_hash").
				Scan(&duplicates).Error
			if err != nil {
				return err
			}

			if len(duplicates) != 0 {
				for _, duplicate := range duplicates {
					hash := duplicate.UniqueHash
					if hash != "" {
						for j, v := range slice {
							if v == hash {
								dupIds[start+j] = duplicate.ID
							}
						}
					}
				}
			}
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		return nil, err
	}
	return dupIds, nil
}

// QAValidator 合法性校验器
type QAValidator struct {
	doc             []*model.TDoc
	ctx             context.Context
	contributor     *ai.Contributor
	scopedAssistant []uint64
}

func NewQAValidator(ctx context.Context, doc []*model.TDoc, contributor *ai.Contributor, scopedAssistant ...uint64) *QAValidator {
	return &QAValidator{
		doc:             doc,
		ctx:             ctx,
		contributor:     contributor,
		scopedAssistant: scopedAssistant,
	}
}

// ValidateQuestion 校验问题重复
func (v *QAValidator) ValidateQuestion() ([]uint64, error) {
	return checkUniqueHash(v.ctx, v.doc, v.contributor, v.scopedAssistant, []ai.DocType{ai.DocType_DOCTYPE_QA}, 0)
}

// TextFileValidator 合法性校验器
type TextFileValidator struct {
	doc             []*model.TDoc
	ctx             context.Context
	contributor     *ai.Contributor
	scopedAssistant []uint64
	dataSource      ai.DocDataSource
}

func NewTextFileValidator(ctx context.Context, doc []*model.TDoc, dataSource ai.DocDataSource, contributor *ai.Contributor, scopedAssistant ...uint64) *TextFileValidator {
	return &TextFileValidator{
		doc:             doc,
		ctx:             ctx,
		contributor:     contributor,
		scopedAssistant: scopedAssistant,
		dataSource:      dataSource,
	}
}

// ValidateFileNameRepeated 校验
func (v *TextFileValidator) ValidateFileNameRepeated() ([]uint64, error) {
	return checkUniqueHash(v.ctx, v.doc, v.contributor, v.scopedAssistant,
		[]ai.DocType{ai.DocType_DOCTYPE_TEXT, ai.DocType_DOCTYPE_FILE}, v.dataSource)
}

// CanDocStateManualChangeTo 判断状态切换
func CanDocStateManualChangeTo(from, to ai.DocState) error {
	mapping := map[ai.DocState][]ai.DocState{
		ai.DocState_DOC_STATE_DISABLED: {ai.DocState_DOC_STATE_ENABLED, ai.DocState_DOC_STATE_DISABLED},
		ai.DocState_DOC_STATE_ENABLED:  {ai.DocState_DOC_STATE_ENABLED, ai.DocState_DOC_STATE_DISABLED},
	}

	if v, ok := mapping[from]; ok && slices.Contains(v, to) {
		return nil
	}
	return xerrors.NewCode(pberrors.AiError_AiCollectionDocStateChangeInvalid)
}

// CreateRagCollectionById 通过id在rag侧创建collection
func CreateRagCollectionById(ctx context.Context, id uint64) error {
	collection, err := GetCollection(ctx, id)
	if err != nil {
		return err
	}
	_, err = client.GetRagClient().CreateCollection(ctx, &rag.ReqCreateCollection{
		CollectionName: collection.RagName,
		VdbType:        collection.VdbType,
	})
	if err != nil {
		log.WithContext(ctx).Errorf("CreateRagCollectionById:%d err: %v", id, err)
		return err
	}
	return nil
}

// FindMd5rows 通过md5文件值查找
func FindMd5rows(findAssistantId uint64, md5rows []*model.TDoc) (bool, *model.TDoc) {
	for _, md5row := range md5rows {
		for _, row := range md5row.States {
			if findAssistantId == row.AssistantID {
				return true, md5row
			}
		}
	}

	return false, &model.TDoc{}
}

// CopyTDocForClone 复制 tdoc 结构，并重置一些字段，用于克隆 doc
// toCreate 需要创建的 doc
// toShareAssistant 需要分享至的助手
func CopyTDocForClone(operator *ai.Operator, rows []*model.TDoc, scopedAssistant ...uint64) (toCreate []*model.TDoc, toShareAssistant [][]uint64) {
	statesMemo := make(map[uint64][]*model.TAssistantDoc)
	toShareAssistant = make([][]uint64, len(rows))
	toCreate = make([]*model.TDoc, 0, len(rows))
	for i, row := range rows {
		statesMemo[row.ID] = row.States
		row.States = nil
		for _, v := range statesMemo[row.ID] {
			if len(scopedAssistant) != 0 && !slices.Contains(scopedAssistant, v.AssistantID) {
				toShareAssistant[i] = append(toShareAssistant[i], v.AssistantID)
				continue
			}
			row.States = append(row.States, &model.TAssistantDoc{
				AssistantID: v.AssistantID,
				// 克隆之后在助手中默认是禁用状态
				State:         ai.DocState_DOC_STATE_DISABLED,
				IsShared:      1,
				ChunkDetailID: v.ChunkDetailID,
			})
		}
		for _, v := range row.Contributors {
			v.ID = 0
			v.DocID = 0
		}
		for _, v := range row.MatchPatterns {
			v.ID = 0
			v.DocID = 0
		}
		if row.DocExtend != nil {
			row.DocExtend.DocID = 0
		}
		for _, v := range row.TableOversize {
			v.ID = 0
			v.DocID = 0
		}
		row.ID = 0
		row.HitCount = 0
		row.Version = 0
		row.RagVersion = 0
		row.RagFilename = GenDocRagFileName()
		row.UpdateBy = operator.GetId()
		row.UpdateByType = operator.GetType()
		row.CreateByType = operator.GetType()
		row.CreateBy = operator.GetId()
		row.CreateDate = time.Now()
		row.UpdateDate = time.Now()
		toCreate = append(toCreate, row)
	}
	return toCreate, toShareAssistant
}

// UpdateDocAttrMaskMapping 更新QA/文本/文件时，mask和数据库字段的映射
var UpdateDocAttrMaskMapping = map[string]any{
	"show_contributor": "show_contributor",
	"download_as_ref":  "download_as_ref",
	// 参考资料已不再存 ref 字段
	// "reference":        "ref",
}

// UpdateDocAttrLogic 封装批量更新逻辑
type UpdateDocAttrLogic struct {
	ctx       context.Context
	tx        *gorm.DB
	req       *ai.ReqUpdateDocAttrInBulk
	batchSize int
	fields    []any

	contributorChanged  bool
	assistantChanged    bool
	referenceChanged    bool
	matchPatternChanged bool

	needUpdateBy    bool
	updateByUpdated bool
}

// NewUpdateDocAttrLogic 初始化逻辑结构体
func NewUpdateDocAttrLogic(ctx context.Context, req *ai.ReqUpdateDocAttrInBulk) *UpdateDocAttrLogic {
	return &UpdateDocAttrLogic{
		ctx:       ctx,
		req:       req,
		batchSize: 200,
	}
}

// PrepareData 准备批量更新所需的数据
func (l *UpdateDocAttrLogic) PrepareData() {
	// 提取更新字段
	l.fields = l.extractUpdateFields()

	// 准备贡献者数据
	l.contributorChanged = slices.Contains(l.req.GetMask().GetPaths(), "contributor")

	// 准备助手状态数据
	l.assistantChanged = slices.Contains(l.req.GetMask().GetPaths(), "assistant_id")

	// 准备参考资料
	l.referenceChanged = slices.Contains(l.req.GetMask().GetPaths(), "reference")

	// 准备 qa 匹配模式
	l.matchPatternChanged = slices.Contains(l.req.GetMask().GetPaths(), "match_patterns")

	l.needUpdateBy = true
	// 只改变助手，不会变化最后更新人
	if len(l.req.Mask.GetPaths()) == 1 && l.req.Mask.GetPaths()[0] == "assistant_id" {
		l.needUpdateBy = false
	}
}

func (l *UpdateDocAttrLogic) extractUpdateFields() []any {
	fields := make([]any, 0, len(l.req.Mask.GetPaths()))
	for _, v := range l.req.Mask.GetPaths() {
		if field, ok := UpdateDocAttrMaskMapping[v]; ok {
			fields = append(fields, field)
		}
	}
	if !slices.Contains(fields, "update_by") && len(fields) != 0 {
		fields = append(fields, "update_by", "update_by_type", "update_by_user")
	}
	return fields
}

func (l *UpdateDocAttrLogic) prepareReferences(batch []uint64) ([]*model.TDocReference, []*model.TDocTextReference) {
	references := make([]*model.TDocReference, 0)
	textReferences := make([]*model.TDocTextReference, 0)
	for _, id := range batch {
		tmpDoc := &model.TDoc{ID: id}
		tmpDoc.ReferenceFromProto(l.req.GetReference())
		for _, v := range tmpDoc.DocReferences {
			references = append(references, &model.TDocReference{
				DocID:     int64(tmpDoc.ID),
				RefID:     int64(v.RefID),
				SortOrder: v.SortOrder,
			})
		}
		for _, v := range tmpDoc.TextReferences {
			textReferences = append(textReferences, &model.TDocTextReference{
				DocID:     tmpDoc.ID,
				Text:      v.Text,
				SortOrder: v.SortOrder,
			})
		}
	}
	return references, textReferences
}

func (l *UpdateDocAttrLogic) prepareMatchPatterns(batch []uint64) []*model.TDocMatchPattern {
	matchPatterns := make([]*model.TDocMatchPattern, 0)
	for _, id := range batch {
		for _, v := range l.req.GetMatchPatterns() {
			matchPatterns = append(matchPatterns, &model.TDocMatchPattern{
				DocID:        id,
				MatchPattern: v,
			})
		}
	}
	return matchPatterns
}

func (l *UpdateDocAttrLogic) prepareContributors(batch []uint64) []*model.TDocContributor {
	contributors := make([]*model.TDocContributor, 0)
	tmpDoc := &model.TDoc{}
	for _, id := range batch {
		tmpDoc.ID = id
		tmpDoc.Contributors = nil
		tmpDoc.FillContributorsFromProto(l.req.Contributor...)
		contributors = append(contributors, tmpDoc.Contributors...)
	}
	return contributors
}

func (l *UpdateDocAttrLogic) prepareStates(batch []uint64) []*model.TAssistantDoc {
	states := make([]*model.TAssistantDoc, 0)
	for _, id := range batch {
		for _, assistantId := range l.req.AssistantId {
			states = append(states, &model.TAssistantDoc{
				DocID:       id,
				AssistantID: assistantId,
				State:       ai.DocState_DOC_STATE_DISABLED,
			})
		}
	}
	return states
}

// ExecuteInTransaction 在事务中分批执行更新逻辑
func (l *UpdateDocAttrLogic) ExecuteInTransaction(tx *gorm.DB) error {
	l.tx = tx

	idBatches := util.SplitIntoBatches(l.req.Id, l.batchSize)
	for _, batch := range idBatches {
		if len(l.fields) > 0 {
			if err := l.updateDocs(batch); err != nil {
				return err
			}
			// 更新了tdoc表，会顺带把更新人更新
			l.updateByUpdated = true
		}
		if l.contributorChanged {
			if err := l.updateContributors(batch); err != nil {
				return err
			}
		}
		if l.assistantChanged {
			if err := l.updateAssistantStates(batch); err != nil {
				return err
			}
		}
		if l.referenceChanged {
			if err := l.updateReferences(batch); err != nil {
				return err
			}
		}
		if l.matchPatternChanged {
			if err := l.updateMatchPatterns(batch); err != nil {
				return err
			}
		}
		if !l.updateByUpdated && l.needUpdateBy {
			if err := l.updateUpdateBy(batch); err != nil {
				return err
			}
		}
	}
	return nil
}

// updateUpdateBy 修改更新人字段
func (l *UpdateDocAttrLogic) updateUpdateBy(batch []uint64) error {
	err := l.tx.Model(&model.TDoc{}).Where("id in (?)", batch).Updates(&model.TDoc{
		UpdateBy:     l.req.GetUpdateBy().GetId(),
		UpdateByType: l.req.GetUpdateBy().GetType(),
		UpdateByUser: l.req.GetUpdateBy().GetUserId(),
		UpdateDate:   time.Now(),
	}).Error
	if err != nil {
		return err
	}
	return nil
}

func (l *UpdateDocAttrLogic) updateDocs(batch []uint64) error {
	doc := &model.TDoc{
		ShowContributor: l.req.ShowContributor,
		DownloadAsRef:   l.req.DownloadAsRef,
		UpdateBy:        l.req.GetUpdateBy().GetId(),
		UpdateByType:    l.req.GetUpdateBy().GetType(),
		UpdateByUser:    l.req.GetUpdateBy().GetUserId(),
		UpdateDate:      time.Now(),
	}
	doc.ReferenceFromProto(l.req.GetReference())
	return l.tx.Where("id in (?)", batch).
		Select(l.fields[0], l.fields[1:]...).
		Updates(doc).Error
}

func (l *UpdateDocAttrLogic) updateContributors(batch []uint64) error {
	if err := l.tx.Model(&model.TDocContributor{}).Delete(nil, "doc_id in ?", batch).Error; err != nil {
		return err
	}
	contributors := l.prepareContributors(batch)
	if len(contributors) > 0 {
		if err := l.tx.Model(&model.TDocContributor{}).Create(&contributors).Error; err != nil {
			return err
		}
	}
	return nil
}

func (l *UpdateDocAttrLogic) updateAssistantStates(batch []uint64) error {
	states := l.prepareStates(batch)

	oldDocs, err := model.LoadDocWithTx(l.tx, batch, []string{"id", "rag_filename", "data_type"}, l.req.ScopedAssistantId...)
	if err != nil {
		return err
	}

	if len(states) > 0 {
		for _, newv := range states {
			for _, doc := range oldDocs {
				if doc.ID == newv.DocID {
					for _, oldv := range doc.States {
						if oldv.AssistantID == newv.AssistantID {
							if oldv.State == ai.DocState_DOC_STATE_ENABLED || oldv.State == ai.DocState_DOC_STATE_DISABLED {
								// 保持原有的状态
								newv.State = oldv.State
							}
							newv.IsShared = oldv.IsShared
						} else {
							// 使用已有状态为启用，新的绑定也为启用
							if oldv.State == ai.DocState_DOC_STATE_ENABLED {
								newv.State = oldv.State
							}
							// TODO: 删除下面的错误注释
							// 如果doc是分享过来的，新的绑定也为分享
							// if oldv.IsShared == 2 {
							// newv.IsShared = oldv.IsShared
							// }

							// 直接绑定的，不算做分享
							newv.IsShared = 1
						}
					}
					break
				}
			}
		}
	}

	if err = model.ReplaceDocAssistants(l.tx, batch, l.req.ScopedAssistantId, states); err != nil {
		return err
	}

	// 更新了助手绑定，异步存储超长提示
	xsync.Go(context.Background(), func(ctx context.Context) error {
		err := StoreOverSizedTipAllAssistantBulkAsync(ctx, batch)
		if err != nil {
			log.WithContext(l.ctx).Errorf("failed to store over sized tip for doc %v, err: %v", batch, err)
			return err
		}
		return nil
	}, boot.TraceGo(l.ctx))

	return UpdateRagDataInBulk(l.ctx, l.tx, oldDocs, oldDocs, UpdateRagDataHint{NoIndexTextUpdate: true}, l.req.ScopedAssistantId...)
}

func (l *UpdateDocAttrLogic) updateReferences(batch []uint64) error {
	err := l.tx.Model(&model.TDocReference{}).Delete(nil, "doc_id in ?", batch).Error
	if err != nil {
		return err
	}
	err = l.tx.Model(&model.TDocTextReference{}).Delete(nil, "doc_id in ?", batch).Error
	if err != nil {
		return err
	}
	references, textReferences := l.prepareReferences(batch)
	if len(references) > 0 {
		if err := l.tx.Model(&model.TDocReference{}).Create(references).Error; err != nil {
			return err
		}
	}
	if len(textReferences) > 0 {
		if err := l.tx.Model(&model.TDocTextReference{}).Create(textReferences).Error; err != nil {
			return err
		}
	}
	return nil
}

func (l *UpdateDocAttrLogic) updateMatchPatterns(batch []uint64) error {
	oldDocs, err := model.LoadDocWithTx(l.tx, batch, []string{"id", "rag_filename", "data_type"})
	if err != nil {
		return err
	}

	err = l.tx.Model(&model.TDocMatchPattern{}).Delete(nil, "doc_id in ?", batch).Error
	if err != nil {
		return err
	}
	matchPatterns := l.prepareMatchPatterns(batch)
	if len(matchPatterns) > 0 {
		if err := l.tx.Model(&model.TDocMatchPattern{}).Create(matchPatterns).Error; err != nil {
			return err
		}
	}

	// 注意: 不能传scopedAssistantId，因为需要在所有助手都更新 rag 数据
	return UpdateRagDataInBulk(l.ctx, l.tx, oldDocs, oldDocs, UpdateRagDataHint{NoIndexTextUpdate: true})
}
