package logic

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/http"
	nu "net/url"
	"path"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/helper"
	"github.com/go-redsync/redsync/v4"
	"golang.org/x/net/html"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// TitleTruncate 根据限制截断字符串
/**
20中文字 40个英文字符内，且英文单词不截断。超出的+ ...
@param input 输入字符串
@param chineseLimit 中文字符限制
@param englishLimit 英文字符限制
*/
// TitleTruncate 根据限制截断字符串
/**
20中文字 40个英文字符内，且英文单词不截断。超出的+ ...
@param input 输入字符串
@param chineseLimit 中文字符限制
@param englishLimit 英文字符限制
*/
func TitleTruncate(input string, chineseLimit int, englishLimit int) string {
	var length int         // 当前字符串长度
	var runeCount int      // 字符计数
	lastTruncateIndex := 0 // 上一次可截断的索引

	if chineseLimit == 0 {
		chineseLimit = 20
	}

	if englishLimit == 0 {
		englishLimit = 40
	}

	for i, r := range input {
		if unicode.Is(unicode.Han, r) {
			length += 2 // 中文字符宽度
		} else if r >= 'a' && r <= 'z' || r >= 'A' && r <= 'Z' || unicode.IsDigit(r) {
			length++ // 英文字母或数字宽度
		} else {
			length++ // 其他字符宽度
		}

		// 记录空格或其他截断点
		//if r == ' ' || r == '|' || r == '：' || r == ':' {
		//	lastSpaceIndex = i
		//}

		// 超过限制
		if length > chineseLimit*2 || runeCount >= englishLimit {
			// 优先按照空格或其他截断点截断
			lastTruncateIndex = i
			return input[:lastTruncateIndex] + "..."
		}

		runeCount++
	}

	return input
}

func generateHash(input string) string {
	hasher := sha256.New()
	hasher.Write([]byte(input))
	hashBytes := hasher.Sum(nil)
	return base64.RawURLEncoding.EncodeToString(hashBytes)
}

// fetchHtmlTitleWithRetry 增加重试逻辑
func fetchHtmlTitleWithRetry(ctx context.Context, url string) string {
	var err error
	var title = url

	// 转换为中文
	url, err = nu.QueryUnescape(url)
	if err != nil {
		return url
	}

	parsedUrl, err := nu.Parse(url)
	if err != nil {
		return url
	}

	// 判断是否为 .pdf 文件
	if strings.HasSuffix(parsedUrl.Path, ".pdf") {
		return path.Base(parsedUrl.Path)
	}

	err, title = fetchHtmlTitle(ctx, url)
	if err == nil && title != url {
		return title
	}

	hostConfig := config.GetStringMap("html.proxy.host")
	splits := config.GetStringSlice("html.proxy.keywords_split")
	log.WithContext(ctx).Infow(fmt.Sprintf("fetchHtmlTitleWithRetry from config splits: %+v ,hostConfig: %+v", splits, hostConfig))

	// 通过host配置获取
	if hostConfig != nil {
		var hostName = parsedUrl.Host
		if strings.HasPrefix(parsedUrl.Host, "www.") {
			hostName = strings.TrimPrefix(hostName, "www.")
		}
		for configHost, configTitle := range hostConfig {
			if strings.Contains(hostName, configHost) {
				return configTitle.(string)
			}
		}
	}

	// 通过splits search获取
	if splits != nil && len(splits) > 0 {
		path := parsedUrl.Path
		for _, split := range splits {
			sls := strings.Split(path, split)
			if len(sls) > 1 {
				return strings.ReplaceAll(sls[len(sls)-1], "/", "")
			}
		}
	}

	// 使用无头浏览器的title
	//if title == url {
	//	_, title = fetchHtmlTitleRod(ctx, url)
	//}

	return title
}

func afterFix(title string) (error, string) {
	if !utf8.ValidString(title) {
		// try gb18030
		reader := transform.NewReader(strings.NewReader(title), simplifiedchinese.GB18030.NewDecoder())
		gbStr, err := io.ReadAll(reader)
		if err != nil {
			return err, title
		}
		title = string(gbStr)
		return errors.New("utf8 invalid url"), title
	}
	if strings.Contains(title, "404") || strings.Contains(title, "loading") || strings.Contains(title, "页面找不到") {
		return errors.New("finalTitle contains 404"), title
	}
	return nil, title
}

func FetchHtmlTitleWithDistributedLock(ctx context.Context, url string) string {
	urlHash := generateHash(url)
	lockName := fmt.Sprintf("support:proxy:htmlTitleCacheLock:%s", urlHash)
	dmtx := helper.NewDistributedLock(lockName,
		redsync.WithExpiry(30*time.Second),
		redsync.WithTries(3))

	err := dmtx.Lock()
	if err != nil {
		log.WithContext(ctx).Errorf("failed to acquire lock for url: %s, err: %v", url, err)
		return url
	}
	defer dmtx.Unlock()

	cacheKey := fmt.Sprintf("support:proxy:htmlTitleCache:%s", urlHash)
	cachedTitle, err := xredis.Default.Get(ctx, cacheKey).Bytes()
	if err == nil && len(cachedTitle) > 0 {
		log.WithContext(ctx).Infof("get cachedTitle from redis: %s, key: %s", string(cachedTitle), cacheKey)
		return string(cachedTitle)
	}

	// 获取title
	log.Infow("fetchHtmlTitleWithRetry start", "url", url)
	title := fetchHtmlTitleWithRetry(ctx, url)
	if title != url {
		if err, title = afterFix(title); err != nil {
			return url
		}
		xredis.Default.Set(ctx, cacheKey, strings.TrimSpace(title), 24*time.Hour)
		return TitleTruncate(strings.TrimSpace(title), 20, 40)
	}
	return url
}

// fetchHtmlTitle 获取页面标题，失败将返回url本身
func fetchHtmlTitle(ctx context.Context, url string) (err error, str string) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return err, url
	}

	userAgent := fmt.Sprintf("Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1")
	req.Header.Set("User-Agent", userAgent)

	client := &http.Client{
		Timeout: time.Duration(config.GetIntOr("html.proxy.fetch_timeout", 5000)) * time.Millisecond,
	}
	resp, err := client.Do(req)

	if err != nil {
		return err, url
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return err, url
	}

	doc, err := html.Parse(resp.Body)
	if err != nil {
		return err, url
	}

	var title = url
	var f func(*html.Node) error
	f = func(n *html.Node) error {
		if n.Type == html.ElementNode && n.Data == "div" {
			for _, attr := range n.Attr {
				if attr.Key == "class" && strings.Contains(attr.Val, "weui-msg__title") {
					if n.FirstChild != nil && strings.Contains(n.FirstChild.Data, "参数错误") {
						title = "页面找不到"
						return errors.New("has found err element")
					}
				}
			}
		}
		if n.Type == html.ElementNode && n.Data == "title" {
			if n.FirstChild != nil && n.FirstChild.Data != "" {
				title = n.FirstChild.Data
				return nil
			}
		}
		// 查找<meta property="og:title" content="..."> 或 <meta name="twitter:title" content="...">
		if n.Type == html.ElementNode && n.Data == "meta" {
			var property string
			var content string
			for _, attr := range n.Attr {
				if attr.Key == "property" && (attr.Val == "og:title") {
					property = attr.Val
				}
				if attr.Key == "name" && (attr.Val == "twitter:title") {
					property = attr.Val
				}
				if attr.Key == "content" {
					content = attr.Val
				}
			}
			if property == "og:title" && content != "" {
				title = content
				return nil
			}
			if property == "twitter:title" && content != "" {
				title = content
				return nil
			}
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			if err := f(c); err != nil {
				return err
			}
		}
		return nil
	}
	if err := f(doc); err != nil {
		return err, url
	}
	return nil, title
}
