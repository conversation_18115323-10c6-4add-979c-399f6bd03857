package logic

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
)

type Client struct {
	httpClient *http.Client
	baseURL    string
	CalcURL    string
	PodURL     string
}

func NewClient() *Client {
	return &Client{
		httpClient: &http.Client{Timeout: config.GetDurationOr("nebula.time_out", 3600*2*time.Second)},
		baseURL:    config.GetStringOr("nebula.base_url", "http://tanlive-rag-vis"),
		PodURL:     config.GetStringOr("nebula.pod_url", "http://tanlive-rag-vis:81"),
		CalcURL:    config.GetStringOr("nebula.calc_url", "http://tanlive-rag-calc-vis"),
	}
}

// Response for api response
type Response struct {
	Status string          `json:"status"`
	Data   json.RawMessage `json:"data"`
}

// FetchCollectionRequest for fetch collection
type FetchCollectionRequest struct {
	CollectionName string `json:"collection_name"`
}

// FetchCollectionResponse for fetch collection response
type FetchCollectionResponse struct {
	Collection     string  `json:"collection"`
	Count          int     `json:"count"`
	SavedPath      string  `json:"saved_path"`
	ElapsedSeconds float64 `json:"elapsed_seconds"`
}

// FetchCollection Fetch collection
func (c *Client) FetchCollection(req FetchCollectionRequest) (*FetchCollectionResponse, error) {
	url := fmt.Sprintf("%s/fetch_collection", c.baseURL)

	var respData FetchCollectionResponse
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

// AddVecToColRequest for adding vectors to collection
type AddVecToColRequest struct {
	InputFile string `json:"input_file"`
	Lang      string `json:"lang"`
	Load      string `json:"load"`
}

// AddVecToColResponse for adding vectors to collection
type AddVecToColResponse struct {
	SuccessCount   int     `json:"success_count"`
	FailCount      int     `json:"fail_count"`
	OutputFile     string  `json:"output_file"`
	ElapsedSeconds float64 `json:"elapsed_seconds"`
	FileSizeMb     int     `json:"file_size_mb"`
}

// AddVecToCol Add vectors to collection
func (c *Client) AddVecToCol(req AddVecToColRequest) (*AddVecToColResponse, error) {
	url := fmt.Sprintf("%s/add_vec_to_col", c.baseURL)

	var respData AddVecToColResponse
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

// UploadQueryFileResponse for uploading query file
type UploadQueryFileResponse struct {
	FileName  string `json:"file_name"`
	SavedPath string `json:"saved_path"`
	FileSize  int    `json:"file_size"`
}

// UploadQueryFile Upload query file
func (c *Client) UploadQueryFile(filePath, fileName string) (*UploadQueryFileResponse, error) {
	url := fmt.Sprintf("%s/upload_query_file?file_name=%s", c.baseURL, fileName)

	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("file", file.Name())
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := io.Copy(part, file); err != nil {
		return nil, fmt.Errorf("failed to copy file content: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close writer: %w", err)
	}

	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("accept", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var apiResp Response
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if apiResp.Status != "success" {
		return nil, fmt.Errorf("api returned non-success status: %s", apiResp.Status)
	}

	var respData UploadQueryFileResponse
	if err := json.Unmarshal(apiResp.Data, &respData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response data: %w", err)
	}

	return &respData, nil
}

type PrecomputeUMAPRequest struct {
	InputFile       string `json:"input_file"`
	InputQueryFile  string `json:"input_file_query"`
	Lang            string `json:"lang"`
	SplitSize       int    `json:"split_size"`
	EnableCosUpload bool   `json:"enable_cos_upload"`
}

type UMAPResultData struct {
	Collection     string   `json:"collection"`
	Language       string   `json:"language"`
	TotalItems     int      `json:"total_items"`
	SplitSize      int      `json:"split_size"`
	ZipFile        string   `json:"zip_file"`
	ZipSize        int      `json:"zip_size"`
	ModelPath      string   `json:"model_path"`
	ElapsedSeconds float64  `json:"elapsed_seconds"`
	CosPrefix      string   `json:"cos_prefix"`
	CosPaths       []string `json:"cos_paths"`
	ZipCosURL      string   `json:"zip_cos_url"`
	OutPutfile     string   `json:"output_file"`
}

type CosUploadStatus struct {
	Enabled      bool `json:"enabled"`
	ActuallyUsed bool `json:"actually_used"`
}

type PrecomputeUMAPResponse struct {
	Status          string          `json:"status"`
	Data            UMAPResultData  `json:"data"`
	CosUploadStatus CosUploadStatus `json:"cos_upload_status"`
}

func (c *Client) PrecomputeUMAP(req PrecomputeUMAPRequest) (*UMAPResultData, error) {
	url := fmt.Sprintf("%s/precompute_umap", c.baseURL)

	var respData UMAPResultData
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

func (c *Client) PrecomputeNewUMAP(req PrecomputeUMAPRequest) (*UMAPResultData, error) {
	url := fmt.Sprintf("%s/precompute_umap", c.CalcURL)

	var respData UMAPResultData
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

// QueryProjectionRequest for query projection
type QueryProjectionRequest struct {
	Query            string `json:"query"`
	ModelPath        string `json:"model_path"`
	Lang             string `json:"lang"`
	OriginalDataPath string `json:"original_data_path"`
	KnownQueriesPath string `json:"known_queries_path"`
}

type QueryVector struct {
	Query      string    `json:"query"`
	Vector     []float64 `json:"vector"`
	Projection []float64 `json:"projection"`
	IsDynamic  bool      `json:"is_dynamic"`
}

type QueryProjectionMetadata struct {
	Model         string `json:"model"`
	ReferenceData struct {
		OriginalData string `json:"original_data"`
		KnownQueries string `json:"known_queries"`
	} `json:"reference_data"`
}

type QueryProjectionResponse struct {
	Queries        []QueryVector           `json:"queries"`
	Algorithm      string                  `json:"algorithm"`
	ProjectionMode string                  `json:"projection_mode"`
	ElapsedSeconds float64                 `json:"elapsed_seconds"`
	Metadata       QueryProjectionMetadata `json:"metadata"`
}

func (c *Client) QueryProjection(req QueryProjectionRequest) (*QueryProjectionResponse, error) {
	url := fmt.Sprintf("%s/query_projection", c.baseURL)

	var respData QueryProjectionResponse
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

// GenerateJSONFromKnowledgeLRequest for generating JSON from MySQL
type GenerateJSONFromKnowledgeLRequest struct {
	CollectionID    []uint64               `json:"collection_ids"`
	OutputPath      string                 `json:"output_path"`
	SplitParams     map[string]interface{} `json:"split_params"`
	ContributorId   uint64                 `json:"contributor_id"`
	ContributorType uint64                 `json:"contributor_type"`
}

// GenerateJSONFromRequestResponse for generating JSON from MySQL response
type GenerateJSONFromRequestResponse struct {
	CollectionID   []int   `json:"collection_id"`
	OutputPath     string  `json:"output_path"`
	ElapsedSeconds float64 `json:"elapsed_seconds"`
	DataCount      int     `json:"data_count"`
}

// GenerateJSONFromKnowledge generates JSON from MySQL data
func (c *Client) GenerateJSONFromKnowledge(req GenerateJSONFromKnowledgeLRequest) (*GenerateJSONFromRequestResponse, error) {
	url := fmt.Sprintf("%s/generate_json_from_knowledge", c.baseURL)

	var respData GenerateJSONFromRequestResponse
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

// GenerateUserQADataRequest for generating user QA data
type GenerateUserQADataRequest struct {
	AssistantnID []uint64 `json:"assistant_id"`
	OutputPath   string   `json:"output_path"`
}

// GenerateUserQADataResponse for generating user QA data response
type GenerateUserQADataResponse struct {
	AssistantnID   []int   `json:"assistant_id"`
	OutputFile     string  `json:"output_file"`
	ElapsedSeconds float64 `json:"elapsed_seconds"`
	DataCount      int     `json:"data_count"`
}

// GenerateUserQAData generates user QA data
func (c *Client) GenerateUserQAData(req GenerateUserQADataRequest) (*GenerateUserQADataResponse, error) {
	url := fmt.Sprintf("%s/generate_user_qa_data", c.baseURL)

	var respData GenerateUserQADataResponse
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

// GetContentRequest 定义获取内容的请求结构
type GetContentRequest struct {
	MD5 string `json:"md5"`
}

// GetContentResponse 定义获取内容的响应结构
type GetContentResponse struct {
	Status  string `json:"status"`
	Content string `json:"content"`
	MD5     string `json:"md5"`
}

// GetContent 通过MD5获取内容
func (c *Client) GetContent(req GetContentRequest) (*GetContentResponse, error) {
	url := fmt.Sprintf("%s/get_content", c.baseURL)

	// 准备请求体
	jsonBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建GET请求（注意：虽然用GET方法但带body，这是根据你的需求实现的）
	httpReq, err := http.NewRequest("GET", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("accept", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求执行失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("非预期的状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var apiResp GetContentResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查API返回状态
	if apiResp.Status != "success" {
		return nil, fmt.Errorf("API返回非成功状态: %s", apiResp.Status)
	}

	return &apiResp, nil
}

// postJSON Helper method for JSON POST requests
func (c *Client) postJSON(url string, reqBody interface{}, respData interface{}) error {
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var apiResp Response
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	if apiResp.Status != "success" {
		return fmt.Errorf("api returned non-success status: %s", apiResp.Status)
	}

	if err := json.Unmarshal(apiResp.Data, respData); err != nil {
		return fmt.Errorf("failed to unmarshal response data: %w", err)
	}

	return nil
}

// CheckPodsRequest 定义检查Pods的请求结构
type CheckPodsRequest struct {
	Namespace   string `json:"namespace"`
	PodPrefix   string `json:"pod_prefix"`
	IntervalSec int    `json:"interval_sec"`
}

// CheckPodsResponse 定义检查Pods的响应结构
type CheckPodsResponse struct {
	AllPodsRunning bool `json:"all_pods_running"`
}

// CheckPods 检查指定命名空间和前缀的Pods状态
func (c *Client) CheckPods(req CheckPodsRequest) (*CheckPodsResponse, error) {
	url := fmt.Sprintf("%s/k8s/check-pods", c.PodURL)

	var respData CheckPodsResponse
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

// UpdateResourcesRequest 定义更新资源的请求结构
type UpdateResourcesRequest struct {
	Namespace      string `json:"namespace"`
	Deployment     string `json:"deployment"`
	LimitsCPU      string `json:"limits_cpu"`
	LimitsMemory   string `json:"limits_memory"`
	RequestsCPU    string `json:"requests_cpu"`
	RequestsMemory string `json:"requests_memory"`
}

// UpdateResourcesResponse 定义更新资源的响应结构
type UpdateResourcesResponse struct {
	Message string `json:"message"`
}

// UpdateResources 更新K8s部署的资源限制和请求
func (c *Client) UpdateResources(req UpdateResourcesRequest) (*UpdateResourcesResponse, error) {
	url := fmt.Sprintf("%s/k8s/update-resources", c.PodURL)

	var respData UpdateResourcesResponse
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, err
	}

	return &respData, nil
}

// ProcessElasticsearchIndexRequest 定义处理Elasticsearch索引的请求结构
type ProcessElasticsearchIndexRequest struct {
	Index string `json:"index"` // 索引名称
}

// ProcessElasticsearchIndexData 定义data字段的结构
type ProcessElasticsearchIndexData struct {
	Index          string  `json:"index"`
	ElapsedSeconds float64 `json:"elapsed_seconds"`
}

// ProcessElasticsearchIndexResponse 定义完整响应结构
type ProcessElasticsearchIndexResponse struct {
	Status string                        `json:"status"`
	Data   ProcessElasticsearchIndexData `json:"data"`
}

// ProcessElasticsearchIndexCurl curl风格实现
func (c *Client) ProcessElasticsearchIndexCurl(indexName string, lang string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/process_elasticsearch_index", c.baseURL)

	// 准备请求体
	reqBody := map[string]interface{}{
		"index": indexName,
		"lang":  lang,
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建并发送请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求执行失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("非预期的状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查status字段
	if status, ok := result["status"].(string); !ok || status != "success" {
		return nil, fmt.Errorf("API返回非成功状态: %v", result["status"])
	}

	// 检查data字段是否存在
	if _, ok := result["data"]; !ok {
		return nil, fmt.Errorf("响应中缺少data字段")
	}

	return result, nil
}

// AddClusteringToProjectionRequest 定义添加聚类到投影的请求结构
type AddClusteringToProjectionRequest struct {
	InputFile        string    `json:"input_file"`        // 输入文件路径
	EnableCosUpload  bool      `json:"enable_cos_upload"` // 是否启用COS上传
	SplitSize        int       `json:"split_size"`        // 分割大小
	ClusteringMethod string    `json:"clustering_method,omitempty"`
	MinSamplesRange  []int32   `json:"min_samples_range,omitempty"`
	EpsRange         []float32 `json:"eps_range,omitempty"`
	NclustersRange   []int32   `json:"n_clusters_range,omitempty"`
}

// ClusterDistribution 定义聚类分布类型，键是聚类编号，值是该聚类的数据量
type ClusterDistribution map[string]int

// BestParams 定义最佳聚类参数
type BestParams struct {
	NClusters int `json:"n_clusters"` // 聚类数量
}

// KruskalResultData 定义Kruskal算法结果的数据结构
type KruskalResultData struct {
	InputFile            string  `json:"input_file"`             // 输入文件
	OutputFile           string  `json:"output_file"`            // 输出文件
	ConnectionFile       string  `json:"connection_file"`        // 连接文件
	TotalGroups          int     `json:"total_groups"`           // 总组数
	MSTEdges             int     `json:"mst_edges"`              // 最小生成树边数
	ConnectedEdges       int     `json:"connected_edges"`        // 连接边数
	AvgDistanceThreshold float64 `json:"avg_distance_threshold"` // 平均距离阈值
	Groups               [][]int `json:"groups"`                 // 分组情况
}

// KruskalResult 定义Kruskal算法完整结果
type KruskalResult struct {
	Status string            `json:"status"` // 状态
	Data   KruskalResultData `json:"data"`   // 数据
}

// AddClusteringToProjectionResponse 定义添加聚类到投影的响应结构
type AddClusteringToProjectionResponse struct {
	InputFile           string              `json:"input_file"`           // 输入文件路径
	ClusteringMethod    string              `json:"clustering_method"`    // 聚类方法
	BestParams          BestParams          `json:"best_params"`          // 最佳参数
	BestScore           float64             `json:"best_score"`           // 最佳分数
	NClusters           int                 `json:"n_clusters"`           // 聚类数量
	ClusterDistribution ClusterDistribution `json:"cluster_distribution"` // 聚类分布
	ModelPath           string              `json:"model_path"`           // 模型路径
	SplitSize           int                 `json:"split_size"`           // 分割大小
	TotalFiles          int                 `json:"total_files"`          // 总文件数
	ZipFile             string              `json:"zip_file"`             // 压缩文件路径
	ZipSize             int                 `json:"zip_size"`             // 压缩文件大小
	ElapsedSeconds      float64             `json:"elapsed_seconds"`      // 耗时(秒)
	CosPrefix           string              `json:"cos_prefix"`           // COS前缀
	CosPaths            []string            `json:"cos_paths"`            // COS路径列表
	ZipCosURL           string              `json:"zip_cos_url"`          // 压缩文件COS URL
	CentersCosURL       string              `json:"centers_cos_url"`      // 中心点COS URL
	ConnectionCosURL    string              `json:"connection_cos_url"`   // 连接COS URL
	CosUploadUsed       bool                `json:"cos_upload_used"`      // 是否使用了COS上传
	KruskalResult       KruskalResult       `json:"kruskal_result"`       // Kruskal算法结果
}

// AddClusteringToProjection 添加聚类到现有投影
// 参数: req - 包含输入文件路径、COS上传标志和分割大小的请求
// 返回: 聚类结果响应和错误信息
func (c *Client) AddClusteringToProjection(req AddClusteringToProjectionRequest) (*AddClusteringToProjectionResponse, error) {
	// 构造完整的API URL
	url := fmt.Sprintf("%s/add_clustering_to_projection", c.baseURL)

	// 准备响应数据结构
	var respData AddClusteringToProjectionResponse

	// 使用已有的postJSON辅助方法发送请求
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, fmt.Errorf("添加聚类到投影失败: %w", err)
	}

	return &respData, nil
}

// AddNewClusteringToProjection 添加聚类到现有投影
// 参数: req - 包含输入文件路径、COS上传标志和分割大小的请求
// 返回: 聚类结果响应和错误信息
func (c *Client) AddNewClusteringToProjection(req AddClusteringToProjectionRequest) (*AddClusteringToProjectionResponse, error) {
	// 构造完整的API URL
	url := fmt.Sprintf("%s/add_clustering_to_projection", c.CalcURL)

	// 准备响应数据结构
	var respData AddClusteringToProjectionResponse

	// 使用已有的postJSON辅助方法发送请求
	if err := c.postJSON(url, req, &respData); err != nil {
		return nil, fmt.Errorf("添加聚类到投影失败: %w", err)
	}

	return &respData, nil
}
