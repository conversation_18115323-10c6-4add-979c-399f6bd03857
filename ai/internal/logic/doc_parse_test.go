package logic

import (
	"context"
	"fmt"
	"os"
	"testing"

	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos"
)

func TestMain(m *testing.M) {
	b := &boot.Bootstrapper{
		ConfigFile: "../../bin/etc/ai.toml",
	}
	if err := b.<PERSON>(xcos.BootCos); err != nil {
		panic(err)
	}
	os.Exit(m.Run())
}

func TestCosHelper_GenCosFullUrl(t *testing.T) {
	path := "atlas_ai/nkjpz5b3w8ge/789b81f1e28a9104a42ca599efd70ae1/企业微信截图_99cd76fa-9368-419e-9436-175d88c832f7.png"
	url, err := new(CosHelper).GenCosSignedUrl(context.Background(), path)
	if err != nil {
		fmt.Println(err.Error())
	}
	fmt.Println(url)
}

func TestDocFileTypeBySuffix(t *testing.T) {
	tests := []struct {
		name           string
		path           string
		expectedType   int
		expectedSuffix string
	}{
		{
			name:           "txt file with hash in name",
			path:           "/atlas_ai/100005/83cdddbcbff/# 杭州市上城区星觉醒社会工作服务中心（简称星觉醒）组织简介.txt",
			expectedType:   DocFileTypeTxt,
			expectedSuffix: ".txt",
		},
		{
			name:           "pdf file with hash in name",
			path:           "/path/# test document.pdf",
			expectedType:   DocFileTypePdf,
			expectedSuffix: ".pdf",
		},
		{
			name:           "docx file with hash in name",
			path:           "/example.com/# 测试文档.docx",
			expectedType:   DocFileTypeDocx,
			expectedSuffix: ".docx",
		},
		{
			name:           "unsupported file type",
			path:           "/path/# test.unknown",
			expectedType:   DocFileTypeUnsupported,
			expectedSuffix: ".unknown",
		},
		{
			name:           "audio file with hash",
			path:           "/path/# 音频文件.mp3",
			expectedType:   DocFileTypeAudio,
			expectedSuffix: ".mp3",
		},
		{
			name:           "video file with hash",
			path:           "/path/# 视频文件.mp4",
			expectedType:   DocFileTypeVideo,
			expectedSuffix: ".mp4",
		},
		{
			name:           "excel file with hash",
			path:           "/path/# 表格文件.xlsx",
			expectedType:   DocFileTypeXlsx,
			expectedSuffix: ".xlsx",
		},
		{
			name:           "ppt file with hash",
			path:           "/path/# 演示文稿.pptx",
			expectedType:   DocFileTypePpt,
			expectedSuffix: ".pptx",
		},
		{
			name:           "image file with hash",
			path:           "/path/# 图片文件.png",
			expectedType:   DocFileTypePicture,
			expectedSuffix: ".png",
		},
		{
			name:           "csv file with hash",
			path:           "/path/# 数据文件.csv",
			expectedType:   DocFileTypeCsv,
			expectedSuffix: ".csv",
		},
		{
			name:           "epub file with hash",
			path:           "/path/# 电子书.epub",
			expectedType:   DocFileTypeEpub,
			expectedSuffix: ".epub",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fileType, suffix := DocFileTypeBySuffix(tt.path)
			if fileType != tt.expectedType {
				t.Errorf("DocFileTypeBySuffix(%q) type = %d, expected %d", tt.path, fileType, tt.expectedType)
			}
			if suffix != tt.expectedSuffix {
				t.Errorf("DocFileTypeBySuffix(%q) suffix = %q, expected %q", tt.path, suffix, tt.expectedSuffix)
			}
		})
	}
}
