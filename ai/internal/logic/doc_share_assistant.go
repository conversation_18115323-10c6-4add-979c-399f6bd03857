package logic

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// SyncDocShareAssistant 创建知识库同步or取消至助手
/*
 @param ctx
 @param senderType 发送者的身份类型 1: 前台用户 2: 前台团队
 @param senderId 发送者ID
 @param docId 知识库ID
 @param assistantIds 目标AI助手ID列表
 @return map[AI助手ID]状态 状态1: 启用 2: 停用

 assistantIds = 0，对当前docId全部取消分享的作用
*/
func SyncDocShareAssistant(ctx context.Context, senderType basepb.IdentityType, senderId uint64, docIdOrSlice any, assistantIds []uint64) error {
	var docId []uint64
	switch docIdOrSlice.(type) {
	case uint64:
		docId = []uint64{docIdOrSlice.(uint64)}
	case []uint64:
		docId = docIdOrSlice.([]uint64)
	}
	if len(docId) == 0 {
		return errors.New("assistantIds docId para is empty")
	}

	var assistantDocs []*model.TAssistantDoc
	mtx := sync.Mutex{}
	ctx = context.WithoutCancel(ctx)

	wg := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	for _, id := range assistantIds {
		assistantId := id

		wg.SafeGo(func(ctx context.Context) error {
			// 1、查询配置是否有分享设置
			shareReceiver, err := model.NewQuery[model.TShareReceiver](ctx).Where("admin_assistant_id = ? ", assistantId).Find()
			if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
				return err
			}

			// 没有设置默认不处理
			if shareReceiver != nil && (shareReceiver.ID == 0 || shareReceiver.ReceiverState == aipb.DocShareAcceptState_DOC_SHARE_ACCEPT_STATE_DISABLED) {
				return nil
			}

			var shareReceiverUser *model.TShareReceiverUser
			sqlCond := " user_id "
			// 2、有用户具体规则判断
			switch senderType {
			case basepb.IdentityType_IDENTITY_TYPE_TEAM:
				sqlCond = " team_id "
			}

			shareReceiverUser, err = model.NewQuery[model.TShareReceiverUser](ctx).Where("admin_assistant_id = ? and "+sqlCond+" = ?",
				assistantId, senderId).Find()
			if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
				return err
			}

			var isHited bool
			mtx.Lock()
			defer mtx.Unlock()
			// 命中了用户指定用户、团队的设置， 配置有效且未设置不接收状态
			if shareReceiverUser != nil && shareReceiverUser.ID > 0 {
				// 命中了特定配置
				isHited = true
				if shareReceiverUser.State != aipb.DocShareState_DOC_SHARE_STATE_NOT_ACCEPTED {
					for _, v := range docId {
						assistantDocs = append(assistantDocs, &model.TAssistantDoc{
							DocID: v, AssistantID: assistantId,
							State: aipb.DocState(shareReceiverUser.State), IsShared: 2,
						})
					}
				}
			}

			// 如果没有命中规则，且其余贡献者未设置不接收
			if !isHited && shareReceiver.OtherState != aipb.DocShareState_DOC_SHARE_STATE_NOT_ACCEPTED {
				log.WithContext(ctx).Debugf("Sync doc to assisatnt using other state: %v, assistantId: %v, docId: %v", shareReceiver.OtherState, assistantId, docId)
				for _, v := range docId {
					assistantDocs = append(assistantDocs, &model.TAssistantDoc{
						DocID: v, AssistantID: assistantId,
						State: aipb.DocState(shareReceiver.OtherState), IsShared: 2,
					})
				}
			}

			return nil
		})

	}

	wg.Wait()

	oldAssistantDocs, err := model.NewQuery[model.TAssistantDoc](ctx).GetBy("doc_id in (?) and is_shared = 2", docId)
	if err != nil {
		return err
	}

	addSyncDocs, delSyncDocs := getToBeSyncDoc(oldAssistantDocs, assistantDocs)
	log.WithContext(ctx).Infow("SyncDocShareAssistant getToBeSyncDoc", "addSyncDocs", addSyncDocs, "delSyncDocs", delSyncDocs)

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		// 先清空原docId
		if err := tx.Model(&model.TAssistantDoc{}).Delete(nil, "doc_id in (?) and is_shared = 2", docId).Error; err != nil {
			return err
		}

		// OnConflict 不做操作，因为部分 doc-assiatant_id对 已经存在了
		if err := tx.Model(&model.TAssistantDoc{}).Clauses(clause.OnConflict{DoNothing: true}).
			CreateInBatches(assistantDocs, 100).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	if len(addSyncDocs) == 0 && len(delSyncDocs) == 0 {
		return nil
	}
	allAssistants := make([]*model.TAssistant, 0)
	err = model.NewQuery[model.TAssistant](ctx).DB().Select("ID").Scopes(model.AssistantWithCollection).Where("1=1").Find(&allAssistants).Error
	if err != nil {
		return err
	}

	// 同步更新向量操作
	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		var docs []*model.TDoc
		err = tx.Model(&model.TDoc{}).Find(&docs, "id in ?", docId).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		if err != nil {
			return err
		}

		if len(addSyncDocs) > 0 {
			var toAdd []*model.TDoc
			for _, doc := range docs {
				if len(addSyncDocs[doc.ID]) != 0 {
					doc.Assistants = allAssistants
					doc.States = addSyncDocs[doc.ID]
					toAdd = append(toAdd, doc)
				}
			}
			err = SyncDocToDBLogBulk(ctx, tx, nil, toAdd)
			if err != nil {
				return err
			}
		}

		if len(delSyncDocs) > 0 {
			var toDel []*model.TDoc
			for _, doc := range docs {
				if len(delSyncDocs[doc.ID]) != 0 {
					doc.Assistants = allAssistants
					doc.States = delSyncDocs[doc.ID]
					toDel = append(toDel, doc)
				}
			}
			err = SyncDocToDBLogBulk(ctx, tx, toDel, nil)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}

	// 更新了助手绑定，异步存储超长提示
	xsync.Go(context.Background(), func(ctx context.Context) error {
		err := StoreOverSizedTipAllAssistantBulkAsync(ctx, docId)
		if err != nil {
			log.WithContext(ctx).Errorf("failed to store over sized tip for doc %v, err: %v", docId, err)
			return err
		}
		return nil
	}, boot.TraceGo(ctx))
	return nil
}

// getToBeSyncDoc 待同步的文档
/*
  @param oldAssistantDocs 原数据
  @param assistantDocs 新数据

  @return addSyncDocs 新增的文档
  @return delSyncDocs 删除的文档
*/
func getToBeSyncDoc(oldAssistantDocs []*model.TAssistantDoc, assistantDocs []*model.TAssistantDoc) (map[uint64][]*model.TAssistantDoc,
	map[uint64][]*model.TAssistantDoc,
) {
	oldDocMap := make(map[string]*model.TAssistantDoc)
	newDocMap := make(map[string]*model.TAssistantDoc)
	addSyncDocs := make(map[uint64][]*model.TAssistantDoc)
	delSyncDocs := make(map[uint64][]*model.TAssistantDoc)

	// 构建旧数据的哈希表
	for _, oldDoc := range oldAssistantDocs {
		key := fmt.Sprintf("%d_%d", oldDoc.AssistantID, oldDoc.DocID)
		oldDocMap[key] = oldDoc
	}

	// 构新数据的哈希表
	for _, newDoc := range assistantDocs {
		key := fmt.Sprintf("%d_%d", newDoc.AssistantID, newDoc.DocID)
		newDocMap[key] = newDoc
	}

	for _, newDoc := range assistantDocs {
		key := fmt.Sprintf("%d_%d", newDoc.AssistantID, newDoc.DocID)

		// 如果新旧数据状态一致且为启用状态，则从哈希表中删除
		if _, exists := oldDocMap[key]; exists && oldDocMap[key].State == newDoc.State && newDoc.State == aipb.DocState_DOC_STATE_ENABLED {
			delete(oldDocMap, key)
			delete(newDocMap, key)
		}

	}

	for _, delDoc := range oldDocMap {
		if delDoc.State == aipb.DocState_DOC_STATE_ENABLED {
			delSyncDocs[delDoc.DocID] = append(delSyncDocs[delDoc.DocID], delDoc)
		}
	}

	for _, newDoc := range newDocMap {
		if newDoc.State == aipb.DocState_DOC_STATE_ENABLED {
			addSyncDocs[newDoc.DocID] = append(addSyncDocs[newDoc.DocID], newDoc)
		}
	}

	return addSyncDocs, delSyncDocs
}

// GetCreateDocShareAssistantList 获取创建文档分享助手的列表
/*
  @param ctx context.Context
  @param adminType 1: 前台用户 2: 前台团队
  @param createBy 创建人
  @return []uint64 助手id列表
*/
func GetCreateDocShareAssistantList(ctx context.Context, adminType basepb.IdentityType, createBy uint64) ([]uint64, error) {
	shareSender, err := model.NewQuery[model.TShareSender](ctx).
		Where("admin_type = ? and  create_by = ? and receiver_type = ?", adminType, createBy, aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_ASSISTANT).Get()
	if err != nil {
		return nil, err
	}

	var assistantIds []uint64
	for _, s := range shareSender {
		assistantIds = append(assistantIds, s.ReceiverID)
	}

	return assistantIds, nil
}

// IsMyAssistant 是否是我的助手
func IsMyAssistant(ctx context.Context, assistantId uint64, adminType basepb.IdentityType, createBy uint64) (bool, error) {
	num, err := model.NewQuery[model.TAssistantAdmin](ctx).Where("assistant_id = ? and admin_id = ? and admin_type = ? ", assistantId,
		createBy, adminType).Count()
	if err != nil {
		return false, err
	}

	return num > 0, nil
}
