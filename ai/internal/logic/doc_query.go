package logic

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tql"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
)

// PreloadForListDocQueryTextFile list 接口preload关系
func PreloadForListDocQueryTextFile(db *gorm.DB) *gorm.DB {
	return db.Preload("References").
		Preload("TextReferences").
		Preload("DocReferences").
		Preload("Assistants").
		Preload("Contributors").
		Preload("States").
		Preload("SyncVersions").
		Preload("DocShares")
}

// PreloadForListDocQueryQA QA preload关系
func PreloadForListDocQueryQA(db *gorm.DB) *gorm.DB {
	return db.Preload("References").
		Preload("TextReferences").
		Preload("DocReferences").
		Preload("Assistants").
		Preload("Contributors").
		Preload("States").
		Preload("SyncVersions").
		Preload("DocShares").
		Preload("MatchPatterns")
}

// ListTextFileApplyFilter ...
func ListTextFileApplyFilter(ctx context.Context, req *ai.ReqListTextFile) (*gorm.DB, []uint64, error) {
	// 可以被列表查到的 doc 状态，启用/禁用/删除中
	statesCanList := []ai.DocState{ai.DocState_DOC_STATE_ENABLED, ai.DocState_DOC_STATE_DISABLED, ai.DocState_DOC_STATE_DELETING}

	db := model.NewQuery[model.TDoc](ctx).DB()
	if len(req.AssistantId) != 0 {
		assistantQuote := model.NewQuery[model.TDoc](ctx).DB()
		// 0 特殊处理，代表没有绑定助手
		// 需要限定在管理的助手内
		if slices.Contains(req.AssistantId, 0) {
			assistantQuote.Scopes(model.DocWithNoAssistantIdFilter(req.GetTenantCond().GetAssistantId(), true))
		}
		req.AssistantId = slices.DeleteFunc(req.AssistantId, func(u uint64) bool {
			return u == 0
		})
		if len(req.AssistantId) != 0 {
			assistantQuote.Scopes(model.DocWithAssistantIdFilter(req.AssistantId, false, statesCanList, true))
		}
		db.Where(assistantQuote)
	}
	if len(req.ExcludedAssistantId) != 0 {
		db.Scopes(model.DocWithAssistantIdFilter(req.ExcludedAssistantId, true, nil))
	}
	db.Scopes(PreloadForListDocQueryTextFile)
	// 外部数据源，查询数据源同步状态
	if req.DataSource > ai.DocDataSource_DOC_DATA_SOURCE_COLLECTION {
		db.Preload("DocDataSource", func(db *gorm.DB) *gorm.DB {
			return db.Select("doc_id", "state")
		})
		if req.DataSourceState != 0 {
			db.Scopes(model.DocWithDataSourceStateFilter(req.DataSourceState))
		}
	}
	if req.WithCopies {
		db.Preload("Copies", func(db *gorm.DB) *gorm.DB {
			return db.Preload("References").Preload("Assistants").Preload("Contributors").Preload("States").
				Where("is_copy = ?", 1)
		})
	}
	var tenantScopeAssistantId []uint64
	var scopedAssistantId []uint64
	if len(req.AssistantId) != 0 {
		scopedAssistantId = req.AssistantId
	} else if len(req.GetTenantCond().GetAssistantId()) != 0 {
		scopedAssistantId = req.GetTenantCond().GetAssistantId()
	}
	tenantScopeAssistantId = req.GetTenantCond().GetAssistantId()
	if req.State != 0 {
		db.Scopes(model.DocWithStateFilter([]ai.DocState{req.State}, scopedAssistantId...))
	}
	applyShareStateFilter(db, req.SharedState, tenantScopeAssistantId...)
	// db.Scopes(model.DocWithSharedAssistantIdFilter(req.SharedAssistant, true))
	if len(req.Contributor) != 0 {
		db.Scopes(model.WithContributorFilter(req.Contributor))
	}
	if len(req.UpdateBy) != 0 {
		db.Scopes(model.DocWithUpdateByFilter(req.UpdateBy...))
	}
	if len(req.CreateBy) != 0 {
		db.Scopes(model.DocWithCreateByFilter(req.CreateBy...))
	}
	if req.GroupRepeated {
		db.Scopes(model.DocWithMd5Repeated())
		db.Order("index_text_md5, id desc")
	}
	if req.Search != nil {
		if req.Search.Text != "" {
			db.Scopes(model.DocWithColumnMatch("index_text", req.Search.Text))
		}
		if req.Search.FileName != "" {
			key := "%" + xorm.EscapeLikeWildcards(req.Search.FileName) + "%"
			db.Where("file_name LIKE ?", key)
		}
		if len(req.Search.FullFileName) != 0 {
			// db.Where("file_name IN ?", req.Search.FullFileName)
			var hashs []string
			for _, f := range req.Search.FullFileName {
				hashs = append(hashs, util.CalculateMD5String(f))
			}
			db.Where("unique_hash IN ?", hashs)
		}
		if req.Search.UgcTitle != "" {
			key := "%" + xorm.EscapeLikeWildcards(req.Search.UgcTitle) + "%"
			db.Where("ugc_title LIKE ?", key)
		}
	}
	if req.Mask != nil {
		db.Select(req.Mask.Paths)
	}
	if req.DocType != nil {
		db.Where("data_type in ?", req.DocType)
	} else {
		db.Where("data_type in (2,3)")
	}
	if len(req.Id) != 0 {
		db.Where("id IN ?", req.Id)
	}
	if req.ShowContributor != 0 {
		db.Where("show_contributor = ?", req.ShowContributor)
	}
	// if !req.IgnoreIsSystem {
	// 	if req.IsSystem {
	// 		db.Where("is_system = ?", 1)
	// 	} else {
	// 		db.Where("is_system = ?", 0)
	// 	}
	// }
	if req.DataSource != 0 {
		db.Where("data_source = ?", req.DataSource)
	}
	if !req.IgnoreIsCopy {
		if req.IsCopy {
			db.Where("is_copy = ?", 1)
		} else {
			db.Where("is_copy = ?", 0)
		}
	}
	if len(req.UgcType) > 0 {
		db.Where("ugc_type IN ?", req.UgcType)
	}
	if len(req.ContentState) > 0 {
		db.Where("content_state IN ?", req.ContentState)
	}
	if req.ParseState > 0 {
		db.Where("state = ?", req.ParseState)
	}
	orGroup := model.NewQuery[model.TDoc](ctx).DB()
	// 检查是否正在筛选"收到分享"，如果是，则不包含贡献者和助手这个 or 组查询
	isFilteringReceivedShare := req.SharedState != nil &&
		len(req.SharedState.SharedState) > 0

	if len(req.GetTenantCond().GetContributor()) != 0 && !isFilteringReceivedShare {
		orGroup = orGroup.Scopes(model.WithContributorFilter(req.GetTenantCond().GetContributor()))
		contributor := req.GetTenantCond().GetContributor()[0]
		switch contributor.Contributor.Type {
		case base.IdentityType_IDENTITY_TYPE_USER:
			orGroup.Scopes(model.DocWithShareTargetIdFilter([]uint64{contributor.Contributor.Id}, ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER, true))
		case base.IdentityType_IDENTITY_TYPE_TEAM:
			orGroup.Scopes(model.DocWithShareTargetIdFilter([]uint64{contributor.Contributor.Id}, ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM, true))
		}
	}
	if len(req.GetTenantCond().GetAssistantId()) != 0 && !isFilteringReceivedShare {
		orGroup.Scopes(model.DocWithAssistantIdFilter(req.GetTenantCond().GetAssistantId(), false, statesCanList, true))
	}
	db.Where(orGroup).Scopes(model.DocWithParseModeFilter(req.ParseMode)).Preload("DocExtend")

	// 处理TipFilter - 过滤解析失败或表格过长的记录
	if req.GetTipFilter() != nil {
		db = NewDocTipLogic().DocWithTipFilter(ctx, db, ai.DocType_DOCTYPE_FILE, req.GetTipFilter(), tenantScopeAssistantId)
	}

	if req.TextExcerpt {
		// if req.GetSearch().GetText() != "" {
		// 	db.Select("*,SUBSTRING(index_text, GREATEST(LOCATE(?, index_text)-16, 1), 300) AS index_text", req.Search.Text).
		// 		Omit("index_text")
		// } else {
		db.Omit("index_text")
		// }
	} else {
		db.Omit("index_text_cerpt")
	}
	labelObjType := model.DocSource2CustomLabelObjectType(req.DataSource)
	db.Scopes(model.WithLabels(labelObjType, req.LabelTenant, req.Labels, req.WithLabelDetailInfo))

	// 高级查询
	if len(req.Tql) != 0 {
		exp, err := tql.Parse(req.Tql)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid tql: %w", err)
		}
		db.Scopes(DocWithTQL(exp, &LabelFilterMeta{Tenant: req.LabelTenant, ObjectType: labelObjType}, false))
	}
	if req.DownloadAsRef != ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_UNSPECIFIED {
		db.Where("download_as_ref = ?", req.DownloadAsRef)
	}
	if req.SharedReceivers != nil {
		db.Scopes(model.DocWithSharedReceiverFilter(req.SharedReceivers))
	}
	return db, tenantScopeAssistantId, nil
}

// ListQAApplyFilter ...
func ListQAApplyFilter(ctx context.Context, req *ai.ReqListQA) (*gorm.DB, []uint64, error) {
	statesCanList := []ai.DocState{ai.DocState_DOC_STATE_ENABLED, ai.DocState_DOC_STATE_DISABLED, ai.DocState_DOC_STATE_DELETING}
	db := model.NewQuery[model.TDoc](ctx).DB()
	if len(req.AssistantId) != 0 {
		assistantQuote := model.NewQuery[model.TDoc](ctx).DB()
		// 0 特殊处理，代表没有绑定助手
		// 需要限定在管理的助手内
		if slices.Contains(req.AssistantId, 0) {
			assistantQuote.Scopes(model.DocWithNoAssistantIdFilter(req.GetTenantCond().GetAssistantId(), true))
		}
		req.AssistantId = slices.DeleteFunc(req.AssistantId, func(u uint64) bool {
			return u == 0
		})
		if len(req.AssistantId) != 0 {
			assistantQuote.Scopes(model.DocWithAssistantIdFilter(req.AssistantId, false, statesCanList, true))
		}
		db.Where(assistantQuote)
	}
	if len(req.ExcludedAssistantId) != 0 {
		db.Scopes(model.DocWithAssistantIdFilter(req.ExcludedAssistantId, true, nil))
	}
	db.Scopes(PreloadForListDocQueryQA)
	var tenantScopeAssistantId []uint64
	var scopedAssistantId []uint64
	if len(req.AssistantId) != 0 {
		scopedAssistantId = req.AssistantId
	} else if len(req.GetTenantCond().GetAssistantId()) != 0 {
		scopedAssistantId = req.GetTenantCond().GetAssistantId()
	}
	tenantScopeAssistantId = req.GetTenantCond().GetAssistantId()
	if req.State != 0 {
		db.Scopes(model.DocWithStateFilter([]ai.DocState{req.State}, scopedAssistantId...))
	}
	applyShareStateFilter(db, req.SharedState, tenantScopeAssistantId...)
	if len(req.Contributor) != 0 {
		db.Scopes(model.WithContributorFilter(req.Contributor))
	}
	if len(req.UpdateBy) != 0 {
		db.Scopes(model.DocWithUpdateByFilter(req.UpdateBy...))
	}
	if len(req.CreateBy) != 0 {
		db.Scopes(model.DocWithCreateByFilter(req.CreateBy...))
	}
	if req.GroupRepeated {
		db.Scopes(model.DocWithMd5Repeated())
		db.Order("index_text_md5, id desc")
	}

	if req.Search != nil {
		if req.Search.Qa != "" {
			subquery := model.NewQuery[model.TDoc](ctx).DB()
			subquery.Scopes(model.DocWithColumnMatch("index_text", req.Search.Qa))
			subquery.Scopes(model.DocWithColumnMatch("text", req.Search.Qa, true))
			db.Where(subquery)
		}
	}
	if len(req.Id) != 0 {
		db.Where("id IN (?)", req.Id)
	}
	if req.ShowContributor != 0 {
		db.Where("show_contributor = ?", req.ShowContributor)
	}
	if len(req.MatchPatterns) > 0 {
		matchPatternSql := "EXISTS (SELECT 1 FROM t_doc_match_pattern AS mp WHERE t_doc.id = mp.doc_id AND mp.match_pattern IN (?))"
		db.Where(matchPatternSql, req.MatchPatterns)
	}
	orGroup := model.NewQuery[model.TDoc](ctx).DB()
	// 检查是否正在筛选"收到分享"，如果是，则不包含贡献者条件
	isFilteringReceivedShare := req.SharedState != nil &&
		len(req.SharedState.SharedState) > 0

	if len(req.GetTenantCond().GetContributor()) != 0 && !isFilteringReceivedShare {
		orGroup.Scopes(model.WithContributorFilter(req.GetTenantCond().GetContributor()))
		contributor := req.GetTenantCond().GetContributor()[0]
		switch contributor.Contributor.Type {
		case base.IdentityType_IDENTITY_TYPE_USER:
			orGroup.Scopes(model.DocWithShareTargetIdFilter([]uint64{contributor.Contributor.Id}, ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER, true))
		case base.IdentityType_IDENTITY_TYPE_TEAM:
			orGroup.Scopes(model.DocWithShareTargetIdFilter([]uint64{contributor.Contributor.Id}, ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM, true))
		}
	}
	if len(req.GetTenantCond().GetAssistantId()) != 0 && !isFilteringReceivedShare {
		orGroup.Scopes(model.DocWithAssistantIdFilter(req.GetTenantCond().GetAssistantId(), false, statesCanList, true))
	}
	db.Where(orGroup)
	db.Where("data_type = ?", 1)
	db.Scopes(model.WithLabels(ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA, req.LabelTenant, req.Labels, req.WithLabelDetailInfo))
	// 高级查询
	if len(req.Tql) != 0 {
		exp, err := tql.Parse(req.Tql)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid tql: %w", err)
		}
		db.Scopes(DocWithTQL(exp, &LabelFilterMeta{Tenant: req.LabelTenant, ObjectType: ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA}, true))
	}
	// 处理TipFilter - 过滤问题超长的记录
	if req.GetTipFilter() != nil {
		db = NewDocTipLogic().DocWithTipFilter(ctx, db, ai.DocType_DOCTYPE_QA, req.GetTipFilter(), tenantScopeAssistantId)
	}
	if req.SharedReceivers != nil {
		db.Scopes(model.DocWithSharedReceiverFilter(req.SharedReceivers))
	}
	return db, tenantScopeAssistantId, nil
}

// 分享的状态筛选
func applyShareStateFilter(db *gorm.DB, filter *ai.ListDocSharedFileter, scopedAssistantId ...uint64) *gorm.DB {
	if filter != nil {
		state := filter.SharedState
		ctx := db.Statement.Context
		subQuery := model.NewQuery[model.TDoc](ctx).DB()
		for _, v := range state {
			d := model.NewQuery[model.TDoc](ctx).DB()
			switch v {
			case ai.DocSharedState_DOC_SHARED_STATE_RECEIVED:
				// 收到分享：查找收到分享的文档，但排除自己是贡献者的文档
				d.Scopes(model.DocWithReceivedShareFilter(filter.Contributor.Contributor.Type, filter.Contributor.Contributor.Id, scopedAssistantId))
				d.Scopes(model.WithContributorFilter([]*ai.ContributorFilter{filter.Contributor}, true))
			case ai.DocSharedState_DOC_SHARED_STATE_ENABLED:
				// 分享的已启用
				d.Scopes(model.DocWithSharedStateFilter([]ai.DocState{ai.DocState_DOC_STATE_ENABLED}))
				d.Scopes(model.WithContributorFilter([]*ai.ContributorFilter{filter.Contributor}))
			case ai.DocSharedState_DOC_SHARED_STATE_DISABLED:
				// 分享的已禁用
				d.Scopes(model.DocWithSharedStateFilter([]ai.DocState{ai.DocState_DOC_STATE_DISABLED, ai.DocState_DOC_STATE_DELETING, ai.DocState_DOC_STATE_UNBOUNDED}))
				d.Scopes(model.WithContributorFilter([]*ai.ContributorFilter{filter.Contributor}))
			}
			subQuery.Or(d)
		}
		db.Where(subQuery)
	}
	return db
}
