package logic

import (
	"context"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
	"gorm.io/gorm"
)

type systemDoc struct {
	doc    *model.TDoc
	copies []*model.TDoc
	newDoc *model.TDoc
}

func (d *systemDoc) pluckEnabledDoc() *model.TDoc {
	if d.isEnabled(d.doc) {
		return d.doc
	}
	for _, copy := range d.copies {
		if d.isEnabled(copy) {
			return copy
		}
	}
	return nil
}

func (d *systemDoc) hasCopyEnabled() bool {
	enabled := d.pluckEnabledDoc()
	if enabled == nil {
		return false
	}
	return enabled.IsCopy
}

func (d *systemDoc) isEnabled(doc *model.TDoc) bool {
	if doc == nil {
		return false
	}
	for _, state := range doc.States {
		if state.State == aipb.DocState_DOC_STATE_ENABLED {
			return true
		}
	}
	return false
}

// SyncSystemDocLogic 同步系统数据文档
type SyncSystemDocLogic struct{}

// Publish 发布
func (l *SyncSystemDocLogic) Publish(ctx context.Context, data *eventspb.SystemDocChanged_Doc) error {
	ugcDocs, err := l.queryUgcDocs(ctx, data.UgcId, data.UgcType)
	if err != nil {
		return err
	}
	textDoc, fileDocs := l.splitDocs(ugcDocs)

	var newFileDocs []*model.TDoc
	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		var err error

		if textDoc.doc == nil {
			err = l.createTextDoc(ctx, tx, textDoc, data)
		} else {
			err = l.updateTextDoc(ctx, tx, textDoc, data)
		}
		if err != nil {
			return err
		}

		if newFileDocs, err = l.createFileDocs(ctx, tx, data, fileDocs); err != nil {
			return err
		}

		if err = l.deleteFileDocs(ctx, tx, data, fileDocs); err != nil {
			return err
		}

		if err = l.publishFileDocs(ctx, tx, data, fileDocs); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	l.parseFiles(ctx, newFileDocs)

	return nil
}

// Delete 删除
func (l *SyncSystemDocLogic) Delete(ctx context.Context, data *eventspb.SystemDocChanged_Doc) error {
	ugcDocs, err := l.queryUgcDocs(ctx, data.UgcId, data.UgcType)
	if err != nil {
		return err
	}
	if len(ugcDocs) == 0 {
		log.WithContext(ctx).Infow("delete system doc: this ugc has no docs",
			"ugc_id", data.UgcId, "ugc_type", data.UgcType)
		return nil
	}

	textDoc, fileDocs := l.splitDocs(ugcDocs)

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		if err := l.deleteSystemDoc(ctx, tx, textDoc); err != nil {
			return err
		}

		for _, fileDoc := range fileDocs {
			if err := l.deleteSystemDoc(ctx, tx, fileDoc); err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

// Unpublish 下架
func (l *SyncSystemDocLogic) Unpublish(ctx context.Context, data *eventspb.SystemDocChanged_Doc) error {
	ugcDocs, err := l.queryUgcDocs(ctx, data.UgcId, data.UgcType)
	if err != nil {
		return err
	}
	if len(ugcDocs) == 0 {
		log.WithContext(ctx).Infow("unpublish system doc: this ugc has no docs",
			"ugc_id", data.UgcId, "ugc_type", data.UgcType)
		return nil
	}

	textDoc, fileDocs := l.splitDocs(ugcDocs)

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		if err := l.unpublishSystemDoc(ctx, tx, textDoc, data); err != nil {
			return err
		}

		for _, fileDoc := range fileDocs {
			if err := l.unpublishSystemDoc(ctx, tx, fileDoc, data); err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (l *SyncSystemDocLogic) queryUgcDocs(ctx context.Context,
	ugcID uint64, ugcType basepb.DataType,
) ([]*model.TDoc, error) {
	docs, err := model.NewQuery[model.TDoc](ctx).
		Where("ugc_id = ?", ugcID).
		Where("ugc_type = ?", ugcType).
		Where("data_source = ?", aipb.DocDataSource_DOC_DATA_SOURCE_UGC).
		With("States").
		Get()
	if err != nil {
		return nil, fmt.Errorf("query original docs: %w", err)
	}
	return docs, nil
}

func (l *SyncSystemDocLogic) splitDocs(docs []*model.TDoc) (*systemDoc, map[string]*systemDoc) {
	var (
		textDoc  = &systemDoc{}
		fileDocs = make(map[string]*systemDoc)
	)
	for _, doc := range docs {
		if doc == nil {
			continue
		}
		var sysDoc *systemDoc
		if doc.DataType == uint32(aipb.DocType_DOCTYPE_TEXT) {
			sysDoc = textDoc
		} else if doc.DataType == uint32(aipb.DocType_DOCTYPE_FILE) {
			if _, ok := fileDocs[doc.Ref.Url]; !ok {
				fileDocs[doc.Ref.Url] = &systemDoc{}
			}
			sysDoc = fileDocs[doc.Ref.Url]
		} else {
			continue
		}

		if doc.IsCopy {
			sysDoc.copies = append(sysDoc.copies, doc)
		} else {
			sysDoc.doc = doc
		}
	}
	return textDoc, fileDocs
}

func (l *SyncSystemDocLogic) createTextDoc(ctx context.Context,
	tx *gorm.DB, textDoc *systemDoc, data *eventspb.SystemDocChanged_Doc,
) error {
	newDoc := l.newTextDoc(data)

	if textDoc.hasCopyEnabled() {
		newDoc.ContentState = aipb.DocContentState_DOC_CONTENT_STATE_HAS_UPDATE
	}

	if err := tx.Create(newDoc).Error; err != nil {
		return fmt.Errorf("create text doc: %w", err)
	}

	// 更新main_id
	newDoc.MainID = newDoc.ID
	if err := tx.Select("main_id").Save(newDoc).Error; err != nil {
		return fmt.Errorf("update text doc main_id: %w", err)
	}

	// 触发同步
	if newDoc.ContentState == aipb.DocContentState_DOC_CONTENT_STATE_SYNCING {
		if err := OnOffDocInBulk(ctx, tx, aipb.DocState_DOC_STATE_ENABLED, []uint64{newDoc.ID}); err != nil {
			return fmt.Errorf("OnOffDocInBulk: %w", err)
		}
	}

	return nil
}

func (l *SyncSystemDocLogic) newTextDoc(data *eventspb.SystemDocChanged_Doc) *model.TDoc {
	doc := &model.TDoc{
		DataType:     uint32(aipb.DocType_DOCTYPE_TEXT),
		IndexText:    data.DocContent,
		IndexTextMd5: util.CalculateMD5String(data.DocContent),
		FileName:     data.DocName,
		RagFilename:  GenDocRagFileName(),
		// IsSystem:     true,
		DataSource: aipb.DocDataSource_DOC_DATA_SOURCE_UGC,
	}
	return l.fillNewDoc(doc, data)
}

func (l *SyncSystemDocLogic) newFileDocs(data *eventspb.SystemDocChanged_Doc) []*model.TDoc {
	docs := make([]*model.TDoc, 0, len(data.Files))
	for _, file := range data.Files {
		docs = append(docs, l.newFileDoc(data, file))
	}
	return docs
}

func (l *SyncSystemDocLogic) newFileDoc(
	data *eventspb.SystemDocChanged_Doc, file *eventspb.SystemDocChanged_File,
) *model.TDoc {
	fileDoc := &model.TDoc{
		DataType:    uint32(aipb.DocType_DOCTYPE_FILE),
		FileName:    file.FileName,
		RagFilename: GenDocRagFileName(),
		// IsSystem:    true,
		DataSource: aipb.DocDataSource_DOC_DATA_SOURCE_UGC,
		Ref: &model.DocReference{
			Name: file.FileName,
			Url:  file.FilePath,
		},
	}
	return l.fillNewDoc(fileDoc, data)
}

func (l *SyncSystemDocLogic) fillNewDoc(doc *model.TDoc, data *eventspb.SystemDocChanged_Doc) *model.TDoc {
	now := time.Now()

	doc.UgcID = data.UgcId
	doc.UgcType = data.UgcType
	doc.UgcTitle = data.UgcTitle
	doc.ShowContributor = l.showContributor(data.UgcType)
	doc.ContentState = aipb.DocContentState_DOC_CONTENT_STATE_SYNCING
	doc.UpdateDate = now
	doc.UpdateBy = data.UpdateBy.IdentityId
	doc.UpdateByType = data.UpdateBy.IdentityType
	doc.CreateDate = now
	doc.CreateBy = data.UpdateBy.IdentityId
	doc.CreateByType = data.UpdateBy.IdentityType

	assistantIDs := config.GetIntSlice("llm.collection.tanliveAssistants")
	for _, assistantID := range assistantIDs {
		doc.States = append(doc.States, &model.TAssistantDoc{
			AssistantID: uint64(assistantID),
			State:       aipb.DocState_DOC_STATE_DISABLED,
			IsShared:    2,
		})
	}

	doc.Contributors = l.newContributors(data.Contributors)

	return doc
}

func (l *SyncSystemDocLogic) newContributors(source []*basepb.Identity) []*model.TDocContributor {
	contributors := make([]*model.TDocContributor, 0, len(source))
	for _, contributor := range source {
		contributors = append(contributors, &model.TDocContributor{
			ContributorID:   contributor.IdentityId,
			ContributorType: uint32(contributor.IdentityType),
			ContributorText: contributor.Name,
		})
	}
	return contributors
}

func (l *SyncSystemDocLogic) updateTextDoc(ctx context.Context,
	tx *gorm.DB, textDoc *systemDoc, data *eventspb.SystemDocChanged_Doc,
) error {
	newDoc := *textDoc.doc
	changes := make([]string, 0)

	if newDoc.IndexText != data.DocContent {
		newDoc.IndexText = data.DocContent
		newDoc.IndexTextMd5 = util.CalculateMD5String(data.DocContent)
		changes = append(changes, "index_text", "index_text_md5")
	}
	if newDoc.UgcTitle != data.UgcTitle {
		newDoc.UgcTitle = data.UgcTitle
		changes = append(changes, "ugc_title")
	}
	if newDoc.FileName != data.DocName {
		newDoc.FileName = data.DocName
		changes = append(changes, "file_name")
	}

	hasCopyEnabled := textDoc.hasCopyEnabled()
	if newDoc.IndexTextMd5 != textDoc.doc.IndexTextMd5 && hasCopyEnabled {
		newDoc.ContentState = aipb.DocContentState_DOC_CONTENT_STATE_HAS_UPDATE
		changes = append(changes, "content_state")
	} else if newDoc.ContentState == aipb.DocContentState_DOC_CONTENT_STATE_UNPUBLISHED {
		newDoc.ContentState = aipb.DocContentState_DOC_CONTENT_STATE_SYNCING
		changes = append(changes, "content_state")
	}

	newDoc.UpdateBy = data.UpdateBy.IdentityId
	newDoc.UpdateByType = data.UpdateBy.IdentityType
	newDoc.UpdateDate = time.Now()
	changes = append(changes, "update_by", "update_by_type", "update_date")

	if err := tx.Select(changes).Save(&newDoc).Error; err != nil {
		return fmt.Errorf("update text doc: %w", err)
	}

	// 同步贡献者
	if err := tx.Model(&newDoc).
		Association("Contributors").Unscoped().Replace(l.newContributors(data.Contributors)); err != nil {
		return fmt.Errorf("upserting doc contributors: %w", err)
	}

	textDoc.newDoc = &newDoc

	if !hasCopyEnabled {
		if err := OnOffDocInBulk(ctx, tx, aipb.DocState_DOC_STATE_ENABLED, []uint64{newDoc.ID}); err != nil {
			return fmt.Errorf("OnOffDocInBulk: %w", err)
		}
	}

	return nil
}

func (l *SyncSystemDocLogic) createFileDocs(ctx context.Context,
	tx *gorm.DB, data *eventspb.SystemDocChanged_Doc, fileDocs map[string]*systemDoc,
) ([]*model.TDoc, error) {
	newFileDocs := make([]*model.TDoc, 0)
	for _, file := range data.Files {
		if _, ok := fileDocs[file.FilePath]; !ok {
			newFileDocs = append(newFileDocs, l.newFileDoc(data, file))
		}
	}
	if len(newFileDocs) > 0 {
		if err := tx.Create(newFileDocs).Error; err != nil {
			return nil, fmt.Errorf("create file docs: %w", err)
		}

		// 更新main_id
		ids := make([]uint64, 0, len(newFileDocs))
		for _, doc := range newFileDocs {
			ids = append(ids, doc.ID)
		}
		if err := tx.Model(&model.TDoc{}).Where("id in ?", ids).Update("main_id", gorm.Expr("id")).Error; err != nil {
			return nil, fmt.Errorf("update main_id: %w", err)
		}
	}
	return newFileDocs, nil
}

func (l *SyncSystemDocLogic) deleteFileDocs(ctx context.Context,
	tx *gorm.DB, data *eventspb.SystemDocChanged_Doc, fileDocs map[string]*systemDoc,
) error {
	newFilePaths := make(map[string]bool)
	for _, file := range data.Files {
		newFilePaths[file.FilePath] = true
	}

	deleteIDs := make([]uint64, 0)
	for _, fileDoc := range fileDocs {
		if !newFilePaths[fileDoc.doc.Ref.Url] {
			deleteIDs = append(deleteIDs, fileDoc.doc.ID)
		}
	}

	if len(deleteIDs) > 0 {
		if err := DeleteDocInBulk(ctx, tx, deleteIDs, true, nil); err != nil {
			return fmt.Errorf("DeleteRagDataInBulk: %w", err)
		}
	}

	return nil
}

func (l *SyncSystemDocLogic) publishFileDocs(ctx context.Context,
	tx *gorm.DB, data *eventspb.SystemDocChanged_Doc, fileDocs map[string]*systemDoc,
) error {
	newFilePaths := make(map[string]bool)
	for _, file := range data.Files {
		newFilePaths[file.FilePath] = true
	}

	for _, fileDoc := range fileDocs {
		if newFilePaths[fileDoc.doc.Ref.Url] {
			fileDoc.doc.UgcTitle = data.UgcTitle
			fileDoc.doc.ContentState = aipb.DocContentState_DOC_CONTENT_STATE_SYNCING
			fileDoc.doc.UpdateBy = data.UpdateBy.IdentityId
			fileDoc.doc.UpdateByType = data.UpdateBy.IdentityType
			fileDoc.doc.UpdateDate = time.Now()
			changes := []string{"ugc_title", "content_state", "update_by", "update_by_type", "update_date"}
			if err := tx.Select(changes).Save(fileDoc.doc).Error; err != nil {
				return fmt.Errorf("update content_state: %w", err)
			}

			if err := OnOffDocInBulk(ctx, tx, aipb.DocState_DOC_STATE_ENABLED, []uint64{fileDoc.doc.ID}); err != nil {
				return fmt.Errorf("OnOffDocInBulk: %w", err)
			}
		}
	}

	return nil
}

func (l *SyncSystemDocLogic) deleteSystemDoc(ctx context.Context, tx *gorm.DB, doc *systemDoc) error {
	if doc.doc != nil {
		doc.doc.ContentState = aipb.DocContentState_DOC_CONTENT_STATE_DELETED
		changes := []string{"content_state"}
		if err := tx.Select(changes).Save(doc.doc).Error; err != nil {
			return fmt.Errorf("update content_state: %w", err)
		}
	}

	if enabledDoc := doc.pluckEnabledDoc(); enabledDoc != nil {
		// 没有副本直接删除数据，否则禁用
		if len(doc.copies) == 0 {
			if err := DeleteDocInBulk(ctx, tx, []uint64{enabledDoc.ID}, true, nil); err != nil {
				return fmt.Errorf("delete doc: %w", err)
			}
		} else {
			if err := OnOffDocInBulk(ctx, tx, aipb.DocState_DOC_STATE_DISABLED, []uint64{enabledDoc.ID}); err != nil {
				return fmt.Errorf("disable doc: %w", err)
			}
		}
	}

	return nil
}

func (l *SyncSystemDocLogic) unpublishSystemDoc(ctx context.Context,
	tx *gorm.DB, doc *systemDoc, data *eventspb.SystemDocChanged_Doc,
) error {
	if doc.doc != nil {
		doc.doc.ContentState = aipb.DocContentState_DOC_CONTENT_STATE_UNPUBLISHED
		doc.doc.UpdateDate = time.Now()
		doc.doc.UpdateBy = data.UpdateBy.IdentityId
		doc.doc.UpdateByType = data.UpdateBy.IdentityType
		changes := []string{"content_state", "update_date", "update_by", "update_by_type"}
		if err := tx.Select(changes).Save(doc.doc).Error; err != nil {
			return fmt.Errorf("update content_state: %w", err)
		}
	}

	if enabledDoc := doc.pluckEnabledDoc(); enabledDoc != nil {
		if err := OnOffDocInBulk(ctx, tx, aipb.DocState_DOC_STATE_DISABLED, []uint64{enabledDoc.ID}); err != nil {
			return fmt.Errorf("disable doc: %w", err)
		}
	}

	return nil
}

func (l *SyncSystemDocLogic) showContributor(ugcType basepb.DataType) uint32 {
	switch ugcType {
	case basepb.DataType_DATA_TYPE_TEAM,
		basepb.DataType_DATA_TYPE_PRODUCT,
		basepb.DataType_DATA_TYPE_GRAPH,
		basepb.DataType_DATA_TYPE_RESOURCE:
		return 1
	default:
		return 2
	}
}

func (l *SyncSystemDocLogic) parseFiles(ctx context.Context, fileDocs []*model.TDoc) {
	if len(fileDocs) == 0 {
		return
	}

	for _, file := range fileDocs {
		file := file
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			NewParseDocLogic().StartParse(ctx, file)

			if err := OnOffDocInBulk(ctx, xorm.DB(ctx),
				aipb.DocState_DOC_STATE_ENABLED, []uint64{file.ID}); err != nil {
				log.WithContext(ctx).Errorw("InsertRagDataInBulk: %w", err)
			}

			return nil
		}, boot.TraceGo(ctx))
	}
}
