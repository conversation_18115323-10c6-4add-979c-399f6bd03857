package logic

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"gorm.io/gorm"
)

// CloneDocInBulk 克隆doc,并走分享
func CloneDocInBulk(ctx context.Context, req *ai.ReqCloneDocInBulk, rsp *ai.RspCloneDocInBulk) error {
	return model.Transaction(ctx, func(tx *gorm.DB) error {
		var rows []*model.TDoc
		err := tx.Model(&model.TDoc{}).
			Preload("References", func(db *gorm.DB) *gorm.DB {
				return db.Select("id")
			}).
			Preload("States").Preload("Contributors").
			Preload("MatchPatterns").Preload("DocExtend").
			Preload("TableOversize").
			Where("id in ?", req.Id).Find(&rows).Error
		if err != nil {
			return err
		}
		if len(rows) == 0 {
			return nil
		}

		toCreate, toShare := CopyTDocForClone(req.Operator, rows, req.ScopedAssistantId...)
		err = tx.Omit("References.*").CreateInBatches(toCreate, 10).Error
		if err != nil {
			return err
		}
		if rsp != nil {
			for _, row := range toCreate {
				rsp.Id = append(rsp.Id, row.ID)
			}
		}
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			// 分享
			for idx, share := range toShare {
				if len(share) > 0 {
					err = SyncDocShareAssistant(ctx, req.Operator.Type, req.Operator.Id, toCreate[idx].ID, share)
					if err != nil {
						return err
					}
				}
			}
			return nil
		}, boot.TraceGo(ctx))
		return nil
	})
}

func CheckDocBeforeEnable(ctx context.Context, req *ai.ReqOnOffDocInBulk, rsp *ai.RspOnOffDocInBulk) error {
	var err error

	// 1. 判断是否doc的状态已经是req.state
	scopedQuery := "state <> ? and doc_id in ?"
	scopedArgs := []interface{}{req.State, req.Id}
	// 1. 先把通过id拿出数据类型，分为qa和其他
	var rows []*model.TDoc
	err = model.NewQuery[model.TDoc](ctx).DB().
		Select("ID", "DataType").
		Where("id in ?", req.Id).Find(&rows).Error
	var qas, files []*model.TDoc
	var qaIds, fileIds []uint64
	for _, row := range rows {
		if row.DataType == uint32(ai.DocType_DOCTYPE_QA) {
			qas = append(qas, row)
			qaIds = append(qaIds, row.ID)
		} else {
			files = append(files, row)
			fileIds = append(fileIds, row.ID)
		}
	}
	if len(qas) == 0 && len(files) == 0 {
		return nil
	}

	// 2. 获取doc信息,对于 States 关联读取，只取 scoped assistant 范围内
	db := model.NewQuery[model.TDoc](ctx).DB().
		Preload("Assistants", model.AssistantWithCollection).
		Preload("States", func(db *gorm.DB) *gorm.DB {
			return db.Where(scopedQuery, scopedArgs...)
		})
	var qasLLM, qasContains []*model.TDoc
	if len(qas) != 0 {
		err = db.Where("id in (?)", qaIds).Scopes(model.DocWithLLMRecallFilter).
			Select("ID", "IndexTextMd5", "IndexText").Find(&qasLLM).Error
		if err != nil {
			return err
		}
		err = db.Where("id in (?)", qaIds).Scopes(model.DocWithMatchPatternFilter(ai.DocMatchPattern_DOC_MATCH_PATTERN_CONTAINS)).
			Select("ID", "IndexTextMd5", "IndexText").Find(&qasContains).Error
		if err != nil {
			return err
		}
	}
	if len(files) != 0 {
		err = db.Where("id in (?)", fileIds).Select("ID", "IndexTextMd5", "FileName").Find(&files).Error
		if err != nil {
			return err
		}
	}
	if len(qasContains) != 0 {
		checkOn := &OnDocLogic{RowsToOn: qasContains, ScopedAssistantId: req.ScopedAssistantId, Ctx: ctx, DocType: ai.DocType_DOCTYPE_QA}
		exceed, err := checkOn.CheckQaContainsMatchPatternLimit()
		if err != nil {
			return err
		}
		if len(exceed) != 0 {
			rsp.ExceedQaContainsMatchLimit = exceed
			return nil
		}
	}
	if len(qasLLM) != 0 {
		checkOn := &OnDocLogic{RowsToOn: qasLLM, ScopedAssistantId: req.ScopedAssistantId, Ctx: ctx, DocType: ai.DocType_DOCTYPE_QA}
		// 请求参数查重
		preRepeat := checkOn.CheckPreRepeat()
		// 数据库查重
		repeated, err := checkOn.CheckRepeated()
		if err != nil {
			return err
		}
		if len(repeated) != 0 || len(preRepeat) != 0 {
			rsp.PreRepeatCollections = preRepeat
			rsp.RepeatCollections = repeated
			return nil
		}
	}
	if len(files) != 0 {
		checkOn := &OnDocLogic{RowsToOn: files, ScopedAssistantId: req.ScopedAssistantId, Ctx: ctx, DocType: ai.DocType_DOCTYPE_FILE}
		// 请求参数查重
		preRepeat := checkOn.CheckPreRepeat()
		// 数据库查重
		repeated, err := checkOn.CheckRepeated()
		if err != nil {
			return err
		}
		if len(repeated) != 0 || len(preRepeat) != 0 {
			rsp.PreRepeatCollections = preRepeat
			rsp.RepeatCollections = repeated
			return nil
		}
	}
	return nil
}

// UpdateDocAttrInBulk ...
func UpdateDocAttrInBulk(ctx context.Context, req *ai.ReqUpdateDocAttrInBulk) error {
	l := NewUpdateDocAttrLogic(ctx, req)
	l.PrepareData()

	return model.Transaction(ctx, func(tx *gorm.DB) error {
		return l.ExecuteInTransaction(tx)
	})
}

// ReparseDocInBulk ...
func ReparseDocInBulk(ctx context.Context, req *ai.ReqReparseTextFiles) error {
	if len(req.Ids) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	docs, err := model.NewQuery[model.TDoc](ctx).Where("id in ?", req.Ids).Get()
	if err != nil {
		return err
	}
	if len(docs) != len(req.Ids) {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	parseTime := time.Now()
	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		_, err := xorm.NewQueryWithDB[model.TDoc](tx).UpdateBy(map[string]any{
			model.TDocColumns.State: ai.DocState_DOC_STATE_REPARSING,
		}, "id in ?", req.Ids)
		if err != nil {
			return err
		}
		_, err = xorm.NewQueryWithDB[model.TDocExtend](tx).UpdateBy(map[string]any{
			model.TDocExtendColumns.ParseMode:   req.ParseMode,
			model.TDocExtendColumns.ParseUserID: req.Operator.Id,
			model.TDocExtendColumns.ParseByType: req.Operator.Type,
			model.TDocExtendColumns.ParseByUser: req.Operator.UserId,
			model.TDocExtendColumns.UpdateDate:  parseTime,
		}, "doc_id in ?", req.Ids)
		return err
	})
	if err != nil {
		return err
	}

	for _, v := range docs {
		v := v
		v.State = ai.DocState_DOC_STATE_REPARSING
		v.DocExtend = &model.TDocExtend{
			DocID:       v.ID,
			ParseMode:   req.ParseMode,
			ParseUserID: req.Operator.Id,
			ParseByType: req.Operator.Type,
			ParseByUser: req.Operator.UserId,
			UpdateDate:  parseTime,
		}
		if v.DataType == uint32(ai.DocType_DOCTYPE_FILE) {
			xsync.SafeGo(context.Background(), func(ctx context.Context) error {
				NewReparseDocLogic().StartParse(ctx, v)
				return nil
			}, boot.TraceGo(ctx))
		}
	}
	return nil
}
