package table

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
)

type DocTableLogic struct {
	db *gorm.DB
}

func NewDocTableLogic(db *gorm.DB) *DocTableLogic {
	return &DocTableLogic{
		db: db,
	}
}

// StoreOversizedTablesAllAssistant 对于所有助手，处理文档的表格逻辑，存入数据库
func (l *DocTableLogic) StoreOversizedTablesAllAssistant(ctx context.Context, docID uint64) error {
	// 从数据库读取文档信息
	var doc model.TDoc
	fields := []string{"id", "index_text"}
	if err := l.db.WithContext(ctx).Select(fields).First(&doc, docID).Error; err != nil {
		return errors.Wrapf(err, "failed to get doc %d", docID)
	}

	// 获取文档绑定的所有助手
	var assistantDocs []model.TAssistantDoc
	if err := l.db.WithContext(ctx).Where("doc_id = ?", docID).Find(&assistantDocs).Error; err != nil {
		return errors.Wrap(err, "failed to get doc assistants")
	}

	assistantIds := make([]uint64, 0, len(assistantDocs))
	for _, assistantDoc := range assistantDocs {
		assistantIds = append(assistantIds, assistantDoc.AssistantID)
	}
	if err := l.ClearDocTableInfo(ctx, docID, assistantIds); err != nil {
		return errors.Wrap(err, "failed to clear doc table info")
	}

	// 如果没有绑定助手，则跳过处理
	if len(assistantDocs) == 0 {
		return nil
	}

	// 先提取一次表格，避免重复提取
	tables, _ := ExtractMarkdownTables(doc.IndexText)
	if len(tables) == 0 {
		return nil
	}

	// 对每个绑定的助手处理表格
	for _, assistantDoc := range assistantDocs {
		// 复用 ProcessDocTables 方法，但传入已提取的表格
		if err := l.processDocTablesWithExtractedTables(ctx, docID, assistantDoc.AssistantID, tables); err != nil {
			return errors.Wrapf(err, "failed to process doc tables for assistant %d", assistantDoc.AssistantID)
		}
	}

	return nil
}

// HasOverSizedTables 检查文档是否包含超长表头的表格
func (l *DocTableLogic) HasOverSizedTables(ctx context.Context, docID, assistantID uint64) (bool, error) {
	var count int64
	err := l.db.WithContext(ctx).Model(&model.TDocTableOversize{}).
		Where("doc_id = ? AND assistant_id = ? AND is_header_over_sized = ?", docID, assistantID, true).
		Count(&count).Error
	if err != nil {
		return false, errors.Wrap(err, "failed to get doc tables")
	}

	return count > 0, nil
}

// HasOverSizedTablesBatch 批量检查多个文档是否包含超长表头的表格
func (l *DocTableLogic) HasOverSizedTablesBatch(ctx context.Context, docIDs []uint64, assistantID []uint64) (map[uint64]bool, error) {
	if len(docIDs) == 0 {
		return make(map[uint64]bool), nil
	}

	var tables []model.TDocTableOversize
	err := l.db.WithContext(ctx).Model(&model.TDocTableOversize{}).
		Scopes(func(db *gorm.DB) *gorm.DB {
			if len(assistantID) != 0 {
				db.Where("assistant_id IN (?)", assistantID)
			}
			return db
		}).
		Where("doc_id IN ?  AND is_header_over_sized = ?", docIDs, true).
		Select("DISTINCT doc_id").
		Find(&tables).Error
	if err != nil {
		return nil, errors.Wrap(err, "failed to get doc tables")
	}

	// 构建结果 map
	result := make(map[uint64]bool, len(docIDs))
	// 默认所有文档都没有超长表头
	for _, docID := range docIDs {
		result[docID] = false
	}
	// 更新有超长表头的文档
	for _, table := range tables {
		result[table.DocID] = true
	}

	return result, nil
}

// checkHeaderLength 检查表头是否超过长度限制
// 返回值:
// - isOverSized: 是否超过长度限制
// - tokenCount: 表头字符串的token数量
// - maxTokens: 嵌入模型的最大token数量
func (l *DocTableLogic) checkHeaderLength(ctx context.Context, embedModel string, header string) (bool, int, int) {
	if embedModel == "" {
		return false, 0, 0
	}

	isExceed, tokenCount, maxTokens, _ := model.IsExceedEmbeddingTokenLimit(embedModel, header)
	return isExceed, tokenCount, maxTokens
}

// processDocTablesWithExtractedTables 使用已提取的表格处理文档表格
func (l *DocTableLogic) processDocTablesWithExtractedTables(ctx context.Context, docID, assistantID uint64, tables []Table) error {
	// 提前获取嵌入模型，避免在循环中重复查询
	embedModel, err := model.GetAssistantEmbeddingModel(ctx, assistantID)
	if err != nil {
		return errors.Wrap(err, "获取嵌入模型失败")
	}

	// 准备批量 upsert 的数据
	tableInfos := make([]*model.TDocTableOversize, 0, len(tables))
	for i, table := range tables {
		isExceed, tokenCount, maxTokens := l.checkHeaderLength(ctx, embedModel, table.Header)
		if isExceed {
			tableInfos = append(tableInfos, &model.TDocTableOversize{
				DocID:             docID,
				AssistantID:       assistantID,
				TablePosition:     i + 1,
				TableTitle:        table.Title,
				TableHeader:       table.Header,
				IsHeaderOverSized: isExceed,
				HeaderLength:      tokenCount,
				HeaderMaxLength:   maxTokens,
			})
		}
	}

	// 执行批量 upsert
	if len(tableInfos) > 0 {
		if err := l.db.WithContext(ctx).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "doc_id"}, {Name: "assistant_id"}, {Name: "table_position"}},
			UpdateAll: true,
		}).Create(&tableInfos).Error; err != nil {
			return errors.Wrap(err, "failed to upsert table info")
		}
	}

	// 清理已不存在的表格记录
	if err := l.db.WithContext(ctx).Where("doc_id = ? AND assistant_id = ? AND table_position > ?",
		docID, assistantID, len(tables)).Delete(&model.TDocTableOversize{}).Error; err != nil {
		return errors.Wrap(err, "failed to clean up old tables")
	}

	return nil
}

// ClearDocTableInfo 清楚 doc 的表格信息
// notin 为没有绑定的助手
func (l *DocTableLogic) ClearDocTableInfo(ctx context.Context, docID uint64, notIn []uint64) error {
	db := model.NewQuery[model.TDocTableOversize](ctx).Where("doc_id = ?", docID)
	if len(notIn) != 0 {
		db.Where("assistant_id not in ?", notIn)
	}
	return db.Delete(&model.TDocTableOversize{})
}

type DocTableCheckLogic struct{}

// GetOverSizedTablesByDocID 通过文档ID查询超长表格的信息
func (l *DocTableCheckLogic) GetOverSizedTablesByDocID(ctx context.Context, docID uint64, scopedAssistant ...uint64) ([]*model.TDocTableOversize, error) {
	var tables []*model.TDocTableOversize

	// 查询指定文档ID的所有超长表格
	db := model.NewQuery[model.TDocTableOversize](ctx).DB()
	if len(scopedAssistant) != 0 {
		db = db.Where("assistant_id in ?", scopedAssistant)
	}
	err := db.
		Where("doc_id = ? AND is_header_over_sized = ?", docID, true).
		Find(&tables).Error
	if err != nil {
		return nil, fmt.Errorf("查询超长表格失败: %w", err)
	}

	return tables, nil
}
