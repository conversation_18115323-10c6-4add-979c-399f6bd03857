package table

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
)

// TableRange 存储表格的行号范围和字符位置
type TableRange struct {
	Start          int // 表格内容开始行
	End            int // 表格内容结束行
	TitledStart    int // 包含标题的起始行，如果没有标题则等于Start
	StartPos       int // 表格内容在原文中的开始字符位置
	EndPos         int // 表格内容在原文中的结束字符位置
	TitledStartPos int // 包含标题的起始字符位置，如果没有标题则等于StartPos
}

// Table 存储表格的完整信息
type Table struct {
	Title         string     // 表格标题
	Content       string     // 表格原始内容（不包含标题）
	TitledContent string     // 包含标题的表格全内容
	Header        string     // 表格表头信息
	Range         TableRange // 表格的行号范围
	TitleLineNum  int        // 标题行号，-1表示无标题
}

// PotentialTitle 存储可能的标题信息
type PotentialTitle struct {
	LineNum   int
	TitleText string
	TitleType string
}

// TempTableInfo 存储临时表格信息
type TempTableInfo struct {
	Content      string
	Range        TableRange
	Header       string
	IsHTML       bool
	CaptionTitle string
}

// TableOption 表格处理选项
type TableOption struct {
	IsExcel bool // 是否为Excel文件
}

// DefaultTableOption 返回默认的表格选项
func DefaultTableOption() TableOption {
	return TableOption{
		IsExcel: false,
	}
}

// 是否打印表格提取调试日志
func shouldPrintTableExtractDebugLog() bool {
	return config.GetBoolOr("llm.collection.table_extract.debug_log", false)
}

// shouldPrintTableExtractDebugLog 通过配置 llm.collection.table_extract.debug_log 开启表格提取调试日志
// 配置项默认关闭(false)，开启后可在日志中查看表格提取的详细过程，帮助排查问题

// extractHTMLTables 从内容中提取所有 HTML 表格
func extractHTMLTables(content string) []TempTableInfo {
	tempTables := []TempTableInfo{}
	htmlPattern := regexp.MustCompile(`(?s)<table[^>]*>.*?</table>`)
	htmlMatches := htmlPattern.FindAllStringSubmatchIndex(content, -1)
	captionPattern := regexp.MustCompile(`(?s)<caption[^>]*>(.*?)</caption>`)

	// 用于记录已提取的HTML表格的字符位置范围
	var htmlTablePositions []struct {
		Start int
		End   int
	}

	// 提取所有HTML表格
	for _, match := range htmlMatches {
		startPos := match[0]
		endPos := match[1]
		tableContent := content[startPos:endPos]

		// 计算行号
		tableStartLine := strings.Count(content[:startPos], "\n")
		endLine := tableStartLine + strings.Count(tableContent, "\n")
		currentTableRange := TableRange{
			Start:          tableStartLine,
			End:            endLine,
			StartPos:       startPos,
			EndPos:         endPos,
			TitledStartPos: startPos, // 默认情况下，标题起始位置等于表格起始位置
		}

		// 检查是否与已提取的HTML表格在字符位置上重叠
		overlaps := false
		for _, tablePos := range htmlTablePositions {
			if (startPos >= tablePos.Start && startPos < tablePos.End) ||
				(endPos > tablePos.Start && endPos <= tablePos.End) ||
				(startPos <= tablePos.Start && endPos >= tablePos.End) {
				overlaps = true
				break
			}
		}

		if !overlaps {
			// 记录这个HTML表格的字符位置范围
			htmlTablePositions = append(htmlTablePositions, struct {
				Start int
				End   int
			}{Start: startPos, End: endPos})

			// 提取表头
			header := extractHTMLTableHeader(tableContent)

			// 尝试从 caption 标签中提取标题
			captionTitle := ""
			if captionMatch := captionPattern.FindStringSubmatch(tableContent); len(captionMatch) > 1 {
				captionTitle = strings.TrimSpace(captionMatch[1])
			}

			// 添加到临时表格列表
			tempTables = append(tempTables, TempTableInfo{
				Content:      tableContent,
				Range:        currentTableRange,
				Header:       header,
				IsHTML:       true,
				CaptionTitle: captionTitle,
			})
		}
	}

	return tempTables
}

// extractMarkdownTables 从内容中提取所有 Markdown 表格
func extractMarkdownTables(content string) []TempTableInfo {
	tempTables := []TempTableInfo{}

	// 两种表格模式
	patterns := []struct {
		name    string
		pattern *regexp.Regexp
	}{
		{
			name:    "standardTable",
			pattern: regexp.MustCompile(`(?m)^ {0,3}\|(?P<table_head>.+)\|[ \t]*\n {0,3}\|(?P<table_align> *[-:]+[-| :]*)\|[ \t]*\n(?P<table_body>(?: {0,3}\|.*\|[ \t]*(?:\n|$))*)`)},
		{
			name:    "noPipeTable",
			pattern: regexp.MustCompile(`(?m)^ {0,3}(?P<nptable_head>\S.*\|.*)\n {0,3}(?P<nptable_align>[-:]+ *\|[-| :]*)\n(?P<nptable_body>(?:.*\|.*(?:\n|$))*)`)},
	}

	// 处理每种模式的表格
	for _, p := range patterns {
		matches := p.pattern.FindAllStringSubmatchIndex(content, -1)
		for _, match := range matches {
			startPos := match[0]
			endPos := match[1]
			tableContent := content[startPos:endPos]

			// 计算行号
			startLine := strings.Count(content[:startPos], "\n")
			endLine := startLine + strings.Count(tableContent, "\n")
			// table没有匹配到文本结束$, endLine需要减 1
			if tableContent[len(tableContent)-1] == '\n' && endPos != len(tableContent) {
				endLine--
			}
			// 提取表头
			var startHeadPos, endHeadPos int
			// 标准表格的表头位置
			startHeadPos = match[2]
			endHeadPos = match[3]
			header := content[startHeadPos:endHeadPos]

			// 为标准表格添加管道符
			if p.name == "standardTable" && !strings.HasPrefix(header, "|") {
				header = "|" + header + "|"
			}

			// 检查表头是否包含有效信息
			// 如果表头只包含空白字符、破折号、冒号或管道符，则认为表头无效
			headerContent := strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(header, "-", ""), ":", ""), "|", ""))
			if headerContent == "" {
				// 尝试从表格主体中提取第一行作为表头
				bodyStart := match[6]
				bodyEnd := match[7]
				bodyContent := content[bodyStart:bodyEnd]

				// 提取第一行数据
				lines := strings.Split(bodyContent, "\n")
				if len(lines) > 0 {
					// 使用第一行数据作为表头
					header = lines[0]
				}
			}

			// 检查是否与已存在的表格重叠
			overlap := false
			for _, table := range tempTables {
				if (startLine >= table.Range.Start && startLine <= table.Range.End) ||
					(endLine >= table.Range.Start && endLine <= table.Range.End) ||
					(startLine <= table.Range.Start && endLine >= table.Range.End) {
					overlap = true
					break
				}
			}

			if !overlap {
				// 添加到临时表格列表
				tempTables = append(tempTables, TempTableInfo{
					Content: tableContent,
					Range: TableRange{
						Start:          startLine,
						End:            endLine,
						StartPos:       startPos,
						EndPos:         endPos,
						TitledStartPos: startPos, // 默认情况下，标题起始位置等于表格起始位置
					},
					Header: header,
					IsHTML: false,
				})
			}
		}
	}

	return tempTables
}

// findPotentialTitles 从内容中提取所有可能的标题
func findPotentialTitles(contentLines []string) []PotentialTitle {
	potentialTitles := []PotentialTitle{}
	inCodeBlock := false

	// 正则表达式模式
	headingPattern := regexp.MustCompile(`^#{1,6}\s+.+`)
	headingLevelPattern := regexp.MustCompile(`^(#+)`)
	equalsPattern := regexp.MustCompile(`^[=]+$`)
	minusPattern := regexp.MustCompile(`^[-]+$`)
	tableKeywordsPattern := regexp.MustCompile(`表格|表|列表|数据|明细|[:：]$`)
	separatorPattern := regexp.MustCompile(`^[-:|\s]+$`)
	htmlTagPattern := regexp.MustCompile(`<[^>]*>`)

	for i, line := range contentLines {
		trimmedLine := strings.TrimSpace(line)

		// 跳过代码块内容
		if strings.HasPrefix(trimmedLine, "```") {
			inCodeBlock = !inCodeBlock
			continue
		}

		if inCodeBlock {
			continue
		}

		// 识别Markdown标题格式（# 标题）
		if headingPattern.MatchString(trimmedLine) {
			level := len(headingLevelPattern.FindStringSubmatch(trimmedLine)[1])
			titleText := strings.TrimSpace(trimmedLine)

			// 添加普通标题
			potentialTitles = append(potentialTitles, PotentialTitle{
				LineNum:   i,
				TitleText: titleText,
				TitleType: fmt.Sprintf("heading%d", level),
			})
		} else if i > 0 && equalsPattern.MatchString(trimmedLine) && strings.TrimSpace(contentLines[i-1]) != "" {
			// 识别另一种Markdown标题格式（标题\n=====）
			titleText := strings.TrimSpace(contentLines[i-1])
			potentialTitles = append(potentialTitles, PotentialTitle{
				LineNum:   i - 1,
				TitleText: titleText,
				TitleType: "heading1",
			})
		} else if i > 0 && minusPattern.MatchString(trimmedLine) && strings.TrimSpace(contentLines[i-1]) != "" {
			// 识别另一种Markdown标题格式（标题\n-----）
			titleText := strings.TrimSpace(contentLines[i-1])
			potentialTitles = append(potentialTitles, PotentialTitle{
				LineNum:   i - 1,
				TitleText: titleText,
				TitleType: "heading2",
			})
		} else if tableKeywordsPattern.MatchString(trimmedLine) && trimmedLine != "" {
			// 识别包含"表格"、"表"等关键词或以冒号结尾的行
			potentialTitles = append(potentialTitles, PotentialTitle{
				LineNum:   i,
				TitleText: trimmedLine,
				TitleType: "paragraph",
			})
		} else if trimmedLine != "" && !strings.HasPrefix(trimmedLine, "|") && !separatorPattern.MatchString(trimmedLine) {
			// 保存非空的普通文本行，但不保存表格分隔符
			// 过滤掉HTML标签
			cleanLine := strings.TrimSpace(htmlTagPattern.ReplaceAllString(trimmedLine, ""))
			// 文本行长度小于80，才能是表格的标题
			if cleanLine != "" && len(cleanLine) <= 80 {
				potentialTitles = append(potentialTitles, PotentialTitle{
					LineNum:   i,
					TitleText: cleanLine,
					TitleType: "text",
				})
			}
		}
	}

	return potentialTitles
}

// findNearestTitle 为表格寻找最近的标题
func findNearestTitle(tableStartLine int, currentTableRange TableRange, potentialTitles []PotentialTitle, tables []Table, usedTitleLineNums map[int]bool, option TableOption) (string, int) {
	bestTitle := ""
	bestLineNum := -1

	// 计算搜索标题的下限边界行号
	boundaryLine := 0
	hasPreviousTables := false

	// 检查前面是否有表格，并获取前面最近表格的结束行作为边界
	for _, table := range tables {
		// 如果该表格在当前表格之前，更新边界行号
		if table.Range.End < tableStartLine && table.Range.End > boundaryLine {
			boundaryLine = table.Range.End
			hasPreviousTables = true
		}
	}

	// 从表格起点往前遍历，找到最近的标题
	for i := len(potentialTitles) - 1; i >= 0; i-- {
		title := potentialTitles[i]
		// 忽略位于当前表格之后的标题
		if title.LineNum >= tableStartLine {
			continue
		}

		// 如果有前面的表格，则忽略位于前面表格之前的标题
		if hasPreviousTables && title.LineNum <= boundaryLine {
			continue
		}

		// 跳过已被使用的标题
		if usedTitleLineNums[title.LineNum] {
			continue
		}

		// 检查这个标题行是否属于之前的表格
		belongsToOtherTable := false
		for _, table := range tables {
			// 跳过当前正在处理的表格
			if table.Range.Start == currentTableRange.Start && table.Range.End == currentTableRange.End {
				continue
			}

			// 如果标题行落在其他表格的范围内，跳过
			if title.LineNum >= table.Range.Start && title.LineNum <= table.Range.End {
				belongsToOtherTable = true
				break
			}
		}

		if belongsToOtherTable {
			continue
		}

		// 如果是Excel文件，跳过"# Sheet1", "# Sheet2"等默认Sheet名称
		if option.IsExcel {
			sheetPattern := regexp.MustCompile(`^# Sheet\d+$`)
			if sheetPattern.MatchString(title.TitleText) {
				continue
			}
		}

		// 如果是标题类型（heading）或者是紧邻表格的描述性文本
		if strings.HasPrefix(title.TitleType, "heading") ||
			((title.TitleType == "text" || title.TitleType == "paragraph") && tableStartLine-title.LineNum <= 2) {
			bestTitle = title.TitleText
			bestLineNum = title.LineNum
			break
		}
	}

	// 如果找到了标题，将其标记为已使用
	if bestLineNum >= 0 {
		usedTitleLineNums[bestLineNum] = true
	}

	return bestTitle, bestLineNum
}

// cleanDocument 清理文档内容，移除表格和标题
func cleanDocument(content string, tables []Table) string {
	// 如果没有表格，直接返回原内容
	if len(tables) == 0 {
		return content
	}

	// 构建不包含表格的新内容
	var result strings.Builder
	lastEndPos := 0

	// 辅助函数：计算文本开头的换行符数量
	countLeadingNewlines := func(text string) int {
		count := 0
		for i := 0; i < len(text) && text[i] == '\n'; i++ {
			count++
		}
		return count
	}

	// 辅助函数：计算文本结尾的换行符数量
	countTrailingNewlines := func(text string) int {
		count := 0
		for i := len(text) - 1; i >= 0 && text[i] == '\n'; i-- {
			count++
		}
		return count
	}

	// 辅助函数：添加文本段，确保段落之间有适当的换行
	appendTextSegment := func(text string) {
		if len(strings.TrimSpace(text)) == 0 || len(strings.Trim(strings.TrimSpace(text), "\n")) == 0 {
			return
		}
		if result.Len() == 0 {
			// 如果结果为空，直接添加
			result.WriteString(text)
			return
		}

		// 确保段落之间有适当的换行
		resultStr := result.String()
		trailingNewlines := countTrailingNewlines(resultStr)
		leadingNewlines := countLeadingNewlines(text)

		// 确保总共有两个换行符
		totalNewlines := trailingNewlines + leadingNewlines
		if totalNewlines < 2 {
			// 添加需要的换行符
			neededNewlines := 2 - totalNewlines
			result.WriteString(strings.Repeat("\n", neededNewlines))
		}

		// 添加文本
		result.WriteString(text)
	}

	// 处理每个表格
	for _, table := range tables {
		startPos := table.Range.TitledStartPos
		endPos := table.Range.EndPos

		// 验证位置是否有效
		if startPos < endPos && startPos < len(content) && endPos <= len(content) {
			// 添加表格前的文本段
			if startPos > lastEndPos {
				appendTextSegment(content[lastEndPos:startPos])
			}
			// 更新最后处理的位置
			lastEndPos = endPos
		}
	}

	// 添加最后一个表格之后的内容
	if lastEndPos < len(content) {
		appendTextSegment(content[lastEndPos:])
	}

	resultContent := result.String()

	// 处理空内容的情况
	if len(resultContent) == 0 {
		return ""
	}

	// 规范化连续的多个空行，确保最多只有两个连续的换行符
	multipleNewlinesPattern := regexp.MustCompile(`\n{3,}`)
	resultContent = multipleNewlinesPattern.ReplaceAllString(resultContent, "\n\n")

	return resultContent
}

// ExtractMarkdownTables 从Markdown文本中提取表格
// 返回：表格对象切片, 去除表格后的文档内容
func ExtractMarkdownTables(content string) ([]Table, string) {
	return ExtractMarkdownTablesWithOption(context.Background(), content, DefaultTableOption())
}

// ExtractMarkdownTablesWithOption 从Markdown文本中提取表格，支持自定义选项
// 返回：表格对象切片, 去除表格后的文档内容
func ExtractMarkdownTablesWithOption(ctx context.Context, content string, option TableOption) ([]Table, string) {
	// 记录开始时间，用于记录性能日志
	startTime := time.Now()
	enableDebugLog := shouldPrintTableExtractDebugLog()

	// 将内容分割成行
	contentLines := strings.Split(content, "\n")

	// 提取所有可能的标题
	potentialTitles := findPotentialTitles(contentLines)

	// 用于记录已使用的标题行号
	usedTitleLineNums := map[int]bool{}

	// 存储所有表格
	tables := []Table{}

	// 提取HTML表格和Markdown表格
	tempTables := []TempTableInfo{}
	tempTables = append(tempTables, extractHTMLTables(content)...)
	tempTables = append(tempTables, extractMarkdownTables(content)...)

	// 按位置排序
	sort.Slice(tempTables, func(i, j int) bool {
		return tempTables[i].Range.StartPos < tempTables[j].Range.StartPos
	})

	// 为每个表格分配标题
	for _, tempTable := range tempTables {
		title := ""
		titleLineNum := -1
		titledStartPos := tempTable.Range.StartPos

		// 如果是HTML表格，优先使用caption
		if tempTable.IsHTML && tempTable.CaptionTitle != "" {
			title = tempTable.CaptionTitle
		} else {
			// 使用findNearestTitle函数寻找最合适的标题
			title, titleLineNum = findNearestTitle(tempTable.Range.Start, tempTable.Range, potentialTitles, tables, usedTitleLineNums, option)

			// 如果找到了标题，计算标题的字符位置
			if titleLineNum >= 0 {
				// 找到标题行在原始文本中的位置
				lineStartIndex := 0
				currentLine := 0
				for currentLine < titleLineNum {
					nextNL := strings.Index(content[lineStartIndex:], "\n")
					if nextNL == -1 {
						break
					}
					lineStartIndex += nextNL + 1
					currentLine++
				}
				titledStartPos = lineStartIndex
			}
		}

		// 设置包含标题的起始行
		titledStart := tempTable.Range.Start
		if title != "" {
			titledStart = titleLineNum
		}
		tempTable.Range.TitledStart = titledStart
		tempTable.Range.TitledStartPos = titledStartPos

		// 生成带标题的内容
		var titledContent string
		if title != "" && titleLineNum >= 0 {
			// 如果是Markdown表格且有标题，TitledContent应包含从标题行到表格结束的所有内容
			titledContent = content[tempTable.Range.TitledStartPos:tempTable.Range.EndPos]
		} else {
			// 如果没有标题，TitledContent就是原始内容
			titledContent = tempTable.Content
		}

		// 创建表格对象
		table := Table{
			Title:         title,
			Content:       tempTable.Content,
			TitledContent: titledContent,
			Header:        tempTable.Header,
			Range:         tempTable.Range,
			TitleLineNum:  titleLineNum,
		}

		tables = append(tables, table)
	}
	sortTablesByPosition(tables)
	// 清理文档内容
	cleanedContent := cleanDocument(content, tables)

	// 合并具有相同表头的表格
	tables = mergeTablesWithSameHeader(tables)

	// 如果开启了调试日志，打印最终提取的表格和清理后的内容
	if enableDebugLog {
		elapsed := time.Since(startTime)
		log.WithContext(ctx).Infow("表格提取完成",
			"处理耗时(ms)", elapsed.Milliseconds(),
			"最终表格数量", len(tables))

		// 记录每个表格的基本信息
		for i, table := range tables {
			log.WithContext(ctx).Infow("提取的表格",
				"表格索引", i,
				"标题", table.Title,
				"表格内容", table.Content,
				"表头", table.Header)
		}

		// 找到表格后才打印
		if len(tables) != 0 {
			// 记录清理后的内容（如果过长则截断）
			const maxContentLogLength = 5000
			contentToLog := cleanedContent
			if len(contentToLog) > maxContentLogLength {
				contentToLog = contentToLog[:maxContentLogLength] + "...(已截断)"
			}
			log.WithContext(ctx).Infow("清理后的内容", "内容", contentToLog)
		}
	}

	return tables, cleanedContent
}

// sortTablesByPosition 根据表格在原文中的位置进行排序
func sortTablesByPosition(tables []Table) {
	sort.Slice(tables, func(i, j int) bool {
		return tables[i].Range.StartPos < tables[j].Range.StartPos
	})
}

// mergeTablesWithSameHeader 合并具有相同表头的表格
func mergeTablesWithSameHeader(tables []Table) []Table {
	if len(tables) <= 1 {
		return tables
	}

	mergedTables := []Table{}
	processed := make(map[int]bool)

	// 按顺序遍历表格，合并相邻的具有相同表头的表格
	i := 0
	for i < len(tables) {
		if processed[i] {
			i++
			continue
		}

		// 找到当前表格的表头
		currentHeader := ""
		if i < len(tables) {
			currentHeader = tables[i].Header
		}

		// 如果当前表格没有表头，不能进行合并
		if currentHeader == "" {
			mergedTables = append(mergedTables, tables[i])
			processed[i] = true
			i++
			continue
		}

		// 收集具有相同表头的连续表格
		consecutiveIndices := []int{i}
		j := i + 1
		for j < len(tables) {
			if j < len(tables) && tables[j].Header == currentHeader {
				// 检查下一个表格是否有明确的标题
				nextTitle := tables[j].Title

				// 如果下一个表格有明确的标题，停止合并
				if nextTitle != "" {
					break
				}

				consecutiveIndices = append(consecutiveIndices, j)
				j++
			} else {
				// 遇到不同表头的表格，停止寻找
				break
			}
		}

		// 如果只找到一个表格，直接添加
		if len(consecutiveIndices) == 1 {
			mergedTables = append(mergedTables, tables[i])
			processed[i] = true
			i++
			continue
		}

		// 对于相同表头的连续表格，进行合并
		isHTML := strings.Contains(tables[i].Content, "<table")

		var mergedTable Table

		if isHTML {
			mergedTable = mergeHTMLTables(tables, consecutiveIndices)
		} else {
			mergedTable = mergeMarkdownTables(tables, consecutiveIndices)
		}

		mergedTables = append(mergedTables, mergedTable)

		// 标记所有已处理的表格
		for _, idx := range consecutiveIndices {
			processed[idx] = true
		}

		// 更新索引，跳过已处理的表格
		i = j
	}

	return mergedTables
}

// mergeHTMLTables 合并具有相同表头的HTML表格
func mergeHTMLTables(tables []Table, indices []int) Table {
	if len(indices) == 0 {
		return Table{}
	}

	firstIdx := indices[0]
	firstTable := tables[firstIdx]
	bestTitle := firstTable.Title

	// 找到第一个表格最合适的标题
	for _, idx := range indices {
		if tables[idx].Title != "" {
			bestTitle = tables[idx].Title
			break
		}
	}

	// 如果只有一个表格，直接返回
	if len(indices) == 1 {
		return firstTable
	}

	// 解析第一个表格，准备合并
	// 提取表格主体：找到最后一个</tr>前的位置
	bodyEndPattern := regexp.MustCompile(`(?s).*</tr>`)
	bodyMatch := bodyEndPattern.FindStringSubmatch(firstTable.Content)
	if len(bodyMatch) == 0 {
		return firstTable // 无法找到表格主体，返回原表格
	}

	tableBody := bodyMatch[0]
	tableEnd := firstTable.Content[len(tableBody):] // 可能包含</tbody></table>等

	// 提取其他表格的数据行
	trPattern := regexp.MustCompile(`(?s)<tr[^>]*>(.*?)</tr>`)

	// 从第二个表格开始，提取数据行并添加到第一个表格
	for _, idx := range indices[1:] {
		// 跳过表头行，只提取数据行
		otherTable := tables[idx]
		trMatches := trPattern.FindAllStringSubmatch(otherTable.Content, -1)

		// 跳过第一行（通常是表头）
		if len(trMatches) > 1 {
			for _, match := range trMatches[1:] {
				if len(match) > 0 {
					// 将数据行添加到第一个表格的表体末尾
					tableBody += "<tr" + match[0][3:] // 保留tr标签及其内容
				}
			}
		}
	}

	// 组合成完整的合并表格
	mergedTableContent := tableBody + tableEnd

	return Table{
		Title:         bestTitle,
		Content:       mergedTableContent,
		TitledContent: mergedTableContent,
		Header:        firstTable.Header,
		Range: TableRange{
			Start:          firstTable.Range.Start,
			End:            firstTable.Range.End,
			TitledStart:    firstTable.Range.TitledStart,
			StartPos:       firstTable.Range.StartPos,
			EndPos:         firstTable.Range.EndPos,
			TitledStartPos: firstTable.Range.TitledStartPos,
		},
		TitleLineNum: firstTable.TitleLineNum,
	}
}

// mergeMarkdownTables 合并具有相同表头的Markdown表格
func mergeMarkdownTables(tables []Table, indices []int) Table {
	if len(indices) == 0 {
		return Table{}
	}

	firstIdx := indices[0]
	firstTable := tables[firstIdx]
	bestTitle := firstTable.Title

	// 找到第一个表格最合适的标题
	for _, idx := range indices {
		if tables[idx].Title != "" {
			bestTitle = tables[idx].Title
			break
		}
	}

	// 如果只有一个表格，直接返回
	if len(indices) == 1 {
		return firstTable
	}

	// 解析第一个表格，准备合并
	lines := strings.Split(firstTable.Content, "\n")
	// 去除最后一个换行
	lines = lines[:len(lines)-1]
	if len(lines) < 3 {
		return firstTable // 表格行数不足，无法合并
	}

	// Markdown表格通常格式为：
	// | 表头1 | 表头2 |
	// | ----- | ----- |
	// | 数据1 | 数据2 |
	// ...

	// 保留表头和分隔行
	headerRow := lines[0]
	separatorRow := lines[1]
	result := []string{headerRow, separatorRow}

	// 添加第一个表格的数据行
	result = append(result, lines[2:]...)

	// 从第二个表格开始，只添加数据行（跳过表头和分隔行）
	for _, idx := range indices[1:] {
		otherTable := tables[idx]
		otherLines := strings.Split(otherTable.Content, "\n")

		// 确保表格格式有效
		if len(otherLines) > 2 {
			// 添加数据行（跳过表头和分隔行）
			result = append(result, otherLines[2:]...)
		}
	}

	mergedTableContent := strings.Join(result, "\n")
	// 创建带标题的内容
	var titledContent string
	if bestTitle != "" {
		titledContent = bestTitle + "\n\n" + mergedTableContent
	} else {
		titledContent = mergedTableContent
	}

	return Table{
		Title:         bestTitle,
		Content:       mergedTableContent,
		TitledContent: titledContent,
		Header:        firstTable.Header,
		Range: TableRange{
			Start:          firstTable.Range.Start,
			End:            firstTable.Range.End,
			TitledStart:    firstTable.Range.TitledStart,
			StartPos:       firstTable.Range.StartPos,
			EndPos:         firstTable.Range.EndPos,
			TitledStartPos: firstTable.Range.TitledStartPos,
		},
		TitleLineNum: firstTable.TitleLineNum,
	}
}

// extractHTMLTableHeader 提取HTML表格的表头
func extractHTMLTableHeader(table string) string {
	// 匹配表头行中的所有 th 标签
	thPattern := regexp.MustCompile(`(?s)<th[^>]*>(.*?)</th>`)
	matches := thPattern.FindAllStringSubmatch(table, -1)
	if len(matches) > 0 {
		headers := make([]string, 0, len(matches))
		for _, match := range matches {
			if len(match) > 1 {
				headers = append(headers, strings.TrimSpace(match[1]))
			}
		}
		header := "|" + strings.Join(headers, " | ") + "|"

		// 检查表头是否包含有效信息
		// 如果表头只包含空白字符、破折号、冒号或管道符，则认为表头无效
		headerContent := strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(header, "-", ""), ":", ""), "|", ""))
		if headerContent == "" {
			// 如果表头无效，尝试使用第一行数据作为表头
			trPattern := regexp.MustCompile(`(?s)<tr[^>]*>(.*?)</tr>`)
			trMatches := trPattern.FindAllStringSubmatch(table, -1)

			if len(trMatches) >= 2 {
				// 提取第一行数据作为表头
				tdPattern := regexp.MustCompile(`(?s)<td[^>]*>(.*?)</td>`)
				firstRowCells := tdPattern.FindAllStringSubmatch(trMatches[1][1], -1)

				if len(firstRowCells) > 0 {
					headers := make([]string, 0, len(firstRowCells))
					for _, cell := range firstRowCells {
						if len(cell) > 1 {
							headers = append(headers, strings.TrimSpace(cell[1]))
						}
					}
					return "|" + strings.Join(headers, " | ") + "|"
				}
			}
		}

		return header
	}

	// 如果没有找到th标签，尝试使用表格的前两行作为表头
	// 首先提取所有表格行
	trPattern := regexp.MustCompile(`(?s)<tr[^>]*>(.*?)</tr>`)
	trMatches := trPattern.FindAllStringSubmatch(table, -1)

	if len(trMatches) >= 2 {
		// 提取第一行和第二行的单元格内容
		tdPattern := regexp.MustCompile(`(?s)<td[^>]*>(.*?)</td>`)

		// 提取第一行单元格
		firstRowCells := tdPattern.FindAllStringSubmatch(trMatches[0][1], -1)
		if len(firstRowCells) == 0 {
			// 如果第一行没有td标签，可能是th标签已被前面处理，尝试第二行
			secondRowCells := tdPattern.FindAllStringSubmatch(trMatches[1][1], -1)
			if len(secondRowCells) > 0 {
				headers := make([]string, 0, len(secondRowCells))
				for _, cell := range secondRowCells {
					if len(cell) > 1 {
						headers = append(headers, strings.TrimSpace(cell[1]))
					}
				}
				header := strings.Join(headers, " | ")

				// 检查表头是否包含有效信息
				headerContent := strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(header, "-", ""), ":", ""), "|", ""))
				if headerContent == "" && len(trMatches) >= 3 {
					// 如果表头无效，尝试使用第二行数据作为表头
					thirdRowCells := tdPattern.FindAllStringSubmatch(trMatches[2][1], -1)
					if len(thirdRowCells) > 0 {
						headers := make([]string, 0, len(thirdRowCells))
						for _, cell := range thirdRowCells {
							if len(cell) > 1 {
								headers = append(headers, strings.TrimSpace(cell[1]))
							}
						}
						return strings.Join(headers, " | ")
					}
				}

				return header
			}
		} else {
			// 使用第一行的单元格作为表头
			headers := make([]string, 0, len(firstRowCells))
			for _, cell := range firstRowCells {
				if len(cell) > 1 {
					headers = append(headers, strings.TrimSpace(cell[1]))
				}
			}
			header := strings.Join(headers, " | ")

			// 检查表头是否包含有效信息
			headerContent := strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(header, "-", ""), ":", ""), "|", ""))
			if headerContent == "" && len(trMatches) >= 2 {
				// 如果表头无效，尝试使用第二行数据作为表头
				secondRowCells := tdPattern.FindAllStringSubmatch(trMatches[1][1], -1)
				if len(secondRowCells) > 0 {
					headers := make([]string, 0, len(secondRowCells))
					for _, cell := range secondRowCells {
						if len(cell) > 1 {
							headers = append(headers, strings.TrimSpace(cell[1]))
						}
					}
					return strings.Join(headers, " | ")
				}
			}

			return header
		}
	}

	return ""
}
