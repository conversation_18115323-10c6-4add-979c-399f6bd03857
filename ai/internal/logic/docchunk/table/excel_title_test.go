package table

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestExcelSheetTitleFiltering 测试Excel文件中不同格式的Sheet标题过滤
func TestExcelSheetTitleFiltering(t *testing.T) {
	tests := []struct {
		name              string
		tableStartLine    int
		currentTableRange TableRange
		potentialTitles   []PotentialTitle
		option            TableOption
		wantTitle         string
		wantLineNum       int
	}{
		{
			name:              "Excel模式-忽略带井号的Sheet名称",
			tableStartLine:    5,
			currentTableRange: TableRange{Start: 5, End: 10},
			potentialTitles: []PotentialTitle{
				{LineNum: 2, TitleText: "# Sheet1", TitleType: "heading1"},
			},
			option:      TableOption{IsExcel: true},
			wantTitle:   "",
			wantLineNum: -1,
		},
		{
			name:              "非Excel模式-保留带井号的Sheet名称",
			tableStartLine:    5,
			currentTableRange: TableRange{Start: 5, End: 10},
			potentialTitles: []PotentialTitle{
				{LineNum: 2, TitleText: "# Sheet1", TitleType: "heading1"},
			},
			option:      DefaultTableOption(), // IsExcel = false
			wantTitle:   "# Sheet1",
			wantLineNum: 2,
		},
		{
			name:              "Excel模式-同时存在Sheet名称和有效标题时选择有效标题",
			tableStartLine:    5,
			currentTableRange: TableRange{Start: 5, End: 10},
			potentialTitles: []PotentialTitle{
				{LineNum: 1, TitleText: "# Sheet1", TitleType: "heading1"},
				{LineNum: 3, TitleText: "# 销售数据", TitleType: "heading1"},
			},
			option:      TableOption{IsExcel: true},
			wantTitle:   "# 销售数据",
			wantLineNum: 3,
		},
		{
			name:              "Excel模式-数字变化的Sheet名称测试",
			tableStartLine:    5,
			currentTableRange: TableRange{Start: 5, End: 10},
			potentialTitles: []PotentialTitle{
				{LineNum: 1, TitleText: "# Sheet10", TitleType: "heading1"},
				{LineNum: 2, TitleText: "Sheet99", TitleType: "text"},
				{LineNum: 3, TitleText: "# 数据表", TitleType: "heading1"},
			},
			option:      TableOption{IsExcel: true},
			wantTitle:   "# 数据表",
			wantLineNum: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usedTitleLineNums := map[int]bool{}
			gotTitle, gotLineNum := findNearestTitle(
				tt.tableStartLine,
				tt.currentTableRange,
				tt.potentialTitles,
				[]Table{},
				usedTitleLineNums,
				tt.option,
			)

			assert.Equal(t, tt.wantTitle, gotTitle, "标题不匹配")
			assert.Equal(t, tt.wantLineNum, gotLineNum, "行号不匹配")
		})
	}
}

// TestExcelSheetTitleFiltering_CompleteDocument 测试在完整文档中Excel表格的标题过滤
func TestExcelSheetTitleFiltering_CompleteDocument(t *testing.T) {
	tests := []struct {
		name            string
		content         string
		option          TableOption
		wantTableTitles []string
	}{
		{
			name: "Excel模式-忽略带井号的默认Sheet名称",
			content: `
# Sheet1

<table>
<tr>
<th>列1</th>
<th>列2</th>
</tr>
<tr>
<td>数据1</td>
<td>数据2</td>
</tr>
</table>

# 季度销售数据

<table>
<tr>
<th>列1</th>
<th>列2</th>
</tr>
<tr>
<td>数据1</td>
<td>数据2</td>
</tr>
</table>
`,
			option:          TableOption{IsExcel: true},
			wantTableTitles: []string{"", "# 季度销售数据"},
		},
		{
			name: "Excel模式-多级标题带默认Sheet名称",
			content: `
# 数据报表

## Sheet1
这是一些描述文字。

<table>
<tr>
<th>列1</th>
<th>列2</th>
</tr>
<tr>
<td>数据1</td>
<td>数据2</td>
</tr>
</table>

## 销售分析
第一季度的销售情况如下：

<table>
<tr>
<th>产品</th>
<th>销量</th>
</tr>
<tr>
<td>产品A</td>
<td>100</td>
</tr>
</table>
`,
			option:          TableOption{IsExcel: true},
			wantTableTitles: []string{"这是一些描述文字。", "第一季度的销售情况如下："},
		},
		{
			name: "Excel模式-混合普通和带井号的Sheet名称",
			content: `
# Sheet1
Sheet2

<table>
<tr>
<th>列1</th>
<th>列2</th>
</tr>
<tr>
<td>数据1</td>
<td>数据2</td>
</tr>
</table>

# 重要数据
Sheet3

<table>
<tr>
<th>列1</th>
<th>列2</th>
</tr>
<tr>
<td>数据1</td>
<td>数据2</td>
</tr>
</table>
`,
			option:          TableOption{IsExcel: true},
			wantTableTitles: []string{"Sheet2", "Sheet3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tables, _ := ExtractMarkdownTablesWithOption(context.Background(), tt.content, tt.option)
			gotTitles := make([]string, 0, len(tables))
			for _, table := range tables {
				gotTitles = append(gotTitles, table.Title)
			}

			assert.Equal(t, tt.wantTableTitles, gotTitles, "表格标题不匹配")
		})
	}
}
