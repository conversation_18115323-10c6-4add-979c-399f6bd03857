package docchunk

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"unicode/utf8"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	assistantlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/assistant"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
)

type chunkLogic struct {
}

func (l *chunkLogic) fetchDocChunks(ctx context.Context, doc *model.TDoc, collection *model.TCollection) ([]*rag.ChunkInfo, []string, error) {
	rsp, err := client.GetRagClient().GetCollection(ctx, &rag.ReqGetCollection{
		CollectionName: collection.RagName,
		VdbType:        collection.VdbType,
		EsIns:          collection.EsIns,
		FileName:       doc.RagFilename,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("fetch doc chunks: %w", err)
	}
	return rsp.Data.Chunks, rsp.Info.Ids, nil
}

func (l *chunkLogic) uniqueDocChunks(chunks []*rag.ChunkInfo) []*rag.ChunkInfo {
	set := make(map[string]*rag.ChunkInfo, len(chunks))
	for _, chunk := range chunks {
		if set[chunk.Id] == nil {
			set[chunk.Id] = chunk
		}
	}
	newChunks := make([]*rag.ChunkInfo, 0, len(set))
	for _, chunk := range set {
		newChunks = append(newChunks, chunk)
	}
	return newChunks
}

func (l *chunkLogic) matchPreviewChunksPosition(doc *model.TDoc, data []string) []*aipb.ChunkItem {
	text := doc.IndexText

	chunks := make([]*aipb.ChunkItem, 0, len(data))
	for _, substring := range data {
		runeStart, runeLen, _, _, found := calcSubstringPosition(text, substring)
		if !found {
			continue
		}

		chunks = append(chunks, &aipb.ChunkItem{
			Start: uint32(runeStart),
			Len:   uint32(runeLen),
		})
	}
	return l.sortChunksPb(chunks)
}

func (l *chunkLogic) sortChunksPb(chunks []*aipb.ChunkItem) []*aipb.ChunkItem {
	sort.Slice(chunks, func(i, j int) bool {
		if chunks[i].Start == chunks[j].Start {
			return chunks[i].Len <= chunks[j].Len
		}
		return chunks[i].Start < chunks[j].Start
	})
	return chunks
}

func (l *chunkLogic) sortChunksModel(chunks []*model.ChunkItem) []*model.ChunkItem {
	sort.Slice(chunks, func(i, j int) bool {
		if chunks[i].RuneStart == chunks[j].RuneStart {
			return chunks[i].RuneLen <= chunks[j].RuneLen
		}
		return chunks[i].RuneStart < chunks[j].RuneStart
	})
	return chunks
}

// 计算子串位置
func calcSubstringPosition(str, substr string) (runeStart, runLen, charStart, charLen int, found bool) {
	if len(str) == 0 || len(substr) == 0 {
		return
	}

	charStart = strings.Index(str, substr)
	if charStart == -1 {
		return
	}

	runeStart = utf8.RuneCountInString(str[:charStart+1]) - 1
	runLen = utf8.RuneCountInString(substr)
	charLen = len(substr)
	found = true

	return
}

func (l *chunkLogic) queryDoc(ctx context.Context, docID uint64, withText bool) (*model.TDoc, error) {
	fields := []string{"id", "rag_filename", "index_text_md5"}
	if withText {
		fields = append(fields, "index_text")
	}

	doc, err := model.NewQuery[model.TDoc](ctx).Select(fields).FindBy("id = ?", docID)
	if err != nil {
		return nil, fmt.Errorf("query doc: %w", err)
	}
	return doc, nil
}

func (l *chunkLogic) queryAssistant(ctx context.Context, assistantID, docID uint64, admin *basepb.Identity) (*model.TAssistant, error) {
	// 如果指定了admin，只查询所属该admin的助手列表
	var teamIDs, userIDs []uint64
	if admin != nil {
		switch admin.IdentityType {
		case basepb.IdentityType_IDENTITY_TYPE_USER:
			userIDs = []uint64{admin.IdentityId}
		case basepb.IdentityType_IDENTITY_TYPE_TEAM:
			teamIDs = []uint64{admin.IdentityId}
		}
	}
	assistants, err := new(assistantlogic.GetAssistantsLogic).Get(ctx, &aipb.ReqGetAssistants{
		Filter: &aipb.ReqGetAssistants_Filter{
			AssistantId: []uint64{assistantID},
			UserId:      userIDs,
			TeamId:      teamIDs,
			IsDraft:     basepb.BoolEnum_BOOL_ENUM_FALSE,
			DocId:       docID,
		},
		Relation: &aipb.ReqGetAssistants_Relation{
			Collection: true,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("query assistant: %w", err)
	}
	if len(assistants) == 0 {
		return nil, xerrors.NotFoundError("assistant not found")
	}

	return assistants[0], nil
}
