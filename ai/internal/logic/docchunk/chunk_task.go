package docchunk

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	tablechunk "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk/table"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/helper"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/go-redsync/redsync/v4"
	"gorm.io/gorm"
)

// ChunkTaskLogic 分段任务逻辑
type ChunkTaskLogic struct {
	chunkLogic
}

// FireSubtasks 触发子任务
func (l *ChunkTaskLogic) FireSubtasks(ctx context.Context) error {
	subtasks, err := model.NewQuery[model.TDocChunkSubtask](ctx).
		Select("id").OrderBy("id", false).
		GetBy("state = ?", aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_ACTIVATED)
	if err != nil {
		return fmt.Errorf("query waiting subtasks: %w", err)
	}
	if len(subtasks) == 0 {
		return nil
	}

	l.fireSubtasksAsync(ctx, subtasks)

	return nil
}

// TryFinishTasks 尝试结束任务
func (l *ChunkTaskLogic) TryFinishTasks(ctx context.Context) error {
	tasks, err := model.NewQuery[model.TDocChunkTask](ctx).
		Select("id").OrderBy("id", false).
		GetBy("state = ?", aipb.DocChunkTaskState_DOC_CHUNK_TASK_STATE_RUNNING)
	if err != nil {
		return fmt.Errorf("query running tasks: %w", err)
	}
	if len(tasks) == 0 {
		return nil
	}

	l.tryFinishTasksAsync(ctx, tasks)

	return nil
}

func (l *ChunkTaskLogic) fireSubtasksAsync(ctx context.Context, subtasks []*model.TDocChunkSubtask) {
	for _, subtask := range subtasks {
		subtaskID := subtask.ID
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			if err := l.RunSubtask(ctx, subtaskID); err != nil {
				log.WithContext(ctx).Errorw("run subtask failed", "error", err)
			}
			return nil
		}, boot.TraceGo(ctx))
	}
}

func (l *ChunkTaskLogic) tryFinishTasksAsync(ctx context.Context, tasks []*model.TDocChunkTask) {
	for _, task := range tasks {
		taskID := task.ID
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			if _, err := l.tryFinishTask(xorm.Use(ctx, model.ConnNameV2), taskID); err != nil {
				log.WithContext(ctx).Errorw("try finish task failed", "error", err)
			}
			return nil
		}, boot.TraceGo(ctx))
	}
}

// RunSubtask 运行子任务
func (l *ChunkTaskLogic) RunSubtask(ctx context.Context, subtaskID uint64) error {
	// 加锁防止同时执行
	lockKey := "DocChunkSubtask:" + strconv.FormatUint(subtaskID, 10)
	lock := helper.NewDistributedLock(lockKey, redsync.WithExpiry(time.Minute*10))
	if err := lock.TryLockContext(ctx); err != nil {
		return fmt.Errorf("try lock: %w", err)
	}
	defer lock.UnlockContext(ctx)

	// 确认子任务状态
	subtask, err := model.NewQuery[model.TDocChunkSubtask](ctx).FindByKey(subtaskID)
	if err != nil {
		return fmt.Errorf("query subtask: %w", err)
	}
	if subtask.State != aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_ACTIVATED {
		return nil
	}

	// 超时直接标记失败
	if time.Since(subtask.CreateDate) > config.GetDurationOr("chunkdoc.subtaskTtl", time.Hour) {
		if err = l.tryFinishSubtask(ctx, subtask, aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_FAILED); err != nil {
			return err
		}
	}

	// 执行子任务
	switch subtask.Type {
	case aipb.DocChunkSubtaskType_DOC_CHUNK_SUBTASK_TYPE_CREATE_CHUNK:
		err = l.runCreateChunkSubtask(ctx, subtask)
	case aipb.DocChunkSubtaskType_DOC_CHUNK_SUBTASK_TYPE_DELETE_CHUNK:
		err = l.runDeleteChunkSubtask(ctx, subtask)
	}

	if err != nil {
		if tryFinishTaskErr := l.tryFinishSubtask(ctx, subtask,
			aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_FAILED); tryFinishTaskErr != nil {
			return fmt.Errorf("error1: %w; error2: %w", err, tryFinishTaskErr)
		}
	} else {
		err = l.tryFinishSubtask(ctx, subtask,
			aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_SUCCESS)
	}

	return nil
}

func (l *ChunkTaskLogic) runCreateChunkSubtask(ctx context.Context, subtask *model.TDocChunkSubtask) error {
	log.WithContext(ctx).Debugw("run edit_collection subtask", "subtask", subtask.ID)

	para := &rag.ReqEditCollection{}
	if err := json.Unmarshal(subtask.Para, para); err != nil {
		return fmt.Errorf("unmarshal edit_collection para: %w", err)
	}

	text := para.Text
	tables, _ := tablechunk.ExtractMarkdownTables(text)
	// 取第一个表格的index_content
	if len(tables) != 0 {
		para.IndexContent = tables[0].GenEditCollectionIndexContent()
	}
	if _, err := client.GetRagClient().EditCollection(ctx, para); err != nil {
		return fmt.Errorf("rag.EditCollection: %w", err)
	}
	err := model.UpdateCollectionEmbUpdateDateByName(model.NewConnection(ctx), para.CollectionName)
	if err != nil {
		return fmt.Errorf("update collection emb update date: %w", err)
	}

	return l.tryFinishSubtask(ctx, subtask, aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_SUCCESS)
}

func (l *ChunkTaskLogic) runDeleteChunkSubtask(ctx context.Context, subtask *model.TDocChunkSubtask) error {
	log.WithContext(ctx).Debugw("run delete_vec subtask", "subtask", subtask.ID)

	para := &rag.ReqDeleteVecById{}
	if err := json.Unmarshal(subtask.Para, para); err != nil {
		return fmt.Errorf("unmarshal delete_vec para: %w", err)
	}

	if _, err := client.GetRagClient().DeleteVecById(ctx, para); err != nil {
		return fmt.Errorf("rag.DeleteVec: %w", err)
	}
	err := model.UpdateCollectionEmbUpdateDateByName(model.NewConnection(ctx), para.CollectionName)
	if err != nil {
		return fmt.Errorf("update collection emb update date: %w", err)
	}

	return l.tryFinishSubtask(ctx, subtask, aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_SUCCESS)
}

func (l *ChunkTaskLogic) tryFinishTask(tx *gorm.DB, taskID uint64) (bool, error) {
	const tryFinishTaskSql = "UPDATE `t_doc_chunk_task` SET `finished_at` = NOW(), `state` = ? WHERE `id` = ? AND " +
		"(SELECT COUNT(*) FROM `t_doc_chunk_subtask` WHERE `task_id` = `t_doc_chunk_task`.`id` AND `state` IN (?)) = `t_doc_chunk_task`.`subtask_count`"

	args := []any{
		aipb.DocChunkTaskState_DOC_CHUNK_TASK_STATE_FINISHED,
		taskID,
		[]aipb.DocChunkSubtaskState{
			aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_SUCCESS,
			aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_FAILED,
		},
	}
	result := tx.Exec(tryFinishTaskSql, args...)
	if result.Error != nil {
		return false, fmt.Errorf("try finish task: %w", result.Error)
	}

	return result.RowsAffected > 0, nil
}

func (l *ChunkTaskLogic) tryFinishSubtask(ctx context.Context, subtask *model.TDocChunkSubtask, subtaskState aipb.DocChunkSubtaskState) error {
	log.WithContext(ctx).Debugw("try finish chunk doc task", "task", subtask.TaskID, "subtask", subtask.ID, "subtask state", subtaskState)

	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		// 更新子任务状态
		err := tx.Model(&model.TDocChunkSubtask{}).Where("id = ?", subtask.ID).Updates(map[string]any{
			"state":       subtaskState,
			"finished_at": time.Now(),
		}).Error
		if err != nil {
			return fmt.Errorf("update subtask.state: %w", err)
		}

		// 尝试结束主任务
		_, err = l.tryFinishTask(tx, subtask.TaskID)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}
