package docchunk

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// GetChunkLogic 查询分段逻辑
type GetChunkLogic struct {
	chunkLogic
}

// GetChunks 获取分段信息
func (l *GetChunkLogic) GetChunks(ctx context.Context, req *aipb.ReqGetDocChunks) ([]*aipb.AssistantChunks, error) {
	// 如果开启debug，需要查询原文并返回分段内容
	debug := config.GetBool("chunk.debug")

	doc, err := l.queryDoc(ctx, req.DocId, debug)
	if err != nil {
		return nil, err
	}

	query := model.NewQuery[model.TAssistantDoc](ctx).With("ChunkDetail").Where("doc_id = ?", doc.ID)
	if len(req.AssistantId) > 0 {
		query.Where("assistant_id IN (?)", req.AssistantId)
	}
	docAssistants, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("query doc assistants: %w", err)
	}

	chunks := make([]*aipb.AssistantChunks, 0)
	sumToChunks := make(map[string]*aipb.AssistantChunks)
	for _, docAssistant := range docAssistants {
		if docAssistant.ChunkDetail == nil {
			continue
		}
		if ac := sumToChunks[docAssistant.ChunkDetail.Sum]; ac != nil {
			ac.AssistantId = append(ac.AssistantId, docAssistant.AssistantID)
		} else {
			if debug {
				docAssistant.ChunkDetail.ChunkDoc(doc.IndexText)
			}

			ac = &aipb.AssistantChunks{
				AssistantId: []uint64{docAssistant.AssistantID},
				Chunks:      model.ChunkItems(docAssistant.ChunkDetail.Items).ToPb(debug),
			}
			sumToChunks[docAssistant.ChunkDetail.Sum] = ac
			chunks = append(chunks, ac)
		}
	}

	return chunks, nil
}
