package docchunk

import (
	"fmt"
	"io"
	"os"
	"testing"
	"unicode/utf8"

	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
)

func TestChunkPos(t *testing.T) {
	file := "/Users/<USER>/test_副本3_mergelines.txt"
	start := 58656
	length := 102
	content, err := readFileContent(file)
	if err != nil {
		t.Fatal(err)
	}

	l := utf8.RuneCountInString(content)
	fmt.Printf("content length: %d\n", l)
	s := xstrings.Substring(content, start, length)
	fmt.Println(s)
}

func readFileContent(file string) (string, error) {
	f, err := os.Open(file)
	if err != nil {
		return "", fmt.Errorf("open file: %w", err)
	}
	defer f.Close()

	content, err := io.ReadAll(f)
	if err != nil {
		return "", fmt.Errorf("read file content: %w", err)
	}

	return string(content), nil
}
