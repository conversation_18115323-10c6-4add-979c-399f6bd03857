package docchunk

// 节点
type linkedListNode[T any] struct {
	value T
	prev  *linkedListNode[T]
	next  *linkedListNode[T]
}

// 链表
type linkedList[T any] struct {
	head   *linkedListNode[T]
	tail   *linkedListNode[T]
	length int
}

// 追加节点
func (l *linkedList[T]) append(v T) {
	node := &linkedListNode[T]{
		value: v,
	}
	if l.tail == nil {
		l.head = node
		l.tail = node
	} else {
		node.prev = l.tail
		l.tail.next = node
		l.tail = node
	}
	l.length++
}

func (l *linkedList[T]) remove(n *linkedListNode[T]) {
	if n == nil {
		return
	}

	if n.prev != nil {
		n.prev.next = n.next
	}
	if n.next != nil {
		n.next.prev = n.prev
	}
	if n == l.head {
		l.head = n.next
	}
	if n == l.tail {
		l.tail = n.prev
	}

	n.prev = nil
	n.next = nil

	l.length--
}

func (l *linkedList[T]) foreach(f func(*linkedListNode[T])) {
	for current := l.head; current != nil; {
		next := current.next
		f(current)
		current = next
	}
}

func (l *linkedList[T]) getLength() int {
	return l.length
}
