package logic

import (
	"context"
	"errors"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	docchunklogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk/table"
	tablechunk "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk/table"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/helper"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	ragcommon "e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag/common"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot/microwrapper"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/metric"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"github.com/go-redsync/redsync/v4"
	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// IndexText 用于文本分片
// Text 用于QA中的回答
// Collections 用于collection
var docDiffFields = []string{"IndexText", "Text", "Collections", "MatchPatterns"}

var (
	DocSyncM     *DocSyncRagManager
	DocSyncMOnce sync.Once

	fairStrategy     *DocSyncQueueStrategyFair
	fairStrategyOnce sync.Once
)

const CreateCollectionMetricType = "AIApiCreateCollection"

type CollectionMetric struct {
	metric.CommonFetchMetric
	DocId        uint64 `json:"doc_id"`
	CollectionId uint64 `json:"collection_id"`
	SessionId    string `json:"session_id"`
}

// DocSyncQueueStrategy doc同步排队策略
type DocSyncQueueStrategy interface {
	GetNextRecord(ctx context.Context, minId uint64) (*model.TDocSync, error)
}

// SyncQueueStrategyFIFO 先进先出
type SyncQueueStrategyFIFO struct{}

func (s *SyncQueueStrategyFIFO) GetNextRecord(ctx context.Context, minId uint64) (*model.TDocSync, error) {
	// 处理失败的doc id
	processingDocIds := make([]uint64, 0)
	db := model.NewQuery[model.TDocSync](ctx).DB()
	var state []uint64
	if minId > 0 {
		db = db.Where("id < ?", minId)
		// state = append(state, 1)
	}
	state = append(state, 3)
	err := db.Where("state in ?", state).Pluck("doc_id", &processingDocIds).Error
	if err != nil {
		return nil, err
	}

	var row *model.TDocSync
	db = model.NewQuery[model.TDocSync](ctx).DB() // new session
	if minId > 0 {
		db = db.Where("id >= ?", minId)
	}
	if len(processingDocIds) != 0 {
		db = db.Where("doc_id not in ?", processingDocIds)
	}
	err = db.Where("state = ?", 1).
		Order("`order` asc").Limit(1).First(&row).Error
	if err != nil {
		return nil, err
	}
	return row, nil
}

// DocSyncQueueStrategyPriorityByCollection 指定特定的collection先同步
type DocSyncQueueStrategyPriorityByCollection struct{}

// GetNextRecord 获取下一个待处理的record(未处理过的), 依据collection_id优先级
func (s *DocSyncQueueStrategyPriorityByCollection) GetNextRecord(ctx context.Context, minId uint64) (*model.TDocSync, error) {
	processingDocIds := make([]uint64, 0)
	db := model.NewQuery[model.TDocSync](ctx).DB()

	var state []uint64
	if minId > 0 {
		db = db.Where("id < ?", minId)
	}
	state = append(state, 3)
	err := db.Where("state in ?", state).Pluck("doc_id", &processingDocIds).Error
	if err != nil {
		return nil, err
	}

	var row *model.TDocSync
	db = model.NewQuery[model.TDocSync](ctx).DB() // new session
	if minId > 0 {
		db = db.Where("id >= ?", minId)
	}
	if len(processingDocIds) != 0 {
		db = db.Where("doc_id not in ?", processingDocIds)
	}

	db = db.Where("state = ?", 1)
	// 如果指定了高优先级的 collection_id，优先按此条件排序
	priorityCollections := config.GetIntSlice("llm.collection.priority_collections")
	if len(priorityCollections) != 0 {
		strNums := make([]string, len(priorityCollections))
		for i, num := range priorityCollections {
			strNums[i] = strconv.Itoa(num)
		}
		result := strings.Join(strNums, ",")
		db.Order(fmt.Sprintf("FIELD(collection_id, %s ) DESC", result))
	}
	db.Order("`order` asc")
	err = db.Limit(1).First(&row).Error
	if err != nil {
		return nil, err
	}

	return row, nil
}

// DocSyncQueueStrategyFair 公平轮询collection
type DocSyncQueueStrategyFair struct {
	lastCollectionID uint64 // 记录上次被调度的 collection_id
}

func (s *DocSyncQueueStrategyFair) GetNextRecord(ctx context.Context, minId uint64) (*model.TDocSync, error) {
	var nextCollectionID uint64
	currentLastID := atomic.LoadUint64(&s.lastCollectionID)

	var row *model.TDocSync
	// 优先查询 collection_id 大于当前 lastCollectionID，且 id >= minId 的任务
	err := model.NewQuery[model.TDocSync](ctx).DB().
		Where("collection_id > ?", currentLastID).
		Where("id >= ?", minId).
		Where("state = ?", 1).
		Order("collection_id asc, id asc").
		Limit(1).
		First(&row).Error

	// 如果没有结果，从头开始查找符合 minId 和 state 条件的第一个任务
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = model.NewQuery[model.TDocSync](ctx).DB().
			Where("id >= ?", minId).
			Where("state = ?", 1).
			Order("collection_id asc, id asc").
			Limit(1).
			First(&row).Error
	}

	// 如果仍然没有找到，返回错误
	if err != nil {
		return nil, err
	}

	// 更新 nextCollectionID
	nextCollectionID = row.CollectionID
	// 原子更新 lastCollectionID
	atomic.StoreUint64(&s.lastCollectionID, nextCollectionID)

	return row, nil
}

func getFairStrategy() *DocSyncQueueStrategyFair {
	fairStrategyOnce.Do(func() {
		fairStrategy = &DocSyncQueueStrategyFair{}
	})
	return fairStrategy
}

// LoadDocSyncQueueStrategy 策略加载器
type LoadDocSyncQueueStrategy func() DocSyncQueueStrategy

var (
	lastDocSyncQueueStrategy    = ""
	lastDocSyncQueueStrategyMtx = sync.Mutex{}
)

// ConfigDocSyncQueueStrategyLoader 从配置文件读取策略的加载器
var ConfigDocSyncQueueStrategyLoader = func() DocSyncQueueStrategy {
	strategy := config.GetString("llm.collection.sync_queue_strategy")
	lastDocSyncQueueStrategyMtx.Lock()
	if lastDocSyncQueueStrategy != strategy {
		log.Infof("Doc sync queue strategy changged to: %s", strategy)
	}
	lastDocSyncQueueStrategy = strategy
	lastDocSyncQueueStrategyMtx.Unlock()
	switch strategy {
	case "priority_by_collection":
		return &DocSyncQueueStrategyPriorityByCollection{}
	case "fair_by_collection":
		return getFairStrategy()
	default:
		return nil
	}
}

func GetDocSyncRagManager(ctx context.Context, strategyLoader ...LoadDocSyncQueueStrategy) *DocSyncRagManager {
	DocSyncMOnce.Do(func() {
		if DocSyncM == nil {
			DocSyncM = NewDocSyncRagManager(ctx, strategyLoader...)
		}
	})
	return DocSyncM
}

// DocSyncRagManager 执行doc往rag侧同步的逻辑
type DocSyncRagManager struct {
	Ctx            context.Context
	wakeChan       chan struct{}
	mtxMap         sync.Map
	strategyLoader LoadDocSyncQueueStrategy
	alertCache     sync.Map // 用于存储告警记录的缓存
}

func NewDocSyncRagManager(ctx context.Context, strategyLoader ...LoadDocSyncQueueStrategy) *DocSyncRagManager {
	m := &DocSyncRagManager{
		Ctx:      ctx,
		wakeChan: make(chan struct{}, 100),
	}
	if len(strategyLoader) != 0 {
		m.strategyLoader = strategyLoader[0]
	}
	return m
}

// 获取下一个待处理的record(已经处理失败过的)
func (m *DocSyncRagManager) getNextFailedRecord(minId uint64) (*model.TDocSync, error) {
	db := model.NewQuery[model.TDocSync](m.Ctx).DB()
	var row *model.TDocSync
	db = db.Where("state = ?", 3).Order("id asc").Limit(1)
	if minId > 0 {
		db = db.Where("id >= ?", minId)
	}
	err := db.First(&row).Error
	return row, err
}

// getNextRecord 获取下一个待处理的record(未处理过的)
// func (m *DocSyncRagManager) getNextRecord(minId uint64) (*model.TDocSync, error) {
// 	// 处理失败的doc id
// 	processingDocIds := make([]uint64, 0)
// 	db := model.NewQuery[model.TDocSync](m.Ctx).DB()
// 	var state []uint64
// 	if minId > 0 {
// 		db = db.Where("id < ?", minId)
// 		// state = append(state, 1)
// 	}
// 	state = append(state, 3)
// 	err := db.Where("state in ?", state).Pluck("doc_id", &processingDocIds).Error
// 	if err != nil {
// 		return nil, err
// 	}
//
// 	var row *model.TDocSync
// 	db = model.NewQuery[model.TDocSync](m.Ctx).DB() // new session
// 	if minId > 0 {
// 		db = db.Where("id >= ?", minId)
// 	}
// 	if len(processingDocIds) != 0 {
// 		db = db.Where("doc_id not in ?", processingDocIds)
// 	}
// 	err = db.Where("state = ?", 1).
// 		Order("id asc").Limit(1).First(&row).Error
// 	if err != nil {
// 		return nil, err
// 	}
// 	return row, nil
// }

// NotifyDo 唤醒执行
func (m *DocSyncRagManager) NotifyDo(ctx context.Context) {
	select {
	case m.wakeChan <- struct{}{}:
	default:
		log.WithContext(ctx).Info("DocSyncRagManager NotifyDo failed publish to channel")
	}
}

type DocSyncFailedAlertInfo struct {
	LogId          uint64        `json:"logId"`             // 重放日志 id
	DocId          uint64        `json:"doc_id"`            // 文档id
	DocName        string        `json:"doc_name"`          // qa为问题，文件文件为文件名
	DocRagFileName string        `json:"doc_rag_file_name"` // rag文件名称
	CollectionId   uint64        `json:"collection_id"`     // collection id
	CollectionName string        `json:"collection"`        // collection 名称
	RetryCnt       uint32        `json:"retry_cnt"`         // 重试次数
	Duration       time.Duration `json:"duration"`          // 持续时间
	ErrMsg         string        `json:"err_msg"`           // 错误信息
}

// DocSyncAlertManager 同步失败告警
type DocSyncAlertManager struct {
	ctx         context.Context
	redisClient xredis.Client
	alertKey    string
	interval    time.Duration
}

func NewDocSyncAlertManager(ctx context.Context, interval time.Duration) *DocSyncAlertManager {
	return &DocSyncAlertManager{
		ctx:         ctx,
		redisClient: xredis.Default.Client(),
		alertKey:    "ai:doc_sync_alert",
		interval:    interval,
	}
}

func (am *DocSyncAlertManager) shouldAlert(docId, collectionId uint64) bool {
	field := fmt.Sprintf("%d:%d", docId, collectionId)
	lastAlertStr, err := am.redisClient.HGet(am.ctx, am.alertKey, field).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.WithContext(am.ctx).Errorf("get last alert time from redis err: %v", err)
		return false
	}

	if lastAlertStr != "" {
		lastAlertTime, err := time.Parse(time.RFC3339, lastAlertStr)
		if err != nil {
			log.WithContext(am.ctx).Errorf("parse last alert time err: %v", err)
			return false
		}
		alertInterval := am.interval
		if time.Since(lastAlertTime) < alertInterval {
			return false
		}
	}
	return true
}

func (am *DocSyncAlertManager) recordAlertTime(docId, collectionId uint64) error {
	field := fmt.Sprintf("%d:%d", docId, collectionId)
	now := time.Now()
	err := am.redisClient.HSet(am.ctx, am.alertKey, field, now.Format(time.RFC3339)).Err()
	if err != nil {
		log.WithContext(am.ctx).Errorf("update last alert time to redis err: %v", err)
		return err
	}
	// 设置整个hash的过期时间
	am.redisClient.Expire(am.ctx, am.alertKey, am.interval)
	return nil
}

// sendSyncAlert 发送同步告警信息
func (m *DocSyncRagManager) sendSyncAlert(row *model.TDocSync, alertType string) {
	// 检查告警频率限制
	alertInterval, _ := time.ParseDuration(config.GetStringOr("llm.collection.sync_alert_interval", "5m"))
	alertManager := NewDocSyncAlertManager(m.Ctx, alertInterval)
	if !alertManager.shouldAlert(row.DocID, row.CollectionID) {
		return
	}

	// 获取文档和Collection信息
	doc, err := model.NewQuery[model.TDoc](m.Ctx).Where("id = ?", row.DocID).
		Select("ID", "DataType", "FileName", "RagFilename", "IndexTextCerpt").
		Find()
	if err != nil {
		log.WithContext(m.Ctx).Errorf("get doc by id err: %v", err)
		return
	}

	collection, err := model.NewQuery[model.TCollection](m.Ctx).Where("id = ?", row.CollectionID).Find()
	if err != nil {
		log.WithContext(m.Ctx).Errorf("get collection by id err: %v", err)
		return
	}

	// 构建告警信息
	docName := doc.FileName
	if doc.DataType == uint32(ai.DocType_DOCTYPE_QA) {
		docName = doc.IndexTextCerpt
	}
	alertInfo := &DocSyncFailedAlertInfo{
		DocId:          row.DocID,
		DocName:        docName,
		DocRagFileName: doc.RagFilename,
		CollectionId:   row.CollectionID,
		CollectionName: collection.Name,
		RetryCnt:       row.RetryCnt,
		ErrMsg:         row.Msg,
		LogId:          row.ID,
	}
	if alertType == "超时" {
		alertInfo.Duration = time.Since(row.UpdateDate)
	} else if row.ActiveDate != nil {
		alertInfo.Duration = time.Since(*row.ActiveDate)
	}

	// 记录详细日志
	log.WithContext(m.Ctx).Infow(fmt.Sprintf("文档同步%s告警", alertType),
		"log_id", alertInfo.LogId,
		"doc_id", alertInfo.DocId,
		"doc_name", alertInfo.DocName,
		"doc_rag_file_name", alertInfo.DocRagFileName,
		"collection_id", alertInfo.CollectionId,
		"collection_name", alertInfo.CollectionName,
		"retry_cnt", alertInfo.RetryCnt,
		"duration", alertInfo.Duration.String(),
		"err_msg", alertInfo.ErrMsg,
	)

	if !config.GetBoolOr("llm.collection.sync_alert_wechat_enabled", true) {
		return
	}

	env := config.GetStringOr("llm.env", "dev")
	// 构建告警消息
	msg := fmt.Sprintf("环境: %s\n文档同步%s告警\n日志ID: %d\n文档ID: %d\n文档名称: %s\n文档rag名称: %s\nCollection ID: %d\nCollection名称: %s\n重试次数: %d\n持续时间: %s\n错误信息: %s",
		env,
		alertType,
		alertInfo.LogId,
		alertInfo.DocId,
		alertInfo.DocName,
		alertInfo.DocRagFileName,
		alertInfo.CollectionId,
		alertInfo.CollectionName,
		alertInfo.RetryCnt,
		alertInfo.Duration.String(),
		alertInfo.ErrMsg,
	)

	// 发送企业微信告警
	client.LLMWeChatSendMsg(msg)

	// 记录本次告警时间
	err = alertManager.recordAlertTime(row.DocID, row.CollectionID)
	if err != nil {
		log.WithContext(m.Ctx).Errorf("record alert time failed: %v", err)
	}
}

func (m *DocSyncRagManager) AlertSyncFailed(row *model.TDocSync) {
	maxRetry := config.GetUint32Or("llm.collection.sync_max_retry_alert", 3)
	// 失败次数小于告警阈值，直接返回
	if row.RetryCnt < maxRetry {
		return
	}
	m.sendSyncAlert(row, "失败")
}

// DoFailed 处理同步失败的记录，固定周期尝试
// 失败一条记录后，继续往后执行。没成功一条，就从0开始查找记录执行
func (m *DocSyncRagManager) DoFailed() {
	retryDelay, _ := time.ParseDuration(config.GetStringOr("llm.collection.sync_retry_delay_failed", "5m"))
	syncSucceed := true
	minId := uint64(0)
	timer := time.NewTimer(retryDelay)
	for {
		if !syncSucceed {
			retryDelay, _ = time.ParseDuration(config.GetStringOr("llm.collection.sync_retry_delay_failed", "5m"))
			if !timer.Stop() {
				select {
				case <-timer.C: // try to drain from the channel
				default:
				}
			}
			timer.Reset(retryDelay)
			select {
			case <-timer.C:
			}
		}

		ctx, _ := microwrapper.StartSpanFromContext(m.Ctx, nil, "")
		logId := uint64(0)
		err := func() error {
			row, err := m.getNextFailedRecord(minId)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					minId = 0
				}
				return err
			}
			logId = row.ID
			_, err = m.getLock(row.DocID, row.CollectionID)
			if err != nil {
				minId = row.ID + 1
				return nil // 未获取到锁直接跳过
				// return fmt.Errorf("DocSyncRagManager get localMtx %s failed, %w", lockName, err)
			}
			defer m.releaseLock(row.DocID, row.CollectionID)
			// 再次检查是否已经被处理了
			state := int32(0)
			err = model.NewQuery[model.TDocSync](ctx).DB().Where("id = ?", row.ID).Pluck("state", &state).Error
			if err != nil {
				return err
			}
			if state != 0 && state != row.State {
				return nil // 已经被处理过，跳过处理
			}

			// merge log
			mergeLog, err := m.MergeLog(ctx, row.DocID, row.CollectionID)
			if err != nil {
				return err
			}
			if mergeLog == nil || mergeLog.ID == 0 {
				return nil
			}
			row = mergeLog

			l := NewDocSyncLogic(ctx, row.New, row.Old)
			log.WithContext(ctx).Debugf("DocSyncRagManager do Syncing")
			err = l.Sync()
			err = model.Transaction(l.ctx, func(tx *gorm.DB) error {
				row.RetryCnt += 1
				row.UpdateDate = time.Now()

				if err != nil {
					row.Msg = err.Error()
					row.State = 3
					m.AlertSyncFailed(row)
					tx.Model(row).Select("state", "update_date", "msg", "retry_cnt").Updates(row)
					minId = row.ID + 1 // 扫描后续的记录
					log.WithContext(m.Ctx).Errorf("Ignored DocSyncRagManager sync failed log id: %d failed: %v, tring next log", logId, err)
					return nil
				}
				row.State = 2
				err = tx.Model(row).Select("state", "update_date", "retry_cnt").Updates(row).Error
				if err != nil {
					return err
				}

				versionMap := map[uint64][]uint64{row.DocID: util.MultiplySlice([]uint64{row.CollectionID}, int(row.MergedCnt))}
				err = UpdateDocCollectionSyncVersion(tx, versionMap, true)
				if err != nil {
					return err
				}

				if row.New == nil {
					err = MarkDocDeletedInCollection(tx, row.DocID, []uint64{row.CollectionID})
					if err != nil {
						return err
					}
				}

				if row.CollectionID != 0 {
					err = model.UpdateDocRagVersion(tx, row.DocID, uint64(row.MergedCnt))
					if err != nil {
						return err
					}
					err = model.UpdateCollectionEmbUpdateDate(tx, row.CollectionID)
					if err != nil {
						return err
					}
				} else {
					// 0代表删除，不用推进RagVersion
					// TODO: 理论上写 log 之前处理好了，不会到这里
					err = model.TryDeleteDoc(tx, row.DocID)
				}
				if err != nil {
					return err
				}

				log.WithContext(m.Ctx).Infof("DocSyncRagManager sync success log id: %d", row.ID)
				// 重置minId，确保跳过的失败记录能够被处理
				minId = 0
				return nil
			})
			if err != nil {
				return err
			}
			return nil
		}()
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.WithContext(m.Ctx).Errorf("DocSyncRagManager sync failed log id: %d failed: %v", logId, err)
			}
			syncSucceed = false
		} else {
			syncSucceed = true
		}
	}
}

// MergeLog 合并 doc 的 log，减少 rag 的处理
func (m *DocSyncRagManager) MergeLog(ctx context.Context, docId, collectionId uint64) (*model.TDocSync, error) {
	merged := &model.TDocSync{}
	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		db := xorm.NewQueryWithDB[model.TDocSync](tx)
		logs, err := db.Where("doc_id = ? and collection_id = ?", docId, collectionId).
			Where("state in (1,3)").OrderBy("id", false).Get()
		if err != nil {
			return err
		}
		if len(logs) == 0 {
			return nil
		}
		merged = logs[len(logs)-1]
		if len(logs) == 1 {
			return nil
		}
		versionCnt := uint32(0)
		ids := make([]uint64, 0)
		// 寻找最早的非空TraceID
		traceID := ""
		for _, v := range logs {
			ids = append(ids, v.ID)
			versionCnt += v.MergedCnt
			if traceID == "" && v.TraceID != "" {
				traceID = v.TraceID
			}
		}
		// 保留找到的最早TraceID
		if traceID != "" {
			merged.TraceID = traceID
		}
		minlog := logs[0]
		merged.Old = minlog.Old
		merged.UpdateDate = time.Now()
		merged.State = 1
		merged.Msg = ""
		merged.MergedMsg = fmt.Sprintf("merged from log %v", ids)
		merged.MergedCnt = versionCnt

		// 设置状态为已合并
		err = tx.Model(&model.TDocSync{}).Where("id in ?", ids).UpdateColumn("state", 4).Error
		if err != nil {
			return err
		}
		return tx.Model(merged).Select("*").Updates(merged).Error
	})
	if err != nil {
		return nil, err
	}
	return merged, nil
}

// 获取锁，成功返回nil
func (m *DocSyncRagManager) getLock(docId, collectionId uint64) (*redsync.Mutex, error) {
	lockName := fmt.Sprintf("ai:DocSyncRagManager:%d:%d", docId, collectionId)
	dmtx := helper.NewDistributedLock(lockName, redsync.WithExpiry(time.Minute*80), redsync.WithTries(1)) // 锁时间设置长一点
	err := dmtx.Lock()
	if err == nil {
		m.mtxMap.Store(dmtx.Name(), dmtx)
	}
	return dmtx, err
}

// 释放锁
func (m *DocSyncRagManager) releaseLock(docId, collectionId uint64) error {
	lockName := fmt.Sprintf("ai:DocSyncRagManager:%d:%d", docId, collectionId)
	dmtx, ok := m.mtxMap.Load(lockName)
	if !ok {
		return nil
	}
	dmtx.(*redsync.Mutex).Unlock()
	m.mtxMap.Delete(lockName)
	return nil
}

// ReleaseAllLock 释放所有锁
func (m *DocSyncRagManager) ReleaseAllLock() error {
	var failed []string
	m.mtxMap.Range(func(k, v any) bool {
		redv := v.(*redsync.Mutex)
		fmt.Println(redv.Name())
		unlock, err := redv.Unlock()
		if err != nil || !unlock {
			failed = append(failed, redv.Name())
		}
		m.mtxMap.Delete(k)
		return true
	})
	if len(failed) > 0 {
		log.WithContext(m.Ctx).Errorf("ReleaseAllLock occur failed. %s", strings.Join(failed, ","))
	}
	fmt.Println("ReleaseAllLock")
	return nil
}

// MonitorPendingSync 主动监控未处理成功的同步记录
func (m *DocSyncRagManager) MonitorPendingSync() {
	for {
		// 获取配置参数
		monitorInterval, _ := time.ParseDuration(config.GetStringOr("llm.collection.sync_monitor_interval", "2m"))
		pendingTimeout, _ := time.ParseDuration(config.GetStringOr("llm.collection.sync_monitor_pending_timeout", "5m"))
		// 每次最大的告警数量
		maxBatch := config.GetIntOr("llm.collection.sync_monitor_pending_bath", 5)

		time.Sleep(monitorInterval)

		// 查询超时未处理的记录
		var records []*model.TDocSync
		db := model.NewQuery[model.TDocSync](m.Ctx).DB()
		err := db.Where("state in (1,3)").
			Where("update_date < ?", time.Now().Add(-pendingTimeout)).
			Limit(maxBatch).
			Order("update_date asc").
			Find(&records).Error
		if err != nil {
			log.WithContext(m.Ctx).Errorf("查询未处理同步记录失败: %v", err)
			time.Sleep(monitorInterval)
			continue
		}

		// 处理每条超时记录
		for _, record := range records {
			// 如果刚好是第一次在执行该任务，把重试次数显示为 1，而不是 0
			if record.RetryCnt == 0 && record.ActiveDate != nil && *record.ActiveDate == record.UpdateDate {
				record.RetryCnt = 1
			}
			m.sendSyncAlert(record, "超时")
		}
	}
}

func (m *DocSyncRagManager) parseLockName(name string) (docId, collectionId uint64) {
	parts := strings.Split(name, ":")
	if len(parts) != 4 {
		return
	}
	docId, _ = strconv.ParseUint(parts[2], 10, 64)
	collectionId, _ = strconv.ParseUint(parts[3], 10, 64)
	return
}

// Do 执行同步逻辑,处理待同步的记录
func (m *DocSyncRagManager) Do(tid int) {
	log.WithContext(m.Ctx).Debugf("DocSyncRagManager Do, theadId: %d", tid)
	retryDelay, _ := time.ParseDuration(config.GetStringOr("llm.collection.sync_retry_delay", "5m"))
	maxRetryDelay, _ := time.ParseDuration(config.GetStringOr("llm.collection.sync_max_retry_delay", "30m"))

	// syncSucceed 为false时，代表：
	// 1. 未找到待处理的记录
	// 2. 同步失败（数据库、第三方接口问题）
	syncSucceed := true
	timer := time.NewTimer(retryDelay)
	minId := uint64(0)
	for {
		// 创建一个新的context，但不关联任何traceid
		baseCtx := context.Background()
		ctx, _ := microwrapper.StartSpanFromContext(baseCtx, nil, "")
		if !syncSucceed {
			if !timer.Stop() {
				select {
				case <-timer.C: // try to drain from the channel
				default:
				}
			}
			timer.Reset(retryDelay)
			select {
			case <-m.wakeChan:
				log.WithContext(ctx).Debugf("DocSyncRagManager Do from wake chan")
			case <-timer.C:
				log.WithContext(m.Ctx).Debugf("DocSyncRagManager Do from time ticker, delay: %s", retryDelay.String())
			}
		}

		db := model.NewQuery[model.TDocSync](ctx).DB()
		err := func() error {
			var queueStrategy DocSyncQueueStrategy
			if m.strategyLoader != nil {
				queueStrategy = m.strategyLoader()
			}
			// 没有设置策略，则默认使用fifo
			if queueStrategy == nil {
				queueStrategy = new(SyncQueueStrategyFIFO)
			}
			row, err := queueStrategy.GetNextRecord(ctx, minId)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					minId = 0
				}
				return err
			}

			// 如果有traceID，使用它重新创建新的context
			if row.TraceID != "" {
				// 创建带有原始traceID的新context
				traceID, _ := trace.TraceIDFromHex(row.TraceID)
				spanCtx := trace.NewSpanContext(trace.SpanContextConfig{
					TraceID: traceID,
					SpanID:  trace.SpanID{}, // 新生成一个SpanID
					Remote:  true,
				})
				ctx = trace.ContextWithSpanContext(baseCtx, spanCtx)
				ctx, _ = microwrapper.StartSpanFromContext(ctx, nil, "DocSyncRagManager.Do")
				log.WithContext(ctx).Debugf("继续原始请求的traceid: %s", row.TraceID)
			}

			var dmtx *redsync.Mutex
			dmtx, err = m.getLock(row.DocID, row.CollectionID)
			if err != nil {
				minId = row.ID + 1
				log.WithContext(ctx).Debugf("DocSyncRagManager lock %s failed theadId: %d, docId: %d\n", dmtx.Name(), tid, row.DocID)
				return nil
			}
			defer m.releaseLock(row.DocID, row.CollectionID)

			// 再次检查是否已经被处理了
			state := int32(0)
			err = db.Model(&model.TDocSync{}).Where("id = ?", row.ID).Pluck("state", &state).Error
			if err != nil {
				return err
			}
			if state != 0 && state != row.State {
				log.WithContext(ctx).Debugf("DocSyncRagManager Get localMtx, but recrod has been processed. theadId: %d, docId: %d\n", tid, row.DocID)
				return nil // 已经被处理过，跳过处理
			}

			mergeLog, err := m.MergeLog(ctx, row.DocID, row.CollectionID)
			if err != nil {
				return err
			}
			if mergeLog == nil || mergeLog.ID == 0 {
				return nil
			}
			row = mergeLog

			activeTime := time.Now()
			row.ActiveDate = &activeTime
			err = model.NewQuery[model.TDocSync](ctx).DB().Model(row).Select("active_date").Updates(row).Error
			if err != nil {
				log.WithContext(ctx).Errorf("Update doc sync log activeTime err, id: %d, err: %s", row.ID, err.Error())
			}

			l := NewDocSyncLogic(ctx, row.New, row.Old)
			err = l.Sync()
			return model.Transaction(l.ctx, func(tx *gorm.DB) error {
				row.RetryCnt += 1
				row.UpdateDate = time.Now()

				if err != nil {
					row.Msg = err.Error()
					row.State = 3
					tx.Model(row).Select("state", "update_date", "retry_cnt", "msg").Updates(row)
					log.WithContext(ctx).Errorf("Ignored DocSyncRagManager sync failed log id: %d failed: %v, tring next log", row.ID, err)
					return nil // 跳过该次操作
				}
				row.State = 2
				err = tx.Model(row).Select("state", "update_date", "retry_cnt").Updates(row).Error
				if err != nil {
					return err
				}

				versionMap := map[uint64][]uint64{row.DocID: util.MultiplySlice([]uint64{row.CollectionID}, int(row.MergedCnt))}
				err = UpdateDocCollectionSyncVersion(tx, versionMap, true)
				if err != nil {
					return err
				}

				if row.New == nil {
					err = MarkDocDeletedInCollection(tx, row.DocID, []uint64{row.CollectionID})
					if err != nil {
						return err
					}
				}

				if row.CollectionID != 0 {
					err = model.UpdateDocRagVersion(tx, row.DocID, uint64(row.MergedCnt))
					if err != nil {
						return err
					}
					err = model.UpdateCollectionEmbUpdateDate(tx, row.CollectionID)
					if err != nil {
						return err
					}
				} else {
					// 0代表删除，不用推进RagVersion
					// TODO: 理论上写 log 之前处理好了，不会到这里
					err = model.TryDeleteDoc(tx, row.DocID)
				}
				if err != nil {
					return err
				}

				minId = 0
				log.WithContext(ctx).Infof("DocSyncRagManager sync success, treadId: %d, log id: %d, docId: %d", tid, row.ID, row.DocID)
				return nil
			})
		}()
		if err != nil {
			syncSucceed = false
			maxRetryDelay, _ = time.ParseDuration(config.GetStringOr("llm.collection.sync_max_retry_delay", "30m"))
			retryDelay = min(2*retryDelay, maxRetryDelay)
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.WithContext(ctx).Errorf("DocSyncRagManager Do failed: %v, will sleep %fs", err, retryDelay.Seconds())
			} else {
				log.WithContext(ctx).Infof("DocSyncRagManager no record find: %v, will sleep %fs", err, retryDelay.Seconds())
			}
		} else {
			syncSucceed = true
			retryDelay, _ = time.ParseDuration(config.GetStringOr("llm.collection.sync_retry_delay", "5m"))
		}
	}
}

// TryDeleteDocWithMerge 尝试删除doc，压缩日志，并不走同步流程
// 只有当前doc没有正在被处理才行
func (m *DocSyncRagManager) TryDeleteDocWithMerge(ctx context.Context, docId, collectionId uint64) error {
	_, err := m.getLock(docId, collectionId)
	if err != nil {
		return err
	}
	defer m.releaseLock(docId, collectionId)

	mergeLog, err := m.MergeLog(ctx, docId, collectionId)
	if err != nil {
		return err
	}
	if mergeLog == nil || mergeLog.ID == 0 {
		return nil
	}
	row := mergeLog

	// 只有old和new都为nil才能免向量同步删除
	if row.Old == nil && row.New == nil {
		l := NewDocSyncLogic(ctx, row.New, row.Old)
		err = l.Sync()
		return model.Transaction(l.ctx, func(tx *gorm.DB) error {
			if err != nil {
				return err
			}
			err = tx.Model(row).UpdateColumn("state", 2).Error
			if err != nil {
				return err
			}

			versionMap := map[uint64][]uint64{row.DocID: util.MultiplySlice([]uint64{row.CollectionID}, int(row.MergedCnt))}
			err = UpdateDocCollectionSyncVersion(tx, versionMap, true)
			if err != nil {
				return err
			}

			if row.New == nil {
				err = MarkDocDeletedInCollection(tx, row.DocID, []uint64{row.CollectionID})
				if err != nil {
					return err
				}
			}

			err = model.UpdateDocRagVersion(tx, row.DocID, uint64(row.MergedCnt))
			if err != nil {
				return err
			}

			return nil
		})
	}
	return nil
}

func min(a, b time.Duration) time.Duration {
	if a < b {
		return a
	}
	return b
}

// DocSyncLogic ...
type DocSyncLogic struct {
	ctx context.Context
	old *model.TDoc
	new *model.TDoc

	upsertedNew bool
}

// NewDocSyncLogic ...
func NewDocSyncLogic(ctx context.Context, new *model.TDoc, old *model.TDoc) *DocSyncLogic {
	return &DocSyncLogic{
		ctx: ctx,
		new: new,
		old: old,
	}
}

func (l *DocSyncLogic) getDocType() uint32 {
	var use *model.TDoc
	if l.old != nil {
		use = l.old
	}
	if l.new != nil {
		use = l.new
	}
	if use == nil {
		return 0
	}
	return use.DataType
}

// RemoveDocDisabledCollection 移除doc中状态为disabled/deleting的collection
func RemoveDocDisabledCollection(t *model.TDoc) {
	if t == nil {
		return
	}
	t.Collections = nil
	mp := make(map[uint64]*model.TCollection)
	stMap := make(map[uint64]ai.DocState)
	if len(t.Assistants) != 0 {
		for _, v := range t.States {
			if v.State == ai.DocState_DOC_STATE_ENABLED {
				stMap[v.AssistantID] = v.State
			}
		}
		for _, v := range t.Assistants {
			if stMap[v.ID] == ai.DocState_DOC_STATE_ENABLED && len(v.Collections) != 0 {
				for _, c := range v.Collections {
					mp[c.ID] = &c.TCollection
				}
			}
		}
		for _, v := range mp {
			t.Collections = append(t.Collections, v)
		}
	}
	t.State = ai.DocState_DOC_STATE_ENABLED
	return
}

// 抽取部分字段用于同步向量
func pluckDocFieldToSync(doc *model.TDoc) *model.TDoc {
	if doc != nil {
		m := &model.TDoc{
			ID:          doc.ID,
			Text:        doc.Text,
			DataType:    doc.DataType,
			IndexText:   doc.IndexText,
			RagFilename: doc.RagFilename,
			Collections: doc.Collections,
			States:      doc.States,
		}
		// for _, v := range doc.Assistants {
		// 	m.Assistants = append(m.Assistants, &model.TAssistant{
		// 		ID:          v.ID,
		// 		Collections: v.Collections,
		// 		Name:        v.Name,
		// 	})
		// }
		return m
	}
	return nil
}

// GenSyncLogEntry 生成同步日志记录，并从 ctx 中提取 traceID
func GenSyncLogEntry(ctx context.Context, docId uint64, old *model.TDoc, new *model.TDoc) []*model.TDocSync {
	if old == nil && new == nil {
		return nil
	}

	logs := make([]*model.TDocSync, 0)
	// 从上下文中提取 traceID
	traceID := ""
	if span := trace.SpanFromContext(ctx); span != nil {
		traceID = span.SpanContext().TraceID().String()
	}

	createLogEntry := func(collection *model.TCollection, oldData, newData *model.TDoc) *model.TDocSync {
		// 如果没有开启大模型召回，设置为null即可
		if !DocUsedLMRecall(oldData) {
			oldData = nil
		}
		if !DocUsedLMRecall(newData) {
			newData = nil
		}
		return &model.TDocSync{
			DocID:        docId,
			CollectionID: collection.ID,
			Old:          pluckDocFieldToSync(oldData),
			New:          pluckDocFieldToSync(newData),
			TraceID:      traceID,
		}
	}

	if old == nil {
		for _, v := range new.Collections {
			clone := *new
			clone.Collections = []*model.TCollection{v}
			logs = append(logs, createLogEntry(v, nil, &clone))
		}
		return logs
	}

	if new == nil {
		// 删除时，如果没有启用，仍然写入一条 log，collection 设置为 0
		if len(old.Collections) == 0 {
			logs = append(logs, createLogEntry(&model.TCollection{}, old, nil))
		}
		for _, v := range old.Collections {
			clone := *old
			clone.Collections = []*model.TCollection{v}
			logs = append(logs, createLogEntry(v, &clone, nil))
		}
		return logs
	}

	oldMap := make(map[uint64]*model.TCollection)
	for _, v := range old.Collections {
		oldMap[v.ID] = v
	}

	for _, v := range new.Collections {
		if oldCollection, exists := oldMap[v.ID]; exists {
			// 在新旧记录中，都存在同一个collection
			oldClone := *old
			oldClone.Collections = []*model.TCollection{oldCollection}
			newClone := *new
			newClone.Collections = []*model.TCollection{v}
			diff := NewDocSyncLogic(context.Background(), &newClone, &oldClone).diff()
			// 没有diff
			if len(diff) != 0 {
				logs = append(logs, createLogEntry(v, &oldClone, &newClone))
			}
			delete(oldMap, v.ID)
		} else {
			newClone := *new
			newClone.Collections = []*model.TCollection{v}
			logs = append(logs, createLogEntry(v, nil, &newClone))
		}
	}

	for _, v := range oldMap {
		oldClone := *old
		oldClone.Collections = []*model.TCollection{v}
		logs = append(logs, createLogEntry(v, &oldClone, nil))
	}

	return logs
}

// DocUsedLMRecall 文档是否启用了大模型召回
// QA 可以设置不使用大模型召回
// 文本/文件，目前必须走大模型
func DocUsedLMRecall(doc *model.TDoc) bool {
	if doc == nil {
		return false
	}
	switch ai.DocType(doc.DataType) {
	case ai.DocType_DOCTYPE_FILE, ai.DocType_DOCTYPE_TEXT:
		return true
	case ai.DocType_DOCTYPE_QA:
		for _, v := range doc.MatchPatterns {
			if v.MatchPattern == ai.DocMatchPattern_DOC_MATCH_PATTERN_LARGE_MODEL_RECALL {
				return true
			}
		}
	}
	return false
}

type SyncDocToDBLogBulkOpt struct {
	// 是删除操作吗，删除可能走快速路径
	IsDelete bool
}

// SyncDocToDBLogBulk 批量数据库记录操作，后续异步消费
func SyncDocToDBLogBulk(ctx context.Context, tx *gorm.DB, olds []*model.TDoc, news []*model.TDoc, opt ...SyncDocToDBLogBulkOpt) (err error) {
	if olds == nil && news != nil {
		olds = make([]*model.TDoc, len(news))
	} else if news == nil && olds != nil {
		news = make([]*model.TDoc, len(olds))
	}
	if len(olds) != len(news) {
		return errors.New("olds and news slices must have the same length")
	}

	var logs []*model.TDocSync
	var ids []uint64
	for i := 0; i < len(olds); i++ {
		old := olds[i]
		new := news[i]
		if old == nil && new == nil {
			continue
		}
		// 先去除状态为禁用的collection，状态为禁用的无需做判断
		RemoveDocDisabledCollection(old)
		RemoveDocDisabledCollection(new)
		docId := uint64(0)
		if old != nil {
			docId = old.ID
		}
		if new != nil {
			docId = new.ID
			// 如果 data_type字段没有，会影响判断，这里从数据库获取
			if ai.DocType(new.DataType) == ai.DocType_DOCTYPE_UNSPECIFIED {
				err = tx.Model(new).Select("data_type").
					Where("id = ?", new.ID).Pluck("data_type", &new.DataType).Error
				if err != nil {
					return err
				}
			}
		}
		// 插入时，如果新值无 collection 去同步，跳过
		if old == nil && len(new.Collections) == 0 {
			continue
		}
		// 没有使用大模型召回，跳过
		if old == nil && !DocUsedLMRecall(new) {
			continue
		}
		// 删除时，如果旧值为使用大模型召回，不能跳过，因为删除需要依赖向量同步触发
		// if new == nil && !DocUsedLMRecall(old) {
		//
		// }
		// 删除时，如果旧值无 collection，不能跳过，因为删除需要依赖向量同步触发
		// if new == nil && len(old.Collections) == 0 {
		// 	continue
		// }
		if new != nil && old != nil {
			// 没有collection 需要写入数据
			if len(old.Collections) == 0 && len(new.Collections) == 0 {
				continue
			}
			diff := NewDocSyncLogic(ctx, new, old).diff()
			// 没有diff
			if len(diff) == 0 {
				continue
			}
		}
		logs = append(logs, GenSyncLogEntry(ctx, docId, old, new)...)
		ids = append(ids, docId)
	}
	if len(logs) == 0 {
		return
	}
	err = tx.Model(&model.TDocSync{}).Table("t_doc_sync").Create(logs).Error
	if err != nil {
		return
	}
	cntMap := make(map[uint64]uint32)
	for _, v := range logs {
		if v.CollectionID != 0 {
			cntMap[v.DocID] += 1
		}
	}
	err = model.UpdateDocVersion(tx, ids, cntMap)
	if err != nil {
		return
	}

	versionMap := make(map[uint64][]uint64)
	for _, v := range logs {
		versionMap[v.DocID] = append(versionMap[v.DocID], v.CollectionID)
	}
	err = UpdateDocCollectionSyncVersion(tx, versionMap)
	if err != nil {
		return err
	}

	if len(opt) != 0 && opt[0].IsDelete {
		go func() {
			for _, v := range logs {
				if v.New == nil {
					GetDocSyncRagManager(context.WithoutCancel(ctx)).TryDeleteDocWithMerge(ctx, v.DocID, v.CollectionID)
				}
			}
		}()
	}

	if len(logs) > 0 {
		go func() {
			// 粗略 sleep 2秒，等待事务完成
			time.Sleep(time.Second * 2)
			for i := 0; i < len(logs); i++ {
				GetDocSyncRagManager(ctx).NotifyDo(ctx)
			}
		}()
	}

	return
}

// UpdateDocCollectionSyncVersion 更新doc版本号,区分不同的 collection
func UpdateDocCollectionSyncVersion(tx *gorm.DB, versionsMap map[uint64][]uint64, ragVersion ...bool) error {
	var data []*model.TDocSyncVersion

	for docID, collectionIDs := range versionsMap {
		for _, collectionID := range collectionIDs {
			data = append(data, &model.TDocSyncVersion{
				DocID:        docID,
				CollectionID: collectionID,
				DocVersion:   1,
				RagVersion:   0,
			})
		}
	}
	column := "doc_version"
	if len(ragVersion) > 0 && ragVersion[0] {
		column = "rag_version"
	}
	// 执行批量更新
	return tx.Model(&model.TDocSyncVersion{}).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "doc_id"}, {Name: "collection_id"}},
		DoUpdates: clause.Set{{Column: clause.Column{Name: column}, Value: gorm.Expr(column+"+ ?", 1)}},
	}).Create(data).Error
}

// MarkDocDeletedInCollection 如果是删除，在同步之后，将状态表里删除中设置为已删除
func MarkDocDeletedInCollection(tx *gorm.DB, docId uint64, collectionId []uint64) error {
	if len(collectionId) == 0 {
		return nil
	}
	var versions []model.TDocSyncVersion
	err := tx.Model(&model.TDocSyncVersion{}).
		Where("doc_id = ?", docId).Where("collection_id in ?", collectionId).Find(&versions).Error
	if err != nil {
		return err
	}
	var collections []*model.TCollection
	err = tx.Model(&model.TCollection{}).Preload("Assistants").Where("id in ?", collectionId).Find(&collections).Error
	if err != nil {
		return err
	}
	toUpdate := make([]model.TAssistantDoc, 0)
	for _, v := range collectionId {
		mark := false
		for _, version := range versions {
			if version.CollectionID == v && version.DocVersion == version.RagVersion {
				mark = true
				break
			}
		}
		if mark {
			for _, collection := range collections {
				if v == collection.ID {
					for _, assistant := range collection.Assistants {
						toUpdate = append(toUpdate, model.TAssistantDoc{
							DocID:       docId,
							AssistantID: assistant.ID,
						})
					}
				}
			}
		}
	}
	if len(toUpdate) == 0 {
		return nil
	}
	err = tx.Model(toUpdate).Where("state = ?", ai.DocState_DOC_STATE_DELETING).
		UpdateColumn("state", ai.DocState_DOC_STATE_UNBOUNDED).Error
	if err != nil {
		return err
	}
	return nil
}

// TODO：移除
// SyncToDBLog 数据库记录操作，后续异步消费
func (l *DocSyncLogic) SyncToDBLog(tx *gorm.DB) error {
	return SyncDocToDBLogBulk(l.ctx, tx, []*model.TDoc{l.old}, []*model.TDoc{l.new})
}

// Sync 同步collection到向量数据库
func (l *DocSyncLogic) Sync() error {
	if l.old == nil && l.new == nil {
		return nil
	}
	// new为nil代表删除
	if l.new == nil || l.new.ID == 0 {
		return l.deleteRagData(l.ctx, l.old)
	}
	// old为nil代表第一次创建
	if l.old == nil || l.old.ID == 0 {
		return l.upsertRagData(l.ctx, l.new, nil)
	}
	diff := l.diff()
	if len(diff) == 0 {
		return nil
	}

	// TODO: 无需判断状态，因为存的都是启用的状态
	// 状态变化
	// if l.stateChanged(diff) {
	// 	return l.syncState(diff)
	// }
	// 如果没有启用，则跳过rag侧的数据更新
	// if l.new.State != ai.DocState_DOC_STATE_ENABLED {
	// 	return nil
	// }
	err := l.syncCollections(diff)
	if err != nil {
		return err
	}
	err = l.syncIndexText(diff)
	if err != nil {
		return err
	}
	err = l.syncText(diff)
	if err != nil {
		return err
	}
	return nil
}

// TODO: 删除，已经无用
// 判断state是否有变化
func (l *DocSyncLogic) stateChanged(diff map[string][2]interface{}) bool {
	if _, ok := diff["State"]; !ok {
		return false
	}
	return true
}

// TODO: 删除，已经无用
func (l *DocSyncLogic) syncState(diff map[string][2]interface{}) error {
	if v, ok := diff["State"]; ok {
		state := v[1].(ai.DocState)
		// 禁用->启用
		if state == ai.DocState_DOC_STATE_ENABLED {
			return l.upsertRagData(l.ctx, l.new, nil)
		}
		// 启用->禁用
		if state == ai.DocState_DOC_STATE_DISABLED || state == ai.DocState_DOC_STATE_DELETING {
			return l.deleteRagData(l.ctx, l.old)
		}
	}
	return nil
}

func (l *DocSyncLogic) syncIndexText(diff map[string][2]interface{}) error {
	if _, ok := diff["IndexText"]; ok {
		var intersect []*model.TCollection
		for _, v := range l.new.Collections {
			for _, vv := range l.old.Collections {
				if vv.ID == v.ID {
					intersect = append(intersect, vv)
				}
			}
		}
		if len(intersect) == 0 {
			return nil
		}
		toUpsertNew := &model.TDoc{
			ID:          l.new.ID,
			Text:        l.new.Text,
			IndexText:   l.new.IndexText,
			RagFilename: l.new.RagFilename,
			DataType:    l.new.DataType,
			Collections: intersect,
		}
		toUpsertOld := &model.TDoc{
			ID:          l.old.ID,
			Text:        l.old.Text,
			IndexText:   l.old.IndexText,
			RagFilename: l.old.RagFilename,
			DataType:    l.old.DataType,
			Collections: intersect,
		}
		l.upsertedNew = true
		return l.upsertRagData(l.ctx, toUpsertNew, toUpsertOld)
	}
	return nil
}

func (l *DocSyncLogic) syncText(diff map[string][2]interface{}) error {
	if _, ok := diff["Text"]; ok {
		if l.upsertedNew { // index text已经进行了upsert，无需操作
			return nil
		}
		return l.upsertRagData(l.ctx, l.new, nil)
	}
	return nil
}

func (l *DocSyncLogic) syncCollections(diff map[string][2]interface{}) error {
	if v, ok := diff["Collections"]; ok {
		oldc := v[0].([]*model.TCollection)
		newc := v[1].([]*model.TCollection)
		var oldNames, newNames []string
		for _, vv := range oldc {
			oldNames = append(oldNames, vv.Name)
		}
		for _, vv := range newc {
			newNames = append(newNames, vv.Name)
		}

		added, removed := util.DiffSlices[string](oldNames, newNames)
		if len(removed) != 0 {
			toRemove := &model.TDoc{
				ID:          l.old.ID,
				Text:        l.old.Text,
				IndexText:   l.old.IndexText,
				RagFilename: l.old.RagFilename,
				DataType:    l.old.DataType,
			}
			for _, vv := range removed {
				for _, vvv := range l.old.Collections {
					if vv == vvv.Name {
						toRemove.Collections = append(toRemove.Collections, vvv)
					}
				}
			}
			err := l.deleteRagData(l.ctx, toRemove)
			if err != nil {
				return err
			}
		}
		if len(added) != 0 {
			toAdd := &model.TDoc{
				ID:          l.old.ID,
				Text:        l.old.Text,
				IndexText:   l.old.IndexText,
				RagFilename: l.old.RagFilename,
				DataType:    l.old.DataType,
			}
			for _, vv := range added {
				for _, vvv := range l.new.Collections {
					if vv == vvv.Name {
						toAdd.Collections = append(toAdd.Collections, vvv)
					}
				}
			}
			err := l.upsertRagData(l.ctx, toAdd, nil)
			if err != nil {
				return err
			}
		}

	}
	return nil
}

func (l *DocSyncLogic) diff() map[string][2]interface{} {
	// 排序，可以使collection比较时相等，减少logEntry写入
	if l.old != nil {
		slices.SortFunc(l.old.Collections, func(a, b *model.TCollection) int {
			return int(a.ID - b.ID)
		})
	}
	if l.new != nil {
		slices.SortFunc(l.new.Collections, func(a, b *model.TCollection) int {
			return int(a.ID - b.ID)
		})
	}

	changes := make(map[string][2]interface{})
	oldVal := reflect.ValueOf(l.old).Elem()
	newVal := reflect.ValueOf(l.new).Elem()
	typ := oldVal.Type()

	// 固定字段diff
	for i := 0; i < oldVal.NumField(); i++ {
		field := typ.Field(i)
		if xslice.ContainsString(docDiffFields, field.Name) != -1 {
			oldField := oldVal.Field(i)
			newField := newVal.Field(i)

			// 忽略零值字段
			// if newField.IsZero() {
			// 	newField.Set(oldField)
			// 	continue
			// }

			// 是否使用大模型，只需要判断是否开启
			if field.Name == "MatchPatterns" {
				oldLmUsed := DocUsedLMRecall(l.old)
				newLmUsed := DocUsedLMRecall(l.new)
				if oldLmUsed != newLmUsed {
					changes["MatchPatterns"] = [2]interface{}{oldLmUsed, newLmUsed}
				}
				continue
			}
			// 通用deep equal
			if !reflect.DeepEqual(oldField.Interface(), newField.Interface()) {
				changes[field.Name] = [2]interface{}{oldField.Interface(), newField.Interface()}
			}
		}
	}
	return changes
}

// upsertRagQa 同步QA到向量数据库
func (l *DocSyncLogic) upsertRagQa(ctx context.Context, new *model.TDoc) error {
	text, indexText := MarshalDocToRag(new)
	if len(text) == 0 {
		log.WithContext(ctx).Warnf("upsertRagData with empty text, will ignore. doc id: %d", new.ID)
		return nil
	}
	for _, v := range new.Collections {
	CollectionCreated:
		req := &rag.ReqEditCollection{
			CollectionName: v.RagName,
			Text:           text,
			IndexContent:   indexText,
			VdbType:        v.VdbType,
			DocName:        new.RagFilename,
			Threshold:      GetCollectionEditThreshold(),
			Lang:           v.Lang,
			EsIns:          v.EsIns,
		}
		start := time.Now()
		_, err := client.GetRagClient().EditCollection(ctx, req)
		var cerr *ragcommon.Error
		if errors.As(err, &cerr) && cerr.Code == strconv.Itoa(ragcommon.ErrCollectionNotExisted) {
			err = EnsureCollectionCreated(ctx, v)
			if err != nil {
				return err
			}
			goto CollectionCreated
		}
		elapsed := time.Since(start)
		if err != nil {
			return fmt.Errorf("edit collection err, %w", err)
		}
		log.WithContext(ctx).Infow("edit collection success", "docId", new.ID,
			"collectionId", v.ID, "cost(ms)", elapsed.Milliseconds())
	}
	return nil
}

func (l *DocSyncLogic) upsertRagTextFile(ctx context.Context, new *model.TDoc) error {
	if len(new.IndexText) == 0 {
		return nil
	}
	docFileType, err := GetDocFileType(ctx, new)
	if err != nil {
		return fmt.Errorf("judge doc file type err, %w", err)
	}

	chunkConfigs, collectionChunks, err := l.queryDocChunksAndConfigs(ctx, new)
	if err != nil {
		return err
	}

	for _, v := range new.Collections {
		// 内容中的表格，会从自动分段和已有分段中提取
		var tablesToEditCollection []tablechunk.Table
		// 在 create_collection 中需传入的 TextChunks
		var textChunksToCreateCollection []string
		// 如果文档已经存在分段详情了，就直接使用历史分段
		// 否则自动分段
		var isAutoChunk bool
		// 提取表格后的文本
		var textWithOutTables string
		forceNoChunk := slices.Contains(config.GetIntSlice("llm.collection.force_no_chunk"), int(v.ID))
		if chunks := collectionChunks[v.ID]; chunks != nil {
			for _, chunk := range chunks {
				tables, _ := tablechunk.ExtractMarkdownTables(chunk)
				if len(tables) != 0 {
					// 取第一个表格的index_content，走表格逻辑
					tablesToEditCollection = append(tablesToEditCollection, tablechunk.Table{
						TitledContent: chunk, // 直接使用整个 chunk 文本
						Title:         tables[0].Title,
						Header:        tables[0].Header,
					})
				} else {
					// 不走表格逻辑
					textChunksToCreateCollection = append(textChunksToCreateCollection, chunk)
				}
			}
		} else {
			if forceNoChunk {
				isAutoChunk = false
				// 直接用文本，不分段
				textChunksToCreateCollection = append(textChunksToCreateCollection, new.IndexText)
			} else {
				isAutoChunk = true
				// 从原始文本中提取表格
				tbls, newIndexText := tablechunk.ExtractMarkdownTablesWithOption(ctx, new.IndexText,
					table.TableOption{IsExcel: docFileType == DocFileTypeXlsx})
				tablesToEditCollection = append(tablesToEditCollection, tbls...)
				textWithOutTables = newIndexText
			}
		}

		// 无表格部分: 使用 create_collection
		var sessionId string
		var chunkCount int
		createCollection := func() error {
			// 如果提取表格后的分段空（全部分段都是表格）
			// 且提取表格后的文本为空
			// 则直接返回（因为表格部分会 edit_collection）
			if len(textChunksToCreateCollection) == 0 && len(textWithOutTables) == 0 {
				return nil
			}
			req := &rag.ReqCreateCollectionAsync{
				CollectionName: v.RagName,
				Lang:           v.Lang,
				// ReturnIds:      true,
				EsIns: v.EsIns,
			}
			// 如果文档已经存在分段详情了，就直接使用历史分段
			// 否则自动分段
			if !isAutoChunk {
				req.TextChunks = textChunksToCreateCollection
				req.FileName = new.RagFilename
			} else {
				bak := new.IndexText
				new.IndexText = textWithOutTables
				fm := NewDocFileManager(ctx, new)
				f, err := fm.GenerateRagFileFromDoc()
				new.IndexText = bak
				defer fm.Clean()
				if err != nil {
					return err
				}

				minChunkSize, maxChunkSize, overlapSize := model.CalcDocChunkPara(v.Lang, chunkConfigs[v.ID])
				req.Files = []string{f}
				req.ChunkMinSize = minChunkSize
				req.ChunkSize = maxChunkSize
				req.ChunkOverlapSize = overlapSize
			}
			maxChunk := CollectionForceChunkSize(ctx, v)
			if maxChunk != 0 {
				req.ChunkMinSize = maxChunk
				req.ChunkSize = maxChunk
			}
			if docFileType == DocFileTypeXlsx {
				req.ChunkMinSize = config.GetIntOr("llm.collection.xlsx_chunk_min_size", 8192)
				req.ChunkSize = config.GetIntOr("llm.collection.xlsx_chunk_size", 8192)
			}
			rsp, err := client.GetRagClient().CreateCollectionAsync(ctx, req)
			if err != nil {
				return fmt.Errorf("create collection err, %w", err)
			}
			sessionId = rsp.SessionId
			time.Sleep(time.Second * 2)
			// 查询进度最多错误20次
			maxFail := 20
			for {
				if ctx.Err() != nil {
					return fmt.Errorf("get create collection progress timeout: %w", ctx.Err())
				}
				progress := &rag.RespCollectionCreateProgress{}
				progress, err = client.GetRagClient().CollectionCreateProgress(ctx, &rag.ReqCollectionCreateProgress{SessionId: sessionId})
				if err != nil {
					if errors.Is(err, rag.ErrCreateCollectionFailed) {
						return err
					}
					maxFail--
					if maxFail < 0 {
						return fmt.Errorf("get create collection progress err, %w", err)
					}
				}
				// 创建完成
				if progress.Progress.CleanProgress == 1 && progress.Progress.UpsertProgress == 1.0 {
					chunkCount = progress.Progress.ChunkCount
					break
				}
				time.Sleep(time.Second * 5)
			}
			return nil
		}

		// 表格部分: 使用 edit_collection
		editCollection := func(tables []tablechunk.Table) error {
			for _, t := range tables {
				req := &rag.ReqEditCollection{
					CollectionName: v.RagName,
					VdbType:        v.VdbType,
					Threshold:      1.0,
					Lang:           v.Lang,
					EsIns:          v.EsIns,
					DocName:        new.RagFilename,
					Text:           t.TitledContent,
					IndexContent:   t.GenEditCollectionIndexContent(),
				}
				_, err = client.GetRagClient().EditCollection(ctx, req)
				if err != nil {
					return err
				}
			}
			return nil
		}

		// NOTE: 必须先执行create_collection，因为 create_collection会清空已存在的向量数据
		// 1. 执行 create_collection部分
		ctx1, cancel := context.WithTimeout(ctx, config.GetDurationOr("llm.collection.create_timeout", time.Minute*30))
		start := time.Now()
		err = util.RunWithCancel(ctx1, createCollection)
		if err != nil {
			cancel()
			log.WithContext(ctx1).Infow("upsertRagTextFile create collection failed", "docId", new.ID,
				"collectionId", v.ID, "err", err.Error())
			return err
		}
		elapsed := time.Since(start)
		report := &CollectionMetric{
			CommonFetchMetric: metric.CommonFetchMetric{
				Elapsed: elapsed.Milliseconds(),
			},
			CollectionId: v.ID,
			DocId:        new.ID,
			SessionId:    sessionId,
		}
		metric.ContextWithApiIdentity(ctx, &basepb.Identity{
			IdentityId:   new.CreateBy,
			IdentityType: new.CreateByType,
		})
		metric.Create(ctx, CreateCollectionMetricType, report, metric.WithIdentity(ctx))

		log.WithContext(ctx).Infow("create collection success", "docId", new.ID,
			"collectionId", v.ID, "cost(ms)", elapsed.Milliseconds())
		cancel()

		// 2. 执行 edit_collection部分,同步表格
		ctx1, cancel = context.WithTimeout(ctx, config.GetDurationOr("llm.collection.edit_timeout", time.Minute*30))
		err := util.RunWithCancel(ctx1, func() error {
			return editCollection(tablesToEditCollection)
		})
		if err != nil {
			cancel()
			log.WithContext(ctx1).Infow("upsertRagTextFile edit collection failed", "docId", new.ID,
				"collectionId", v.ID, "err", err.Error())
			return err
		}
		cancel()

		// 指定了不分片时，也要更新分段详情
		if !isAutoChunk && forceNoChunk {
			isAutoChunk = true
		}
		// 更新分段详情
		if isAutoChunk && len(v.Assistants) > 0 {
			if delay := config.GetDurationOr("llm.collection.createDocChunksDelay", 5*time.Second); delay > 0 {
				time.Sleep(delay)
			}

			maxTries := config.GetIntOr("llm.collection.createDocChunksMaxTries", 5)
			retryDelay := config.GetDurationOr("llm.collection.createDocChunksRetryDelay", time.Minute)
			return (&docchunklogic.ChunkDocLogic{}).CreateDocChunks(ctx, new, v, v.Assistants[0], chunkCount, maxTries, retryDelay)
		}
	}
	return nil
}

// upsertRagData
// case 1. 在搜索服务创建QA，或者更新answer，或者纯文本
// case 2: 在搜索服务创建文档、更新文档
// 默认new和old的collection相同
func (l *DocSyncLogic) upsertRagData(ctx context.Context, new *model.TDoc, old *model.TDoc) error {
	if old != nil {
		err := l.deleteRagData(ctx, old)
		if err != nil {
			return err
		}
	}

	switch ai.DocType(l.getDocType()) {
	case ai.DocType_DOCTYPE_FILE, ai.DocType_DOCTYPE_TEXT:
		return l.upsertRagTextFile(ctx, new)
	case ai.DocType_DOCTYPE_QA:
		return l.upsertRagQa(ctx, new)
	}
	return nil
}

// deleteRagData 删除文档的向量数据
func (l *DocSyncLogic) deleteRagData(ctx context.Context, doc *model.TDoc) error {
	if doc == nil || len(doc.IndexText) == 0 {
		return nil
	}
	for _, v := range doc.Collections {
		var ids []string
		var err error

		// 从 rag 接口获取全量的向量id，然后删除
		rsp, err := client.GetRagClient().GetCollection(ctx, &rag.ReqGetCollection{
			CollectionName: v.RagName,
			FileName:       doc.RagFilename,
			VdbType:        v.VdbType,
			EsIns:          v.EsIns,
		})
		if err != nil {
			return err
		}
		for _, chunk := range rsp.Data.Chunks {
			ids = append(ids, chunk.Id)
		}
		if len(ids) != 0 {
			_, err = client.GetRagClient().DeleteVecById(ctx, &rag.ReqDeleteVecById{
				CollectionName: v.RagName,
				VecIds:         ids,
				EsIns:          v.EsIns,
			})
			if err != nil {
				return fmt.Errorf("delete vec by id err, %w", err)
			}
			err = model.DeleteDocEmbeddingSegments(ctx, doc.ID, v.ID)
			if err != nil {
				log.WithContext(ctx).Errorf("delete doc embedding segment failed, docId:%d, collectionId:%d ,error:%v", doc.ID, v.ID, err)
			}
		}
	}
	return nil
}

// EnsureCollectionCreated 确保 collection 已经创建好
func EnsureCollectionCreated(ctx context.Context, collection *model.TCollection) error {
	tmpf, err := os.CreateTemp("", "*.txt")
	if err != nil {
		return err
	}
	tmpf.WriteString("no meaning data")
	tmpf.Close()
	defer os.Remove(tmpf.Name())
	rsp, err := client.GetRagClient().CreateCollection(ctx, &rag.ReqCreateCollection{
		CollectionName: collection.RagName,
		Lang:           collection.Lang,
		Files:          []string{tmpf.Name()},
		ReturnIds:      true,
		EsIns:          collection.EsIns,
	})
	if err != nil {
		return err
	}
	for i := 0; i < 10; i++ {
		_, err = client.GetRagClient().DeleteVecById(ctx, &rag.ReqDeleteVecById{
			CollectionName: collection.RagName, VecIds: rsp.Ids, EsIns: collection.EsIns,
		})
		if err != nil {
			time.Sleep(time.Millisecond * 100)
			continue
		}
		return nil
	}
	return nil
}

type QA struct {
	Question string
	Answer   string
}

// MarshalDocToRag 将doc转化成rag侧的格式
func MarshalDocToRag(doc *model.TDoc) (text string, indexText string) {
	switch doc.DataType {
	case uint32(ai.DocType_DOCTYPE_TEXT):
		text = doc.IndexText
		indexText = doc.IndexText
	case uint32(ai.DocType_DOCTYPE_QA):
		// qa不拼接 【gemini 2.0，chat回答里会有json】
		// https://tapd.tencent.com/tapd_fe/69992984/bug/detail/1069992984138873065
		// qa := &QA{
		// 	Question: doc.IndexText,
		// 	Answer:   doc.Text,
		// }
		// marshal, err := json.Marshal(qa)
		// if err != nil {
		// 	return "", ""
		// }
		text = doc.Text
		indexText = doc.IndexText
	}
	return
}

// IsDocSynced 判断 doc 是否已经同步完成
func IsDocSynced(ctx context.Context, tx *gorm.DB, docId []uint64, scopedAssistantId ...uint64) (map[uint64]bool, error) {
	synced := make(map[uint64]bool)
	// 没有指定助手，直接从全局表里面拿同步数据
	if len(scopedAssistantId) == 0 {
		docs := make([]*model.TDoc, 0)
		err := tx.Model(&model.TDoc{}).Select("id, version, rag_version").Where("id in ?", docId).Find(&docs).Error
		if err != nil {
			return nil, err
		}
		for _, doc := range docs {
			synced[doc.ID] = doc.Version == doc.RagVersion
		}
	} else {
		// 指定了助手，从 doc_sync_version表，获取同步状态
		var err error
		docs := make([]*model.TDoc, 0)
		err = tx.Model(&model.TDoc{}).Where("id in ?", docId).Select("id").
			Preload("SyncVersions").Preload("Assistants", func(db *gorm.DB) *gorm.DB {
			db.Scopes(model.AssistantWithCollection)
			db.Where("id in (?)", scopedAssistantId)
			return db
		}).Find(&docs).Error
		if err != nil {
			return nil, err
		}
		for _, doc := range docs {
			// 默认设置为 true
			synced[doc.ID] = true
			mp := doc.GenEmbeddingVersionMap()
			for _, v := range mp {
				if v != 0 {
					synced[doc.ID] = false
				}
			}
		}
	}
	return synced, nil
}

// 查询文档的历史分段和配置
func (l *DocSyncLogic) queryDocChunksAndConfigs(ctx context.Context, doc *model.TDoc) (map[uint64]*ai.AssistantChunkConfig, map[uint64][]string, error) {
	collectionMap := make(map[uint64]*model.TCollection)
	collectionIDs := make([]uint64, 0, len(doc.Collections))
	for _, collection := range doc.Collections {
		if collection != nil && collection.ID > 0 {
			collectionIDs = append(collectionIDs, collection.ID)
			collectionMap[collection.ID] = collection
		}
	}
	if len(collectionIDs) == 0 {
		return nil, nil, nil
	}

	// 查询配置
	assistantToCollection := make(map[uint64]uint64, len(doc.Collections))
	assistantIDs := make([]uint64, 0, len(doc.Collections))
	acs, err := model.NewQuery[model.TAssistantCollection](ctx).GetBy("collection_id in ?", collectionIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("query chunk config: %w", err)
	}
	configs := make(map[uint64]*ai.AssistantChunkConfig, len(acs))
	for _, ac := range acs {
		assistantIDs = append(assistantIDs, ac.AssistantID)
		configs[ac.CollectionID] = ac.ChunkConfig
		assistantToCollection[ac.AssistantID] = ac.CollectionID
		// 填充一个助手信息，避免重复查询
		if collection, ok := collectionMap[ac.CollectionID]; ok {
			collection.Assistants = []*model.TAssistant{{ID: ac.AssistantID}}
		}
	}

	// 查询分段
	ads, err := model.NewQuery[model.TAssistantDoc](ctx).
		With("ChunkDetail").GetBy("doc_id = ? AND assistant_id in ?", doc.ID, assistantIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("query assistant doc: %w", err)
	}
	sumToChunks := make(map[string][]string, len(doc.Collections))
	collectionChunks := make(map[uint64][]string, len(ads))
	for _, ad := range ads {
		if ad.ChunkDetail == nil {
			continue
		}
		collectionID := assistantToCollection[ad.AssistantID]
		chunks := sumToChunks[ad.ChunkDetail.Sum]
		if chunks == nil {
			chunks = ad.ChunkDetail.ChunkDocThenPluckContents(doc.IndexText)
			sumToChunks[ad.ChunkDetail.Sum] = chunks
		}
		collectionChunks[collectionID] = chunks
	}

	return configs, collectionChunks, nil
}

// CollectionForceChunkSize 判断collection是否用指定的chunk_size去写入
// 临时需求加的特殊逻辑，先从配置读取
func CollectionForceChunkSize(ctx context.Context, collection *model.TCollection) int {
	cfgName := "llm.collection.sync_force_chunk_size." + collection.RagName
	force := config.GetInt(cfgName)
	return force
}
