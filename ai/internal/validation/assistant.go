package validation

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	validatorv10 "github.com/go-playground/validator/v10"
)

var assistantStructFields = map[string]bool{
	model.TAssistantColumns.VisibleChainConfig:     true,
	model.TAssistantColumns.FieldManageConfig:      true,
	model.TAssistantColumns.ChatOrSqlConfig:        true,
	model.TAssistantColumns.AskSuggestionConfig:    true,
	model.TAssistantColumns.WeixinDevelopConfig:    true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.KefuConfig:             true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.WebsiteConfig:          true,
	model.TAssistantColumns.MiniprogramConfig:      true,
	model.TAssistantColumns.GraphParseConfig:       true,
	model.TAssistantColumns.WhatsappDevelopConfig:  true,
	model.TAssistantColumns.QuestionTypeConfig:     true,
	model.TAssistantColumns.AllowlistConfig:        true,
	model.TAssistantCollectionColumns.ChunkConfig:  true,
}

var assistantSliceFields = map[string]bool{
	"admins":                                true,
	model.TAssistantColumns.SystemLanguages: true,
}

// NewAssistantFilterFunc 新建助手校验器过滤函数
func NewAssistantFilterFunc(mask []string, channel aipb.AssistantChannel) validatorv10.FilterFunc {
	var (
		staticFields = make(map[string]bool)
		wildFields   []string
	)

	for _, modelField := range mask {
		structField := formatAssistantFieldName(xstrings.Pascal(modelField), channel)
		if structField == "" {
			continue
		}

		staticFields[structField] = true

		if assistantStructFields[modelField] {
			wildFields = append(wildFields, structField+".")
		} else if assistantSliceFields[modelField] {
			wildFields = append(wildFields, structField+"[")
		}
	}

	return func(fieldBytes []byte) bool {
		field := xstrings.FromBytes(fieldBytes)

		if staticFields[field] {
			return false
		}

		for _, prefix := range wildFields {
			if strings.HasPrefix(field, prefix) {
				return false
			}
		}

		return true
	}
}

func formatAssistantFieldName(field string, channel aipb.AssistantChannel) string {
	var exists bool

	if _, exists = assistantConfigRules[field]; exists {
		return "AssistantConfig." + field
	}

	switch channel {
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
		aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN:
		if _, exists = assistantWeixinChannelConfigRules[field]; exists {
			return "WeixinChannelConfig." + field
		}
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB:
		if _, exists = assistantTanliveWebChannelConfigRules[field]; exists {
			return "TanliveWebChannelConfig." + field
		}
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP:
		if _, exists = assistantTanliveAppChannelConfigRules[field]; exists {
			return "TanliveAppChannelConfig." + field
		}
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP:
		if _, exists = assistantWhatsappChannelConfigRules[field]; exists {
			return "WhatsappChannelConfig." + field
		}
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM:
		if _, exists = assistantMiniprogramChannelConfigRules[field]; exists {
			return "MiniprogramChannelConfig." + field
		}
	}

	return ""
}

func initAssistantRules(v *validatorv10.Validate) {
	v.RegisterValidation("assistant_chat_model", dynamicOneof(pluckAssistantChatModelFields))
	v.RegisterValidation("assistant_visible_chain_field", dynamicOneof(pluckAssistantVisibleChainFields))
	v.RegisterValidation("assistant_embedding_model", dynamicOneof(pluckAssistantEmbeddingModels))
	v.RegisterValidation("assistant_readable_field", dynamicOneof(getStaticStringSlice(model.AssistantReadableInConsole)))
	v.RegisterValidation("assistant_writeable_field", dynamicOneof(getStaticStringSlice(model.AssistantWritableInConsole)))
	v.RegisterValidation("assistant_chatorsql_model", dynamicOneof(getStringSliceConfig("assistant.chatOrSqlModel")))
	v.RegisterValidation("assistant_graph_parse_model", dynamicOneof(getStringSliceConfig("assistant.graphParseModel")))
	v.RegisterValidation("assistant_search_engine", dynamicOneof(getStringSliceConfig("assistant.searchEngine")))
	v.RegisterValidation("assistant_question_type", dynamicOneof(getStringSliceConfig("assistant.questionType")))
	v.RegisterValidation("assistant_name_en", assistantNameEn)
	v.RegisterValidation("assistant_route_path", assistantRoutePath)

	v.RegisterStructValidationMapRules(assistantConfigRules, &aipb.AssistantConfig{})
	v.RegisterStructValidationMapRules(assistantWeixinChannelConfigRules, &aipb.AssistantWeixinChannelConfig{})
	v.RegisterStructValidationMapRules(assistantTanliveWebChannelConfigRules, &aipb.AssistantTanliveWebChannelConfig{})
	v.RegisterStructValidationMapRules(assistantTanliveAppChannelConfigRules, &aipb.AssistantTanliveAppChannelConfig{})
	v.RegisterStructValidationMapRules(assistantWhatsappChannelConfigRules, &aipb.AssistantWhatsappChannelConfig{})
	v.RegisterStructValidationMapRules(assistantMiniprogramChannelConfigRules, &aipb.AssistantMiniprogramChannelConfig{})
	v.RegisterStructValidationMapRules(assistantVisibleChainConfigRules, &aipb.AssistantVisibleChainConfig{})
	v.RegisterStructValidationMapRules(assistantFieldManageConfigRules, &aipb.AssistantFieldManageConfig{})
	v.RegisterStructValidationMapRules(assistantWelcomeMessageConfigRules, &aipb.AssistantWelcomeMessageConfig{})
	v.RegisterStructValidationMapRules(assistantWelcomeMessageRules, &aipb.AssistantWelcomeMessage{})
	v.RegisterStructValidationMapRules(assistantPresetQuestionConfigRules, &aipb.AssistantPresetQuestionConfig{})
	v.RegisterStructValidationMapRules(assistantPresetQuestionRules, &aipb.AssistantPresetQuestion{})
	v.RegisterStructValidationMapRules(assistantRatingScaleReplyConfigRules, &aipb.AssistantRatingScaleReplyConfig{})
	v.RegisterStructValidationMapRules(assistantRatingScaleReplyRules, &aipb.AssistantRatingScaleReply{})
	v.RegisterStructValidationMapRules(assistantInteractiveCodeConfigRules, &aipb.AssistantInteractiveCodeConfig{})
	v.RegisterStructValidationMapRules(assistantInteractiveCodeRules, &aipb.AssistantInteractiveCode{})
	v.RegisterStructValidationMapRules(assistantGraphParseRules, &aipb.AssistantGraphParseConfig{})
	v.RegisterStructValidationMapRules(assistantChatOrSqlConfigRules, &aipb.AssistantChatOrSqlConfig{})
	v.RegisterStructValidationMapRules(assistantAskSuggestionConfigRules, &aipb.AssistantAskSuggestionConfig{})
	v.RegisterStructValidationMapRules(assistantWebsiteConfigRules, &aipb.AssistantWebsiteConfig{})
	v.RegisterStructValidationMapRules(assistantMiniprogramConfigRules, &aipb.AssistantMiniprogramConfig{})
	v.RegisterStructValidationMapRules(assistantKefuConfigRules, &aipb.AssistantKefuConfig{})
	v.RegisterStructValidationMapRules(assistantKefuStaffRules, &aipb.AssistantKefuStaff{})
	v.RegisterStructValidationMapRules(assistantWeixinDevelopConfigRules, &aipb.AssistantWeixinDevelopConfig{})
	v.RegisterStructValidationMapRules(assistantWhatsappDevelopConfigRules, &aipb.AssistantWhatsappDevelopConfig{})
	v.RegisterStructValidationMapRules(assistantQuestionTypeConfigRules, &aipb.AssistantQuestionTypeConfig{})
	v.RegisterStructValidationMapRules(assistantAllowlistConfigRules, &aipb.AssistantAllowlistConfig{})
	v.RegisterStructValidationMapRules(assistantChunkConfigRules, &aipb.AssistantChunkConfig{})
}

func pluckAssistantChatModelFields() []string {
	var options []*aipb.ChatModelOption
	if err := config.Unmarshal("assistant.chatModelV2", &options); err != nil {
		panic(err)
	}

	fields := make([]string, 0, len(options))
	for _, option := range options {
		fields = append(fields, option.Model)
	}
	return fields
}

func pluckAssistantVisibleChainFields() []string {
	var options []*aipb.VisibleChainOption
	if err := config.Unmarshal("assistant.visibleChainOption", &options); err != nil {
		panic(err)
	}

	fields := make([]string, 0, len(options))
	for _, option := range options {
		fields = append(fields, option.Field)
	}
	return fields
}

func pluckAssistantEmbeddingModels() []string {
	var options []*aipb.EmbeddingModelOption
	if err := config.Unmarshal("assistant.embeddingModel", &options); err != nil {
		panic(err)
	}

	fields := make([]string, 0, len(options))
	for _, option := range options {
		fields = append(fields, option.Model)
	}
	return fields
}

var assistantVisibleChainConfigRules = map[string]string{
	"Visible": "required_if=Enabled true,dive,required",
}

var assistantFieldManageConfigRules = map[string]string{
	"Readable": "omitempty,dive,required,assistant_readable_field",
	"Write":    "omitempty,dive,required,assistant_writeable_field",
}

var assistantWelcomeMessageConfigRules = map[string]string{
	"Messages": "required,dive,required",
}

var assistantWelcomeMessageRules = map[string]string{
	"Lang":    "required,ugc_lang",
	"Content": "required",
}

var assistantPresetQuestionConfigRules = map[string]string{
	"Questions": "required_if=Enabled true,omitempty,dive,required",
}

var assistantPresetQuestionRules = map[string]string{
	"Lang":    "required,ugc_lang",
	"Content": "required",
}

var assistantRatingScaleReplyConfigRules = map[string]string{
	"Replies": "required_if=Enabled true,omitempty,dive,required",
}

var assistantRatingScaleReplyRules = map[string]string{
	"Lang":        "required,ugc_lang",
	"RatingScale": "required",
	"Content":     "required",
}

var assistantInteractiveCodeConfigRules = map[string]string{
	"Codes": "required,dive,required",
}

var assistantInteractiveCodeRules = map[string]string{
	"Lang":            "required,ugc_lang",
	"InteractiveCode": "required",
	"Content":         "required",
}

var assistantGraphParseRules = map[string]string{
	"Model": "required_if=Enabled true,omitempty,assistant_graph_parse_model",
}

var assistantChatOrSqlConfigRules = map[string]string{
	"Model": "required_if=Enabled true,omitempty,assistant_chatorsql_model",
}

var assistantAskSuggestionConfigRules = map[string]string{
	"Prompt": "required_if=Enabled true,omitempty,max=8000",
	"Count":  "required_if=Enabled true,omitempty,min=1,max=9",
	"Times":  "required_if=Mode 2,omitempty,min=1,max=100",
}

var assistantWebsiteConfigRules = map[string]string{
	"RoutePath": "required,assistant_route_path",
	"Title":     "required",
}

var assistantMiniprogramConfigRules = map[string]string{
	"ShareTitle": "required",
}

var assistantKefuConfigRules = map[string]string{
	"Staffs": "omitempty,dive,required",
}

var assistantKefuStaffRules = map[string]string{
	"Username": "required",
	"Nickname": "required",
}

var assistantWeixinDevelopConfigRules = map[string]string{
	"CorpId":   "required",
	"OpenKfid": "required",
	"KfUrl":    "required",
}

var assistantWhatsappDevelopConfigRules = map[string]string{
	"BusinessNumber": "required",
}

var assistantChunkConfigRules = map[string]string{
	"MinCharLang": "required,ugc_lang",
	"MaxCharLang": "required,ugc_lang",
	"OverlapLang": "required,ugc_lang",
}

var assistantQuestionTypeConfigRules = map[string]string{
	"ChatModel":    "required_if=Enabled true,omitempty,assistant_chat_model",
	"SimpleModel":  "required_if=Enabled true,omitempty,assistant_chat_model",
	"ComplexModel": "required_if=Enabled true,omitempty,assistant_chat_model",
	"Prompt":       "required_if=Enabled true,omitempty,max=8000",
}

var assistantAllowlistConfigRules = map[string]string{
	"Type":   "required_if=Enabled true",
	"Phones": "omitempty,dive,required,phone",
}

var assistantConfigRules = map[string]string{
	"VisibleChainConfig":       "required",
	"FieldManageConfig":        "required",
	"Name":                     "required,ugc_between=2-30",
	"NameEn":                   "required,ugc_between=2-30,assistant_name_en",
	"Channel":                  "required",
	"Admins":                   "required,dive,required",
	"CollectionLang":           "required,assistant_embedding_model",
	"PromptPrefix":             "required,max=8000",
	"Model":                    "required,assistant_chat_model",
	"Threshold":                "min=0,max=1",
	"HistoryRounds":            "required,min=1,max=50",
	"SearchEngine":             "required_if=CloseSearch false,omitempty,assistant_search_engine",
	"DocTopN":                  "required,min=1,max=100",
	"SearchTopN":               "required,min=1,max=100",
	"ChunkConfig":              "required",
	"ChatOrSqlConfig":          "required",
	"AskSuggestionConfig":      "required",
	"TextWeight":               "omitempty,min=0,max=1",
	"MissReply":                "required_if=CloseSearch true",
	"BriefIntro":               "omitempty,max=200",
	"DetailIntro":              "omitempty,max=1024",
	"TextRecallTopN":           "omitempty,min=0",
	"TextRecallSlop":           "omitempty,min=0",
	"Temperature":              "omitempty,min=0,max=2",
	"QuestionTypeConfig":       "omitempty",
	"AllowlistConfig":          "omitempty",
	"WeixinChannelConfig":      "required_if=Channel 1,required_if=Channel 5",
	"TanliveWebChannelConfig":  "required_if=Channel 2",
	"TanliveAppChannelConfig":  "required_if=Channel 3",
	"WhatsappChannelConfig":    "required_if=Channel 4",
	"MiniprogramChannelConfig": "required_if=Channel 6",
}

var assistantWeixinChannelConfigRules = map[string]string{
	"Nickname":               "required,ugc_between=2-30",
	"AvatarUrl":              "omitempty",
	"SystemLang":             "required,ugc_lang",
	"WeixinDevelopConfig":    "required",
	"WelcomeMessageConfig":   "required",
	"PresetQuestionConfig":   "required",
	"KefuConfig":             "required",
	"RatingScaleReplyConfig": "required",
	"InteractiveCodeConfig":  "required",
	"ChatIdleDuration":       "required",
	//"SystemLanguages":        "required,dive,required,ugc_lang",
}

var assistantTanliveWebChannelConfigRules = map[string]string{
	"Nickname":               "required,ugc_between=2-30",
	"NicknameEn":             "required,ugc_between=2-30",
	"AvatarUrl":              "omitempty",
	"AssistantLang":          "required,ugc_lang",
	"WebsiteConfig":          "required",
	"WelcomeMessageConfig":   "required",
	"PresetQuestionConfig":   "required",
	"RatingScaleReplyConfig": "required",
	"InteractiveCodeConfig":  "required",
	"GraphParseConfig":       "-",
	//"SystemLanguages":        "required,dive,required,ugc_lang",
}

var assistantTanliveAppChannelConfigRules = map[string]string{
	"Nickname":               "required,ugc_between=2-30",
	"NicknameEn":             "required,ugc_between=2-30",
	"AvatarUrl":              "omitempty",
	"AssistantLang":          "required,ugc_lang",
	"WelcomeMessageConfig":   "required",
	"PresetQuestionConfig":   "required",
	"RatingScaleReplyConfig": "required",
	"InteractiveCodeConfig":  "required",
	"GraphParseConfig":       "required",
	//"SystemLanguages":        "required,dive,required,ugc_lang",
}

var assistantWhatsappChannelConfigRules = map[string]string{
	"SystemLang":             "required,ugc_lang",
	"WhatsappDevelopConfig":  "required",
	"WelcomeMessageConfig":   "required",
	"PresetQuestionConfig":   "required",
	"RatingScaleReplyConfig": "required",
	"ChatIdleDuration":       "required",
	//"SystemLanguages":        "required,dive,required,ugc_lang",
}

var assistantMiniprogramChannelConfigRules = map[string]string{
	"Nickname":               "required,ugc_between=2-30",
	"AvatarUrl":              "omitempty",
	"AssistantLang":          "required,ugc_lang",
	"MiniprogramConfig":      "required",
	"WelcomeMessageConfig":   "required",
	"PresetQuestionConfig":   "required",
	"RatingScaleReplyConfig": "required",
	"InteractiveCodeConfig":  "required",
	"GraphParseConfig":       "-",
	//"SystemLanguages":        "required,dive,required,ugc_lang",
}

var (
	assistantNameEnRegex    = regexp.MustCompile(`^[a-zA-Z0-9_\- ]+$`)
	assistantRoutePathRegex = regexp.MustCompile(`^[a-zA-Z0-9_\-]{2,30}$`)
)

func assistantNameEn(fl validatorv10.FieldLevel) bool {
	return assistantNameEnRegex.MatchString(fl.Field().String())
}

func assistantRoutePath(fl validatorv10.FieldLevel) bool {
	return assistantRoutePathRegex.MatchString(fl.Field().String())
}

func dynamicOneof(getVals func() []string) validatorv10.Func {
	return func(fl validatorv10.FieldLevel) bool {
		vals := getVals()
		field := fl.Field()

		var v string
		switch field.Kind() {
		case reflect.String:
			v = field.String()
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			v = strconv.FormatInt(field.Int(), 10)
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			v = strconv.FormatUint(field.Uint(), 10)
		default:
			panic(fmt.Sprintf("Bad field type %T", field.Interface()))
		}
		for i := 0; i < len(vals); i++ {
			if vals[i] == v {
				return true
			}
		}
		return false
	}
}

func getStaticStringSlice(vals []string) func() []string {
	return func() []string {
		return vals
	}
}

func getStringSliceConfig(key string) func() []string {
	return func() []string {
		return config.GetStringSlice(key)
	}
}
