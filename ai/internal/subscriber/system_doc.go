package subscriber

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
)

// OnSystemDocChanged 系统数据文档变化
func (*Ai) OnSystemDocChanged(ctx context.Context, event *eventspb.SystemDocChanged) error {
	log.WithContext(ctx).Infow("OnSystemDocChanged", "ugc_id", event.Doc.UgcId, "ugc_type", event.Doc.UgcType)

	l := logic.SyncSystemDocLogic{}

	var err error
	switch event.Action {
	case eventspb.SystemDocChanged_ACTION_PUBLISH:
		err = l.Publish(ctx, event.Doc)
	case eventspb.SystemDocChanged_ACTION_DELETE:
		err = l.Delete(ctx, event.Doc)
		logic.DisbandDocContributorAsync(ctx, event.Doc)
	case eventspb.SystemDocChanged_ACTION_UNPUBLISH:
		err = l.Unpublish(ctx, event.Doc)
	}
	if err != nil {
		log.WithContext(ctx).Errorw("sync system doc failed", "err", err)
	}

	return nil
}
