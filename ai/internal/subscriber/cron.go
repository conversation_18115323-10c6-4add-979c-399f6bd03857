package subscriber

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/event"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/handler"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	assistantLogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/assistant"
	docchunklogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
)

// OnCronJob 定时任务
func (*Ai) OnCronJob(ctx context.Context, job *eventspb.CronJob) error {
	if <PERSON><PERSON><PERSON>Expired(ctx, time.Minute) {
		return nil
	}

	switch job.Name {
	case "FireChunkDocSubtasks":
		fireChunkDocSubtasks(ctx)
	case "TryFinishChunkTasks":
		tryFinishChunkTasks(ctx)
	case "CreateCronGTBText":
		createCronGTBText(ctx)
	case "NebulaCalcResetPod":
		NebulaCalcResetPod(ctx)
	case "NebulaCalcKnowLedge":
		NebulaCalcKnowLedge(ctx)
	case "CreateAllAssistantDataSnapshot":
		createAllAssistantDataSnapshot(ctx)
	case "RefreshTencentDocUserInfo":
		refreshTencentDocUserInfo(ctx)
	}

	return nil
}

// 启动文档分段子任务
func fireChunkDocSubtasks(ctx context.Context) {
	if err := new(docchunklogic.ChunkTaskLogic).FireSubtasks(ctx); err != nil {
		log.WithContext(ctx).Errorw("FireChunkDocSubtasks", "error", err)
	}
}

func tryFinishChunkTasks(ctx context.Context) {
	if err := new(docchunklogic.ChunkTaskLogic).TryFinishTasks(ctx); err != nil {
		log.WithContext(ctx).Errorw("TryFinishChunkTasks", "error", err)
	}
}

func createCronGTBText(ctx context.Context) {
	ai := handler.Ai{}

	rsp := &aipb.RspCreateGTBText{}
	req := &aipb.ReqCreateGTBText{
		IsPullAll: true,
		AdminType: uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM),
		AccountId: config.GetUint64("llm.collection.external.gtb_team_id"),
	}
	err := ai.CreateGTBText(ctx, req, rsp)
	if err != nil {
		log.WithContext(ctx).Errorw("createCronGTBText", "error", err)
	}
}

func NebulaCalcKnowLedge(ctx context.Context) {
	if err := logic.NebulaCalcKnowLedge(ctx); err != nil {
		log.WithContext(ctx).Errorw("NebulaCalcKnowLedge", "error", err)
	}
}

func NebulaCalcResetPod(ctx context.Context) {
	if err := logic.NebulaCalcResetPod(ctx); err != nil {
		log.WithContext(ctx).Errorw("NebulaCalcResetPod", "error", err)
	}
}

func createAllAssistantDataSnapshot(ctx context.Context) {
	log.WithContext(ctx).Infow("createAllAssistantDataSnapshot start")
	if err := assistantLogic.CreateAllAssistantDataSnapshot(ctx); err != nil {
		log.WithContext(ctx).Errorw("createAllAssistantDataSnapshot", "error", err)
	}
}

// refreshTencentDocUserInfo 定时刷新腾讯文档用户信息
func refreshTencentDocUserInfo(ctx context.Context) {
	log.WithContext(ctx).Infow("refreshTencentDocUserInfo start")
	if err := logic.RefreshAllTencentDocUserInfo(ctx); err != nil {
		log.WithContext(ctx).Errorw("refreshTencentDocUserInfo", "error", err)
	}
}

// IsJobExpired 判断任务是否过期
func IsJobExpired(ctx context.Context, d time.Duration) bool {
	if md := event.MetadataFromContext(ctx); md != nil && md.Ts != nil {
		return time.Since(md.Ts.AsTime()) > d
	}
	return false
}
