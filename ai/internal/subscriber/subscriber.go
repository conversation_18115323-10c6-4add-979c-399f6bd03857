// Package subscriber ...
package subscriber

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
	"github.com/asim/go-micro/v3"
)

// Ai ai主题监听器
type Ai struct {
}

// RegisterSubscriber 注册subscriber
func RegisterSubscriber(svc micro.Service) {
	subscriberQueue := config.GetString("service.name")

	if config.GetBoolOr("subscriber.aiTopicEnabled", true) {
		subscriberTopic := config.GetStringOr("subscriber.topic", "tanlive.v2.ai")
		eventspb.SubscribeAiTopic(subscriberTopic, svc.Server(), &Ai{}, subscriberQueue)
	}

	if config.GetBool("subscriber.ragTopicEnabled") {
		ragTopic := config.GetStringOr("subscriber.ragTopic", "tanlive.v2.rag")
		eventspb.SubscribeRagTopic(ragTopic, svc.Server(), &Rag{}, subscriberQueue)
	}
}
