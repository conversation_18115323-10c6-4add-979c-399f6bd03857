package subscriber

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/cmd"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
	"gorm.io/gorm/clause"
)

// Rag rag主题监听器
type Rag struct {
}

// OnDocToCollection 将文档写入Collection
func (r *Rag) OnDocToCollection(ctx context.Context, evt *eventspb.DocToCollection) error {
	// 清理chunks
	model.NewQuery[model.TAssistantDoc](ctx).UpdateBy(
		map[string]any{"chunk_detail_id": 0},
		"doc_id = ? AND assistant_id = (SELECT assistant_id FROM t_assistant_collection WHERE collection_id = ?)",
		evt.DocId, evt.CollectionId)

	processingTime, processedCount, err := docToCollection(ctx, evt.DocId, evt.CollectionId, evt.IsDelete)
	success, errMsg := 1, ""
	if err != nil {
		success, errMsg = 0, err.Error()
	}
	logKeyAndValues := []any{
		"task_id", evt.TaskId,
		"doc_id", evt.DocId,
		"collection_id", evt.CollectionId,
		"cost", processingTime,
		"tried", processedCount,
		"success", success,
		"is_delete", evt.IsDelete,
		"error", errMsg,
	}
	log.WithContext(ctx).Infow("DocToCollectionResult", logKeyAndValues...)

	// 保存任务结果
	_ = model.NewQuery[cmd.TMigrateAllDoc](ctx).Clause(clause.OnConflict{
		Columns:   []clause.Column{{Name: "doc_id"}, {Name: "collection_id"}, {Name: "task_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"success", "error"}),
	}).Create(&cmd.TMigrateAllDoc{
		DocID:        evt.DocId,
		CollectionID: evt.CollectionId,
		TaskID:       evt.TaskId,
		Success:      success,
		IsDelete:     evt.IsDelete,
		Error:        errMsg,
	})

	return nil
}

func docToCollection(ctx context.Context, docID, collectionID uint64, isDelete bool) (int64, int64, error) {
	doc, err := findDocByID(ctx, docID, isDelete)
	if err != nil {
		return 0, 0, err
	}

	collection, err := findCollectionByID(ctx, collectionID)
	if err != nil {
		return 0, 0, err
	}

	return cmd.SyncDoc(ctx, doc, collection, false, 10, isDelete)
}

func findDocByID(ctx context.Context, docID uint64, isDelete bool) (*model.TDoc, error) {
	query := model.NewQuery[model.TDoc](ctx).
		With("MatchPatterns").
		Where("id = ?", docID)
	if isDelete {
		query.DB().Unscoped()
	}

	doc, err := query.Find()
	if err != nil {
		return nil, fmt.Errorf("find doc: %w", err)
	}
	return doc, nil
}

func findCollectionByID(ctx context.Context, collectionID uint64) (*model.TCollection, error) {
	collection, err := model.NewQuery[model.TCollection](ctx).Where("id = ?", collectionID).Find()
	if err != nil {
		return nil, fmt.Errorf("find collection: %e", err)
	}
	return collection, nil
}
