package handler

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"google.golang.org/protobuf/types/known/emptypb"
)

// CreateSystemDocCopy 创建系统文档副本
func (a *Ai) CreateSystemDocCopy(ctx context.Context,
	req *aipb.ReqCreateSystemDocCopy, rsp *aipb.RspCreateSystemDocCopy) error {
	l := logic.CreateSystemDocCopyLogic{}

	newID, err := l.Create(ctx, req.CopyFrom, req.CreateBy)
	if err != nil {
		return err
	}

	rsp.DocId = newID
	return nil
}

// EnableSystemDoc 启用系统文档
func (a *Ai) EnableSystemDoc(ctx context.Context,
	req *aipb.ReqEnableSystemDoc, _ *emptypb.Empty) error {
	l := logic.EnableSystemDocLogic{}

	if err := l.Enable(ctx, req.DocId, req.Operator); err != nil {
		return err
	}

	return nil
}

// DisableSystemDoc 停用系统文档
func (a *Ai) DisableSystemDoc(ctx context.Context,
	req *aipb.ReqDisableSystemDoc, _ *emptypb.Empty) error {
	l := logic.DisableSystemDocLogic{}

	if err := l.Disable(ctx, req.DocId, req.Operator); err != nil {
		return err
	}

	return nil
}

// DeleteSystemDoc 删除系统文档
func (a *Ai) DeleteSystemDoc(ctx context.Context,
	req *aipb.ReqDeleteSystemDoc, _ *emptypb.Empty) error {
	l := logic.DeleteSystemDocLogic{}

	if err := l.Delete(ctx, req.DocId, req.Operator); err != nil {
		return err
	}

	return nil
}
