package handler

import (
	"context"
	"fmt"

	assistantlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/assistant"
	docchunklogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// GetDocChunks 查询文档分段信息
func (a *Ai) GetDocChunks(ctx context.Context, req *aipb.ReqGetDocChunks, rsp *aipb.RspGetDocChunks) error {
	l := docchunklogic.GetChunkLogic{}

	assistantChunks, err := l.GetChunks(ctx, req)
	if err != nil {
		return err
	}

	rsp.AssistantChunks = assistantChunks

	return nil
}

// ChunkDoc 文档分段
func (a *Ai) ChunkDoc(ctx context.Context, req *aipb.ReqChunkDoc, rsp *aipb.RspChunkDoc) error {
	l := docchunklogic.ChunkDocLogic{}

	doc, newText, err := l.GetDoc(ctx, req.DocId, req.NewText)
	if err != nil {
		return err
	}

	var (
		chunks []*aipb.ChunkItem
		task   *model.TDocChunkTask
	)
	switch para := req.ChunkPara.(type) {
	case *aipb.ReqChunkDoc_AutoPara:
		chunks, err = l.AutoChunk(ctx, doc, para.AutoPara, req.CreateBy)
	case *aipb.ReqChunkDoc_ManualPara:
		task, err = l.ManualChunk(ctx, doc, newText, para.ManualPara, req.CreateBy)
	}

	if err != nil {
		return err
	}

	rsp.Chunks = chunks
	if task != nil {
		rsp.TaskId = task.ID
	}

	return nil
}

// GetChunkDocTasks 查询文档分段任务列表
func (a *Ai) GetChunkDocTasks(ctx context.Context, req *aipb.ReqGetChunkDocTasks, rsp *aipb.RspGetChunkDocTasks) error {
	tasks, err := model.NewQuery[model.TDocChunkTask](ctx).
		Select("id", "doc_id", "state").
		GetBy("id in (SELECT MAX(id) FROM t_doc_chunk_task WHERE doc_id IN (?) GROUP BY doc_id)", req.DocId)
	if err != nil {
		return fmt.Errorf("query doc chunk tasks: %w", err)
	}

	// 排序
	taskMap := make(map[uint64]*model.TDocChunkTask, len(tasks))
	for _, task := range tasks {
		taskMap[task.DocID] = task
	}
	sortedTasks := make([]*model.TDocChunkTask, 0, len(tasks))
	for _, task := range tasks {
		sortedTasks = append(sortedTasks, taskMap[task.DocID])
	}

	rsp.Tasks = model.TDocChunkTasks(sortedTasks).ToPb()
	return nil
}

// GetDocEmbeddingModels 查询文档的向量化模型
func (a *Ai) GetDocEmbeddingModels(ctx context.Context, req *aipb.ReqGetDocEmbeddingModels, rsp *aipb.RspGetDocEmbeddingModels) error {
	assistantQuery := model.NewConnection(ctx).Model(&model.TAssistant{}).Select("`id`")
	model.ApplyAssistantAdminCond(assistantQuery, req.AdminUserId, req.AdminTeamId, false)
	model.ApplyAssistantDoc(assistantQuery, req.DocId)

	const sql = "SELECT `lang` AS `collection_lang`, COUNT(*) AS `count` FROM `t_collection` " +
		"WHERE `id` IN (SELECT `collection_id` FROM `t_assistant_collection` WHERE `assistant_id` IN (?)) GROUP BY `collection_lang`"

	var embeddingModels []*aipb.EmbeddingModelCount
	err := model.NewConnection(ctx).Raw(sql, assistantQuery).Find(&embeddingModels).Error
	if err != nil {
		return fmt.Errorf("query doc embedding models: %w", err)
	}

	// 填充名称
	names, err := assistantlogic.GetEmbeddingModelNames()
	if err != nil {
		return err
	}
	for _, embeddingModel := range embeddingModels {
		embeddingModel.EmbeddingModelName = names[embeddingModel.CollectionLang]
	}

	rsp.EmbeddingModels = embeddingModels
	return nil
}
