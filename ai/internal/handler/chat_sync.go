package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/golang/protobuf/ptypes/empty"
	"gorm.io/gorm"
)

type TOpDocs struct {
	ID       int64  `db:"id" json:"id"`
	Title    string `db:"title" json:"title,omitempty"`
	Texts    string `db:"texts" json:"texts,omitempty"`
	State    int    `db:"state" json:"state"`
	Del      int    `db:"del" json:"del"`
	Language string `db:"language" json:"language"`
}

// CollectionSnapshot structure for chat message collection data
type CollectionSnapshot struct {
	StartTime   time.Time                    `json:"startTime,omitempty"`
	EndTime     time.Time                    `json:"endTime,omitempty"`
	Items       []*aipb.SearchCollectionItem `json:"items,omitempty"`
	CleanChunks bool                         `json:"cleanChunks,omitempty"`
}

func (a *Ai) FetchHtmlTitles(ctx context.Context, req *aipb.ReqFetchHtmlTitles, rsp *aipb.RspFetchHtmlTitles) error {
	var wg sync.WaitGroup
	titles := make(map[string]string)
	var mu sync.Mutex
	for _, url := range req.Urls {
		wg.Add(1)
		url := url
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			defer wg.Done()
			title := logic.FetchHtmlTitleWithDistributedLock(ctx, url)
			mu.Lock()
			titles[url] = title
			mu.Unlock()
			return nil
		}, boot.TraceGo(ctx))
	}

	wg.Wait()
	log.WithContext(ctx).Infof("html proxy url titles %+v", titles)
	rsp.UrlWithTitles = titles
	return nil
}

func (a *Ai) SyncFixChatMessageCollection2(ctx context.Context, req *aipb.ReqSyncFixChatMessageCollection, _ *empty.Empty) error {
	const batchSize = 1000
	var offset int64 = 0

	for {
		var messages []model.TChatMessage
		if err := model.Transaction(ctx, func(tx *gorm.DB) error {
			// 分批查询
			if err := tx.Where("collection_snapshot IS NOT NULL AND collection_snapshot != ''").
				Offset(int(offset)).
				Limit(batchSize).
				Find(&messages).Error; err != nil {
				return err
			}

			if len(messages) == 0 {
				return gorm.ErrRecordNotFound
			}

			// 处理这批记录
			for _, msg := range messages {
				var snapshot CollectionSnapshot
				var collections []*aipb.SearchCollectionItem
				if err := json.Unmarshal([]byte(msg.CollectionSnapshot), &snapshot); err != nil {
					err = json.Unmarshal([]byte(msg.CollectionSnapshot), &collections)
					if err != nil {
						continue
					}
					snapshot.Items = collections
				}

				content, err := json.Marshal(snapshot.Items)
				if err != nil {
					continue
				}

				collection := &model.TChatMessageCollection{
					MessageID:   msg.ID,
					Content:     string(content),
					StartTime:   &snapshot.StartTime,
					EndTime:     &snapshot.EndTime,
					CleanChunks: snapshot.CleanChunks,
					CreateDate:  time.Now(),
				}

				if err := tx.Create(collection).Error; err != nil {
					log.WithContext(ctx).Errorw("failed to create collection record",
						"message_id", msg.ID, "error", err)
					continue
				}

				if err := tx.Model(&model.TChatMessage{}).
					Where("id = ?", msg.ID).
					Update("collection_snapshot", "").Error; err != nil {
					log.WithContext(ctx).Errorw("failed to clear collection snapshot",
						"message_id", msg.ID, "error", err)
					continue
				}
			}
			log.WithContext(ctx).Infow("sync fix chat message collection success", "limit", batchSize, "offset", offset)
			return nil
		}); err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break // 没有更多数据了
			}
			return err
		}

		offset += int64(len(messages))
	}

	return nil
}

func (a *Ai) SyncFixChatMessageCollection(ctx context.Context, req *aipb.ReqSyncFixChatMessageCollection, _ *empty.Empty) error {
	type MessageExportItem struct {
		CollectionName  string                       `json:"collection_name"`
		FinalQuery      string                       `json:"final_query"`
		Query           string                       `json:"query"`
		CollectionItems []*aipb.SearchCollectionItem `json:"collection_items"`
	}

	// 立即返回，让RPC调用不会超时，改为异步执行
	go func() {
		// 使用新的context，避免原context取消导致任务中断
		newCtx := context.Background()
		log.WithContext(newCtx).Infow("starting async export task")
		
		// 创建导出目录
		exportDir := "chat_export_" + time.Now().Format("20060102_150405")
		if err := os.MkdirAll(exportDir, 0755); err != nil {
			log.WithContext(newCtx).Errorw("failed to create export directory", "dir", exportDir, "error", err)
			return
		}
		
		// 记录进度和统计信息的文件
		progressFile := exportDir + "/progress.txt"
		progressLog := func(msg string) {
			f, err := os.OpenFile(progressFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
			if err != nil {
				log.WithContext(newCtx).Errorw("failed to open progress file", "error", err)
				return
			}
			defer f.Close()
			timeStr := time.Now().Format("2006-01-02 15:04:05")
			if _, err := f.WriteString(timeStr + " - " + msg + "\n"); err != nil {
				log.WithContext(newCtx).Errorw("failed to write to progress file", "error", err)
			}
		}

		progressLog("开始导出任务")
		
		const batchSize = 1000
		var offset int64 = 0
		var totalProcessed int64 = 0
		var successCount int64 = 0
		
		// 存储所有导出项的切片
		var allExportItems []MessageExportItem
		
		for {
			var messages []model.TChatMessage
			if err := model.Transaction(newCtx, func(tx *gorm.DB) error {
				// 分批查询
				if err := tx.Where("type = ?", aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION).
					Preload("Collections").
					Offset(int(offset)).
					Limit(batchSize).
					Find(&messages).Error; err != nil {
					return err
				}

				if len(messages) == 0 {
					return gorm.ErrRecordNotFound
				}

				// 处理这批记录
				for _, msg := range messages {
					totalProcessed++
					chatMessage, err := msg.TransformToAiMessage()
					if err != nil {
						log.WithContext(newCtx).Errorw("failed to transform message", "message_id", msg.ID, "error", err)
						continue
					}
					
					if chatMessage.CollectionSnapshot == nil || len(chatMessage.CollectionSnapshot.Items) == 0 {
						log.WithContext(newCtx).Warnw("empty collection snapshot", "message_id", msg.ID)
						continue
					}
					
					// 创建导出项
					exportItem := MessageExportItem{
						CollectionItems: make([]*aipb.SearchCollectionItem, 0, len(chatMessage.CollectionSnapshot.Items)),
					}
					
					// 处理CollectionItems，移除Contributor字段
					for _, item := range chatMessage.CollectionSnapshot.Items {
						// 创建一个新的item，但不包含Contributor字段
						cleanItem := &aipb.SearchCollectionItem{
							Id:        item.Id,
							Score:     item.Score,
							Text:      item.Text,
							Question:  item.Question,
							FileName:  item.FileName,
							Url:       item.Url,
							Type:      item.Type,
							IsRelated: item.IsRelated,
							RefName:   item.RefName,
							RefUrl:    item.RefUrl,
							DocName:   item.DocName,
							// 不包含Contributor
						}
						exportItem.CollectionItems = append(exportItem.CollectionItems, cleanItem)
					}
					
					// 1. 获取CollectionName: 通过TChatMessage的assistant_id关联TAssistantCollection表再关联TCollection表取RagName
					var assistantCollection model.TAssistantCollection
					if err := tx.Where("assistant_id = ?", msg.AssistantID).
						Preload("Collection").
						First(&assistantCollection).Error; err == nil && assistantCollection.Collection != nil {
						exportItem.CollectionName = assistantCollection.Collection.RagName
					} else {
						log.WithContext(newCtx).Warnw("failed to get collection name", 
							"message_id", msg.ID, "assistant_id", msg.AssistantID, "error", err)
					}
					
					// 2 & 3. 获取FinalQuery和Query: 通过msg.question_id查询问题消息
					if msg.QuestionID > 0 {
						// 通过问题ID查询对应的问题消息
						var questionMsg model.TChatMessage
						if err := tx.Where("id = ?", msg.QuestionID).First(&questionMsg).Error; err == nil {
							// 解析问题消息的content获取text字段
							var content map[string]interface{}
							if err := json.Unmarshal(questionMsg.Content, &content); err == nil {
								if text, ok := content["text"].(string); ok {
									exportItem.Query = text
									exportItem.FinalQuery = text // 同时设置FinalQuery
								} else {
									log.WithContext(newCtx).Warnw("question content does not contain text field", 
										"question_id", msg.QuestionID)
								}
							} else {
								log.WithContext(newCtx).Warnw("failed to parse question content", 
									"question_id", msg.QuestionID, "error", err)
							}
						} else {
							log.WithContext(newCtx).Warnw("failed to get question message", 
								"message_id", msg.ID, "question_id", msg.QuestionID, "error", err)
						}
					}

					var chatLog model.TChatLog
					if err := tx.Where("message_id = ? AND message_type = ?", msg.ID, msg.Type).
						First(&chatLog).Error; err == nil {
						// FinalQuery从ConfigSnapshot中取
						var configMap map[string]interface{}
						if err := json.Unmarshal([]byte(chatLog.ConfigSnapshot), &configMap); err == nil {
							if finalQuery, ok := configMap["final_query"].(string); ok && finalQuery != "" {
								exportItem.FinalQuery = finalQuery
							}
						}
					} else {
						log.WithContext(newCtx).Warnw("failed to get chat log", 
							"message_id", msg.ID, "error", err)
					}
					
					// 将导出项添加到切片中
					allExportItems = append(allExportItems, exportItem)
					successCount++
					if successCount%100 == 0 {
						progressLog(fmt.Sprintf("已成功处理 %d 条记录", successCount))
					}
				}
				
				progressLog(fmt.Sprintf("已处理 %d 条记录，当前批次完成", totalProcessed))
				return nil
			}); err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					progressLog(fmt.Sprintf("数据处理完成，共处理 %d 条记录，成功处理 %d 条", totalProcessed, successCount))
					break // 没有更多数据了
				}
				log.WithContext(newCtx).Errorw("处理批次时发生错误", "error", err)
				progressLog(fmt.Sprintf("处理批次时发生错误: %v", err))
				return
			}

			offset += int64(len(messages))
		}
		
		// 以FinalQuery为键进行去重处理
		log.WithContext(newCtx).Infow("开始进行FinalQuery去重",
			"before_dedup", len(allExportItems))
		progressLog(fmt.Sprintf("开始进行FinalQuery去重，去重前记录数: %d", len(allExportItems)))
		
		// 使用map存储唯一的FinalQuery
		uniqueQueries := make(map[string]bool)
		var dedupExportItems []MessageExportItem
		
		for _, item := range allExportItems {
			// 如果FinalQuery为空，直接保留
			if item.FinalQuery == "" {
				dedupExportItems = append(dedupExportItems, item)
				continue
			}
			
			// 检查是否已存在相同的FinalQuery
			if _, exists := uniqueQueries[item.FinalQuery]; !exists {
				// 不存在，添加到去重后的列表并记录
				uniqueQueries[item.FinalQuery] = true
				dedupExportItems = append(dedupExportItems, item)
			}
		}
		
		log.WithContext(newCtx).Infow("FinalQuery去重完成",
			"before_dedup", len(allExportItems),
			"after_dedup", len(dedupExportItems),
			"removed", len(allExportItems) - len(dedupExportItems))
		progressLog(fmt.Sprintf("FinalQuery去重完成，去重前记录数: %d，去重后记录数: %d，移除记录数: %d",
			len(allExportItems), len(dedupExportItems), len(allExportItems) - len(dedupExportItems)))
		
		// 使用去重后的数据
		allExportItems = dedupExportItems
		
		// 将导出项按每1000条拆分成多个JSON文件
		const itemsPerFile = 1000
		totalFiles := (len(allExportItems) + itemsPerFile - 1) / itemsPerFile // 向上取整
		log.WithContext(newCtx).Infow("开始拆分JSON文件", 
			"total_items", len(allExportItems), 
			"items_per_file", itemsPerFile,
			"total_files", totalFiles)
		progressLog(fmt.Sprintf("开始将 %d 条记录拆分为 %d 个文件，每个文件 %d 条记录", 
			len(allExportItems), totalFiles, itemsPerFile))
		
		var fileSuccessCount int
		for i := 0; i < totalFiles; i++ {
			start := i * itemsPerFile
			end := (i + 1) * itemsPerFile
			if end > len(allExportItems) {
				end = len(allExportItems)
			}
			
			// 当前批次的数据
			batchItems := allExportItems[start:end]
			
			// 生成文件名
			outputFilePath := fmt.Sprintf("%s/export_items_part_%d.json", exportDir, i+1)
			
			// 序列化数据
			exportItemsJSON, err := json.MarshalIndent(batchItems, "", "  ")
			if err != nil {
				log.WithContext(newCtx).Errorw("序列化导出数据失败", "batch", i+1, "error", err)
				progressLog(fmt.Sprintf("序列化第 %d 批数据失败: %v", i+1, err))
				continue
			}
			
			// 写入文件
			if err := os.WriteFile(outputFilePath, exportItemsJSON, 0644); err != nil {
				log.WithContext(newCtx).Errorw("写入导出文件失败", "file_path", outputFilePath, "error", err)
				progressLog(fmt.Sprintf("写入第 %d 批数据失败: %v", i+1, err))
				continue
			}
			
			fileSuccessCount++
			log.WithContext(newCtx).Infow("成功写入批次文件", 
				"file_path", outputFilePath, 
				"items_count", len(batchItems),
				"batch", i+1)
			progressLog(fmt.Sprintf("成功写入第 %d 批数据，包含 %d 条记录", i+1, len(batchItems)))
		}
		
		// 生成完成标记文件
		completionMsg := fmt.Sprintf("导出任务完成\n"+
			"总处理记录数: %d\n"+
			"成功处理记录数: %d\n"+
			"成功导出文件数: %d/%d\n"+
			"导出目录: %s\n"+
			"每个文件记录数: %d", 
			totalProcessed, successCount, fileSuccessCount, totalFiles, exportDir, itemsPerFile)
			
		completeFilePath := exportDir + "/COMPLETE.txt"
		if err := os.WriteFile(completeFilePath, []byte(completionMsg), 0644); err != nil {
			log.WithContext(newCtx).Errorw("写入完成标记文件失败", "error", err)
		}
		
		// 创建索引文件，记录所有生成的文件名
		var fileIndex strings.Builder
		fileIndex.WriteString("# 导出文件索引\n\n")
		fileIndex.WriteString(fmt.Sprintf("总记录数: %d\n", successCount))
		fileIndex.WriteString(fmt.Sprintf("文件数: %d\n", totalFiles))
		fileIndex.WriteString(fmt.Sprintf("每个文件记录数: %d\n\n", itemsPerFile))
		fileIndex.WriteString("## 文件列表\n\n")
		
		for i := 1; i <= totalFiles; i++ {
			fileName := fmt.Sprintf("export_items_part_%d.json", i)
			fileIndex.WriteString(fmt.Sprintf("%d. %s\n", i, fileName))
		}
		
		indexFilePath := exportDir + "/INDEX.md"
		if err := os.WriteFile(indexFilePath, []byte(fileIndex.String()), 0644); err != nil {
			log.WithContext(newCtx).Errorw("写入索引文件失败", "error", err)
		}
		
		log.WithContext(newCtx).Infow("导出任务完成", 
			"total_processed", totalProcessed, 
			"success_count", successCount,
			"total_files", totalFiles,
			"success_files", fileSuccessCount,
			"export_dir", exportDir)
	}()
	
	return nil
}
