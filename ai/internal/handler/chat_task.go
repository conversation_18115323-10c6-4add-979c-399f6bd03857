package handler

import (
	"context"
	"errors"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// DescribeChatAgentTask 查询agent task
func (a *Ai) DescribeChatAgentTask(ctx context.Context, req *aipb.ReqDescribeChatAgentTask, rsp *aipb.RspDescribeChatAgentTask) error {
	var tasks []model.TPipelineTask

	query := model.NewQuery[model.TPipelineTask](ctx).DB().
		Joins("JOIN t_pipeline ON t_pipeline.id = t_pipeline_task.pipeline_id").
		Joins("JOIN t_assistant_pipeline ON t_assistant_pipeline.pipeline_id = t_pipeline.id").
		Where("t_assistant_pipeline.assistant_id = ? AND t_pipeline.command = ?", req.AssistantId, req.Prompt)

	if req.NoNextOrderTask {
		query.Where("t_pipeline_task.task_order = 1")
	}
	if req.NoNextTask {
		query.Where("t_pipeline_task.pre_task_id = 0")
	}

	query.Order("t_pipeline_task.`task_order` ASC")

	// 通过联表查询直接获取pipeline下的tasks
	if err := query.Order("t_pipeline_task.`task_order` ASC").
		Find(&tasks).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		return nil
	}

	// 转换为响应格式
	for _, task := range tasks {
		pbTask := task.TransToChatAgentTask()
		pbTask.Command = req.Prompt
		rsp.Tasks = append(rsp.Tasks, pbTask)
	}

	return nil
}

// DescribePreMessageTask 查询前置消息任务
func (a *Ai) DescribePreMessageTask(ctx context.Context, req *aipb.ReqDescribePreMessageTask, rsp *aipb.RspReqDescribePreMessageTask) error {
	var prevMessage model.TChatMessage
	if err := model.NewQuery[model.TChatMessage](ctx).DB().
		Where("chat_id = ? AND id < ?", req.ChatId, req.MessageId).
		Order("id DESC").
		First(&prevMessage).Error; err != nil {
		return xerrors.NotFoundError(err)
	}

	var messageTask model.TChatMessageTask
	if err := model.NewQuery[model.TChatMessageTask](ctx).DB().
		Where("message_id = ? AND state = ?",
			prevMessage.ID,
			aipb.PipelineTaskState_CHAT_AGENT_TASK_STATE_FINISHED).
		First(&messageTask).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	rsp.TaskId = messageTask.TaskID
	return nil
}

// DescribeChatAgentTaskByPreTask 查询当前message是否有需要执行的task
func (a *Ai) DescribeChatAgentTaskByPreTask(ctx context.Context, req *aipb.ReqDescribeChatAgentTaskByPreTask, rsp *aipb.RspDescribeChatAgentTaskByPreTask) error {
	// 通过task_id查询pipeline task
	var preTask model.TPipelineTask
	if err := model.NewQuery[model.TPipelineTask](ctx).DB().
		Where("id = ?", req.PreTaskId).Find(&preTask).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}

	// 查询pipeline中order大于pre_task的task及其子task
	var tasks []model.TPipelineTask
	if err := model.NewQuery[model.TPipelineTask](ctx).DB().
		Where("pipeline_id = ? AND task_order = ?",
			preTask.PipelineID, preTask.TaskOrder+1).
		Order("task_order ASC").
		Find(&tasks).Error; err != nil {
		return err
	}

	// 4. 转换为响应格式
	for _, task := range tasks {
		rsp.Tasks = append(rsp.Tasks, task.TransToChatAgentTask())
	}

	return nil
}
