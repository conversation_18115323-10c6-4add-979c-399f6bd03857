package handler

import (
	"context"
	"errors"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"github.com/golang/protobuf/ptypes/empty"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ListCustomLabel 获取对话标签
func (a *Ai) ListCustomLabel(ctx context.Context, req *aipb.ReqListCustomLabel, rsp *aipb.RspListCustomLabel) error {
	if req.TenantId == 0 {
		return xerrors.New(errorspb.AiError_AiTenantInvalid, "tenant id is zero")
	}
	db := model.NewQuery[model.TCustomLabel](ctx).DB()
	if len(req.Ids) != 0 {
		db = db.Where("id in ?", req.Ids)
	}
	if req.ObjectType != aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_UNSPECIFIED {
		db = db.Where("object_type = ?", req.ObjectType)
	}
	if req.Page != nil {
		pager := xorm.NewPaginator(req.Page.Offset, req.Page.Limit)
		db = (&pager).Apply(db)
	}
	db.Where(&model.TCustomLabel{TenantID: req.TenantId})
	rows := make([]model.TCustomLabel, 0, len(req.Ids))
	err := db.Scopes(model.LabelWithChain).Find(&rows).Error

	if err != nil {
		return err
	}
	for _, v := range rows {
		rsp.Labels = append(rsp.Labels, v.ToProto())
	}

	return nil
}

// UpsertCustomLabels upsert对话标签
// func (a *Ai) UpsertCustomLabels(ctx context.Context, req *aipb.ReqUpsertCustomLabels, rsp *aipb.RspUpsertCustomLabels) error {
// 	if len(req.Labels) == 0 {
// 		return nil
// 	}
//
// 	rows := make([]*model.TCustomLabel, 0, len(req.Labels))
// 	for i, v := range req.Labels {
// 		rows = append(rows, new(model.TCustomLabel).FromProto(v, req.ObjectType, req.UserId))
// 		rows[i].TenantID = req.TenantId
// 	}
//
// 	err := model.Transaction(ctx, func(tx *gorm.DB) error {
// 		for _, row := range rows {
// 			// 第一步：查找当前标签的状态
// 			var existingLabel model.TCustomLabel
// 			err := tx.First(&existingLabel, row.ID).Error
// 			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
// 				return err
// 			}
// 			var next model.TCustomLabel
// 			if row.NextLabelId != 0 {
// 				err = tx.First(&next, row.NextLabelId).Error
// 				if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
// 					return err
// 				}
// 			}
// 			// 将 sort 值设置为下一个节点的值
// 			if next.ID != 0 {
// 				row.Sort = next.Sort
// 			}
// 			// 第二步：执行 Upsert 操作
// 			err = tx.Clauses(clause.OnConflict{
// 				Columns:   []clause.Column{{Name: "id"}},
// 				DoUpdates: clause.AssignmentColumns([]string{"key", "value", "update_by", "type", "next_label", "sort"}),
// 			}).Create(row).Error
// 			if err != nil {
// 				return err
// 			}
//
// 			// 第三步：检查 Sort 值是否改变
// 			if existingLabel.Sort != row.Sort || (row.Sort == 0 && row.NextLabelId != 0) {
// 				// 更新当前标签的排序位置
// 				err = tx.Model(&model.TCustomLabel{}).
// 					Where("sort >= ? AND id != ? AND tenant_id = ? AND object_type = ?", row.Sort, row.ID, req.TenantId, req.ObjectType).
// 					Update("sort", gorm.Expr("sort + 1")).Error
// 				if err != nil {
// 					return err
// 				}
// 			}
// 		}
// 		return nil
// 	})
//
// 	if err != nil {
// 		return err
// 	}
//
// 	return nil
// }

// DeleteCustomLabels 删除自定义标签
// func (a *Ai) DeleteCustomLabels(ctx context.Context, req *aipb.ReqDeleteCustomLabels, rsp *emptypb.Empty) error {
// 	if len(req.Ids) == 0 {
// 		return nil
// 	}
// 	_, err := model.NewQuery[model.TCustomLabel](ctx).DeleteByKeys(req.Ids)
// 	if err != nil {
// 		return err
// 	}
// 	return nil
// }

// UpsertCustomLabels upsert对话标签
func (a *Ai) UpsertCustomLabels(ctx context.Context, req *aipb.ReqUpsertCustomLabels, rsp *aipb.RspUpsertCustomLabels) error {
	if len(req.Labels) == 0 {
		return nil
	}

	rows := make([]*model.TCustomLabel, 0, len(req.Labels))
	for i, v := range req.Labels {
		rows = append(rows, new(model.TCustomLabel).FromProto(v, req.ObjectType, req.UserId))
		rows[i].TenantID = req.TenantId
	}

	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		for _, row := range rows {
			// 1. 查找当前标签的状态
			var existingLabel model.TCustomLabel
			err := tx.First(&existingLabel, row.ID).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}

			// 2. 从链表中移除
			if existingLabel.ID != 0 && (existingLabel.NextLabel != row.NextLabel || existingLabel.NextLabelId != row.NextLabelId) {
				err = RemoveLabelFromChain(tx, existingLabel.ID)
				if err != nil {
					return err
				}
			}

			// 3. 插入
			err = InsertLabelToChain(tx, row, req.TenantId, req.ObjectType)
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

// InsertLabelToChain 将label插入到链表中
func InsertLabelToChain(tx *gorm.DB, label *model.TCustomLabel, tenantId uint64, objType aipb.CustomLabelObjectType) error {
	// 1. 找出前置节点
	var prev *model.TCustomLabel
	err := tx.Model(&model.TCustomLabel{}).
		Where("next_label_id = ?", label.NextLabelId).
		Where("next_label = ?", label.NextLabel).
		Where("tenant_id = ?", tenantId).
		Where("object_type = ?", objType).
		Find(&prev).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 2. 插入label
	err = tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"key", "value", "update_by", "type", "next_label", "next_label_id"}),
	}).Create(label).Error
	if err != nil {
		return err
	}

	// 3.更新前置节点指向
	if prev.ID != 0 && prev.ID != label.ID {
		err = tx.Model(prev).
			Select("next_label_id", "next_label").
			Updates(&model.TCustomLabel{
				NextLabelId: label.ID,
			}).Error
		if err != nil {
			return err
		}
	}

	return nil
}

// RemoveLabelFromChain 将label链表中移除
func RemoveLabelFromChain(tx *gorm.DB, id uint64) error {
	// 1. 找出前置节点
	var prev *model.TCustomLabel
	err := tx.Model(&model.TCustomLabel{}).
		Where("next_label_id = ?", id).
		Find(&prev).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if prev.ID == 0 {
		return nil
	}

	// 2. 找出待当前待移除的标签
	var current *model.TCustomLabel
	err = tx.Model(&model.TCustomLabel{}).
		Where("id = ?", id).
		Find(&current).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 3. 更新前置节点的 next_label_id
	err = tx.Model(prev).
		Select("next_label_id", "next_label").
		Updates(&model.TCustomLabel{
			NextLabelId: current.NextLabelId,
			NextLabel:   current.NextLabel,
		}).Error
	if err != nil {
		return err
	}
	return nil
}

func (a *Ai) DeleteCustomLabels(ctx context.Context, req *aipb.ReqDeleteCustomLabels, rsp *emptypb.Empty) error {
	if len(req.Ids) == 0 {
		return nil
	}

	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		for _, id := range req.Ids {
			err := RemoveLabelFromChain(tx, id)
			if err != nil {
				return err
			}
		}

		// 删除标签
		err := tx.Model(&model.TCustomLabel{}).
			Where("id IN ?", req.Ids).
			Delete(&model.TCustomLabel{}).Error
		if err != nil {
			return err
		}
		return nil
	})

	return err
}

// UpdateUserChatLabels 更新对话标签
func (a *Ai) UpdateUserChatLabels(ctx context.Context, req *aipb.ReqUpdateCustomChatLabels, rsp *empty.Empty) error {
	req.ObjectType = aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_CHAT
	return logic.UpdateObjectLabels(ctx, req)
}

// UpdateDocLabels 更新doc标签
func (a *Ai) UpdateDocLabels(ctx context.Context, req *aipb.ReqUpdateCustomChatLabels, rsp *empty.Empty) error {
	return logic.UpdateObjectLabels(ctx, req)
}

// GetCustomLabelValueTopN 获取标签绑定的topn值
func (a *Ai) GetCustomLabelValueTopN(ctx context.Context, req *aipb.ReqGetCustomLabelValueTopN, rsp *aipb.RspGetCustomLabelValueTopN) error {
	label, err := model.NewQuery[model.TCustomLabel](ctx).Where("id = ?", req.Id).Find()
	if err != nil {
		return err
	}

	uniques := make([]*model.TObjectLabel, 0)
	column := model.ObjectLabelColumnMapping[label.Type]
	err = model.NewQuery[model.TObjectLabel](ctx).Where("label_id = ?", req.Id).DB().Limit(int(req.TopN)).
		Distinct(column).Find(&uniques).Error
	if err != nil {
		return err
	}

	for _, v := range uniques {
		v.LabelType = label.Type
		rsp.Values = append(rsp.Values, v.ToLabelPb().GetValue())
	}
	return nil
}

// ConvertCustomLabel 转换标签类型
func (a *Ai) ConvertCustomLabel(ctx context.Context, req *aipb.ReqConvertCustomLabel, rsp *aipb.RspConvertCustomLabel) error {
	source, err := model.NewQuery[model.TCustomLabel](ctx).Where("id = ?", req.Id).Find()
	if err != nil {
		return err
	}

	target := new(model.TCustomLabel).FromProto(req.Target, aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_UNSPECIFIED)
	l := logic.NewLabelConvertLogic(ctx, source, target, req.DryRun)

	affected, err := l.ConvertLabelValue()
	if err != nil {
		return err
	}
	err = l.ResetLabelInfo()
	if err != nil {
		return err
	}

	rsp.Deleted = uint32(affected.Deleted)
	rsp.Reserved = uint32(affected.Reserved)
	return nil
}
