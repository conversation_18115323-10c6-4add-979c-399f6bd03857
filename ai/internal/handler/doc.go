package handler

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// GetDocs 查询文档列表
func (a *Ai) GetDocs(ctx context.Context, req *aipb.ReqGetDocs, rsp *aipb.RspGetDocs) error {
	docs, err := model.NewQuery[model.TDoc](ctx).
		Select("id", "data_type", "file_name", "index_text_cerpt as index_text", "data_source").
		GetByKey(req.DocId)
	if err != nil {
		return fmt.Errorf("query docs: %w", err)
	}

	rsp.Docs = model.TDocs(docs).ToChatMessageDocs()

	return nil
}
