package handler

import (
	"context"
	"errors"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
)

// CreateDocShareConfigSender 创建助手发送方设置
func (a *Ai) CreateDocShareConfigSender(ctx context.Context, req *ai.ReqCreateDocShareConfigSender, rsp *emptypb.Empty) error {
	// 先删除旧配置
	if _, err := model.NewQuery[model.TShareSender](ctx).
		DeleteBy("admin_type = ? and create_by = ?", req.AdminType, req.UserId); err != nil {
		return err
	}

	assistants, err := model.NewQuery[model.TAssistant](ctx).GetBy("id in ?", req.ShareAssistantId)
	if err != nil {
		return err
	}

	var assistantIds []uint64
	for _, assistant := range assistants {
		assistantIds = append(assistantIds, assistant.ID)
	}

	var shareSenders []*model.TShareSender
	for _, assistantId := range assistantIds {

		sender := &model.TShareSender{
			ReceiverID:   assistantId,
			ReceiverType: ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_ASSISTANT,
			AdminType:    uint32(req.AdminType),
			CreateBy:     req.UserId,
		}

		shareSenders = append(shareSenders, sender)

	}
	for _, v := range req.ShareUserId {
		shareSenders = append(shareSenders, &model.TShareSender{
			ReceiverID:   v,
			ReceiverType: ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER,
			AdminType:    uint32(req.AdminType),
			CreateBy:     req.UserId,
		})
	}
	for _, v := range req.ShareTeamId {
		shareSenders = append(shareSenders, &model.TShareSender{
			ReceiverID:   v,
			ReceiverType: ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM,
			AdminType:    uint32(req.AdminType),
			CreateBy:     req.UserId,
		})
	}

	if err := model.NewQuery[model.TShareSender](ctx).BatchInsert(shareSenders, 100); err != nil {
		return err
	}

	return nil
}

// ListeDocShareConfigSender 获取助手发送方设置
func (a *Ai) ListeDocShareConfigSender(ctx context.Context, req *ai.ReqListeDocShareConfigSender, rsp *ai.RspListeDocShareConfigSender) error {
	shareSenders, err := model.NewQuery[model.TShareSender](ctx).Where("admin_type = ? and create_by = ?",
		req.AdminType, req.CreateBy).Get()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	// 过滤掉自己管理的助手
	assistantAdmins, err := model.NewQuery[model.TAssistantAdmin](ctx).Where("admin_id = ? and admin_type = ?",
		req.CreateBy, req.AdminType).Get()
	if err != nil {
		return err
	}

	for _, myAdmin := range assistantAdmins {
		rsp.MyAdminAssistantId = append(rsp.MyAdminAssistantId, myAdmin.AssistantID)
	}

	for _, sender := range shareSenders {
		switch sender.ReceiverType {
		case ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_ASSISTANT:
			rsp.ShareAssistantId = append(rsp.ShareAssistantId, sender.ReceiverID)
		case ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER:
			rsp.ShareUserId = append(rsp.ShareUserId, sender.ReceiverID)
		case ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM:
			rsp.ShareTeamId = append(rsp.ShareTeamId, sender.ReceiverID)
		}
	}

	return nil
}

// ListMyAssistantIds 查询已经设置并开启知识库接收的助手
func (a *Ai) ListMyAssistantIds(ctx context.Context, req *ai.ReqListMyAssistantIds, rsp *ai.RspListMyAssistantIds) error {
	shareReceivers, err := model.NewQuery[model.TShareReceiver](ctx).
		Where(" create_by = ? and admin_type = ? and receiver_state = 1", req.CreateBy, req.AdminType).
		Where("admin_assistant_id <> 0").
		Where("admin_assistant_id IN (SELECT assistant_id FROM t_assistant_admin WHERE admin_id = ? AND admin_type = ?)",
			req.CreateBy, req.AdminType).
		OrderBy("update_date", true).
		Get()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}
	//
	// subQ := model.NewQuery[model.TAssistant](ctx).Select("1").DB().
	// 	Where("t_assistant.id = t_share_receiver.admin_assistant_id")
	// shareReceivers, err := model.NewQuery[model.TShareReceiver](ctx).
	// 	Where(" create_by = ? and admin_type = ? and receiver_state = 1", req.CreateBy, req.AdminType).
	// 	OrderBy("update_date", true).
	// 	Where("EXISTS (?)", subQ).
	// 	Get()
	// if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
	// 	return err
	// }

	for _, receiver := range shareReceivers {
		rsp.ShareAssistantIds = append(rsp.ShareAssistantIds, receiver.AdminAssistantID)
	}

	return nil
}

// ListAssistantCanShareDoc 查询可分享的助手列表
func (a *Ai) ListAssistantCanShareDoc(ctx context.Context, req *ai.ReqListAssistantCanShareDoc, rsp *ai.RspListAssistantCanShareDoc) error {
	assistants := make([]*struct {
		Id     uint64 `gorm:"column:id" json:"id"`
		Name   string `gorm:"column:name" json:"name"`
		NameEn string `gorm:"column:name_en" json:"name_en"`
	}, 0)

	var teamOrUser string
	switch req.AdminType {
	case basepb.IdentityType_IDENTITY_TYPE_TEAM:
		teamOrUser = "`team_id`"
	default:
		teamOrUser = "`user_id`"
	}

	args := []interface{}{req.CreateBy, req.AdminType, req.CreateBy, req.CreateBy}
	sql := "SELECT `id` , `name`, `name_en`  FROM `t_assistant`  WHERE (id " +
		// 在接收分享助手列表
		"IN (  SELECT DISTINCT(`admin_assistant_id`)   FROM `t_share_receiver`  WHERE receiver_state = 1)" +
		// 不是自己的助手
		"AND id NOT IN (  SELECT `assistant_id`  FROM `t_assistant_admin`   WHERE `admin_id` = ? AND `admin_type` = ? )" +
		// 检查其余贡献者，是否不接受分享
		"AND id NOT IN (  SELECT `admin_assistant_id` FROM t_share_receiver tsr WHERE tsr.other_state = 3 AND NOT EXISTS (SELECT 1 FROM t_share_receiver_user tsru " +
		" WHERE tsru.admin_assistant_id = tsr.admin_assistant_id AND " + "tsru." + teamOrUser + " = ? ))" +
		// 个性化配置排除他人
		"AND id NOT IN (  SELECT `admin_assistant_id` FROM `t_share_receiver_user`  WHERE `state` = 3 AND " + teamOrUser + " = ?)) "

	if len(req.Name) > 0 && req.Language == "en" {
		sql += "AND name_en like ? "
		args = append(args, "%"+xorm.EscapeLikeWildcards(req.Name)+"%")
	} else if len(req.Name) > 0 {
		sql += "AND name like ? "
		args = append(args, "%"+xorm.EscapeLikeWildcards(req.Name)+"%")
	}

	// 过滤排序
	sql += "AND show_type = 1 AND deleted_at is NULL ORDER BY id desc "
	err := model.NewConnection(ctx).Raw(sql, args...).Scan(&assistants).Error

	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if len(assistants) == 0 {
		return nil
	}

	var assistantIds []uint64
	for _, assistant := range assistants {
		assistantIds = append(assistantIds, assistant.Id)
	}

	// 获取知识库和助手的关系
	assistantShareStateMaps := make(map[uint64]bool)
	assistantToRemoveAssistantMaps := make(map[uint64]bool)

	assistantDocs, err := model.NewQuery[model.TAssistantDoc](ctx).Where("assistant_id IN (?) and doc_id = ?", assistantIds, req.DocId).Get()
	if err != nil {
		return err
	}

	for _, ass := range assistantDocs {
		switch ass.IsShared {
		case 1:
			assistantToRemoveAssistantMaps[ass.AssistantID] = true
		case 2:
			assistantShareStateMaps[ass.AssistantID] = true
		}
	}

	for _, assistant := range assistants {
		// jh提出: 知识库和助手的关系， share = 1 & doc_id > 0 , 需要过滤assistant_id ，
		if !assistantToRemoveAssistantMaps[assistant.Id] {
			rsp.Assistants = append(rsp.Assistants,
				&ai.RspListAssistantCanShareDoc_SharedAssistant{
					Id:         assistant.Id,
					Name:       assistant.Name,
					NameEn:     assistant.NameEn,
					IsSelected: assistantShareStateMaps[assistant.Id],
				},
			)
		}
	}

	return nil
}

// ListTeamCanShareDoc 查看可分享知识的助手列表
func (a *Ai) ListTeamCanShareDoc(ctx context.Context, req *ai.ReqListTeamCanShareDoc, rsp *ai.RspListTeamCanShareDoc) error {
	receivers, err := logic.NewDocShareConfigReceiverLogic().GetTeamCanShareDoc(ctx, &logic.DocShareSender{
		Type: req.AdminType,
		ID:   req.CreateBy,
	}, req.Name, req.Offset, req.Limit)
	if err != nil {
		return err
	}
	teamIds := make([]uint64, 0, len(receivers))
	for _, receiver := range receivers {
		teamIds = append(teamIds, receiver.Id)
	}

	if len(teamIds) == 0 {
		return nil
	}

	var teams []*model.TDocShare
	if req.DocId > 0 {
		// 判断是否已经选择
		teams, err = model.NewQuery[model.TDocShare](ctx).
			Where("target_id in (?)", teamIds).
			Where("share_type = ?", ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM).
			Where("doc_id = ?", req.DocId).
			Get()
		if err != nil {
			return err
		}
	}
	for _, v := range receivers {
		// 不能分享给自己
		if req.AdminType == basepb.IdentityType_IDENTITY_TYPE_TEAM && v.Id == req.CreateBy {
			continue
		}
		item := &ai.RspListTeamCanShareDoc_Teams{
			Id:     v.Id,
			Name:   v.Name,
			NameEn: v.NameEn,
		}
		for _, team := range teams {
			if team.TargetID == v.Id {
				item.IsSelected = true
			}
		}
		rsp.Teams = append(rsp.Teams, item)
	}

	return nil
}

// ListUserCanShareDoc 查询可分享知识的个人列表
func (a *Ai) ListUserCanShareDoc(ctx context.Context, req *ai.ReqListUserCanShareDoc, rsp *ai.RspListUserCanShareDoc) error {
	receivers, err := logic.NewDocShareConfigReceiverLogic().GetUserCanShareDoc(ctx, &logic.DocShareSender{
		Type: req.AdminType,
		ID:   req.CreateBy,
	}, req.UserId)
	if err != nil {
		return err
	}

	userIds := make([]uint64, 0, len(receivers))
	for _, receiver := range receivers {
		userIds = append(userIds, receiver.Id)
	}

	if len(userIds) == 0 {
		return nil
	}

	// 判断是否已经选择
	var users []*model.TDocShare
	if req.DocId > 0 {
		// 如果有文档ID，则根据文档ID查询
		users, err = model.NewQuery[model.TDocShare](ctx).
			Where("target_id in (?)", userIds).
			Where("share_type = ?", ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER).
			Where("doc_id = ?", req.DocId).
			Get()
		if err != nil {
			return err
		}
	}

	for _, v := range receivers {
		// 不能分享给自己
		if req.AdminType == basepb.IdentityType_IDENTITY_TYPE_USER && v.Id == req.CreateBy {
			continue
		}
		item := &ai.RspListUserCanShareDoc_Users{
			Id:   v.Id,
			Name: v.Name,
		}
		for _, user := range users {
			if user.TargetID == v.Id {
				item.IsSelected = true
			}
		}
		rsp.Users = append(rsp.Users, item)
	}
	return nil
}

// CreateDocShareConfigReceiverAssistant 创建助手接收方设置
func (a *Ai) CreateDocShareConfigReceiverAssistant(ctx context.Context, req *ai.ReqCreateDocShareConfigReceiverAssistant, empty *emptypb.Empty) error {
	isMyAssistant, err := logic.IsMyAssistant(ctx, req.AssistantId, req.AdminType, req.CreateBy)
	if err != nil {
		return err
	}

	if !isMyAssistant {
		log.WithContext(ctx).Infow("CreateDocShareConfigReceiverAssistant IsMyAssistant empty", "AssistantId", req.AssistantId,
			"AdminType", req.AdminType, "CreateBy", req.CreateBy)
		return nil
	}

	// 查出原始数据
	oldReceiver, err := model.NewQuery[model.TShareReceiver](ctx).Where("admin_assistant_id = ? ", req.AssistantId).Find()

	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	// 同数据删除
	if _, err := model.NewQuery[model.TShareReceiver](ctx).DeleteBy("admin_assistant_id = ? ", req.AssistantId); err != nil {
		return err
	}

	receiver := &model.TShareReceiver{
		AdminAssistantID: req.AssistantId,
		AdminType:        req.AdminType,
		CreateBy:         req.CreateBy,
		ReceiverState:    req.ReceiverState,
		OtherState:       req.OtherState,
	}
	if err := model.NewQuery[model.TShareReceiver](ctx).Create(receiver); err != nil {
		return err
	}

	// 关联数据删除
	if oldReceiver != nil && oldReceiver.ID != 0 {
		if _, err := model.NewQuery[model.TShareReceiverUser](ctx).DeleteBy(" receiver_id = ?",
			oldReceiver.ID); err != nil {
			return err
		}
	}

	// 设置数据少for循环依次处理
	for groupId, userShare := range req.UserShares {

		// groupId 用于分块显示
		groupId += 1

		var receiverUsers []*model.TShareReceiverUser

		for _, u := range userShare.UserId {
			receiverUser := &model.TShareReceiverUser{
				ReceiverID:       receiver.ID,
				AdminAssistantID: req.AssistantId,
				UserID:           u,
				State:            userShare.State,
				GroupID:          uint64(groupId),
			}

			receiverUsers = append(receiverUsers, receiverUser)
		}

		for _, t := range userShare.TeamId {
			receiverUser := &model.TShareReceiverUser{
				ReceiverID:       receiver.ID,
				AdminAssistantID: req.AssistantId,
				TeamID:           t,
				State:            userShare.State,
				GroupID:          uint64(groupId),
			}

			receiverUsers = append(receiverUsers, receiverUser)
		}

		if err := model.NewQuery[model.TShareReceiverUser](ctx).BatchInsert(receiverUsers, 100); err != nil {
			return err
		}

	}

	return nil
}

// mergedShare 用于合并
type mergedShare struct {
	GroupId uint64
	State   ai.DocShareState
	UserIds []uint64
	TeamIds []uint64
}

// CreateDocShareConfigReceiverUserTeam 创建个人/团队接收方设置
func (a *Ai) CreateDocShareConfigReceiverUserTeam(ctx context.Context, req *ai.ReqCreateDocShareConfigReceiverUserTeam, empty *emptypb.Empty) error {
	logic.NewDocShareConfigReceiverLogic().
		CreateUserTeamConfig(ctx, req.CreateBy, req.AdminType, req.ReceiverState, req.OtherState, req.UserShares)
	return nil
}

// ListDocShareConfigReceiverAssistant 获取助手接收方设置
func (a *Ai) ListDocShareConfigReceiverAssistant(ctx context.Context, req *ai.ReqListDocShareConfigReceiverAssistant,
	rsp *ai.RspListDocShareConfigReceiverAssistant,
) error {
	isMyAssistant, err := logic.IsMyAssistant(ctx, req.AssistantId, req.AdminType, req.CreateBy)
	if err != nil {
		return err
	}

	if !isMyAssistant {
		log.WithContext(ctx).Infow("CreateDocShareConfigReceiverAssistant IsMyAssistant empty", "AssistantId", req.AssistantId,
			"AdminType", req.AdminType, "CreateBy", req.CreateBy)
		return nil
	}

	oldReceiver, err := model.NewQuery[model.TShareReceiver](ctx).Where("admin_assistant_id = ? ", req.AssistantId).Find()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if oldReceiver == nil {
		return nil
	}

	shareReceiverUsers, err := model.NewQuery[model.TShareReceiverUser](ctx).Where("receiver_id = ?  ",
		oldReceiver.ID).Get()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	rsp.AssistantId = oldReceiver.AdminAssistantID
	rsp.ReceiverState = oldReceiver.ReceiverState
	rsp.OtherState = oldReceiver.OtherState

	teamMap := make(map[uint64][]uint64)
	teamStateMap := make(map[uint64]ai.DocShareState)
	userMap := make(map[uint64][]uint64)
	userStateMap := make(map[uint64]ai.DocShareState)

	for _, v := range shareReceiverUsers {
		if v.TeamID > 0 {
			teamMap[v.GroupID] = append(teamMap[v.GroupID], v.TeamID)
			teamStateMap[v.GroupID] = v.State
		}

		if v.UserID > 0 {
			userMap[v.GroupID] = append(userMap[v.GroupID], v.UserID)
			userStateMap[v.GroupID] = v.State
		}
	}

	// 通过group_id 将team_id user_id 聚合在一起
	mergedMap := make(map[uint64]*mergedShare)

	for k := range teamMap {
		if _, exists := mergedMap[k]; !exists {
			mergedMap[k] = &mergedShare{
				GroupId: k,
				State:   teamStateMap[k],
				TeamIds: teamMap[k],
			}
		} else {
			mergedMap[k].TeamIds = append(mergedMap[k].TeamIds, teamMap[k]...)
		}
	}

	for k := range userStateMap {
		if _, exists := mergedMap[k]; !exists {
			mergedMap[k] = &mergedShare{
				GroupId: k,
				State:   userStateMap[k],
				UserIds: userMap[k],
			}
		} else {
			mergedMap[k].UserIds = append(mergedMap[k].UserIds, userMap[k]...)
		}
	}

	// 将合并后的数据添加到 rsp.UserShares 中
	for _, merged := range mergedMap {
		rsp.UserShares = append(rsp.UserShares, &ai.RspListDocShareConfigReceiverAssistant_UserShare{
			GroupId: merged.GroupId,
			State:   merged.State,
			UserId:  merged.UserIds,
			TeamId:  merged.TeamIds,
		})
	}

	return nil
}

// ListDocShareConfigReceiverUserTeam 查询个人/团队知识分享接收设置
func (a *Ai) ListDocShareConfigReceiverUserTeam(ctx context.Context, req *ai.ReqListDocShareConfigReceiverUserTeam,
	rsp *ai.RspListDocShareConfigReceiverUserTeam,
) error {
	shareReceiver, err := model.NewQuery[model.TShareReceiver](ctx).
		With("ReceiverUserDetail").
		Where("admin_assistant_id = 0").
		Where("create_by = ? and admin_type = ?", req.CreateBy, req.AdminType).Find()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if shareReceiver == nil {
		return nil
	}
	rsp.ReceiverState = shareReceiver.ReceiverState
	rsp.OtherState = shareReceiver.OtherState

	teamMap := make(map[uint64][]uint64)
	teamStateMap := make(map[uint64]ai.DocShareState)
	userMap := make(map[uint64][]uint64)
	userStateMap := make(map[uint64]ai.DocShareState)

	for _, v := range shareReceiver.ReceiverUserDetail {
		if v.TeamID > 0 {
			teamMap[v.GroupID] = append(teamMap[v.GroupID], v.TeamID)
			teamStateMap[v.GroupID] = v.State
		}

		if v.UserID > 0 {
			userMap[v.GroupID] = append(userMap[v.GroupID], v.UserID)
			userStateMap[v.GroupID] = v.State
		}
	}

	// 通过group_id 将team_id user_id 聚合在一起
	mergedMap := make(map[uint64]*mergedShare)

	for k := range teamMap {
		if _, exists := mergedMap[k]; !exists {
			mergedMap[k] = &mergedShare{
				GroupId: k,
				State:   teamStateMap[k],
				TeamIds: teamMap[k],
			}
		} else {
			mergedMap[k].TeamIds = append(mergedMap[k].TeamIds, teamMap[k]...)
		}
	}

	for k := range userMap {
		if _, exists := mergedMap[k]; !exists {
			mergedMap[k] = &mergedShare{
				GroupId: k,
				State:   userStateMap[k],
				UserIds: userMap[k],
			}
		} else {
			mergedMap[k].UserIds = append(mergedMap[k].UserIds, userMap[k]...)
		}
	}

	// 将合并后的数据添加到 rsp.UserShares 中
	for _, merged := range mergedMap {
		rsp.UserShares = append(rsp.UserShares, &ai.RspListDocShareConfigReceiverUserTeam_UserShare{
			GroupId: merged.GroupId,
			State:   merged.State,
			UserId:  merged.UserIds,
			TeamId:  merged.TeamIds,
		})
	}
	return nil
}

// CreateDocShare 创建知识库分享至助手、个人、团队
func (a *Ai) CreateDocShare(ctx context.Context, req *ai.ReqCreateDocShare,
	rsp *ai.RspCreateDocShare,
) error {
	if req.QueryId > 0 {
		asyncTask, err := logic.CreateDocShareBatchTask(ctx, req)
		rsp.Async = asyncTask
		return err
	}

	return logic.DoCreateDocShare(ctx, req)
}

// CreateDocShareAssistant 创建知识库同步or取消至助手（保持向后兼容性）
func (a *Ai) CreateDocShareAssistant(ctx context.Context, req *ai.ReqCreateDocShare,
	rsp *ai.RspCreateDocShare,
) error {
	return a.CreateDocShare(ctx, req, rsp)
}
