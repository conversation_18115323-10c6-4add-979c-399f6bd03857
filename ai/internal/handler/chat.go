package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	chatlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/chat"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"github.com/golang/protobuf/ptypes/empty"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// StopQuestionReply 停止回答在问题上
func (a *Ai) StopQuestionReply(ctx context.Context, req *aipb.ReqStopQuestionReply, rsp *aipb.RspStopQuestionReply) error {
	stopAnswer, _ := model.NewQuery[model.TChatMessage](ctx).FindBy("question_id = ? and type = ?", req.MessageId, aipb.ChatMessageType_CHAT_MESSAGE_TYPE_CANCEL_ERROR)
	if stopAnswer != nil { // 该问题已生成停止回答的答案，不继续生成
		return nil
	}

	question, _ := model.NewQuery[model.TChatMessage](ctx).FindBy("id = ?", req.MessageId)
	if question == nil {
		return xerrors.NotFoundError("question not found")
	}

	// 创建操作日志
	if err := model.NewQuery[model.TChatOperation](ctx).DB().Create(&model.TChatOperation{
		ChatID:        question.ChatID,
		OperationType: uint32(aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_TEXT),
		MessageID:     req.MessageId,
	}).Error; err != nil {
		return err
	}

	// 创建默认答案
	answer := &aipb.ChatMessage{
		ChatId:      question.ChatID,
		Type:        aipb.ChatMessageType_CHAT_MESSAGE_TYPE_CANCEL_ERROR,
		Text:        config.GetStringOr("llm.chat.question_cancel_default_answer", "已暂停生成"),
		CreateBy:    question.CreateBy,
		QuestionId:  question.ID,
		StartTime:   timestamppb.New(time.Now()),
		EndTime:     timestamppb.New(time.Now()),
		AssistantId: question.AssistantID,
	}

	msg, err := chatlogic.NewChatMessage(answer).Save(ctx)
	if err != nil {
		return err
	}
	answer.Id = msg.ID
	rsp.Message = answer
	return nil
}

// StopAnswerReply 停止answer回答
func (a *Ai) StopAnswerReply(ctx context.Context, req *aipb.ReqStopAnswerReply, rsp *aipb.RspStopAnswerReply) error {
	answer, _ := model.NewQuery[model.TChatMessage](ctx).FindBy("id = ? and hash_id = ?", req.MessageId, req.HashId)
	if answer == nil {
		return xerrors.NotFoundError("answer not found")
	}

	operaType := uint32(aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_TEXT)
	if req.StopChunkState == int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK) {
		operaType = uint32(aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_THINK)
	}

	if err := model.NewQuery[model.TChatOperation](ctx).DB().Create(&model.TChatOperation{
		ChatID:         answer.ChatID,
		OperationType:  operaType,
		HashID:         req.HashId,
		StopText:       req.StopText,
		StopThink:      req.StopThink,
		StopChunkState: req.StopChunkState,
		MessageID:      answer.ID,
	}).Error; err != nil {
		return err
	}
	rsp.Id = answer.ID
	return nil
}

// CreateUserChat 创建会话
func (a *Ai) CreateUserChat(ctx context.Context, req *aipb.ReqCreateUserChat, rsp *aipb.RspCreateUserChat) error {
	chat, err := chatlogic.CreateChat(ctx, req)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	rsp.Id = chat.ID
	return nil
}

// DescribeMessage 获取消息详情
func (a *Ai) DescribeMessage(ctx context.Context, req *aipb.ReqDescribeMessage, rsp *aipb.RspDescribeMessage) error {
	var message *model.TChatMessage
	query := model.NewQuery[model.TChatMessage](ctx).DB()
	if req.WithDocs {
		query.Preload("Docs")
	}
	if req.WithFeedback {
		query.Preload("Feedback")
	}

	if req.WithCollection {
		query.Preload("Collections")
	}

	if err := query.Preload("Think").First(&message, "id = ?", req.MessageId).Error; err != nil {
		return xerrors.BadRequestError(err)
	}
	if message == nil || message.ChatID == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	var err error
	chatMessage, err := message.TransformToAiMessage()
	if err != nil {
		return err
	}
	rsp.Message = chatMessage
	return nil
}

// FixSearchCollectionItems 修复search collection数据
func (a *Ai) FixSearchCollectionItems(ctx context.Context, req *aipb.ReqFixSearchCollectionItems, rsp *aipb.RspFixSearchCollectionItems) error {
	if len(req.Items) == 0 || req.Items[0].DocId > 0 {
		return xerrors.NotFoundError("no items need to fix")
	}

	items, err := chatlogic.QueryDocsByCollectionItems(ctx, req.Items)
	if err != nil {
		log.WithContext(ctx).Infow("query docs by collection items failed", "err", err)
	}

	rsp.Items = items
	return nil
}

// DescribeMessageByQuestionId 获取问题详情
func (a *Ai) DescribeMessageByQuestionId(ctx context.Context, req *aipb.ReqDescribeMessageByQuestionId, rsp *aipb.RspDescribeMessageByQuestionId) error {
	dbm, _ := model.NewQuery[model.TChatMessage](ctx).FindBy("question_id", req.QuestionId)
	if dbm != nil {
		message, err := dbm.TransformToAiMessage()
		if err != nil {
			return xerrors.BadRequestError(err)
		}
		rsp.Message = message
	}
	return nil
}

// CreateChatTaskMessage 根据assistant的agent的生成回答
func (a *Ai) CreateChatTaskMessage(ctx context.Context, req *aipb.ReqCreateChatTaskMessage, rsp *aipb.RspCreateChatTaskMessage) error {
	var tasks []*aipb.ChatAgentTask

	// 过滤出文本和多模态类型的任务
	for _, task := range req.Tasks {
		if task.FetchType != aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_UNSPECIFIED && task.FetchType != aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_MATCH_QA {
			tasks = append(tasks, task)
		}
	}

	// 组装mainTask
	var mainTask []*chatlogic.ChatTask
	for _, task := range tasks {
		if task.PreTaskId == 0 {
			mt := chatlogic.TransAgentTaskToChatTask(ctx, task, req.Message, req.AssistantDetail)
			mainTask = append(mainTask, mt)
		}
	}

	for _, task := range tasks {
		for _, mt := range mainTask {
			if mt.Id == task.PreTaskId {
				nextTask := chatlogic.TransAgentTaskToChatTask(ctx, task, nil, req.AssistantDetail)
				mt.SetNextTask(nextTask)
			}
		}
	}

	// 任务链获取答案
	if len(mainTask) > 0 {
		answers, err := chatlogic.GetChatTasksAnswer(ctx, req.AssistantDetail, mainTask)
		if err != nil {
			return xerrors.BadRequestError(err)
		}
		rsp.Messages = answers
		return nil
	}

	// 非任务链获取答案
	message, err := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
		ReqMessage: chatlogic.NewChatMessage(req.Message),
	}).CreatChatTaskChainAnswer(req.AssistantDetail).Save()
	if err != nil {
		return xerrors.BadRequestError(err)
	}
	rsp.Messages = []*aipb.ChatMessage{message}
	return nil

}

// SendMessagesSync 消息发送同步接口,只处理命令（已弃用）
func (a *Ai) SendMessagesSync(ctx context.Context, req *aipb.ReqSendMessageSync, rsp *aipb.RspSendMessagesSync) error {
	var questionId = req.Message.Id

	if len(req.Message.ImageUrl) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	timeoutText := config.GetStringOr("miniprogram.ai.timeoutDefaultAnswer",
		"抱歉，我走神儿了，可以尝试重新提问。或者通过发送邮件至****************联系人工服务")

	var tasks []*chatlogic.ChatTask

	switch req.Message.Text {
	case chatlogic.MessageCMDReadBom, chatlogic.MessageCMDReadBomPicture, chatlogic.MessageCMDDecipherBom: // 读配料表
		reqQuestion := chatlogic.NewChatMessage(req.Message).CloseChatOrSql()
		reqQuestion.ImageUrl = req.Message.ImageUrl
		reqQuestion.Text = req.AssistantDetail.MultimodalPrompt.ReadBom
		reqQuestion.DefaultText = config.GetStringOr("miniprogram.ai.nullBomAnswer", "没有识别到配料哦，请换个照片试试呢")
		reqQuestion.WaitAnswer = true
		reqQuestion.QuestionId = questionId
		task := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
			FetchType:       aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
			ReqMessage:      reqQuestion,
			NoHistoryRounds: true,
		})
		task.SetNextTask(&chatlogic.ChatTask{
			FetchType:       aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT,
			NoHistoryRounds: true,
			BuildReqMessage: func(msg *chatlogic.ChatMessage) *chatlogic.ChatMessage {
				msg.PromptPrefix = req.AssistantDetail.MultimodalPrompt.BomHazards
				msg.DefaultText = timeoutText

				return msg
			},
		})
		tasks = []*chatlogic.ChatTask{task}
	case chatlogic.MessageCMDReadReport, chatlogic.MessageCMDDecipherReport: // 读检测报告
		// 识别认证标识
		question1 := chatlogic.NewChatMessage(req.Message).CloseChatOrSql().SetText(req.AssistantDetail.MultimodalPrompt.ComplianceMark)
		question1.DefaultText = config.GetStringOr("miniprogram.ai.nullReportAnswer", "没有识别到报告哦，请换个文件试试呢")
		question1.WaitAnswer = true
		question1.QuestionId = questionId
		task := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
			FetchType:       aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
			ReqMessage:      question1,
			NoHistoryRounds: true,
		})
		task.SetNextTask(&chatlogic.ChatTask{
			FetchType:       aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT,
			NoHistoryRounds: true,
			BuildReqMessage: func(msg *chatlogic.ChatMessage) *chatlogic.ChatMessage {
				msg.PromptPrefix = req.AssistantDetail.MultimodalPrompt.AskMark
				msg.DefaultText = timeoutText
				return msg
			},
		})
		// 读检测报告
		question2 := chatlogic.NewChatMessage(req.Message).CloseChatOrSql().SetText(req.AssistantDetail.MultimodalPrompt.ReadTestReport)
		question2.DefaultText = config.GetStringOr("miniprogram.ai.nullReportAnswer", "没有识别到报告哦，请换个文件试试呢")
		question2.QuestionId = questionId
		task2 := &chatlogic.ChatTask{
			FetchType:       aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
			ReqMessage:      question2,
			NoHistoryRounds: true,
		}

		tasks = []*chatlogic.ChatTask{task2, task}
	default:
		reqQuestion := chatlogic.NewChatMessage(req.Message)
		reqQuestion.ImageUrl = req.Message.ImageUrl
		reqQuestion.DefaultText = config.GetStringOr("miniprogram.ai.nullUserCmdAnswer", "照片无法识别，请换个照片试试呢")
		reqQuestion.QuestionId = questionId
		task := &chatlogic.ChatTask{
			FetchType:       aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
			ReqMessage:      reqQuestion,
			NoHistoryRounds: true,
		}
		tasks = []*chatlogic.ChatTask{task}
	}

	answers, err := chatlogic.GetChatTasksAnswer(ctx, req.AssistantDetail, tasks)
	rsp.Messages = answers
	return err
}

// ResendMessageSync 重发消息 (已弃用）
func (a *Ai) ResendMessageSync(ctx context.Context, req *aipb.ReqResendMessageSync, rsp *aipb.RspResendMessageSync) error {
	if req.ClearAnswer {
		if err := a.DeleteChatMessage(ctx, &aipb.ReqDeleteChatMessage{
			QuestionId: req.QuestionId,
		}, nil); err != nil {
			return xerrors.InternalServerError(err)
		}
	}
	tasks, err := chatlogic.GetResendChatTask(ctx, req.QuestionId, req.PublishHashId)
	if err != nil {
		return err
	}

	if tasks == nil || len(tasks) == 0 { // 上一个答案都还没生成
		var question *model.TChatMessage
		if err := model.NewQuery[model.TChatMessage](ctx).DB().First(&question, "id = ?", req.QuestionId).Error; err != nil {
			return xerrors.BadRequestError(err)
		}
		pbQuestion, err := question.TransformToAiMessage()
		if err != nil {
			return err
		}
		pbQuestion.PublishHashId = req.PublishHashId
		var res = &aipb.RspSendMessageSync{}
		err = a.SendMessageSync(ctx, &aipb.ReqSendMessageSync{
			AssistantDetail: req.AssistantDetail,
			Message:         pbQuestion,
		}, res)
		if err != nil {
			return err
		}
		if res != nil {
			rsp.Message = []*aipb.ChatMessage{res.Message}
		}
		return nil
	}

	wg := xsync.NewGroup(context.Background(), xsync.GroupGoOption(boot.TraceGo(ctx)))
	var messages = make([]*aipb.ChatMessage, len(tasks))
	for i, task := range tasks {
		wg.SafeGo(func(ctx context.Context) error {
			msg, err := task.CreatChatTaskChainAnswer(req.AssistantDetail).Save()
			if err != nil {
				return err
			}
			messages[i] = msg
			return nil
		})
	}
	wg.Wait()
	rsp.Message = messages
	return nil
}

// ParseChatDoc 将对话中的文件转换成文本
func (a *Ai) ParseChatDoc(ctx context.Context, req *aipb.ReqParseChatDoc, rsp *aipb.RspParseChatDoc) error {
	if len(req.Files) == 0 {
		return nil
	}
	var urls []string
	for _, file := range req.Files {
		if file.Url != "" {
			urls = append(urls, file.Url)
		}
	}

	// 2. 准备批量插入的数据
	var newFiles []*model.TChatMessageFile
	var fileIds []uint64

	for _, file := range req.Files {
		url := file.Url
		docType, _ := logic.DocFileTypeBySuffix(url)
		// 创建新记录
		if file.State == 0 {
			file.State = aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSING
		}
		newFiles = append(newFiles, &model.TChatMessageFile{
			MessageID: req.MessageId,
			URL:       url,
			State:     file.State,
			Type:      int32(docType),
			ParsedURL: file.ParsedUrl,
			ID:        file.Id,
		})
	}

	// 3. 批量插入数据
	if err := model.NewQuery[model.TChatMessageFile](ctx).DB().Clauses(clause.OnConflict{UpdateAll: true}).
		CreateInBatches(newFiles, 100).Error; err != nil {
		log.WithContext(ctx).Errorw("Batch create TChatMessageFile failed", "err", err)
		return err
	}

	ctx = context.Background()
	for _, file := range newFiles {
		file := file
		fileIds = append(fileIds, file.ID)
		if file.State == aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS || file.State == aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_FAILED {
			continue
		}
		xsync.SafeGo(ctx, func(ctx context.Context) error {
			state := aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS
			parseText, err := chatlogic.ChatMessageFileToParse(ctx, file)
			if err != nil {
				log.WithContext(ctx).Errorw("ChatMessageFileToParse failed", "err", err)
				state = aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_FAILED
			}
			if err = chatlogic.UpdateFileParseState(ctx, file.ID, state, parseText); err != nil {
				return err
			}
			return nil
		}, boot.TraceGo(ctx))
	}
	rsp.Ids = fileIds
	return nil
}

// DescribeChatMessageFileState 查询文件解析状态
func (a *Ai) DescribeChatMessageFileState(ctx context.Context, req *aipb.ReqDescribeChatMessageFileState, rsp *aipb.RspDescribeChatMessageFileState) error {
	var files []model.TChatMessageFile
	err := model.NewQuery[model.TChatMessageFile](ctx).DB().
		Select("id, state, url").
		Where("message_id = ?", req.MessageId).
		Find(&files).Error
	if err != nil {
		log.WithContext(ctx).Errorw("Query chat message files failed", "err", err)
		return nil
	}

	rsp.Files = make([]*aipb.ChatMessageFile, len(files))
	for i, file := range files {
		rsp.Files[i] = &aipb.ChatMessageFile{
			Id:    file.ID,
			State: file.State,
			Url:   file.URL,
		}
	}

	return nil
}

// SendMessageSync 消息发送同步接口（弃用）
func (a *Ai) SendMessageSync(ctx context.Context, req *aipb.ReqSendMessageSync, rsp *aipb.RspSendMessageSync) error {
	m := chatlogic.NewChatMessage(req.Message)

	fetchType := aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT
	if len(req.Message.ImageUrl) > 0 {
		fetchType = aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION
	}
	m.DefaultText = req.AssistantDetail.MissReply
	message, err := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
		FetchType:  fetchType,
		ReqMessage: m,
	}).CreatChatTaskChainAnswer(req.AssistantDetail).Save()
	if err != nil {
		return err
	}
	rsp.Message = message
	return nil
}

// SendMessageWithoutSaveSync 消息发送同步接口
func (a *Ai) SendMessageWithoutSaveSync(ctx context.Context, req *aipb.ReqSendMessageWithoutSaveSync, rsp *aipb.RspSendMessageWithoutSaveSync) error {
	pbMsg := chatlogic.NewChatMessage(&aipb.ChatMessage{
		Text:          req.Text,
		Id:            req.QuestionId,
		AssistantId:   req.AssistantDetail.Id,
		WaitAnswer:    req.WaitAnswer,
		PublishHashId: req.HashId,
	})

	m := pbMsg.SetDisableClearGptRefMark()
	task := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
		FetchType:       aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT,
		ReqMessage:      m,
		NoHistoryRounds: true,
	}).CreatChatTaskChainAnswer(req.AssistantDetail)
	message, err := task.GetRspMessage()
	if err != nil {
		return err
	}
	rsp.Message = message
	return nil
}

// PublishChatMessage 推送event
func (a *Ai) PublishChatMessage(ctx context.Context, req *aipb.ReqPublishChatMessage, rsp *empty.Empty) error {
	var r []byte
	var err error
	var topic = config.GetStringOr("llm.channel_event", "tanlive:channel:ai")

	if !req.IsOp {
		eventMsg := chatlogic.EventMessageTransformWithHash(req.Message, req.IsOnlySearch)
		r, err = json.Marshal(eventMsg)
		if req.Message.QuestionId > 0 {
			if err := chatlogic.UpdateMessage(ctx, req.Message.QuestionId, map[string]interface{}{
				model.TChatMessageColumns.EndTime: time.Now(),
				model.TChatMessageColumns.State:   aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND,
			}); err != nil {
				return xerrors.InternalServerError(err)
			}
		}
	} else {
		r, err = json.Marshal(req.Message)
		topic = "tanlive:channel:op:ai"
	}
	if err != nil {
		log.WithContext(ctx).Errorw("PublishChatMessage", "err", err)
		return err
	}

	_ = chatlogic.SendChatEventSourceMgmt(ctx, req.UserId, int32(basepb.AIEventSource_AI_CHAT), string(r), topic)

	return nil
}

// CreateChatMessage 保存消息
func (a *Ai) CreateChatMessage(ctx context.Context, req *aipb.ReqCreateChatMessage, rsp *aipb.RspCreateChatMessage) error {
	var message *model.TChatMessage
	var err error
	reqMessage := req.Message
	reqMessage.AssistantId = req.AssistantId

	if reqMessage.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER { // 问题
		message, err = chatlogic.SaveQuestionMessage(ctx, req.Message, "", "", "", "")
		if err != nil {
			return err
		}
	} else { // 答案
		message, err = chatlogic.NewChatMessage(reqMessage).Save(ctx)
		if err != nil {
			return err
		}
	}

	rsp.Message, err = message.TransformToAiMessage()
	rsp.Message.PublishHashId = req.Message.PublishHashId
	if err != nil {
		return err
	}

	return nil
}

// CreateQaMatchMessage 创建QA匹配模式命中的答案
func (a *Ai) CreateQaMatchMessage(ctx context.Context, req *aipb.ReqCreateQaMatchMessage, rsp *aipb.RspCreateQaMatchMessage) error {
	var docNames []string
	var answerText string
	if len(req.Docs) > 0 {
		docNames = []string{req.Docs[0].RagFilename}
		answerText = req.Docs[0].Text
		for i := 1; i < len(req.Docs); i++ {
			answerText += "\n\n" + req.Docs[i].Text
			docNames = append(docNames, req.Docs[i].RagFilename)
		}
	}

	question := req.Question
	save, err := chatlogic.NewChatMessage(&aipb.ChatMessage{
		ChatId:             question.ChatId,
		CreateBy:           question.CreateBy,
		Type:               aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION,
		QuestionId:         question.Id,
		AssistantId:        question.AssistantId,
		Text:               answerText,
		DocMatchPattern:    req.MatchPattern,
		DocNames:           docNames,
		FinalQuery:         question.Text,
		PublishHashId:      question.PublishHashId,
		StartTime:          req.StartTime,
		EndTime:            req.EndTime,
		CollectionSnapshot: req.CollectionSnapshot,
		Task:               req.Task,
	}).Save(ctx)
	if err != nil {
		return err
	}
	msg, err := save.TransformToAiMessage()
	if err != nil {
		return err
	}
	msg.WaitAnswer = true
	rsp.Message = msg
	rsp.Message.PublishHashId = question.PublishHashId
	return nil
}

// DescribeChatQuestionAnswersByPage 获取用户会话的所有question和answer
func (a *Ai) DescribeChatQuestionAnswersByPage(ctx context.Context, req *aipb.ReqDescribeChatQuestionAnswersByPage, rsp *aipb.RspDescribeChatQuestionAnswersByPage) error {
	if req.UserId > 0 {
		if err := chatlogic.CheckUserChat(ctx, req.ChatId, req.UserId); err != nil {
			return xerrors.ForbiddenError("chat is not accessible")
		}
	}
	var orderBy = "create_date ASC"
	if req.IsReverse {
		orderBy = "create_date DESC"
	}
	questions, answers, total, err := chatlogic.DescribeChatQuestionAnswers(ctx, req.ChatId, req.QuestionId, req.Limit, req.Offset, orderBy)
	if err != nil {
		return err
	}
	rsp.Questions = questions
	rsp.Answers = answers
	rsp.TotalCount = uint32(total)
	return nil
}

// DescribeUserChats 获取会话列表
func (a *Ai) DescribeUserChats(ctx context.Context, req *aipb.ReqDescribeUserChats, rsp *aipb.RspDescribeUserChats) error {
	db := model.NewQuery[model.TChat](ctx).DB()
	var rows []*model.TChat
	if req.AssistantId > 0 {
		db.Where("assistant_id = ?", req.AssistantId)
	}
	if len(req.Ids) > 0 {
		db.Where("id in (?)", req.Ids)
	}

	db.Where("create_by = ?", req.UserId)
	db.Order("create_date DESC")
	var cnt int64
	if err := db.Count(&cnt).Error; err != nil {
		return err
	}
	rsp.TotalCount = uint32(cnt)

	pager := xorm.NewPaginator(req.Offset, req.Limit)
	db = (&pager).Apply(db)

	if err := db.Find(&rows).Error; err != nil {
		return err
	}
	for _, m := range rows {
		rsp.Chat = append(rsp.Chat, model.TransformChatToPb(m))
	}

	return nil
}

// DeleteUserChat 删除会话
func (a *Ai) DeleteUserChat(ctx context.Context, req *aipb.ReqDeleteUserChat, rsp *empty.Empty) error {
	if err := chatlogic.CheckUserChat(ctx, req.ChatId, req.UserId); err != nil {
		return xerrors.ForbiddenError("chat is not accessible")
	}
	if err := model.NewQuery[model.TChat](ctx).DeleteByKey(req.ChatId); err != nil {
		return err
	}
	return nil
}

// CheckChatPermission 检查会话访问权限
func (a *Ai) CheckChatPermission(ctx context.Context, req *aipb.ReqCheckChatPermission, rsp *empty.Empty) error {
	if err := chatlogic.CheckUserChat(ctx, req.ChatId, req.UserId); err != nil {
		return xerrors.ForbiddenError("chat is not accessible")
	}
	return nil
}

// DeleteChatMessage 删除消息
func (a *Ai) DeleteChatMessage(ctx context.Context, req *aipb.ReqDeleteChatMessage, _ *empty.Empty) error {
	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		if req.QuestionId > 0 {
			err := tx.Model(&model.TChatMessage{}).Delete(nil, "question_id = ?", req.QuestionId).Error
			if err != nil {
				return err
			}

			return nil
		}
		if req.MessageId > 0 {
			if err := tx.Model(&model.TChatMessage{}).Delete(&model.TChatMessage{}, "id = ?", req.MessageId).Error; err != nil {
				return err
			}
			tx.Model(&model.TChatLog{}).Delete(nil, "message_id = ?", req.QuestionId)
			return nil
		}
		return nil
	})
	return err
}

func (a *Ai) UpdateMessageText(ctx context.Context, req *aipb.ReqUpdateMessageText, _ *empty.Empty) error {
	m := &model.TChatMessage{}
	err := model.NewQuery[model.TChatMessage](ctx).Where("id = ?", req.Id).DB().First(&m).Error
	if err != nil || m == nil {
		return err
	}

	err = chatlogic.UpdateMessageTextAndThink(ctx, m, req.Text, req.Think, aipb.ChatMessageType(m.Type))
	if err != nil {
		return err
	}

	return nil
}

// UpdateChatMessageThink 更新message think
func (a *Ai) UpdateChatMessageThink(ctx context.Context, req *aipb.ReqUpdateChatMessageThink, _ *empty.Empty) error {
	updates := model.TChatMessageThink{
		MessageID: req.MessageId,
		Duration:  uint32(req.Duration),
	}

	return model.NewQuery[model.TChatMessageThink](ctx).DB().Where("message_id = ?", req.MessageId).Assign(map[string]interface{}{"duration": req.Duration}).FirstOrCreate(&updates).Error
}

// UpdateChatMessage 更新chat message
func (a *Ai) UpdateChatMessage(ctx context.Context, req *aipb.ReqUpdateChatMessage, _ *empty.Empty) error {
	var UpdateChatMessageMaskMapping = map[string]any{
		"assistant_id": "AssistantID",
		"lang":         "Lang",
		"content":      "Content",
		"state":        "State",
	}
	row := &model.TChatMessage{
		ID:          req.Id,
		AssistantID: req.AssistantId,
		Lang:        req.Lang,
		Content:     datatypes.JSON(req.Content),
		State:       int32(req.State),
	}
	var fields []any
	for _, path := range req.GetMask().GetPaths() {
		if p, ok := UpdateChatMessageMaskMapping[path]; ok {
			fields = append(fields, p)
		}
	}
	db := model.NewQuery[model.TChatMessage](ctx).DB().Model(row)
	if len(fields) > 0 {
		if err := db.Select(fields[0], fields[1:]...).Updates(row).Error; err != nil {
			return err
		}
	}
	return nil
}

// GetAssistantChatCreators 获取助手的聊天创建者列表
func (a *Ai) GetAssistantChatCreators(ctx context.Context,
	req *aipb.ReqGetAssistantChatCreators, rsp *aipb.RspGetAssistantChatCreators) error {
	var userIDs []uint64
	err := model.NewQuery[model.TChat](ctx).
		Wheres(func(db *gorm.DB) {
			db.Where("assistant_id in ?", req.AssistantIds)
			if len(req.UserIds) > 0 {
				db.Where("create_by in ?", req.UserIds)
			} else {
				db.Where("create_by > ?", 0)
			}
		}).
		Pluck("create_by", &userIDs)
	if err != nil {
		return errors.New("query t_chat: " + err.Error())
	}

	rsp.UserId = userIDs
	return nil
}

// ListChat 运营端获取AI对话列表
func (a *Ai) ListChat(ctx context.Context, req *aipb.ReqListChat, rsp *aipb.RspListChat) error {
	query := chatlogic.FilterListChat(ctx, req)
	totalCount, err := query.Count()
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if totalCount == 0 {
		return nil
	}
	rsp.TotalCount = totalCount

	paginator := &xorm.Paginator{
		Offset: req.Offset,
		Limit:  req.Limit,
	}
	paginator.Apply(query.DB())
	model.ParseOrder(req.OrderBy, model.ChatOrderByAllowedColumns, query, func(column string) string {
		if column == "update_date" { // 根据会话的最后更新时间排序
			return "v_chat_summary.update_date"
		}
		if column == "question_cnt" {
			return "v_chat_summary.question_cnt"
		}
		if column == "avg_duration" {
			return "v_chat_summary.avg_duration"
		}
		if column == "doc_hits" {
			return "v_chat_summary.doc_hits"
		}
		if column == "rating_scale" {
			return "v_chat_summary.rating_scale"
		}
		return column
	})
	chats, err := query.Get()
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	rsp.Chats = make([]*aipb.Chat, len(chats))
	chatIds := make([]uint64, len(chats))
	for i, v := range chats {
		chatIds[i] = v.ID
		rsp.Chats[i] = model.TransformChatToPb(v)
	}
	dateByChatID, err := chatlogic.GetMessageMaxUpdateDateByChatID(ctx, chatIds)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	for _, chat := range rsp.Chats {
		if v, ok := dateByChatID[chat.Id]; ok {
			chat.UpdateDate = timestamppb.New(v)
			// duration := logic.GetChatFinishState(nil)
			// if chatlogic.ChatType != aipb.ChatType_CHAT_TYPE_WEB && v.Before(time.Now().Add(-duration)) {
			//	chatlogic.ChatState = aipb.ChatCurrentState_CHAT_CURRENT_STATE_TIMEOUT
			// }
		}
	}
	return nil
}

// GetChatDetail 运营端获取AI对话详情
func (a *Ai) GetChatDetail(ctx context.Context, req *aipb.ReqGetChatDetail, rsp *aipb.RspGetChatDetail) error {
	if req.Id == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	chats, err := model.NewQuery[model.TChat](ctx).Wheres(func(db *gorm.DB) {
		if req.Origin != aipb.ChatOrigin_CHAT_ORIGIN_UNSPECIFIED {
			db.Where("origin = ?", req.Origin)
		}
		if len(req.AssistantId) != 0 {
			db.Where("assistant_id IN ?", req.AssistantId)
		}
		db.Where("id = ?", req.Id)
	}).Get()
	if err != nil {
		return err
	}
	if len(chats) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	chat := chats[0]
	rsp.ChatDetail = &aipb.ChatDetail{
		Id:             chat.ID,
		Title:          chat.Title,
		CreateBy:       chat.CreateBy,
		Nickname:       chat.Nickname,
		ChatType:       aipb.ChatType(chat.Type),
		AssistantId:    chat.AssistantID,
		LiveAgentName:  chat.LiveAgentName,
		ExternalUserId: chat.ExternalUserID,
		SupportType:    aipb.ChatSupportType(chat.SupportType),
		CreateDate:     timestamppb.New(chat.CreateDate),
		// UpdateDate:     timestamppb.New(chatlogic.UpdateDate),
		ChatState: aipb.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED,
	}
	if chat.Type == int32(aipb.ChatType_CHAT_TYPE_WEB) { // web端会话不受48小时限制，一直可用，不需要设置Finished
		return nil
	}

	updateDateInfoMap, err := chatlogic.GetMessageMaxUpdateDateByChatID(ctx, []uint64{chat.ID})
	if err != nil {
		return err
	}
	if v, ok := updateDateInfoMap[chat.ID]; ok {
		rsp.ChatDetail.UpdateDate = timestamppb.New(v)
	}
	if chat.FinishDate != nil { // 会话已经结束了
		rsp.ChatDetail.ChatState = aipb.ChatCurrentState_CHAT_CURRENT_STATE_REPLACED
	}

	return nil
}

// DescribeMessageDocs 获取消息docs
func (a *Ai) DescribeMessageDocs(ctx context.Context, req *aipb.ReqDescribeMessageDocs, rsp *aipb.RspDescribeMessageDocs) error {
	if req.DocNames == "" {
		return nil
	}
	docNames := strings.Split(req.DocNames, ",")
	rsp.Docs = make([]*aipb.ChatMessageDoc, 0, len(docNames))
	docIds := make([]uint64, 0, len(docNames))

	docs, err := chatlogic.DescribeMessageDocs(ctx, docNames)
	if err != nil {
		return err
	}
	// references := chatlogic.DescribeDocReferences(ctx, docs)
	for _, doc := range docs {
		if xstrings.In(doc.RagFilename, docNames) {
			docIds = append(docIds, doc.ID)
			msg := doc.TransformTDocToMessageDoc()
			rsp.Docs = append(rsp.Docs, msg)
		}
	}

	// 计数
	if req.HitCount {
		db := model.NewQuery[model.TDoc](ctx)
		if err := db.DB().WithContext(ctx).Model(&model.TDoc{}).
			Where("id IN (?)", docIds).
			Select("hit_count").
			Update("hit_count", gorm.Expr("hit_count + ?", 1)).Error; err != nil {
			log.Errorw("FetchCreateAIMessage hit_count add err", "err", err)
		}
	}

	return nil
}

// DescribeChatLogAuthItem 获取chat log字段访问权限
func (a *Ai) DescribeChatLogAuthItem(ctx context.Context, req *aipb.ReqDescribeChatLogAuthItem, rsp *aipb.RspDescribeChatLogAuthItem) error {
	items, err := chatlogic.MessageDetailConfigAuthItem(ctx, req.AssistantId)
	if err != nil {
		return err
	}
	rsp.AuthFields = items
	return nil
}

// DescribeMessageLogs 获取message请求日志
func (a *Ai) DescribeMessageLogs(ctx context.Context, req *aipb.ReqDescribeMessageLog, rsp *aipb.RspDescribeMessageLog) error {
	chatLogs, err := model.NewQuery[model.TChatLog](ctx).GetBy("message_id = ?", req.MessageId)
	if err != nil {
		return err
	}
	var fields []string
	if !req.IgnoreChainVisible && req.AssistantDetail != nil {
		fields = req.AssistantDetail.ChainVisible
	}

	for _, l := range chatLogs {
		var ignoreFields []string
		if l.PromptType == chatlogic.AIChatPromptTypeChat {
			ignoreFields = config.GetStringSliceOr("llm.visible_chain_ignore_chat", []string{"simple_model", "complex_model", "model", "threshold", "search_engine", "temperature", "text_recall_top_n", "clean_chunks"})
		}
		if l.PromptType == chatlogic.AIChatPromptTypeSimple {
			ignoreFields = config.GetStringSliceOr("llm.visible_chain_ignore_simple", []string{"chat_model", "complex_model", "model"})
		}
		if l.PromptType == chatlogic.AIChatPromptTypeComplex {
			ignoreFields = config.GetStringSliceOr("llm.visible_chain_ignore_complex", []string{"chat_model", "simple_model", "model"})
		}
		configShot, err := chatlogic.TransformMessageConfigToBffMessageConfig(l.ConfigSnapshot, fields, ignoreFields)
		if err != nil {
			return err
		}
		log.WithContext(ctx).Infow("DescribeMessageLogs log prompt type", "configSnapshot", l.ConfigSnapshot, "fields", fields, "ignoreFields", ignoreFields, "configShot", configShot)
		rsp.MessageLogs = append(rsp.MessageLogs, &aipb.ChatMessageLog{
			MessageId:      l.MessageID,
			SqlQuery:       l.SQLQuery,
			Enhancement:    l.Enhancement,
			Gpt:            l.Gpt,
			Ref:            l.Ref,
			Code:           l.Code,
			StartTime:      timestamppb.New(l.StartTime),
			EndTime:        timestamppb.New(l.EndTime),
			ConfigSnapshot: configShot,
			Type:           aipb.ChatMessageType(l.MessageType),
			RequestText:    l.RequestText,
			CreateDate:     timestamppb.New(l.CreateDate),
		})
	}
	return nil
}

// DescribeSuggestLogs 获取建议问题日志
func (a *Ai) DescribeSuggestLogs(ctx context.Context, req *aipb.ReqDescribeSuggestLog, rsp *aipb.RspDescribeSuggestLog) error {
	chatLogs, err := model.NewQuery[model.TChatSuggestLog](ctx).GetBy("message_id = ?", req.MessageId)
	if err != nil {
		return err
	}
	for _, l := range chatLogs {
		rsp.Logs = append(rsp.Logs, &aipb.ChatSuggestLog{
			MessageId:      req.MessageId,
			Collections:    l.Collections,
			Gpt:            l.Gpt,
			ConfigSnapshot: l.ConfigSnapshot,
			RequestType:    l.RequestType,
			CreateDate:     timestamppb.New(l.CreateDate),
		})
	}
	return nil
}

// CreateMessageSuggestQuestion 创建建议问题
func (a *Ai) CreateMessageSuggestQuestion(ctx context.Context, req *aipb.ReqCreateMessageSuggestQuestion, rsp *aipb.RspCreateMessageSuggestQuestion) error {
	if req.AssistantDetail == nil || req.AssistantDetail.SuggestionConfig == nil || !req.AssistantDetail.SuggestionConfig.Enabled {
		return errors.New("assistant suggestion is disEnabled")
	}
	suggests := chatlogic.CreateModeSuggestion(ctx, req.QuestionText, req.AnswerText, req.AssistantDetail, req.CreateBy, req.MessageId)
	if len(suggests) == 0 {
		return nil
	}
	rows := make([]*model.TChatSuggest, 0)
	for _, suggest := range suggests {
		rows = append(rows, &model.TChatSuggest{
			MessageID: req.MessageId,
			Suggest:   suggest,
			Mode:      uint32(req.AssistantDetail.SuggestionConfig.Mode),
		})
	}
	if err := model.NewQuery[model.TChatSuggest](ctx).Insert(rows); err != nil {
		return err
	}

	rsp.SuggestQuestions = suggests
	if req.RecordType == aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU && req.RecordInfo != "" {
		record := &model.TChatSendRecord{
			MessageID:  req.MessageId,
			Content:    req.RecordInfo,
			ChatID:     req.ChatId,
			Type:       aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU,
			State:      int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_UNSENT), // 1 未发送
			CreateDate: time.Now(),
		}
		canSend, continueAnswer := chatlogic.GetRecordSendInfo(ctx, req.ChatId)
		if continueAnswer { // 判断是否追加继续回答的按钮
			rsp.ContinueAnswering = true
			record.SendType = chatlogic.RecordTypeContinueMenu
		}

		if err := model.NewQuery[model.TChatSendRecord](ctx).Create(record); err != nil {
			return err
		}
		if canSend {
			rsp.RecordId = record.ID
		}
	}
	return nil
}

// DescribeExportChatMessages 查询message导出的结构
func (a *Ai) DescribeExportChatMessages(ctx context.Context, req *aipb.ReqDescribeExportChatMessages, rsp *aipb.RspDescribeExportChatMessages) error {
	var ugcLabelMap = map[basepb.DataType]string{
		basepb.DataType_DATA_TYPE_RESOURCE: "资源",
		basepb.DataType_DATA_TYPE_GRAPH:    "图谱",
		basepb.DataType_DATA_TYPE_PRODUCT:  "产品",
		basepb.DataType_DATA_TYPE_TEAM:     "团队",
	}
	var chatIds []uint64
	var chatMap = make(map[uint64]*aipb.ReqDescribeExportChatMessages_ExportChat)
	for _, chat := range req.ExportChats {
		chatIds = append(chatIds, chat.Id)
		chatMap[chat.Id] = chat
	}
	var messages []*model.TChatMessage

	if err := model.NewQuery[model.TChatMessage](ctx).DB().Preload("Docs").Find(&messages, "chat_id in (?)", chatIds).Error; err != nil {
		return err
	}

	answerMap := make(map[uint64]*model.TChatMessage)
	indexMessages := make([][]*model.TChatMessage, len(req.ExportChats))

	for _, message := range messages {
		switch aipb.ChatMessageType(message.Type) {
		case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH, aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY, aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION:
			answerMap[message.QuestionID] = message
		}
		for i, chat := range req.ExportChats {
			if message.ChatID == chat.Id {
				indexMessages[i] = append(indexMessages[i], message)
			}
		}
	}
	var results []*aipb.ExportMessage
	for _, messageArr := range indexMessages {
		for _, m := range messageArr {
			if m.Type != int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER) {
				continue
			}

			qContent := &aipb.ChatMessageContent{}
			if err := json.Unmarshal(m.Content, qContent); err != nil {
				return err
			}
			exportMsg := &aipb.ExportMessage{
				LiveAgentName: m.LiveAgentName,
				QuestionText:  qContent.Text,
			}

			if m.StartTime != nil && m.EndTime != nil {
				duration := m.EndTime.Sub(*m.StartTime).Seconds()
				exportMsg.ChatDuration = fmt.Sprintf("%.3f", duration)
				exportMsg.StartTime = m.StartTime.Format("2006-01-02 15:04:05")
				exportMsg.EndTime = m.EndTime.Format("2006-01-02 15:04:05")

			}

			if chatMap[m.ChatID] != nil {
				exportMsg.AssistantName = chatMap[m.ChatID].AssistantName
				exportMsg.UserName = chatMap[m.ChatID].UserName
				exportMsg.RegionCode = chatMap[m.ChatID].RegionCode
			}

			if m.LiveAgentName == "" {
				exportMsg.LiveAgentName = "AI"
			}

			// 处理答案
			if answer := answerMap[m.ID]; answer != nil {
				exportMsg.Type = aipb.ChatMessageType(answer.Type)
				for _, md := range answer.Docs {
					exportMsg.DocNames = append(exportMsg.DocNames, md.RagFilename)
				}

				aContent := &aipb.ChatMessageContent{}
				if err := json.Unmarshal(answer.Content, aContent); err != nil {
					continue
				}

				exportMsg.AnswerText = aContent.Text

				// search link
				if answer.Type == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH) {
					exportMsg.SearchLinkLabel = aContent.Link
				}
				// ugc type
				if answer.Type == int32(aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY) {
					var ugcTypeLabels []string
					for _, ugc := range aContent.Ugcs {
						ugcTypeLabels = append(ugcTypeLabels, fmt.Sprintf("UGC资源：%s", ugcLabelMap[ugc.UgcType]))
					}
					exportMsg.UgcTypeLabel = strings.Join(ugcTypeLabels, ";")
				}

				// 评价等级：1满意，2一般，3不满意
				switch aipb.RatingScale(answer.RatingScale) {
				case aipb.RatingScale_RATING_SCALE_SATISFIED:
					exportMsg.RatingScaleLabel = "满意"
				case aipb.RatingScale_RATING_SCALE_AVERAGE:
					exportMsg.RatingScaleLabel = "一般"
				case aipb.RatingScale_RATING_SCALE_DISSATISFIED:
					exportMsg.RatingScaleLabel = "不满意"
				}

			}

			results = append(results, exportMsg)

		}
	}
	rsp.ExportMessages = results
	return nil
}

// DescribeMessageMatchQa 查询question匹配的QA
func (a *Ai) DescribeMessageMatchQa(ctx context.Context, req *aipb.ReqDescribeMessageMatchQa, rsp *aipb.RspDescribeMessageMatchQa) error {
	f := func(pattern aipb.DocMatchPattern, text string, limit int) (docs []*model.TDoc, totalCount int64, err error) {
		if pattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_CONTAINS {
			docs, err = chatlogic.FindQAContainsText(ctx, []uint64{req.AssistantId}, req.Text, -1)
			if err == nil {
				totalCount = int64(len(docs))
				rsp.MatchPattern = aipb.DocMatchPattern_DOC_MATCH_PATTERN_CONTAINS
			}
			return
		}

		tx := model.NewQuery[model.TDoc](ctx).With("Contributors").DB().Debug().
			Select("`t_doc`.`id`,`t_doc`.`rag_filename`,`t_doc`.`data_type`,`t_doc`.`text`, `t_doc`.`unique_hash`,"+
				"`t_doc`.`normalized_hash`,`t_doc`.`ref`,`t_doc`.`index_text`,`t_doc`.`file_name`,`t_doc`.`update_by`,"+
				"`t_doc`.`update_by_type`,`t_doc`.`update_by_user`,`t_doc`.`data_source`").
			Joins("JOIN t_assistant_doc ON t_assistant_doc.doc_id = t_doc.id").
			Joins("JOIN t_doc_match_pattern ON t_doc_match_pattern.doc_id = t_doc.id").
			Where("t_assistant_doc.assistant_id = ?", req.AssistantId).
			Where("t_assistant_doc.state = ?", aipb.DocState_DOC_STATE_ENABLED).
			Order(`t_doc.create_date DESC`)
		// Preload("Assistants").
		// Preload("MatchPatterns").
		// Preload("Contributors")

		if pattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_IGNORE_MARK_MATCH {
			tx.Where("t_doc_match_pattern.match_pattern = ? AND t_doc.normalized_hash = ?",
				aipb.DocMatchPattern_DOC_MATCH_PATTERN_IGNORE_MARK_MATCH, util.CalculateMD5String(model.NormalizeQA(req.Text)))
		} else {
			tx.Where("t_doc_match_pattern.match_pattern = ? AND t_doc.unique_hash = ?",
				aipb.DocMatchPattern_DOC_MATCH_PATTERN_FULL_MATCH, util.CalculateMD5String(req.Text))
		}
		if limit > 0 {
			tx.Limit(limit)
		}
		err = tx.Count(&totalCount).Error
		if err != nil {
			return
		}
		if req.Page != nil {
			tx.Limit(int(req.Page.Limit)).Offset(int(req.Page.Offset))
		}
		rsp.MatchPattern = pattern
		err = tx.Find(&docs).Error

		return
	}

	var docs []*model.TDoc
	var err error
	var totalCount int64
	if req.Filter != nil { // 带有过滤条件的查询
		docs, totalCount, err = f(req.Filter.MatchPatterns, req.Text, int(req.Filter.Limit))
		if err != nil {
			return err
		}
	} else {
		// 优先级 指定匹配模式 > 完全匹配 > 无标点匹配 >  包含 >大模型
		if req.MatchPattern != 0 {
			docs, totalCount, err = f(req.MatchPattern, req.Text, 0)
			if err != nil {
				return err
			}
		} else {
			docs, totalCount, err = f(aipb.DocMatchPattern_DOC_MATCH_PATTERN_FULL_MATCH, req.Text, 0)
			if err != nil {
				return err
			}
			if len(docs) == 0 { // 无标点匹配
				docs, totalCount, err = f(aipb.DocMatchPattern_DOC_MATCH_PATTERN_IGNORE_MARK_MATCH, req.Text, 0)
				if err != nil {
					return err
				}
			}
			if len(docs) == 0 { // 包含
				docs, totalCount, err = f(aipb.DocMatchPattern_DOC_MATCH_PATTERN_CONTAINS, req.Text, 0)
				if err != nil {
					return err
				}
			}
		}
	}
	if len(docs) == 0 {
		return nil
	}

	rsp.Docs = make([]*aipb.ChatMessageDoc, len(docs))
	rsp.TotalCount = uint32(totalCount)
	for i, doc := range docs {
		// v :=
		// if doc.MatchPatterns != nil && len(doc.MatchPatterns) > 0 {
		//	for _, pattern := range doc.MatchPatterns {
		//		if pattern.MatchPattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_FULL_MATCH ||
		//			pattern.MatchPattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_IGNORE_MARK_MATCH ||
		//			pattern.MatchPattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_CONTAINS {
		//			v.DocMatchPattern = pattern.MatchPattern
		//			return nil
		//		}
		//	}
		// }
		rsp.Docs[i] = &aipb.ChatMessageDoc{
			Id:              doc.ID,
			RagFilename:     doc.RagFilename,
			DataType:        doc.DataType,
			Text:            doc.Text,
			DocMatchPattern: rsp.MatchPattern,
			IndexText:       doc.IndexText,
			FileName:        doc.FileName,
			Contributor:     doc.ContributorToPb(),
			DataSource:      doc.DataSource,
			UpdateBy: &aipb.Operator{
				Type:   doc.UpdateByType,
				Id:     doc.UpdateBy,
				UserId: doc.UpdateByUser,
			},
		}
		if doc.Ref != nil {
			rsp.Docs[i].Url = doc.Ref.Url
		}
	}

	return nil
}

// MigrationChatMessageInfo 迁移会话记录信息
func (a *Ai) MigrationChatMessageInfo(ctx context.Context, req *aipb.ReqMigrationChatMessageInfo,
	_ *emptypb.Empty) error {
	if req.New == 0 || req.Old == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	return model.Transaction(ctx, func(tx *gorm.DB) error {
		// 迁移用户反馈
		feedbackSql := "UPDATE `t_feedback` SET " +
			"`portal_read_by` = CASE WHEN `portal_read_by` = ? THEN ? ELSE `portal_read_by` END, " +
			"`portal_handled_by` = CASE WHEN `portal_handled_by` = ? THEN ? ELSE `portal_handled_by` END, " +
			"`create_by` = CASE WHEN `create_by` = ? THEN ? ELSE `create_by` END, " +
			"`update_by` = CASE WHEN `update_by` = ? THEN ? ELSE `update_by` END " +
			"WHERE `create_by` = ? OR `update_by` = ? OR `portal_read_by` = ? OR `portal_handled_by` = ? "
		err := xorm.NewQueryWithDB[model.TFeedback](tx).DB().
			Exec(feedbackSql, req.Old, req.New, req.Old, req.New, req.Old, req.New, req.Old, req.Old,
				req.Old, req.Old, req.Old, req.Old).Error
		if err != nil {
			return err
		}

		args := []any{req.Old, req.New, req.Old, req.New, req.Old, req.Old}

		// 迁移用户反馈日志
		logSql := "UPDATE `t_feedback_log` SET " +
			"`feedback_user_id` = CASE WHEN `feedback_user_id` = ? THEN ? ELSE `feedback_user_id` END, " +
			"`create_by` = CASE WHEN `create_by` = ? THEN ? ELSE `create_by` END " +
			"WHERE `feedback_user_id` = ? OR `create_by` = ? "
		err = xorm.NewQueryWithDB[model.TFeedback](tx).DB().
			Exec(logSql, args...).Error
		if err != nil {
			return err
		}

		// 迁移会话
		chatSql := "UPDATE `%s` SET `create_by` = CASE WHEN `create_by` = ? THEN ? ELSE `create_by` END, " +
			"`update_by` = CASE WHEN `update_by` = ? THEN ? ELSE `update_by` END " +
			"WHERE `create_by` = ? OR `update_by` = ? "

		// 迁移 t_chat
		err = xorm.NewQueryWithDB[model.TChat](tx).DB().
			Exec(fmt.Sprintf(chatSql, (*model.TChat)(nil).TableName()), args...).Error
		if err != nil {
			return err
		}

		// 迁移 t_chat_message
		return xorm.NewQueryWithDB[model.TChatMessage](tx).DB().
			Exec(fmt.Sprintf(chatSql, (*model.TChatMessage)(nil).TableName()), args...).Error
	})
}

// DescribeChatRegionCode 获取会话的地区编码
func (a *Ai) DescribeChatRegionCode(ctx context.Context, req *aipb.ReqDescribeChatRegionCode,
	rsp *aipb.RspDescribeChatRegionCode) error {
	tx := model.NewQuery[model.TChat](ctx).DB().Distinct()
	if len(req.AssistantIds) > 0 {
		tx.Where("assistant_id IN (?)", req.AssistantIds)
	}
	return tx.Pluck("region_code", &rsp.RegionCodes).Error
}

// CreateChatOperation 更新会话操作日志
func (a *Ai) CreateChatOperation(ctx context.Context, req *aipb.ReqCreateChatOperation, _ *emptypb.Empty) error {
	err := chatlogic.CreateChatOperation(ctx, &model.TChatOperation{
		QuestionID:     req.QuestionId,
		OperationType:  uint32(req.OperationType),
		HashID:         req.HashId,
		StopText:       req.StopText,
		StopThink:      req.StopThink,
		StopChunkState: req.StopChunkState,
		MessageID:      req.MessageId,
	})

	if err != nil {
		return err
	}
	return nil
}

// UpdateChatMessageCollections 更新message的collections
func (a *Ai) UpdateChatMessageCollections(ctx context.Context, req *aipb.ReqUpdateChatMessageCollections, _ *emptypb.Empty) error {
	startTime := req.StartTime.AsTime()
	endTime := req.EndTime.AsTime()

	marshal, err := json.Marshal(req.Items)
	if err != nil {
		return err
	}

	updates := model.TChatMessageCollection{
		StartTime:   &startTime,
		EndTime:     &endTime,
		Content:     string(marshal),
		CleanChunks: req.CleanChunks,
		MessageID:   req.MessageId,
	}

	return model.NewQuery[model.TChatMessageCollection](ctx).DB().Where("message_id = ?", req.MessageId).
		Assign(map[string]interface{}{
			model.TChatMessageCollectionColumns.StartTime:   &startTime,
			model.TChatMessageCollectionColumns.EndTime:     &endTime,
			model.TChatMessageCollectionColumns.Content:     string(marshal),
			model.TChatMessageCollectionColumns.CleanChunks: req.CleanChunks,
		}).FirstOrCreate(&updates).Error
}

// GetAssistantChatUser 获取助手的会话用户信息
func (a *Ai) GetAssistantChatUser(ctx context.Context, req *aipb.ReqGetAssistantChatUser, rsp *aipb.RspGetAssistantChatUser) error {
	rows, err := model.NewQuery[model.TChat](ctx).Select("assistant_id,create_by").Wheres(func(db *gorm.DB) {
		if len(req.AssistantIds) > 0 {
			db.Where("assistant_id IN (?)", req.AssistantIds)
		}
		db.Where("create_by > 0 ")
		db.Group("assistant_id")
		db.Group("create_by")
	}).Get()
	if err != nil {
		return err
	}
	m := make(map[uint64][]uint64)
	for _, v := range rows {
		m[v.AssistantID] = append(m[v.AssistantID], v.CreateBy)
	}
	rsp.UserInfo = make([]*aipb.RspGetAssistantChatUser_Info, 0, len(m))
	for k, v := range m {
		rsp.UserInfo = append(rsp.UserInfo, &aipb.RspGetAssistantChatUser_Info{
			AssistantId: k,
			UserId:      v,
		})
	}
	return nil
}
