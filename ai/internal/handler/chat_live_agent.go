package handler

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
)

// ListChatLiveAgent 获取对话人工客服列表
func (a *Ai) ListChatLiveAgent(ctx context.Context, req *aipb.ReqListChatLiveAgent,
	rsp *aipb.RspListChatLiveAgent) error {
	if len(req.AssistantIds) == 0 {
		return nil
	}

	query := model.NewQuery[model.TChatLiveAgent](ctx).Wheres(func(db *gorm.DB) {
		db.Where("assistant_id in (?)", req.AssistantIds)
		if len(req.LiveAgentName) > 0 {
			db.Where("nickname like ?", "%"+xorm.EscapeLikeWildcards(req.LiveAgentName)+"%")
		}
		if len(req.LiveAgentFullName) > 0 {
			db.Where("nickname = ?", req.LiveAgentFullName)
		}
		if len(req.LiveAgentIds) > 0 {
			db.Where("id in ?", req.LiveAgentIds)
		}
		if req.Status != aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_UNSPECIFIED {
			db.Where("status = ?", req.Status)
		}
	}).OrderBy("order", false).DB()

	if req.Page != nil {
		pager := xorm.NewPaginator(req.Page.Offset, req.Page.Limit)
		query = (&pager).Apply(query)
	}

	var liveAgents []*model.TChatLiveAgent
	err := query.Find(&liveAgents).Error
	if err != nil {
		return err
	}

	if len(liveAgents) == 0 {
		log.WithContext(ctx).Debugw("ListChatLiveAgent no live agents", "assistant_id", req.AssistantIds)
		return nil
	}

	rsp.ChatLiveAgents = make([]*aipb.ChatLiveAgent, 0, len(liveAgents))
	for _, v := range liveAgents {
		rsp.ChatLiveAgents = append(rsp.ChatLiveAgents, v.ToPb())
	}
	return nil
}

// SwitchChatLiveAgent 切换会话人工坐席
func (a *Ai) SwitchChatLiveAgent(ctx context.Context, req *aipb.ReqSwitchChatLiveAgent, _ *emptypb.Empty) error {
	m := map[string]interface{}{
		"live_agent_name": req.LiveAgentName,
		"support_type":    req.SupportType,
		"update_by":       req.UpdateBy,
	}
	if req.UpdateDefaultAnswerRecord {
		m["default_answer_record"] = req.DefaultAnswerRecord
	}
	if req.SupportType == aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT {
		m["is_manual"] = 2 // 是否转过人工服务，1-否，2-是
	}
	_, err := model.NewQuery[model.TChat](ctx).UpdateByKey(req.ChatId, m)
	return err
}

// LiveAgentStatusChange 人工坐席状态变化
func (a *Ai) LiveAgentStatusChange(ctx context.Context, req *aipb.ReqLiveAgentStatusChange, _ *emptypb.Empty) error {
	if req.Status == aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_UNSPECIFIED ||
		req.AssistantId == 0 || req.UserName == "" {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	_, err := model.NewQuery[model.TChatLiveAgent](ctx).UpdateBy(map[string]any{
		model.TChatLiveAgentColumns.Status: req.Status,
	}, "assistant_id = ? and username = ?", req.AssistantId, req.UserName)
	return err
}
