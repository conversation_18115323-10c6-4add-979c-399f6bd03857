package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	docModel "github.com/chinahtl/tencent-doc-sdk/model"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/bootstrap"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
)

func TestMain(m *testing.M) {
	bootstrap.Boot("../../bin/etc/ai.toml")
	os.Exit(m.Run())
}

func TestDoc(t *testing.T) {
	ctx := context.Background()
	cli, err := logic.NewTencentDocWrapper().GetTokendClient(ctx, 1000002, "65d464411d313ad63c40fb03386b90c6d1386945224a25e5d48d4406107a41ea")
	if err != nil {
		t.Fatal(err)
	}
	rsp, err := cli.SearchDocuments(ctx, &docModel.SearchParams{
		SearchKey:  "me",
		SearchType: "title",
		// SortType:   "title",
	})
	if err != nil {
		t.Fatal(err)
	}
	s, _ := json.Marshal(rsp)
	fmt.Println(string(s))
}
