package handler

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcrypto"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	docClient "github.com/chinahtl/tencent-doc-sdk/client"
	docModel "github.com/chinahtl/tencent-doc-sdk/model"
	docUtil "github.com/chinahtl/tencent-doc-sdk/util"
	"github.com/google/uuid"
	"github.com/tencentyun/cos-go-sdk-v5"
	"google.golang.org/protobuf/types/known/emptypb"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
)

func (a *Ai) CreateGTBText(ctx context.Context, req *aipb.ReqCreateGTBText, rsp *aipb.RspCreateGTBText) error {
	log.WithContext(ctx).Infow("CreateGTBText data", "req", req)

	ctx = context.Background()
	rspUuid := uuid.NewString()
	rsp.Uuid = rspUuid

	// 使用defer确保任务状态最终会被记录
	var taskErr error
	defer func() {
		taskState := model.TaskStateCompleted
		if taskErr != nil {
			taskState = model.TaskStateFailed
			log.WithContext(ctx).Errorw("CreateGTBText err", "uuid", rspUuid, "err", taskErr)
		}
		if err := logic.FinishTask(ctx, model.AchievementQuery, taskState, req.UserId, rspUuid); err != nil {
			log.WithContext(ctx).Errorw("CreateGTBText err to record task state", "uuid", rspUuid, "err", err)
		}
	}()

	// 记录任务开始
	if err := logic.FinishTask(ctx, model.AchievementQuery, model.TaskStateProcessing, req.UserId, rspUuid); err != nil {
		taskErr = fmt.Errorf("CreateGTBText record task start err: %w", err)
		return taskErr
	}

	// 获取当前用户管理的助手
	assistantIds, err := logic.LoadManagedAssistants(ctx, req.AdminType, req.AccountId)
	if err != nil {
		taskErr = fmt.Errorf("CreateGTBText LoadManagedAssistants err: %w", err)
		return taskErr
	}

	// 导入时默认创建绿技行tab
	if err := logic.ModifyInternalDocTab(ctx, config.GetString("llm.collection.external.gtb_default_name"),
		req.AdminType, req.AccountId, uint32(model.GreenTech)); err != nil {
		taskErr = fmt.Errorf("CreateGTBText ModifyInternalDocTab err: %w", err)
		return taskErr
	}

	// 开启并发执行，处理成果和专家数据
	wg := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))
	wg.SafeGo(func(ctx context.Context) error {
		if err := a.processGTBData(ctx, req, assistantIds, rspUuid); err != nil {
			taskErr = fmt.Errorf("CreateGTBText processGTBData err: %w", err)
			return taskErr
		}
		return nil
	})

	return nil
}

func (a *Ai) processGTBData(ctx context.Context, req *aipb.ReqCreateGTBText, assistantIds []uint64, taskUUID string) error {
	startDate := time.Date(2000, time.January, 1, 0, 0, 0, 0, time.UTC)

	// 如果isPullAll为false，则从数据库中查询最近一次任务的结束时间
	if !req.IsPullAll {
		startDate = logic.FindTaskEndDate(ctx)
	}

	currentDate := time.Now()

	// 按月循环拉取专家、成果数据
	for currentMonth := startDate; currentMonth.Before(currentDate); currentMonth = currentMonth.AddDate(0, 1, 0) {
		// 计算该月的最后一天
		nextMonth := currentMonth.AddDate(0, 1, 0)
		endDate := nextMonth.AddDate(0, 0, -1) // 下个月的前一天就是本月的最后一天

		// 如果结束日期超过当前日期，则使用当前日期
		if endDate.After(currentDate) {
			endDate = currentDate
		}

		startStr := currentMonth.Format("2006-01-02")
		endStr := endDate.Format("2006-01-02")

		log.WithContext(ctx).Infow("CreateGTBText deal month data", "startStr", startStr, "endStr", endStr)

		// 特殊处理2017年11月的数据
		if currentMonth.Year() == 2017 && currentMonth.Month() == time.November {
			// 处理2017年11月的数据（分两部分）
			if err := a.handleNovember2017Data(ctx, startStr, endStr, req, assistantIds, taskUUID); err != nil {
				return fmt.Errorf("CreateGTBText handleNovember2017Data err: %w", err)
			}
		} else {
			// 正常处理其他月份的数据
			if err := a.processAchievementData(ctx, startStr, endStr, req, assistantIds, taskUUID); err != nil {
				return fmt.Errorf("CreateGTBText processAchievementData err: %w", err)
			}
		}

		// 处理专家数据
		if err := a.processExpertData(ctx, startStr, endStr, req, assistantIds, taskUUID); err != nil {
			return fmt.Errorf("CreateGTBText processExpertData err: %w", err)
		}
	}

	log.WithContext(ctx).Infow("CreateGTBText done")

	return nil
}

func (a *Ai) handleNovember2017Data(ctx context.Context, startStr, endStr string, req *aipb.ReqCreateGTBText,
	assistantIds []uint64, taskUUID string,
) error {
	// 第一部分：2017-11-01 至 2017-11-10
	part1Start := startStr
	part1End := time.Date(2017, time.November, 10, 0, 0, 0, 0, time.UTC).Format("2006-01-02")

	// 第二部分：2017-11-11 至 2017-11-30
	part2Start := time.Date(2017, time.November, 11, 0, 0, 0, 0, time.UTC).Format("2006-01-02")
	part2End := endStr

	// 处理第一部分数据
	if err := a.processAchievementData(ctx, part1Start, part1End, req, assistantIds, taskUUID); err != nil {
		return err
	}

	// 处理第二部分数据
	return a.processAchievementData(ctx, part2Start, part2End, req, assistantIds, taskUUID)
}

func (a *Ai) processExpertData(ctx context.Context, startStr, endStr string, req *aipb.ReqCreateGTBText,
	assistantIds []uint64, taskUUID string,
) error {
	expertQuery := logic.NewQuery(model.ExpertQuery, startStr, endStr)
	experts, err := expertQuery.QueryExperts()
	if err != nil {
		log.WithContext(ctx).Errorw("QueryExperts failed", "uuid", taskUUID,
			"startStr", startStr, "endStr", endStr, "err", err)
		return fmt.Errorf("QueryExperts failed: %w", err)
	}

	for _, expert := range experts {
		if len(expert.Guid) == 0 {
			continue
		}

		insertId, isUpdate, err := logic.AddGTBExpert(ctx, expert, req.AdminType, req.AccountId)
		if err != nil {
			return fmt.Errorf("AddGTBExpert failed: %w", err)
		}

		if !isUpdate {
			bulk := logic.PrePareExpertDoc(req, assistantIds, expert)
			log.WithContext(ctx).Infow("PrePareExpertDoc data", "bulk", bulk, "uuid", taskUUID)

			rspTextFile := &aipb.RspCreateTextFileInBulk{}
			if err := a.CreateTextFileInBulk(ctx, &aipb.ReqCreateTextFileInBulk{Items: []*aipb.TextFile{bulk}}, rspTextFile); err != nil {
				return fmt.Errorf("CreateTextFileInBulk failed: %w", err)
			}

			if err := logic.UpdateExternalSource(ctx, insertId, &model.TDocExternalSource{DocID: rspTextFile.Id[0]}); err != nil {
				return fmt.Errorf("UpdateExternalSource failed: %w", err)
			}
		}
		time.Sleep(time.Microsecond * 300)
	}

	return nil
}

// 处理成果数据
func (a *Ai) processAchievementData(ctx context.Context, startStr, endStr string, req *aipb.ReqCreateGTBText,
	assistantIds []uint64, rspUuid string,
) error {
	achievementQuery := logic.NewQuery(model.AchievementQuery, startStr, endStr)
	achievements, err := achievementQuery.QueryAchievements()
	if err != nil {
		log.WithContext(ctx).Errorw("CreateGTBText QueryAchievements err", "uuid", rspUuid,
			"startStr", startStr, "endStr", endStr, "err", err)
		return nil // 继续处理其他数据
	}

	for _, achievement := range achievements {
		if len(achievement.Guid) > 0 {
			insertId, isUpdate, err := logic.AddGTBAchievement(ctx, achievement, req.AdminType, req.AccountId)
			if err != nil {
				log.WithContext(ctx).Errorw("CreateGTBText AddGTBAchievement err", "uuid", rspUuid, "err", err)
				return err
			}

			if !isUpdate {
				bulk := logic.PrePareAchievementDoc(req, assistantIds, achievement)
				log.WithContext(ctx).Infow("CreateGTBText PrePareAchievementDoc data", "bulk", bulk)

				rspTextFile := &aipb.RspCreateTextFileInBulk{}
				if err := a.CreateTextFileInBulk(ctx, &aipb.ReqCreateTextFileInBulk{Items: []*aipb.TextFile{bulk}}, rspTextFile); err != nil {
					log.WithContext(ctx).Errorw("CreateGTBText CreateTextFileInBulk err", "uuid", rspUuid, "err", err)
					return err
				}

				if err := logic.UpdateExternalSource(ctx, insertId, &model.TDocExternalSource{DocID: rspTextFile.Id[0]}); err != nil {
					log.WithContext(ctx).Errorw("CreateGTBText UpdateExternalSource err", "uuid", rspUuid, "err", err)
					return err
				}
			}
			time.Sleep(time.Microsecond * 300)
		}
	}
	return nil
}

// DescribeGTBText 查询绿技行数据
func (a *Ai) DescribeGTBText(ctx context.Context, req *aipb.ReqDescribeGTBText, rsp *aipb.RspDescribeGTBText) error {
	return nil
}

// CreateTencentDocAuthUrl 创建腾讯文档授权链接
func (a *Ai) CreateTencentDocAuthUrl(ctx context.Context, req *aipb.ReqCreateTencentDocAuthUrl,
	rsp *aipb.RspCreateTencentDocAuthUrl,
) error {
	docClient, err := logic.NewTencentDocWrapper().GetClient(ctx)
	if err != nil {
		log.WithContext(ctx).Errorw("CreateTencentDocAuthUrl getDocClient err", "err", err)
		return err
	}

	rsp.Url = docClient.GetAuthURL()

	return nil
}

// DescribeDocList 查询腾讯文档列表
func (a *Ai) DescribeDocList(ctx context.Context, req *aipb.ReqDescribeTencentDocList,
	rsp *aipb.RspDescribeTencentDocList,
) error {
	// 导入时默认创建腾讯文档tab
	logic.ModifyInternalDocTab(ctx, config.GetString("llm.collection.external.tdoc_default_name"),
		req.AdminType, req.AccountId, uint32(model.TencentDoc))

	supportType := "doc-slide-sheet-pdf-folder"

	var docs []*aipb.TencentDoc
	var next uint32

	docClient, err := logic.NewTencentDocWrapper().GetTokendClient(ctx, req.GetUserId(), req.GetEncryptedOpenid())
	if err != nil {
		if err == logic.ErrTencentDocTokenExpired || err == logic.ErrTencentDocTokenCanceled {
			rsp.HasExpired = true
			return xerrors.NewCode(errpb.AiError_AiDocExternalSourceTokenExpired)
		}
		return err
	}

	if len(req.Search) > 0 {
		// 搜索文档
		searchDocuments, err := docClient.SearchDocuments(ctx, &docModel.SearchParams{
			SearchKey:  req.Search,
			SearchType: "title",
			FolderID:   req.FolderId,
			FileTypes:  supportType,
		})
		if err != nil {
			log.WithContext(ctx).Errorw("DescribeDocList SearchDocuments failed", "err", err)
			if searchDocuments != nil && searchDocuments.Ret == logic.CodeTencentDocApiErrTokenExpired {
				return xerrors.NewCode(errpb.AiError_AiDocExternalSourceTokenExpired)
			}
			return err
		}

		for _, document := range searchDocuments.Data.List {
			doc, err := logic.ConvertDocToResponse(document)
			if err != nil {
				log.WithContext(ctx).Errorw("Failed to convert document", "err", err)
				continue
			}
			docs = append(docs, doc)
		}
		next = uint32(searchDocuments.Data.Next)
	} else {
		// 文档列表
		dReq := &docModel.ListParams{
			FileType: supportType,
			FolderID: req.FolderId,
			Start:    int(req.Start),
		}
		if len(req.TitleNameSort) > 0 {
			dReq.SortType = "title"
			if req.TitleNameSort == "asc" {
				dReq.Asc = 1
			} else {
				dReq.Asc = 0
			}
		}
		documents, err := docClient.ListDocuments(ctx, dReq)
		if err != nil {
			log.WithContext(ctx).Errorw("DescribeDocList ListDocuments failed", "err", err)
			if documents != nil && documents.Ret == logic.CodeTencentDocApiErrTokenExpired {
				return xerrors.NewCode(errpb.AiError_AiDocExternalSourceTokenExpired)
			}
			return err
		}

		for _, document := range documents.Data.List {
			doc, err := logic.ConvertDocToResponse(document)
			if err != nil {
				log.WithContext(ctx).Errorw("Failed to convert document", "err", err)
				continue
			}
			docs = append(docs, doc)
		}
		next = uint32(documents.Data.Next)
	}

	// 更新Token优先级
	err = logic.NewTencentDocWrapper().UpdateTokenPriority(ctx, req.UserId, req.EncryptedOpenid)
	if err != nil {
		log.WithContext(ctx).Warnw("Failed to update token priority", "userId", req.UserId, "encryptedOpenid", req.EncryptedOpenid, "err", err)
	}

	rsp.Docs = docs
	rsp.Next = next

	return nil
}

// ImportTencentDoc 导入腾讯文档
func (a *Ai) ImportTencentDoc(ctx context.Context, req *aipb.ReqImportTencentDoc, rsp *aipb.RspImportTencentDoc) error {
	// ctx = context.Background()
	ctx = context.WithoutCancel(ctx)
	rspUuid := uuid.NewString()
	rsp.Uuid = rspUuid

	// 记录任务开始
	if err := logic.FinishTask(ctx, model.TencentDocQuery, model.TaskStateProcessing, req.UserId, rspUuid); err != nil {
		log.WithContext(ctx).Errorw("ImportTencentDoc record task start err", "uuid", rspUuid, "err", err)
		return fmt.Errorf("ImportTencentDoc record task start err: %w", err)
	}

	// 获取所有关联的助手
	assistantIds, err := logic.LoadManagedAssistants(ctx, req.AdminType, req.AccountId)
	if err != nil {
		updateTaskStateAsync(ctx, model.TaskStateFailed, req.UserId, rspUuid, err)
		return fmt.Errorf("ImportTencentDoc LoadManagedAssistants err: %w", err)
	}

	existingToken, err := logic.NewTencentDocWrapper().GetToken(ctx, req.UserId, req.EncryptedOpenid, true)
	if err != nil {
		updateTaskStateAsync(ctx, model.TaskStateFailed, req.UserId, rspUuid, err)
		return fmt.Errorf("ImportTencentDoc GetToken err: %w", err)
	}
	docClient, err := logic.NewTencentDocWrapper().GetClient(ctx)
	if err != nil {
		updateTaskStateAsync(ctx, model.TaskStateFailed, req.UserId, rspUuid, err)
		return fmt.Errorf("ImportTencentDoc GetClient err: %w", err)
	}

	// 启动异步处理
	go a.asyncProcessFiles(ctx, docClient, existingToken, req, assistantIds, rspUuid, req.UserId)

	return nil
}

// ReimportTencentDoc 重新导入腾讯文档(根据知识库 doc id)
func (a *Ai) ReimportTencentDoc(ctx context.Context, req *aipb.ReqReimportTencentDoc, rsp *aipb.RspReimportTencentDoc) error {
	authInvalidMap, doc2Token, err := logic.NewReimportTencentDocLogic(ctx, req.DocIds).PreCheck()
	if err != nil {
		return err
	}
	// 有失效的 token，直接返回
	if len(authInvalidMap) != 0 {
		docIds := make([]uint64, 0)
		for docId := range authInvalidMap {
			docIds = append(docIds, docId)
		}
		docs, err := model.NewQuery[model.TDoc](ctx).Select("id", "file_name").Where("id in ?", docIds).Get()
		if err != nil {
			return err
		}
		docNameMap := make(map[uint64]string)
		for _, doc := range docs {
			docNameMap[doc.ID] = doc.FileName
		}

		for docId, user := range authInvalidMap {
			rsp.Failed = append(rsp.Failed, &aipb.RspReimportTencentDoc_FailInfo{
				DocId:    docId,
				FileName: docNameMap[docId],
				User:     user,
			})
		}
		return nil
	}
	for fid, token := range doc2Token {
		// 记录任务开始
		taskId := uuid.NewString()
		if err := logic.FinishTask(ctx, model.TencentDocQuery, model.TaskStateProcessing, req.UserId, taskId); err != nil {
			log.WithContext(ctx).Errorw("ImportTencentDoc record task start err", "uuid", taskId, "err", err)
			return fmt.Errorf("ImportTencentDoc record task start err: %w", err)
		}
		docClient, err := logic.NewTencentDocWrapper().GetClient(ctx)
		if err != nil {
			updateTaskStateAsync(ctx, model.TaskStateFailed, req.UserId, taskId, err)
			return fmt.Errorf("ImportTencentDoc GetClient err: %w", err)
		}
		token := token
		go a.asyncProcessFiles(context.WithoutCancel(ctx), docClient, token, &aipb.ReqImportTencentDoc{
			FileIds:   []string{fid},
			AdminType: req.AdminType,
			AccountId: req.AccountId,
			UserId:    req.UserId,
		}, nil, taskId, req.UserId)
	}
	return nil
}

// 异步更新任务状态的辅助函数
func updateTaskStateAsync(ctx context.Context, state int, userID uint64, taskUUID string, err error) {
	go func() {
		if err := logic.FinishTask(ctx, model.TencentDocQuery, state, userID, taskUUID); err != nil {
			log.WithContext(ctx).Errorw("Async update task state failed", "uuid", taskUUID, "err", err)
		}
	}()
}

// 异步处理文件并更新任务状态
func (a *Ai) asyncProcessFiles(ctx context.Context, docClient *docClient.Client, token *docModel.Token,
	req *aipb.ReqImportTencentDoc, assistantIds []uint64, taskUUID string, userID uint64,
) {
	var taskErr error
	defer func() {
		taskState := model.TaskStateCompleted
		if taskErr != nil {
			taskState = model.TaskStateFailed
			log.WithContext(ctx).Errorw("ImportTencentDoc process files err", "uuid", taskUUID, "err", taskErr)
		}
		if err := logic.FinishTask(ctx, model.TencentDocQuery, taskState, userID, taskUUID); err != nil {
			log.WithContext(ctx).Errorw("ImportTencentDoc record task finish err", "uuid", taskUUID, "err", err)
		}
	}()

	log.WithContext(ctx).Infow("ImportTencentDoc async process start", "uuid", taskUUID)

	for _, fileID := range req.GetFileIds() {
		if err := a.processSingleFile(ctx, docClient, token, fileID, req, assistantIds, taskUUID); err != nil {
			taskErr = err
			return
		}
	}
}

// processSingleFile 处理单个文件
func (a *Ai) processSingleFile(ctx context.Context, docClient *docClient.Client, token *docModel.Token, fileID string,
	req *aipb.ReqImportTencentDoc, assistantIds []uint64, taskUUID string,
) error {
	// 获取腾讯文档元数据
	docMetadata, err := docClient.WithToken(token).GetFileMetadata(ctx, fileID)
	if err != nil {
		return fmt.Errorf(" GetFileMetadata err: %w", err)
	}

	tencentDoc := &logic.TencentDoc{
		FileId:         fileID,
		FileName:       docMetadata.Data.Title,
		FileModifyTime: docMetadata.Data.LastModifyTime,
		FileCreateTime: docMetadata.Data.CreateTime,
	}

	doc, err := docClient.WithToken(token).ExportDocument(ctx, fileID, &docModel.ExportRequest{})
	if err != nil {
		return fmt.Errorf(" ExportDocument err: %w", err)
	}

	for i := 0; i < 100; i++ {
		progress, err := docClient.WithToken(token).GetExportProgress(ctx, fileID, doc.Data.OperationID)
		if err != nil {
			return fmt.Errorf(" GetExportProgress err: %w", err)
		}

		if progress.Data.Progress == 100 {
			return a.handleExportedFile(ctx, progress.Data.URL, tencentDoc, req, assistantIds, taskUUID, token.UserID)
		}
		time.Sleep(time.Second * 2)
	}

	log.WithContext(ctx).Infow("ImportTencentDoc done")

	return nil
}

// handleExportedFile 处理已导出的文件
func (a *Ai) handleExportedFile(ctx context.Context, fileURL string, doc *logic.TencentDoc,
	req *aipb.ReqImportTencentDoc, assistantIds []uint64, taskUUID string, userOpenId string,
) error {
	localPath, err := docUtil.DownloadFromCOS(fileURL, model.TmpTencentDoc)
	if err != nil {
		return fmt.Errorf(" DownloadFromCOS err: %w", err)
	}

	fileName := strings.TrimLeft(localPath, model.TmpTencentDoc)
	localDownPath := fmt.Sprintf("./%s", localPath)

	insertId, isUpdate, err := logic.AddTencentDocFile(ctx, doc, localDownPath, req, userOpenId)
	if err != nil {
		return fmt.Errorf(" AddTencentDocFile err: %w", err)
	}
	if insertId == 0 {
		log.WithContext(ctx).Infow("ImportTencentDoc update, but no diff, skip update", "docId", doc.FileId)
		return nil
	}

	// 上传至cos中
	cosUrl := fmt.Sprintf("/atlas_ai/%s/%s/%s", time.Now().Format("2006-01-02"), xstrings.Random(25), fileName)
	if _, err = logic.GetAiDocCosClient().Object.PutFromFile(ctx, cosUrl, localPath, &cos.ObjectPutOptions{}); err != nil {
		return fmt.Errorf(" PutFromFile err: %w", err)
	}

	// 更新腾讯文档处理状态
	logic.AsyncUpdateTencentDocFile(ctx, insertId, localDownPath)

	if err := logic.UpdateExternalSource(ctx, insertId, &model.TDocExternalSource{CosURL: cosUrl}); err != nil {
		return fmt.Errorf(" UpdateExternalSource err: %w", err)
	}

	// 如果文档有更新，且文档id不为0，则更新文档, insertId=0代表无需更新
	if isUpdate {
		log.WithContext(ctx).Infow("ImportTencentDoc PrePareTencentDoc isUpdate data",
			"updateText", &aipb.ReqUpdateTextFile{Id: doc.DocId, Url: cosUrl})
		operator := &aipb.Operator{
			Type:   base.IdentityType(req.AdminType),
			Id:     req.AccountId,
			UserId: req.UserId,
		}

		if err := a.UpdateTextFile(ctx, &aipb.ReqUpdateTextFile{Id: doc.DocId, Url: cosUrl, UpdateBy: operator}, nil); err != nil {
			return fmt.Errorf(" UpdateTextFile err: %w", err)
		}
		if err := logic.UpdateExternalSource(ctx, insertId, &model.TDocExternalSource{
			State: uint32(model.Updated),
		}); err != nil {
			return fmt.Errorf(" UpdateExternalSource err: %w", err)
		}
		return nil
	}

	// 写入知识库中
	bulk := logic.PrePareTencentDoc(req, assistantIds, cosUrl, fileName)
	log.WithContext(ctx).Infow("ImportTencentDoc PrePareTencentDoc data", "bulk", bulk)

	rspTextFile := &aipb.RspCreateTextFileInBulk{}
	if err := a.CreateTextFileInBulk(ctx, &aipb.ReqCreateTextFileInBulk{Items: []*aipb.TextFile{bulk}}, rspTextFile); err != nil {
		return fmt.Errorf(" CreateTextFileInBulk err: %w", err)
	}

	if err := logic.UpdateExternalSource(ctx, insertId, &model.TDocExternalSource{DocID: rspTextFile.Id[0]}); err != nil {
		return fmt.Errorf(" UpdateExternalSource err: %w", err)
	}

	// 暂时不上这个功能
	// if err := logic.UpdateTencentDocPathLabelValue(ctx, req.CustomLabelTenantId, req.Language, insertId, req.FilePath); err != nil {
	// 	return fmt.Errorf(" UpdateTencentDocPathLabelValue err: %w", err)
	// }

	return nil
}

// ModifyDocTab 修改文档标签
func (a *Ai) ModifyDocTab(ctx context.Context, req *aipb.ReqModifyDocTab, _ *emptypb.Empty) error {
	tab := &logic.ModifyDocTab{}
	tab.Name = req.Name
	tab.AdminType = req.AdminType
	tab.AccountId = req.AccountId
	tab.Type = req.Type

	err := tab.ModifyDocTab(ctx)
	if err != nil {
		return err
	}

	return nil
}

// DescribeDocTab 查询文档标签
func (a *Ai) DescribeDocTab(ctx context.Context, req *aipb.ReqDescribeDocTab, rsp *aipb.RspDescribeDocTab) error {
	tab := &logic.ModifyDocTab{}

	tab.AdminType = req.AdminType
	tab.AccountId = req.AccountId
	tab.Type = req.Type

	docExternalTabs, err := tab.DescribeDocTabs(ctx)
	if err != nil {
		return err
	}

	for _, docExternalTab := range docExternalTabs {

		doc := &aipb.RspDescribeDocTab_DocTab{
			Id:   docExternalTab.ID,
			Name: docExternalTab.Name,
			Type: docExternalTab.Type,
		}

		if docExternalTab.Type == uint32(model.TencentDoc) {
			doc.IsShow = true
		}

		if docExternalTab.Type == uint32(model.GreenTech) && req.TeamId == config.GetUint64("llm.collection.external.gtb_team_id") {
			doc.IsShow = true
		}

		rsp.Tabs = append(rsp.Tabs, doc)

	}

	return nil
}

// AuthTencentCode 鉴权腾讯文档code
func (a *Ai) AuthTencentCode(ctx context.Context, req *aipb.ReqAuthTencentCode, rsp *aipb.RspAuthTencentCode) error {
	log.WithContext(ctx).Infow("AuthTencentCode data", "req", req)

	docClient, err := logic.NewTencentDocWrapper().GetClient(ctx)
	if err != nil {
		log.WithContext(ctx).Errorw("AuthTencentCode getDocClient err", "err", err)
		return err
	}

	token, err := logic.NewTencentDocWrapper().StoreTokenByCode(ctx, req.GetCode(), req.Path, req.GetUserId())
	if err != nil {
		log.WithContext(ctx).Errorw("AuthTencentCode getOrRefreshToken err", "err", err)
		return err
	}

	// 获取腾讯文档用户信息
	userInfo, err := docClient.WithToken(token).GetUserInfo(ctx)
	if err != nil {
		log.WithContext(ctx).Errorw("AuthTencentCode GetUserInfo err", "err", err)
		return err
	}

	if err := logic.AddExternalSourceUser(ctx, userInfo, token, req.UserId); err != nil {
		log.WithContext(ctx).Errorw("AuthTencentCode AddExternalSourceUser err", "err", err)
		return err
	}

	return nil
}

func (a *Ai) ListExternalSourceUser(ctx context.Context, req *aipb.ReqListExternalSourceUser, rsp *aipb.RspListExternalSourceUser) error {
	users, err := logic.ListExternalSourceUser(ctx, req.UserId)
	if err != nil {
		return err
	}
	if len(users) == 0 {
		return nil
	}
	userTokens, err := logic.NewTencentDocWrapper().ListUserTokens(ctx, req.UserId)
	if err != nil {
		return err
	}

	// 把 users 根据userTokens的access_time优先级排序, access_time越大排序越靠前
	sort.Slice(users, func(i, j int) bool {
		return userTokens[users[i].OpenID].LastAccessTime > userTokens[users[j].OpenID].LastAccessTime
	})

	for _, user := range users {
		if token, ok := userTokens[user.OpenID]; ok {
			rsp.Users = append(rsp.Users, &aipb.ExternalSourceUser{
				UserId:     xcrypto.Sha256(user.OpenID),
				Nickname:   user.Nick,
				Avatar:     user.Avatar,
				AuthState:  token.AuthState(),
				AuthSource: user.Source,
			})
		}
	}

	return nil
}

// DescribeTokenIsEmpty 查询腾讯文档授权是否为空（未授权/授权取消/授权过期）
func (a *Ai) DescribeTokenIsEmpty(ctx context.Context, req *aipb.ReqDescribeTokenIsEmpty, rsp *aipb.RspDescribeTokenIsEmpty) error {
	_, err := logic.NewTencentDocWrapper().GetToken(ctx, req.UserId, req.EncryptedOpenid, true)
	if err != nil {
		if err == logic.ErrTencentDocTokenNotFound ||
			err == logic.ErrTencentDocTokenExpired ||
			err == logic.ErrTencentDocTokenCanceled {
			rsp.IsEmpty = true
			return nil
		}
		return err
	}
	return nil
}

// DescribeMyDoc 获取我的文档
func (a *Ai) DescribeMyDoc(ctx context.Context, req *aipb.ReqDescribeMyDoc, rsp *aipb.RspDescribeMyDoc) error {
	fileUUIDs, err := logic.LoadMyDocList(ctx, req.AdminType, req.AccountId)
	if err != nil {
		return err
	}

	rsp.FileIds = fileUUIDs

	return nil
}

// DescribeTencentDocTask 查询腾讯文档任务
func (a *Ai) DescribeTencentDocTask(ctx context.Context, req *aipb.ReqDescribeTencentDocTask, rsp *aipb.RspDescribeTencentDocTask) error {
	taskRunning, err := logic.DescribeTencentDocTask(ctx, uint32(model.TencentDocQuery), req.UserId)
	if err != nil {
		log.Errorw("DescribeTencentDocTask err", "err", err)
		return err
	}

	rsp.IsRunning = taskRunning

	return nil
}

// DelTencentDocAuth 删除腾讯文档授权（取消授权）
func (a *Ai) DelTencentDocAuth(ctx context.Context, req *aipb.ReqDelTencentDocAuth, rsp *emptypb.Empty) error {
	err := logic.NewTencentDocWrapper().CancelToken(ctx, req.UserId, req.EncryptedOpenid)
	if err != nil {
		log.Errorw("DelTencentDocAuth err", "err", err)
		return err
	}

	return nil
}

// ImportTencentDocWebClip 导入腾讯文档网页剪存文档
func (a *Ai) ImportTencentDocWebClip(ctx context.Context, req *aipb.ReqImportTencentDocWebClip, rsp *aipb.RspImportTencentDocWebClip) error {
	// 导入时默认创建腾讯文档tab
	logic.ModifyInternalDocTab(ctx, config.GetString("llm.collection.external.tdoc_default_name"),
		req.AdminType, req.AccountId, uint32(model.TencentDoc))

	docClient, err := logic.NewTencentDocWrapper().GetTokendClient(ctx, req.UserId, req.EncryptedOpenid)
	if err != nil {
		if err == logic.ErrTencentDocTokenExpired || err == logic.ErrTencentDocTokenCanceled {
			return xerrors.NewCode(errpb.AiError_AiDocExternalSourceTokenExpired)
		}
		return err
	}
	clips, err := logic.ListTencentDocWebClips(ctx, docClient, req.AfterTime.AsTime())
	if err != nil {
		if errors.Is(err, logic.ErrTencentDocFolderNotFound) {
			return xerrors.NewCode(errpb.AiError_AiDocTencentWebClicpDirNotExisted)
		}
		return err
	}

	fids := make([]string, 0, len(clips))
	for _, clip := range clips {
		fids = append(fids, clip.ID)
	}
	if len(fids) == 0 {
		return nil
	}

	importRsp := &aipb.RspImportTencentDoc{}
	err = a.ImportTencentDoc(ctx, &aipb.ReqImportTencentDoc{
		UserId:              req.UserId,
		AdminType:           req.AdminType,
		AccountId:           req.AccountId,
		EncryptedOpenid:     req.EncryptedOpenid,
		FileIds:             fids,
		CustomLabelTenantId: req.CustomLabelTenantId,
		Language:            req.Language,
		FilePath:            logic.TencentDocWebClipPath,
	}, importRsp)
	if err != nil {
		return err
	}

	for _, clip := range clips {
		doc, err := logic.ConvertDocToResponse(clip)
		if err != nil {
			log.WithContext(ctx).Errorw("Failed to convert document", "err", err)
			continue
		}
		rsp.Docs = append(rsp.Docs, doc)
	}

	return nil
}
