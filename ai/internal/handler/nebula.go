package handler

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	uuid2 "github.com/google/uuid"
	"gorm.io/gorm"
)

// CreateNebulaTask 创建星云任务
func (a *Ai) CreateNebulaTask(ctx context.Context, req *aipb.ReqCreateNebulaTask,
	rsp *aipb.RspCreateNebulaTask) error {

	ctx = context.Background()
	uuid := uuid2.New().String()

	wg := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))
	wg.SafeGo(func(ctx context.Context) error {

		log.WithContext(ctx).Infow("create nebula task", "uuid", uuid, "req", req)
		err := logic.CreateNebulaTask(ctx, req, uuid)
		if err != nil {
			return err
		}

		return nil
	})

	rsp.Uuid = uuid

	return nil
}

// DescribeNebulaTask 查询星云任务
func (a *Ai) DescribeNebulaTask(ctx context.Context, req *aipb.ReqDescribeNebulaTask,
	rsp *aipb.RspDescribeNebulaTask) error {
	if (len(req.AssistantId) > 0 || len(req.QueryAssistantId) > 0) && len(req.Lang) > 0 && len(req.Uuid) == 0 {
		err := logic.FindCalcNebulaPCdata(ctx, req, rsp)
		if err != nil {
			return err
		}
		return nil
	}
	query := model.NewQuery[model.TNebulaTask](ctx).Where("uuid = ?", req.Uuid)

	if req.AsTeam {
		query = query.Where("team_id = ?  and admin_type = ? ", req.TeamId, model.AdminTypeTeam)
	} else {
		query = query.Where("user_id = ?  and admin_type = ? ", req.UserId, model.AdminTypeUser)
	}

	_, err := model.NewQuery[model.TNebulaTask](ctx).UpdateBy(map[string]any{"is_read": model.Readed},
		"uuid = ?", req.Uuid)

	nebula, err := query.Find()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if nebula == nil {
		return nil
	}

	rsp.Uuid = req.Uuid
	rsp.State = nebula.State
	rsp.CreateDate = nebula.CreateDate.Format(time.RFC3339)
	rsp.FilterText = nebula.FilterText

	if len(nebula.CalcuResult) > 0 {
		var calcuResult []string
		err = json.Unmarshal([]byte(nebula.CalcuResult), &calcuResult)
		if err != nil {
			return err
		}

		rsp.EndDate = nebula.EndDate.Format(time.RFC3339)
		rsp.CalcuResult = calcuResult
		rsp.ClusterList = logic.CosUrlToCdnUrl(nebula.CentersCosURL)
		rsp.ConnectInfo = logic.CosUrlToCdnUrl(nebula.ConnectionCosURL)
	}

	return nil
}

// DescribeNebulaTaskList 查询星云任务列表
func (a *Ai) DescribeNebulaTaskList(ctx context.Context, req *aipb.ReqDescribeNebulaTaskList,
	rsp *aipb.RspDescribeNebulaTaskList) error {

	if req.Page != nil && req.Page.Limit == 0 {
		req.Page.Limit = 10
	}

	query := model.NewQuery[model.TNebulaTask](ctx)
	if req.AsTeam {
		query = query.Where("team_id = ?  and admin_type = ? and auto_create = ?", req.TeamId,
			model.AdminTypeTeam, model.AutoNormal)
	} else {
		query = query.Where("user_id = ?  and admin_type = ? and auto_create = ?", req.UserId,
			model.AdminTypeUser, model.AutoNormal)
	}

	if len(req.Uuid) > 0 {
		query = query.Where("uuid in ?", req.Uuid)
	}

	newQuery := query

	count, err := newQuery.Count()
	if err != nil {
		return err
	}

	nebulaList, err := query.Offset(int(req.Page.Offset)).
		Limit(int(req.Page.Limit)).OrderBy("id", true).Get()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	unread, err := newQuery.Where("is_read = ?", model.Unread).Count()
	if err != nil {
		return err
	}

	rsp.Tasks = make([]*aipb.RspDescribeNebulaTaskList_Task, 0, len(nebulaList))
	for _, nebula := range nebulaList {
		task := &aipb.RspDescribeNebulaTaskList_Task{
			Uuid:       nebula.UUID,
			State:      nebula.State,
			Lang:       nebula.Lang,
			CreateDate: nebula.CreateDate.Format(time.RFC3339),
			FilterText: nebula.FilterText,
			IsRead:     nebula.IsRead,
		}

		if nebula.EndDate != nil {
			task.EndDate = nebula.EndDate.Format(time.RFC3339)
		} else {
			task.EndDate = ""
		}

		rsp.Tasks = append(rsp.Tasks, task)
	}

	rsp.UnreadNum = unread
	rsp.Total = count

	return nil
}

// DescribeNebulaProjection 查询星云任务投影
func (a *Ai) DescribeNebulaProjection(ctx context.Context, req *aipb.ReqDescribeNebulaProjection,
	rsp *aipb.RspDescribeNebulaProjection) error {

	projection, err := logic.DescribeNebulaProjection(ctx, req)
	if err != nil {
		log.WithContext(ctx).Errorw("DescribeNebulaProjection", "err", err, "req", req)
		return xerrors.BadRequestError("DescribeNebulaProjection err")
	}

	rsp.Projection = projection

	return nil
}

// DescribeNebulaData 获取星云数据
func (a *Ai) DescribeNebulaData(ctx context.Context, req *aipb.ReqDescribeNebulaData, rsp *aipb.RspDescribeNebulaData) error {

	if len(req.ContentHash) > 0 {
		data, err := logic.DescribeNebulaData(ctx, req)
		if err != nil {
			log.WithContext(ctx).Errorw("DescribeNebulaData", "err", err, "req", req)
		}
		rsp.Content = data
	}

	return nil
}

// BatchCreateNebulaTasks 批量创建星云任务，自动化任务
func (a *Ai) BatchCreateNebulaTasks(ctx context.Context, req *aipb.ReqBatchCreateNebulaTasks,
	rsp *aipb.RspBatchCreateNebulaTasks) error {
	ctx = context.Background()

	wg := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))
	wg.SafeGo(func(ctx context.Context) error {

		if req.TeamId > 0 || req.UserId > 0 {
			logic.BatchCreateNebulaTasksPara(ctx, req)
			return nil
		}

		wg.SafeGo(func(ctx context.Context) error {
			// 助手逐个处理， 知识库+提问+语言
			logic.BatchCreateNebulaTasks(ctx, req)
			return nil
		})

		wg.SafeGo(func(ctx context.Context) error {
			// 助手逐个处理， 团队下的知识库+提问
			logic.BatchCreateNebulaManyTasks(ctx, req)
			return nil
		})

		wg.SafeGo(func(ctx context.Context) error {
			// 助手逐个处理， 知识库和提问独立处理
			logic.BatchCreateNebulaTasksBySplit(ctx, req)
			return nil
		})

		wg.SafeGo(func(ctx context.Context) error {
			// 助手逐个处理， 团队下的知识库+提问独立处理
			logic.BatchCreateNebulaManyTasksBySplit(ctx, req)
			return nil
		})

		return nil
	})

	return nil
}

// CreateAssistantCollectionInit 创建助手集合初始化
func (a *Ai) CreateAssistantCollectionInit(ctx context.Context, req *aipb.ReqCreateAssistantCollectionInit,
	rsp *aipb.RspCreateAssistantCollectionInit) error {

	ctx = context.Background()

	wg := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))
	wg.SafeGo(func(ctx context.Context) error {

		if req.TeamId > 0 || req.UserId > 0 {
			logic.CreateAssistantCollectionInitPara(ctx, req)
			return nil
		}
		logic.CreateAssistantCollectionInit(ctx)
		return nil

	})

	return nil
}
