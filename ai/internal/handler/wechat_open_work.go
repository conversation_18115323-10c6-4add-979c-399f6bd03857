package handler

import (
	"context"
	"errors"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
)

// CreateOpenWechat 创建开放企微租户
func (a *Ai) CreateOpenWechat(ctx context.Context, req *aipb.ReqCreateOpenWechat, empty *emptypb.Empty) error {
	openWork := &model.TOpenWork{
		PermanentCode: req.PermanentCode,
		CorpID:        req.CorpId,
		OpenKfid:      req.OpenKfid,
		Action:        req.Action,
	}
	if err := model.NewQuery[model.TOpenWork](ctx).Create(openWork); err != nil {
		return err
	}

	return nil
}

// DescribeOpenWechat 获取开放企微租户信息
func (a *Ai) DescribeOpenWechat(ctx context.Context, req *aipb.ReqDescribeOpenWechat, rsp *aipb.RspDescribeOpenWechat) error {
	openWechat, err := model.NewQuery[model.TOpenWork](ctx).OrderBy("id", true).
		Where("corp_id = ? and permanent_code != '' ", req.CorpId).Find()

	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if openWechat == nil {
		return nil
	}

	rsp.PermanentCode = openWechat.PermanentCode
	return nil
}
