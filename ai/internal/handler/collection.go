package handler

import (
	"context"
	"errors"
	"os"
	"path"
	"sort"
	"strconv"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	chatlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/chat"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	ragcommon "e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag/common"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	pberrors "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"github.com/hashicorp/go-uuid"
	"golang.org/x/exp/slices"
	"golang.org/x/sync/errgroup"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
)

func (a *Ai) SearchCollectionOneShot(ctx context.Context, req *ai.ReqSearchCollectionOneShot, rsp *ai.RspSearchCollectionOneShot) error {
	searchThreshold := req.Threshold
	allCollectionsToSearch := make(map[uint64]*model.TCollection)
	// 选择所有助手中最小阈值
	if searchThreshold == 0 && len(req.AssistantId) > 0 {
		assistants, err := logic.GetAssistantsWithCollection(ctx, req.AssistantId...)
		if err != nil {
			return err
		}
		if len(assistants) == 0 {
			return nil
		}
		for _, v := range assistants {
			for _, vv := range v.Collections {
				for _, collection := range v.Collections {
					allCollectionsToSearch[collection.ID] = &collection.TCollection
				}
				if searchThreshold < vv.Threshold {
					searchThreshold = vv.Threshold
				}
			}
		}
	}

	g := &errgroup.Group{}
	var totalCount int
	searched := make(chan *rag.CollectionRow)
	searchTexted := make(chan *rag.CollectionRow)

	timerStart := make([]time.Time, len(req.AssistantId))
	timerEnd := make([]time.Time, len(req.AssistantId))

	for i, v := range req.AssistantId {
		v := v
		i := i
		g.Go(func() error {
			arsp := &ai.RspGetAssistant{}
			err := a.GetAssistant(ctx, &ai.ReqGetAssistant{
				Id: v,
			}, arsp)
			if err != nil {
				return err
			}
			if arsp.AssistantDetail == nil {
				return nil
			}
			assistant := arsp.AssistantDetail
			timerStart[i] = time.Now()

			if req.IsSearchChat {
				searchThreshold = req.Threshold
				assistant.TextRecallTopN = int32(req.TextRecallTopN)
				assistant.DocTopN = int32(req.TopN)
				assistant.TextRecallQuery = req.TextRecallQuery
				assistant.TextRecallSlop = req.TextRecallSlop
				assistant.TextRecallPattern = req.TextRecallPattern
				assistant.CleanChunks = req.CleanChunks
				assistant.Temperature = req.Temperature
			}

			topN := assistant.DocTopN
			lang := assistant.Lang
			esIns := assistant.EsIns
			textRecallTopN := assistant.TextRecallTopN

			var chunkIds []string
			s, err := client.GetRagClient().SearchCollectionOneShot(ctx, &rag.ReqSearchCollection{
				CollectionName:  assistant.CollectionName,
				Text:            req.Search,
				TopN:            int(topN),
				Threshold:       searchThreshold,
				From:            int(req.From),
				TotalHits:       true,
				Lang:            lang,
				EsIns:           esIns,
				TextRecallTopN:  int(textRecallTopN),
				TextRecallQuery: chatlogic.GetTextRecallQuery(assistant),
				CleanChunks:     assistant.CleanChunks,
				Temperature:     assistant.Temperature,
			})
			timerEnd[i] = time.Now()
			if err != nil {
				var cerr *ragcommon.Error
				if errors.As(err, &cerr) && cerr.Code == strconv.Itoa(ragcommon.ErrCodeModelNotExist) {
					return nil
				}
				return err
			}
			chunkIds = s.Data.ChunkIds

			if topN > 0 {
				for _, vv := range s.Data.RawDocs {
					if xslice.Contains(chunkIds, vv.Id) != -1 {
						vv.IsRelated = true
					}
					searched <- vv
				}
			}

			for _, vv := range s.Data.RawTextDocs {
				if xslice.Contains(chunkIds, vv.Id) != -1 {
					vv.IsRelated = true
				}
				searchTexted <- vv
			}
			// mu.Lock()
			// if s.Info != nil {
			//	totalCount += s.Info.TotalHits
			// }
			// mu.Unlock()
			return nil
		})
	}

	var searchRags, searchTextedRags []*rag.CollectionRow

	g1 := &sync.WaitGroup{}
	g1.Add(1)
	go func() {
		defer g1.Done()
		for v := range searched {
			v.Type = int(ai.SearchCollectionType_SEARCH_COLLECTION_TYPE_VECTOR)
			searchRags = append(searchRags, v)
		}
	}()

	g1.Add(1)
	go func() {
		defer g1.Done()
		for v := range searchTexted {
			v.Type = int(ai.SearchCollectionType_SEARCH_COLLECTION_TYPE_TEXT)
			searchTextedRags = append(searchTextedRags, v)
		}
	}()

	err := g.Wait()
	close(searched)
	close(searchTexted)
	if err != nil {
		return err
	}
	g1.Wait()

	if len(searchRags) > 0 {
		sort.Slice(searchRags, func(i, j int) bool {
			return searchRags[i].Score > searchRags[j].Score // 默认降序
		})
	}

	if len(searchTextedRags) > 0 {
		sort.Slice(searchTextedRags, func(i, j int) bool {
			return searchTextedRags[i].Score > searchTextedRags[j].Score // 默认降序
		})
	}

	var docNames []string
	for _, v := range searchRags {
		docNames = append(docNames, v.DocName)
	}
	for _, v := range searchTextedRags {
		docNames = append(docNames, v.DocName)
	}
	docs, err := logic.GetDocByRagDocNames(ctx, docNames, true, nil)
	if err != nil {
		return err
	}
	var docToDelete []string
	var idsToDelete []string

	rsp.SearchItems = logic.SearchCollectionItemsWithDoc(searchRags, docs, &docToDelete, &idsToDelete)
	rsp.SearchTextItems = logic.SearchCollectionItemsWithDoc(searchTextedRags, docs, &docToDelete, &idsToDelete)

	// 2.7之前，删除doc时，没有把向量数据完全删除，这里触发删除
	cs := make([]*model.TCollection, 0, len(allCollectionsToSearch))
	for _, collection := range allCollectionsToSearch {
		cs = append(cs, collection)
	}
	if len(idsToDelete) != 0 {
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			for _, v := range cs {
				_, _ = client.GetRagClient().DeleteVecById(ctx, &rag.ReqDeleteVecById{
					CollectionName: v.RagName,
					VecIds:         idsToDelete,
					EsIns:          v.EsIns,
				})
			}
			return nil
		}, boot.TraceGo(ctx))
	}
	for _, v := range docToDelete {
		docName := v
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			err := logic.NewDocSyncLogic(context.Background(), nil, &model.TDoc{
				RagFilename: docName,
				IndexText:   "no meaning data",
				Collections: cs,
			}).Sync()
			if err != nil {
				log.WithContext(ctx).Errorf("clean deleted doc rag data error: %v", err.Error())
			}
			return nil
		}, boot.TraceGo(ctx))
	}

	if req.TimeRecord {
		maxT := time.Duration(0)
		maxId := 0
		for i := 0; i < len(timerStart); i++ {
			if timerEnd[i].Sub(timerStart[i]) > maxT {
				maxT = timerEnd[i].Sub(timerStart[i])
				maxId = i
			}
		}
		rsp.StartTime = timestamppb.New(timerStart[maxId])
		rsp.EndTime = timestamppb.New(timerEnd[maxId])
	}
	rsp.TotalCount = uint32(totalCount)
	return nil
}

// SearchCollection 搜索collection
func (a *Ai) SearchCollection(ctx context.Context, req *ai.ReqSearchCollection, rsp *ai.RspSearchCollection) error {
	searchThreshold := req.Threshold
	// 选择所有助手中最小阈值
	if searchThreshold == 0 {
		assistants, err := logic.GetAssistantsWithCollection(ctx, req.AssistantId...)
		if err != nil {
			return err
		}
		if len(assistants) == 0 {
			return nil
		}
		for _, v := range assistants {
			for _, vv := range v.Collections {
				if searchThreshold < vv.Threshold {
					searchThreshold = vv.Threshold
				}
			}
		}
	}

	g := &errgroup.Group{}
	var (
		totalCount int
		mu         sync.Mutex
	)
	allCollectionsToSearch := make(map[uint64]*model.TCollection)
	searched := make(chan *rag.CollectionRow)
	timerStart := make([]time.Time, len(req.AssistantId))
	timerEnd := make([]time.Time, len(req.AssistantId))

	for i, v := range req.AssistantId {
		v := v
		i := i
		g.Go(func() error {
			assistant, err := logic.GetAssistantWithCollection(ctx, v)
			if err != nil {
				return err
			}
			if assistant == nil {
				return nil
			}

			collections := assistant.Collections
			if len(collections) == 0 {
				return nil
			}
			mu.Lock()
			for _, collection := range collections {
				allCollectionsToSearch[collection.ID] = &collection.TCollection
			}
			mu.Unlock()
			timerStart[i] = time.Now()
			// TODO: 多个collection如何搜索，v2.5暂时只支持一个collection
			topN := collections[0].DocTopN
			lang := collections[0].Lang
			esIns := collections[0].EsIns

			if req.TopN > 0 {
				topN = int32(req.TopN)
			}
			textWeight := assistant.TextWeight
			if req.IsSearchChat {
				textWeight = req.TextWeight
				searchThreshold = req.Threshold
			}

			s, err := client.GetRagClient().SearchCollection(ctx, &rag.ReqSearchCollection{
				CollectionName: collections[0].RagName,
				Text:           req.Search,
				TopN:           int(topN),
				Threshold:      searchThreshold,
				From:           int(req.From),
				TotalHits:      true,
				Lang:           lang,
				EsIns:          esIns,
				TextWeight:     textWeight,
			})
			timerEnd[i] = time.Now()
			if err != nil {
				var cerr *ragcommon.Error
				if errors.As(err, &cerr) && cerr.Code == strconv.Itoa(ragcommon.ErrCodeModelNotExist) {
					return nil
				}
				return err
			}
			for _, vv := range s.Data.Rows {
				searched <- vv
			}
			mu.Lock()
			if s.Info != nil {
				totalCount += s.Info.TotalHits
			}
			mu.Unlock()
			return nil
		})
	}

	var rags []*rag.CollectionRow
	g1 := &sync.WaitGroup{}
	g1.Add(1)
	go func() {
		defer g1.Done()
		for v := range searched {
			rags = append(rags, v)
		}
	}()

	err := g.Wait()
	close(searched)
	if err != nil {
		return err
	}
	g1.Wait()

	// sort.Slice(rags, func(i, j int) bool {
	//	if rags[i].DocName == rags[j].DocName {
	//		return rags[i].Content < rags[j].Content
	//	}
	//	return rags[i].DocName < rags[j].DocName
	// })
	// rags = slices.CompactFunc(rags, func(row *rag.CollectionRow, row2 *rag.CollectionRow) bool {
	//	if row == nil || row2 == nil {
	//		return false
	//	}
	//	return row.DocName == row2.DocName && row.Content == row2.Content
	// })
	sort.Slice(rags, func(i, j int) bool {
		return rags[i].Score > rags[j].Score // 默认降序
	})
	var docNames []string
	for _, v := range rags {
		docNames = append(docNames, v.DocName)
	}
	docs, err := logic.GetDocByRagDocNames(ctx, docNames, true, nil)
	if err != nil {
		return err
	}
	var docToDelete []string
	var idsToDelete []string
	for _, v := range rags {

		// if req.DocType != ai.DocType_DOCTYPE_UNSPECIFIED && uint32(req.DocType) != doc.DataType {
		//	continue
		// }

		item := &ai.SearchCollectionItem{
			Score:   v.Score,
			Id:      v.Id,
			DocName: v.DocName,
		}
		doc := docs[v.DocName]
		if doc != nil {
			switch ai.DocType(doc.DataType) {
			case ai.DocType_DOCTYPE_QA:
				item.Text = doc.Text
				item.Question = doc.IndexText
			case ai.DocType_DOCTYPE_FILE, ai.DocType_DOCTYPE_TEXT:
				item.Text = v.Content // 使用rag侧返回的文本段
				item.RefName = doc.FileName
				if doc.Ref != nil {
					item.RefUrl = doc.Ref.Url
				}
			}
			item.Contributor = doc.ContributorToPb()
			item.UpdateBy = &ai.Operator{Type: doc.UpdateByType, Id: doc.UpdateBy}
		} else {
			if !slices.Contains(docToDelete, v.DocName) {
				docToDelete = append(docToDelete, v.DocName)
			}
			if !slices.Contains(idsToDelete, v.Id) {
				idsToDelete = append(idsToDelete, v.Id)
			}
		}

		rsp.Items = append(rsp.Items, item)
	}

	// 2.7之前，删除doc时，没有把向量数据完全删除，这里触发删除
	cs := make([]*model.TCollection, 0, len(allCollectionsToSearch))
	for _, collection := range allCollectionsToSearch {
		cs = append(cs, collection)
	}
	if len(idsToDelete) != 0 {
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			for _, v := range cs {
				_, _ = client.GetRagClient().DeleteVecById(ctx, &rag.ReqDeleteVecById{
					CollectionName: v.RagName,
					VecIds:         idsToDelete,
					EsIns:          v.EsIns,
				})
			}
			return nil
		}, boot.TraceGo(ctx))
	}
	for _, v := range docToDelete {
		docName := v
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			err := logic.NewDocSyncLogic(context.Background(), nil, &model.TDoc{
				RagFilename: docName,
				IndexText:   "no meaning data",
				Collections: cs,
			}).Sync()
			if err != nil {
				log.WithContext(ctx).Errorf("clean deleted doc rag data error: %v", err.Error())
			}
			return nil
		}, boot.TraceGo(ctx))
	}
	if req.TimeRecord {
		maxT := time.Duration(0)
		maxId := 0
		for i := 0; i < len(timerStart); i++ {
			if timerEnd[i].Sub(timerStart[i]) > maxT {
				maxT = timerEnd[i].Sub(timerStart[i])
				maxId = i
			}
		}
		rsp.StartTime = timestamppb.New(timerStart[maxId])
		rsp.EndTime = timestamppb.New(timerEnd[maxId])
	}
	rsp.TotalCount = uint32(totalCount)
	return nil
}

// ListCollection 获取collection列表
func (a *Ai) ListCollection(ctx context.Context, req *ai.ReqListCollection, rsp *ai.RspListCollection) error {
	var rows []*model.TCollection
	db := model.NewQuery[model.TCollection](ctx).Where("1 = 1")
	if len(req.AssistantId) != 0 {
		subQ := model.NewQuery[model.TAssistantCollection](ctx).Where("id IN (?)", req.AssistantId).Select("collection_id")
		db = db.Where("id IN (?)", subQ)
	}
	err := db.Scan(&rows)
	if err != nil {
		return err
	}
	for _, v := range rows {
		rsp.Collections = append(rsp.Collections, &ai.Collection{
			Id:   v.ID,
			Name: v.Name,
		})
	}
	return nil
}

// ListContributor 获取贡献者筛选项
func (a *Ai) ListContributor(ctx context.Context, req *ai.ReqListContributor, rsp *ai.RspListContributor) error {
	return logic.ListContributor(ctx, req, rsp)
}

// ListUpdateBy 获取最近更新人
func (a *Ai) ListUpdateBy(ctx context.Context, req *ai.ReqListUpdateBy, rsp *ai.RspListUpdateBy) error {
	db := model.NewQuery[model.TDoc](ctx).DB()
	if len(req.DcoId) != 0 {
		db.Where("doc_id in ?", req.DcoId)
	}
	orGroup := model.NewQuery[model.TDocContributor](ctx).DB()
	if req.GetOr().GetContributor() != nil {
		contributor := req.GetOr().GetContributor()
		subQ := model.NewQuery[model.TDocContributor](ctx).DB().
			Where("contributor_id = ? and contributor_type = ?", contributor.Id, contributor.Type)
		if len(contributor.Text) != 0 {
			subQ = subQ.Or("contributor_text = ? and contributor_type = ?", contributor.Text, contributor.Type)
		}
		subQ.Select("distinct(doc_id)")
		orGroup.Where("id in (?)", subQ)
	}
	if len(req.GetOr().GetScopedAssistantId()) != 0 {
		assistantIds := req.GetOr().GetScopedAssistantId()
		subQ := model.NewQuery[model.TAssistantDoc](ctx).DB().
			Where("assistant_id in ? and state in ?", assistantIds, []ai.DocState{ai.DocState_DOC_STATE_ENABLED, ai.DocState_DOC_STATE_DISABLED}).Select("distinct(doc_id)")
		orGroup.Or("id in (?)", subQ)
	}
	db.Where(orGroup)
	switch req.Type {
	case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_QA:
		db.Where("data_type = ?", ai.DocType_DOCTYPE_QA)
	case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_TEXTFILE:
		db.Where("data_type in ?", []ai.DocType{ai.DocType_DOCTYPE_TEXT, ai.DocType_DOCTYPE_FILE})
		if req.DataSource != 0 {
			db.Where("data_source = ?", req.DataSource)
		}
	case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_SYSTEM:
		db.Where("data_source = ?", ai.DocDataSource_DOC_DATA_SOURCE_UGC)
	}
	var rows []*model.TDoc
	err := db.Distinct("update_by_type", "update_by", "update_by_user").Scan(&rows).Error
	if err != nil {
		return err
	}
	for _, v := range rows {
		rsp.Operators = append(rsp.Operators, &ai.Operator{
			Type:   v.UpdateByType,
			Id:     v.UpdateBy,
			UserId: v.UpdateByUser,
		})
	}
	return nil
}

// ListCreateBy 获取创建人
func (a *Ai) ListCreateBy(ctx context.Context, req *ai.ReqListUpdateBy, rsp *ai.RspListUpdateBy) error {
	db := model.NewQuery[model.TDoc](ctx).DB()
	if len(req.DcoId) != 0 {
		db.Where("doc_id in ?", req.DcoId)
	}
	orGroup := model.NewQuery[model.TDocContributor](ctx).DB()
	if req.GetOr().GetContributor() != nil {
		contributor := req.GetOr().GetContributor()
		subQ := model.NewQuery[model.TDocContributor](ctx).DB().
			Where("contributor_id = ? and contributor_type = ?", contributor.Id, contributor.Type)
		if len(contributor.Text) != 0 {
			subQ = subQ.Or("contributor_text = ? and contributor_type = ?", contributor.Text, contributor.Type)
		}
		subQ.Select("distinct(doc_id)")
		orGroup.Where("id in (?)", subQ)
	}
	if len(req.GetOr().GetScopedAssistantId()) != 0 {
		assistantIds := req.GetOr().GetScopedAssistantId()
		subQ := model.NewQuery[model.TAssistantDoc](ctx).DB().
			Where("assistant_id in ? and state in ?", assistantIds, []ai.DocState{ai.DocState_DOC_STATE_ENABLED, ai.DocState_DOC_STATE_DISABLED}).Select("distinct(doc_id)")
		orGroup.Or("id in (?)", subQ)
	}
	db.Where(orGroup)
	switch req.Type {
	case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_QA:
		db.Where("data_type = ?", ai.DocType_DOCTYPE_QA)
	case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_TEXTFILE:
		db.Where("data_type in ?", []ai.DocType{ai.DocType_DOCTYPE_TEXT, ai.DocType_DOCTYPE_FILE})
		if req.DataSource != 0 {
			db.Where("data_source = ?", req.DataSource)
		}
	case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_SYSTEM:
		db.Where("data_source = ?", ai.DocDataSource_DOC_DATA_SOURCE_UGC)
	}
	var rows []*model.TDoc
	err := db.Distinct("create_by_type", "create_by", "create_by_user").Scan(&rows).Error
	if err != nil {
		return err
	}
	for _, v := range rows {
		rsp.Operators = append(rsp.Operators, &ai.Operator{
			Type:   v.CreateByType,
			Id:     v.CreateBy,
			UserId: v.CreateByUser,
		})
	}
	return nil
}

// ValidateQAInBulk 校验QA合法性
func (a *Ai) ValidateQAInBulk(ctx context.Context, req *ai.ReqValidateQAInBulk, rsp *ai.RspValidateQAInBulk) error {
	rsp.Errors = make([]*ai.RspValidateQAInBulk_Item, len(req.Items))

	docs := make([]*model.TDoc, 0, len(req.Items))
	for _, v := range req.Items {
		docs = append(docs, new(model.TDoc).FromQAProto(v))
	}

	ids, err := logic.NewQAValidator(ctx, docs, req.Contributor, req.ScopedAssistant...).ValidateQuestion()
	if err != nil {
		return err
	}
	for i, v := range ids {
		if v != 0 {
			rsp.Errors[i] = &ai.RspValidateQAInBulk_Item{Error: pberrors.AiError_AiCollectionQuestionExisted, Id: v}
		} else {
			rsp.Errors[i] = &ai.RspValidateQAInBulk_Item{Error: pberrors.AiError_AiNoError, Id: 0}
		}
	}

	return nil
}

// ValidateTextFileInBulk 校验文本/文件合法性
func (a *Ai) ValidateTextFileInBulk(ctx context.Context, req *ai.ReqValidateTextFileInBulk, rsp *ai.RspValidateTextFileInBulk) error {
	rsp.Errors = make([]*ai.RspValidateTextFileInBulk_Item, len(req.Items))
	docs := make([]*model.TDoc, 0, len(req.Items))
	for _, v := range req.Items {
		docs = append(docs, new(model.TDoc).FromTextFileProto(v))
	}

	ids, err := logic.NewTextFileValidator(ctx, docs, req.DataSource, req.Contributor, req.ScopedAssistant...).ValidateFileNameRepeated()
	if err != nil {
		return err
	}
	for i, v := range ids {
		if v != 0 {
			rsp.Errors[i] = &ai.RspValidateTextFileInBulk_Item{Error: pberrors.AiError_AiCollectionTextFileExisted, Id: v}
		} else {
			rsp.Errors[i] = &ai.RspValidateTextFileInBulk_Item{Error: pberrors.AiError_AiNoError, Id: 0}
		}
	}

	return nil
}

// DeleteDocInBulk 删除doc
// 非直接删除，先设置关联表状态为删除中，等待同步完成之后才删除 doc
func (a *Ai) DeleteDocInBulk(ctx context.Context, req *ai.ReqDeleteDocInBulk, rsp *ai.RspDeleteDocInBulk) error {
	if req.QueryId > 0 {
		asyncTask, err := logic.CreateDeleteBatchTask(ctx, req)
		rsp.Async = asyncTask
		return err
	}

	if len(req.Id) == 0 {
		return nil
	}
	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		return logic.DeleteDocInBulk(ctx, tx, req.Id, req.IsContributorDelete, req.Operator, req.ScopedAssistantId...)
	})
	if err != nil {
		return err
	}
	return nil
}

// CloneDocInBulk 克隆doc,并走分享
func (a *Ai) CloneDocInBulk(ctx context.Context, req *ai.ReqCloneDocInBulk, rsp *ai.RspCloneDocInBulk) error {
	// if req.QueryId > 0 {
	//	return logic.CreateCloneBatchTask(ctx, req)
	// }
	return logic.CloneDocInBulk(ctx, req, rsp)
}

// OnOffDocInBulk 启用/禁用
func (a *Ai) OnOffDocInBulk(ctx context.Context, req *ai.ReqOnOffDocInBulk, rsp *ai.RspOnOffDocInBulk) error {
	if req.QueryId > 0 {
		asyncTask, err := logic.CreateOnOffBatchTask(ctx, req, rsp)
		rsp.Async = asyncTask
		return err
	}
	// 启用预处理，校验重复
	if req.State == ai.DocState_DOC_STATE_ENABLED {
		err := logic.CheckDocBeforeEnable(ctx, req, rsp)
		if err != nil {
			return err
		}
		if len(rsp.PreRepeatCollections) != 0 || len(rsp.RepeatCollections) != 0 || len(rsp.ExceedQaContainsMatchLimit) != 0 {
			return nil
		}
	}

	return model.Transaction(ctx, func(tx *gorm.DB) error {
		return logic.OnOffDocInBulk(ctx, tx, req.State, req.Id, req.ScopedAssistantId...)
	})
}

// CreateAllDocMd5 创建所有文档md5
func (a *Ai) CreateAllDocMd5(ctx context.Context, req *ai.ReqCreateAllDocMd5, empty *emptypb.Empty) error {
	go func() {
		ctx = context.Background()
		tempPath := "./temp_md5_doc"

		if _, err := os.Stat(tempPath); err != nil {
			if err := os.Mkdir(tempPath, os.ModePerm); err != nil {
				log.WithContext(ctx).Errorf("Mkdir.err: %v", err)
				return
			}
		}

		var rows []*model.TDoc
		var err error

		err = model.NewQuery[model.TDoc](ctx).DB().Model(&model.TDoc{}).
			Where("data_type = ? ", 3).Find(&rows).Error
		if err != nil {
			log.WithContext(ctx).Errorf("model.NewQuery: %v", err)
			return
		}

		for _, row := range rows {

			fileName, err := uuid.GenerateUUID()
			if err != nil {
				return
			}
			localFilePath := path.Join(tempPath, fileName)

			err = new(logic.CosHelper).GetFile(ctx, row.Ref.Url, localFilePath)
			if err != nil {
				log.WithContext(ctx).Errorf("oss.GetPubFile: %v", err)
				return
			}

			md5, err := util.CalculateMD5File(localFilePath)
			if err != nil {
				log.WithContext(ctx).Errorf("logic.CalculateMD5File: %v", err)
				return
			}

			err = model.NewQuery[model.TDoc](ctx).DB().Where("id = ?", row.ID).
				UpdateColumn("index_text_md5", md5).Error
			if err != nil {
				log.WithContext(ctx).Errorf("NewQuery.Update: %v", err)
				return
			}

		}

		log.WithContext(ctx).Infof("NewQuery.Update: %v", rows)
	}()
	return nil
}

// ListDocByRef 根据文档ref_id查询文档
func (a *Ai) ListDocByRef(ctx context.Context, req *ai.ReqListDocByRef, rsp *ai.RspListDocByRef) error {
	if len(req.Ref) == 0 {
		return nil
	}
	var rows []*model.TDoc
	err := model.NewQuery[model.TDoc](ctx).DB().
		Where("rag_filename IN (?)", req.Ref).
		Preload("Contributors").
		Find(&rows).Error
	if err != nil {
		return err
	}
	for _, v := range rows {
		item := &ai.RspListDocByRef_Doc{
			FileName:    v.FileName,
			Contributor: v.ContributorToPb(),
			UpdateBy: &ai.Operator{
				Type:   base.IdentityType(v.UpdateByType),
				Id:     v.UpdateBy,
				UserId: v.UpdateByUser,
			},
			Ref: v.RagFilename,
		}
		switch ai.DocType(v.DataType) {
		case ai.DocType_DOCTYPE_QA:
			item.Text = v.Text
			item.Question = v.IndexText
		case ai.DocType_DOCTYPE_FILE, ai.DocType_DOCTYPE_TEXT:
			item.Text = v.IndexText
			if v.Ref != nil {
				item.Url = v.Ref.Url
			}
		}
		rsp.Docs = append(rsp.Docs, item)
	}
	return nil
}

// UpdateDocAttrInBulk 批量更新doc
func (a *Ai) UpdateDocAttrInBulk(ctx context.Context, req *ai.ReqUpdateDocAttrInBulk, rsp *ai.RspUpdateDocAttrInBulk) error {
	if req.QueryId > 0 {
		asyncTask, err := logic.CreateUpdateBatchTask(ctx, req)
		rsp.Async = asyncTask
		return err
	}
	return logic.UpdateDocAttrInBulk(ctx, req)
}

// CreateDocQuery ...
func (a *Ai) CreateDocQuery(ctx context.Context, req *ai.ReqCreateDocQuery, rsp *ai.RspCreateDocQuery) error {
	var err error
	if req.Qa != nil {
		rsp.QueryId, rsp.TotalCount, err = logic.CreateQAQuery(ctx, req.Qa, req.CreateBy)
	} else if req.Doc != nil {
		rsp.QueryId, rsp.TotalCount, err = logic.CreateDocQuery(ctx, req.Doc, req.CreateBy)
	} else {
		return xerrors.NewCode(pberrors.BaseError_UnprocessableEntity)
	}
	rsp.IsEmpty = rsp.QueryId == 0
	return err
}

// ListSharedAssistant 获取已分享的助手列表，用于表头筛选
func (a *Ai) ListSharedAssistant(ctx context.Context, req *ai.ReqListSharedAssistant, rsp *ai.RspListSharedAssistant) error {
	mainQuery := model.NewQuery[model.TAssistantDoc](ctx).DB().
		Where("is_shared = ?", 2).
		Select("1").
		Where("assistant_id = t_assistant.id")

	// 添加贡献者过滤条件
	if req.GetContributor() != nil {
		contributor := req.GetContributor()
		contributorQuery := model.NewQuery[model.TDocContributor](ctx).DB().
			Select("1").
			Where("t_assistant_doc.doc_id = t_doc_contributor.doc_id")
		if contributor.Id != 0 {
			contributorQuery.Where("contributor_id = ? and contributor_type = ?", contributor.Id, contributor.Type)
		} else if len(contributor.Text) != 0 {
			contributorQuery.Where("contributor_text = ? and contributor_type = ?", contributor.Text, contributor.Type)
		}
		mainQuery = mainQuery.Where("EXISTS (?)", contributorQuery)
	}

	// 添加文档类型过滤
	docQuery := model.NewQuery[model.TDoc](ctx).DB().
		Select("1").
		Where("t_assistant_doc.doc_id = t_doc.id")
	if req.Type != ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_UNSPECIFIED {
		switch req.Type {
		case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_QA:
			docQuery = docQuery.Where("data_type = ?", ai.DocType_DOCTYPE_QA)
		case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_TEXTFILE:
			docQuery = docQuery.Where("data_type in ?", []ai.DocType{ai.DocType_DOCTYPE_TEXT, ai.DocType_DOCTYPE_FILE})
			// 添加数据源过滤
			if req.DataSource != ai.DocDataSource_DOC_DATA_SOURCE_UNSPECIFIED {
				docQuery = docQuery.Where("data_source = ?", req.DataSource)
			}
		case ai.ListDocFilterType_LIST_DOC_FILTER_TYPE_SYSTEM:
			docQuery.Where("data_source = ?", ai.DocDataSource_DOC_DATA_SOURCE_UGC)
		}
	}
	mainQuery = mainQuery.Where("EXISTS (?)", docQuery)

	rows := make([]*model.TAssistant, 0)
	db := model.NewQuery[model.TAssistant](ctx).DB()
	if len(req.Search) != 0 {
		search := "%" + xorm.EscapeLikeWildcards(req.Search) + "%"
		db.Where(db.Where("name like ?", search).Or("name_en like ?", search))
	}
	err := db.Where("EXISTS (?)", mainQuery).Find(&rows).Error
	if err != nil {
		return err
	}

	for _, v := range rows {
		rsp.Assistants = append(rsp.Assistants, &ai.Assistant{
			Id:     v.ID,
			Name:   v.Name,
			NameEn: v.NameEn,
		})
	}
	return nil
}

// ListSharedTeam 查询已分享的团队列表，用于表头筛选
func (a *Ai) ListSharedTeam(ctx context.Context, req *ai.ReqListSharedTeam, rsp *ai.RspListSharedTeam) error {
	teams, err := logic.GetSharedTeamList(ctx, req.Contributor, req.Type, req.DataSource)
	if err != nil {
		return err
	}

	rsp.Ids = teams
	return nil
}

// ListSharedUser 查询已分享的用户列表，用于表头筛选
func (a *Ai) ListSharedUser(ctx context.Context, req *ai.ReqListSharedUser, rsp *ai.RspListSharedUser) error {
	userIds, err := logic.GetSharedReceiverList(ctx, req.Contributor, req.Type, req.DataSource, ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER)
	if err != nil {
		return err
	}

	rsp.Ids = userIds
	return nil
}
