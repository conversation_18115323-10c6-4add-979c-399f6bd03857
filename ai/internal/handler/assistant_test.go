package handler

import (
	"context"
	"fmt"
	"testing"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// func TestMain(m *testing.M) {
// 	b := boot.Bootstrap{
// 		ConfigFile: "./etc/ai.toml",
// 	}
// 	if err := b.<PERSON>(); err != nil {
// 		panic(err)
// 	}
// 	m.Run()
// }

func TestAi_ListAssistant(t *testing.T) {
	rsp := &ai.RspListAssistant{}
	err := new(Ai).ListAssistant(context.Background(), &ai.ReqListAssistant{
		Admins: []*ai.AssistantAdmin{
			{
				Id:   1,
				Type: 1,
			},
			{
				Id:   1,
				Type: 2,
			},
		},
	}, rsp)
	if err != nil {
		t.Fatal(err)
	}
}

func TestA(t *testing.T) {
	var a []*model.TAssistant
	err := model.NewQuery[model.TAssistant](context.Background()).DB().Scopes(model.AssistantWithCollection).Find(&a)
	if err != nil {
		t.Fatal(err)
	}
}

func TestLoadDoc(t *testing.T) {
	doc, err := model.LoadDoc(context.Background(), []uint64{709}, nil)
	if err != nil {
		t.Fatal(err)
	}
	for _, v := range doc {
		fmt.Println(*v)
	}
}
