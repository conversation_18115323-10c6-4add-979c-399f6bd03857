package handler

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	chatlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/chat"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// RateAiAnswerWechat 评价微信问题
func (a *Ai) RateAiAnswerWechat(ctx context.Context, req *aipb.ReqRateAiAnswerWechat, rsp *emptypb.Empty) error {
	if req.AnswerId == 0 {
		//err := model.NewQuery[model.TChatMessage](ctx).
		//	Where("chat_id = ? AND state > ? AND question_id > 0", req.ChatId, model.Unsent).
		//	OrderBy("id", true).
		//	Limit(1).
		//	Pluck("question_id", &req.MessageId)
		//if err != nil {
		//	log.WithContext(ctx).Errorw("RateAiAnswerWechat RateAiAnswer Query", "err", err,
		//		"req", req)
		//	return nil
		//}
		//if req.MessageId == 0 {
		//	log.WithContext(ctx).Error("RateAiAnswerWechat RateAiAnswer Query messageId = 0")
		//	return nil
		//}
		log.WithContext(ctx).Debug("RateAiAnswerWechat answerId is nil")
		return nil
	}

	_, err := model.NewQuery[model.TChatMessage](ctx).UpdateBy(map[string]any{
		"rating_scale": req.RatingScale,
		"update_by":    0,
		"update_date":  time.Now(),
	}, "id = ? ", req.AnswerId)
	if err != nil {
		log.WithContext(ctx).Errorw("WechatAIMessage RateAiAnswer", "err", err, "req", req)
	}
	return err
}

// GetAnswerWechat 获取问题答案
func (a *Ai) GetAnswerWechat(ctx context.Context, req *aipb.ReqGetAnswerWechat, rsp *aipb.RspGetAnswerWechat) error {
	// 校验消息是否重复
	chatMessages, err := model.NewQuery[model.TChatMessage](ctx).GetBy("third_record_uuid = ?", req.ThirdRecordUuid)
	if err != nil {
		return err
	} else if len(chatMessages) > 0 { // 重复消息不做处理
		rsp.QuestionType = aipb.RspGetAnswerWechat_QUESTION_TYPE_ID_REPETITIVE
		return nil
	}

	chatlogic.SetUnSendQuestionCount(ctx, req.Message.ChatId, true) // 未处理问题+1
	defer chatlogic.SetUnSendQuestionCount(ctx, req.Message.ChatId, false)

	chat, err := model.NewQuery[model.TChat](ctx).FindByKey(req.Message.ChatId)
	if err != nil {
		return err
	}
	var question *model.TChatMessage
	if chat.SupportType == int32(aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT) && len(chat.LiveAgentName) == 0 { // 填充默认接待池人名称
		chat.LiveAgentName = "人工接待池中" // 填充默认名称 人工接待池中"
	}
	var lastQuestionId uint64
	lastQuestionId, rsp.QuestionType = chatlogic.SetLastQuestionInfoWechat(ctx, req.Message)

	question, err = chatlogic.SaveQuestionMessage(ctx, req.Message, req.CurrentCursor, req.ThirdRecordUuid,
		req.ExternalUserId, chat.LiveAgentName)
	if err != nil {
		return err
	}

	rsp.QuestionId = question.ID
	req.Message.Id = question.ID
	if req.Message.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT ||
		chat.SupportType == int32(aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT) {
		// 当前保存的是客服的回答或者会话已经转人工了，则不用问ai了
		rsp.SupportType = aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT
		return nil
	}

	var answer = &aipb.ChatMessage{
		AssistantId: req.AssistantDetail.Id,
		Lang:        question.Lang,
		ChatId:      req.Message.ChatId,
		QuestionId:  question.ID,
		StartTime:   req.AnswerStartTime,
		EndTime:     req.AnswerEndTime,
	}
	if len(req.Docs) > 0 && req.Message.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_MATCH_PATTERN { // 处理QA匹配到了答案的情况
		for _, v := range req.Docs {
			answer.DocNames = append(answer.DocNames, v.RagFilename)
		}
		answer.CollectionSnapshot = req.CollectionSnapshot
		answer.Text = req.SetChatAnswer
		answer.DocMatchPattern = req.MatchPattern
		answer.FinalQuery = req.Message.Text
	}

	var saveAnswer *chatlogic.ChatMessage
	switch req.Message.AskType {
	case aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION:
		tasks, err := chatlogic.GetResendChatTask(ctx, lastQuestionId, "")
		if err != nil {
			return err
		}
		if len(tasks) == 0 {
			message, err := question.TransformToAiMessage()
			if err != nil {
				return err
			}
			task := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
				FetchType: aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT,
				ReqMessage: chatlogic.NewChatMessage(message).
					SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, chat.LiveAgentName),
				NoHistoryRounds: true,
			})
			tasks = append(tasks, task)
		}
		answers, err := chatlogic.WxGetChatTasksAnswer(ctx, req.AssistantDetail, tasks)
		if err != nil {
			return err
		}
		rsp.Answers = answers
	case aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL, aipb.QuestionAskType_QUESTION_ASK_TYPE_CONTINUE,
		aipb.QuestionAskType_QUESTION_ASK_TYPE_VOICE: // 获取ai答案
		reqQuestion := chatlogic.NewChatMessage(req.Message).
			SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, chat.LiveAgentName)
		tasks := []*chatlogic.ChatTask{{
			FetchType:  aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT,
			ReqMessage: reqQuestion,
		}}
		answers, err := chatlogic.WxGetChatTasksAnswer(ctx, req.AssistantDetail, tasks)
		if err != nil {
			return err
		}
		rsp.Answers = answers
	case aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET, aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET,
		aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE, aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE,
		aipb.QuestionAskType_QUESTION_ASK_TYPE_MATCH_PATTERN:
		answer.AskType = req.Message.AskType
		if req.Message.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE ||
			req.Message.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE {
			answer.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET
		}
		answer.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER
		if req.Message.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_MATCH_PATTERN {
			answer.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION
		}
		answer.Text = req.SetChatAnswer
		answer.ShowType = int32(chatlogic.MessageShowTypeNormal)
		saveAnswer = chatlogic.NewChatMessage(answer).SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, "")
		dbAn, err := saveAnswer.Save(ctx)
		if err != nil {
			return err
		}
		answer.Id = dbAn.ID
		rsp.Answers = []*aipb.ChatMessage{answer}
	}
	return nil
}

// GetAnswerWechatForFile ...
func (a *Ai) GetAnswerWechatForFile(ctx context.Context, req *aipb.ReqGetAnswerWechat, rsp *aipb.RspGetAnswerWechat) error {
	// 校验消息是否重复
	chatMessages, err := model.NewQuery[model.TChatMessage](ctx).GetBy("third_record_uuid = ?", req.ThirdRecordUuid)
	if err != nil {
		return err
	} else if len(chatMessages) > 0 { // 重复消息不做处理
		rsp.QuestionType = aipb.RspGetAnswerWechat_QUESTION_TYPE_ID_REPETITIVE
		return nil
	}
	chatlogic.SetUnSendQuestionCount(ctx, req.Message.ChatId, true) // 未处理问题+1
	defer chatlogic.SetUnSendQuestionCount(ctx, req.Message.ChatId, false)

	if req.AssistantDetail.MultimodalPrompt != nil && req.Message.Text == "" {
		switch req.FileCmd {
		case aipb.AIFileProcessing_AI_FILE_PROCESSING_BOM_WITHOUT_FILE, aipb.AIFileProcessing_AI_FILE_PROCESSING_BOM_WITH_FILE:
			req.Message.Text = chatlogic.MessageCMDReadBom
		case aipb.AIFileProcessing_AI_FILE_PROCESSING_REPORT_WITHOUT_FILE, aipb.AIFileProcessing_AI_FILE_PROCESSING_REPORT_WITH_FILE:
			req.Message.Text = chatlogic.MessageCMDReadReport
		}
	}

	var question *model.TChatMessage
	question, err = chatlogic.SaveQuestionMessage(ctx, req.Message, req.CurrentCursor, req.ThirdRecordUuid,
		req.ExternalUserId, "")
	if err != nil {
		return err
	}

	rsp.QuestionId = question.ID
	if req.FileCmd == aipb.AIFileProcessing_AI_FILE_PROCESSING_FILE_WITHOUT_CMD {
		// 还没有输入命令，直接返回
		return nil
	}
	req.Message.Id = question.ID

	var tasks []*chatlogic.ChatTask
	var imageUrls []string
	var fileQuestionIds []uint64
	if req.AssistantDetail.MultimodalPrompt != nil {
		switch req.FileCmd {
		case aipb.AIFileProcessing_AI_FILE_PROCESSING_BOM_WITHOUT_FILE: // 配料表
			reqQuestion := chatlogic.NewChatMessage(req.Message).SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, "")
			reqQuestion.Text = req.AssistantDetail.MultimodalPrompt.ReadBom
			reqQuestion.DefaultText = config.GetStringOr("workweixin.ai.nullBomAnswer", "没有识别到配料哦，请换个照片试试呢")
			task := &chatlogic.ChatTask{
				FetchType:  aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
				ReqMessage: reqQuestion,
			}
			task.SetNextTask(&chatlogic.ChatTask{
				NoHistoryRounds: true,
				BuildReqMessage: func(repMsg *chatlogic.ChatMessage) *chatlogic.ChatMessage {
					msg := chatlogic.PromptPrefix(repMsg, req.AssistantDetail.MultimodalPrompt.BomHazards)
					msg.DefaultText = req.TimeoutDefaultAnswer
					return msg
				},
			})
			tasks = append(tasks, task)
		case aipb.AIFileProcessing_AI_FILE_PROCESSING_REPORT_WITHOUT_FILE: // 检测报告
			question1 := chatlogic.NewChatMessage(req.Message).SetText(req.AssistantDetail.MultimodalPrompt.ComplianceMark).SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, "")
			question1.DefaultText = config.GetStringOr("workweixin.ai.nullReportAnswer", "没有识别到报告哦，请换个文件试试呢")
			task := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
				FetchType:  aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
				ReqMessage: question1,
			})
			task.SetNextTask(&chatlogic.ChatTask{
				NoHistoryRounds: true,
				BuildReqMessage: func(repMsg *chatlogic.ChatMessage) *chatlogic.ChatMessage {
					msg := chatlogic.PromptPrefix(repMsg, req.AssistantDetail.MultimodalPrompt.AskMark)
					msg.DefaultText = req.TimeoutDefaultAnswer
					return msg
				},
			})
			question2 := chatlogic.NewChatMessage(req.Message).SetText(req.AssistantDetail.MultimodalPrompt.ReadTestReport).SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, "")
			question2.DefaultText = config.GetStringOr("workweixin.ai.nullReportAnswer", "没有识别到报告哦，请换个文件试试呢")
			task2 := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
				FetchType:  aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
				ReqMessage: question2,
			})
			tasks = []*chatlogic.ChatTask{task, task2}
		case aipb.AIFileProcessing_AI_FILE_PROCESSING_REPORT_WITH_FILE: // 检测报告
			imageUrls, fileQuestionIds = chatlogic.GetChatPictureList(ctx, req.Message.ChatId, question.ID, true)
			slices.Reverse(imageUrls)
			for _, imageUrl := range imageUrls {
				req.Message.ImageUrl = []string{imageUrl}
				// 识别认证标识
				question1 := chatlogic.NewChatMessage(req.Message).SetText(req.AssistantDetail.MultimodalPrompt.ComplianceMark).SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, "")
				question1.DefaultText = config.GetStringOr("workweixin.ai.nullReportAnswer", "没有识别到报告哦，请换个文件试试呢")
				task := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
					FetchType:  aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
					ReqMessage: question1,
				})
				task.SetNextTask(&chatlogic.ChatTask{
					NoHistoryRounds: true,
					BuildReqMessage: func(repMsg *chatlogic.ChatMessage) *chatlogic.ChatMessage {
						msg := chatlogic.PromptPrefix(repMsg, req.AssistantDetail.MultimodalPrompt.AskMark)
						msg.DefaultText = req.TimeoutDefaultAnswer
						return msg
					},
				})
				// 读检测报告
				question2 := chatlogic.NewChatMessage(req.Message).SetText(req.AssistantDetail.MultimodalPrompt.ReadTestReport).SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, "")
				question2.DefaultText = config.GetStringOr("workweixin.ai.nullReportAnswer", "没有识别到报告哦，请换个文件试试呢")
				task2 := chatlogic.NewChatTask(ctx, chatlogic.ChatTask{
					FetchType:  aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
					ReqMessage: question1,
				})
				tasks = []*chatlogic.ChatTask{task, task2}
			}
			rsp.QuestionType = aipb.RspGetAnswerWechat_QUESTION_TYPE_CLEAR_FILE_CACHE
		case aipb.AIFileProcessing_AI_FILE_PROCESSING_BOM_WITH_FILE: // 配料表
			imageUrls, fileQuestionIds = chatlogic.GetChatPictureList(ctx, req.Message.ChatId, question.ID, true)
			for _, imageUrl := range imageUrls {
				reqQuestion := chatlogic.NewChatMessage(req.Message).SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, "")
				reqQuestion.ImageUrl = []string{imageUrl}
				reqQuestion.Text = req.AssistantDetail.MultimodalPrompt.ReadBom
				reqQuestion.DefaultText = config.GetStringOr("workweixin.ai.nullBomAnswer", "没有识别到配料哦，请换个照片试试呢")
				task := &chatlogic.ChatTask{
					FetchType:  aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
					ReqMessage: reqQuestion,
				}
				task.SetNextTask(&chatlogic.ChatTask{
					NoHistoryRounds: true,
					BuildReqMessage: func(repMsg *chatlogic.ChatMessage) *chatlogic.ChatMessage {
						msg := chatlogic.PromptPrefix(repMsg, req.AssistantDetail.MultimodalPrompt.BomHazards)
						msg.DefaultText = req.TimeoutDefaultAnswer
						return msg
					},
				})
				tasks = append(tasks, task)
			}
			rsp.QuestionType = aipb.RspGetAnswerWechat_QUESTION_TYPE_CLEAR_FILE_CACHE
		default:
		}
	}
	if aipb.AIFileProcessing_AI_FILE_PROCESSING_FILL_WITT_USER_CMD == req.FileCmd {
		imageUrls, fileQuestionIds = chatlogic.GetChatPictureList(ctx, req.Message.ChatId, question.ID, true)
		reqQuestion := chatlogic.NewChatMessage(req.Message).SetWorkWeiXinRecordValue(req.NextCursor, "", req.ExternalUserId, "")
		reqQuestion.ImageUrl = imageUrls
		reqQuestion.DefaultText = config.GetStringOr("workweixin.ai.nullUserCmdAnswer", "照片无法识别，请换个照片试试呢")
		tasks = append(tasks, &chatlogic.ChatTask{
			FetchType:  aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_VISION,
			ReqMessage: reqQuestion,
		})
		rsp.QuestionType = aipb.RspGetAnswerWechat_QUESTION_TYPE_CLEAR_FILE_CACHE
	}

	answers, err := chatlogic.WxGetChatTasksAnswer(ctx, req.AssistantDetail, tasks)
	if err != nil {
		return err
	}

	rsp.Answers = answers
	rsp.FileQuestionIds = fileQuestionIds
	return nil
}

// GetAnswerWechatForContinue 获取继续回答的纯文本答案
func (a *Ai) GetAnswerWechatForContinue(ctx context.Context, req *aipb.ReqGetAnswerWechat,
	rsp *aipb.RspGetAnswerWechat) error {
	if req.RecordId == 0 {
		log.WithContext(ctx).Errorw("GetAnswerWechatForContinue param error", "req", req)
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	// 校验消息是否重复
	chatMessages, err := model.NewQuery[model.TChatMessage](ctx).GetBy("third_record_uuid = ?", req.ThirdRecordUuid)
	if err != nil {
		return err
	} else if len(chatMessages) > 0 { // 重复消息不做处理
		rsp.QuestionType = aipb.RspGetAnswerWechat_QUESTION_TYPE_ID_REPETITIVE
		return nil
	}

	chatlogic.SetUnSendQuestionCount(ctx, req.Message.ChatId, true) // 未处理问题+1
	defer chatlogic.SetUnSendQuestionCount(ctx, req.Message.ChatId, false)

	question, err := chatlogic.SaveQuestionMessage(ctx, req.Message, req.CurrentCursor, req.ThirdRecordUuid,
		req.ExternalUserId, "")
	if err != nil {
		return err
	}
	rsp.QuestionId = question.ID

	records, err := model.NewQuery[model.TChatSendRecord](ctx).Select("id", "message_id", "content", "type", "extra_id").
		Wheres(func(db *gorm.DB) {
			db.Where("chat_id = ?", question.ChatID)
			db.Where("message_id < ?", question.ID)
			db.Where("id > ?", req.RecordId)
			db.Where("message_type in ?", []aipb.AiRecordMessageType{
				aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL,
				aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER,
			})
			db.Where("state = 1")// 未发送
			//db.Where("type != ?", int32(aipb.AiRecordType_AI_RECORD_TYPE_USER))
		}).Limit(chatlogic.WorkWeixinMaxSendSize+1).OrderBy("id", false).Get()
	if err != nil {
		return err
	}

	var suggestQuestionId []uint64
	for i := 0; i < len(records) && i < chatlogic.WorkWeixinMaxSendSize; i++ {
		if records[i].Type == aipb.AiRecordType_AI_RECORD_TYPE_USER {
			break
		}
		if records[i].Type == aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU {
			suggestQuestionId = append(suggestQuestionId, records[i].MessageID)
		}
	}

	// 填充建议问题
	var suggest map[uint64][]string

	if len(suggestQuestionId) > 0 {
		var messages []*model.TChatMessage
		suggest = make(map[uint64][]string)
		model.NewQuery[model.TChatMessage](ctx).DB().Preload("Suggests").Find(&messages, "id in ?", suggestQuestionId)
		for _, v := range messages {
			if len(v.Suggests) > 0 {
				var suggests []string
				for _, s := range v.Suggests {
					suggests = append(suggests, s.Suggest)
				}
				suggest[v.ID] = suggests
			}
		}
	}

	for i := 0; i < len(records) && i < chatlogic.WorkWeixinMaxSendSize; i++ {
		if records[i].Type == aipb.AiRecordType_AI_RECORD_TYPE_USER {
			break
		}
		v := &aipb.AiSendResultRecord{
			Id:                records[i].ID,
			Content:           records[i].Content,
			Type:              records[i].Type,
			ContinueAnswering: records[i].SendType == chatlogic.RecordTypeContinueMenu,
			MessageId:         records[i].MessageID,
			ExtraId:           records[i].ExtraID,
		}
		rsp.AnswerSharding = append(rsp.AnswerSharding, v)
		if records[i].Type == aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU {
			v.SuggestQuestions = suggest[records[i].MessageID]
		}
	}
	if len(rsp.AnswerSharding) == chatlogic.WorkWeixinMaxSendSize && len(records) == chatlogic.WorkWeixinMaxSendSize+1 &&
		records[chatlogic.WorkWeixinMaxSendSize].Type != aipb.AiRecordType_AI_RECORD_TYPE_USER { // 说明后续还有非用户回答的消息，追加继续回答
		rsp.AnswerSharding[chatlogic.WorkWeixinMaxSendSize-1].ContinueAnswering = true
	}

	return nil
}

// UpdateSendRecord 更新发送记录
func (a *Ai) UpdateSendRecord(ctx context.Context, req *aipb.ReqUpdateSendRecord, _ *emptypb.Empty) error {
	if len(req.UserRecallMsgUuid) > 0 {
		_, err := model.NewQuery[model.TChatMessage](ctx).UpdateBy(map[string]interface{}{
			"ask_type": aipb.QuestionAskType_QUESTION_ASK_TYPE_RECALL,
		}, "third_record_uuid = ?", req.UserRecallMsgUuid)
		if err != nil {
			return err
		}
	}

	if len(req.Records) == 0 {
		return nil
	}

	var rows []*model.TChatSendRecord
	var rowsByUuid []*model.TChatSendRecord
	var uuids []string
	now := time.Now()
	for _, v := range req.Records {
		if v.Id == 0 && v.Uuid != "" {
			rowsByUuid = append(rowsByUuid, &model.TChatSendRecord{
				UUID:     v.Uuid,
				SendDate: now,
				Info:     v.Info,
				State:    v.State,
			})
			uuids = append(uuids, v.Uuid)
			continue
		}

		rows = append(rows, &model.TChatSendRecord{
			ID:       v.Id,
			UUID:     v.Uuid,
			SendDate: now,
			Info:     v.Info,
			State:    v.State,
		})
	}
	return model.Transaction(ctx, func(tx *gorm.DB) error {
		if len(uuids) > 0 {
			infoSql := " info = CASE"
			stateSql := " state = CASE"
			var infoArgs []interface{}
			var stateArgs []interface{}
			for _, v := range rowsByUuid {
				infoSql += " WHEN uuid = ? THEN ?"
				infoArgs = append(infoArgs, v.UUID, v.Info)
				stateSql += " WHEN uuid = ? THEN ?"
				stateArgs = append(stateArgs, v.UUID, v.State)
			}
			sql := "UPDATE t_chat_send_record SET " + infoSql + " ELSE '' END," + stateSql + " ELSE 0 END WHERE uuid IN ?"
			err := xorm.NewQueryWithDB[model.TChatSendRecord](tx).DB().
				Exec(sql, append(infoArgs, append(stateArgs, uuids)...)...).Error
			if err != nil {
				return err
			}
		}

		if len(rows) > 0 {
			return xorm.NewQueryWithDB[model.TChatSendRecord](tx).Clause(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns([]string{"state", "uuid", "info", "send_date"}),
			}).Insert(rows)
		}
		return nil
	})

}

// FinishChatWechat 结束微信会话
func (a *Ai) FinishChatWechat(ctx context.Context, req *aipb.ReqFinishChatWechat, rsp *emptypb.Empty) error {
	if len(req.ExternalUserId) == 0 || req.AssistantId == 0 {
		log.WithContext(ctx).Errorw("FinishChatWechat param error", "req", req)
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	chat, err := model.NewQuery[model.TChat](ctx).Where("external_user_id = ? AND assistant_id = ? AND type = ?",
		req.ExternalUserId, req.AssistantId, req.ChatType).
		OrderBy("id", true).
		Find()

	if err != nil {
		log.WithContext(ctx).Errorw("UpdateChatType find err", "err", err)
		return nil
	}
	if chat == nil || chat.ID == 0 || chat.FinishDate != nil {
		log.WithContext(ctx).Debugw("UpdateChatType find no chat", "req", req, "chat", chat)
		return nil
	}

	_, err = model.NewQuery[model.TChat](ctx).UpdateByKey(chat.ID, map[string]any{
		"finish_date": time.Now(),
	})
	if err != nil {
		log.WithContext(ctx).Errorw("UpdateChatType err", "err", err, "req", req)
	}
	return err
}

// GetWhiteListWechat 获取微信白名单
func (a *Ai) GetWhiteListWechat(ctx context.Context, req *aipb.ReqGetWhiteListWechat, rsp *aipb.RspGetWhiteListWechat) error {
	var username []string
	err := model.NewQuery[model.TChatUser](ctx).Where("assistant_id = ?", req.AssistantId).Pluck("username", &username)
	if err != nil {
		log.WithContext(ctx).Errorw("getWeixinWhiteList err", "err", err, "assistant_id", req.AssistantId)
	}
	rsp.Username = username
	return err
}

// UpdateChatMessageStateWechat 更新微信问答状态
//
//	@Description: 由于增加了t_chat_send_record 表，扩展为 可以更新问题和答案的状态、发送记录状态 其中一个或者组合
func (a *Ai) UpdateChatMessageStateWechat(ctx context.Context, req *aipb.ReqUpdateChatMessageStateWechat, _ *emptypb.Empty) error {
	if len(req.Infos) == 0 {
		log.WithContext(ctx).Errorw("updateMessageState param error", "req", req)
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	sendFlag := aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND_DEFAULT
	if req.NormalReply {
		sendFlag = aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND
	}

	var updateRecord []*model.TChatSendRecord
	var updateMessage []*model.TChatMessage
	questionIds := req.FileQuestionIds
	now := time.Now()
	for _, info := range req.Infos {
		if info.AnswerId > 0 {
			updateMessage = append(updateMessage, &model.TChatMessage{
				ID:           info.AnswerId,
				State:        int32(sendFlag),
				RejectReason: info.RejectReason,
			})
		}
		if info.QuestionId > 0 {
			questionIds = append(questionIds, info.QuestionId)
		}

		for _, v := range info.Records {
			updateRecord = append(updateRecord, &model.TChatSendRecord{
				ID:       v.Id,
				State:    v.State,
				UUID:     v.Uuid,
				Info:     v.Info,
				SendDate: now,
			})
		}
	}
	if req.QuestionId > 0 {
		questionIds = append(questionIds, req.QuestionId)
	}

	return model.Transaction(ctx, func(tx *gorm.DB) error {
		if req.ChatId > 0 {
			_, err := xorm.NewQueryWithDB[model.TChat](tx).UpdateByKey(req.ChatId, map[string]any{
				"default_answer_record": req.DefaultAnswerRecord,
			})
			if err != nil {
				return err
			}
		}

		// 更新问题的状态
		if len(questionIds) > 0 {
			_, err := xorm.NewQueryWithDB[model.TChatMessage](tx).UpdateBy(map[string]any{
				"state":    sendFlag,
				"end_time": time.Now(),
			}, "id in ? ", questionIds)
			if err != nil {
				return err
			}
		}

		// 更新答案的状态和审核结果
		if len(updateMessage) > 0 {
			if err := xorm.NewQueryWithDB[model.TChatMessage](tx).Clause(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns([]string{"state", "reject_reason"}),
			}).Insert(updateMessage); err != nil {
				return err
			}
		}

		// 更新记录表的信息
		if len(updateRecord) > 0 {
			return xorm.NewQueryWithDB[model.TChatSendRecord](tx).Clause(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns([]string{"state", "uuid", "info", "send_date"}),
			}).Insert(updateRecord)
		}

		return nil
	})
}

// GetMessageCursorWechat 获取微信消息游标
func (a *Ai) GetMessageCursorWechat(ctx context.Context, req *aipb.ReqGetMessageCursorWechat,
	rsp *aipb.RspGetMessageCursorWechat) error {
	return model.NewQuery[model.TChatMessage](ctx).
		Where("assistant_id = ?", req.AssistantId).
		Where("next_cursor != ''").
		Where("question_id > 0 ").
		OrderBy("id", true).Limit(1).Pluck("next_cursor", &rsp.Cursor)

	//return model.NewQuery[model.TChatMessage](ctx).Where(
	//	"question_id > 0 and state in ? and next_cursor != '' and assistant_id = ?",
	//	[]int{model.Send, model.SendDefault}, req.AssistantId).
	//	OrderBy("id", true).
	//	Limit(1).
	//	Pluck("next_cursor", &rsp.Cursor)
}

// GetChatWechat 获取微信用户的会话
func (a *Ai) GetChatWechat(ctx context.Context, req *aipb.ReqGetChatWechat, rsp *aipb.RspGetChatWechat) error {
	if len(req.ExternalUserIds) == 0 || req.AssistantId == 0 || req.ChatType == aipb.ChatType_CHAT_TYPE_UNSPECIFIED {
		log.WithContext(ctx).Errorw("GetChatIDWechat param error", "req", req)
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	var chatIds []uint64
	err := model.NewQuery[model.TChat](ctx).Wheres(
		func(db *gorm.DB) {
			db.Select("max(id) as id").
				Where("external_user_id in ? ", req.ExternalUserIds).Group("external_user_id").
				Where("type = ? ", req.ChatType). //type =2 微信
				Where("assistant_id = ? ", req.AssistantId).
				Group("external_user_id")
		}).Pluck("id", &chatIds)
	if err != nil {
		return err
	}
	if len(chatIds) == 0 {
		return nil
	}
	chats, err := model.NewQuery[model.TChat](ctx).Where("id in (?)", chatIds).Get()
	if err != nil {
		log.WithContext(ctx).Errorw("FindChatByAppID chatIds", "err", err, "ExternalUserIds", req.ExternalUserIds)
		return err
	}
	if len(chats) == 0 {
		return nil
	}
	updateDateInfoMap, err := chatlogic.GetMessageMaxUpdateDateByChatID(ctx, chatIds)
	if err != nil {
		return err
	}

	rsp.ChatMap = make(map[string]*aipb.ChatWeChat)
	rsp.FinishedChatLiveAgentMap = make(map[string]string)
	for _, chat := range chats {
		pbChat := model.TransformChatWechatToPb(chat)
		if v, ok := updateDateInfoMap[chat.ID]; ok && v.Before(time.Now().Add(-48*time.Hour)) { // 微信默认是48小时退出当前会话
			pbChat.End = true // 微信退出了就不要保留人工客服状态了
			rsp.ChatMap[chat.ExternalUserID] = pbChat
			continue
		}
		duration := time.Duration(logic.GetChatFinishState(req.ChatIdleDuration)) * time.Minute
		v, ok := updateDateInfoMap[chat.ID]
		if ok && v.Before(time.Now().Add(-duration)) || chat.FinishDate != nil { // 最近一次会话已结束
			if chat.SupportType == int32(aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT) {
				rsp.FinishedChatLiveAgentMap[chat.ExternalUserID] = chat.LiveAgentName // 记录48小时内的人工客服信息
			}
			pbChat.End = true // 微信退出了就不要保留人工客服状态了
		}
		rsp.ChatMap[chat.ExternalUserID] = pbChat
	}

	return nil
}

// CreateChatWechat 创建用户会话
func (a *Ai) CreateChatWechat(ctx context.Context, req *aipb.ReqCreateChatWechat,
	rsp *aipb.RspCreateChatWechat) error {

	if len(req.Chats) == 0 {
		return nil
	}
	chats := make([]*model.TChat, len(req.Chats))
	now := time.Now()
	for i, chat := range req.Chats {
		chats[i] = &model.TChat{
			Title:               chat.Title,
			CreateDate:          now,
			Type:                int32(chat.ChatType),
			AppID:               chat.AppId,
			ExternalUserID:      chat.ExternalUserId,
			Nickname:            chat.Nickname,
			AssistantID:         req.AssistantId,
			SupportType:         int32(chat.SupportType),
			LiveAgentName:       chat.LiveAgentName,
			DefaultAnswerRecord: chat.DefaultAnswerRecord,
		}
	}

	if err := model.NewQuery[model.TChat](ctx).Insert(chats); err != nil {
		log.WithContext(ctx).Errorw("CreateChatWechat err", "err", err, "req", req)
		return err
	}
	rsp.ChatMap = make(map[string]uint64)
	for _, chat := range chats {
		rsp.ChatMap[chat.ExternalUserID] = chat.ID
	}

	return nil
}

// GetLastQuestionStateWechat 获取最后一个微信问题状态
// 如果state=0 表示没有上一条消息或者上一条已经超时了;2 上一条已发送;3 上一条消息发送了默认信息
func (a *Ai) GetLastQuestionStateWechat(ctx context.Context, req *aipb.ReqGetLastQuestionStateWechat,
	rsp *aipb.RspGetLastQuestionStateWechat) error {
	messages, err := model.NewQuery[model.TChatMessage](ctx).Select("state", "create_date").
		Where("question_id = 0 and live_agent_name = '' "). // 获取非工人坐席回答的问题
		Where("type = ?", aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER).
		Where("chat_id = ? ", req.ChatId).
		Where("id < ?", req.QuestionId).
		OrderBy("id", true).
		Limit(1).Get()
	if err != nil {
		return nil
	}
	if len(messages) == 0 {
		return nil
	}
	sec := time.Duration(config.GetIntOr("workWeixin.ai.questionMixSecond", 47)) * time.Second
	if messages[0].CreateDate.Before(time.Now().Add(sec)) { // 47秒以前的数据忽略了
		return nil
	}
	rsp.State = messages[0].State

	return nil
}

// UpdateChatMessageRecordWechat 更新chat的默认回答记录
func (a *Ai) UpdateChatMessageRecordWechat(ctx context.Context, req *aipb.ReqUpdateChatMessageRecordWechat,
	rsp *aipb.RspUpdateChatMessageRecordWechat) error {
	answer := chatlogic.NewChatMessage(&aipb.ChatMessage{
		ChatId:      req.ChatId,
		Text:        req.AnswerText,
		QuestionId:  req.QuestionId,
		Type:        aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER,
		Lang:        "zh",
		AssistantId: req.AssistantId,
	})
	answer.SetWorkWeiXinRecordValue("", req.AnswerThirdRecordUuid, req.ExternalUserId, "")
	dbm, err := answer.TransformToTMessage()
	dbm.State = int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND)
	if err != nil {
		return err
	}

	var record *model.TChatSendRecord
	//判断是否追加继续回答的按钮
	canSend, continueAnswering := chatlogic.GetRecordSendInfo(ctx, req.ChatId)
	if err = model.Transaction(ctx, func(tx *gorm.DB) error {
		_, err = xorm.NewQueryWithDB[model.TChat](tx).UpdateByKey(req.ChatId, map[string]any{
			model.TChatColumns.DefaultAnswerRecord: req.DefaultAnswerRecord,
		})
		if err != nil {
			return err
		}
		if err = xorm.NewQueryWithDB[model.TChatMessage](tx).Create(dbm); err != nil {
			return err
		}

		record = &model.TChatSendRecord{
			MessageID:  dbm.ID,
			Content:    req.AnswerText,
			ChatID:     req.ChatId,
			Type:       req.SendRecordType,
			State:      int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_UNSENT), // 1 默认未发送
			UUID:       req.AnswerThirdRecordUuid,
			CreateDate: time.Now(),
			SendDate:   time.Now(),
		}
		return xorm.NewQueryWithDB[model.TChatSendRecord](tx).Create(record)
	}); err != nil {
		return err
	}
	if canSend {
		rsp.RecordId = record.ID
		rsp.ContinueAnswering = continueAnswering
	}
	rsp.AnswerId = dbm.ID

	return nil
}
