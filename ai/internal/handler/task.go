package handler

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/golang/protobuf/ptypes/empty"
)

func (a *Ai) CreateExportTask(ctx context.Context, req *aipb.ReqCreateExportTask, rsp *aipb.RspCreateExportTask) error {
	var err error
	task := &model.TExportTask{
		Type:           int32(req.Type),
		CreateBy:       req.UserId,
		OperationType:  uint32(req.OperationType),
		FilterSnapshot: req.FilterSnapshot,
		FieldsSnapshot: req.FieldsSnapshot,
	}
	if err = model.NewQuery[model.TExportTask](ctx).Save(task); err != nil {
		return err
	}
	if rsp.Task, err = task.ToPb(); err != nil {
		return err
	}
	return nil
}

func (a *Ai) UpdateExportTask(ctx context.Context, req *aipb.ReqUpdateExportTask, _ *empty.Empty) error {
	fields := map[string]interface{}{}
	if req.State > 0 {
		fields[model.TExportTaskColumns.State] = req.State
	}

	if req.Url != "" {
		fields[model.TExportTaskColumns.URL] = req.Url
	}
	if req.ExtraInfo != "" {
		fields[model.TExportTaskColumns.ExtraInfo] = req.ExtraInfo
	}

	if len(req.Paths) > 0 {
		fields[model.TExportTaskColumns.Paths] = req.Paths
	}

	if req.FieldsSnapshot != "" {
		fields[model.TExportTaskColumns.FieldsSnapshot] = req.FieldsSnapshot
	}
	if req.FilterSnapshot != "" {
		fields[model.TExportTaskColumns.FilterSnapshot] = req.FilterSnapshot
	}

	_, err := model.NewQuery[model.TExportTask](ctx).UpdateByKey(req.Id, fields)
	if err != nil {
		return err
	}
	return nil
}

func (a *Ai) DescribeExportTasks(ctx context.Context, req *aipb.ReqDescribeExportTasks, rsp *aipb.RspDescribeExportTasks) error {
	db := model.NewQuery[model.TExportTask](ctx).OrderBy(model.TExportTaskColumns.CreateDate, true)
	if len(req.Type) > 0 {
		db = db.Where("type in (?)", req.Type)
	}
	if req.OperationType > 0 {
		db = db.Where("operation_type = ?", req.OperationType)
	}
	if len(req.Ids) > 0 {
		db = db.Where("id in (?)", req.Ids)
	}
	tasks, err := db.GetBy("create_by = ?", req.UserId)
	if err != nil {
		return err
	}
	for _, task := range tasks {
		if tb, err := task.ToPb(); err != nil {
			return err
		} else {
			rsp.Tasks = append(rsp.Tasks, tb)
		}
	}

	go func() {
		err := MarkStaleTask(context.WithoutCancel(ctx))
		if err != nil {
			log.WithContext(ctx).Errorf("mark stale task failed: %v", err)
			return
		}
	}()
	return nil
}

// TaskMaxAlvieDuration 任务最大存活时间为2小时
var TaskMaxAlvieDuration = func() time.Duration {
	d, err := time.ParseDuration(config.GetStringOr("task.max_alvie_duration", "2h"))
	if err != nil {
		return time.Hour * 2
	}
	return d
}

// MarkStaleTask 标记任务已经超市失败
func MarkStaleTask(ctx context.Context) error {
	return model.NewQuery[model.TExportTask](ctx).DB().
		Where("last_update_date < ?", time.Now().Add(-TaskMaxAlvieDuration())).
		Where("state = ?", aipb.ExportTaskState_EXPORT_TASK_STATE_RUNNING).
		UpdateColumn("state", aipb.ExportTaskState_EXPORT_TASK_STATE_FAILED).Error
}
