package handler

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	feedbacklogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/feedback"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"google.golang.org/protobuf/types/known/emptypb"
)

// RateAiAnswer 评价AI回答
func (a *Ai) RateAiAnswer(ctx context.Context, req *aipb.ReqRateAiAnswer, _ *emptypb.Empty) error {
	l := logic.RateAnswerLogic{}

	message, err := l.CheckMessage(ctx, req.ChatMessageId, req.UpdateBy)
	if err != nil {
		return err
	}

	err = l.Rate(ctx, message, req.RatingScale, req.UpdateBy)
	if err != nil {
		return err
	}

	return nil
}

// UpsertUserFeedback 更新/创建用户反馈
func (a *Ai) UpsertUserFeedback(ctx context.Context, req *aipb.ReqUpsertUserFeedback, rsp *aipb.RspUpsertFeedback) error {
	l := feedbacklogic.UpsertUserFeedbackLogic{}

	feedback, err := l.Upsert(ctx, req)
	if err != nil {
		return err
	}

	rsp.FeedbackId = feedback.ID
	return nil
}

// UpsertOpFeedback 更新/创建运营反馈
func (a *Ai) UpsertOpFeedback(ctx context.Context, req *aipb.ReqUpsertOpFeedback, rsp *aipb.RspUpsertFeedback) error {
	l := feedbacklogic.UpsertOpFeedbackLogic{}

	feedback, err := l.Upsert(ctx, req)
	if err != nil {
		return err
	}

	rsp.FeedbackId = feedback.ID
	return nil
}

// UpsertMgmtFeedback 创建/更新碳LIVE反馈
func (a *Ai) UpsertMgmtFeedback(ctx context.Context, req *aipb.ReqUpsertMgmtFeedback, rsp *aipb.RspUpsertFeedback) error {
	l := feedbacklogic.UpsertMgmtFeedbackLogic{}

	feedback, err := l.Upsert(ctx, req)
	if err != nil {
		return err
	}

	rsp.FeedbackId = feedback.ID

	return nil
}

// GetFeedbacks 查询用户反馈列表
func (a *Ai) GetFeedbacks(ctx context.Context,
	req *aipb.ReqGetFeedbacks, rsp *aipb.RspGetFeedbacks) error {
	l := feedbacklogic.GetFeedbacksLogic{}

	feedbacks, err := l.Get(ctx, req)
	if err != nil {
		return err
	}

	var totalCount uint32
	if req.WithTotalCount {
		totalCount, err = l.Count(ctx, req)
		if err != nil {
			return err
		}
	}

	rsp.TotalCount = totalCount
	rsp.Feedbacks = model.TFeedbacks(feedbacks).ToFullPb()

	return nil
}

// ReadFeedback 已读用户反馈
func (a *Ai) ReadFeedback(ctx context.Context, req *aipb.ReqReadFeedback, _ *emptypb.Empty) error {
	l := feedbacklogic.ReadFeedbackLogic{}

	feedback, err := l.Check(ctx, req.FeedbackId)
	if err != nil {
		return err
	}

	var readBy uint64
	var operatorType int32

	switch req.UserType.(type) {
	case *aipb.ReqReadFeedback_PortalHandledBy:
		readBy = req.GetPortalHandledBy()
		operatorType = model.FeedbackLogOperatorTypePortal
	case *aipb.ReqReadFeedback_MgmtReadBy:
		readBy = req.GetMgmtReadBy()
		operatorType = model.FeedbackLogOperatorTypeMgmt
	}

	err = l.Read(ctx, feedback, readBy, operatorType)
	if err != nil {
		return err
	}

	return nil
}

// AcceptFeedback 采用用户反馈
func (a *Ai) AcceptFeedback(ctx context.Context, req *aipb.ReqAcceptFeedback, _ *emptypb.Empty) error {
	l := feedbacklogic.AcceptFeedbackLogic{}

	feedback, err := l.Check(ctx, req.FeedbackId, req.Operator)
	if err != nil {
		return err
	}

	err = l.Accept(ctx, feedback, req)
	if err != nil {
		return err
	}

	return nil
}

// GetFeedbackLogs 查询用户反馈日志列表
func (a *Ai) GetFeedbackLogs(ctx context.Context,
	req *aipb.ReqGetFeedbackLogs, rsp *aipb.RspGetFeedbackLogs) error {
	l := feedbacklogic.GetFeedbackLogsLogic{}

	logs, err := l.Get(ctx, req)
	if err != nil {
		return err
	}

	var totalCount uint32
	if req.WithTotalCount {
		totalCount, err = l.Count(ctx, req)
		if err != nil {
			return err
		}
	}

	rsp.TotalCount = totalCount
	rsp.Logs = model.TFeedbackLogs(logs).ToFullPb()

	return nil
}

// DescribeFeedbackRegionCode ...
func (a *Ai) DescribeFeedbackRegionCode(ctx context.Context, _ *aipb.ReqDescribeFeedbackRegionCode,
	rsp *aipb.RspDescribeFeedbackRegionCode) error {
	return model.NewQuery[model.TFeedback](ctx).DB().Distinct().Pluck("region_code", &rsp.RegionCodes).Error
}
