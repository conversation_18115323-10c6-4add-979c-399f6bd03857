package handler

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	assistantlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/assistant"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/db/xormhelper"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ListAssistant 获取ai助手列表
func (a *Ai) ListAssistant(ctx context.Context, req *aipb.ReqListAssistant, rsp *aipb.RspListAssistant) error {
	query, err := logic.ListAssistantApplyFilter(ctx, req)
	if err != nil {
		return err
	}

	total, err := query.Count()
	if err != nil {
		return err
	}
	if req.Order != nil {
		query.OrderBy(req.Order.Column, req.Order.Desc)
	} else {
		query.OrderBy(model.TAssistantColumns.Name, false)
	}

	if req.Page != nil {
		pager := xorm.NewPaginator(req.Page.Offset, req.Page.Limit)
		(&pager).Apply(query.DB())
	}

	rows, err := query.Get()
	if err != nil {
		return err
	}
	for _, row := range rows {
		rsp.Assistants = append(rsp.Assistants, row.ToProto())
	}
	rsp.Total = uint32(total)
	return nil
}

// GetAssistant 获取小助手详细信息
func (a *Ai) GetAssistant(ctx context.Context, req *aipb.ReqGetAssistant, rsp *aipb.RspGetAssistant) error {
	var assistant *model.TAssistant
	var err error

	if req.Id > 0 {
		assistant, err = logic.GetAssistantWithCollection(ctx, req.Id)
	} else if len(req.AppCode) > 0 {
		err = model.NewQuery[model.TAssistant](ctx).Wheres(func(db *gorm.DB) {
			if len(req.CorpId) == 0 { // 查询whatsapp
				db.Where("channel = ?", aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP)
				db.Where("JSON_UNQUOTE(JSON_EXTRACT(`whatsapp_develop_config`,'$.business_number')) = ?", req.AppCode)
			} else {
				db.Where("JSON_UNQUOTE(JSON_EXTRACT(`weixin_develop_config`,'$.corp_id')) = ?", req.CorpId)
				db.Where("JSON_UNQUOTE(JSON_EXTRACT(`weixin_develop_config`,'$.open_kfid')) = ?", req.AppCode)
				db.Where("channel in (?)", []aipb.AssistantChannel{
					aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
					aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN,
				})
			}
		}).DB().Scopes(model.AssistantWithCollection).Find(&assistant).Error
	}
	if err != nil {
		return err
	}
	if assistant == nil {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	pbAssistant := assistant.TransToPbAssistantDetail(req.Language)
	rsp.AssistantDetail = pbAssistant
	return nil
}

// GetAssistantInfoMap 获取助手信息map
func (a *Ai) GetAssistantInfoMap(ctx context.Context, req *aipb.ReqGetAssistantInfoMap,
	rsp *aipb.RspGetAssistantInfoMap) error {
	var rows []*model.TAssistant
	err := model.NewQuery[model.TAssistant](ctx).Select("id", "name", "name_en", "chat_idle_duration",
		"create_by", "channel", "create_date", "enabled", "is_draft").
		Wheres(func(db *gorm.DB) {
			if len(req.Ids) > 0 {
				db = db.Where("id in ?", req.Ids)
			}
		}).Scan(&rows)

	if err != nil {
		return err
	}

	rsp.InfoMap = make(map[uint64]*aipb.RspGetAssistantInfoMap_Info)
	for _, v := range rows {
		rsp.InfoMap[v.ID] = &aipb.RspGetAssistantInfoMap_Info{
			Name:             v.Name,
			NameEn:           v.NameEn,
			ChatIdleDuration: logic.GetChatFinishState(int64(v.ChatIdleDuration)),
			CreateBy:         v.CreateBy,
			Channel:          v.Channel,
			CreateDate:       timestamppb.New(v.CreateDate),
			Enabled:          v.Enabled,
			IsDraft:          v.IsDraft,
			Id:               v.ID,
		}
	}
	return nil
}

// GetAssistants 查询助手
func (a *Ai) GetAssistants(ctx context.Context, req *aipb.ReqGetAssistants, rsp *aipb.RspGetAssistants) error {
	var (
		assistants []*model.TAssistant
		totalCount uint32
		err        error
	)

	l := assistantlogic.GetAssistantsLogic{}

	if req.WithTotalCount {
		if totalCount, err = l.Count(ctx, req); err != nil {
			return err
		}
	}

	if !req.WithTotalCount || totalCount > 0 {
		if assistants, err = l.Get(ctx, req); err != nil {
			return err
		}
	}

	l.GenerateMiniprogramQrcode(ctx, assistants)

	fullAssistants := model.TAssistants(assistants).ToFullPb()
	l.ApplyVisibility(fullAssistants, req.VisibleScene)

	rsp.Assistants = fullAssistants
	rsp.TotalCount = totalCount

	return nil
}

// BatchCreateAssistant 批量创建助手
func (a *Ai) BatchCreateAssistant(ctx context.Context, req *aipb.ReqBatchCreateAssistant, rsp *aipb.RspBatchCreateAssistant) error {
	l := assistantlogic.CreateAssistantLogic{}

	batchNo, ids, err := l.BatchCreate(ctx, req.Configs, req.CreateBy, req.IsDraft)
	if err != nil {
		return err
	}

	rsp.BatchNo = batchNo
	rsp.AssistantId = ids

	return nil
}

// BatchUpdateAssistant 批量更新助手
func (a *Ai) BatchUpdateAssistant(ctx context.Context, req *aipb.ReqBatchUpdateAssistant, _ *emptypb.Empty) error {
	l := assistantlogic.UpdateAssistantLogic{}

	if err := l.BatchUpdate(ctx, req); err != nil {
		return err
	}

	return nil
}

// DeleteAssistant 删除助手
func (a *Ai) DeleteAssistant(ctx context.Context, req *aipb.ReqDeleteAssistant, _ *emptypb.Empty) error {
	l := assistantlogic.DeleteAssistantLogic{}

	if err := l.BatchDelete(ctx, req); err != nil {
		return err
	}

	return nil
}

// GetAssistantLogs 查询助手日志
func (a *Ai) GetAssistantLogs(ctx context.Context, req *aipb.ReqGetAssistantLogs, rsp *aipb.RspGetAssistantLogs) error {
	var (
		logs       []*model.TAssistantLog
		totalCount uint32
		err        error
	)

	l := assistantlogic.GetLogsLogic{}

	if req.WithTotalCount {
		if totalCount, err = l.Count(ctx, req); err != nil {
			return err
		}
	}

	if !req.WithTotalCount || totalCount > 0 {
		if logs, err = l.Get(ctx, req); err != nil {
			return err
		}
	}

	rsp.Logs = model.TAssistantLogs(logs).ToPb()
	rsp.TotalCount = totalCount

	return nil
}

// GetAssistantOptions 获取助手下拉选项
func (a *Ai) GetAssistantOptions(ctx context.Context, _ *emptypb.Empty, rsp *aipb.RspGetAssistantOptions) error {
	rsp.ChatModel = config.GetStringSlice("assistant.chatModel")
	rsp.ChatOrSqlModel = config.GetStringSlice("assistant.chatOrSqlModel")
	rsp.GraphParseMode = config.GetStringSlice("assistant.graphParseModel")
	rsp.SearchEngine = config.GetStringSlice("assistant.searchEngine")
	rsp.AskSuggestionModel = config.GetStringSlice("assistant.askSuggestionModel")
	rsp.MiniWhiteUrl = config.GetStringSlice("assistant.miniWhiteUrl")

	var err error
	if err = config.Unmarshal("assistant.chatModelV2", &rsp.ChatModelV2); err != nil {
		return fmt.Errorf("unmarshal assistant.chatModelV2: %w", err)
	}
	if err = config.Unmarshal("assistant.interactiveCodeOption", &rsp.InteractiveCode); err != nil {
		return fmt.Errorf("unmarshal assistant.interactiveCodeOption: %w", err)
	}
	if err = config.Unmarshal("assistant.visibleChainOption", &rsp.VisibleChain); err != nil {
		return fmt.Errorf("unmarshal assistant.visibleChainOption: %w", err)
	}
	if err = config.Unmarshal("assistant.searchEngineV2", &rsp.SearchEngineV2); err != nil {
		return fmt.Errorf("unmarshal assistant.searchEngineV2: %w", err)
	}
	if rsp.EmbeddingModel, err = model.GetEmbeddingModelOptions(); err != nil {
		return err
	}
	if err = config.Unmarshal("assistant.quickAction", &rsp.QuickActions); err != nil {
		return fmt.Errorf("unmarshal assistant.quickAction: %w", err)
	}
	return nil
}

// CheckAssistantAllowlist 检查助手白名单
func (a *Ai) CheckAssistantAllowlist(ctx context.Context, req *aipb.ReqCheckAssistantAllowlist, rsp *aipb.RspCheckAssistantAllowlist) error {
	l := assistantlogic.CheckAllowlistLogic{}

	var (
		allowed bool
		err     error
	)
	switch para := req.TypePara.(type) {
	case *aipb.ReqCheckAssistantAllowlist_PhoneTypePara:
		allowed, err = l.CheckPhone(ctx, req.AssistantId, para.PhoneTypePara)
	}
	if err != nil {
		return err
	}

	rsp.Allowed = allowed
	return nil
}

// GetRecentlyUsedAssistantIds 获取最近使用的助手列表
func (a *Ai) GetRecentlyUsedAssistantIds(ctx context.Context, req *aipb.ReqGetRecentlyUsedAssistantIds,
	rsp *aipb.RspGetRecentlyUsedAssistantIds) error {
	query := model.NewQuery[model.TChat](ctx).Select("`t_chat`.`assistant_id`,`msg`.`create_date` as `create_date`").
		Wheres(func(db *gorm.DB) {
			db.Where("type = ?", req.ChatType)
			db.Where("create_by = ?", req.UserId)
			db.Joins("LEFT JOIN (SELECT assistant_id,chat_id,max(create_date) as create_date FROM t_chat_message "+
				"WHERE create_by = ? GROUP BY assistant_id,chat_id) as msg "+
				"ON msg.chat_id = t_chat.id AND msg.assistant_id = t_chat.assistant_id", req.UserId)
		})
	chats, err := query.OrderBy("msg.create_date", true).Get()
	if err != nil {
		return err
	}

	m := make(map[uint64]struct{})
	for _, chat := range chats {
		if chat.AssistantID == 0 {
			continue
		}
		if _, ok := m[chat.AssistantID]; !ok {
			rsp.Ids = append(rsp.Ids, chat.AssistantID)
			m[chat.AssistantID] = struct{}{}
		}
	}
	return nil
}

// GetRecentlyUsedAssistants 获取最近使用过的助手
func (a *Ai) GetRecentlyUsedAssistants(ctx context.Context, req *aipb.ReqGetRecentlyUsedAssistants, rsp *aipb.RspGetRecentlyUsedAssistants) error {
	query := model.NewQuery[model.TAssistant](ctx)
	db := query.DB()

	reverseAssistantIds := make([]uint64, len(req.UseAssistantIds))
	for i, v := range req.UseAssistantIds { // 因为数据库排序时需要将id倒叙排列，此处先reverse之后，得到的数据就是预期的正序
		reverseAssistantIds[len(req.UseAssistantIds)-1-i] = v
	}

	var assistants []*model.TAssistant
	var err error
	var totalCount uint32

	query.Where("channel = ? ", req.Channel)
	if req.OnlyRecentlyUse {
		xormhelper.ApplyIn(db, "id", req.UseAssistantIds)
	} else {
		filter := "is_draft = ? AND enabled = ? "
		args := []interface{}{false, true}
		if req.ShowInList != basepb.BoolEnum_BOOL_ENUM_UNSPECIFIED {
			filter += "AND show_in_list = ? "
			args = append(args, req.ShowInList == basepb.BoolEnum_BOOL_ENUM_TRUE)
		}

		orFilter := "AND (1 <> 1 "
		var orArgs []interface{}
		if req.Search != "" {
			orFilter += "OR nickname like ? OR nickname_en like ? "
			name := "%" + xorm.EscapeLikeWildcards(req.Search) + "%"
			orArgs = append(orArgs, name, name)
		}
		if len(req.UserIds) > 0 {
			orFilter += "OR EXISTS (SELECT 1 FROM t_assistant_admin AS admin WHERE " +
				"admin.assistant_id = t_assistant.id AND admin.admin_id in (?) AND admin.admin_type = ?) "
			orArgs = append(orArgs, req.UserIds, basepb.IdentityType_IDENTITY_TYPE_USER)
		}
		if len(req.TeamIds) > 0 {
			orFilter += "OR EXISTS (SELECT 1 FROM t_assistant_admin AS admin WHERE " +
				"admin.assistant_id = t_assistant.id AND admin.admin_id in (?) AND admin.admin_type = ?) "
			orArgs = append(orArgs, req.TeamIds, basepb.IdentityType_IDENTITY_TYPE_TEAM)
		}
		if len(orArgs) > 0 {
			filter += orFilter + ")"
			args = append(args, orArgs...)
		}
		db.Where(filter, args...)

		if len(req.UseAssistantIds) > 0 && len(orArgs) == 0 { // 除了用户最近使用的，要追加所有满足条件的助手
			db.Where("( 1 = 1 OR id in ?)", req.UseAssistantIds)
		}
	}

	totalCount, err = query.Count()
	if err != nil {
		return err
	}

	db.Preload("Admins")
	if len(req.UseAssistantIds) > 0 {
		db.Clauses(clause.OrderBy{
			Expression: clause.Expr{SQL: "FIELD(id,?) DESC,update_date DESC", Vars: []interface{}{reverseAssistantIds}, WithoutParentheses: true},
		})
	}

	assistants, err = query.Paging(xormhelper.ToPaginator(req.Page))
	if err != nil {
		return err
	}

	fullAssistants := model.TAssistants(assistants).ToFullPb()
	l := assistantlogic.GetAssistantsLogic{}
	l.ApplyVisibility(fullAssistants, aipb.ReqGetAssistants_VISIBLE_SCENE_IN_WEB)

	rsp.Assistants = fullAssistants
	rsp.TotalCount = totalCount

	return nil
}

// GetAssistantAdmin 获取所有启用的助手的管理员信息
func (a *Ai) GetAssistantAdmin(ctx context.Context, req *aipb.ReqGetAssistantAdmin, rsp *aipb.RspGetAssistantAdmin) error {
	admins, err := model.NewQuery[model.TAssistantAdmin](ctx).Wheres(func(db *gorm.DB) {
		if len(req.AssistantIds) > 0 {
			db.Where("assistant_id in (?)", req.AssistantIds)
		}
		if req.Page != nil {
			db.Limit(int(req.Page.Limit))
			db.Offset(int(req.Page.Offset))
		}
		db.Where(
			"EXISTS ( SELECT 1 FROM t_assistant WHERE t_assistant.id = t_assistant_admin.assistant_id " +
				"AND t_assistant.deleted_at IS NULL AND t_assistant.enabled = 1 ) ")
	}).Get()
	if err != nil {
		return err
	}

	m := make(map[uint64][]*basepb.Identity)
	for _, admin := range admins {
		m[admin.AssistantID] = append(m[admin.AssistantID], &basepb.Identity{
			IdentityType: admin.AdminType,
			IdentityId:   admin.AdminID,
		})
	}
	rsp.Admins = make([]*aipb.RspGetAssistantAdmin_Info, 0, len(m))
	for assistantId, v := range m {
		rsp.Admins = append(rsp.Admins, &aipb.RspGetAssistantAdmin_Info{
			AssistantId: assistantId,
			Admins:      v,
		})
	}

	return nil
}
