package handler

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/chat"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
)

// CreateSendRecord 创建发送记录
//
//	@Description:
//	@receiver a
//	@param ctx
//	@param req
//	@param rsp
//	@return error
func (a *Ai) CreateSendRecord(ctx context.Context, req *aipb.ReqCreateSendRecord,
	rsp *aipb.RspCreateSendRecord) error {
	if req.MessageId == 0 || req.ChatId == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	switch req.ChatType {
	case aipb.ChatType_CHAT_TYPE_WECHAT:
		if len(req.Pieces) == 0 {
			return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
		}
		rows, invalidNum, canBeSend, questionNum, err := chat.GetCreateSendRecord(ctx, req)
		if err != nil {
			return err
		}
		log.WithContext(ctx).Debugw("CreateSendRecord wechat", "canBeSend", canBeSend)
		if err = model.NewQuery[model.TChatSendRecord](ctx).Insert(rows); err != nil {
			return err
		}
		for i := 0; i < len(rows); i++ {
			if len(rsp.Records) == canBeSend {
				break
			}
			if rows[i].MessageType == aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL ||
				rows[i].MessageType == aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER {
				rsp.Records = append(rsp.Records, &aipb.RspCreateSendRecord_RecordInfo{
					RecordId:  rows[i].ID,
					MessageId: rows[i].MessageID,
					Type:      rows[i].Type,
					Content:   rows[i].Content,
					ExtraId:   rows[i].ExtraID,
				})
			}
		}
		if canBeSend > len(rows)-invalidNum || canBeSend == len(rows)-invalidNum && questionNum <= 2 && !req.MoreToCome {
			// 2包括自身收到微信消息时设置的问题一次，发送答案前记录的一次 || req.MoreToCome 后续还有消息
			return nil
		}
		// 追加继续回答
		rsp.ContinueAnswering = true
	case aipb.ChatType_CHAT_TYPE_WHATSAPP:
		if req.WhatsAppRecord == nil {
			return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
		}
		now := time.Now()
		row := &model.TChatSendRecord{
			MessageID:   req.MessageId,
			ChatID:      req.ChatId,
			Type:        req.WhatsAppRecord.Type,
			Content:     req.WhatsAppRecord.Content,
			State:       req.WhatsAppRecord.State,
			UUID:        req.WhatsAppRecord.Uuid,
			Info:        req.WhatsAppRecord.Info,
			SendDate:    now,
			CreateDate:  now,
			MessageType: req.WhatsAppRecord.MessageType,
		}
		err := model.NewQuery[model.TChatSendRecord](ctx).Create(row)
		if err != nil {
			return err
		}
		rsp.Records = []*aipb.RspCreateSendRecord_RecordInfo{
			{RecordId: row.ID, MessageId: row.MessageID, Type: row.Type, Content: row.Content},
		}

	default:
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	return nil
}

// DescribeUserChatRecords 查询用户的会话发送记录
func (a *Ai) DescribeUserChatRecords(ctx context.Context, req *aipb.ReqDescribeUserChatRecords,
	rsp *aipb.RspDescribeUserChatRecords) error {
	if req.ChatId == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	query := model.NewQuery[model.TChatSendRecord](ctx).Select("t_chat_send_record.id", "t_chat_send_record.message_id",
		"t_chat_send_record.content", "t_chat_send_record.send_date", "t_chat_send_record.type",
		"t_chat_message.rating_scale", "t_chat_message.type as message_type", "t_chat_message.reject_reason",
		"t_chat_message.show_type", "t_chat_message.suggest_question", "t_chat_message.ask_type", "t_chat_message.match_pattern").
		Join("LEFT JOIN t_chat_message ON t_chat_send_record.message_id = t_chat_message.id").
		Wheres(func(db *gorm.DB) {
			db.Where("t_chat_send_record.chat_id = ?", req.ChatId)
			db.Where("(t_chat_send_record.state = ? AND t_chat_send_record.message_type = ?) OR "+
				"(t_chat_send_record.state = ? AND t_chat_send_record.message_type = ?)",
				aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND, aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL, // 返回已发送的正常消息
				aipb.ChatMessageState_CHAT_MESSAGE_STATE_UNSENT, aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_REPEAT_ANSWER) // 返回未发送的重复消息
			if len(req.Keyword) > 0 {
				db.Where("t_chat_send_record.content like ?", "%"+xorm.EscapeLikeWildcards(req.Keyword)+"%")
			}
			if req.SendRange != nil {
				if req.SendRange.Start != nil {
					db.Where("t_chat_send_record.send_date >= ?", req.SendRange.Start.AsTime().Local())
				}
				if req.SendRange.End != nil {
					db.Where("t_chat_send_record.send_date <= ?", req.SendRange.End.AsTime().Local())
				}
			}
		})
	count, err := query.Count()
	if err != nil {
		return err
	}
	rsp.TotalCount = count
	if count == 0 {
		return nil
	}

	var results []*chat.ChatSendRecordInfo
	var messageIds []uint64
	paginator := xorm.NewPaginator(req.Page.Offset, req.Page.Limit)
	paginator.Apply(query.DB())
	query.OrderBy("t_chat_send_record.send_date", false).OrderBy("t_chat_send_record.id", false)
	if query.Scan(&results) != nil {
		return err
	}
	for _, v := range results {
		messageIds = append(messageIds, v.MessageId)
	}
	suggestMap, suggesModelMap, err1 := chat.GetSendRecordSuggest(ctx, messageIds)
	if err1 != nil {
		return err1
	}
	rsp.Records = make([]*aipb.ChatSendRecordInfo, len(results))
	for i, v := range results {
		rsp.Records[i] = v.ToProto(suggestMap, suggesModelMap)
	}

	return nil
}

// DescribeAssistantMessage 只能查询出要迁移的非web端的数据
func (a *Ai) DescribeAssistantMessage(ctx context.Context, req *aipb.ReqDescribeAssistantMessage,
	rsp *aipb.RspDescribeAssistantMessage) error {
	results, err := model.NewQuery[model.TChatMessage](ctx).Select("t_chat_message.id", "t_chat_message.content",
		"t_chat_message.create_date", "t_chat_message.type", "t_chat_message.chat_id",
		"t_chat_message.show_type", "t_chat_message.ask_type", "t_chat_message.suggest_question").
		Join("INNER JOIN t_chat ON t_chat.id = t_chat_message.chat_id AND t_chat.type = ?", req.ChatType).
		Where("NOT EXISTS (SELECT 1 FROM t_chat_send_record WHERE t_chat_send_record.message_id = t_chat_message.id )").
		Where("t_chat_message.assistant_id = ?", req.AssistantId).
		OrderBy("t_chat_message.id", false).
		Paging(xorm.NewPaginator(req.Page.Offset, req.Page.Limit))
	if err != nil {
		return err
	}
	if len(results) == 0 {
		return nil
	}

	rsp.Message = make([]*aipb.ChatMessage, len(results))
	for i, v := range results {
		message, err := v.TransformToAiMessage()
		if err != nil {
			return err
		}
		rsp.Message[i] = message
	}
	return nil
}

// InsertAssistantMessageRecord ...
func (a *Ai) InsertAssistantMessageRecord(ctx context.Context, req *aipb.ReqInsertAssistantMessageRecord,
	_ *emptypb.Empty) error {
	if len(req.Records) == 0 {
		log.WithContext(ctx).Debug("InsertAssistantMessageRecord len is 0")
		return nil
	}
	rows := make([]*model.TChatSendRecord, len(req.Records))
	for i, record := range req.Records {
		rows[i] = &model.TChatSendRecord{
			MessageID:   record.MessageId,
			ChatID:      record.ChatId,
			Type:        record.Type,
			Content:     record.Content,
			UUID:        record.Uuid,
			CreateDate:  record.CreateDate.AsTime().Local(),
			SendDate:    record.SendDate.AsTime().Local(),
			State:       record.State,
			SendType:    record.SendType,
			MessageType: record.MessageType,
		}
		if record.Type == aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU {
			rows[i].SendDate = rows[i].SendDate.Add(time.Millisecond * 600)
		}
	}
	return model.NewQuery[model.TChatSendRecord](ctx).Insert(rows)
}
