package client

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"github.com/hibiken/asynq"
)

// ReuseXredisOpts 复用xredis提供的client
type ReuseXredisOpts struct {
}

// MakeRedisClient 返回全局的xredis客户端
func (r *ReuseXredisOpts) MakeRedisClient() interface{} {
	return xredis.Default
}

var (
	taskClient *asynq.Client
)

// BootTaskClient 启动task
func BootTaskClient() {
	taskClient = asynq.NewClient(&ReuseXredisOpts{})
}

// SubmitTask 提交任务
func SubmitTask(task *asynq.Task, opts ...asynq.Option) error {
	_, err := taskClient.Enqueue(task, opts...)
	return err
}
