package server

import (
	"fmt"
	"os"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/tasks/client"
	microLogger "github.com/asim/go-micro/v3/logger"
	"github.com/hibiken/asynq"
)

var (
	taskServer *asynq.Server
	stopSignal chan struct{}
	stopped    chan struct{}
)

type baseLogger struct {
}

// Debug logs a message at Debug level.
func (l *baseLogger) Debug(args ...interface{}) {
	l.levelPrint(microLogger.DebugLevel, args...)
}

// Info logs a message at Info level.
func (l *baseLogger) Info(args ...interface{}) {
	l.levelPrint(microLogger.InfoLevel, args...)
}

// Warn logs a message at Warning level.
func (l *baseLogger) Warn(args ...interface{}) {
	l.levelPrint(microLogger.WarnLevel, args...)
}

// Error logs a message at Error level.
func (l *baseLogger) Error(args ...interface{}) {
	l.levelPrint(microLogger.ErrorLevel, args...)
}

// Fatal logs a message at Fatal level
// and process will exit with status set to 1.
func (l *baseLogger) Fatal(args ...interface{}) {
	l.levelPrint(microLogger.FatalLevel, args...)
	os.Exit(1)
}

func (l *baseLogger) levelPrint(level microLogger.Level, args ...interface{}) {
	args = append([]interface{}{"Asynq Tasks "}, args...)
	microLogger.DefaultLogger.Log(level, args...)
}

// StopTaskServer 停止任务服务
func StopTaskServer() error {
	close(stopSignal)
	<-stopped
	return nil
}

func BootTaskServer() {
	taskServer = asynq.NewServer(&client.ReuseXredisOpts{}, asynq.Config{
		Logger:      &baseLogger{},
		Concurrency: config.GetIntOr("llm.collection.doc_parse.parallelism", 0),
	})
	stopSignal = make(chan struct{})
	stopped = make(chan struct{})
	mux := asynq.NewServeMux()
	for k, v := range TaskHandlers {
		mux.Handle(k, v)
	}
	if err := taskServer.Start(mux); err != nil {
		panic(fmt.Sprintf("could not run task server: %v", err))
	}
	<-stopSignal
	taskServer.Shutdown()
	close(stopped)
}

// TaskHandlers 任务列表
var TaskHandlers = map[string]asynq.Handler{
	logic.AiParseDocTask:        asynq.HandlerFunc(logic.HandleParseDocTask),
	logic.AiBatchDocTaskClone:   asynq.HandlerFunc(logic.HandleBatchCloneDocTask),
	logic.AiBatchDocTaskDelete:  asynq.HandlerFunc(logic.HandleBatchDeleteDocTask),
	logic.AiBatchDocTaskOnOff:   asynq.HandlerFunc(logic.HandleBatchOnOffDocTask),
	logic.AiBatchDocTaskUpdate:  asynq.HandlerFunc(logic.HandleBatchUpdateDocTask),
	logic.AiBatchDocTaskReparse: asynq.HandlerFunc(logic.HandleBatchReparseDocTask),
	logic.AiBatchDocTaskShare:   asynq.HandlerFunc(logic.HandleBatchShareDocTask),
}
