package tasks

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/tasks/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/tasks/server"
)

// Boot 启动task服务
func Boot() error {
	client.BootTaskClient()
	enabled := config.GetBoolOr("llm.collection.doc_parse.enabled", false)
	if enabled {
		go func() {
			server.BootTaskServer()
		}()
	}
	return nil
}
