package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TFeedbackLog 用户反馈日志表
type TFeedbackLog struct {
	ID             uint64              `gorm:"primaryKey;column:id" json:"id"`
	AssistantID    uint64              `gorm:"column:assistant_id;default:0" json:"assistantId"`                              // 助手id
	FeedbackID     uint64              `gorm:"column:feedback_id;default:0" json:"feedbackId"`                                // 反馈ID
	Action         aipb.FeedbackAction `gorm:"column:action;default:0" json:"action"`                                         // 操作类型
	CreateDate     time.Time           `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	CreateIdentity *basepb.Identity    `gorm:"column:create_identity;serializer:json"`                                        // 创建人身份

	Feedback *TFeedback `gorm:"foreignKey:id;references:feedback_id"`

	// 弃用字段
	FeedbackUserID uint64 `gorm:"column:feedback_user_id;default:0" json:"feedbackUserId"` // 反馈用户ID
	OperatorType   int32  `gorm:"column:operator_type;default:0" json:"operatorType"`      // 操作人类型 1 门户人员 2 运营人员
	CreateBy       uint64 `gorm:"column:create_by;default:0" json:"createBy"`              // 运营端用户
}

// TableName get sql table name.获取数据库表名
func (m *TFeedbackLog) TableName() string {
	return "t_feedback_log"
}

// TFeedbackLogColumns get sql column name.获取数据库列名
var TFeedbackLogColumns = struct {
	ID             string
	FeedbackID     string
	FeedbackUserID string
	Action         string
	CreateBy       string
	CreateDate     string
	OperatorType   string
	AssistantID    string
}{
	ID:             "id",
	FeedbackID:     "feedback_id",
	FeedbackUserID: "feedback_user_id",
	Action:         "action",
	CreateBy:       "create_by",
	CreateDate:     "create_date",
	OperatorType:   "operator_type",
	AssistantID:    "assistant_id",
}
