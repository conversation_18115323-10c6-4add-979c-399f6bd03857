package model

import (
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	replaceAssistantDocsDeleteAllSql        = "DELETE FROM `t_assistant_doc` WHERE `doc_id` IN ?"
	replaceAssistantDocsDeleteAllScopedSql  = "DELETE FROM `t_assistant_doc` WHERE `doc_id` IN ? AND `assistant_id` IN ?"
	replaceAssistantDocsDeletePartSql       = "DELETE FROM `t_assistant_doc` WHERE `doc_id` IN ? AND (`doc_id`, `assistant_id`) NOT IN ?"
	replaceAssistantDocsDeletePartScopedSql = "DELETE FROM `t_assistant_doc` WHERE `doc_id` IN ? AND `assistant_id` IN ? AND (`doc_id`, `assistant_id`) NOT IN ?"
)

// ReplaceDocAssistants 替换
func ReplaceDocAssistants(tx *gorm.DB, docIDs, scopedAssistantIDs []uint64, states []*TAssistantDoc) error {
	if len(docIDs) == 0 {
		return nil
	}

	docToStates := make(map[uint64][]*TAssistantDoc)
	for _, state := range states {
		if _, ok := docToStates[state.DocID]; !ok {
			docToStates[state.DocID] = make([]*TAssistantDoc, 0)
		}
		docToStates[state.DocID] = append(docToStates[state.DocID], state)
	}

	var (
		err              error
		deleteAllDocIDs  []uint64
		deletePartDocIDs []uint64
		deletePartNotIn  [][]uint64
	)
	for _, docID := range docIDs {
		docStates := docToStates[docID]
		if len(docStates) == 0 {
			deleteAllDocIDs = append(deleteAllDocIDs, docID)
		} else {
			deletePartDocIDs = append(deletePartDocIDs, docID)
			for _, docState := range docStates {
				deletePartNotIn = append(deletePartNotIn, []uint64{docID, docState.AssistantID})
			}
		}
	}

	if len(deleteAllDocIDs) > 0 {
		if len(scopedAssistantIDs) > 0 {
			err = tx.Exec(replaceAssistantDocsDeleteAllScopedSql, deleteAllDocIDs, scopedAssistantIDs).Error
		} else {
			err = tx.Exec(replaceAssistantDocsDeleteAllSql, deleteAllDocIDs).Error
		}
		if err != nil {
			return fmt.Errorf("delete doc's all assistant: %v", err)
		}
	}

	if len(deletePartDocIDs) > 0 {
		if len(scopedAssistantIDs) > 0 {
			err = tx.Exec(replaceAssistantDocsDeletePartScopedSql, deletePartDocIDs, scopedAssistantIDs, deletePartNotIn).Error
		} else {
			err = tx.Exec(replaceAssistantDocsDeletePartSql, deletePartDocIDs, deletePartNotIn).Error
		}
		if err != nil {
			return fmt.Errorf("delete doc's part assistant: %v", err)
		}
	}

	if len(states) > 0 {
		err = tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "doc_id"}, {Name: "assistant_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"state"}),
		}).Create(states).Error
		if err != nil {
			return fmt.Errorf("upsert doc's assistant: %v", err)
		}
	}

	return nil
}
