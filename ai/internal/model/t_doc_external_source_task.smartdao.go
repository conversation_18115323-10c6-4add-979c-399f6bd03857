// Code generated by smartdao. DO NOT EDIT.
// versions:
//  smartdao	v0.0.13

package model

import (
	"time"
)

// TDocExternalSourceTask 外部数据源任务
type TDocExternalSourceTask struct {
	ID         uint64    `gorm:"primaryKey;column:id" json:"id"`
	UUID       string    `gorm:"column:uuid" json:"uuid"`
	UserID     uint64    `gorm:"column:user_id" json:"userId"`
	Result     uint64    `gorm:"column:result" json:"result"`         // doc主键id
	Type       uint32    `gorm:"column:type;default:0" json:"type"`   // 1 腾讯文档 2 绿技行-专家 3 绿技行-成果
	State      uint32    `gorm:"column:state;default:1" json:"state"` // 1 进行中 2任务完成 3任务失败
	EndDate    time.Time `gorm:"column:end_date" json:"endDate"`      // 最后一次任务结束时间
	CreateDate time.Time `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"`
	UpdateDate time.Time `gorm:"column:update_date;autoUpdateTime;default:CURRENT_TIMESTAMP" json:"updateDate"`
}

// TableName get sql table name.获取数据库表名
func (m *TDocExternalSourceTask) TableName() string {
	return "db_tanlive_ai.t_doc_external_source_task"
}

// TDocExternalSourceTaskColumns get sql column name.获取数据库列名
var TDocExternalSourceTaskColumns = struct {
	ID         string
	UUID       string
	UserID     string
	Result     string
	Type       string
	State      string
	EndDate    string
	CreateDate string
	UpdateDate string
}{
	ID:         "id",
	UUID:       "uuid",
	UserID:     "user_id",
	Result:     "result",
	Type:       "type",
	State:      "state",
	EndDate:    "end_date",
	CreateDate: "create_date",
	UpdateDate: "update_date",
}
