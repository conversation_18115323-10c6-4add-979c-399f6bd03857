package model

import (
	"encoding/json"
	"fmt"
	"slices"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// AssistantReadableInConsole 助手可读字段（用户后台）
var AssistantReadableInConsole = []string{
	"admins",
	TAssistantColumns.SearchDebug,
	TAssistantColumns.VisibleChainConfig,
	TAssistantColumns.Channel,
	TAssistantColumns.Name,
	TAssistantColumns.NameEn,
	TAssistantColumns.Enabled,
	TAssistantColumns.Nickname,
	TAssistantColumns.NicknameEn,
	TAssistantColumns.AvatarUrl,
	TAssistantColumns.WeixinDevelopConfig,
	TAssistantColumns.WhatsappDevelopConfig,
	TAssistantColumns.WebsiteConfig,
	TAssistantColumns.MiniprogramConfig,
	TAssistantColumns.WelcomeMessageConfig,
	TAssistantColumns.PresetQuestionConfig,
	TAssistantColumns.KefuConfig,
	TAssistantColumns.RatingScaleReplyConfig,
	TAssistantColumns.InteractiveCodeConfig,
	TAssistantColumns.FeedbackEnabled,
	TAssistantColumns.GraphParseConfig,
	TAssistantColumns.BriefIntro,
	TAssistantColumns.DetailIntro,
	TAssistantColumns.PromptPrefix,
	TAssistantColumns.Model,
	TAssistantCollectionColumns.Threshold,
	TAssistantColumns.HistoryRounds,
	TAssistantColumns.SearchEngine,
	TAssistantColumns.ChatOrSqlConfig,
	TAssistantCollectionColumns.DocTopN,
	TAssistantColumns.SearchTopN,
	TAssistantColumns.AskSuggestionConfig,
	TAssistantColumns.AppId,
	TAssistantColumns.SwitchAssistantId,
	TAssistantColumns.SystemLanguages,
	TAssistantColumns.ChatIdleDuration,
	TAssistantColumns.TextWeight,
	TAssistantColumns.ShowThink,
	TAssistantColumns.TextRecallTopN,
	TAssistantColumns.TextRecallQuery,
	TAssistantColumns.TextRecallPattern,
	TAssistantColumns.TextRecallSlop,
	TAssistantColumns.Temperature,
	TAssistantColumns.QuestionTypeConfig,
	TAssistantColumns.ShowInList,
	TAssistantColumns.AllowlistConfig,
	TAssistantColumns.CleanChunks,
	TAssistantCollectionColumns.ChunkConfig,
	"collection_name",
	"collection_lang",
}

// AssistantWritableInConsole 助手可写字段（用户后台）
var AssistantWritableInConsole = []string{
	TAssistantColumns.Name,
	TAssistantColumns.NameEn,
	TAssistantColumns.Enabled,
	TAssistantColumns.Nickname,
	TAssistantColumns.NicknameEn,
	TAssistantColumns.AvatarUrl,
	TAssistantColumns.WebsiteConfig,
	TAssistantColumns.MiniprogramConfig,
	TAssistantColumns.WelcomeMessageConfig,
	TAssistantColumns.PresetQuestionConfig,
	TAssistantColumns.KefuConfig,
	TAssistantColumns.RatingScaleReplyConfig,
	TAssistantColumns.InteractiveCodeConfig,
	TAssistantColumns.FeedbackEnabled,
	TAssistantColumns.GraphParseConfig,
	TAssistantColumns.BriefIntro,
	TAssistantColumns.DetailIntro,
	TAssistantColumns.PromptPrefix,
	TAssistantColumns.Model,
	TAssistantCollectionColumns.Threshold,
	TAssistantColumns.HistoryRounds,
	TAssistantColumns.SearchEngine,
	TAssistantColumns.ChatOrSqlConfig,
	TAssistantCollectionColumns.DocTopN,
	TAssistantColumns.SearchTopN,
	TAssistantColumns.AskSuggestionConfig,
	TAssistantColumns.SwitchAssistantId,
	TAssistantColumns.ChatIdleDuration,
	TAssistantColumns.TextWeight,
	TAssistantColumns.ShowThink,
	TAssistantColumns.TextRecallTopN,
	TAssistantColumns.TextRecallQuery,
	TAssistantColumns.TextRecallPattern,
	TAssistantColumns.TextRecallSlop,
	TAssistantColumns.Temperature,
	TAssistantColumns.QuestionTypeConfig,
	TAssistantColumns.ShowInList,
	TAssistantColumns.AllowlistConfig,
	TAssistantColumns.CleanChunks,
	TAssistantCollectionColumns.ChunkConfig,
}

// ToProto ...
func (m *TAssistant) ToProto() *aipb.Assistant {
	pb := &aipb.Assistant{
		Id:                m.ID,
		Name:              m.Name,
		NameEn:            m.NameEn,
		SearchDebug:       m.SearchDebug,
		TextWeight:        m.TextWeight,
		TextRecallTopN:    m.TextRecallTopN,
		Temperature:       m.Temperature,
		CleanChunks:       m.CleanChunks,
		TextRecallQuery:   m.TextRecallQuery,
		TextRecallPattern: m.TextRecallPattern,
		TextRecallSlop:    m.TextRecallSlop,
	}
	if m.WebsiteConfig != nil {
		pb.WebsiteRoute = m.WebsiteConfig.RoutePath
	}
	if len(m.Collections) != 0 {
		for _, v := range m.Collections {
			pb.Collections = append(pb.Collections, v.ToProto())
		}
		collection := m.Collections[0]
		pb.TopN = collection.DocTopN
		pb.Threshold = collection.Threshold
	}
	return pb
}

// TransToPbAssistantDetail ...
func (m *TAssistant) TransToPbAssistantDetail(lang string) *aipb.AssistantDetail {
	if lang != "zh" && lang != "en" && len(m.SystemLanguages) > 0 {
		lang = m.SystemLanguages[0]
	}
	if len(lang) == 0 {
		lang = "zh"
	}
	rsp := &aipb.AssistantDetail{}
	rsp.Id = m.ID
	rsp.Channel = m.Channel
	rsp.Name = m.Name
	rsp.NameEn = m.NameEn
	rsp.WechatAutoReply = m.WechatAutoReply
	rsp.PromptPrefix = m.PromptPrefix
	// rsp.AppCode = m.AppCode
	// rsp.ShowType = m.ShowType
	rsp.Model = m.Model
	rsp.HistoryRounds = m.HistoryRounds
	// rsp.CorpId = m.CorpID
	rsp.SearchRewrite = m.SearchRewrite
	rsp.VisionModel = m.VisionModel
	rsp.CloseSearch = m.CloseSearch
	rsp.CloseRefMark = m.CloseRefMark
	rsp.Enabled = m.Enabled
	rsp.ChatIdleDuration = int64(m.ChatIdleDuration)
	rsp.TextWeight = m.TextWeight
	rsp.MissReply = m.MissReply
	rsp.QuestionTypeConfig = m.QuestionTypeConfig
	rsp.TextRecallTopN = m.TextRecallTopN
	rsp.Temperature = m.Temperature
	rsp.CleanChunks = m.CleanChunks
	rsp.TextRecallPattern = m.TextRecallPattern
	rsp.TextRecallSlop = m.TextRecallSlop
	rsp.TextRecallQuery = m.TextRecallQuery
	rsp.NickName = m.Nickname

	if m.AskSuggestionConfig != nil {
		rsp.SuggestionConfig = m.AskSuggestionConfig
	}
	rsp.ShowThink = m.ShowThink
	if m.VisibleChainConfig != nil {
		rsp.ChainVisible = m.VisibleChainConfig.Visible
	}

	if len(m.Collections) > 0 {
		collection := m.Collections[0]
		rsp.CollectionName = collection.RagName
		rsp.Threshold = collection.Threshold
		rsp.DocTopN = collection.DocTopN
		rsp.Lang = collection.Lang
		rsp.EsIns = collection.EsIns
	}
	rsp.SearchTopN = m.SearchTopN
	rsp.SearchEngine = m.SearchEngine

	if m.ChatOrSqlConfig != nil && m.ChatOrSqlConfig.Enabled { // 填充chatOrSql
		rsp.ChatOrSql = 1
		rsp.SqlModel = m.ChatOrSqlConfig.Model
	}
	if m.WebsiteConfig != nil {
		rsp.WebsiteRoute = m.WebsiteConfig.RoutePath
	}

	rsp.WelcomeMsg = &aipb.AssistantWelcomeMsg{} // 填充欢迎语
	if m.WelcomeMessageConfig != nil {
		rsp.WelcomeMsg.HeadMsg = assistantConfigToStr[*aipb.AssistantWelcomeMessage](lang, m.WelcomeMessageConfig.Messages,
			func(v *aipb.AssistantWelcomeMessage, lang string) string {
				if v != nil && v.Lang == lang {
					return v.Content
				}
				return ""
			})
	}
	if m.InteractiveCodeConfig != nil {
		rsp.WelcomeMsg.CodeConfig = &aipb.AssistantInteractiveCodeConfig{
			SendInteractiveCode: m.InteractiveCodeConfig.SendInteractiveCode,
		}
		optionMap := getInteractiveCodeOption()
		for _, v := range m.InteractiveCodeConfig.Codes {
			if v.Lang != lang {
				continue
			}
			if code, ok := optionMap[v.InteractiveCode]; ok && v.Lang == "en" {
				v.Content = code.DefaultPreEn + v.Content
			} else if ok {
				v.Content = code.DefaultPreZh + v.Content
			}
			rsp.WelcomeMsg.CodeConfig.Codes = append(rsp.WelcomeMsg.CodeConfig.Codes, v)

		}
	}
	if m.PresetQuestionConfig != nil && m.PresetQuestionConfig.Enabled && len(m.PresetQuestionConfig.Questions) > 0 {
		for _, v := range m.PresetQuestionConfig.Questions { // 填充预设问题
			if v.Lang == lang {
				rsp.WelcomeMsg.Question = append(rsp.WelcomeMsg.Question, v.Content)
			}
		}
	}
	if m.KefuConfig != nil { // 转人工
		rsp.LiveAgentMsg = &aipb.AssistantLiverAgentConfigMsg{
			Enable: m.KefuConfig.Enabled,
			Msg:    m.KefuConfig.AfterRemindMessage,
			RemindPushMsg: &aipb.AssistantDefaultConfigMsg{
				Enable: m.KefuConfig.BeforeRemindEnabled,
				Msg:    m.KefuConfig.BeforeRemindMessage,
			},
			Reply: m.KefuConfig.Reply,
		}
	}

	if m.RatingScaleReplyConfig != nil && m.RatingScaleReplyConfig.Enabled {
		ratingScale := func(s aipb.RatingScale) string {
			for _, v := range m.RatingScaleReplyConfig.Replies {
				if v.RatingScale == s && v.Lang == lang {
					return v.Content
				}
			}
			return ""
		}
		rsp.RatingScaleMsg = &aipb.AssistantRatingScaleMsg{
			Enable:       true,
			Satisfied:    ratingScale(aipb.RatingScale_RATING_SCALE_SATISFIED),
			Average:      ratingScale(aipb.RatingScale_RATING_SCALE_AVERAGE),
			Dissatisfied: ratingScale(aipb.RatingScale_RATING_SCALE_DISSATISFIED),
		}
	}
	if m.MultimodalPromptPrefix != nil {
		_ = json.Unmarshal(m.MultimodalPromptPrefix, &rsp.MultimodalPrompt)
	}

	return toIMConfig(rsp, m)
}

func toIMConfig(config *aipb.AssistantDetail, m *TAssistant) *aipb.AssistantDetail {
	switch m.Channel {
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
		aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN:
		if m.WeixinDevelopConfig != nil {
			config.CorpId = m.WeixinDevelopConfig.CorpId
			config.AppCode = m.WeixinDevelopConfig.OpenKfid
		}
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP:
		if m.WhatsappDevelopConfig != nil {
			config.AppCode = m.WhatsappDevelopConfig.BusinessNumber
		}
	default:
	}

	return config
}

func assistantConfigToStr[T any](lang string, array []T, f func(v T, lang string) string) string {
	var result string
	for i, v := range array {
		vv := f(v, lang)
		if vv == "" {
			continue
		}
		result += vv
		if i < len(array)-1 {
			result += "\n"
		}
	}
	return result
}

// AfterFind hook. 填充助手对应的每个collection的ai参数
func (m *TAssistant) AfterFind(tx *gorm.DB) error {
	if len(m.AssistantCollections) != 0 && len(m.Collections) != 0 {
		// 先 copy 出来，在 in 查询得到的结果中，Collections地址是在多个TAssistant下复用的
		for i, vv := range m.Collections {
			newV := *vv
			m.Collections[i] = &newV
		}
		for _, v := range m.AssistantCollections {
			for _, vv := range m.Collections {
				if v.CollectionID == vv.ID {
					vv.Threshold = v.Threshold
					vv.SearchTopN = v.SearchTopN
					vv.DocTopN = v.DocTopN
					vv.SearchEngine = v.SearchEngine
				}
			}
		}
	}

	// 将app_id零值改为空字符串
	if m.AppId == "0" {
		m.AppId = ""
	}

	return nil
}

// BeforeCreate hook
func (m *TAssistant) BeforeCreate(tx *gorm.DB) error {
	// 确保app_id的空字符串转为零值
	if m.AppId == "" {
		m.AppId = "0"
	}

	return nil
}

// BeforeUpdate hook
func (m *TAssistant) BeforeUpdate(tx *gorm.DB) error {
	// 确保app_id的空字符串转为零值
	if m.AppId == "" {
		m.AppId = "0"
	}

	return nil
}

// AssistantByAdmins 通过管理员id过滤
func AssistantByAdmins(admins ...*aipb.AssistantAdmin) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(admins) == 0 {
			return db
		}
		condition := "1 <> 1"
		var args []interface{}
		for _, v := range admins {
			condition += " or (admin_id = ? and admin_type = ?)"
			args = append(args, v.Id, v.Type)
		}
		subQuery := NewQuery[TAssistantAdmin](db.Statement.Context).DB().Where(condition, args...).Select("distinct(assistant_id)")
		return db.Where("id in (?)", subQuery)
	}
}

// AssistantWithCollection preload collection信息
func AssistantWithCollection(db *gorm.DB) *gorm.DB {
	return db.Preload("Collections").Preload("AssistantCollections")
}

// TCollectionJoinedAssistant 在特定助手下的Collection信息，扩展了模型、阈值、搜索引擎等信息
type TCollectionJoinedAssistant struct {
	TCollection
	Threshold    float32 `json:"threshold"`    // chat默认阈值
	SearchTopN   int32   `json:"searchTopN"`   // search top n
	DocTopN      int32   `json:"docTopN"`      // doc top n
	SearchEngine string  `json:"searchEngine"` // ai搜索引擎
}

// TAssistants 列表
type TAssistants []*TAssistant

// ToFullPb 转换成完整配置pb
func (ms TAssistants) ToFullPb() []*aipb.FullAssistant {
	pbs := make([]*aipb.FullAssistant, 0, len(ms))
	for _, m := range ms {
		pbs = append(pbs, m.ToFullConfig())
	}
	return pbs
}

// ToFullConfig 转换成完整配置pb
func (m *TAssistant) ToFullConfig() *aipb.FullAssistant {
	if m == nil {
		return nil
	}

	return &aipb.FullAssistant{
		Assistant:      m.ToAssistantV2(),
		TermsConfirmed: m.TermsConfirmation != nil && m.TermsConfirmation.IsAgreed,
	}
}

// ToAssistantV2 协议转换
func (m *TAssistant) ToAssistantV2() *aipb.AssistantV2 {
	if m == nil {
		return nil
	}

	return &aipb.AssistantV2{
		Id:         m.ID,
		CreateBy:   m.CreateBy,
		CreateDate: timestamppb.New(m.CreateDate),
		UpdateBy:   m.UpdateBy,
		UpdateDate: timestamppb.New(m.UpdateDate),
		Config:     m.ToConfig(),
		IsDraft:    m.IsDraft,
		BatchNo:    m.BatchNo,
	}
}

// ToConfig 转换成配置pb
func (m *TAssistant) ToConfig() *aipb.AssistantConfig {
	if m == nil {
		return nil
	}

	pb := &aipb.AssistantConfig{
		SearchDebug:         m.SearchDebug,
		VisibleChainConfig:  m.VisibleChainConfig,
		FieldManageConfig:   m.FieldManageConfig,
		Name:                m.Name,
		NameEn:              m.NameEn,
		Channel:             m.Channel,
		Enabled:             m.Enabled,
		PromptPrefix:        m.PromptPrefix,
		Model:               m.Model,
		HistoryRounds:       m.HistoryRounds,
		CloseSearch:         m.CloseSearch,
		SearchEngine:        m.SearchEngine,
		SearchTopN:          m.SearchTopN,
		ChatOrSqlConfig:     m.ChatOrSqlConfig,
		AskSuggestionConfig: m.AskSuggestionConfig,
		TextWeight:          m.TextWeight,
		MissReply:           m.MissReply,
		BriefIntro:          m.BriefIntro,
		DetailIntro:         m.DetailIntro,
		ShowThink:           m.ShowThink,
		TextRecallTopN:      m.TextRecallTopN,
		TextRecallQuery:     m.TextRecallQuery,
		TextRecallPattern:   m.TextRecallPattern,
		TextRecallSlop:      m.TextRecallSlop,
		Temperature:         m.Temperature,
		QuestionTypeConfig:  m.QuestionTypeConfig,
		ShowInList:          m.ShowInList,
		AllowlistConfig:     m.AllowlistConfig,
		CleanChunks:         m.CleanChunks,
	}

	switch m.Channel {
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
		aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN:
		pb.WeixinChannelConfig = m.toWeixinChannelConfig()
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB:
		pb.TanliveWebChannelConfig = m.toTanliveWebChannelConfig()
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP:
		pb.TanliveAppChannelConfig = m.toTanliveAppChannelConfig()
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP:
		pb.WhatsappChannelConfig = m.toWhatsappChannelConfig()
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM:
		pb.MiniprogramChannelConfig = m.toMiniprogramChannelConfig()
	}

	// admins
	for _, admin := range m.Admins {
		pb.Admins = append(pb.Admins, &basepb.Identity{
			IdentityType: admin.AdminType,
			IdentityId:   admin.AdminID,
		})
	}

	// collection
	if len(m.AssistantCollections) > 0 {
		assistantCollection := m.AssistantCollections[0]
		pb.Threshold = assistantCollection.Threshold
		pb.DocTopN = assistantCollection.DocTopN
		pb.ChunkConfig = getChunkConfigOrDefault(assistantCollection)

		if assistantCollection.Collection != nil {
			pb.CollectionLang = assistantCollection.Collection.Lang
			pb.CollectionName = assistantCollection.Collection.RagName
		}
	}

	// 白名单
	if m.Allowlist != nil && pb.AllowlistConfig != nil {
		for _, item := range m.Allowlist {
			if item.Type == aipb.AssistantAllowlistType_ASSISTANT_ALLOWLIST_TYPE_PHONE {
				pb.AllowlistConfig.Phones = append(pb.AllowlistConfig.Phones, item.Value)
			}
		}
	}

	return pb
}

func (m *TAssistant) toWeixinChannelConfig() *aipb.AssistantWeixinChannelConfig {
	pb := &aipb.AssistantWeixinChannelConfig{}

	pb.Nickname = m.Nickname
	pb.AvatarUrl = m.AvatarUrl
	pb.SystemLanguages = m.SystemLanguages
	pb.WeixinDevelopConfig = m.WeixinDevelopConfig
	pb.WelcomeMessageConfig = m.WelcomeMessageConfig
	pb.PresetQuestionConfig = m.PresetQuestionConfig
	pb.KefuConfig = m.KefuConfig
	pb.RatingScaleReplyConfig = m.RatingScaleReplyConfig
	pb.InteractiveCodeConfig = m.InteractiveCodeConfig
	pb.ChatIdleDuration = m.ChatIdleDuration

	slices.SortStableFunc(m.LiveAgents, func(a, b *TChatLiveAgent) int {
		return a.Order - b.Order
	})
	for _, liveAgent := range m.LiveAgents {
		pb.KefuConfig.Staffs = append(pb.KefuConfig.Staffs, &aipb.AssistantKefuStaff{
			Id:       liveAgent.ID,
			Username: liveAgent.Username,
			Nickname: liveAgent.Nickname,
		})
	}

	// 兼容system_lang字段
	if len(m.SystemLanguages) > 0 {
		pb.SystemLang = m.SystemLanguages[0]
	}

	return pb
}

func (m *TAssistant) toTanliveWebChannelConfig() *aipb.AssistantTanliveWebChannelConfig {
	pb := &aipb.AssistantTanliveWebChannelConfig{}
	pb.Nickname = m.Nickname
	pb.NicknameEn = m.NicknameEn
	pb.AvatarUrl = m.AvatarUrl
	pb.SystemLanguages = m.SystemLanguages
	pb.WebsiteConfig = m.WebsiteConfig
	pb.WelcomeMessageConfig = m.WelcomeMessageConfig
	pb.PresetQuestionConfig = m.PresetQuestionConfig
	pb.RatingScaleReplyConfig = m.RatingScaleReplyConfig
	pb.InteractiveCodeConfig = m.InteractiveCodeConfig
	pb.FeedbackEnabled = m.FeedbackEnabled
	pb.GraphParseConfig = m.GraphParseConfig
	pb.SwitchAssistantId = m.SwitchAssistantId
	pb.KefuConfig = m.KefuConfig

	// 兼容assistant_lang字段
	if len(m.SystemLanguages) > 0 {
		pb.AssistantLang = m.SystemLanguages[0]
	}

	return pb
}

func (m *TAssistant) toTanliveAppChannelConfig() *aipb.AssistantTanliveAppChannelConfig {
	pb := &aipb.AssistantTanliveAppChannelConfig{}
	pb.Nickname = m.Nickname
	pb.NicknameEn = m.NicknameEn
	pb.AvatarUrl = m.AvatarUrl
	pb.SystemLanguages = m.SystemLanguages
	pb.WelcomeMessageConfig = m.WelcomeMessageConfig
	pb.PresetQuestionConfig = m.PresetQuestionConfig
	pb.RatingScaleReplyConfig = m.RatingScaleReplyConfig
	pb.InteractiveCodeConfig = m.InteractiveCodeConfig
	pb.FeedbackEnabled = m.FeedbackEnabled
	pb.GraphParseConfig = m.GraphParseConfig
	pb.KefuConfig = m.KefuConfig
	pb.AppId = m.AppId

	// 兼容assistant_lang字段
	if len(m.SystemLanguages) > 0 {
		pb.AssistantLang = m.SystemLanguages[0]
	}

	return pb
}

func (m *TAssistant) toWhatsappChannelConfig() *aipb.AssistantWhatsappChannelConfig {
	pb := &aipb.AssistantWhatsappChannelConfig{}
	pb.Nickname = m.Nickname
	pb.AvatarUrl = m.AvatarUrl
	pb.SystemLanguages = m.SystemLanguages
	pb.WhatsappDevelopConfig = m.WhatsappDevelopConfig
	pb.WelcomeMessageConfig = m.WelcomeMessageConfig
	pb.PresetQuestionConfig = m.PresetQuestionConfig
	pb.RatingScaleReplyConfig = m.RatingScaleReplyConfig
	pb.ChatIdleDuration = m.ChatIdleDuration

	// 兼容system_lang字段
	if len(m.SystemLanguages) > 0 {
		pb.SystemLang = m.SystemLanguages[0]
	}

	return pb
}

func (m *TAssistant) toMiniprogramChannelConfig() *aipb.AssistantMiniprogramChannelConfig {
	pb := &aipb.AssistantMiniprogramChannelConfig{}
	pb.Nickname = m.Nickname
	pb.NicknameEn = m.NicknameEn
	pb.AvatarUrl = m.AvatarUrl
	pb.SystemLanguages = m.SystemLanguages
	pb.MiniprogramConfig = m.MiniprogramConfig
	pb.WelcomeMessageConfig = m.WelcomeMessageConfig
	pb.PresetQuestionConfig = m.PresetQuestionConfig
	pb.RatingScaleReplyConfig = m.RatingScaleReplyConfig
	pb.InteractiveCodeConfig = m.InteractiveCodeConfig
	pb.FeedbackEnabled = m.FeedbackEnabled
	pb.GraphParseConfig = m.GraphParseConfig
	pb.KefuConfig = m.KefuConfig

	// 兼容assistant_lang字段
	if len(m.SystemLanguages) > 0 {
		pb.AssistantLang = m.SystemLanguages[0]
	}

	return pb
}

// ApplyAssistantAdminCond 应用助手过滤条件
func ApplyAssistantAdminCond(db *gorm.DB, userIDs, teamIDs []uint64, not bool) {
	ApplyAssistantAdminCondWithField(db, userIDs, teamIDs, not, "t_assistant.id")
}

// ApplyAssistantAdminCondWithField 应用助手过滤条件
func ApplyAssistantAdminCondWithField(db *gorm.DB, userIDs, teamIDs []uint64, not bool, assistantIDField string) {
	sql := "EXISTS (SELECT 1 FROM t_assistant_admin AS admin WHERE " +
		"admin.assistant_id = " + assistantIDField + " AND admin.admin_id in (?) AND admin.admin_type = ?)"

	var exprs []clause.Expression

	if len(userIDs) > 0 {
		userSql := sql
		if not {
			userSql = "NOT " + sql
		}
		exprs = append(exprs, clause.Expr{
			SQL:  userSql,
			Vars: []any{userIDs, basepb.IdentityType_IDENTITY_TYPE_USER},
		})
	}
	if len(teamIDs) > 0 {
		teamSql := sql
		if not {
			teamSql = "NOT " + sql
		}
		exprs = append(exprs, clause.Expr{
			SQL:  teamSql,
			Vars: []any{teamIDs, basepb.IdentityType_IDENTITY_TYPE_TEAM},
		})
	}
	if len(exprs) > 0 {
		if not {
			db.Where(clause.AndConditions{Exprs: exprs})
		} else {
			db.Where(clause.OrConditions{Exprs: exprs})
		}
	}
}

// ApplyAssistantDoc 拥有指定文档的助手
func ApplyAssistantDoc(db *gorm.DB, docID uint64) {
	const sql = "EXISTS (SELECT 1 FROM t_assistant_doc AS doc WHERE doc.assistant_id = t_assistant.id AND doc.doc_id = ? AND doc.state = ?)"
	if docID > 0 {
		db.Where(sql, docID, aipb.DocState_DOC_STATE_ENABLED)
	}
}

// IsWeixinChannel 是否为微信渠道
func (m *TAssistant) IsWeixinChannel() bool {
	return m.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN ||
		m.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN
}

// IsWeixinChannelConfig 是否为微信渠道配置
func IsWeixinChannelConfig(config *aipb.AssistantConfig) bool {
	return config.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN ||
		config.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN
}

func getChunkConfigOrDefault(ac *TAssistantCollection) *aipb.AssistantChunkConfig {
	if ac == nil {
		return &aipb.AssistantChunkConfig{}
	}

	if ac.ChunkConfig != nil {
		return ac.ChunkConfig
	}

	if ac.Collection == nil {
		return &aipb.AssistantChunkConfig{}
	}

	cfg, err := GetDefaultChunkConfig(ac.Collection.Lang, "zh")
	if err != nil {
		return &aipb.AssistantChunkConfig{}
	}

	return cfg
}

func getInteractiveCodeOption() map[aipb.InteractiveCode]*aipb.InteractiveCodeOption {
	var codes []*aipb.InteractiveCodeOption
	if err := config.Unmarshal("assistant.interactiveCodeOption", &codes); err != nil {
		return nil
	}

	m := make(map[aipb.InteractiveCode]*aipb.InteractiveCodeOption)
	for _, v := range codes {
		m[v.Code] = v
	}
	return m
}

// IsAssistantAdmin 判断是否为助手的管理员
func IsAssistantAdmin(tx *gorm.DB, assistantID uint64, identity *basepb.Identity) (bool, error) {
	var cnt int64
	err := tx.Model(&TAssistantAdmin{}).Where("assistant_id = ? AND admin_id = ? AND admin_type = ?",
		assistantID, identity.IdentityId, identity.IdentityType).Count(&cnt).Error
	if err != nil {
		return false, fmt.Errorf("query assistant admin count: %w", err)
	}

	return cnt > 0, nil
}
