// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TShareSender 发送方分享设置
type TShareSender struct {
	ID           uint64                    `gorm:"primaryKey;column:id" json:"id"`                                                // 自增id
	AdminType    uint32                    `gorm:"column:admin_type" json:"adminType"`                                            // 1: 前台用户 2: 前台团队
	ReceiverID   uint64                    `gorm:"column:receiver_id" json:"receiverId"`                                          // 分享的接收方id,助手id、
	ReceiverType aipb.DocShareReceiverType `gorm:"column:receiver_type;default:1" json:"receiverType"`                            // 接收方类型: 助手、个人、团队
	CreateBy     uint64                    `gorm:"column:create_by;default:0" json:"createBy"`                                    // 创建人id
	UpdateBy     uint64                    `gorm:"column:update_by;default:0" json:"updateBy"`                                    // 最近更新人id
	UpdateDate   time.Time                 `gorm:"column:update_date;autoUpdateTime;default:CURRENT_TIMESTAMP" json:"updateDate"` // 最近更新时间
	CreateDate   time.Time                 `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TShareSender) TableName() string {
	return "t_share_sender"
}

// TShareSenderColumns get sql column name.获取数据库列名
var TShareSenderColumns = struct {
	ID               string
	AdminType        string
	ShareAssistantID string
	CreateBy         string
	UpdateBy         string
	UpdateDate       string
	CreateDate       string
}{
	ID:               "id",
	AdminType:        "admin_type",
	ShareAssistantID: "share_assistant_id",
	CreateBy:         "create_by",
	UpdateBy:         "update_by",
	UpdateDate:       "update_date",
	CreateDate:       "create_date",
}
