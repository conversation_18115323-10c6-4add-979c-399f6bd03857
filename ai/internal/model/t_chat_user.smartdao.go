// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatUser [...]
type TChatUser struct {
	ID       uint64 `gorm:"primaryKey;column:id" json:"id"`             // 自增id
	Username string `gorm:"column:username;default:''" json:"username"` // 用户名称
	Nickname string `gorm:"column:nickname;default:''" json:"nickname"` // 微信昵称
	Origin   uint32 `gorm:"column:origin" json:"origin"`                // 用户类型 1-tanlive 2-无毒
}

// TableName get sql table name.获取数据库表名
func (m *TChatUser) TableName() string {
	return "t_chat_user"
}

// TChatUserColumns get sql column name.获取数据库列名
var TChatUserColumns = struct {
	ID       string
	Username string
	Nickname string
	Origin   string
}{
	ID:       "id",
	Username: "username",
	Nickname: "nickname",
	Origin:   "origin",
}
