package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// ToPb ...
func (m *TAssistantLog) ToPb() *aipb.AssistantLog {
	if m == nil {
		return nil
	}
	return &aipb.AssistantLog{
		Id:          m.ID,
		AssistantId: m.AssistantID,
		Action:      m.Action,
		Changes:     m.Changes,
		CreateBy:    m.CreateBy,
		CreateDate:  timestamppb.New(m.CreateDate),
	}
}

// TAssistantLogs 日志列表
type TAssistantLogs []*TAssistantLog

// ToPb ...
func (ms TAssistantLogs) ToPb() []*aipb.AssistantLog {
	pbs := make([]*aipb.AssistantLog, 0, len(ms))
	for _, m := range ms {
		pbs = append(pbs, m.ToPb())
	}
	return pbs
}
