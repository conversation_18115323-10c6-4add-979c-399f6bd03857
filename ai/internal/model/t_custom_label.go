package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// LabelWithChain 获取标签的下一列信息
func LabelWithChain(db *gorm.DB) *gorm.DB {
	return db
	// return db.Select("*, (SELECT id FROM t_custom_label AS c2 WHERE c2.sort > t_custom_label.sort AND c2.tenant_id = t_custom_label.tenant_id and c2.object_type = t_custom_label.object_type  ORDER BY c2.sort ASC LIMIT 1) AS next_label_id").
	// 	// 为了纠正多个标签指向相同固定表头时的顺序
	// 	Order("update_date desc")
}

// ToProto proto格式
func (t *TCustomLabel) ToProto() *aipb.CustomLabel {
	if t == nil {
		return nil
	}

	pb := &aipb.CustomLabel{
		Id:   t.ID,
		Type: aipb.CustomLabelType(t.Type),
		Key:  t.Key,
	}
	switch t.Type {
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM:
		pb.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_EnumValue{EnumValue: t.Value}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M:
		pb.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_EnumMValue{EnumMValue: t.Value}}
	}
	if len(t.NextLabel) != 0 {
		pb.NextLabel = &aipb.CustomLabel_NextLabelName{NextLabelName: t.NextLabel}
	}
	if t.NextLabelId != 0 {
		pb.NextLabel = &aipb.CustomLabel_NextLabelId{NextLabelId: t.NextLabelId}
	}
	return pb
}

// FromProto ...
func (t *TCustomLabel) FromProto(pb *aipb.CustomLabel, objType aipb.CustomLabelObjectType, operatorId ...uint64) *TCustomLabel {
	if t == nil {
		return t
	}
	t.ID = pb.Id
	t.Type = pb.Type
	t.Key = pb.Key
	t.CreateBy = pb.CreateBy
	t.UpdateBy = pb.UpdateBy
	if len(operatorId) > 0 {
		t.CreateBy = operatorId[0]
		t.UpdateBy = operatorId[0]
	}
	t.ObjectType = objType
	if pb.GetNextLabelId() != 0 {
		t.NextLabelId = pb.GetNextLabelId()
	}
	if pb.GetNextLabelName() != "" {
		t.NextLabel = pb.GetNextLabelName()
	}
	switch pb.Type {
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM:
		t.Value = pb.GetValue().GetEnumValue()
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M:
		t.Value = pb.GetValue().GetEnumMValue()
	}
	return t
}
