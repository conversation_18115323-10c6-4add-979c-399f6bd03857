package model

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"golang.org/x/exp/slices"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 仅用于测试的模拟结构体
type TestDoc struct {
	ID uint64
}

type TestAssistantDoc struct {
	DocID       uint64
	AssistantID uint64
	State       int
}

// mockQuery 用于模拟 NewQuery 函数，仅用于测试
func mockQuery[T any](ctx context.Context) *Query[T] {
	return &Query[T]{ctx: ctx}
}

// 测试用的 Query 结构
type Query[T any] struct {
	ctx context.Context
	db  *gorm.DB
}

func (q *Query[T]) DB() *gorm.DB {
	if q.db == nil {
		db, _ := gorm.Open(sqlite.Open("file::memory:"), &gorm.Config{})
		q.db = db.Model(new(T))
	}
	return q.db
}

// 辅助函数，用于获取查询的SQL
func getSQLFromFilter(t *testing.T, filter func(*gorm.DB) *gorm.DB) string {
	db, err := gorm.Open(sqlite.Open("file::memory:"), &gorm.Config{})
	if err != nil {
		t.Fatalf("failed to open database: %v", err)
	}

	// 确保DryRun会正确处理子查询
	session := db.Session(&gorm.Session{DryRun: true})
	stmt := session.Model(&TestDoc{}).Scopes(filter).Find(&TestDoc{}).Statement
	sql := stmt.SQL.String()
	// 简化SQL以便于测试，移除参数占位符
	sql = strings.Replace(sql, "?", "PARAM", -1)
	return sql
}

// 模拟DocWithNoAssistantIdFilter函数
func mockNoAssistantIdFilter(assistantId []uint64) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("NOT EXISTS (SELECT 1 FROM t_assistant_docs WHERE doc_id = t_doc.id)")
	}
}

// 模拟当前实现
func mockCurrentAssistantIdFilter(ctx context.Context, assistantId []uint64, excluded bool, zeroAsNoAssistant bool, isOr ...bool) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(assistantId) == 0 {
			return tx
		}

		// 扩号扩住的条件组
		quoteQuery := mockQuery[TestDoc](ctx).DB()

		hasZero := slices.Contains(assistantId, 0)
		if hasZero && zeroAsNoAssistant {
			quoteQuery.Scopes(mockNoAssistantIdFilter(assistantId))
		}
		subQuery := mockQuery[TestAssistantDoc](ctx).
			DB().
			Select("1").
			Where("assistant_id IN (?)", assistantId).
			Where("doc_id = t_doc.id")
		quoteQuery.Or("EXISTS (?)", subQuery)

		query := mockQuery[TestDoc](ctx).DB()
		if excluded {
			query.Not("EXISTS (?)", subQuery)
		} else {
			query.Where("EXISTS (?)", subQuery)
		}
		// 应用query到tx
		if len(isOr) > 0 && isOr[0] {
			return tx.Or(query)
		}
		return tx.Where(query)
	}
}

// 模拟修改后的实现
func mockFixedAssistantIdFilter(ctx context.Context, assistantId []uint64, excluded bool, zeroAsNoAssistant bool, isOr ...bool) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(assistantId) == 0 {
			return tx
		}

		// 构建子查询
		subQuery := mockQuery[TestAssistantDoc](ctx).
			DB().
			Select("1").
			Where("assistant_id IN (?)", assistantId).
			Where("doc_id = t_doc.id")

		// 构建查询条件
		mainQuery := mockQuery[TestDoc](ctx).DB()

		hasZero := slices.Contains(assistantId, 0)
		if hasZero && zeroAsNoAssistant {
			// 创建一个子查询，查找没有绑定助手的文档
			noAssistantQuery := mockNoAssistantIdFilter(nil)(mockQuery[TestDoc](ctx).DB())

			combinedQuery := mockQuery[TestDoc](ctx).DB()
			if excluded {
				// 排除情况：既不在指定助手列表中，也不是无助手的文档
				combinedQuery.Where("NOT EXISTS (?)", subQuery)
				combinedQuery.Where("NOT " + noAssistantQuery.Statement.SQL.String())
			} else {
				// 包含情况：在指定助手列表中，或者是无助手的文档
				combinedQuery.Where(func(db *gorm.DB) *gorm.DB {
					return db.Where("EXISTS (?)", subQuery).Or(noAssistantQuery.Statement.SQL.String())
				})
			}
			mainQuery = combinedQuery
		} else {
			// 不考虑"无助手"的情况
			if excluded {
				mainQuery.Where("NOT EXISTS (?)", subQuery)
			} else {
				mainQuery.Where("EXISTS (?)", subQuery)
			}
		}

		// 应用到tx
		if len(isOr) > 0 && isOr[0] {
			return tx.Or(mainQuery)
		}
		return tx.Where(mainQuery)
	}
}

func TestAssistantIdFilter(t *testing.T) {

	tests := []struct {
		name              string
		assistantId       []uint64
		excluded          bool
		zeroAsNoAssistant bool
		isOr              bool
	}{
		{
			name:              "1. Empty assistantId",
			assistantId:       []uint64{},
			excluded:          false,
			zeroAsNoAssistant: false,
			isOr:              false,
		},
		{
			name:              "2. Normal assistantId, not excluded",
			assistantId:       []uint64{1, 2, 3},
			excluded:          false,
			zeroAsNoAssistant: false,
			isOr:              false,
		},
		{
			name:              "3. Normal assistantId, excluded",
			assistantId:       []uint64{1, 2, 3},
			excluded:          true,
			zeroAsNoAssistant: false,
			isOr:              false,
		},
		{
			name:              "4. With zero, zeroAsNoAssistant=true",
			assistantId:       []uint64{0, 1, 2},
			excluded:          false,
			zeroAsNoAssistant: true,
			isOr:              false,
		},
		{
			name:              "5. With zero, zeroAsNoAssistant=true, excluded=true",
			assistantId:       []uint64{0, 1, 2},
			excluded:          true,
			zeroAsNoAssistant: true,
			isOr:              false,
		},
		{
			name:              "6. Only zero, zeroAsNoAssistant=true",
			assistantId:       []uint64{0},
			excluded:          false,
			zeroAsNoAssistant: true,
			isOr:              false,
		},
		{
			name:              "7. Normal assistantId, OR condition",
			assistantId:       []uint64{1, 2, 3},
			excluded:          false,
			zeroAsNoAssistant: false,
			isOr:              true,
		},
	}

	fmt.Println("===== 当前实现 =====")
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := DocWithAssistantIdFilter(tt.assistantId, tt.excluded, tt.zeroAsNoAssistant, tt.isOr)
			sql := getSQLFromFilter(t, filter)
			fmt.Printf("%s\nSQL: %s\n\n", tt.name, sql)
		})
	}

}
