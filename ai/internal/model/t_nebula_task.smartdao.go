// Code generated by smartdao. DO NOT EDIT.
// versions:
//  smartdao	v0.0.13

package model

import (
	"time"

	"gorm.io/gorm"
)

// TNebulaTask 云星任务
type TNebulaTask struct {
	ID                    uint64         `gorm:"primaryKey;column:id" json:"id"`
	UUID                  string         `gorm:"column:uuid" json:"uuid"`
	UserID                uint64         `gorm:"column:user_id" json:"userId"`
	TeamID                uint64         `gorm:"column:team_id" json:"teamId"`
	AdminType             uint32         `gorm:"column:admin_type;default:1" json:"adminType"`    // 个人用户 1 团队用户 2
	SplitSize             uint32         `gorm:"column:split_size" json:"splitSize"`              // 拆分节点数值
	Lang                  string         `gorm:"column:lang;default:''" json:"lang"`              // 语言 zh、 en、bgem3
	FilterHash            string         `gorm:"column:filter_hash;default:''" json:"filterHash"` // 排列组合hash值
	FilterText            string         `gorm:"column:filter_text" json:"filterText"`            // 过滤条件json
	AssistantIDs          string         `gorm:"column:assistant_ids" json:"assistantIds"`
	QueryAssistantIDs     string         `gorm:"column:query_assistant_ids;default:''" json:"queryAssistantIds"`
	PklPath               string         `gorm:"column:pkl_path;default:''" json:"pklPath"`           // 模型文件地址
	CollectionPath        string         `gorm:"column:collection_path" json:"collectionPath"`        // collection原始路径
	VecCollectionPath     string         `gorm:"column:vec_collection_path" json:"vecCollectionPath"` // 向量后的collection数据
	VecFileSize           uint32         `gorm:"column:vec_file_size" json:"vecFileSize"`
	ResultCollectionToVec string         `gorm:"column:result_collection_to_vec" json:"resultCollectionToVec"`
	VecQueryPath          string         `gorm:"column:vec_query_path;default:''" json:"vecQueryPath"` // 向量后的query数据
	VecQueryFileSize      uint32         `gorm:"column:vec_query_file_size" json:"vecQueryFileSize"`
	ResultQueryToVec      string         `gorm:"column:result_query_to_vec" json:"resultQueryToVec"`
	ResultFetchCollection string         `gorm:"column:result_fetch_collection;default:''" json:"resultFetchCollection"`
	ResultPrecompute      string         `gorm:"column:result_precompute" json:"resultPrecompute"`
	ResultCluster         string         `gorm:"column:result_cluster" json:"resultCluster"`
	CentersCosURL         string         `gorm:"column:centers_cos_url;default:''" json:"centersCosUrl"`
	ConnectionCosURL      string         `gorm:"column:connection_cos_url;default:''" json:"connectionCosUrl"`
	ModelPathCluster      string         `gorm:"column:model_path_cluster;default:''" json:"modelPathCluster"`
	CalcuResult           string         `gorm:"column:calcu_result;default:''" json:"calcuResult"` // 数据结果
	ErrMsg                string         `gorm:"column:err_msg;default:''" json:"errMsg"`           // 错误信息
	AutoCreate            uint32         `gorm:"column:auto_create;default:01" json:"autoCreate"`   // 1 普通 2自动
	IsRead                uint32         `gorm:"column:is_read;default:1" json:"isRead"`            // 1 未读 2已读
	State                 uint32         `gorm:"column:state;default:1" json:"state"`               // 1 进行中 2任务完成 3任务失败
	EndDate               *time.Time     `gorm:"column:end_date" json:"endDate"`                    // 最后一次任务结束时间
	CreateDate            time.Time      `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"`
	UpdateDate            time.Time      `gorm:"column:update_date;autoUpdateTime;default:CURRENT_TIMESTAMP" json:"updateDate"`
	DeletedAt             gorm.DeletedAt `gorm:"column:deleted_at" json:"deletedAt"`
}

// TableName get sql table name.获取数据库表名
func (m *TNebulaTask) TableName() string {
	return "db_tanlive_ai.t_nebula_task"
}

// TNebulaTaskColumns get sql column name.获取数据库列名
var TNebulaTaskColumns = struct {
	ID                    string
	UUID                  string
	UserID                string
	TeamID                string
	AdminType             string
	SplitSize             string
	Lang                  string
	FilterHash            string
	FilterText            string
	AssistantIDs          string
	QueryAssistantIDs     string
	PklPath               string
	CollectionPath        string
	VecCollectionPath     string
	VecFileSize           string
	ResultCollectionToVec string
	VecQueryPath          string
	VecQueryFileSize      string
	ResultQueryToVec      string
	ResultFetchCollection string
	ResultPrecompute      string
	ResultCluster         string
	CentersCosURL         string
	ConnectionCosURL      string
	ModelPathCluster      string
	CalcuResult           string
	ErrMsg                string
	AutoCreate            string
	IsRead                string
	State                 string
	EndDate               string
	CreateDate            string
	UpdateDate            string
	DeletedAt             string
}{
	ID:                    "id",
	UUID:                  "uuid",
	UserID:                "user_id",
	TeamID:                "team_id",
	AdminType:             "admin_type",
	SplitSize:             "split_size",
	Lang:                  "lang",
	FilterHash:            "filter_hash",
	FilterText:            "filter_text",
	AssistantIDs:          "assistant_ids",
	QueryAssistantIDs:     "query_assistant_ids",
	PklPath:               "pkl_path",
	CollectionPath:        "collection_path",
	VecCollectionPath:     "vec_collection_path",
	VecFileSize:           "vec_file_size",
	ResultCollectionToVec: "result_collection_to_vec",
	VecQueryPath:          "vec_query_path",
	VecQueryFileSize:      "vec_query_file_size",
	ResultQueryToVec:      "result_query_to_vec",
	ResultFetchCollection: "result_fetch_collection",
	ResultPrecompute:      "result_precompute",
	ResultCluster:         "result_cluster",
	CentersCosURL:         "centers_cos_url",
	ConnectionCosURL:      "connection_cos_url",
	ModelPathCluster:      "model_path_cluster",
	CalcuResult:           "calcu_result",
	ErrMsg:                "err_msg",
	AutoCreate:            "auto_create",
	IsRead:                "is_read",
	State:                 "state",
	EndDate:               "end_date",
	CreateDate:            "create_date",
	UpdateDate:            "update_date",
	DeletedAt:             "deleted_at",
}
