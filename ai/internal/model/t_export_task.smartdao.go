// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TExportTask 导出任务
type TExportTask struct {
	ID             uint64         `gorm:"primaryKey;column:id" json:"id"`                                 // 自增主键
	State          int32          `gorm:"column:state;default:1" json:"state"`                            // 导出任务状态
	Type           int32          `gorm:"column:type;default:1" json:"type"`                              // 类型：QA、文本文件、会话
	URL            string         `gorm:"column:url;default:''" json:"url"`                               // cos存储路径
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at" json:"deletedAt"`                             // 删除时间
	CreateBy       uint64         `gorm:"column:create_by;default:0" json:"createBy"`                     // 创建人
	CreateDate     time.Time      `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	LastUpdateDate time.Time      `gorm:"column:last_update_date;default:CURRENT_TIMESTAMP" json:"lastUpdateDate"`
	OperationType  uint32         `gorm:"column:operation_type;default:1" json:"operationType"` // 操作类型 1导出 2导入
	ExtraInfo      string         `gorm:"column:extra_info" json:"extraInfo"`                   // 其他业务信息
	Paths          datatypes.JSON `gorm:"column:paths" json:"paths"`                            // 存储多个路径的JSON数组
	FilterSnapshot string         `gorm:"column:filter_snapshot" json:"filterSnapshot"`         // 请求列表筛选条件快照
	FieldsSnapshot string         `gorm:"column:fields_snapshot" json:"fieldsSnapshot"`         // 请求列表筛选字段快照
}

// TableName get sql table name.获取数据库表名
func (m *TExportTask) TableName() string {
	return "t_export_task"
}

// TExportTaskColumns get sql column name.获取数据库列名
var TExportTaskColumns = struct {
	ID             string
	State          string
	Type           string
	URL            string
	DeletedAt      string
	CreateBy       string
	CreateDate     string
	LastUpdateDate string
	OperationType  string
	ExtraInfo      string
	Paths          string
	FilterSnapshot string
	FieldsSnapshot string
}{
	ID:             "id",
	State:          "state",
	Type:           "type",
	URL:            "url",
	DeletedAt:      "deleted_at",
	CreateBy:       "create_by",
	CreateDate:     "create_date",
	LastUpdateDate: "last_update_date",
	OperationType:  "operation_type",
	ExtraInfo:      "extra_info",
	Paths:          "paths",
	FilterSnapshot: "filter_snapshot",
	FieldsSnapshot: "fields_snapshot",
}
