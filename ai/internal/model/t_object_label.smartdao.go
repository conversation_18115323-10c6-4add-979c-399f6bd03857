// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TObjectLabel [...]
type TObjectLabel struct {
	ObjectID       uint64                     `gorm:"column:object_id" json:"objectId"`
	LabelID        uint64                     `gorm:"column:label_id" json:"labelId"`
	McSort         uint32                     `gorm:"column:mc_sort" json:"mcSort"`                                           // 多选项的排序
	IntValue       int64                      `gorm:"column:int_value" json:"intValue"`
	UINtValue      uint64                     `gorm:"column:uint_value" json:"uintValue"`
	FloatValue     float64                    `gorm:"column:float_value" json:"floatValue"`
	StringValue    string                     `gorm:"column:string_value" json:"stringValue"`
	CreateDate     time.Time                  `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"`          // 创建时间
	LastUpdateDate time.Time                  `gorm:"column:last_update_date;default:CURRENT_TIMESTAMP" json:"lastUpdateDate"` // 最近更新时间
	ObjectType     aipb.CustomLabelObjectType `gorm:"column:object_type" json:"objectType"`
	LabelType      aipb.CustomLabelType       `gorm:"column:label_type" json:"labelType"`

	// 标签详情
	LabelInfo      *TCustomLabel              `gorm:"foreignKey:LabelID" json:"labelInfo"`
	DeletedAt       gorm.DeletedAt     		  `gorm:"column:deleted_at" json:"deletedAt,omitempty"`                                   // 删除时间
}

// TableName get sql table name.获取数据库表名
func (m *TObjectLabel) TableName() string {
	return "t_object_label"
}

// TObjectLabelColumns get sql column name.获取数据库列名
var TObjectLabelColumns = struct {
	ObjectID       string
	LabelID        string
	IntValue       string
	UINtValue      string
	FloatValue     string
	StringValue    string
	CreateDate     string
	LastUpdateDate string
	ObjectType     string
	LabelType      string
	DeletedAt      string
	McSort         string
}{
	ObjectID:       "object_id",
	LabelID:        "label_id",
	IntValue:       "int_value",
	UINtValue:      "uint_value",
	FloatValue:     "float_value",
	StringValue:    "string_value",
	CreateDate:     "create_date",
	LastUpdateDate: "last_update_date",
	ObjectType:     "object_type",
	LabelType:      "label_type",
	DeletedAt:      "deleted_at",
	McSort:         "mc_sort",
}
