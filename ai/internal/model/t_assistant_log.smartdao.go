package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TAssistantLog [...]
type TAssistantLog struct {
	ID          uint64                 `gorm:"primaryKey;column:id" json:"id"`                                 // 主键
	AssistantID uint64                 `gorm:"column:assistant_id;default:0" json:"assistantId"`               // 助手ID
	Action      aipb.AssistantAction   `gorm:"column:action;default:0" json:"action"`                          // 操作
	Changes     *aipb.AssistantChanges `gorm:"column:changes;serializer:json" json:"changes"`                  // 变化
	CreateBy    *basepb.Identity       `gorm:"column:create_by;serializer:json" json:"createBy"`               // 操作人
	CreateDate  time.Time              `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 操作时间
}

// TableName get sql table name.获取数据库表名
func (m *TAssistantLog) TableName() string {
	return "t_assistant_log"
}

// TAssistantLogColumns get sql column name.获取数据库列名
var TAssistantLogColumns = struct {
	ID          string
	AssistantID string
	Action      string
	Changes     string
	CreateBy    string
	CreateDate  string
}{
	ID:          "id",
	AssistantID: "assistant_id",
	Action:      "action",
	Changes:     "changes",
	CreateBy:    "create_by",
	CreateDate:  "create_date",
}
