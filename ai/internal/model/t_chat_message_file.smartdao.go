// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatMessageFile [...]
type TChatMessageFile struct {
	ID        uint64 `gorm:"primaryKey;column:id" json:"id"`      // 自增id
	MessageID uint64 `gorm:"column:message_id" json:"messageId"`  // 消息id
	URL       string `gorm:"column:url" json:"url"`               // 文件url
	State     aipb.ChatMessageFileState `gorm:"column:state;default:0" json:"state"` // 状态
	Text      string `gorm:"column:text" json:"text"`             // 文件解析内容
	Type      int32 `gorm:"column:type;default:0" json:"type"`   // 文件类型
	UpdateDate    time.Time      `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"` // 更新时间
	CreateDate    time.Time      `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	ParsedURL       string `gorm:"column:parsed_url" json:"parsed_url"`               // 解析后的url
}

// TableName get sql table name.获取数据库表名
func (m *TChatMessageFile) TableName() string {
	return "t_chat_message_file"
}

// TChatMessageFileColumns get sql column name.获取数据库列名
var TChatMessageFileColumns = struct {
	ID        string
	MessageID string
	URL       string
	State     string
	Text      string
	Type      string
}{
	ID:        "id",
	MessageID: "message_id",
	URL:       "url",
	State:     "state",
	Text:      "text",
	Type:      "type",
}
