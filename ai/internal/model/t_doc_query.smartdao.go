// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocQuery 文件查询表
type TDocQuery struct {
	ID       uint64         `gorm:"primaryKey;column:id" json:"id"`             // 自增id
	Data     DocIds         `gorm:"column:data" json:"data"`                    // id的json数组
	DataType uint32         `gorm:"column:data_type;default:0" json:"dataType"` // 1:qa 2:doc
	CreateBy *ai.Operator   `gorm:"column:create_by;serializer:json" json:"createBy,omitempty"`           // 创建人信息
}

// TableName get sql table name.获取数据库表名
func (m *TDocQuery) TableName() string {
	return "t_doc_query"
}

// TDocQueryColumns get sql column name.获取数据库列名
var TDocQueryColumns = struct {
	ID       string
	Data     string
	DataType string
	CreateBy string
}{
	ID:       "id",
	Data:     "data",
	DataType: "data_type",
	CreateBy: "create_by",
}
