package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocChunkDetail [...]
type TDocChunkDetail struct {
	ID    uint64       `gorm:"primaryKey;column:id" json:"id"`            // 主键
	Sum   string       `gorm:"column:sum;default:''" json:"sum"`          // 摘要
	Items []*ChunkItem `gorm:"column:items;serializer:json" json:"items"` // 分段信息
}

// TableName get sql table name.获取数据库表名
func (m *TDocChunkDetail) TableName() string {
	return "t_doc_chunk_detail"
}

// TDocChunkDetailColumns get sql column name.获取数据库列名
var TDocChunkDetailColumns = struct {
	ID        string
	Sum       string
	ChunkInfo string
}{
	ID:        "id",
	Sum:       "sum",
	ChunkInfo: "chunk_info",
}
