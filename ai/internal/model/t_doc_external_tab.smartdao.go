// Code generated by smartdao. DO NOT EDIT.
// versions:
//  smartdao	v0.0.13

package model

import (
	"time"
)

// TDocExternalTab 知识库用户自定义tab
type TDocExternalTab struct {
	ID         uint64    `gorm:"primaryKey;column:id" json:"id"`
	Type       uint32    `gorm:"column:type;default:0" json:"type"` // 1 腾讯文档 2 SQL类型
	Name       string    `gorm:"column:name;default:''" json:"name"`
	AdminType  int32     `gorm:"column:admin_type" json:"adminType"` // 1: 前台用户 2: 前台团队
	AccountID  int64     `gorm:"column:account_id" json:"accountId"`
	CreateDate time.Time `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"`
	UpdateDate time.Time `gorm:"column:update_date;autoUpdateTime;default:CURRENT_TIMESTAMP" json:"updateDate"`
}

// TableName get sql table name.获取数据库表名
func (m *TDocExternalTab) TableName() string {
	return "db_tanlive_ai.t_doc_external_tab"
}

// TDocExternalTabColumns get sql column name.获取数据库列名
var TDocExternalTabColumns = struct {
	ID         string
	Type       string
	Name       string
	AdminType  string
	AccountID  string
	CreateDate string
	UpdateDate string
}{
	ID:         "id",
	Type:       "type",
	Name:       "name",
	AdminType:  "admin_type",
	AccountID:  "account_id",
	CreateDate: "create_date",
	UpdateDate: "update_date",
}
