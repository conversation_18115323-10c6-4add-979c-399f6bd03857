// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatAgentTask agent配置表
type TChatAgentTask struct {
	ID           uint64         `gorm:"primaryKey;column:id" json:"id"`                      // 自增id
	AssistantID  uint64         `gorm:"column:assistant_id" json:"assistantId"`              // 助手id
	Command      string         `gorm:"column:command" json:"command"`                       // 暗号
	PromptPrefix string         `gorm:"column:prompt_prefix;default:''" json:"promptPrefix"` // prompt
	PreTaskID    uint64         `gorm:"column:pre_task_id" json:"preTaskId"`                 // 助手id
	FetchType    int32          `gorm:"column:fetch_type;default:1" json:"fetchType"`
	DefaultText  string         `gorm:"column:default_text" json:"defaultText"` // 默认回复
	Prompt       string         `gorm:"column:prompt" json:"prompt"`            // 固定问题
	MatchConfig  datatypes.JSON `gorm:"column:match_config" json:"matchConfig"` // 匹配配置
}

// TableName get sql table name.获取数据库表名
func (m *TChatAgentTask) TableName() string {
	return "t_chat_agent_task"
}

// TChatAgentTaskColumns get sql column name.获取数据库列名
var TChatAgentTaskColumns = struct {
	ID           string
	AssistantID  string
	Command      string
	PromptPrefix string
	PreTaskID    string
	FetchType    string
	DefaultText  string
	Prompt       string
	MatchConfig  string
}{
	ID:           "id",
	AssistantID:  "assistant_id",
	Command:      "command",
	PromptPrefix: "prompt_prefix",
	PreTaskID:    "pre_task_id",
	FetchType:    "fetch_type",
	DefaultText:  "default_text",
	Prompt:       "prompt",
	MatchConfig:  "match_config",
}
