// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatLiveAgent 会话人工客服表
type TChatLiveAgent struct {
	ID          uint64                   `gorm:"primaryKey;column:id" json:"id"`                                                // 自增主键
	AssistantID uint64                   `gorm:"column:assistant_id" json:"assistantId"`                                        // 助手id
	Username    string                   `gorm:"column:username;default:''" json:"username"`                                    // 人工坐席名称
	Nickname    string                   `gorm:"column:nickname;default:''" json:"nickname"`                                    // 昵称
	CreateDate  time.Time                `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	Status      aipb.ChatLiveAgentStatus `gorm:"column:status;default:1" json:"status"`                                         // 1:接待中,2:停止接待3:正在挂起。
	Order       int                      `gorm:"column:order;default:0" json:"order"`                                           // 排序
}

// TableName get sql table name.获取数据库表名
func (m *TChatLiveAgent) TableName() string {
	return "t_chat_live_agent"
}

// TChatLiveAgentColumns get sql column name.获取数据库列名
var TChatLiveAgentColumns = struct {
	ID          string
	AssistantID string
	Username    string
	Nickname    string
	CreateDate  string
	Status      string
	Order       string
}{
	ID:          "id",
	AssistantID: "assistant_id",
	Username:    "username",
	Nickname:    "nickname",
	CreateDate:  "create_date",
	Status:      "status",
	Order:       "order",
}
