package model

import (
	"database/sql"
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocChunkTask [...]
type TDocChunkTask struct {
	ID           uint64                 `gorm:"primaryKey;column:id" json:"id"`
	DocID        uint64                 `gorm:"column:doc_id;default:0" json:"docId"`                           // 文档ID
	State        aipb.DocChunkTaskState `gorm:"column:state;default:0" json:"state"`                            // 状态：1执行中、2已完成
	Type         aipb.DocChunkTaskType  `gorm:"column:type;default:0" json:"type"`                              // 任务类型：1手动分段、2自动分段
	Para         *TDocChunkTaskPara     `gorm:"column:para;serializer:json" json:"para"`                        // 任务参数
	SubtaskCount int                    `gorm:"column:subtask_count;default:0" json:"subtaskCount"`             // 子任务数量
	FinishedAt   sql.NullTime           `gorm:"column:finished_at" json:"finishedAt"`                           // 完成时间
	CreateBy     *basepb.Identity       `gorm:"column:create_by;serializer:json" json:"createBy"`               // 创建人
	CreateDate   time.Time              `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间

	CollectionIds []uint64            `gorm:"->;column:collection_ids;serializer:json" json:"collectionIds"` // 知识库ID列表
	Subtasks      []*TDocChunkSubtask `gorm:"foreignKey:task_id" json:"subtasks,omitempty"`                  // 子任务列表
	Doc           *TDoc               `gorm:"foreignKey:doc_id" json:"doc,omitempty"`                        // 所属文档
}

// TableName get sql table name.获取数据库表名
func (m *TDocChunkTask) TableName() string {
	return "t_doc_chunk_task"
}

// TDocChunkTaskColumns get sql column name.获取数据库列名
var TDocChunkTaskColumns = struct {
	ID           string
	DocID        string
	State        string
	Type         string
	Para         string
	SubtaskCount string
	FinishedAt   string
	CreateBy     string
	CreateDate   string
}{
	ID:           "id",
	DocID:        "doc_id",
	State:        "state",
	Type:         "type",
	Para:         "para",
	SubtaskCount: "subtask_count",
	FinishedAt:   "finished_at",
	CreateBy:     "create_by",
	CreateDate:   "create_date",
}
