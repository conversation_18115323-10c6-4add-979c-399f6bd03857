package model

import (
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TAssistantDoc [...]
type TAssistantDoc struct {
	DocID         uint64      `gorm:"primaryKey;column:doc_id" json:"docId"`             // 文档id
	AssistantID   uint64      `gorm:"primaryKey;column:assistant_id" json:"assistantId"` // assistant的id
	State         ai.DocState `gorm:"column:state;default:2" json:"state"`               // 1: 启用 2：停用 7:删除中
	IsShared      uint32      `gorm:"column:is_shared;default:1" json:"isShared"`        // 1: 否 2:是
	ChunkDetailID uint64      `gorm:"column:chunk_detail_id" json:"chunkDetailId"`       // 分段详情ID

	ChunkDetail *TDocChunkDetail `gorm:"foreignKey:chunk_detail_id" json:"chunkDetail"` // 分段详情
}

// TableName get sql table name.获取数据库表名
func (m *TAssistantDoc) TableName() string {
	return "t_assistant_doc"
}

// TAssistantDocColumns get sql column name.获取数据库列名
var TAssistantDocColumns = struct {
	DocID         string
	AssistantID   string
	State         string
	IsShared      string
	ChunkDetailID string
}{
	DocID:         "doc_id",
	AssistantID:   "assistant_id",
	State:         "state",
	IsShared:      "is_shared",
	ChunkDetailID: "chunk_detail_id",
}
