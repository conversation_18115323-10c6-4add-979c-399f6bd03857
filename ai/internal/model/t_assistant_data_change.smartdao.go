package model

import (
	"time"
)

// ChangeType 变化类型
type ChangeType int8

const (
	ChangeTypeDocOperation ChangeType = 1 // 文档操作（绑定/解绑）
	ChangeTypeNewQuestion  ChangeType = 2 // 新增提问
)

// TAssistantDataChange 助手数据变化记录表
type TAssistantDataChange struct {
	ID          uint64     `gorm:"primaryKey;column:id" json:"id"`
	AssistantID uint64     `gorm:"column:assistant_id;not null;index:idx_assistant_time" json:"assistantId"` // 助手ID
	ChangeType  ChangeType `gorm:"column:change_type;not null" json:"changeType"`                            // 变化类型：1-doc操作，2-提问
	DocID       *uint64    `gorm:"column:doc_id" json:"docId"`                                               // 文档ID
	MessageID   *uint64    `gorm:"column:message_id" json:"messageId"`                                       // 消息ID
	ChangeTime  time.Time  `gorm:"column:change_time;not null;index:idx_assistant_time" json:"changeTime"`   // 变化时间
	CreateDate  time.Time  `gorm:"column:create_date;autoCreateTime" json:"createDate"`                      // 创建时间
}

// TableName 表名
func (TAssistantDataChange) TableName() string {
	return "t_assistant_data_change"
}

// TAssistantDataChangeColumns 表列名
var TAssistantDataChangeColumns = struct {
	ID          string
	AssistantID string
	ChangeType  string
	DocID       string
	MessageID   string
	ChangeTime  string
	CreateDate  string
}{
	ID:          "id",
	AssistantID: "assistant_id",
	ChangeType:  "change_type",
	DocID:       "doc_id",
	MessageID:   "message_id",
	ChangeTime:  "change_time",
	CreateDate:  "create_date",
}
