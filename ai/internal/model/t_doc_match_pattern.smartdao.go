// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocMatchPattern [...]
type TDocMatchPattern struct {
	ID           uint64               `gorm:"primaryKey;column:id" json:"id"`
	DocID        uint64               `gorm:"column:doc_id;default:0" json:"docId"`               // 文档ID
	MatchPattern aipb.DocMatchPattern `gorm:"column:match_pattern;default:0" json:"matchPattern"` // 匹配模式
}

// TableName get sql table name.获取数据库表名
func (m *TDocMatchPattern) TableName() string {
	return "t_doc_match_pattern"
}

// TDocMatchPatternColumns get sql column name.获取数据库列名
var TDocMatchPatternColumns = struct {
	ID           string
	DocID        string
	MatchPattern string
}{
	ID:           "id",
	DocID:        "doc_id",
	MatchPattern: "match_pattern",
}
