package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TAssistantAllowlist [...]
type TAssistantAllowlist struct {
	ID          uint64                      `gorm:"primaryKey;column:id" json:"id"`                                 // 主键
	AssistantID uint64                      `gorm:"column:assistant_id;default:0" json:"assistantId"`               // 助手ID
	Type        aipb.AssistantAllowlistType `gorm:"column:type;default:0" json:"type"`                              // 类型：1手机号，2微信昵称
	Value       string                      `gorm:"column:value;default:''" json:"value"`                           // 值
	Value2      string                      `gorm:"column:value2;default:''" json:"value2"`                         // 值2
	CreateDate  time.Time                   `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TAssistantAllowlist) TableName() string {
	return "t_assistant_allowlist"
}

// TAssistantAllowlistColumns get sql column name.获取数据库列名
var TAssistantAllowlistColumns = struct {
	ID          string
	AssistantID string
	Type        string
	Value       string
	Value2      string
	CreateDate  string
}{
	ID:          "id",
	AssistantID: "assistant_id",
	Type:        "type",
	Value:       "value",
	Value2:      "value2",
	CreateDate:  "create_date",
}
