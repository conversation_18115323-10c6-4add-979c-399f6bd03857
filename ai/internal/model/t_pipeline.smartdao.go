// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TPipeline chat pipeline记录表
type TPipeline struct {
	ID         uint64    `gorm:"primaryKey;column:id" json:"id"`                                 // 自增id
	Command    string    `gorm:"column:command" json:"command"`                                  // 暗号
	State      int32     `gorm:"column:state;default:1" json:"state"`                            // 状态：0-禁用，1-启用
	UpdateDate time.Time `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"` // 更新时间
	CreateDate time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TPipeline) TableName() string {
	return "t_pipeline"
}

// TPipelineColumns get sql column name.获取数据库列名
var TPipelineColumns = struct {
	ID         string
	Command    string
	State      string
	UpdateDate string
	CreateDate string
}{
	ID:         "id",
	Command:    "command",
	State:      "state",
	UpdateDate: "update_date",
	CreateDate: "create_date",
}
