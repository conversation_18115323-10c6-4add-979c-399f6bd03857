// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocExternalSourceUser 外部数据源关联用户
type TDocExternalSourceUser struct {
	ID           uint64    `gorm:"primaryKey;column:id" json:"id"`
	AccessToken  string    `gorm:"column:access_token" json:"accessToken"`
	RefreshToken string    `gorm:"column:refresh_token;default:''" json:"refreshToken"`
	OpenID       string    `gorm:"column:open_id;default:''" json:"openId"`
	UnionID      string    `gorm:"column:union_id" json:"unionId"`
	Nick         string    `gorm:"column:nick;default:''" json:"nick"`
	Avatar       string    `gorm:"column:avatar;default:''" json:"avatar"`
	Source       string    `gorm:"column:source;default:''" json:"source"`
	CreateDate   time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"`
	UpdateDate   time.Time `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"`
	CreateBy     uint64    `gorm:"column:create_by;default:0" json:"createBy"` // tanlive用户 id
}

// TableName get sql table name.获取数据库表名
func (m *TDocExternalSourceUser) TableName() string {
	return "t_doc_external_source_user"
}

// TDocExternalSourceUserColumns get sql column name.获取数据库列名
var TDocExternalSourceUserColumns = struct {
	ID           string
	AccessToken  string
	RefreshToken string
	OpenID       string
	UnionID      string
	Nick         string
	Avatar       string
	Source       string
	CreateDate   string
	UpdateDate   string
	CreateBy     string
}{
	ID:           "id",
	AccessToken:  "access_token",
	RefreshToken: "refresh_token",
	OpenID:       "open_id",
	UnionID:      "union_id",
	Nick:         "nick",
	Avatar:       "avatar",
	Source:       "source",
	CreateDate:   "create_date",
	UpdateDate:   "update_date",
	CreateBy:     "create_by",
}
