package model

import (
	"encoding/json"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (m *TExportTask) ToPb() (*aipb.ExportTask, error) {
	var paths []string
	if m.Paths != nil {
		err := json.Unmarshal(m.Paths, &paths)
		if err != nil {
			return nil, err
		}
	}

	return &aipb.ExportTask{
		Id:             m.ID,
		State:          aipb.ExportTaskState(m.State),
		Type:           aipb.ExportTaskType(m.Type),
		OperationType:  aipb.TaskOperationType(m.OperationType),
		Url:            m.URL,
		CreateBy:       m.CreateBy,
		CreateDate:     timestamppb.New(m.CreateDate),
		LastUpdateDate: timestamppb.New(m.LastUpdateDate),
		ExtraInfo:      m.ExtraInfo,
		Paths:          paths,
		FilterSnapshot: m.FilterSnapshot,
		FieldsSnapshot: m.<PERSON>napshot,
	}, nil
}
