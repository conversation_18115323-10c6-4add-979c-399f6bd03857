// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocContributor [...]
type TDocContributor struct {
	ID              uint64 `gorm:"primaryKey;column:id" json:"id"`                            // 主键id
	DocID           uint64 `gorm:"column:doc_id" json:"docId"`                                // doc的id
	ContributorID   uint64 `gorm:"column:contributor_id;default:0" json:"contributorId"`      // 用户/团队id
	ContributorText string `gorm:"column:contributor_text;default:''" json:"contributorText"` // 自定义的贡献者，纯文本
	ContributorType uint32 `gorm:"column:contributor_type;default:0" json:"contributorType"`  // 贡献者类型 1:个人用户 2:团队用户 3:运营后台 4:自定义
	DeletedAt       gorm.DeletedAt     `gorm:"column:deleted_at" json:"deletedAt,omitempty"`                                            // 删除时间
}

// TableName get sql table name.获取数据库表名
func (m *TDocContributor) TableName() string {
	return "t_doc_contributor"
}

// TDocContributorColumns get sql column name.获取数据库列名
var TDocContributorColumns = struct {
	ID              string
	DocID           string
	ContributorID   string
	ContributorText string
	ContributorType string
}{
	ID:              "id",
	DocID:           "doc_id",
	ContributorID:   "contributor_id",
	ContributorText: "contributor_text",
	ContributorType: "contributor_type",
}
