// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocExtend 文件扩展表
type TDocExtend struct {
	DocID       uint64            `gorm:"primaryKey;column:doc_id" json:"docId"`
	ParseMode   ai.DocParseMode   `gorm:"column:parse_mode;default:1" json:"parseMode"`                     // 解析模式 1:智能解析 2:文件解析 3:图像解析 4:表格解析
	ParseUserID uint64            `gorm:"column:parse_user_id;default:0" json:"parseUserId"`                // 解析用户id
	UpdateDate  time.Time         `gorm:"column:update_date;default:1970-01-01 00:00:00" json:"updateDate"` // 更新时间
	ParseByType base.IdentityType `gorm:"column:parse_by_type;default:0" json:"parseByType"`                // 最近解析人类型 1团队 2个人 3运营账户
	ParseByUser uint64            `gorm:"column:parse_by_user;default:0" json:"parseByUser"`                // 解析用户的团队id
}

// TableName get sql table name.获取数据库表名
func (m *TDocExtend) TableName() string {
	return "t_doc_extend"
}

// TDocExtendColumns get sql column name.获取数据库列名
var TDocExtendColumns = struct {
	DocID       string
	ParseMode   string
	ParseUserID string
	UpdateDate  string
	ParseByType string
	ParseByUser string
}{
	DocID:       "doc_id",
	ParseMode:   "parse_mode",
	ParseUserID: "parse_user_id",
	UpdateDate:  "update_date",
	ParseByType: "parse_by_type",
	ParseByUser: "parse_by_user",
}
