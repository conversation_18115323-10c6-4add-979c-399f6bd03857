// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocEmbdding 文件解析缓存表
type TDocEmbdding struct {
	DocID        uint64         `gorm:"primaryKey;column:doc_id" json:"docId"`               // doc_id
	CollectionID uint64         `gorm:"primaryKey;column:collection_id" json:"collectionId"` // collection_id
	SegIDs []string `gorm:"column:seg_ids;serializer:json" json:"segIds"`          // 向量片段id
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at" json:"deletedAt"`                  // 软删除
	SegCount     int32          `gorm:"column:seg_count;default:0" json:"segCount"`          // 向量片段数量
}

// TableName get sql table name.获取数据库表名
func (m *TDocEmbdding) TableName() string {
	return "t_doc_embdding"
}

// TDocEmbddingColumns get sql column name.获取数据库列名
var TDocEmbddingColumns = struct {
	DocID        string
	CollectionID string
	SegIDs       string
	DeletedAt    string
	SegCount     string
}{
	DocID:        "doc_id",
	CollectionID: "collection_id",
	SegIDs:       "seg_ids",
	DeletedAt:    "deleted_at",
	SegCount:     "seg_count",
}
