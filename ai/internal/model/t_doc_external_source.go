package model

import (
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
)

type QueryType int

const (
	// 临时文件目录
	TmpTencentDoc = "./tmp_tencent_doc"
)

const (
	DefaultQuery QueryType = iota
	// 腾讯文档
	TencentDocQuery
	// 成果库
	AchievementQuery
	// 专家库查询
	ExpertQuery
)

// 任务状态常量
const (
	TaskStateProcessing int = 1 // 任务进行中
	TaskStateCompleted  int = 2 // 任务完成
	TaskStateFailed     int = 3 // 任务失败
)

// 外部数据源同步状态
type SyncStatus int

const (
	// 1 已同步
	Synced SyncStatus = iota + 1
	// 2 已删除
	Deleted
	// 3 有更新
	Updated
	// 4 人工
	Manual
	// 5 同步中
	Syncing
)

// 数据源类型
type SourceType uint32

const (
	// 1 腾讯文档
	TencentDoc SourceType = iota + 1
	// 2 绿技行
	GreenTech
)

// WithExternalSourceTenant 根据用户或者团队 id，生成ExternalSource的租户条件
func WithExternalSourceTenant(adminType uint32, accountId uint64, userId uint64) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if adminType == uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM) {
			db = db.Where("account_id = ? and admin_type = ?", accountId, adminType)
		} else if adminType == uint32(basepb.IdentityType_IDENTITY_TYPE_USER) {
			db = db.Where("account_user_id = ? and admin_type = ?", userId, adminType)
		}
		return db
	}
}
