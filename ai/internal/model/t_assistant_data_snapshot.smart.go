package model

import (
	"time"
)

// TAssistantDataSnapshot 助手数据快照表
type TAssistantDataSnapshot struct {
	ID            uint64    `gorm:"primaryKey;column:id" json:"id"`
	AssistantID   uint64    `gorm:"column:assistant_id;not null;index:idx_assistant_time" json:"assistantId"`   // 助手ID
	DocCount      uint64    `gorm:"column:doc_count;not null" json:"docCount"`                                  // 文档数量
	QuestionCount uint64    `gorm:"column:question_count;not null" json:"questionCount"`                        // 提问数量
	TotalCount    uint64    `gorm:"column:total_count;not null" json:"totalCount"`                              // 总数据量
	SnapshotTime  time.Time `gorm:"column:snapshot_time;not null;index:idx_assistant_time" json:"snapshotTime"` // 快照时间
	CreateDate    time.Time `gorm:"column:create_date;autoCreateTime" json:"createDate"`                        // 创建时间
	UpdateDate    time.Time `gorm:"column:update_date;autoUpdateTime" json:"updateDate"`                        // 更新时间
}

// TableName 表名
func (TAssistantDataSnapshot) TableName() string {
	return "t_assistant_data_snapshot"
}

// TAssistantDataSnapshotColumns 表列名
var TAssistantDataSnapshotColumns = struct {
	ID            string
	AssistantID   string
	DocCount      string
	QuestionCount string
	TotalCount    string
	SnapshotTime  string
	CreateDate    string
	UpdateDate    string
}{
	ID:            "id",
	AssistantID:   "assistant_id",
	DocCount:      "doc_count",
	QuestionCount: "question_count",
	TotalCount:    "total_count",
	SnapshotTime:  "snapshot_time",
	CreateDate:    "create_date",
	UpdateDate:    "update_date",
}
