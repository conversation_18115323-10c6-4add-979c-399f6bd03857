// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatMessageDoc [...]
type TChatMessageDoc struct {
	MessageID   uint64 `gorm:"column:message_id" json:"messageId"`     // 消息id
	RagFilename string `gorm:"column:rag_filename" json:"ragFilename"` // 文档的文件名
}

// TableName get sql table name.获取数据库表名
func (m *TChatMessageDoc) TableName() string {
	return "t_chat_message_doc"
}

// TChatMessageDocColumns get sql column name.获取数据库列名
var TChatMessageDocColumns = struct {
	MessageID   string
	RagFilename string
}{
	MessageID:   "message_id",
	RagFilename: "rag_filename",
}
