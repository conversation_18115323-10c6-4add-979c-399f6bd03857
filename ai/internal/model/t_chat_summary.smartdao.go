// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatSummary VIEW
type TChatSummary struct {
	ID          uint64         `gorm:"column:id;default:0" json:"id"`                                    // 自增id
	Title       string         `gorm:"column:title;default:''" json:"title"`                             // 会话标题
	CreateDate  time.Time      `gorm:"column:create_date;default:0000-00-00 00:00:00" json:"createDate"` // 会话创建时间
	FinishDate  *time.Time     `gorm:"column:finish_date" json:"finishDate"`                             // 会话结束时间
	CreateBy    uint64         `gorm:"column:create_by" json:"createBy"`                                 // 创建人
	UpdateBy    uint64         `gorm:"column:update_by" json:"updateBy"`                                 // 最后更新人
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at" json:"deletedAt"`                               // 删除时间
	UpdateDate  time.Time     `gorm:"column:update_date" json:"updateDate"`                             // 更新时间
	Type        int32          `gorm:"column:type;default:1" json:"type"`                                // 类型 1 web 2 微信
	AppID       string         `gorm:"column:app_id;default:''" json:"appId"`                            // 对应微信union_id
	Image       string         `gorm:"column:image;default:''" json:"image"`                             // 对话用户头像（非tanlive本地头像）
	Nickname    string         `gorm:"column:nickname;default:''" json:"nickname"`                       // 对话用户昵称
	Origin      int32          `gorm:"column:origin;default:1" json:"origin"`                            // 对话来源 1 tanlive 2 无毒
	SupportType int32          `gorm:"column:support_type;default:1" json:"supportType"`                 // 聊天客服类型 1 ai 2 人工服务
	AssistantID uint64         `gorm:"column:assistant_id;default:0" json:"assistantId"`                 // 助手id
	QuestionCnt int64          `gorm:"column:question_cnt;default:0" json:"questionCnt"`
	DocHits     float64        `gorm:"column:doc_hits;default:0.0000" json:"docHits"`
	AvgDuration float64        `gorm:"column:avg_duration" json:"avgDuration"`
	RatingScale float64        `gorm:"column:rating_scale" json:"ratingScale"`
	Labels              []*TObjectLabel `gorm:"foreignKey:object_id" json:"labels,omitempty"`
}

// TableName get sql table name.获取数据库表名
func (m *TChatSummary) TableName() string {
	return "v_chat_summary"
}

// VChatSummaryColumns get sql column name.获取数据库列名
var VChatSummaryColumns = struct {
	ID          string
	Title       string
	CreateDate  string
	FinishDate  string
	CreateBy    string
	UpdateBy    string
	DeletedAt   string
	UpdateDate  string
	Type        string
	AppID       string
	Image       string
	Nickname    string
	Origin      string
	SupportType string
	AssistantID string
	QuestionCnt string
	DocHits     string
	AvgDuration string
	RatingScale string
	Labels              string
}{
	ID:          "id",
	Title:       "title",
	CreateDate:  "create_date",
	FinishDate:  "finish_date",
	CreateBy:    "create_by",
	UpdateBy:    "update_by",
	DeletedAt:   "deleted_at",
	UpdateDate:  "update_date",
	Type:        "type",
	AppID:       "app_id",
	Image:       "image",
	Nickname:    "nickname",
	Origin:      "origin",
	SupportType: "support_type",
	AssistantID: "assistant_id",
	QuestionCnt: "question_cnt",
	DocHits:     "doc_hits",
	AvgDuration: "avg_duration",
	RatingScale: "rating_scale",
	Labels:              "labels",
}
