package model

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm/clause"
)

// ParseDocFromCache 从数据库中获取文件解析内容
// 没有会返回error
func ParseDocFromCache(ctx context.Context, md5 string) (aipb.DocParseMode, string, error) {
	row, err := NewQuery[TDocParseCache](ctx).FindBy("md5 = ?", md5)
	if err != nil {
		return 0, "", err
	}
	return row.ParseMode, row.Text, nil
}

// SetDocExtend 设置解析模式
func SetDocExtend(ctx context.Context, extend *TDocExtend, parseMode aipb.DocParseMode) {
	if extend == nil || extend.DocID == 0 {
		return
	}
	if parseMode != aipb.DocParseMode_DOC_PARSE_MODE_UNSPECIFIED {
		extend.ParseMode = parseMode
	}

	if err := NewQuery[TDocExtend](ctx).DB().Clauses(clause.OnConflict{
		DoUpdates: clause.AssignmentColumns(
			[]string{"parse_mode", "update_date", "parse_user_id", "parse_by_type", "parse_by_user"}),
		Columns: []clause.Column{{Name: "doc_id"}},
	}).Create(extend).Error; err != nil {
		log.WithContext(ctx).Errorw("failed to set extend after prase doc", "err", err, "extend", extend)
	}
}

// SetDocParseCache 设置解析存储
func SetDocParseCache(ctx context.Context, md5 string, doc string, docId uint64, parseMode aipb.DocParseMode) error {
	return NewQuery[TDocParseCache](ctx).DB().Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "md5"}},
		UpdateAll: true,
	}).Create(&TDocParseCache{
		Md5:       md5,
		Text:      doc,
		DocID:     docId,
		ParseMode: parseMode,
	}).Error
}
