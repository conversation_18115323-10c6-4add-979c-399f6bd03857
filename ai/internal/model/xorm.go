package model

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
)

const ConnNameV1 = "v1"

const ConnNameV2 = "v2"
const ConnNameV1Mgmt = "v1_op"
const ConnNameBI = "metric"

// NewQuery 新建查询
func NewQuery[Model any](ctx context.Context) *xorm.Query[Model] {
	return xorm.NewQueryUsing[Model](ctx, ConnNameV2)
}

// NewConnectionV1 新建connection
func NewConnectionV1(ctx context.Context) *gorm.DB {
	db := xorm.Use(ctx, ConnNameV1)
	return db
}

// NewConnection 新建connection
func NewConnection(ctx context.Context) *gorm.DB {
	db := xorm.Use(ctx, ConnNameV2)
	return db
}

// NewConnectionV1Mgmt 新建connection
func NewConnectionV1Mgmt(ctx context.Context) *gorm.DB {
	db := xorm.Use(ctx, ConnNameV1Mgmt)
	return db
}

// Transaction 事务
func Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	return xorm.Use(ctx, ConnNameV2).Transaction(f)
}


// ParseOrder 处理排序
func ParseOrder[Model any](orderBy []*basepb.OrderBy, whitelist map[string]bool,
	query *xorm.Query[Model], parse func(string) string) {
	if len(orderBy) == 0 {
		return
	}

	for _, v := range orderBy {
		if whitelist[v.Column] {
			if parse != nil {
				query.OrderBy(parse(v.Column), v.Desc)
			} else {
				query.OrderBy(v.Column, v.Desc)
			}
		}
	}
}