// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatMessageThink [...]
type TChatMessageThink struct {
	MessageID uint64     `gorm:"column:message_id" json:"messageId"`        // 消息id
	Content   string     `gorm:"column:content" json:"content"`             // 思考内容
	EndTime   *time.Time `gorm:"column:end_time" json:"endTime"`            // 结束时间
	StartTime *time.Time `gorm:"column:start_time" json:"startTime"`        // 开始时间
	Duration  uint32     `gorm:"column:duration;default:0" json:"duration"` // 时长（秒）
}

// TableName get sql table name.获取数据库表名
func (m *TChatMessageThink) TableName() string {
	return "t_chat_message_think"
}

// TChatMessageThinkColumns get sql column name.获取数据库列名
var TChatMessageThinkColumns = struct {
	MessageID string
	Content   string
	EndTime   string
	StartTime string
	Duration  string
}{
	MessageID: "message_id",
	Content:   "content",
	EndTime:   "end_time",
	StartTime: "start_time",
	Duration:  "duration",
}
