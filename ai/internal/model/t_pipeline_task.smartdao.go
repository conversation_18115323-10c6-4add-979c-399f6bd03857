// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TPipelineTask agent配置表
type TPipelineTask struct {
	ID            uint64         `gorm:"primaryKey;column:id" json:"id"`                      // 自增id
	PipelineID    uint64         `gorm:"column:pipeline_id" json:"pipelineId"`                // 流程Id
	PromptPrefix  string         `gorm:"column:prompt_prefix" json:"promptPrefix"` // prompt
	PreTaskID     uint64         `gorm:"column:pre_task_id" json:"preTaskId"`                 // 助手id
	FetchType     int32          `gorm:"column:fetch_type;default:1" json:"fetchType"`
	DefaultText   string         `gorm:"column:default_text" json:"defaultText"`                         // 默认回复
	Prompt        string         `gorm:"column:prompt" json:"prompt"`                                    // 固定问题
	MatchConfig   *aipb.PipelineTaskMatchConfig `gorm:"column:match_config;serializer:json" json:"match_config,omitempty"`                         // 匹配配置
	SuggestConfig *aipb.AssistantAskSuggestionConfig `gorm:"column:suggest_config;serializer:json" json:"suggest_config,omitempty"` // 问题建议配置
	UpdateDate    time.Time      `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"` // 更新时间
	CreateDate    time.Time      `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	TaskOrder     int32          `gorm:"column:task_order;default:1" json:"taskOrder"`                   // 顺序
	State         int32          `gorm:"column:state;default:1" json:"state"`                            // 状态：0-禁用，1-启用
	VisionModel   string         `gorm:"column:vision_model" json:"visionModel"`	// 多模态模型
}

// TableName get sql table name.获取数据库表名
func (m *TPipelineTask) TableName() string {
	return "t_pipeline_task"
}

// TPipelineTaskColumns get sql column name.获取数据库列名
var TPipelineTaskColumns = struct {
	ID            string
	PipelineID    string
	PromptPrefix  string
	PreTaskID     string
	FetchType     string
	DefaultText   string
	Prompt        string
	MatchConfig   string
	SuggestConfig string
	UpdateDate    string
	CreateDate    string
	TaskOrder     string
	State         string
}{
	ID:            "id",
	PipelineID:    "pipeline_id",
	PromptPrefix:  "prompt_prefix",
	PreTaskID:     "pre_task_id",
	FetchType:     "fetch_type",
	DefaultText:   "default_text",
	Prompt:        "prompt",
	MatchConfig:   "match_config",
	SuggestConfig: "suggest_config",
	UpdateDate:    "update_date",
	CreateDate:    "create_date",
	TaskOrder:     "task_order",
	State:         "state",
}
