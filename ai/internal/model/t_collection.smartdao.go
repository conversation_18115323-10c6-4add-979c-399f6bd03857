// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TCollection [...]
type TCollection struct {
	ID      uint64 `gorm:"primaryKey;column:id" json:"id,omitempty"`            // 自增id
	Name    string `gorm:"column:name;default:''" json:"name,omitempty"`        // 前端用于显示的名称
	RagName string `gorm:"column:rag_name;default:''" json:"ragName,omitempty"` // 向量库的collection名称，英文用en_开头
	VdbType string `gorm:"column:vdb_type;default:''" json:"vdbType,omitempty"`
	Lang    string `gorm:"column:lang;default:''" json:"lang,omitempty"`        // 向量嵌入语言 zh:中文 en:英文
	EsIns   string `gorm:"column:es_ins;default:''" json:"esIns,omitempty"`     // es实例
	EmbUpdateDate time.Time `gorm:"column:emb_update_date;default:1970-01-01 00:00:00" json:"embUpdateDate,omitempty"`
	Assistants []*TAssistant `gorm:"many2many:t_assistant_collection;joinForeignKey:collection_id;joinReferences:assistant_id" json:"assistants"`
}

// TableName get sql table name.获取数据库表名
func (m *TCollection) TableName() string {
	return "t_collection"
}

// TCollectionColumns get sql column name.获取数据库列名
var TCollectionColumns = struct {
	ID            string
	Name          string
	RagName       string
	VdbType       string
	Lang          string
	EsIns         string
	EmbUpdateDate string
}{
	ID:            "id",
	Name:          "name",
	RagName:       "rag_name",
	VdbType:       "vdb_type",
	Lang:          "lang",
	EsIns:         "es_ins",
	EmbUpdateDate: "emb_update_date",
}
