package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// TypedReferencesToFeedbackReferences ...
func TypedReferencesToFeedbackReferences(pbs []*aipb.TypedReference, createBy uint64) []*TFeedbackReference {
	models := make([]*TFeedbackReference, 0, len(pbs))
	for _, pb := range pbs {
		m := &TFeedbackReference{}
		m.FromTypedReference(pb, createBy)
		models = append(models, m)
	}
	return models
}

// FromTypedReference ...
func (m *TFeedbackReference) FromTypedReference(pb *aipb.TypedReference, createBy uint64) {
	if pb == nil {
		return
	}

	switch reference := pb.Reference.(type) {
	case *aipb.TypedReference_Url:
		m.Type = aipb.ReferenceType_REFERENCE_TYPE_URL
		m.URL = reference.Url.Url
	case *aipb.TypedReference_Text:
		m.Type = aipb.ReferenceType_REFERENCE_TYPE_TEXT
		m.Text = reference.Text.Text
	case *aipb.TypedReference_File:
		m.Type = aipb.ReferenceType_REFERENCE_TYPE_FILE
		m.FilePath = reference.File.FilePath
		m.FileName = reference.File.FileName
	default:
		return
	}

	m.CreateBy = createBy
	m.UpdateBy = createBy
	m.CreateDate = time.Now()
	m.UpdateDate = time.Now()
}

// ToPb ...
func (m *TFeedbackReference) ToPb() *aipb.FeedbackReference {
	if m == nil {
		return nil
	}
	pb := &aipb.FeedbackReference{}
	pb.Id = m.ID
	pb.FeedbackId = m.FeedbackID
	pb.Type = m.Type
	pb.Url = m.URL
	pb.Text = m.Text
	pb.FilePath = m.FilePath
	pb.FileName = m.FileName
	pb.CreateBy = m.CreateBy
	pb.CreateDate = toTimestamppb(m.CreateDate)
	pb.UpdateBy = m.UpdateBy
	pb.UpdateDate = toTimestamppb(m.UpdateDate)
	return pb
}

// TFeedbackReferences 参考文献列表
type TFeedbackReferences []*TFeedbackReference

// ToPb 协议转换
func (set TFeedbackReferences) ToPb() []*aipb.FeedbackReference {
	pbs := make([]*aipb.FeedbackReference, 0, len(set))
	for _, m := range set {
		pbs = append(pbs, m.ToPb())
	}
	return pbs
}
