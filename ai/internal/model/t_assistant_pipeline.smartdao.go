// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TAssistantPipeline chat pipeline记录表
type TAssistantPipeline struct {
	AssistantID uint64    `gorm:"column:assistant_id" json:"assistantId"` // 助手Id
	PipelineID  uint64    `gorm:"column:pipeline_id" json:"pipelineId"`   // pipeline Id
	State       int32     `gorm:"column:state;default:1" json:"state"`    // 状态：0-禁用，1-启用
	UpdateDate  time.Time `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"`
	CreateDate  time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"`
}

// TableName get sql table name.获取数据库表名
func (m *TAssistantPipeline) TableName() string {
	return "t_assistant_pipeline"
}

// TAssistantPipelineColumns get sql column name.获取数据库列名
var TAssistantPipelineColumns = struct {
	AssistantID string
	PipelineID  string
	State       string
	UpdateDate  string
	CreateDate  string
}{
	AssistantID: "assistant_id",
	PipelineID:  "pipeline_id",
	State:       "state",
	UpdateDate:  "update_date",
	CreateDate:  "create_date",
}
