package model

import (
	"database/sql"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
)

// 转换为timestamppb.Timestamp
func toTimestamppb(t time.Time) *timestamppb.Timestamp {
	if t.Unix() == 0 {
		return nil
	}
	return timestamppb.New(t)
}

// 转换为timestamppb.Timestamp
func nullTimeToTimestamppb(t sql.NullTime) *timestamppb.Timestamp {
	if t.Valid {
		return timestamppb.New(t.Time)
	}
	return nil
}
