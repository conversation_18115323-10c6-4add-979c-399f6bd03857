// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocBatchTask 文件批量执行任务表
type TDocBatchTask struct {
	ID         uint64              `gorm:"primaryKey;column:id" json:"id"` // 自增id
	QueryID    uint64              `gorm:"column:query_id" json:"queryId"`
	Args       datatypes.JSON      `gorm:"column:args;" json:"args"`                                       // 任务参数json
	TraceID    string              `gorm:"column:trace_id;default:''" json:"traceId"`                      // 记录日志
	TaskType   ai.DocBatchTaskType `gorm:"column:task_type;default:1" json:"taskType"`                     // 1:复制 2:删除 3:启/停用 4:更新 5:重新解析
	Status     DocBatchTaskType    `gorm:"column:status;default:0" json:"status"`                          // 任务状态 0:初始化；1:执行中；2:执行完成；3:执行失败
	CreateBy   *ai.Operator        `gorm:"column:create_by;serializer:json" json:"createBy,omitempty"`     // 创建人信息
	CreateDate time.Time           `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TDocBatchTask) TableName() string {
	return "t_doc_batch_task"
}

// TDocBatchTaskColumns get sql column name.获取数据库列名
var TDocBatchTaskColumns = struct {
	ID         string
	QueryID    string
	Args       string
	TraceID    string
	TaskType   string
	CreateBy   string
	CreateDate string
	Status     string
}{
	ID:         "id",
	QueryID:    "query_id",
	Args:       "args",
	TraceID:    "trace_id",
	TaskType:   "task_type",
	CreateBy:   "create_by",
	CreateDate: "create_date",
	Status:     "status",
}
