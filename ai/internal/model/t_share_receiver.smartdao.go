// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TShareReceiver 接收方分享设置
type TShareReceiver struct {
	ID               uint64                   `gorm:"primaryKey;column:id" json:"id"`                                                // 自增id
	AdminAssistantID uint64                   `gorm:"column:admin_assistant_id" json:"adminAssistantId"`                             // 管理的助手id，为0时代表当前用户或团队
	ReceiverState    aipb.DocShareAcceptState `gorm:"column:receiver_state;default:2" json:"receiverState"`                          // 接收他人共享的知识 1 接收 2不接收
	OtherState       aipb.DocShareState       `gorm:"column:other_state;default:2" json:"otherState"`                                // 在t_share_receiver_user表没配置的知识默认状态  1: 启用 2: 停用 3:不接收
	AdminType        basepb.IdentityType      `gorm:"column:admin_type" json:"adminType"`                                            // 创建人的类型 1: 前台用户 2: 前台团队
	CreateBy         uint64                   `gorm:"column:create_by;default:0" json:"createBy"`                                    // 创建人id
	UpdateBy         uint64                   `gorm:"column:update_by;default:0" json:"updateBy"`                                    // 最近更新人id
	UpdateDate       time.Time                `gorm:"column:update_date;autoUpdateTime;default:CURRENT_TIMESTAMP" json:"updateDate"` // 最近更新时间
	CreateDate       time.Time                `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间

	ReceiverUserDetail []*TShareReceiverUser `gorm:"foreignKey:ReceiverID;references:ID" json:"receiverUserDetail"`
}

// TableName get sql table name.获取数据库表名
func (m *TShareReceiver) TableName() string {
	return "t_share_receiver"
}

// TShareReceiverColumns get sql column name.获取数据库列名
var TShareReceiverColumns = struct {
	ID               string
	AdminAssistantID string
	ReceiverState    string
	OtherState       string
	AdminType        string
	CreateBy         string
	UpdateBy         string
	UpdateDate       string
	CreateDate       string
}{
	ID:               "id",
	AdminAssistantID: "admin_assistant_id",
	ReceiverState:    "receiver_state",
	OtherState:       "other_state",
	AdminType:        "admin_type",
	CreateBy:         "create_by",
	UpdateBy:         "update_by",
	UpdateDate:       "update_date",
	CreateDate:       "create_date",
}
