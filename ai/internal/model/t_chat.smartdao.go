// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChat [...]
type TChat struct {
	ID                  uint64          `gorm:"primaryKey;column:id" json:"id"`                                                // 自增id
	Title               string          `gorm:"column:title;default:''" json:"title"`                                          // 会话标题
	CreateDate          time.Time       `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"` // 会话创建时间
	FinishDate          *time.Time      `gorm:"column:finish_date" json:"finishDate"`                                          // 会话结束时间
	CreateBy            uint64          `gorm:"column:create_by" json:"createBy"`                                              // 创建人
	UpdateBy            uint64          `gorm:"column:update_by" json:"updateBy"`                                              // 最后更新人
	DeletedAt           gorm.DeletedAt  `gorm:"column:deleted_at" json:"deletedAt"`                                            // 删除时间
	UpdateDate          time.Time       `gorm:"column:update_date;autoUpdateTime;default:CURRENT_TIMESTAMP" json:"updateDate"` // 会话创建时间
	Type                int32           `gorm:"column:type;default:1" json:"type"`                                             // 类型 1 web 2 微信
	AppID               string          `gorm:"column:app_id;default:''" json:"appId"`                                         // 对应微信union_id
	ExternalUserID      string          `gorm:"column:external_user_id;default:''" json:"externalUserId"`                      // 对应微信客服用户的open_id
	Image               string          `gorm:"column:image;default:''" json:"image"`                                          // 对话用户头像（非tanlive本地头像）
	Nickname            string          `gorm:"column:nickname;default:''" json:"nickname"`                                    // 对话用户昵称
	Origin              int32           `gorm:"column:origin;default:1" json:"origin"`                                         // 对话来源 1 tanlive 2 无毒
	AssistantID         uint64          `gorm:"column:assistant_id;default:0" json:"assistantId"`                              // 助手id
	LiveAgentName       string          `gorm:"column:live_agent_name;default:''" json:"liveAgentName"`                        // 人工坐席
	SupportType         int32           `gorm:"column:support_type;default:1" json:"supportType"`                              // 聊天客服类型 1 ai 2 人工服务
	QuestionCnt         uint32          `gorm:"->;column:question_cnt;default:0" json:"questionCnt"`                           // 问题数量，只用于查询
	Labels              []*TObjectLabel `gorm:"foreignKey:object_id" json:"labels,omitempty"`
	DefaultAnswerRecord uint32          `gorm:"column:default_answer_record;default:0" json:"defaultAnswerRecord"` // 转人工 暗号发送次数
	RegionCode          string          `gorm:"column:region_code;default:''" json:"regionCode"`   // 用户所在国家或地区编码
	IsManual            int32           `gorm:"column:is_manual;default:1" json:"isManual"`                        // 是否转人工服务，1-否，2-是

	AvgDuration     float32 `gorm:"->;column:avg_duration;default:0" json:"avgDuration"`          // 平均持续时间，查询时聚合
	DocHits         float32 `gorm:"->;column:doc_hits;default:0" json:"docHits"`                  // 文档命中率，查询时聚合
	RatingScale     float32 `gorm:"->;column:rating_scale;default:0" json:"ratingScale"`          // 自定义评分平均值，查询时聚合
	RejectJobResult uint32  `gorm:"->;column:reject_job_result;default:0" json:"rejectJobResult"` // 审核结果
	IsShared        bool    `gorm:"column:is_shared" json:"IsShared,omitempty"`                                        // 是否为分享会话：0否|1是
}

// TableName get sql table name.获取数据库表名
func (m *TChat) TableName() string {
	return "t_chat"
}

// TChatColumns get sql column name.获取数据库列名
var TChatColumns = struct {
	ID                  string
	Title               string
	CreateDate          string
	FinishDate          string
	CreateBy            string
	UpdateBy            string
	DeletedAt           string
	UpdateDate          string
	Type                string
	AppID               string
	ExternalUserID      string
	Image               string
	Nickname            string
	Origin              string
	Labels              string
	AssistantID         string
	LiveAgentName       string
	SupportType         string
	DefaultAnswerRecord string
	RegionCode          string
	IsManual            string
}{
	ID:                  "id",
	Title:               "title",
	CreateDate:          "create_date",
	FinishDate:          "finish_date",
	CreateBy:            "create_by",
	UpdateBy:            "update_by",
	DeletedAt:           "deleted_at",
	UpdateDate:          "update_date",
	Type:                "type",
	AppID:               "app_id",
	ExternalUserID:      "external_user_id",
	Image:               "image",
	Nickname:            "nickname",
	Origin:              "origin",
	Labels:              "labels",
	AssistantID:         "assistant_id",
	LiveAgentName:       "live_agent_name",
	SupportType:         "support_type",
	DefaultAnswerRecord: "default_answer_record",
	RegionCode:          "region_code",
	IsManual:            "is_manual",
}
