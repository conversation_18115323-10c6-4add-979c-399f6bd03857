// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatSendRecord [...]
type TChatSendRecord struct {
	ID          uint64                 `gorm:"primaryKey;column:id" json:"id"`                                 // 自增id
	MessageID   uint64                 `gorm:"column:message_id" json:"messageId"`                             // message_id
	Content     string                 `gorm:"column:content" json:"content"`                                  // 发送的内容
	ChatID      uint64                 `gorm:"column:chat_id" json:"chatId"`                                   // 会话id
	State       int32                  `gorm:"column:state;default:1" json:"state"`                            // 状态 1未发送 2已发送 3发送异常
	Type        ai.AiRecordType        `gorm:"column:type;default:1" json:"type"`                              // 1 用户询问 2 助手回答-text 3 助手回答-menu 4 助手回答建议问题菜单消息
	UUID        string                 `gorm:"column:uuid;default:''" json:"uuid"`                             // 记录第三方uuid
	Info        string                 `gorm:"column:info" json:"info"`                                        // 发送记录信息
	CreateDate  time.Time              `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	SendDate    time.Time              `gorm:"column:send_date;default:1970-01-01 00:00:00" json:"sendDate"`   // 发送时间时间
	SendType    uint32                 `gorm:"column:send_type;default:0" json:"sendType"`                     // 0 不追加继续回答 1 追加继续回答
	MessageType ai.AiRecordMessageType `gorm:"column:message_type;default:1" json:"messageType"`               // 消息类型 1 常规消息 2 重复回答 3 隐藏的消息
	ExtraID     string                 `gorm:"column:extra_id;default:''" json:"extraId"`                      // 备用id，用于记录第三方发送的过程中需要记录的id
	ExtraInfo   string                 `gorm:"column:extra_info" json:"extraInfo"`                             // 备用记录信息，用于记录第三方发送的过程中需要记录的信息
}

// TableName get sql table name.获取数据库表名
func (m *TChatSendRecord) TableName() string {
	return "t_chat_send_record"
}

// TChatSendRecordColumns get sql column name.获取数据库列名
var TChatSendRecordColumns = struct {
	ID          string
	MessageID   string
	Content     string
	ChatID      string
	State       string
	Type        string
	UUID        string
	Info        string
	CreateDate  string
	SendDate    string
	SendType    string
	MessageType string
	ExtraID     string
	ExtraInfo   string
}{
	ID:          "id",
	MessageID:   "message_id",
	Content:     "content",
	ChatID:      "chat_id",
	State:       "state",
	Type:        "type",
	UUID:        "uuid",
	Info:        "info",
	CreateDate:  "create_date",
	SendDate:    "send_date",
	SendType:    "send_type",
	MessageType: "message_type",
	ExtraID:     "extra_id",
	ExtraInfo:   "extra_info",
}
