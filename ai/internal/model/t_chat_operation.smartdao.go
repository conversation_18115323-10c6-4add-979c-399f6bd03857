// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatOperation 用户问答操作日志表
type TChatOperation struct {
	ID              uint64         `gorm:"primaryKey;column:id" json:"id"`                                 // 主键ID
	ChatID          uint64         `gorm:"column:chat_id" json:"chatId"`                                   // 所属会话ID
	QuestionID      uint64         `gorm:"column:question_id" json:"questionId"`                           // 问题ID
	OperationType   uint32         `gorm:"column:operation_type;default:1" json:"operationType"`           // 操作类型：1开始回答 1停止回答 2重新回答 3继续回答
	HashID          string         `gorm:"column:hash_id" json:"hashId"`                                   // 问答哈希(SHA256)
	StopText        string         `gorm:"column:stop_text" json:"stopText"`                               // 停止时的文本内容
	StopThink       string         `gorm:"column:stop_think" json:"stopThink"`                             // 停止时的思考内容
	StopChunkState  int32          `gorm:"column:stop_chunk_state;default:0" json:"stopChunkState"`        // 停止时的chunk状态
	OperationParams datatypes.JSON `gorm:"column:operation_params" json:"operationParams"`                 // 操作附加参数
	CreateDate      time.Time      `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	MessageID       uint64         `gorm:"column:message_id;default:0" json:"messageId"`                   // 消息ID
}

// TableName get sql table name.获取数据库表名
func (m *TChatOperation) TableName() string {
	return "t_chat_operation"
}

// TChatOperationColumns get sql column name.获取数据库列名
var TChatOperationColumns = struct {
	ID              string
	ChatID          string
	QuestionID      string
	OperationType   string
	HashID          string
	StopText        string
	StopThink       string
	StopChunkState  string
	OperationParams string
	CreateDate      string
	MessageID       string
}{
	ID:              "id",
	ChatID:          "chat_id",
	QuestionID:      "question_id",
	OperationType:   "operation_type",
	HashID:          "hash_id",
	StopText:        "stop_text",
	StopThink:       "stop_think",
	StopChunkState:  "stop_chunk_state",
	OperationParams: "operation_params",
	CreateDate:      "create_date",
	MessageID:       "message_id",
}
