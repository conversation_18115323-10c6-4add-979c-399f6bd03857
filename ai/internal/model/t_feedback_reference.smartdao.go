package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TFeedbackReference [...]
type TFeedbackReference struct {
	ID         uint64             `gorm:"primaryKey;column:id" json:"id"`
	FeedbackID uint64             `gorm:"column:feedback_id;default:0" json:"feedbackId"`                 // 反馈ID
	Type       aipb.ReferenceType `gorm:"column:type;default:0" json:"type"`                              // 类型：1. URL，2. 文本，3. 文件
	URL        string             `gorm:"column:url;default:''" json:"url"`                               // URL
	Text       string             `gorm:"column:text;default:''" json:"text"`                             // 文本
	FilePath   string             `gorm:"column:file_path;default:''" json:"filePath"`                    // 文件路径
	FileName   string             `gorm:"column:file_name;default:''" json:"fileName"`                    // 文件名
	CreateBy   uint64             `gorm:"column:create_by;default:0" json:"createBy"`                     // 创建人
	CreateDate time.Time          `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	UpdateBy   uint64             `gorm:"column:update_by" json:"updateBy"`                               // 更新人
	UpdateDate time.Time          `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"` // 创建人
}

// TableName get sql table name.获取数据库表名
func (m *TFeedbackReference) TableName() string {
	return "t_feedback_reference"
}

// TFeedbackReferenceColumns get sql column name.获取数据库列名
var TFeedbackReferenceColumns = struct {
	ID         string
	FeedbackID string
	Type       string
	URL        string
	Text       string
	FilePath   string
	FileName   string
	CreateBy   string
	CreateDate string
	UpdateBy   string
	UpdateDate string
}{
	ID:         "id",
	FeedbackID: "feedback_id",
	Type:       "type",
	URL:        "url",
	Text:       "text",
	FilePath:   "file_path",
	FileName:   "file_name",
	CreateBy:   "create_by",
	CreateDate: "create_date",
	UpdateBy:   "update_by",
	UpdateDate: "update_date",
}
