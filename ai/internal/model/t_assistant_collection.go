package model

import (
	"context"
	"fmt"
)

// GetAssistantEmbeddingModel 获取助手对应的嵌入模型
func GetAssistantEmbeddingModel(ctx context.Context, assistantID uint64) (string, error) {
	var collection TCollection

	err := NewQuery[TCollection](ctx).DB().
		Select("t_collection.lang").
		Joins("JOIN t_assistant_collection ON t_collection.id = t_assistant_collection.collection_id").
		Where("t_assistant_collection.assistant_id = ?", assistantID).
		Limit(1).
		Scan(&collection).Error

	if err != nil {
		return "", fmt.Errorf("获取助手嵌入模型失败: %w", err)
	}

	// 缺省的默认是 zh
	if collection.Lang == "" {
		return "zh", nil
	}

	return collection.Lang, nil
}
