// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatMessageCollection [...]
type TChatMessageCollection struct {
	MessageID   uint64     `gorm:"column:message_id" json:"messageId"`               // 消息id
	Content     string     `gorm:"column:content" json:"content"`                    // search_collection列表
	EndTime     *time.Time `gorm:"column:end_time" json:"endTime"`                   // 结束时间
	StartTime   *time.Time `gorm:"column:start_time" json:"startTime"`               // 开始时间
	Duration    uint32     `gorm:"column:duration;default:0" json:"duration"`        // 时长（秒）
	ShowRelated int32      `gorm:"column:show_related;default:1" json:"showRelated"` // 是否显示关联关系
	CleanChunks bool      `gorm:"column:clean_chunks;type:tinyint(4);;default:0" json:"cleanChunks"`
	CreateDate         time.Time      `gorm:"column:create_date;default:CURRENT_TIMESTAMP;not null" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TChatMessageCollection) TableName() string {
	return "t_chat_message_collection"
}

// TChatMessageCollectionColumns get sql column name.获取数据库列名
var TChatMessageCollectionColumns = struct {
	MessageID   string
	Content     string
	EndTime     string
	StartTime   string
	Duration    string
	ShowRelated string
	CleanChunks string
}{
	MessageID:   "message_id",
	Content:     "content",
	EndTime:     "end_time",
	StartTime:   "start_time",
	Duration:    "duration",
	ShowRelated: "show_related",
	CleanChunks: "clean_chunks",
}
