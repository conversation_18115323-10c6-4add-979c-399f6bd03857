package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// ToPb 协议转换
func (m *TFeedback) ToPb() *aipb.Feedback {
	if m == nil {
		return nil
	}
	pb := &aipb.Feedback{}
	pb.Id = m.ID
	pb.Question = m.Question
	pb.Answer = m.Answer
	pb.State = m.State
	pb.CreateDate = toTimestamppb(m.CreateDate)
	pb.UpdateDate = toTimestamppb(m.UpdateDate)
	pb.AssistantId = m.AssistantID
	pb.QuestionId = m.QuestionID
	pb.AnswerId = m.AnswerID
	pb.AnswerRating = m.AnswerRating
	pb.HitExpectedDoc = m.HitExpectedDoc
	pb.OpComment = m.OpComment
	pb.MgmtFeedback = m.MgmtFeedback
	pb.MgmtComment = m.MgmtComment
	pb.HasUserFeedback = m.HasUserFeedback
	pb.HasOpFeedback = m.HasOpFeedback
	pb.HasMgmtFeedback = m.HasMgmtFeedback
	pb.UserFeedbackBy = m.UserFeedbackBy
	pb.OpFeedbackBy = m.OpFeedbackBy
	pb.MgmtFeedbackBy = m.MgmtFeedbackBy
	pb.UserFeedbackAt = nullTimeToTimestamppb(m.UserFeedbackAt)
	pb.OpFeedbackAt = nullTimeToTimestamppb(m.OpFeedbackAt)
	pb.MgmtFeedbackAt = nullTimeToTimestamppb(m.MgmtFeedbackAt)
	pb.CreateIdentity = m.CreateIdentity
	pb.UpdateIdentity = m.UpdateIdentity
	return pb
}

// TFeedbacks 用户反馈列表
type TFeedbacks []*TFeedback

// ToPb 协议转换
func (set TFeedbacks) ToPb() []*aipb.Feedback {
	pbs := make([]*aipb.Feedback, 0, len(set))
	for _, m := range set {
		pbs = append(pbs, m.ToPb())
	}
	return pbs
}

// ToFullPb 协议转换
func (set TFeedbacks) ToFullPb() []*aipb.FullFeedback {
	pbs := make([]*aipb.FullFeedback, 0, len(set))
	for _, m := range set {
		originalQuestion, _ := m.OriginalQuestion.TransformToAiMessage()
		originalAnswer, _ := m.OriginalAnswer.TransformToAiMessage()

		pb := &aipb.FullFeedback{
			Feedback:          m.ToPb(),
			References:        TFeedbackReferences(m.References).ToPb(),
			OriginalQuestion:  originalQuestion,
			OriginalAnswer:    originalAnswer,
			ExpectedDocId:     TFeedbackDocs(m.ExpectedDocs).PluckDocId(),
			ExpectedMgmtDocId: TFeedbackDocs(m.ExpectedMgmtDocs).PluckDocId(),
		}
		pbs = append(pbs, pb)
	}
	return pbs
}

// FeedbacksWithAssistant ...
func FeedbacksWithAssistant(db *gorm.DB) *gorm.DB {
	return db.Preload("Assistant")
}
