package model

import aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"

// TFeedbackDocs 文档列表
type TFeedbackDocs []*TFeedbackDoc

// PluckDocId 提取文档ID列表
func (docs TFeedbackDocs) PluckDocId() []uint64 {
	docIDs := make([]uint64, 0, len(docs))
	for _, doc := range docs {
		if doc.DocID > 0 {
			docIDs = append(docIDs, doc.DocID)
		}
	}
	return docIDs
}

// ToDocsPb ...
func (docs TFeedbackDocs) ToDocsPb() []*aipb.ChatMessageDoc {
	pbs := make([]*aipb.ChatMessageDoc, 0, len(docs))
	for _, doc := range docs {
		var pb *aipb.ChatMessageDoc
		if doc.Doc != nil {
			pb = &aipb.ChatMessageDoc{
				Id:         doc.Doc.ID,
				DataType:   doc.Doc.DataType,
				FileName:   doc.Doc.FileName,
				IndexText:  doc.Doc.IndexText,
				DataSource: doc.Doc.DataSource,
			}
		}
		pbs = append(pbs, pb)
	}
	return pbs
}

// ToFeedbackDocs ...
func ToFeedbackDocs(feedbackID uint64, typ aipb.FeedbackDocType, docIDs []uint64) ([]uint64, []*TFeedbackDoc) {
	if len(docIDs) == 0 {
		return nil, nil
	}

	uniqueDocIDs := make([]uint64, 0, len(docIDs))
	set := make(map[uint64]bool, len(docIDs))

	models := make([]*TFeedbackDoc, 0, len(docIDs))
	for _, docID := range docIDs {
		if set[docID] {
			continue
		}

		set[docID] = true
		uniqueDocIDs = append(uniqueDocIDs, docID)

		models = append(models, &TFeedbackDoc{
			FeedbackID: feedbackID,
			DocID:      docID,
			Type:       typ,
		})
	}

	return uniqueDocIDs, models
}
