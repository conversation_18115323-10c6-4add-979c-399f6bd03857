// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TTaggable 标签关联表
type TTaggable struct {
	ID           uint64    `gorm:"primaryKey;column:id" json:"id"`
	TagID        uint64    `gorm:"column:tag_id" json:"tagId"`               // 标签ID
	IndexID      uint64    `gorm:"column:index_id" json:"indexId"`           // 索引id
	TaggableID   uint64    `gorm:"column:taggable_id" json:"taggableId"`     // 关联对象ID
	DataType     uint32    `gorm:"column:data_type" json:"dataType"`         // 对象数据类型 1 团队｜2 产品｜3 资源｜4 图谱｜5 用户个人
	TaggableType uint64    `gorm:"column:taggable_type" json:"taggableType"` // 标签对象类型 2 团队类型 | 3 团队属性 | 4 团队行业 | 5 资源受众行业 | 6 产品技术-面向用户 | 7 资源类型
	CreateBy     uint64    `gorm:"column:create_by;default:0" json:"createBy"`
	CreateDate   time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TTaggable) TableName() string {
	return "t_taggable"
}

// TTaggableColumns get sql column name.获取数据库列名
var TTaggableColumns = struct {
	ID           string
	TagID        string
	IndexID      string
	TaggableID   string
	DataType     string
	TaggableType string
	CreateBy     string
	CreateDate   string
}{
	ID:           "id",
	TagID:        "tag_id",
	IndexID:      "index_id",
	TaggableID:   "taggable_id",
	DataType:     "data_type",
	TaggableType: "taggable_type",
	CreateBy:     "create_by",
	CreateDate:   "create_date",
}
