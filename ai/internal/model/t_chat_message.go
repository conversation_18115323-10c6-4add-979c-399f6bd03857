package model

import (
	"encoding/json"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
)

// TChatMessages 消息列表
type TChatMessages []*TChatMessage

// TransformToAiMessages ...
func (ms TChatMessages) TransformToAiMessages() ([]*aipb.ChatMessage, error) {
	pbs := make([]*aipb.ChatMessage, 0, len(ms))
	for _, m := range ms {
		pb, err := m.TransformToAiMessage()
		if err != nil {
			return nil, err
		}
		pbs = append(pbs, pb)
	}
	return pbs, nil
}

// FilterDocs 根据消息过滤文档
func (m *TChatMessage) FilterDocs(docs []*TDoc) []*TDoc {
	tDocs := make([]*TDoc, 0)
	for _, md := range m.Docs {
		for _, doc := range docs {
			if doc.RagFilename == md.RagFilename {
				tDocs = append(tDocs, doc)
			}
		}
	}
	return tDocs
}

func (m *TChatMessage) TransformToAiMessage() (*aipb.ChatMessage, error) {
	if m == nil {
		return nil, nil
	}
	message := &aipb.ChatMessage{
		Id:              m.ID,
		ChatId:          m.ChatID,
		Type:            aipb.ChatMessageType(m.Type),
		QuestionId:      m.QuestionID,
		CreateBy:        m.CreateBy,
		RejectReason:    m.RejectReason,
		CreateDate:      timestamppb.New(m.CreateDate),
		RatingScale:     aipb.RatingScale(m.RatingScale),
		AssistantId:     m.AssistantID,
		Lang:            m.Lang,
		AskType:         aipb.QuestionAskType(m.AskType),
		LiveAgentName:   m.LiveAgentName,
		State:           aipb.ChatMessageState(m.State),
		ShowType:        m.ShowType,
		DocMatchPattern: m.MatchPattern,
		PromptType:      m.PromptType,
		PublishHashId:   m.HashId,
		IsFileReady:     true,
	}

	// TChatMessageFile
	if m.Files != nil {
		for _, file := range m.Files {
			if file.State == aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSING {
				message.IsFileReady = false
			}
			message.Files = append(message.Files, &aipb.ChatMessageFile{
				Url:       file.URL,
				State:     file.State,
				ParsedUrl: file.ParsedURL,
				Id:        file.ID,
			})
		}
	}

	// TChatSuggest
	if len(m.Suggests) > 0 {
		for _, sug := range m.Suggests {
			message.SuggestQuestion = append(message.SuggestQuestion, sug.Suggest)
		}
		message.SuggestionMode = aipb.AskSuggestionMode(m.Suggests[0].Mode)
	}

	// TChatMessageCollection
	if m.Collections != nil {
		var items []*aipb.SearchCollectionItem
		err := json.Unmarshal([]byte(m.Collections.Content), &items)
		if err != nil {
			return nil, err
		}
		message.CollectionSnapshot = &aipb.MessageCollectionSnapshot{
			StartTime:   timestamppb.New(*m.Collections.StartTime),
			EndTime:     timestamppb.New(*m.Collections.EndTime),
			Items:       items,
			CleanChunks: m.Collections.CleanChunks,
			MessageId:   m.ID,
		}
	}

	// TChatMessageTask
	if m.Task != nil {
		message.Task = &aipb.ChatMessageTask{
			PipelineId: m.Task.PipelineID,
			TaskId:     m.Task.TaskID,
			State:      aipb.PipelineTaskState(m.Task.State),
		}
	}

	// TChatMessageThink
	if m.Think != nil {
		message.Think = m.Think.Content
		message.ThinkDuration = int32(m.Think.Duration)
	}

	// TChatMessageDoc
	if len(m.Docs) > 0 {
		for _, doc := range m.Docs {
			message.DocNames = append(message.DocNames, doc.RagFilename)
		}
	}

	// TChatMessageLog
	for _, log := range m.MessageLog {
		message.Logs = append(message.Logs, log.TransToAiMessageLog(log.ConfigSnapshot))
	}

	if m.LastOperation != nil {
		message.PublishHashId = m.LastOperation.HashID
		message.LastOperationType = aipb.ChatOperationType(m.LastOperation.OperationType)
		message.LastOperator = &aipb.ChatMessageOperator{
			OperationType:   aipb.ChatOperationType(m.LastOperation.OperationType),
			StopText:        m.LastOperation.StopText,
			StopThink:       m.LastOperation.StopThink,
			StopChunkState:  m.LastOperation.StopChunkState,
			OperationParams: string(m.LastOperation.OperationParams),
			HashId:          m.LastOperation.HashID,
		}
	}

	if m.StartTime != nil {
		message.StartTime = timestamppb.New(*m.StartTime)
	}
	if m.EndTime != nil {
		message.EndTime = timestamppb.New(*m.EndTime)
	}
	if m.Content != nil {
		content := &aipb.ChatMessageContent{}
		err := json.Unmarshal(m.Content, &content)
		if err != nil {
			return nil, err
		}
		message.Text = content.Text
		message.Link = content.Link
		message.SqlQuery = content.SqlQuery
		message.Ugcs = content.Ugcs
		message.ImageUrl = content.ImageUrl
		message.FinalQuery = content.FinalQuery
	}
	if m.Feedback != nil {
		message.FeedbackId = m.Feedback.ID
		message.HasUserFeedback = m.Feedback.HasUserFeedback
	}

	return message, nil
}

func TransformChatToPb(session *TChat) *aipb.Chat {
	chat := &aipb.Chat{
		Id:         session.ID,
		Title:      session.Title,
		CreateBy:   session.CreateBy,
		CreateDate: timestamppb.New(session.CreateDate),
		UpdateDate: timestamppb.New(session.UpdateDate),
		ChatType:   aipb.ChatType(session.Type),
		// UserImage:   session.Image, 2.6迭代，法务要求头像私密信息不显示不保存
		Nickname:        session.Nickname,
		Origin:          aipb.ChatOrigin(session.Origin),
		AssistantId:     session.AssistantID,
		ChatState:       aipb.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED,
		SupportType:     aipb.ChatSupportType(session.SupportType),
		QuestionCnt:     session.QuestionCnt,
		AvgDuration:     session.AvgDuration,
		RatingScale:     session.RatingScale,
		DocHits:         session.DocHits,
		IsManual:        session.IsManual,
		RejectJobResult: session.RejectJobResult,
		RegionCode:      session.RegionCode,
		IsShared:        session.IsShared,
	}
	for _, v := range session.Labels {
		chat.Labels = append(chat.Labels, v.ToLabelPb())
	}
	if session.FinishDate != nil && (session.Type == int32(aipb.ChatType_CHAT_TYPE_WECHAT) ||
		session.Type == int32(aipb.ChatType_CHAT_TYPE_WHATSAPP)) {
		chat.ChatState = aipb.ChatCurrentState_CHAT_CURRENT_STATE_REPLACED
	}
	//if session.FinishDate != nil && session.Type == int32(aipb.ChatType_CHAT_TYPE_WECHAT) {
	//}
	return chat
}

// TransformChatWechatToPb ...
func TransformChatWechatToPb(m *TChat) *aipb.ChatWeChat {
	chat := &aipb.ChatWeChat{
		Id:                  m.ID,
		Title:               m.Title,
		Nickname:            m.Nickname,
		ChatState:           aipb.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED,
		SupportType:         aipb.ChatSupportType(m.SupportType),
		LiveAgentName:       m.LiveAgentName,
		AppId:               m.AppID,
		ExternalUserId:      m.ExternalUserID,
		AssistantId:         m.AssistantID,
		DefaultAnswerRecord: m.DefaultAnswerRecord,
	}
	if m.FinishDate != nil && (m.Type == int32(aipb.ChatType_CHAT_TYPE_WECHAT) ||
		m.Type == int32(aipb.ChatType_CHAT_TYPE_WHATSAPP)) {
		chat.ChatState = aipb.ChatCurrentState_CHAT_CURRENT_STATE_REPLACED
	}
	return chat
}

func MessageWithLog(db *gorm.DB) *gorm.DB {
	return db.Preload("MessageLog")
}

func (m *TChatLog) GetTChatLogConfig() *aipb.MessageConfig {
	var configSnapshot *aipb.MessageConfig
	err := json.Unmarshal([]byte(m.ConfigSnapshot), &configSnapshot)
	if err != nil {
		return nil
	}
	return configSnapshot
}

func (m *TChatLog) GetTChatLogRequestText() interface{} {
	var result interface{}
	if json.Valid([]byte(m.RequestText)) {
		if err := json.Unmarshal([]byte(m.RequestText), &result); err != nil {
			return nil
		}
		return result
	}
	return m.RequestText
}

func (m *TChatLog) TransToAiMessageLog(configShot string) *aipb.ChatMessageLog {
	return &aipb.ChatMessageLog{
		SqlQuery:       m.SQLQuery,
		Enhancement:    m.Enhancement,
		Gpt:            m.Gpt,
		Ref:            m.Ref,
		Code:           m.Code,
		StartTime:      timestamppb.New(m.StartTime),
		EndTime:        timestamppb.New(m.EndTime),
		ConfigSnapshot: configShot,
		Type:           aipb.ChatMessageType(m.MessageType),
		RequestText:    m.RequestText,
		CreateDate:     timestamppb.New(m.CreateDate),
		FetchRespTime: func() *timestamppb.Timestamp {
			if m.FetchRespTime == nil {
				return nil
			}
			return timestamppb.New(*m.FetchRespTime)
		}(),
	}
}

func (m *TPipelineTask) TransToChatAgentTask() *aipb.ChatAgentTask {
	task := &aipb.ChatAgentTask{
		Id:           m.ID,
		PromptPrefix: m.PromptPrefix,
		PreTaskId:    m.PreTaskID,
		FetchType:    aipb.PipelineTaskFetchType(m.FetchType),
		DefaultText:  m.DefaultText,
		Prompt:       m.Prompt,
		PipelineId:   m.PipelineID,
		VisionModel:  m.VisionModel,
	}
	if m.SuggestConfig != nil {
		task.SuggestionConfig = m.SuggestConfig
	}
	if m.MatchConfig != nil {
		task.MatchPattern = m.MatchConfig.DocMatchPattern
	}

	return task
}
