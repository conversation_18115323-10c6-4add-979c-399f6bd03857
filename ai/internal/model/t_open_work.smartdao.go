// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)


// TOpenWork 开放企微租户
type TOpenWork struct {
	ID            uint64    `gorm:"primaryKey;column:id" json:"id"`                                                // 自增主键
	PermanentCode string    `gorm:"column:permanent_code;default:''" json:"permanentCode"`                         // 永久授权码
	CorpID        string    `gorm:"column:corp_id;default:''" json:"corpId"`                                       // 企微租户ID
	Action        string    `gorm:"column:action;default:''" json:"action"`
	OpenKfid      string    `gorm:"column:open_kfid;default:''" json:"openKfid"`
	CreateDate    time.Time `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TOpenWork) TableName() string {
	return "t_open_work"
}

// TOpenWorkColumns get sql column name.获取数据库列名
var TOpenWorkColumns = struct {
	ID            string
	PermanentCode string
	CorpID        string
	Action        string
	OpenKfid      string
	CreateDate    string
}{
	ID:            "id",
	PermanentCode: "permanent_code",
	CorpID:        "corp_id",
	Action:        "action",
	OpenKfid:      "open_kfid",
	CreateDate:    "create_date",
}
