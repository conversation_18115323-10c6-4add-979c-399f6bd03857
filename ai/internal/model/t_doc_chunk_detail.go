package model

import (
	"fmt"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// AssistantChunks 助手分段
type AssistantChunks struct {
	// AssistantIds 助手列表
	AssistantIds []uint64
	// Chunks 分段列表
	Chunks []*ChunkItem
	// Sum 摘要
	Sum string
}

// ChunkItem 分段项
type ChunkItem struct {
	// ChunkId 分段ID
	ChunkId string `json:"chunk_id"`
	// RuneStart unicode起始索引
	RuneStart int `json:"rune_start"`
	// RuneLen unicode长度
	RuneLen int `json:"rune_len"`
	// CharStart 字节起始索引
	CharStart int `json:"char_start"`
	// CharLen 字节长度
	CharLen int `json:"char_len"`
	// Content 分段内容
	Content string `json:"-"`
}

// ToPb ...
func (ci *ChunkItem) ToPb(debug bool) *aipb.ChunkItem {
	if ci == nil {
		return nil
	}
	pb := &aipb.ChunkItem{
		Start: uint32(ci.RuneStart),
		Len:   uint32(ci.RuneLen),
	}
	if debug {
		pb.Content = ci.Content
	}
	return pb
}

// ChunkItems 列表
type ChunkItems []*ChunkItem

// ToPb ...
func (cis ChunkItems) ToPb(debug bool) []*aipb.ChunkItem {
	pbs := make([]*aipb.ChunkItem, 0, len(cis))
	for _, ci := range cis {
		if pb := ci.ToPb(debug); pb != nil {
			pbs = append(pbs, pb)
		}
	}
	return pbs
}

// ChunkDocThenPluckContents 文本分段后提取分段内容到string-slice
func (m *TDocChunkDetail) ChunkDocThenPluckContents(text string) []string {
	m.ChunkDoc(text)
	return m.PluckContents()
}

// PluckContents 提取分段内容到string-slice
func (m *TDocChunkDetail) PluckContents() []string {
	chunks := make([]string, 0, len(m.Items))

	for _, item := range m.Items {
		chunks = append(chunks, item.Content)
	}

	return chunks
}

// ChunkDoc 文本分段
func (m *TDocChunkDetail) ChunkDoc(text string) {
	textLen := len(text)

	var start, end int
	for _, item := range m.Items {
		if start = item.CharStart; start >= textLen {
			continue
		}
		if end = start + item.CharLen; end > textLen {
			end = textLen
		}
		item.Content = text[start:end]
	}
}

// DeleteDocChunks 删除文档分段
func DeleteDocChunks(tx *gorm.DB, docID uint64, assistantIDs []uint64) error {
	query := tx.Model(&TAssistantDoc{}).Where("doc_id = ?", docID)
	if len(assistantIDs) > 0 {
		query = query.Where("assistant_id IN (?)", assistantIDs)
	}
	if err := query.Update(TAssistantDocColumns.ChunkDetailID, 0).Error; err != nil {
		return fmt.Errorf("delete doc chunks: %w", err)
	}
	return nil
}
