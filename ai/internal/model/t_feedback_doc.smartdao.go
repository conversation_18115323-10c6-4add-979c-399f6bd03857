package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TFeedbackDoc [...]
type TFeedbackDoc struct {
	ID         uint64               `gorm:"primaryKey;column:id" json:"id"`                 // 主键
	FeedbackID uint64               `gorm:"column:feedback_id;default:0" json:"feedbackId"` // 反馈ID
	DocID      uint64               `gorm:"column:doc_id;default:0" json:"docId"`           // 文本ID
	Type       aipb.FeedbackDocType `gorm:"column:type;default:0" json:"type"`              // 类型

	Doc *TDoc `gorm:"foreignKey:id;references:doc_id"` // 文档
}

// TableName get sql table name.获取数据库表名
func (m *TFeedbackDoc) TableName() string {
	return "t_feedback_doc"
}

// TFeedbackDocColumns get sql column name.获取数据库列名
var TFeedbackDocColumns = struct {
	ID         string
	FeedbackID string
	DocID      string
	Type       string
}{
	ID:         "id",
	FeedbackID: "feedback_id",
	DocID:      "doc_id",
	Type:       "type",
}
