// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatShare 聊天消息分享主表
type TChatShare struct {
	ID                 uint64         `gorm:"primaryKey;column:id" json:"id"`                               // 自增id
	ShareID            string         `gorm:"column:share_id" json:"shareId"`                               // 分享唯一标识(可考虑UUID)
	ChatID             uint64         `gorm:"column:chat_id" json:"chatId"`                                 // 关联的原会话id
	OriginalMessageIDs datatypes.JSON `gorm:"column:original_message_ids" json:"originalMessageIds"`        // 原始消息id集合(JSON数组格式)
	ShareDate          time.Time      `gorm:"column:share_date;default:CURRENT_TIMESTAMP" json:"shareDate"` // 分享创建时间
	ExpireDate         *time.Time     `gorm:"column:expire_date" json:"expireDate"`                         // 分享过期时间(可为空表示永久有效)
	SharedBy           uint64         `gorm:"column:shared_by" json:"sharedBy"`                             // 分享者用户id
	AccessCount        int32          `gorm:"column:access_count;default:0" json:"accessCount"`             // 被访问次数统计
	LastAccessTime     *time.Time     `gorm:"column:last_access_time" json:"lastAccessTime"`                // 最后访问时间
	ShareType          int32          `gorm:"column:share_type;default:1" json:"shareType"`                 // 分享类型:1链接 2二维码 3小程序码
	ShareStatus        int32          `gorm:"column:share_status;default:1" json:"shareStatus"`             // 分享状态:1有效 2已失效
	AssistantID        uint64         `gorm:"column:assistant_id" json:"assistantId"`                       // 关联的助手id
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at" json:"deletedAt"`                           // 删除时间
}

// TableName get sql table name.获取数据库表名
func (m *TChatShare) TableName() string {
	return "t_chat_share"
}

// TChatShareColumns get sql column name.获取数据库列名
var TChatShareColumns = struct {
	ID                 string
	ShareID            string
	ChatID             string
	OriginalMessageIDs string
	ShareDate          string
	ExpireDate         string
	SharedBy           string
	AccessCount        string
	LastAccessTime     string
	ShareType          string
	ShareStatus        string
	AssistantID        string
	DeletedAt          string
}{
	ID:                 "id",
	ShareID:            "share_id",
	ChatID:             "chat_id",
	OriginalMessageIDs: "original_message_ids",
	ShareDate:          "share_date",
	ExpireDate:         "expire_date",
	SharedBy:           "shared_by",
	AccessCount:        "access_count",
	LastAccessTime:     "last_access_time",
	ShareType:          "share_type",
	ShareStatus:        "share_status",
	AssistantID:        "assistant_id",
	DeletedAt:          "deleted_at",
}
