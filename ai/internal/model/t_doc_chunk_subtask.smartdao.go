package model

import (
	"database/sql"
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocChunkSubtask [...]
type TDocChunkSubtask struct {
	ID           uint64                    `gorm:"primaryKey;column:id" json:"id"`
	TaskID       uint64                    `gorm:"column:task_id;default:0" json:"taskId"`                         // 任务ID
	DocID        uint64                    `gorm:"column:doc_id;default:0" json:"docId"`                           // 文档ID
	AssistantID  uint64                    `gorm:"column:assistant_id;default:0" json:"assistantId"`               // 助手ID
	CollectionID uint64                    `gorm:"column:collection_id;default:0" json:"collectionID"`             // 知识库ID
	Type         aipb.DocChunkSubtaskType  `gorm:"column:type;default:0" json:"type"`                              // 类型：1 update_chunks、2 edit_collection、3 delete_vec
	Para         []byte                    `gorm:"column:para" json:"para"`                                        // 子任务参数
	State        aipb.DocChunkSubtaskState `gorm:"column:state;default:0" json:"state"`                            // 状态：1成功、2失败
	FinishedAt   sql.NullTime              `gorm:"column:finished_at" json:"finishedAt"`                           // 完成时间
	CreateDate   time.Time                 `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间

	Task *TDocChunkTask `gorm:"foreignKey:task_id" json:"task,omitempty"` // 所属任务
}

// TableName get sql table name.获取数据库表名
func (m *TDocChunkSubtask) TableName() string {
	return "t_doc_chunk_subtask"
}

// TDocChunkSubtaskColumns get sql column name.获取数据库列名
var TDocChunkSubtaskColumns = struct {
	ID           string
	TaskID       string
	DocID        string
	AssistantID  string
	CollectionID string
	Type         string
	Para         string
	State        string
	FinishedAt   string
	CreateDate   string
}{
	ID:           "id",
	TaskID:       "task_id",
	DocID:        "doc_id",
	AssistantID:  "assistant_id",
	CollectionID: "collection_id",
	Type:         "type",
	Para:         "para",
	State:        "state",
	FinishedAt:   "finished_at",
	CreateDate:   "create_date",
}
