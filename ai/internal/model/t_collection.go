package model

import (
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

const (
	CollectionLangZH = "zh"
	CollectionLangEN = "en"
)

// ToProto ...
func (t *TCollection) ToProto() *ai.Collection {
	return &ai.Collection{
		Id:   t.ID,
		Name: t.Name,
	}
}

// UpdateCollectionEmbUpdateDate 更新collection的向量更新时间
func UpdateCollectionEmbUpdateDate(tx *gorm.DB, collectionId uint64) error {
	return tx.Model(&TCollection{}).Where("id = ?", collectionId).UpdateColumn("emb_update_date", time.Now()).Error
}

// UpdateCollectionEmbUpdateDateByName 更新collection的向量更新时间
func UpdateCollectionEmbUpdateDateByName(tx *gorm.DB, collectionName string) error {
	return tx.Model(&TCollection{}).Where("rag_name = ?", collectionName).UpdateColumn("emb_update_date", time.Now()).Error
}
