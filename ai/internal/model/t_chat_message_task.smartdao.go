// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatMessageTask chat pipeline记录表
type TChatMessageTask struct {
	ChatID     uint64    `gorm:"column:chat_id" json:"chatId"`                                   // 会话Id
	State      int32     `gorm:"column:state;default:0" json:"state"`                            // 任务状态：0-未执行，1-执行中，2-已完成，3-失败
	MessageID  uint64    `gorm:"column:message_id" json:"messageId"`                             // 问题Id
	PipelineID uint64    `gorm:"column:pipeline_id" json:"pipelineId"`                           // 流程Id
	TaskID     uint64    `gorm:"column:task_id" json:"taskId"`                                   // 移动任务游标Id
	UpdateDate time.Time `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"` // 更新时间
	CreateDate time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TChatMessageTask) TableName() string {
	return "t_chat_message_task"
}

// TChatMessageTaskColumns get sql column name.获取数据库列名
var TChatMessageTaskColumns = struct {
	ChatID     string
	State      string
	MessageID  string
	PipelineID string
	TaskID     string
	UpdateDate string
	CreateDate string
}{
	ChatID:     "chat_id",
	State:      "state",
	MessageID:  "message_id",
	PipelineID: "pipeline_id",
	TaskID:     "task_id",
	UpdateDate: "update_date",
	CreateDate: "create_date",
}
