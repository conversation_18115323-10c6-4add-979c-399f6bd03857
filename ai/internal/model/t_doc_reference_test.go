package model

import (
	"context"
	"testing"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

// TestReferenceFromProtoWithSort 测试参考资料从Proto转换时的排序
func TestReferenceFromProtoWithSort(t *testing.T) {
	doc := &TDoc{ID: 1}

	// 创建测试参考资料，包含引用文档和纯文本
	refs := []*ai.DocReference{
		{Id: 100, Name: "第一个引用文档"},
		{Text: "第一个纯文本参考资料"},
		{Id: 200, Name: "第二个引用文档"},
		{Text: "第二个纯文本参考资料"},
		{Text: "第三个纯文本参考资料"},
	}

	// 调用转换方法
	doc.ReferenceFromProtoWithSort(refs)

	// 验证引用文档关联表数量和排序
	assert.Equal(t, 2, len(doc.DocReferences))
	assert.Equal(t, int64(100), doc.DocReferences[0].RefID)
	assert.Equal(t, int32(0), doc.DocReferences[0].SortOrder) // 索引0对应第一个元素
	assert.Equal(t, int64(200), doc.DocReferences[1].RefID)
	assert.Equal(t, int32(2), doc.DocReferences[1].SortOrder) // 索引2对应第三个元素

	// 验证纯文本参考资料数量和排序
	assert.Equal(t, 3, len(doc.TextReferences))
	assert.Equal(t, "第一个纯文本参考资料", doc.TextReferences[0].Text)
	assert.Equal(t, int32(1), doc.TextReferences[0].SortOrder) // 索引1对应第二个元素
	assert.Equal(t, "第二个纯文本参考资料", doc.TextReferences[1].Text)
	assert.Equal(t, int32(3), doc.TextReferences[1].SortOrder) // 索引3对应第四个元素
	assert.Equal(t, "第三个纯文本参考资料", doc.TextReferences[2].Text)
	assert.Equal(t, int32(4), doc.TextReferences[2].SortOrder) // 索引4对应第五个元素
}

// TestReferenceToProto 测试参考资料转换为Proto时的排序
func TestReferenceToProto(t *testing.T) {
	doc := &TDoc{
		ID: 1,
		References: []*TDoc{
			{ID: 100, FileName: "引用文档1"},
			{ID: 200, FileName: "引用文档2"},
		},
		DocReferences: []*TDocReference{
			{DocID: 1, RefID: 100, SortOrder: 0},
			{DocID: 1, RefID: 200, SortOrder: 2},
		},
		TextReferences: []*TDocTextReference{
			{DocID: 1, Text: "纯文本1", SortOrder: 1},
			{DocID: 1, Text: "纯文本2", SortOrder: 3},
			{DocID: 1, Text: "纯文本3", SortOrder: 4},
		},
	}

	// 调用转换方法
	refs := doc.ReferenceToProto()

	// 验证返回的参考资料数量
	assert.Equal(t, 5, len(refs))

	// 验证排序是否正确（按SortOrder排序）
	assert.Equal(t, uint64(100), refs[0].Id) // SortOrder: 0
	assert.Equal(t, "纯文本1", refs[1].Text)    // SortOrder: 1
	assert.Equal(t, uint64(200), refs[2].Id) // SortOrder: 2
	assert.Equal(t, "纯文本2", refs[3].Text)    // SortOrder: 3
	assert.Equal(t, "纯文本3", refs[4].Text)    // SortOrder: 4

	// 验证引用文档的名称是否正确填充
	assert.Equal(t, "引用文档1", refs[0].Name)
	assert.Equal(t, "引用文档2", refs[2].Name)
}

// TestReplaceReferencesWithSort 测试替换参考资料并保持排序的功能
func TestReplaceReferencesWithSort(t *testing.T) {
	// 注意：这个测试需要真实的数据库连接，在实际项目中应该使用测试数据库
	// 这里只是展示测试的结构

	doc := &TDoc{ID: 1}

	refs := []*ai.DocReference{
		{Id: 100, Name: "引用文档1"},
		{Text: "纯文本1"},
		{Id: 200, Name: "引用文档2"},
		{Text: "纯文本2"},
	}

	// 在实际测试中，这里需要mock数据库事务
	// err := doc.ReplaceReferencesWithSort(mockTx, refs)
	// assert.NoError(t, err)

	// 验证数据库中的记录是否正确创建并包含正确的排序信息
	// 这需要查询数据库来验证

	t.Skip("需要数据库连接的集成测试")
}
