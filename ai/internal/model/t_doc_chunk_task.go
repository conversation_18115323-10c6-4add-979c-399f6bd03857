package model

import (
	"fmt"
	"math"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// TDocChunkTasks 任务列表
type TDocChunkTasks []*TDocChunkTask

// TDocChunkTaskPara 任务参数
type TDocChunkTaskPara struct {
	// 助手列表
	Assistants []uint64 `json:"assistants"`
	// 自动分段参数
	AutoPara *aipb.AutoChunkPara `json:"auto_para,omitempty"`
	// 手动分段参数
	ManualPara *aipb.ManualChunkPara `json:"manual_para,omitempty"`
}

// ToPb ...
func (ms TDocChunkTasks) ToPb() []*aipb.DocChunkTask {
	pbs := make([]*aipb.DocChunkTask, 0, len(ms))
	for _, m := range ms {
		pbs = append(pbs, m.ToPb())
	}
	return pbs
}

// ToPb ...
func (m *TDocChunkTask) ToPb() *aipb.DocChunkTask {
	if m == nil {
		return nil
	}

	return &aipb.DocChunkTask{
		Id:    m.ID,
		State: m.State,
	}
}

// GetDefaultChunkConfig 获取默认的分段配置
func GetDefaultChunkConfig(embeddingModel, lang string) (*aipb.AssistantChunkConfig, error) {
	opt, err := getEmbeddingModelOption(embeddingModel)
	if err != nil {
		return nil, err
	}
	return getDefaultChunkConfig(lang, opt), nil
}

func getDefaultChunkConfig(lang string, opt *aipb.EmbeddingModelOption) *aipb.AssistantChunkConfig {
	minCharCount := tokenCountToCharCount(int(opt.UserSegMinTokens), lang, opt, math.Ceil)
	maxCharCount := tokenCountToCharCount(int(opt.UserSegMaxTokens), lang, opt, math.Floor)
	overlapCount := tokenCountToCharCount(int(opt.UserOverlapTokens), lang, opt, math.Floor)

	return &aipb.AssistantChunkConfig{
		MinCharCount: int32(minCharCount),
		MinCharLang:  lang,
		MaxCharCount: int32(maxCharCount),
		MaxCharLang:  lang,
		OverlapCount: int32(overlapCount),
		OverlapLang:  lang,
	}
}

// CalcDocChunkPara 计算分段参数
func CalcDocChunkPara(embeddingModel string, cfg *aipb.AssistantChunkConfig) (int, int, int) {
	if cfg == nil {
		return 0, 0, 0
	}

	opt, err := getEmbeddingModelOption(embeddingModel)
	if err != nil {
		return 0, 0, 0
	}

	minTokenCount := charCountToTokenCount(int(cfg.MinCharCount), cfg.MinCharLang, opt, math.Ceil)
	maxTokenCount := charCountToTokenCount(int(cfg.MaxCharCount), cfg.MaxCharLang, opt, math.Floor)
	overlapTokenCount := charCountToTokenCount(int(cfg.OverlapCount), cfg.OverlapLang, opt, math.Floor)

	return minTokenCount, maxTokenCount, overlapTokenCount
}

func getEmbeddingModelOption(embeddingModel string) (*aipb.EmbeddingModelOption, error) {
	opts, err := getEmbeddingModelOptions()
	if err != nil {
		return nil, err
	}

	var opt *aipb.EmbeddingModelOption
	for _, item := range opts {
		if embeddingModel == item.Model {
			opt = item
			break
		}
	}

	return opt, nil
}

// GetEmbeddingModelOptions 获取向量化模型选项配置
func GetEmbeddingModelOptions() ([]*aipb.EmbeddingModelOption, error) {
	cfg, err := getEmbeddingModelOptions()
	if err != nil {
		return nil, err
	}

	for _, item := range cfg {
		item.ZhDefault = getDefaultChunkConfig("zh", item)
		item.EnDefault = getDefaultChunkConfig("en", item)
	}

	return cfg, nil
}

func getEmbeddingModelOptions() ([]*aipb.EmbeddingModelOption, error) {
	var embeddingModelOptions []*aipb.EmbeddingModelOption
	if err := config.Unmarshal("assistant.embeddingModel", &embeddingModelOptions); err != nil {
		return nil, fmt.Errorf("unmarshal assistant.embeddingModel: %w", err)
	}
	return embeddingModelOptions, nil
}

func tokenCountToCharCount(tokenCount int, lang string, opt *aipb.EmbeddingModelOption, round func(float64) float64) int {
	tokenCountPerChar := getTokenCountPerChar(lang, opt)
	if tokenCountPerChar == 0 {
		return 0
	}

	size := round(float64(tokenCount) / float64(tokenCountPerChar))
	return int(size)
}

func charCountToTokenCount(charCount int, lang string, opt *aipb.EmbeddingModelOption, round func(float64) float64) int {
	tokenCountPerChar := getTokenCountPerChar(lang, opt)
	if tokenCountPerChar == 0 {
		return 0
	}

	size := round(float64(charCount) * float64(tokenCountPerChar))
	return int(size)
}

func getTokenCountPerChar(lang string, opt *aipb.EmbeddingModelOption) float32 {
	if opt == nil {
		return 0
	}

	var tokenCountPerChar float32
	switch lang {
	case "zh":
		tokenCountPerChar = (opt.ZhMaxTokensPerChar + opt.ZhMinTokensPerChar) / 2
	case "en":
		tokenCountPerChar = (opt.EnMaxTokensPerChar + opt.EnMinTokensPerChar) / 2
	}
	return tokenCountPerChar
}

// IsExceedEmbeddingTokenLimit 检查文本是否超过嵌入模型的最大token限制
// 参数:
// - embeddingModel: 嵌入模型名称
// - text: 待检查的文本内容
// 返回:
// - bool: 是否超过限制(true表示超过)
// - int: 估算的token数量
// - int: 模型的最大token限制
// - error: 错误信息
func IsExceedEmbeddingTokenLimit(embeddingModel string, text string) (bool, int, int, error) {
	// 获取模型配置
	opt, err := getEmbeddingModelOption(embeddingModel)
	if err != nil {
		return false, 0, 0, fmt.Errorf("获取嵌入模型配置失败: %w", err)
	}

	if opt == nil {
		return false, 0, 0, fmt.Errorf("未找到指定的嵌入模型: %s", embeddingModel)
	}

	// 分离中文和英文字符
	chineseChars := 0
	englishChars := 0

	for _, r := range text {
		// 判断是否为中文字符（Unicode范围）
		if r >= 0x4E00 && r <= 0x9FFF {
			chineseChars++
		} else {
			englishChars++
		}
	}

	// 分别计算中文和英文的token数量
	chineseTokens := 0
	if chineseChars > 0 {
		chineseTokens = charCountToTokenCount(chineseChars, "zh", opt, math.Ceil)
	}

	englishTokens := 0
	if englishChars > 0 {
		englishTokens = charCountToTokenCount(englishChars, "en", opt, math.Ceil)
	}

	// 计算总token数量
	totalTokenCount := chineseTokens + englishTokens

	// 获取模型的最大token限制
	modelMaxTokens := int(opt.TechSegMaxTokens)

	// 判断是否超过限制
	isExceed := totalTokenCount > modelMaxTokens

	return isExceed, totalTokenCount, modelMaxTokens, nil
}
