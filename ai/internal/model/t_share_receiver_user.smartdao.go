// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TShareReceiverUser 接收方分享的关联用户信息
type TShareReceiverUser struct {
	ID               uint64             `gorm:"primaryKey;column:id" json:"id"`                    // 自增id
	ReceiverID       uint64             `gorm:"column:receiver_id" json:"receiverId"`              // 接收方全局配置主键id
	AdminAssistantID uint64             `gorm:"column:admin_assistant_id" json:"adminAssistantId"` // 管理的助手id
	UserID           uint64             `gorm:"column:user_id" json:"userId"`                      // 发送方用户id
	TeamID           uint64             `gorm:"column:team_id" json:"teamId"`                      // 发送方团队id
	GroupID          uint64             `gorm:"column:group_id" json:"groupId"`                    // 分组
	State            aipb.DocShareState `gorm:"column:state;default:2" json:"state"`               // 接收后知识的状态 1: 启用 2: 停用 3:不接收
}

// TableName get sql table name.获取数据库表名
func (m *TShareReceiverUser) TableName() string {
	return "t_share_receiver_user"
}

// TShareReceiverUserColumns get sql column name.获取数据库列名
var TShareReceiverUserColumns = struct {
	ID               string
	ReceiverID       string
	AdminAssistantID string
	UserID           string
	TeamID           string
	GroupID          string
	State            string
}{
	ID:               "id",
	ReceiverID:       "receiver_id",
	AdminAssistantID: "admin_assistant_id",
	UserID:           "user_id",
	TeamID:           "team_id",
	GroupID:          "group_id",
	State:            "state",
}
