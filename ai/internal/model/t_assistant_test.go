package model

import (
	"encoding/json"
	"fmt"
	"testing"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

var assistantFieldManageConfigDefaultUnreadable = []string{
	"collection_name",
}

var assistantFieldManageConfigDefaultWritable = []string{
	TAssistantColumns.Name,
	TAssistantColumns.NameEn,
	TAssistantColumns.Enabled,
	TAssistantColumns.Nickname,
	TAssistantColumns.NicknameEn,
	TAssistantColumns.AvatarUrl,
	TAssistantColumns.SwitchAssistantId,
	TAssistantColumns.WelcomeMessageConfig,
	TAssistantColumns.PresetQuestionConfig,
}

// 生成默认助手字段管理值
func TestDefaultAssistantFieldManageConfig(t *testing.T) {
	readable := make([]string, 0, len(AssistantReadableInConsole))
	for _, field := range AssistantReadableInConsole {
		if xstrings.In(field, assistantFieldManageConfigDefaultUnreadable) {
			continue
		}
		readable = append(readable, field)
	}

	data := map[string][]string{
		"readable": readable,
		"writable": AssistantWritableInConsole,
	}
	s, _ := json.Marshal(data)
	fmt.Println(string(s))
}

var defaultAssistantVisibleChainFieldsBlocklist = map[string]bool{
	"sql_model": true,
}

// 生成默认助手链路查询字段
func TestGenDefaultAssistantVisibleChainFields(t *testing.T) {
	cfg := config.New(
		config.WithSource(
			config.NewSource(config.SourcePath("../../bin/etc/ai.toml")),
		),
	)
	cfg.Load()
	var visibleChainOption []*aipb.VisibleChainOption
	if err := cfg.Unmarshal("assistant.visibleChainOption", &visibleChainOption); err != nil {
		t.Fatal(err)
	}

	var fields []string
	for _, option := range visibleChainOption {
		if defaultAssistantVisibleChainFieldsBlocklist[option.Field] {
			continue
		}
		fields = append(fields, option.Field)
	}
	s, _ := json.Marshal(fields)
	fmt.Println(string(s))
}
