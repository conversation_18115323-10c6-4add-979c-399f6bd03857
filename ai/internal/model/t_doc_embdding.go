package model

import (
	"context"
)

// GetDocEmbeddingSegments 获取doc对应的向量分片
func GetDocEmbeddingSegments(ctx context.Context, docId, collectionId uint64) ([]string, error) {
	var row TDocEmbdding
	err := NewQuery[TDocEmbdding](ctx).DB().Where("doc_id = ? and collection_id = ?", docId, collectionId).Find(&row).Error
	if err != nil {
		return nil, err
	}
	return row.SegIDs, nil
}

// SetDocEmbeddingSegments 记录doc对应的向量分片
func SetDocEmbeddingSegments(ctx context.Context, docId, collectionId uint64, segIDs []string) error {
	var row = &TDocEmbdding{
		DocID:        docId,
		CollectionID: collectionId,
		SegIDs:       segIDs,
		SegCount:     int32(len(segIDs)),
	}
	err := NewQuery[TDocEmbdding](ctx).DB().Model(&row).Save(row).Error
	if err != nil {
		return err
	}
	return nil
}

// DeleteDocEmbeddingSegments 删除doc对应的向量分片记录
func DeleteDocEmbeddingSegments(ctx context.Context, docId, collectionId uint64) error {
	var row = &TDocEmbdding{
		DocID:        docId,
		CollectionID: collectionId,
	}
	err := NewQuery[TDocEmbdding](ctx).DB().Delete(row).Error
	if err != nil {
		return err
	}
	return nil
}
