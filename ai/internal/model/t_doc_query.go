package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
)

// DocIds ...
type DocIds []uint64

// Value ...
func (a DocIds) Value() (driver.Value, error) {
	return json.Marshal(a)
}

// Scan ...
func (a *DocIds) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("failed to convert database value to []byte")
	}
	return json.Unmarshal(bytes, a)
}

// DocIdsIterator ...
type DocIdsIterator struct {
	docIds   DocIds
	pos      int // 当前位置
	pageSize int // 每页大小
}

// NewDocIdsIterator ...
func NewDocIdsIterator(docIds DocIds, args ...int) *DocIdsIterator {
	v := &DocIdsIterator{
		docIds:   docIds,
		pos:      0,
		pageSize: config.GetIntOr("llm.collection.patchOperateLimit", 20),
	}
	if len(args) > 0 {
		if args[0] > 0 && args[0] < 200 {
			v.pageSize = args[0]
		}
	}
	return v
}

// HasNext ...
func (it *DocIdsIterator) HasNext() bool {
	return it.pos < len(it.docIds)
}

// SleepDefaultNextBatch ...
func (it *DocIdsIterator) SleepDefaultNextBatch() []uint64 {
	interval := config.GetIntOr("llm.collection.patchDefaultSleep", 1000)
	return it.SleepNextBatch(time.Duration(interval) * time.Millisecond)
}

// SleepLongNextBatch ...
func (it *DocIdsIterator) SleepLongNextBatch() []uint64 {
	interval := config.GetIntOr("llm.collection.patchDefaultSleep", 2500)
	return it.SleepNextBatch(time.Duration(interval) * time.Millisecond)
}

// SleepNextBatch ...
func (it *DocIdsIterator) SleepNextBatch(sleep time.Duration) []uint64 {
	data := it.NextBatch()
	if it.pos != 0 && sleep != 0 {
		time.Sleep(sleep)
	}
	return data
}

// NextBatch ...
func (it *DocIdsIterator) NextBatch() []uint64 {
	if !it.HasNext() {
		return nil
	}

	end := it.pos + it.pageSize
	if end > len(it.docIds) {
		end = len(it.docIds)
	}

	batch := it.docIds[it.pos:end]
	it.pos = end

	return batch
}
