package model

import (
	"database/sql"
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TFeedback 用户反馈表
type TFeedback struct {
	ID          uint64             `gorm:"primaryKey;column:id" json:"id"`
	Question    string             `gorm:"column:question" json:"question"`                                               // 问题
	Answer      string             `gorm:"column:answer" json:"answer"`                                                   // 回答
	State       aipb.FeedbackState `gorm:"column:state;default:0" json:"state"`                                           // 状态
	CreateDate  time.Time          `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	UpdateDate  time.Time          `gorm:"column:update_date;autoUpdateTime;default:CURRENT_TIMESTAMP" json:"updateDate"` // 更新时间
	AssistantID uint64             `gorm:"column:assistant_id;default:2" json:"assistantId"`                              // 助手id

	QuestionID      uint64                    `gorm:"column:question_id;default:0"`                    // 问题ID
	AnswerID        uint64                    `gorm:"column:answer_id;default:0"`                      // 答案ID
	AnswerRating    aipb.FeedbackAnswerRating `gorm:"column:answer_rating;default:0"`                  // AI回答评价
	HitExpectedDoc  bool                      `gorm:"column:hit_expected_doc"`                         // 是否命中预期知识
	OpComment       *aipb.FeedbackComment     `gorm:"column:op_comment;serializer:json"`               // 分析备注
	MgmtFeedback    *aipb.FeedbackComment     `gorm:"column:mgmt_feedback;serializer:json"`            // 碳LIVE反馈
	MgmtComment     *aipb.FeedbackComment     `gorm:"column:mgmt_comment;serializer:json"`             // 碳LIVE备注
	HasUserFeedback bool                      `gorm:"column:has_user_feedback"`                        // 是否有用户反馈
	HasOpFeedback   bool                      `gorm:"column:has_op_feedback"`                          // 是否有运营反馈
	HasMgmtFeedback bool                      `gorm:"column:has_mgmt_feedback"`                        // 是否有碳LIVE反馈
	UserFeedbackBy  *basepb.Identity          `gorm:"column:user_feedback_by;serializer:json"`         // 用户反馈人
	OpFeedbackBy    *basepb.Identity          `gorm:"column:op_feedback_by;serializer:json"`           // 运营反馈人
	MgmtFeedbackBy  *basepb.Identity          `gorm:"column:mgmt_feedback_by;serializer:json"`         // 碳LIVE反馈人
	UserFeedbackAt  sql.NullTime              `gorm:"column:user_feedback_at"`                         // 用户反馈时间
	OpFeedbackAt    sql.NullTime              `gorm:"column:op_feedback_at"`                           // 运营反馈时间
	MgmtFeedbackAt  sql.NullTime              `gorm:"column:mgmt_feedback_at"`                         // 碳LIVE反馈时间
	CreateIdentity  *basepb.Identity          `gorm:"column:create_identity;serializer:json"`          // 创建人身份
	UpdateIdentity  *basepb.Identity          `gorm:"column:update_identity;serializer:json"`          // 更新人身份
	HandledBy       *basepb.Identity          `gorm:"column:handled_by;serializer:json"`               // 处理人身份
	HandledAt       sql.NullTime              `gorm:"column:handled_at" json:"handledAt"`              // 处理时间
	RegionCode      string                    `gorm:"column:region_code;default:''" json:"regionCode"` // 用户所在国家或地区编码

	Assistant        *TAssistant           `gorm:"foreignKey:id;references:assistant_id"`
	References       []*TFeedbackReference `gorm:"foreignKey:feedback_id"`
	ExpectedDocs     []*TFeedbackDoc       `gorm:"foreignKey:feedback_id"`
	ExpectedMgmtDocs []*TFeedbackDoc       `gorm:"foreignKey:feedback_id"`
	OriginalQuestion *TChatMessage         `gorm:"foreignKey:id;references:question_id"`
	OriginalAnswer   *TChatMessage         `gorm:"foreignKey:id;references:answer_id"`

	// 废弃字段
	CreateBy uint64 `gorm:"column:create_by;default:0" json:"createBy"` // 创建人
	UpdateBy uint64 `gorm:"column:update_by;default:0" json:"updateBy"` // 更新人
}

// TableName get sql table name.获取数据库表名
func (m *TFeedback) TableName() string {
	return "t_feedback"
}

// TFeedbackColumns get sql column name.获取数据库列名
var TFeedbackColumns = struct {
	ID          string
	Question    string
	Answer      string
	State       string
	CreateBy    string
	CreateDate  string
	UpdateBy    string
	UpdateDate  string
	AssistantID string
}{
	ID:          "id",
	Question:    "question",
	Answer:      "answer",
	State:       "state",
	CreateBy:    "create_by",
	CreateDate:  "create_date",
	UpdateBy:    "update_by",
	UpdateDate:  "update_date",
	AssistantID: "assistant_id",
}
