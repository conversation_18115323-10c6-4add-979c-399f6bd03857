package model

import (
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/services/pkg/db/cryptohelper"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// BeforeCreate gorm hook.
// 手机类型的需要加密
func (m *TAssistantAllowlist) BeforeCreate(*gorm.DB) error {
	var err error

	if m.Type == aipb.AssistantAllowlistType_ASSISTANT_ALLOWLIST_TYPE_PHONE && len(m.Value) > 0 {
		if m.Value, err = cryptohelper.AesEncrypt(m.Value); err != nil {
			return fmt.Errorf("encrypt phone: %w", err)
		}
	}

	return nil
}

// AfterFind gorm hook.
// 手机类型的需要解密
func (m *TAssistantAllowlist) AfterFind(*gorm.DB) error {
	var err error

	if m.Type == aipb.AssistantAllowlistType_ASSISTANT_ALLOWLIST_TYPE_PHONE && len(m.Value) > 0 {
		if m.Value, err = cryptohelper.AesDecrypt(m.Value); err != nil {
			return fmt.Errorf("decrypt phone: %w", err)
		}
	}

	return nil
}
