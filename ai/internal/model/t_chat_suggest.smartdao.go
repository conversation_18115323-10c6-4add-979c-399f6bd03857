// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatSuggest [...]
type TChatSuggest struct {
	MessageID uint64 `gorm:"column:message_id" json:"messageId"` // 消息id
	Mode      uint32 `gorm:"column:mode;default:0" json:"mode"`  // 建议问题生成模式
	Suggest   string `gorm:"column:suggest" json:"suggest"`      // 建议问题
}

// TableName get sql table name.获取数据库表名
func (m *TChatSuggest) TableName() string {
	return "t_chat_suggest"
}

// TChatSuggestColumns get sql column name.获取数据库列名
var TChatSuggestColumns = struct {
	MessageID string
	Mode      string
	Suggest   string
}{
	MessageID: "message_id",
	Mode:      "mode",
	Suggest:   "suggest",
}
