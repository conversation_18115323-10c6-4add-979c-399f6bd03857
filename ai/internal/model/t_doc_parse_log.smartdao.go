// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocParseLog 文件解析缓存表
type TDocParseLog struct {
	ID         uint64    `gorm:"primaryKey;column:id" json:"id"`                                 // 自增主键
	DocID      uint64    `gorm:"column:doc_id;default:0" json:"docId"`                           // doc_id
	Text       string    `gorm:"column:text" json:"text"`                                        // 内容
	CreateDate time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TDocParseLog) TableName() string {
	return "t_doc_parse_log"
}

// TDocParseLogColumns get sql column name.获取数据库列名
var TDocParseLogColumns = struct {
	ID         string
	DocID      string
	Text       string
	CreateDate string
}{
	ID:         "id",
	DocID:      "doc_id",
	Text:       "text",
	CreateDate: "create_date",
}
