package model

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func (m *TObjectLabel) AfterFind(tx *gorm.DB) error {
	// 如果使用了关联查询，使用 label 表的类型来覆盖
	if m.LabelInfo != nil {
		m.LabelType = m.LabelInfo.Type
	}
	return nil
}

// LabelMcSort 设置标签多选项的排序
func LabelMcSort(labels []*TObjectLabel) {
	// 设置多选项的排序
	mp := make(map[uint64]uint32)
	for _, label := range labels {
		if sort, ok := mp[label.LabelID]; ok {
			label.McSort = sort + 1
		}
		mp[label.LabelID] = label.McSort
	}
}

func (m *TObjectLabel) FromLabelPb(objId uint64, objType aipb.CustomLabelObjectType, label *aipb.CustomLabel) *TObjectLabel {
	m.ObjectID = objId
	m.LabelID = label.Id
	m.ObjectType = objType
	// TODO: 增加null判断
	if label.GetValue() == nil {
		m.DeletedAt = gorm.DeletedAt{}
		_ = m.DeletedAt.Scan(time.Now())
	}
	switch t := label.GetValue().GetAnyValue().(type) {
	case *aipb.LabelValue_IntValue:
		m.IntValue = t.IntValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT
	case *aipb.LabelValue_TimeValue:
		m.IntValue = t.TimeValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME
	case *aipb.LabelValue_YValue:
		m.IntValue = t.YValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y
	case *aipb.LabelValue_YmValue:
		m.IntValue = t.YmValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM
	case *aipb.LabelValue_YmdValue:
		m.IntValue = t.YmdValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD
	case *aipb.LabelValue_DatetimeValue:
		m.IntValue = t.DatetimeValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME
	case *aipb.LabelValue_UintValue:
		m.UINtValue = t.UintValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT
	case *aipb.LabelValue_FloatValue:
		m.FloatValue = t.FloatValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT
	case *aipb.LabelValue_EnumValue:
		m.StringValue = t.EnumValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM
	case *aipb.LabelValue_EnumMValue:
		m.StringValue = t.EnumMValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M
	case *aipb.LabelValue_TextValue:
		m.StringValue = t.TextValue
		m.LabelType = aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT
	}
	if label.Type != aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UNSPECIFIED {
		m.LabelType = label.Type
	}
	return m
}

func (m *TObjectLabel) ToLabelPb() *aipb.CustomLabel {
	var l *aipb.CustomLabel
	if m.LabelInfo != nil {
		l = m.LabelInfo.ToProto()
	} else {
		l = &aipb.CustomLabel{
			Id:   m.LabelID,
			Type: m.LabelType,
		}
	}
	switch m.LabelType {
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_IntValue{IntValue: m.IntValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_UintValue{UintValue: m.UINtValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_FloatValue{FloatValue: m.FloatValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_YValue{YValue: m.IntValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_YmValue{YmValue: m.IntValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_YmdValue{YmdValue: m.IntValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_DatetimeValue{DatetimeValue: m.IntValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_TimeValue{TimeValue: m.IntValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_EnumValue{EnumValue: m.StringValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_EnumMValue{EnumMValue: m.StringValue}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT:
		l.Value = &aipb.LabelValue{AnyValue: &aipb.LabelValue_TextValue{TextValue: m.StringValue}}
	}
	return l
}

// GetLabelValue 获取 label 的 value
func GetLabelValue(v *aipb.LabelValue) (aipb.CustomLabelType, any) {
	switch t := v.GetAnyValue().(type) {
	case *aipb.LabelValue_IntValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT, t.IntValue
	case *aipb.LabelValue_TimeValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME, t.TimeValue
	case *aipb.LabelValue_YValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y, t.YValue
	case *aipb.LabelValue_YmValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM, t.YmValue
	case *aipb.LabelValue_YmdValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD, t.YmdValue
	case *aipb.LabelValue_DatetimeValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME, t.DatetimeValue
	case *aipb.LabelValue_UintValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT, t.UintValue
	case *aipb.LabelValue_FloatValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT, t.FloatValue
	case *aipb.LabelValue_EnumValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM, t.EnumValue
	case *aipb.LabelValue_EnumMValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M, t.EnumMValue
	case *aipb.LabelValue_TextValue:
		return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT, t.TextValue
	}
	return aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UNSPECIFIED, nil
}

// ToLabelValue 转成label value
func ToLabelValue(labelType aipb.CustomLabelType, value any) *aipb.LabelValue {
	switch labelType {
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_IntValue{IntValue: value.(int64)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_UintValue{UintValue: value.(uint64)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_FloatValue{FloatValue: value.(float64)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_YValue{YValue: value.(int64)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_YmValue{YmValue: value.(int64)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_YmdValue{YmdValue: value.(int64)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_DatetimeValue{DatetimeValue: value.(int64)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_TimeValue{TimeValue: value.(int64)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_TextValue{TextValue: value.(string)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_EnumValue{EnumValue: value.(string)}}
	case aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M:
		return &aipb.LabelValue{AnyValue: &aipb.LabelValue_EnumMValue{EnumMValue: value.(string)}}
	}
	return nil
}

// ObjectLabelColumnMapping 不同标签类型对应的数据库列名
var ObjectLabelColumnMapping = map[aipb.CustomLabelType]string{
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_INT:      "int_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_UINT:     "uint_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_FLOAT:    "float_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TEXT:     "string_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM:     "string_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_ENUM_M:   "string_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATETIME: "int_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YMD: "int_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_YM:  "int_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_DATE_Y:   "int_value",
	aipb.CustomLabelType_CUSTOM_LABEL_TYPE_TIME:     "int_value",
}

func BuildLabelFilterQuery(labels []*aipb.LabelFilter) (string, []any) {
	// 单独把exist查询放在一组
	var existQuery []string
	var existArgs []any
	// 最终的query
	var query []string
	// 最终的args
	var args []any

	// not exist查询
	var notExistQuery []string
	var notExistArgs []any

	if len(labels) == 0 {
		return "", nil
	}

	for _, v := range labels {
		var operator string
		var labelValue any
		var labelType aipb.CustomLabelType

		switch v.Op {
		case aipb.LabelFilterOp_LABEL_FILTER_OP_EQ:
			labelType, labelValue = GetLabelValue(v.GetEq())
			if labelValue == nil {
				// 未绑定标签值查询
				notExistQuery = append(notExistQuery, fmt.Sprintf(
					"NOT EXISTS (SELECT 1 FROM t_object_label WHERE t_object_label.label_id = ? AND t_object_label.object_id = %s.id AND t_object_label.deleted_at IS NULL)",
					"{TABLE}", // 主表占位符
				))
				notExistArgs = append(notExistArgs, v.Id)
			} else {
				// 精确匹配
				operator = " = ?"
				existArgs = append(existArgs, v.Id, labelValue)
			}
		case aipb.LabelFilterOp_LABEL_FILTER_OP_IN:
			// IN (1, 2, null) 可以支持null in
			if v.GetIn() != nil {
				var inNotExistQ string
				var inArgs []any
				var inQuery []string
				for _, value := range v.GetIn() {
					_, labelValue = GetLabelValue(value)
					if labelValue == nil {
						// 包含null，用exist语句
						inNotExistQ = fmt.Sprintf(
							"NOT EXISTS (SELECT 1 FROM t_object_label WHERE t_object_label.label_id = ? AND t_object_label.object_id = %s.id AND t_object_label.deleted_at IS NULL)",
							"{TABLE}",
						)
					} else {
						labelType, labelValue = GetLabelValue(value)
						inArgs = append(inArgs, labelValue)
					}
				}
				if len(inArgs) > 0 {
					column := ObjectLabelColumnMapping[labelType]
					args = append(args, v.Id, inArgs)
					q := "t_object_label.label_id = ? and " + column + " IN ?"
					inQueryStr := fmt.Sprintf(
						"EXISTS (SELECT 1 FROM t_object_label WHERE %s and t_object_label.object_id = %s.id AND t_object_label.deleted_at IS NULL)",
						q,
						"{TABLE}", // 主表占位符
					)
					inQuery = append(inQuery, inQueryStr)
				}
				// IN 里面有null
				if len(inNotExistQ) > 0 {
					args = append(args, v.Id)
					inQuery = append(inQuery, inNotExistQ)
				}
				query = append(query, strings.Join(inQuery, " OR "))
			}
		case aipb.LabelFilterOp_LABEL_FILTER_OP_LTE:
			if v.GetLte() != nil {
				// 小于等于
				labelType, labelValue = GetLabelValue(v.GetLte())
				operator = " <= ?"
				existArgs = append(existArgs, v.Id, labelValue)
			}
		case aipb.LabelFilterOp_LABEL_FILTER_OP_GTE:
			if v.GetGte() != nil {
				// 大于等于
				labelType, labelValue = GetLabelValue(v.GetGte())
				operator = " >= ?"
				existArgs = append(existArgs, v.Id, labelValue)
			}
		case aipb.LabelFilterOp_LABEL_FILTER_OP_LIKE:
			if v.GetLike() != nil {
				// 模糊搜索
				labelType, labelValue = GetLabelValue(v.GetLike())
				if _, ok := labelValue.(string); ok {
					operator = " LIKE ?"
					existArgs = append(existArgs, v.Id, "%"+xorm.EscapeLikeWildcards(labelValue.(string))+"%")
				}
			}
		}

		if operator != "" {
			column := ObjectLabelColumnMapping[labelType]
			existQuery = append(existQuery, "t_object_label.label_id = ? and "+column+operator)
		}
	}
	if len(existQuery) > 0 {
		existQuery := fmt.Sprintf(
			"EXISTS (SELECT 1 FROM t_object_label WHERE %s and t_object_label.object_id = %s.id AND t_object_label.deleted_at IS NULL)",
			strings.Join(existQuery, " and "),
			"{TABLE}", // 主表占位符
		)
		// query = nil
		query = append(query, existQuery)
		args = append(args, existArgs...)
	}
	query = append(query, notExistQuery...)
	args = append(args, notExistArgs...)
	// 括号把每个条件括起来
	for i, q := range query {
		query[i] = fmt.Sprintf("(%s)", q)
	}
	return strings.Join(query, " and "), args
}

// WithLabels 关联查询标签
// func WithLabels(objectType aipb.CustomLabelObjectType, labels ...*aipb.LabelFilter) func(*gorm.DB) *gorm.DB {
// 	return func(db *gorm.DB) *gorm.DB {
// 		if len(labels) == 0 {
// 			return db.Preload("Labels", "object_type = ?", objectType)
// 		}
// 		db.Joins("left join t_object_label on t_object_label.object_id = t_doc.id and t_object_label.object_type = ?", objectType)
// 		query, args := BuildLabelFilterQuery(labels)
// 		if len(query) != 0 {
// 			db = db.Where(query, args...)
// 		}
// 		return db
// 	}
// }

// DocSource2CustomLabelObjectType doc文本文件根据 data_source得到自定义标签类型
func DocSource2CustomLabelObjectType(source aipb.DocDataSource) aipb.CustomLabelObjectType {
	switch source {
	case aipb.DocDataSource_DOC_DATA_SOURCE_COLLECTION:
		return aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_FILE
	case aipb.DocDataSource_DOC_DATA_SOURCE_TCLOUD_DOCUMENT:
		return aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE
	case aipb.DocDataSource_DOC_DATA_SOURCE_SQL:
		return aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_SQL_FILE
	}
	return aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_UNSPECIFIED
}

// 标签对象对应的原始表
var labelObjectTableMapping = map[aipb.CustomLabelObjectType]string{
	aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA:          "t_doc",
	aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_FILE:        "t_doc",
	aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE: "t_doc",
	aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_SQL_FILE:    "t_doc",
	aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_CHAT:        "t_chat",
}

// WithLabels 关联查询标签
func WithLabels(objectType aipb.CustomLabelObjectType, labelTenant uint64, labels []*aipb.LabelFilter, withLabelInfo bool, isOr ...bool) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		subQ := NewQuery[TCustomLabel](db.Statement.Context).DB().Where("tenant_id = ? and object_type = ?", labelTenant, objectType).Select("id")
		db.Preload("Labels", "object_type = ? and label_id in (?)", objectType, subQ)
		if withLabelInfo {
			db.Preload("Labels.LabelInfo", func(db *gorm.DB) *gorm.DB {
				return db.Scopes(LabelWithChain)
			})
		}
		query, args := BuildLabelFilterQuery(labels)

		// 替换占位符
		query = strings.ReplaceAll(query, "{TABLE}", labelObjectTableMapping[objectType])
		if len(query) != 0 {
			// subQ := NewQuery[TObjectLabel](db.Statement.Context).DB().Where("object_type = ?", objectType).
			// 	Where(query, args...).Select("object_id")
			if len(isOr) != 0 && isOr[0] {
				return db.Or(query, args...)
			}
			return db.Where(query, args...)
		}
		return db
	}
}

// WithLabels1 关联查询标签，为了支持null查询，使用join查询
func WithLabels1(objectType aipb.CustomLabelObjectType, labelTenant uint64, labels []*aipb.LabelFilter, withLabelInfo bool, isOr ...bool) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		subQ := NewQuery[TCustomLabel](db.Statement.Context).DB().Where("tenant_id = ? and object_type = ?", labelTenant, objectType).Select("id")
		db.Preload("Labels", "object_type = ? and label_id in (?)", objectType, subQ)
		if withLabelInfo {
			db.Preload("Labels.LabelInfo", func(db *gorm.DB) *gorm.DB {
				return db.Scopes(LabelWithChain)
			})
		}

		// join
		db.Joins("LEFT JOIN "+
			" (select * from t_object_label where t_object_label.object_type = ?) tol"+
			" ON tol.object_id = "+labelObjectTableMapping[objectType]+".id ", objectType)

		query, args := BuildLabelFilterQuery(labels)
		if len(query) != 0 {
			subQ := NewQuery[TObjectLabel](db.Statement.Context).DB().Where("object_type = ?", objectType).
				Where(query, args...).Select("object_id")
			if len(isOr) != 0 && isOr[0] {
				return db.Or("id in (?)", subQ)
			}
			return db.Where("id in (?)", subQ)
		}
		return db
	}
}

// OrderByLabel 按照标签排序
func OrderByLabel(objType aipb.CustomLabelObjectType, order *aipb.OrderByLabel) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if order == nil || order.Id == 0 {
			return db
		}
		ctx := db.Statement.Context
		labelInfo, err := NewQuery[TCustomLabel](ctx).FindByKey(order.Id)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			db.Error = err
			return nil
		}
		if labelInfo == nil || labelInfo.ID == 0 {
			return db
		}
		column := ObjectLabelColumnMapping[labelInfo.Type]

		// join之后排序
		return db.Joins("LEFT JOIN "+
			" (select * from t_object_label where t_object_label.object_type = ? AND t_object_label.label_id = ?) tol"+
			" ON tol.object_id = "+labelObjectTableMapping[objType]+".id ", objType, labelInfo.ID).
			Order(clause.OrderByColumn{
				Column: clause.Column{Name: "tol." + column},
				Desc:   order.Desc,
			})
	}
}
