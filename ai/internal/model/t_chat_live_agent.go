package model

import aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"

func (m *TChatLiveAgent) ToPb() *aipb.ChatLiveAgent {
	if m == nil {
		return nil
	}
	return &aipb.ChatLiveAgent{
		Id:          m.ID,
		Username:    m.Username,
		Nickname:    m.Nickname,
		Status:      m.Status,
		CreateDate:  toTimestamppb(m.CreateDate),
		Order:       int32(m.Order),
		AssistantId: m.AssistantID,
	}
}
