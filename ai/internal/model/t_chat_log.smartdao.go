// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatLog chat请求日志表
type TChatLog struct {
	MessageID      uint64     `gorm:"column:message_id;default:0" json:"messageId"`                   // 答案ID
	SQLQuery       string     `gorm:"column:sql_query" json:"sqlQuery"`                               // sql结果
	Enhancement    string     `gorm:"column:enhancement;default:''" json:"enhancement"`               // doc,search
	Gpt            string     `gorm:"column:gpt" json:"gpt"`                                          // gpt内容
	Ref            string     `gorm:"column:ref" json:"ref"`                                          // ref结果
	Code           uint64     `gorm:"column:code;default:0" json:"code"`                              // code
	CreateDate     time.Time  `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	MessageType    int32      `gorm:"column:message_type;default:0" json:"messageType"`               // 1.sql 2.doc 3.search 11.vision
	EndTime        time.Time  `gorm:"column:end_time" json:"endTime"`                                 // 结束时间
	StartTime      time.Time  `gorm:"column:start_time" json:"startTime"`                             // 开始时间
	ConfigSnapshot string     `gorm:"column:config_snapshot" json:"configSnapshot"`
	RequestText    string     `gorm:"column:request_text" json:"requestText"`          // 多轮对话请求参数text
	TaskIndex      int32      `gorm:"column:task_index;default:0" json:"taskIndex"`    // 任务索引
	PromptType     string     `gorm:"column:prompt_type;default:''" json:"promptType"` // 问题类型
	FetchRespTime  *time.Time `gorm:"column:fetch_resp_time" json:"fetchRespTime"`     // 请求响应时间
}

// TableName get sql table name.获取数据库表名
func (m *TChatLog) TableName() string {
	return "t_chat_log"
}

// TChatLogColumns get sql column name.获取数据库列名
var TChatLogColumns = struct {
	MessageID      string
	SQLQuery       string
	Enhancement    string
	Gpt            string
	Ref            string
	Code           string
	CreateDate     string
	MessageType    string
	EndTime        string
	StartTime      string
	ConfigSnapshot string
	RequestText    string
	TaskIndex      string
	PromptType     string
	FetchRespTime  string
}{
	MessageID:      "message_id",
	SQLQuery:       "sql_query",
	Enhancement:    "enhancement",
	Gpt:            "gpt",
	Ref:            "ref",
	Code:           "code",
	CreateDate:     "create_date",
	MessageType:    "message_type",
	EndTime:        "end_time",
	StartTime:      "start_time",
	ConfigSnapshot: "config_snapshot",
	RequestText:    "request_text",
	TaskIndex:      "task_index",
	PromptType:     "prompt_type",
	FetchRespTime:  "fetch_resp_time",
}
