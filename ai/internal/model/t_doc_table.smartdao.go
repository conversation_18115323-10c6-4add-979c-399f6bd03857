package model

import (
	"time"
)

// TDocTableOversize 存储文档表格超长信息
type TDocTableOversize struct {
	ID                uint64    `gorm:"primaryKey;column:id"`
	DocID             uint64    `gorm:"index;column:doc_id"`               // 文档ID
	AssistantID       uint64    `gorm:"index;column:assistant_id"`         // 助手ID
	TablePosition     int       `gorm:"column:table_position"`             // 表格在文档中的位置（第几个表格）
	TableTitle        string    `gorm:"column:table_title;type:text"`      // 表格标题
	TableHeader       string    `gorm:"column:table_header;type:text"`     // 表格表头
	IsHeaderOverSized bool      `gorm:"column:is_header_over_sized"`       // 表头是否超过长度限制
	HeaderLength      int       `gorm:"column:header_length"`              // 表头长度
	HeaderMaxLength   int       `gorm:"column:header_max_length"`          // 表头最大允许长度
	CreateDate        time.Time `gorm:"column:create_date;autoCreateTime"` // 创建时间
	UpdateDate        time.Time `gorm:"column:update_date;autoUpdateTime"` // 更新时间
}

// TableName 设置表名
func (TDocTableOversize) TableName() string {
	return "t_doc_table_oversize"
}

// TDocTableInfoColumns 获取数据库列名
var TDocTableInfoColumns = struct {
	ID                string
	DocID             string
	AssistantID       string
	TablePosition     string
	TableTitle        string
	TableHeader       string
	IsHeaderOverSized string
	HeaderLength      string
	HeaderMaxLength   string
	CreateDate        string
	UpdateDate        string
}{
	ID:                "id",
	DocID:             "doc_id",
	AssistantID:       "assistant_id",
	TablePosition:     "table_position",
	TableTitle:        "table_title",
	TableHeader:       "table_header",
	IsHeaderOverSized: "is_header_over_sized",
	HeaderLength:      "header_length",
	HeaderMaxLength:   "header_max_length",
	CreateDate:        "create_date",
	UpdateDate:        "update_date",
}
