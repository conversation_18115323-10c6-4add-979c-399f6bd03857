// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatShareAccess 分享访问记录表
type TChatShareAccess struct {
	ID          uint64    `gorm:"primaryKey;column:id" json:"id"`                                 // 自增id
	ShareID     string    `gorm:"column:share_id" json:"shareId"`                                 // 关联的分享id
	AccessBy    uint64    `gorm:"column:access_by" json:"accessBy"`                               // 访问者用户id
	AccessDate  time.Time `gorm:"column:access_date;default:CURRENT_TIMESTAMP" json:"accessDate"` // 访问时间
	IsContinued int32     `gorm:"column:is_continued;default:0" json:"isContinued"`               // 是否点击了"接着聊"
	NewChatID   uint64    `gorm:"column:new_chat_id" json:"newChatId"`                            // 新建的会话id(如果点击了接着聊)
}

// TableName get sql table name.获取数据库表名
func (m *TChatShareAccess) TableName() string {
	return "t_chat_share_access"
}

// TChatShareAccessColumns get sql column name.获取数据库列名
var TChatShareAccessColumns = struct {
	ID          string
	ShareID     string
	AccessBy    string
	AccessDate  string
	AccessIP    string
	UserAgent   string
	IsContinued string
	NewChatID   string
}{
	ID:          "id",
	ShareID:     "share_id",
	AccessBy:    "access_by",
	AccessDate:  "access_date",
	AccessIP:    "access_ip",
	UserAgent:   "user_agent",
	IsContinued: "is_continued",
	NewChatID:   "new_chat_id",
}
