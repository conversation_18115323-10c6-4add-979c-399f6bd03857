// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocParseCache 文件解析缓存表
type TDocParseCache struct {
	ID         uint64            `gorm:"primaryKey;column:id" json:"id"`   // 自增主键
	Md5        string            `gorm:"column:md5;default:''" json:"md5"` // md5
	Text       string            `gorm:"column:text" json:"text"`          // 内容
	DocID      uint64            `gorm:"column:doc_id;default:0" json:"docId"`
	ParseMode  aipb.DocParseMode `gorm:"column:parse_mode;default:1" json:"parseMode"`
	UpdateDate time.Time         `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"` // 最近更新时间
	CreateDate time.Time         `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TDocParseCache) TableName() string {
	return "t_doc_parse_cache"
}

// TDocParseCacheColumns get sql column name.获取数据库列名
var TDocParseCacheColumns = struct {
	ID         string
	Md5        string
	Text       string
	DocID      string
	ParseMode  string
	UpdateDate string
	CreateDate string
}{
	ID:         "id",
	Md5:        "md5",
	Text:       "text",
	DocID:      "doc_id",
	ParseMode:  "parse_mode",
	UpdateDate: "update_date",
	CreateDate: "create_date",
}
