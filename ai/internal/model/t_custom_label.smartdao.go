// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TCustomLabel ai对话标签
type TCustomLabel struct {
	ID         uint64         `gorm:"primaryKey;column:id" json:"id"`                                 // 自增主键
	Type aipb.CustomLabelType         `gorm:"column:type;default:1" json:"type"`                      // 值类型
	Key        string         `gorm:"column:key;default:''" json:"key"`                               // 标签名
	Value      string         `gorm:"column:value;default:''" json:"value"`                           // 可选标签值
	CreateBy   uint64         `gorm:"column:create_by;default:0" json:"createBy"`                     // 创建人
	UpdateBy   uint64         `gorm:"column:update_by;default:0" json:"updateBy"`                     // 最近更新人
	CreateDate time.Time      `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	UpdateDate time.Time      `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"` // 最近更新时间
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at" json:"deletedAt"`                             // 删除时间
	TenantID   uint64         `gorm:"column:tenant_id;default:0" json:"tenantId"`                               // 租户id
	ObjectType aipb.CustomLabelObjectType         `gorm:"column:object_type;default:0" json:"objectType"` // 被标签的类型 1：chat对话 2：知识库文档
	NextLabel  string         `gorm:"column:next_label;default:''" json:"nextLabel"`                  // 下一个标签,指向固定表头
	NextLabelId uint64        `gorm:"column:next_label_id;default:0" json:"nextLabelId"`
	// Sort       uint32         `gorm:"column:sort;default:0" json:"sort"`
}

// TableName get sql table name.获取数据库表名
func (m *TCustomLabel) TableName() string {
	return "t_custom_label"
}

// TCustomLabelColumns get sql column name.获取数据库列名
var TCustomLabelColumns = struct {
	ID         string
	StringType string
	Key        string
	Value      string
	CreateBy   string
	UpdateBy   string
	CreateDate string
	UpdateDate string
	DeletedAt  string
	TenantID   string
	ObjectType string
}{
	ID:         "id",
	StringType: "string_type",
	Key:        "key",
	Value:      "value",
	CreateBy:   "create_by",
	UpdateBy:   "update_by",
	CreateDate: "create_date",
	UpdateDate: "update_date",
	DeletedAt:  "deleted_at",
	TenantID:   "tenant_id",
	ObjectType: "object_type",
}
