// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TCollectionDoc [...]
type TCollectionDoc struct {
	DocID        uint64 `gorm:"column:doc_id" json:"docId"`               // 文档id
	CollectionID uint64 `gorm:"column:collection_id" json:"collectionId"` // collection的id
}

// TableName get sql table name.获取数据库表名
func (m *TCollectionDoc) TableName() string {
	return "t_collection_doc"
}

// TCollectionDocColumns get sql column name.获取数据库列名
var TCollectionDocColumns = struct {
	DocID        string
	CollectionID string
}{
	DocID:        "doc_id",
	CollectionID: "collection_id",
}
