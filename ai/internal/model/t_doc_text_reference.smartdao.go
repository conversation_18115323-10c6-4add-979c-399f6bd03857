// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocTextReference 文档纯文本参考资料表
type TDocTextReference struct {
	ID         uint64    `gorm:"primaryKey;column:id" json:"id"`                                 // 主键ID
	DocID      uint64    `gorm:"column:doc_id" json:"docId"`                                     // doc的id
	Text       string    `gorm:"column:text" json:"text"`                                        // 纯文本参考资料内容
	SortOrder  int32     `gorm:"column:sort_order;default:0" json:"sortOrder"`                   // 排序字段
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"createTime"` // 创建时间
}

// TableName get sql table name.获取数据库表名
func (m *TDocTextReference) TableName() string {
	return "t_doc_text_reference"
}

// TDocTextReferenceColumns get sql column name.获取数据库列名
var TDocTextReferenceColumns = struct {
	ID         string
	DocID      string
	Text       string
	SortOrder  string
	CreateTime string
}{
	ID:         "id",
	DocID:      "doc_id",
	Text:       "text",
	SortOrder:  "sort_order",
	CreateTime: "create_time",
}
