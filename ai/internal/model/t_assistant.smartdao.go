package model

import (
	"database/sql"
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TAssistant ai助手
type TAssistant struct {
	// 基础字段
	ID                 uint64                            `gorm:"primaryKey;column:id" json:"id"`                                                  // 助手ID
	Channel            aipb.AssistantChannel             `gorm:"column:channel" json:"channel,omitempty"`                                         // 渠道：1 微信，2 碳LIVE_Web，3 Web，4 WhatsApp
	Name               string                            `gorm:"column:name;default:''" json:"name,omitempty"`                                    // 中文名称
	NameEn             string                            `gorm:"column:name_en;default:''" json:"nameEn,omitempty"`                               // 英文名称
	Nickname           string                            `gorm:"column:nickname;default:''" json:"nickname,omitempty"`                            // 助手昵称
	NicknameEn         string                            `gorm:"column:nickname_en;default:''" json:"nicknameEn,omitempty"`                       // 助手昵称（英文）
	AvatarUrl          string                            `gorm:"column:avatar_url;default:''" json:"avatarUrl,omitempty"`                         // 头像URL
	SearchDebug        bool                              `gorm:"column:search_debug;default:false" json:"searchDebug,omitempty"`                  // 搜索测试
	VisibleChainConfig *aipb.AssistantVisibleChainConfig `gorm:"column:visible_chain_config;serializer:json" json:"visibleChainConfig,omitempty"` // 链路查询配置
	FieldManageConfig  *aipb.AssistantFieldManageConfig  `gorm:"column:field_manage_config;serializer:json" json:"FieldManageConfig,omitempty"`   // 参数管理权限配置
	CreateBy           *basepb.Identity                  `gorm:"column:create_by;serializer:json" json:"createBy,omitempty"`                      // 创建人
	CreateDate         time.Time                         `gorm:"column:create_date;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createDate"`   // 创建时间
	UpdateBy           *basepb.Identity                  `gorm:"column:update_by;serializer:json" json:"updateBy,omitempty"`                      // 更新人
	UpdateDate         time.Time                         `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"`                  // 最近更新时间
	DeletedAt          gorm.DeletedAt                    `gorm:"column:deleted_at" json:"deletedAt,omitempty"`                                    // 删除时间
	Enabled            bool                              `gorm:"column:enabled;default:0" json:"enabled,omitempty"`                               // 助手是否启用：0/1
	EnabledAt          sql.NullTime                      `gorm:"column:enabled_at" json:"enabledAt,omitempty"`                                    // 助手启用/禁用时间
	IsDraft            bool                              `gorm:"column:is_draft" json:"isDraft,omitempty"`                                        // 是否为草稿：0否|1是
	BatchNo            string                            `gorm:"column:batch_no" json:"batchNo,omitempty"`                                        // 批次号

	// 渠道配置字段
	AppId                  string                                `gorm:"column:app_id;default:'0'" json:"appId,omitempty"`                                         // 应用ID（为碳LIVE应用、WEB应用分配）
	SwitchAssistantId      uint64                                `gorm:"column:switch_assistant_id;default:0" json:"switchAssistantId,omitempty"`                  // 切换助手ID
	AssistantLang          string                                `gorm:"column:assistant_lang;default:''" json:"assistantLang,omitempty"`                          // 助手语言
	SystemLanguages        []string                              `gorm:"column:system_languages;serializer:json" json:"systemLanguages,omitempty"`                 // 系统语言
	FeedbackEnabled        bool                                  `gorm:"column:feedback_enabled;default:0" json:"feedbackEnabled,omitempty"`                       // 是否启用用户教学反馈：0/1
	ChatIdleDuration       int32                                 `gorm:"column:chat_idle_duration;default:0" json:"chatIdleDuration,omitempty"`                    // 会话闲置超时时间（分钟）
	WebsiteConfig          *aipb.AssistantWebsiteConfig          `gorm:"column:website_config;serializer:json" json:"websiteConfig,omitempty"`                     // 网站配置
	MiniprogramConfig      *aipb.AssistantMiniprogramConfig      `gorm:"column:miniprogram_config;serializer:json" json:"miniprogram,omitempty"`                   // 小程序配置
	WeixinDevelopConfig    *aipb.AssistantWeixinDevelopConfig    `gorm:"column:weixin_develop_config;serializer:json" json:"weixinDevelopConfig,omitempty"`        // 微信开发配置
	WhatsappDevelopConfig  *aipb.AssistantWhatsappDevelopConfig  `gorm:"column:whatsapp_develop_config;serializer:json" json:"whatsappDevelopConfig,omitempty"`    // WhatsApp开发配置
	WelcomeMessageConfig   *aipb.AssistantWelcomeMessageConfig   `gorm:"column:welcome_message_config;serializer:json" json:"welcomeMessageConfig,omitempty"`      // 欢迎语配置
	PresetQuestionConfig   *aipb.AssistantPresetQuestionConfig   `gorm:"column:preset_question_config;serializer:json" json:"presetQuestionConfig,omitempty"`      // 预设问题配置
	KefuConfig             *aipb.AssistantKefuConfig             `gorm:"column:kefu_config;serializer:json" json:"kefuConfig,omitempty"`                           // 人工客服配置
	RatingScaleReplyConfig *aipb.AssistantRatingScaleReplyConfig `gorm:"column:rating_scale_reply_config;serializer:json" json:"ratingScaleReplyConfig,omitempty"` // 满意度回复配置
	InteractiveCodeConfig  *aipb.AssistantInteractiveCodeConfig  `gorm:"column:interactive_code_config;serializer:json" json:"interactiveCodeConfig,omitempty"`    // 互动暗号配置
	GraphParseConfig       *aipb.AssistantGraphParseConfig       `gorm:"column:graph_parse_config;serializer:json" json:"graphParseConfig,omitempty"`              // 图谱解析配置
	BriefIntro             string                                `gorm:"column:brief_intro" json:"briefIntro,omitempty"`                                           // 一句话介绍
	DetailIntro            string                                `gorm:"column:detail_intro" json:"detailIntro,omitempty"`                                         // 助手介绍
	ShowInList             bool                                  `gorm:"column:show_in_list;default:0" json:"showInList,omitempty"`                                // 是否在助手列表展示
	AllowlistConfig        *aipb.AssistantAllowlistConfig        `gorm:"column:allowlist_config;serializer:json" json:"allowlistConfig,omitempty"`                 // 白名单配置

	// AI参数配置字段
	PromptPrefix        string                             `gorm:"column:prompt_prefix;default:''" json:"promptPrefix,omitempty"`                     // 提示词
	Model               string                             `gorm:"column:model;default:''" json:"model,omitempty"`                                    // 对话模型
	HistoryRounds       int32                              `gorm:"column:history_rounds;default:1" json:"historyRounds,omitempty"`                    // 对话轮数
	CloseSearch         bool                               `gorm:"column:close_search;default:0" json:"closeSearch,omitempty"`                        // 是否关闭搜索增强
	SearchEngine        string                             `gorm:"column:search_engine;default:''" json:"searchEngine"`                               // 搜索引擎
	SearchTopN          int32                              `gorm:"column:search_top_n;default:5" json:"searchTopN"`                                   // 互联网Top_N
	ChatOrSqlConfig     *aipb.AssistantChatOrSqlConfig     `gorm:"column:chat_or_sql_config;serializer:json" json:"chatOrSqlConfig,omitempty"`        // ChatOrSql配置
	AskSuggestionConfig *aipb.AssistantAskSuggestionConfig `gorm:"column:ask_suggestion_config;serializer:json" json:"askSuggestionConfig,omitempty"` // 问题建议配置
	TextWeight          float32                            `gorm:"column:text_weight;default:0" json:"textWeight"`                                    // 关键词搜索权重
	MissReply           string                             `gorm:"column:miss_reply;default:''" json:"missReply"`                                     // 未命中知识库自动回复
	CloseRefMark        bool                               `gorm:"column:close_ref_mark;default:1" json:"closeRefMark"`                               // 关闭回答中ref的标记
	ShowThink           bool                               `gorm:"column:show_think;default:0" json:"showThink"`                                      // 是否展示思考过程
	TextRecallTopN      int32                              `gorm:"column:text_recall_top_n;default:0" json:"textRecallTopN"`                          // 关键词召回条数
	TextRecallQuery     aipb.TextRecallQuery               `gorm:"column:text_recall_query;default:0" json:"textRecallQuery"`                         // QA关键词召回目标
	TextRecallPattern   aipb.TextRecallPattern             `gorm:"column:text_recall_pattern;default:0" json:"textRecallPattern"`                     // 关键词召回模式
	TextRecallSlop      int32                              `gorm:"column:text_recall_slop;default:0" json:"textRecallSlop"`                           // 允许平移距离
	Temperature         float32                            `gorm:"column:temperature;default:0" json:"temperature"`                                   // 温度
	QuestionTypeConfig  *aipb.AssistantQuestionTypeConfig  `gorm:"column:question_type_config;serializer:json" json:"QuestionTypeConfig"`             // 问题分类配置
	CleanChunks         bool                               `gorm:"column:clean_chunks" json:"cleanChunks"`                                            // 自动过滤

	// 关系
	Admins               []*TAssistantAdmin            `gorm:"foreignKey:assistant_id" json:"admins,omitempty"`                                                              // 管理员
	AssistantCollections []*TAssistantCollection       `gorm:"foreignKey:assistant_id" json:"assistantCollections,omitempty"`                                                // 知识库配置
	LiveAgents           []*TChatLiveAgent             `gorm:"foreignKey:assistant_id" json:"liveAgents,omitempty"`                                                          // 人工坐席列表
	Collections          []*TCollectionJoinedAssistant `gorm:"many2many:t_assistant_collection;joinForeignKey:assistant_id;joinReferences:collection_id" json:"collections"` // 知识库
	TermsConfirmation    *VAssistantTerms              `gorm:"foreignKey:assistant_id" json:"termsConfirmation"`                                                             // 确认协议
	Allowlist            []*TAssistantAllowlist        `gorm:"foreignKey:assistant_id" json:"allowlist,omitempty"`                                                           // 白名单

	// 迁移字段
	WelcomeMsg      *aipb.AssistantWelcomeMsg          `gorm:"column:welcome_msg;serializer:json" json:"welcomeMsg,omitempty"`          // 欢迎语
	WechatAutoReply string                             `gorm:"column:wechat_auto_reply;default:''" json:"wechatAutoReply,omitempty"`    // 微信客服用户主动发消息的自动回复（24小时）
	AppCode         string                             `gorm:"column:app_code;default:''" json:"appCode,omitempty"`                     // 应用唯一标识（如 微信客服ID）
	WebsiteRoute    string                             `gorm:"column:website_route;default:''" json:"websiteRoute,omitempty"`           // 网站路由
	CorpID          string                             `gorm:"column:corp_id;default:''" json:"corpId,omitempty"`                       // 应用唯一标识（如 微信客服ID）
	RatingScaleMsg  *aipb.AssistantRatingScaleMsg      `gorm:"column:rating_scale_msg;serializer:json" json:"ratingScaleMsg,omitempty"` // 评分消息配置
	AtlasParse      bool                               `gorm:"column:atlas_parse;default:0" json:"atlasParse,omitempty"`                // 是否支持图谱解析 0:否 1:是
	ChatOrSQL       int32                              `gorm:"column:chat_or_sql;default:0" json:"chatOrSql,omitempty"`                 // 是否走sql逻辑，0:否，1:是
	SqlModel        string                             `gorm:"column:sql_model;default:''" json:"sql_model,omitempty"`                  // chat or sql模型
	SuggestPrefix   string                             `gorm:"column:suggest_prompt;default:''" json:"suggestPrefix,omitempty"`         // 问题建议Prompt
	SuggestCount    int32                              `gorm:"column:suggest_count;default:1" json:"suggestCount,omitempty"`            // 问题建议数量
	LiveAgentMsg    *aipb.AssistantLiverAgentConfigMsg `gorm:"column:live_agent_msg;serializer:json" json:"liveAgentMsg,omitempty"`     // 人工相关配置

	// 保留字段
	SearchRewrite          int32          `gorm:"column:search_rewrite;default:0" json:"searchRewrite,omitempty"`            // 是否开搜索关键词，0:否，1:是
	VisionModel            string         `gorm:"column:vision_model;default:'hunyuan-vision'" json:"visionModel,omitempty"` // 多模态模型
	MultimodalPromptPrefix datatypes.JSON `gorm:"column:multimodal_prompt_prefix" json:"multimodalPromptPrefix,omitempty"`   // 多模态ai提示词前缀
	DefaultPushMsg         datatypes.JSON `gorm:"column:default_push_msg" json:"defaultPushMsg,omitempty"`                   // 默认答案推送内容配置
	NoPermissionMsg        datatypes.JSON `gorm:"column:no_permission_msg" json:"noPermissionMsg,omitempty"`                 // 无权限时的回答

	// 废弃字段
	ShowType   int32  `gorm:"column:show_type;default:1" json:"showType,omitempty"`      // 显示类型 1 正常 2 使用中但不显示
	Manual     string `gorm:"column:manual;default:''" json:"manual,omitempty"`          // 人工坐席账户
	SystemLang string `gorm:"column:system_lang;default:''" json:"systemLang,omitempty"` // 系统语言
}

// TableName get sql table name.获取数据库表名
func (m *TAssistant) TableName() string {
	return "t_assistant"
}

// TAssistantColumns get sql column name.获取数据库列名
var TAssistantColumns = struct {
	ID                     string
	Channel                string
	Name                   string
	NameEn                 string
	Nickname               string
	NicknameEn             string
	AvatarUrl              string
	SearchDebug            string
	VisibleChainConfig     string
	FieldManageConfig      string
	CreateBy               string
	CreateDate             string
	UpdateBy               string
	UpdateDate             string
	DeletedAt              string
	Enabled                string
	EnabledAt              string
	IsDraft                string
	BatchNo                string
	AppId                  string
	SwitchAssistantId      string
	AssistantLang          string
	SystemLang             string
	SystemLanguages        string
	FeedbackEnabled        string
	ChatIdleDuration       string
	WebsiteConfig          string
	MiniprogramConfig      string
	WeixinDevelopConfig    string
	WhatsappDevelopConfig  string
	WelcomeMessageConfig   string
	PresetQuestionConfig   string
	KefuConfig             string
	RatingScaleReplyConfig string
	InteractiveCodeConfig  string
	GraphParseConfig       string
	BriefIntro             string
	DetailIntro            string
	PromptPrefix           string
	Model                  string
	HistoryRounds          string
	CloseSearch            string
	SearchEngine           string
	SearchTopN             string
	ChatOrSqlConfig        string
	AskSuggestionConfig    string
	TextWeight             string
	MissReply              string
	ShowThink              string
	TextRecallTopN         string
	Temperature            string
	QuestionTypeConfig     string
	ShowInList             string
	AllowlistConfig        string
	CleanChunks            string
	TextRecallQuery        string
	TextRecallPattern      string
	TextRecallSlop         string
}{
	ID:                     "id",
	Channel:                "channel",
	Name:                   "name",
	NameEn:                 "name_en",
	Nickname:               "nickname",
	NicknameEn:             "nickname_en",
	AvatarUrl:              "avatar_url",
	SearchDebug:            "search_debug",
	VisibleChainConfig:     "visible_chain_config",
	FieldManageConfig:      "field_manage_config",
	CreateBy:               "create_by",
	CreateDate:             "create_date",
	UpdateBy:               "update_by",
	UpdateDate:             "update_date",
	DeletedAt:              "deleted_at",
	IsDraft:                "is_draft",
	BatchNo:                "batch_no",
	Enabled:                "enabled",
	EnabledAt:              "enabled_at",
	AppId:                  "app_id",
	SwitchAssistantId:      "switch_assistant_id",
	AssistantLang:          "assistant_lang",
	SystemLang:             "system_lang",
	SystemLanguages:        "system_languages",
	FeedbackEnabled:        "feedback_enabled",
	ChatIdleDuration:       "chat_idle_duration",
	WebsiteConfig:          "website_config",
	MiniprogramConfig:      "miniprogram_config",
	WeixinDevelopConfig:    "weixin_develop_config",
	WhatsappDevelopConfig:  "whatsapp_develop_config",
	WelcomeMessageConfig:   "welcome_message_config",
	PresetQuestionConfig:   "preset_question_config",
	KefuConfig:             "kefu_config",
	RatingScaleReplyConfig: "rating_scale_reply_config",
	InteractiveCodeConfig:  "interactive_code_config",
	GraphParseConfig:       "graph_parse_config",
	BriefIntro:             "brief_intro",
	DetailIntro:            "detail_intro",
	PromptPrefix:           "prompt_prefix",
	Model:                  "model",
	HistoryRounds:          "history_rounds",
	CloseSearch:            "close_search",
	SearchEngine:           "search_engine",
	SearchTopN:             "search_top_n",
	ChatOrSqlConfig:        "chat_or_sql_config",
	AskSuggestionConfig:    "ask_suggestion_config",
	TextWeight:             "text_weight",
	MissReply:              "miss_reply",
	ShowThink:              "show_think",
	TextRecallTopN:         "text_recall_top_n",
	Temperature:            "temperature",
	QuestionTypeConfig:     "question_type_config",
	ShowInList:             "show_in_list",
	AllowlistConfig:        "allowlist_config",
	CleanChunks:            "clean_chunks",
	TextRecallQuery:        "text_recall_query",
	TextRecallPattern:      "text_recall_pattern",
	TextRecallSlop:         "text_recall_slop",
}
