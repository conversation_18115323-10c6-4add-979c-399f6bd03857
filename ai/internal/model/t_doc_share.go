package model

import (
	"slices"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"

	"gorm.io/gorm"
)

// DocWithShareTargetIdFilter 查询分享给团队/个人的文档
func DocWithShareTargetIdFilter(targetIds []uint64, shareType aipb.DocShareReceiverType, isOr ...bool) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(targetIds) == 0 {
			return tx
		}
		// 扩号扩住的条件组
		quoteQuery := NewQuery[TDoc](tx.Statement.Context).DB()

		hasZero := slices.Contains(targetIds, 0)
		if hasZero {
			quoteQuery.Scopes(DocWithNoSharedTargetIdFilter(shareType, true))
			targetIds = slices.DeleteFunc(targetIds, func(id uint64) bool {
				return id == 0
			})
		}

		// 去除0后，如果还有值，则构建子查询
		if len(targetIds) != 0 {
			// 构建子查询
			subQuery := NewQuery[TDocShare](tx.Statement.Context).
				DB().
				Select("1").
				Where("target_id IN (?)", targetIds).
				Where("share_type = ?", shareType).
				Where("doc_id = t_doc.id")

			// 使用 EXISTS 判断子查询是否存在
			quoteQuery.Or("EXISTS (?)", subQuery)
		}

		if len(isOr) > 0 && isOr[0] {
			return tx.Or(quoteQuery)
		}
		return tx.Where(quoteQuery)
	}
}

// DocWithNoSharedTargetIdFilter 查询没有分享给团队/个人的文档
func DocWithNoSharedTargetIdFilter(shareType aipb.DocShareReceiverType, isOr ...bool) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		subQuery := NewQuery[TDocShare](tx.Statement.Context).
			DB().
			Select("1").
			Where("share_type = ?", shareType).
			Where("doc_id = t_doc.id")
		if len(isOr) > 0 && isOr[0] {
			return tx.Or("NOT EXISTS (?)", subQuery)
		}
		return tx.Where(" NOT EXISTS (?)", subQuery)
	}
}

// DocWithSharedTeamUserFilter 查询分享给团队/个人的文档,默认是或的关系
func DocWithSharedTeamUserFilter(teamIds []uint64, userIds []uint64) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		tx.Scopes(DocWithShareTargetIdFilter(teamIds, aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM))
		tx.Scopes(DocWithShareTargetIdFilter(userIds, aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER, true))
		return tx
	}
}
