// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatSuggestLog suggest请求日志表
type TChatSuggestLog struct {
	MessageID      uint64    `gorm:"column:message_id;default:0" json:"messageId"` // 答案ID
	ConfigSnapshot string    `gorm:"column:config_snapshot" json:"configSnapshot"`
	Collections    string    `gorm:"column:collections" json:"collections"`
	Gpt            string    `gorm:"column:gpt" json:"gpt"`                                          // gpt内容
	CreateDate     time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	RequestType    string    `gorm:"column:request_type;default:''" json:"requestType"`              // chat,search_collection
}

// TableName get sql table name.获取数据库表名
func (m *TChatSuggestLog) TableName() string {
	return "t_chat_suggest_log"
}

// TChatSuggestLogColumns get sql column name.获取数据库列名
var TChatSuggestLogColumns = struct {
	MessageID      string
	ConfigSnapshot string
	Collections    string
	Gpt            string
	CreateDate     string
	RequestType    string
}{
	MessageID:      "message_id",
	ConfigSnapshot: "config_snapshot",
	Collections:    "collections",
	Gpt:            "gpt",
	CreateDate:     "create_date",
	RequestType:    "request_type",
}
