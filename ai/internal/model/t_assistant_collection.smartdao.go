package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TAssistantCollection ai助手
type TAssistantCollection struct {
	AssistantID  uint64                     `gorm:"primaryKey;column:assistant_id" json:"assistantId"`                // 助手id
	CollectionID uint64                     `gorm:"primaryKey;column:collection_id" json:"collectionId"`              // collection id
	Threshold    float32                    `gorm:"column:threshold;default:0.6" json:"threshold"`                    // chat默认阈值
	DocTopN      int32                      `gorm:"column:doc_top_n;default:5" json:"docTopN"`                        // chat配置 doc_top_n
	ChunkConfig  *aipb.AssistantChunkConfig `gorm:"column:chunk_config;serializer:json" json:"chunkConfig,omitempty"` // 分段配置

	// 关系
	Collection *TCollection `gorm:"foreignKey:collection_id;references:id" json:"collection,omitempty"` // 知识库

	// 废弃字段
	SearchTopN   int32  `gorm:"column:search_top_n;default:5" json:"searchTopN"`     // chat配置 search_top_n
	SearchEngine string `gorm:"column:search_engine;default:''" json:"searchEngine"` // AI搜索引擎
	Remark       string `gorm:"column:remark;default:''" json:"remark"`              // 备注
}

// TableName get sql table name.获取数据库表名
func (m *TAssistantCollection) TableName() string {
	return "t_assistant_collection"
}

// TAssistantCollectionColumns get sql column name.获取数据库列名
var TAssistantCollectionColumns = struct {
	AssistantID  string
	CollectionID string
	Threshold    string
	Remark       string
	SearchTopN   string
	DocTopN      string
	SearchEngine string
	ChunkConfig  string
}{
	AssistantID:  "assistant_id",
	CollectionID: "collection_id",
	Threshold:    "threshold",
	Remark:       "remark",
	SearchTopN:   "search_top_n",
	DocTopN:      "doc_top_n",
	SearchEngine: "search_engine",
	ChunkConfig:  "chunk_config",
}
