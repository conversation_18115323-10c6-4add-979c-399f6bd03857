// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TAssistantAdmin [...]
type TAssistantAdmin struct {
	ID          uint64              `gorm:"primaryKey;column:id" json:"id"`
	AssistantID uint64              `gorm:"column:assistant_id" json:"assistantId"`       // 助手id
	AdminID     uint64              `gorm:"column:admin_id" json:"adminId"`               // 管理员id
	AdminType   basepb.IdentityType `gorm:"column:admin_type;default:0" json:"adminType"` // 1: 前台用户 2: 前台团队
}

// TableName get sql table name.获取数据库表名
func (m *TAssistantAdmin) TableName() string {
	return "t_assistant_admin"
}

// TAssistantAdminColumns get sql column name.获取数据库列名
var TAssistantAdminColumns = struct {
	ID          string
	AssistantID string
	AdminID     string
	AdminType   string
}{
	ID:          "id",
	AssistantID: "assistant_id",
	AdminID:     "admin_id",
	AdminType:   "admin_type",
}
