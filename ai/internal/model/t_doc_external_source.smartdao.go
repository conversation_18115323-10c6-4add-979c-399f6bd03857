// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocExternalSource 外部数据源
type TDocExternalSource struct {
	ID             uint64     `gorm:"primaryKey;column:id" json:"id"`
	DocID          uint64     `gorm:"column:doc_id" json:"docId"`         // doc主键id
	AdminType      uint32     `gorm:"column:admin_type" json:"adminType"` // 1: 前台用户 2: 前台团队
	AccountID      uint64     `gorm:"column:account_id" json:"accountId"`
	AccountUserID  uint64     `gorm:"column:account_user_id" json:"accountUserId"`
	Type           uint32     `gorm:"column:type;default:0" json:"type"`   // 1 腾讯文档 2 绿技行-专家 3 绿技行-成果
	State          uint32     `gorm:"column:state;default:1" json:"state"` // 1 已同步 2 已删除 3有更新 4 人工 5 同步中
	ContentJSON    string     `gorm:"column:content_json" json:"contentJson"`
	FileName       string     `gorm:"column:file_name;default:''" json:"fileName"`
	FileUUID       string     `gorm:"column:file_uuid;default:''" json:"fileUuid"`
	FileMd5        string     `gorm:"column:file_md5;default:''" json:"fileMd5"`
	CosURL         string     `gorm:"column:cos_url;default:''" json:"cosUrl"`
	TdocOpenID     string     `gorm:"column:tdoc_open_id;default:''" json:"tdocOpenId"`
	FileCreateDate *time.Time `gorm:"column:file_create_date" json:"fileCreateDate"`
	FileUpdateDate *time.Time `gorm:"column:file_update_date" json:"fileUpdateDate"`
	CreateDate     time.Time  `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"`
	UpdateDate     time.Time  `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"`
}

// TableName get sql table name.获取数据库表名
func (m *TDocExternalSource) TableName() string {
	return "t_doc_external_source"
}

// TDocExternalSourceColumns get sql column name.获取数据库列名
var TDocExternalSourceColumns = struct {
	ID             string
	DocID          string
	AdminType      string
	AccountID      string
	AccountUserID  string
	Type           string
	State          string
	ContentJSON    string
	FileName       string
	FileUUID       string
	FileMd5        string
	CosURL         string
	TdocOpenID     string
	FileCreateDate string
	FileUpdateDate string
	CreateDate     string
	UpdateDate     string
}{
	ID:             "id",
	DocID:          "doc_id",
	AdminType:      "admin_type",
	AccountID:      "account_id",
	AccountUserID:  "account_user_id",
	Type:           "type",
	State:          "state",
	ContentJSON:    "content_json",
	FileName:       "file_name",
	FileUUID:       "file_uuid",
	FileMd5:        "file_md5",
	CosURL:         "cos_url",
	TdocOpenID:     "tdoc_open_id",
	FileCreateDate: "file_create_date",
	FileUpdateDate: "file_update_date",
	CreateDate:     "create_date",
	UpdateDate:     "update_date",
}
