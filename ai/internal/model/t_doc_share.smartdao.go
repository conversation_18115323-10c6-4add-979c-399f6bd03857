// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocShare 文档分享表
type TDocShare struct {
	ID           uint64                    `gorm:"primaryKey;column:id" json:"id"`                                                // 自增主键
	DocID        uint64                    `gorm:"column:doc_id" json:"docId"`                                                    // 文档ID
	ShareType    aipb.DocShareReceiverType `gorm:"column:share_type;default:1" json:"shareType"`                                  // 分享类型: 1-助手 2-个人 3-团队
	TargetID     uint64                    `gorm:"column:target_id" json:"targetId"`                                              // 目标ID（助手/个人/团队ID）
	State        uint32                    `gorm:"column:state;default:1" json:"state"`                                           // 状态: 1-启用 2-禁用
	CreateBy     uint64                    `gorm:"column:create_by" json:"createBy"`                                              // 创建者ID
	CreateByType basepb.IdentityType       `gorm:"column:create_by_type" json:"createByType"`                                     // 创建者类型
	CreateTime   time.Time                 `gorm:"column:create_time;autoCreateTime;default:CURRENT_TIMESTAMP" json:"createTime"` // 创建时间
	UpdateTime   time.Time                 `gorm:"column:update_time;autoUpdateTime;default:CURRENT_TIMESTAMP" json:"updateTime"` // 更新时间
}

// TableName get sql table name
func (m *TDocShare) TableName() string {
	return "t_doc_share"
}

// TDocShareColumns get sql column name.获取数据库列名
var TDocShareColumns = struct {
	ID           string
	DocID        string
	ShareType    string
	TargetID     string
	State        string
	CreateBy     string
	CreateByType string
	CreateTime   string
	UpdateTime   string
}{
	ID:           "id",
	DocID:        "doc_id",
	ShareType:    "share_type",
	TargetID:     "target_id",
	State:        "state",
	CreateBy:     "create_by",
	CreateByType: "create_by_type",
	CreateTime:   "create_time",
	UpdateTime:   "update_time",
}
