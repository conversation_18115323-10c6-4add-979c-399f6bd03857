// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocSync [...]
type TDocSync struct {
	ID         uint64    `gorm:"primaryKey;column:id" json:"id"`
	DocID      uint64    `gorm:"column:doc_id" json:"docId"`          // doc主键id
	State      int32     `gorm:"column:state;default:1" json:"state"` // 1:待同步 2: 已同步 3:同步失败 4：已被合并
	CollectionID uint64    `gorm:"column:collection_id" json:"collectionId"`
	Old   *TDoc  		 `gorm:"column:old;serializer:json" json:"old"`
	New   *TDoc  		 `gorm:"column:new;serializer:json" json:"new"`
	CreateDate time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"`
	UpdateDate time.Time `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"`
	Msg        string    `gorm:"column:msg" json:"msg"`                        // 历史出错记录
	MergedCnt  uint32    `gorm:"column:merged_cnt;default:1" json:"mergedCnt"` // 日志合并的数量
	MergedMsg  string    `gorm:"column:merged_msg" json:"mergedMsg"` // 合并日志的信息
	Order      int32     `gorm:"column:order;default:0" json:"order"` // 优先级，越小优先级越高
	RetryCnt     uint32      `gorm:"column:retry_cnt;default:0" json:"retryCnt"`   // 重试次数
	ActiveDate   *time.Time `gorm:"column:active_date" json:"activeDate"`         // 第一次执行时间
	TraceID     string    `gorm:"column:trace_id" json:"traceId"`              // 原始请求的 traceID，用于跟踪异步执行
}

// TableName get sql table name.获取数据库表名
func (m *TDocSync) TableName() string {
	return "t_doc_sync"
}

// TDocSyncColumns get sql column name.获取数据库列名
var TDocSyncColumns = struct {
	ID           string
	DocID        string
	CollectionID string
	State        string
	Old          string
	New          string
	CreateDate   string
	UpdateDate   string
	Msg          string
	MergedCnt    string
	MergedMsg    string
	Order        string
	RetryCnt     string
	ActiveDate   string
	TraceID      string
}{
	ID:           "id",
	DocID:        "doc_id",
	CollectionID: "collection_id",
	State:        "state",
	Old:          "old",
	New:          "new",
	CreateDate:   "create_date",
	UpdateDate:   "update_date",
	Msg:          "msg",
	MergedCnt:    "merged_cnt",
	MergedMsg:    "merged_msg",
	Order:        "order",
	RetryCnt:     "retry_cnt",
	ActiveDate:   "active_date",
	TraceID:      "trace_id",
}
