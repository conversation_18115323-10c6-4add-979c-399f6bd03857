package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// FeedbackLogOperatorTypePortal 门户端人员操作
const FeedbackLogOperatorTypePortal = 1

// FeedbackLogOperatorTypeMgmt 运营端人员操作
const FeedbackLogOperatorTypeMgmt = 2

// ToPb ...
func (m *TFeedbackLog) ToPb() *aipb.FeedbackLog {
	if m == nil {
		return nil
	}
	pb := &aipb.FeedbackLog{}
	pb.Id = m.ID
	pb.FeedbackId = m.FeedbackID
	pb.Action = m.Action
	pb.CreateIdentity = m.CreateIdentity
	pb.CreateDate = toTimestamppb(m.CreateDate)
	return pb
}

// TFeedbackLogs 日志列表
type TFeedbackLogs []*TFeedbackLog

// ToPb 协议转换
func (set TFeedbackLogs) ToPb() []*aipb.FeedbackLog {
	pbs := make([]*aipb.FeedbackLog, 0, len(set))
	for _, m := range set {
		pbs = append(pbs, m.ToPb())
	}
	return pbs
}

// ToFullPb 协议转换
func (set TFeedbackLogs) ToFullPb() []*aipb.FullFeedbackLog {
	pbs := make([]*aipb.FullFeedbackLog, 0, len(set))
	for _, m := range set {
		var originalQuestion *aipb.ChatMessage
		if m.Feedback != nil {
			originalQuestion, _ = m.Feedback.OriginalQuestion.TransformToAiMessage()
		}

		pbs = append(pbs, &aipb.FullFeedbackLog{
			Log:              m.ToPb(),
			Feedback:         m.Feedback.ToPb(),
			OriginalQuestion: originalQuestion,
		})
	}
	return pbs
}
