package cmd

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"golang.org/x/net/context"
	"gorm.io/gorm/clause"
)

type cmsResource struct {
	CmsKey   string `gorm:"column:cms_key"`
	Extend   string `gorm:"column:extend"`
	Language string `gorm:"column:language"`
}

func (m *cmsResource) TableName() string {
	return "db_carbon_live_op.t_op_cms_resource"
}

type cmsTenantConfig struct {
	tenancy *cmsAiTenancy
	tenant  *cmsAiTenancyTenant
}

type cmsAiTenancy struct {
	Type      string                `json:"type"`
	Name      string                `json:"name"`
	Key       string                `json:"key"`
	Id        string                `json:"id"`
	Title     string                `json:"title"`
	Des       string                `json:"des"`
	Route     string                `json:"route"`
	KeyWords  string                `json:"keyWords"`
	Items     []*cmsAiTenancyItem   `json:"items"`
	Tenants   []*cmsAiTenancyTenant `json:"tenants"`
	Knowledge bool                  `json:"knowledge"`
	Team      bool                  `json:"team"`
}

type cmsAiTenancyItem struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type cmsAiTenancyTenant struct {
	Text        string      `json:"text"`
	Type        json.Number `json:"type"`
	AssistantId string      `json:"assistant_id"`
	Lang        string      `json:"lang"`
}

type cmsAiAuth struct {
	AuthAll []*cmsAiAuthAll `json:"auth_all"`
}

type cmsAiAuthAll struct {
	Name        string   `json:"name"`
	Title       string   `json:"title"`
	AssistantId string   `json:"assistant_id"`
	Auth        []string `json:"auth"`
	Detail      bool     `json:"detail"`
	Img         string   `json:"img"`
	TitleAi     string   `json:"title_ai"`
}

func migrateAssistant() {
	if !config.GetBool("tanlive.cmd.MIGRATEASSISTANT") {
		return
	}
	defer os.Exit(0)

	fmt.Println("start to migrate assistants...")

	ctx := context.Background()

	query := model.NewQuery[model.TAssistant](ctx).With("AssistantCollections.Collection")
	if maxId := config.GetUint64("assistant_maxid"); maxId != 0 {
		query.Where("id <= ?", maxId)
	}
	assistants, err := query.Get()
	if err != nil {
		log.Errorw("query assistants", "err", err)
		panic(err)
	}

	zhConfigs, enConfigs, autConfigs, err := prepareCmsData(ctx)
	if err != nil {
		log.Errorw("prepare cms data", "err", err)
		panic(err)
	}

	for _, assistant := range assistants {
		// 清理英文名称
		assistant.NameEn = strings.ReplaceAll(assistant.NameEn, " -", "-")
		assistant.NameEn = strings.ReplaceAll(assistant.NameEn, " ", "-")

		zhConfig, enConfig, authConfig := zhConfigs[assistant.ID], enConfigs[assistant.ID], autConfigs[assistant.ID]

		assistant.Channel = determineAssistantChannel(assistant)

		migrateBaseConfig(assistant, zhConfig, enConfig, authConfig)
		migrateAiConfig(assistant, zhConfig, enConfig, authConfig)

		switch assistant.Channel {
		case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN:
			migrateWeixinChannelConfig(assistant, zhConfig, enConfig, authConfig)
		case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB:
			migrateTanliveWebChannelConfig(assistant, zhConfig, enConfig, authConfig)
		case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP:
			migrateTanliveAppChannelConfig(assistant, zhConfig, enConfig, authConfig)
		case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP:
			migrateWhatsappChannelConfig(assistant, zhConfig, enConfig, authConfig)
		}

		if err = model.NewQuery[model.TAssistant](ctx).Omit(clause.Associations).Save(assistant); err != nil {
			err = fmt.Errorf("save assistant: %w", err)
			log.Errorw("save assistant", "err", err)
			panic(err)
		}

		fmt.Println("[OK] assistant:", assistant.Name)
	}

	fmt.Println("finished.")
}

func determineAssistantChannel(assistant *model.TAssistant) aipb.AssistantChannel {
	if assistant.Name == "碳LIVE-web" || assistant.Name == "碳LIVE-web-海外" {
		return aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP
	}
	if assistant.AppCode != "" {
		if assistant.CorpID != "" {
			return aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN
		} else {
			return aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP
		}
	}
	return aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB
}

func migrateBaseConfig(assistant *model.TAssistant, enConfig, zhConfig *cmsTenantConfig, authConfig *cmsAiAuthAll) {
	assistant.VisibleChainConfig = &aipb.AssistantVisibleChainConfig{}
	assistant.FieldManageConfig = &aipb.AssistantFieldManageConfig{}

	if authConfig != nil {
		if len(authConfig.Auth) > 0 {
			assistant.VisibleChainConfig.Enabled = true
			assistant.VisibleChainConfig.Visible = migrateVisibleChainConfig(authConfig.Auth)
		}
	}

	// 中文
	if zhConfig != nil {
		// 启用
		if zhConfig.tenant.Type == "1" {
			assistant.Enabled = true
		}
	}
}

func migrateAiConfig(assistant *model.TAssistant, enConfig, zhConfig *cmsTenantConfig, authConfig *cmsAiAuthAll) {
	if len(assistant.AssistantCollections) > 0 {
		assistant.SearchEngine = assistant.AssistantCollections[0].SearchEngine
		assistant.SearchTopN = assistant.AssistantCollections[0].SearchTopN
	}
	assistant.ChatOrSqlConfig = &aipb.AssistantChatOrSqlConfig{
		Enabled: assistant.ChatOrSQL == 1,
		Model:   assistant.SqlModel,
	}
	assistant.AskSuggestionConfig = &aipb.AssistantAskSuggestionConfig{
		Enabled: assistant.SuggestCount > 0,
		Prompt:  assistant.SuggestPrefix,
		Count:   assistant.SuggestCount,
	}
}

func migrateWeixinChannelConfig(assistant *model.TAssistant, zhConfig, enConfig *cmsTenantConfig, authConfig *cmsAiAuthAll) {
	migrateNicknameAndAvatar(assistant, authConfig, false)
	assistant.SystemLang = "zh"
	assistant.WeixinDevelopConfig = &aipb.AssistantWeixinDevelopConfig{
		CorpId:   assistant.CorpID,
		OpenKfid: assistant.AppCode,
	}
	assistant.WelcomeMessageConfig = &aipb.AssistantWelcomeMessageConfig{}
	assistant.PresetQuestionConfig = &aipb.AssistantPresetQuestionConfig{}
	assistant.KefuConfig = &aipb.AssistantKefuConfig{}
	assistant.RatingScaleReplyConfig = migrateRatingScaleReplyConfig(assistant.RatingScaleMsg, false)
	assistant.InteractiveCodeConfig = migrateInteractiveCodeConfig(assistant.WelcomeMsg, false)
	assistant.ChatIdleDuration = 120

	// 欢迎语
	migrateWelcomeMessageFromDB(assistant, "zh")
	// 预设问题
	migratePresetQuestionFromDB(assistant, "zh")

	if assistant.LiveAgentMsg != nil {
		assistant.KefuConfig.Enabled = assistant.LiveAgentMsg.Enable
		assistant.KefuConfig.AfterRemindMessage = assistant.LiveAgentMsg.Msg
		if assistant.LiveAgentMsg.RemindPushMsg != nil {
			assistant.KefuConfig.BeforeRemindEnabled = assistant.LiveAgentMsg.RemindPushMsg.Enable
			assistant.KefuConfig.BeforeRemindMessage = assistant.LiveAgentMsg.RemindPushMsg.Msg
		}
	}
}

func migrateTanliveWebChannelConfig(assistant *model.TAssistant, zhConfig, enConfig *cmsTenantConfig, authConfig *cmsAiAuthAll) {
	migrateNicknameAndAvatar(assistant, authConfig, true)
	assistant.AssistantLang = "zh"
	assistant.FeedbackEnabled = true
	assistant.WebsiteConfig = &aipb.AssistantWebsiteConfig{}
	assistant.WelcomeMessageConfig = &aipb.AssistantWelcomeMessageConfig{}
	assistant.PresetQuestionConfig = &aipb.AssistantPresetQuestionConfig{}
	assistant.RatingScaleReplyConfig = newWebRatingScaleReplyConfig(true, true)
	assistant.InteractiveCodeConfig = newInteractiveCodeConfig(true, false, true, true)
	assistant.GraphParseConfig = &aipb.AssistantGraphParseConfig{}

	// 中文
	if zhConfig != nil {
		// 网站配置
		if assistant.WebsiteConfig.RoutePath == "" {
			assistant.WebsiteConfig.RoutePath = strings.TrimPrefix(zhConfig.tenancy.Route, "/ai/")
		}
		if assistant.WebsiteConfig.Title == "" {
			assistant.WebsiteConfig.Title = zhConfig.tenancy.KeyWords
		}
		// 欢迎语
		migrateWelcomeMessageFromCms(assistant, zhConfig, "zh")
		// 预设问题
		migratePresetQuestionFromCms(assistant, zhConfig, "zh")
		// 图谱解析
		migrateGraphParseConfigFromCms(assistant, zhConfig)
	}

	// 英文
	if enConfig != nil {
		// 欢迎语
		migrateWelcomeMessageFromCms(assistant, enConfig, "en")
		// 预设问题
		migratePresetQuestionFromCms(assistant, enConfig, "en")
	}

	// cms未取到，直接使用数据库字段
	// 欢迎语
	if len(assistant.WelcomeMessageConfig.Messages) == 0 {
		migrateWelcomeMessageFromDB(assistant, "zh")
	}
	// 预设问题
	if len(assistant.PresetQuestionConfig.Questions) == 0 {
		migratePresetQuestionFromDB(assistant, "zh")
	}
}

func migrateTanliveAppChannelConfig(assistant *model.TAssistant, zhConfig, enConfig *cmsTenantConfig, authConfig *cmsAiAuthAll) {
	migrateNicknameAndAvatar(assistant, authConfig, true)
	// 只有Tan-Web是英文
	if assistant.Name == "碳LIVE-web-海外" {
		assistant.AssistantLang = "en"
	} else {
		assistant.AssistantLang = "zh"
	}
	assistant.FeedbackEnabled = true
	assistant.WelcomeMessageConfig = &aipb.AssistantWelcomeMessageConfig{}
	assistant.PresetQuestionConfig = &aipb.AssistantPresetQuestionConfig{}
	assistant.RatingScaleReplyConfig = newWebRatingScaleReplyConfig(true, true)
	assistant.InteractiveCodeConfig = newInteractiveCodeConfig(true, false, true, true)
	assistant.GraphParseConfig = &aipb.AssistantGraphParseConfig{}

	// 中文
	if zhConfig != nil {
		// 欢迎语
		migrateWelcomeMessageFromCms(assistant, zhConfig, "zh")
		// 预设问题
		migratePresetQuestionFromCms(assistant, zhConfig, "zh")
		// 图谱解析
		migrateGraphParseConfigFromCms(assistant, zhConfig)
	}

	// 英文
	if enConfig != nil {
		// 欢迎语
		migrateWelcomeMessageFromCms(assistant, enConfig, "en")
		// 预设问题
		migratePresetQuestionFromCms(assistant, enConfig, "en")
	}

	// cms未取到，直接使用数据库字段
	// 欢迎语
	if len(assistant.WelcomeMessageConfig.Messages) == 0 {
		migrateWelcomeMessageFromDB(assistant, assistant.AssistantLang)
	}
	// 预设问题
	if len(assistant.PresetQuestionConfig.Questions) == 0 {
		migratePresetQuestionFromDB(assistant, assistant.AssistantLang)
	}
}

func migrateWhatsappChannelConfig(assistant *model.TAssistant, zhConfig, enConfig *cmsTenantConfig, authConfig *cmsAiAuthAll) {
	assistant.SystemLang = "en"
	assistant.WhatsappDevelopConfig = &aipb.AssistantWhatsappDevelopConfig{
		BusinessNumber: assistant.AppCode,
	}
	assistant.WelcomeMessageConfig = &aipb.AssistantWelcomeMessageConfig{}
	assistant.PresetQuestionConfig = &aipb.AssistantPresetQuestionConfig{}
	assistant.RatingScaleReplyConfig = migrateRatingScaleReplyConfig(assistant.RatingScaleMsg, true)
	assistant.ChatIdleDuration = 120

	// 欢迎语
	migrateWelcomeMessageFromDB(assistant, "en")
	// 预设问题
	migratePresetQuestionFromDB(assistant, "en")
}

func prepareCmsData(ctx context.Context) (zhConfigs, enConfigs map[uint64]*cmsTenantConfig, authConfigs map[uint64]*cmsAiAuthAll, err error) {
	var resources []*cmsResource
	resources, err = model.NewQuery[cmsResource](ctx).
		Where("cms_key IN (?)", []string{"ai_tenancy", "ai_auth"}).Get()
	if err != nil {
		err = fmt.Errorf("query cms resources: %w", err)
		return
	}

	var zhData, enData []*cmsAiTenancy
	authData := &cmsAiAuth{}
	for _, resource := range resources {
		switch {
		case resource.CmsKey == "ai_tenancy" && resource.Language == "zh":
			err = json.Unmarshal([]byte(resource.Extend), &zhData)
		case resource.CmsKey == "ai_tenancy" && resource.Language == "en":
			err = json.Unmarshal([]byte(resource.Extend), &enData)
		case resource.CmsKey == "ai_auth":
			err = json.Unmarshal([]byte(resource.Extend), authData)
		}
	}
	if err != nil {
		err = fmt.Errorf("query cms resources: %w", err)
		return
	}

	zhConfigs = make(map[uint64]*cmsTenantConfig)
	enConfigs = make(map[uint64]*cmsTenantConfig)
	authConfigs = make(map[uint64]*cmsAiAuthAll)

	for _, tenancy := range zhData {
		for _, tenant := range tenancy.Tenants {
			if id, err2 := hashids.Decode(tenant.AssistantId); err2 != nil {
				log.Infof("decode AiTenancyZh hashid %s: %v", tenant.AssistantId, err2)
			} else {
				zhConfigs[id] = &cmsTenantConfig{
					tenancy: tenancy,
					tenant:  tenant,
				}
			}

		}
	}
	for _, tenancy := range enData {
		for _, tenant := range tenancy.Tenants {
			if id, err2 := hashids.Decode(tenant.AssistantId); err2 != nil {
				log.Infof("decode AiTenancyEn hashid %s: %v", tenant.AssistantId, err2)
			} else {
				enConfigs[id] = &cmsTenantConfig{
					tenancy: tenancy,
					tenant:  tenant,
				}
			}
		}
	}
	for _, authAll := range authData.AuthAll {
		if id, err2 := hashids.Decode(authAll.AssistantId); err2 != nil {
			log.Infof("decode ai_auth.auth_all hashid %s: %v", authAll.AssistantId, err2)
		} else {
			authConfigs[id] = authAll
		}
	}

	return
}

func concatTenancyWelcomeMessage(tenancy *cmsAiTenancy) string {
	var content string
	if tenancy.Title != "" {
		content += tenancy.Title
	}
	if tenancy.Des != "" {
		if content != "" {
			content += "\n"
		}
		content += tenancy.Des
	}
	return content
}

func newWebRatingScaleReplyConfig(zh, en bool) *aipb.AssistantRatingScaleReplyConfig {
	config := &aipb.AssistantRatingScaleReplyConfig{
		Enabled: true,
	}
	if zh {
		config.Replies = append(config.Replies, &aipb.AssistantRatingScaleReply{
			Lang:        "zh",
			RatingScale: aipb.RatingScale_RATING_SCALE_SATISFIED,
			Content:     "感谢您的支持！",
		}, &aipb.AssistantRatingScaleReply{
			Lang:        "zh",
			RatingScale: aipb.RatingScale_RATING_SCALE_AVERAGE,
			Content:     "我会继续努力的！",
		}, &aipb.AssistantRatingScaleReply{
			Lang:        "zh",
			RatingScale: aipb.RatingScale_RATING_SCALE_DISSATISFIED,
			Content:     "我会继续努力的！",
		})
	}
	if en {
		config.Replies = append(config.Replies, &aipb.AssistantRatingScaleReply{
			Lang:        "en",
			RatingScale: aipb.RatingScale_RATING_SCALE_SATISFIED,
			Content:     "Thank you for the compliment to me.",
		}, &aipb.AssistantRatingScaleReply{
			Lang:        "en",
			RatingScale: aipb.RatingScale_RATING_SCALE_AVERAGE,
			Content:     "I will keep trying!",
		}, &aipb.AssistantRatingScaleReply{
			Lang:        "en",
			RatingScale: aipb.RatingScale_RATING_SCALE_DISSATISFIED,
			Content:     "I will keep trying!",
		})
	}
	return config
}

func migrateRatingScaleReplyConfig(old *aipb.AssistantRatingScaleMsg, en bool) *aipb.AssistantRatingScaleReplyConfig {
	if old == nil || !old.Enable {
		if en {
			return newWebRatingScaleReplyConfig(false, true)
		}
		return newWebRatingScaleReplyConfig(true, false)
	}

	lang := "zh"
	if en {
		lang = "en"
	}

	return &aipb.AssistantRatingScaleReplyConfig{
		Enabled: true,
		Replies: []*aipb.AssistantRatingScaleReply{
			{
				Lang:        lang,
				RatingScale: aipb.RatingScale_RATING_SCALE_SATISFIED,
				Content:     old.Satisfied,
			},
			{
				Lang:        lang,
				RatingScale: aipb.RatingScale_RATING_SCALE_AVERAGE,
				Content:     old.Average,
			},
			{
				Lang:        lang,
				RatingScale: aipb.RatingScale_RATING_SCALE_DISSATISFIED,
				Content:     old.Dissatisfied,
			},
		},
	}
}

func migrateInteractiveCodeConfig(old *aipb.AssistantWelcomeMsg, en bool) *aipb.AssistantInteractiveCodeConfig {
	hasManual := true
	hasOther := true
	hasZh := true
	hasEn := false

	if old != nil && !strings.Contains(old.TailMsg, "回复“人工”，转人工客服") {
		hasManual = false
	}

	if en {
		hasZh = false
		hasEn = true
	}

	return newInteractiveCodeConfig(hasManual, hasOther, hasZh, hasEn)
}

func newInteractiveCodeConfig(hasManual, hasOther, hasZh, hasEn bool) *aipb.AssistantInteractiveCodeConfig {
	var codes []*aipb.AssistantInteractiveCode
	if hasManual {
		if hasZh {
			codes = append(codes, &aipb.AssistantInteractiveCode{
				Lang:            "zh",
				InteractiveCode: aipb.InteractiveCode_INTERACTIVE_CODE_MANUAL,
				Content:         "回复“人工”，转人工客服",
			})
		}
		if hasEn {
			codes = append(codes, &aipb.AssistantInteractiveCode{
				Lang:            "en",
				InteractiveCode: aipb.InteractiveCode_INTERACTIVE_CODE_MANUAL,
				Content:         "Reply with \"Manual\" to transfer to human customer service.",
			})
		}
	}
	if hasOther {
		if hasZh {
			codes = append(codes, &aipb.AssistantInteractiveCode{
				Lang:            "zh",
				InteractiveCode: aipb.InteractiveCode_INTERACTIVE_CODE_ANSWER_AGAIN,
				Content:         "回复“重新回答”，AI重新回答",
			}, &aipb.AssistantInteractiveCode{
				Lang:            "zh",
				InteractiveCode: aipb.InteractiveCode_INTERACTIVE_CODE_CLEAR_CONTEXT,
				Content:         "回复“清空上下文”，开始新对话",
			})
		}
		if hasEn {
			codes = append(codes, &aipb.AssistantInteractiveCode{
				Lang:            "en",
				InteractiveCode: aipb.InteractiveCode_INTERACTIVE_CODE_ANSWER_AGAIN,
				Content:         "Reply with \"Retry\" for AI to provide a new response.",
			}, &aipb.AssistantInteractiveCode{
				Lang:            "en",
				InteractiveCode: aipb.InteractiveCode_INTERACTIVE_CODE_CLEAR_CONTEXT,
				Content:         "Reply with \"Clear Context\" to start a new conversation.",
			})
		}
	}
	return &aipb.AssistantInteractiveCodeConfig{
		Codes: codes,
	}
}

func migrateNicknameAndAvatar(assistant *model.TAssistant, authConfig *cmsAiAuthAll, withEn bool) {
	if authConfig != nil {
		assistant.Nickname = authConfig.TitleAi
		assistant.AvatarUrl = authConfig.Img

		if withEn {
			assistant.NicknameEn = authConfig.TitleAi
		}
	}
}

func migrateWelcomeMessageFromCms(assistant *model.TAssistant, config *cmsTenantConfig, lang string) {
	if assistant.WelcomeMessageConfig == nil {
		assistant.WelcomeMessageConfig = &aipb.AssistantWelcomeMessageConfig{}
	}
	if config != nil {
		if content := concatTenancyWelcomeMessage(config.tenancy); content != "" {
			assistant.WelcomeMessageConfig.Messages = append(assistant.WelcomeMessageConfig.Messages, &aipb.AssistantWelcomeMessage{
				Lang:    lang,
				Content: content,
			})
		}
	}
}

func migrateWelcomeMessageFromDB(assistant *model.TAssistant, lang string) {
	if assistant.WelcomeMessageConfig == nil {
		assistant.WelcomeMessageConfig = &aipb.AssistantWelcomeMessageConfig{}
	}
	if assistant.WelcomeMsg != nil && assistant.WelcomeMsg.HeadMsg != "" {
		assistant.WelcomeMessageConfig.Messages = []*aipb.AssistantWelcomeMessage{
			{
				Lang:    lang,
				Content: assistant.WelcomeMsg.HeadMsg,
			},
		}
	}
}

func migratePresetQuestionFromCms(assistant *model.TAssistant, config *cmsTenantConfig, lang string) {
	if assistant.PresetQuestionConfig == nil {
		assistant.PresetQuestionConfig = &aipb.AssistantPresetQuestionConfig{}
	}
	if config != nil {
		if len(config.tenancy.Items) > 0 {
			assistant.PresetQuestionConfig.Enabled = true
		}
		for _, item := range config.tenancy.Items {
			assistant.PresetQuestionConfig.Questions = append(assistant.PresetQuestionConfig.Questions, &aipb.AssistantPresetQuestion{
				Lang:    lang,
				Content: item.Text,
			})
		}
	}
}

func migratePresetQuestionFromDB(assistant *model.TAssistant, lang string) {
	if assistant.PresetQuestionConfig == nil {
		assistant.PresetQuestionConfig = &aipb.AssistantPresetQuestionConfig{}
	}
	if assistant.WelcomeMsg != nil && len(assistant.WelcomeMsg.Question) > 0 {
		assistant.PresetQuestionConfig.Enabled = true
		for _, question := range assistant.WelcomeMsg.Question {
			assistant.PresetQuestionConfig.Questions = append(assistant.PresetQuestionConfig.Questions, &aipb.AssistantPresetQuestion{
				Lang:    lang,
				Content: question,
			})
		}
	}
}

func migrateGraphParseConfigFromCms(assistant *model.TAssistant, config *cmsTenantConfig) {
	if assistant.GraphParseConfig == nil {
		assistant.GraphParseConfig = &aipb.AssistantGraphParseConfig{}
	}
	if config.tenancy.Knowledge {
		assistant.GraphParseConfig.Enabled = true
		assistant.GraphParseConfig.Model = "hy"
	}
}

var (
	assistantVisibleChainOnce   sync.Once
	assistantVisibleChainFields = make(map[string]bool)
	assistantVisibleChainNames  = make(map[string]string)
)

func migrateVisibleChainConfig(auth []string) []string {
	assistantVisibleChainOnce.Do(func() {
		var options []*aipb.VisibleChainOption
		if err := config.Unmarshal("assistant.visibleChainOption", &options); err != nil {
			panic(err)
		}

		for _, option := range options {
			assistantVisibleChainFields[option.Field] = true
			assistantVisibleChainNames[option.Name] = option.Field
		}
	})

	filtered := make([]string, 0, len(auth))
	for _, field := range auth {
		if f, ok := assistantVisibleChainNames[field]; ok {
			filtered = append(filtered, f)
		} else if assistantVisibleChainFields[field] {
			filtered = append(filtered, field)
		}
	}
	return filtered
}
