# 如何迁移知识
本文主要介绍如何使用命令行工具迁移知识。分为以下几个部分：
1. 迁移知识到另外一个助手
2. 迁移知识到另外一个Collection

## 1. 从一个/多个助手迁移知识到另外一个助手

一般来说，如果要把一个/多个助手的知识迁移到另外一个新开的助手，就使用该命令。

目前只支持从一个/多个源助手（假设名称为 from），迁移知识到另外一个from），迁移知识到另外一个目标助手（假设名称为 to）。

迁移的命令如下：
```bash
# 后台运行
TANLIVE_CMD_CLONEDOCTOASSISTANT=true TANLIVE_CMD_CLONEDOCTOASSISTANT_FROM={FROM} TANLIVE_CMD_CLONEDOCTOASSISTANT_TO={TO} TANLIVE_CMD_CLONEDOCTOASSISTANT_RETRY=100 TANLIVE_CMD_CLONEDOCTOASSISTANT_LASTDOCID=0 TANLIVE_CMD_CLONEDOCTOASSISTANT_MAXDOCID=0 TANLIVE_CMD_CLONEDOCTOASSISTANT_INSERTMAPPING=true nohup ./ai &
```

```bash
# 前台运行
TANLIVE_CMD_CLONEDOCTOASSISTANT=true TANLIVE_CMD_CLONEDOCTOASSISTANT_FROM={FROM} TANLIVE_CMD_CLONEDOCTOASSISTANT_TO={TO} TANLIVE_CMD_CLONEDOCTOASSISTANT_RETRY=100 TANLIVE_CMD_CLONEDOCTOASSISTANT_LASTDOCID=0 TANLIVE_CMD_CLONEDOCTOASSISTANT_MAXDOCID=0 TANLIVE_CMD_CLONEDOCTOASSISTANT_INSERTMAPPING=true  ./ai
```



参数解释：
- TANLIVE_CMD_CLONEDOCTOASSISTANT_FROM 源助手（多个助手用逗号分隔，如1,2,3）
- TANLIVE_CMD_CLONEDOCTOASSISTANT_TO 目标助手
- TANLIVE_CMD_CLONEDOCTOASSISTANT_RETRY 迁移过程中出现向量化接口调用失败后重试次数，-1 表示无限
- TANLIVE_CMD_CLONEDOCTOASSISTANT_LASTDOCID 从哪一个知识（tdoc）的 id 开始迁移（小于等于该值的 doc 不会被迁移）
- TANLIVE_CMD_CLONEDOCTOASSISTANT_MAXDOCID 迁移到哪一个知识（tdoc）的 id 结束（大于该值的 doc 不会被迁移）
- TANLIVE_CMD_CLONEDOCTOASSISTANT_INSERTMAPPING 是否自动创建助手和知识的关联（t_assistant_doc 表）


## 2. 从一个助手迁移知识到另外一个 Collection
该命令一般用于把一个助手的底层 collection 数据复制到一个全新的 collection

目前只支持从一个源助手（假设名称为 from），迁移知识到另外一个目标 Collection（假设名称为 to）。
可以在不暂停源助手的知识新增/删除/启用/禁用的情况下，同时迁移。

迁移的命令如下：
```bash
TANLIVE_CMD_CLONEDOCTOCOLLECTION=true \
            TANLIVE_CMD_CLONEDOCTOCOLLECTION_LASTDOCID=0 \
            TANLIVE_CMD_CLONEDOCTOCOLLECTION_FROM={FROM} \
            TANLIVE_CMD_CLONEDOCTOCOLLECTION_TO={TO} \
            TANLIVE_CMD_CLONEDOCTOCOLLECTION_RETRY=100 \
            TANLIVE_CMD_CLONEDOCTOCOLLECTION_MIGRATE_EXISTING=true \
            TANLIVE_CMD_CLONEDOCTOCOLLECTION_MIGRATE_INCREMENTAL=true \
            TANLIVE_CMD_CLONEDOCTOCOLLECTION_MIGRATE_INCREMENTAL_START=-1 \
            nohup ./ai &
```