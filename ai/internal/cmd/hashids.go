package cmd

import (
	"fmt"
	"os"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
)

func decodeHashids() {
	if !config.GetBool("tanlive.cmd.DECODEHASHIDS") {
		return
	}
	defer os.Exit(0)

	hashid := config.GetString("hashid")
	id, err := hashids.Decode(hashid)
	if err != nil {
		fmt.Println("decode hashid err:", err)
		panic(err)
	}
	fmt.Println(id)
}

func encodeHashids() {
	if !config.GetBool("tanlive.cmd.ENCODEHASHIDS") {
		return
	}
	defer os.Exit(0)

	id := config.GetUint64("id")
	hashid, err := hashids.Encode(id)
	if err != nil {
		fmt.Println("encode id err:", err)
		panic(err)
	}
	fmt.Println(hashid)
}
