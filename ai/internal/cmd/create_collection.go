package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
)

func createCollection() {
	if !config.GetBool("tanlive.cmd.CREATECOLLECTION") {
		return
	}
	defer os.Exit(0)

	f, err := os.Open(config.GetString("file"))
	if err != nil {
		panic(err)
	}
	defer f.Close()
	text, err := io.ReadAll(f)
	if err != nil {
		panic(err)
	}

	rsp, err := client.GetRagClient().CreateCollection(context.Background(), &rag.ReqCreateCollection{
		CollectionName:   config.GetString("collectionName"),
		FullText:         string(text),
		FileName:         filepath.Base(f.Name()),
		Lang:             config.GetString("lang"),
		ReturnIds:        true,
		ReturnChunksOnly: config.GetBool("returnChunksOnly"),
	})
	if err != nil {
		fmt.Println("create collection error:", err)
		return
	}

	s, _ := json.Marshal(rsp)
	fmt.Println("create collection success:", string(s))
}
