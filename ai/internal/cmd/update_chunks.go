package cmd

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	docchunklogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
)

func updateChunks() {
	if !config.GetBool("tanlive.cmd.UPDATECHUNKS") {
		return
	}
	defer os.Exit(0)

	ctx := context.Background()
	docID := config.GetUint64("doc")
	assistantStrs := strings.Split(config.GetString("assistant"), ",")
	assistantIDs := make([]uint64, 0, len(assistantStrs))
	for _, str := range assistantStrs {
		id, err := strconv.ParseUint(str, 10, 64)
		if err != nil {
			panic(fmt.Sprintf("parse assistant id [%d] failed: %v", id, err))
		}
		if id == 0 {
			continue
		}
		assistantIDs = append(assistantIDs, id)
	}

	doc, err := model.NewQuery[model.TDoc](ctx).FindByKey(docID)
	if err != nil {
		panic(fmt.Sprintf("query doc failed: %v", err))
	}
	assistants, err := model.NewQuery[model.TAssistant](ctx).
		With("AssistantCollections.Collection").GetByKey(assistantIDs)
	if err != nil {
		panic(fmt.Sprintf("query assistants failed: %v", err))
	}

	for _, assistant := range assistants {
		if len(assistant.AssistantCollections) == 0 || assistant.AssistantCollections[0].Collection == nil {
			fmt.Printf("assistant [%d] has no collection", assistant.ID)
			continue
		}
		err = new(docchunklogic.ChunkDocLogic).
			UpdateDocChunks(ctx, doc, assistant.AssistantCollections[0].Collection, assistant)
		if err != nil {
			panic(fmt.Sprintf("update chunk: %v", err))
		}
	}
}
