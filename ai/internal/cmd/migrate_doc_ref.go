package cmd

import (
	"context"
	"fmt"
	"os"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"gorm.io/gorm"
)

// MigrateTextReferences 迁移纯文本参考资料
func MigrateTextReferences() {
	if !config.GetBool("tanlive.cmd.MIGRATE_DOC_REFERENCE") {
		return
	}
	defer os.Exit(0)
	ctx := context.Background()
	migrateLogic := NewMigrateTextReferencesLogic(ctx)
	if err := migrateLogic.MigrateAll(500, 10); err != nil {
		log.WithContext(ctx).Errorf("迁移纯文本参考资料失败: %v", err)
		return
	}
	log.WithContext(ctx).Info("纯文本参考资料迁移流程执行完毕。")
	return
}

// VerifyMigratedTextReferences 验证已迁移的纯文本参考资料的正确性
func VerifyMigratedTextReferences() {
	if !config.GetBool("tanlive.cmd.VERIFY_MIGRATED_DOC_REFERENCE") {
		return
	}
	defer os.Exit(0)

	ctx := context.Background()
	logic := NewMigrateTextReferencesLogic(ctx)

	var totalDocs int64
	if err := logic.db.Model(&model.TDoc{}).Where("ref IS NOT NULL").Count(&totalDocs).Error; err != nil {
		log.WithContext(ctx).Errorf("验证错误: 无法统计符合条件的文档总数: %v", err)
		return
	}
	if totalDocs == 0 {
		log.WithContext(ctx).Info("验证信息: 没有找到 ref IS NOT NULL 的文档，无需验证。")
		return
	}
	log.WithContext(ctx).Infof("开始验证已迁移的纯文本参考资料，总候选文档数: %d", totalDocs)

	var lastID uint64 = 0
	batchSize := 500
	goroutines := 10

	totalChecked := 0
	totalMismatched := 0
	totalErrors := 0

	for {
		var docs []*model.TDoc
		if err := logic.db.Model(&model.TDoc{}).
			Where("id > ? AND ref IS NOT NULL", lastID).
			Select("id", "ref").
			Order("id ASC").
			Limit(batchSize).
			Find(&docs).Error; err != nil {
			log.WithContext(ctx).Errorf("验证错误: 查询文档批次失败 (lastID: %d): %v", lastID, err)
			return
		}

		if len(docs) == 0 {
			break
		}
		lastID = docs[len(docs)-1].ID

		wg := xsync.NewGroup(ctx, xsync.GroupQuota(goroutines))

		type result struct {
			mismatched bool
			queryError bool
		}
		resultChan := make(chan result, len(docs))

		for _, doc := range docs {
			doc := doc
			wg.Go(func(ctx2 context.Context) error {
				isMatch, _, _, err := logic.verifySingleDocConsistency(doc)
				if err != nil {
					resultChan <- result{mismatched: false, queryError: true}
					return err
				}
				resultChan <- result{mismatched: !isMatch, queryError: false}
				return nil
			})
		}
		wg.Wait()
		close(resultChan)

		batchMismatches := 0
		batchErrors := 0
		for res := range resultChan {
			if res.queryError {
				batchErrors++
			}
			if res.mismatched {
				batchMismatches++
			}
		}

		totalMismatched += batchMismatches
		totalErrors += batchErrors
		totalChecked += len(docs)

		log.WithContext(ctx).Infof("验证进度: 已处理 %d/%d 文档。本批次不匹配: %d, 本批次查询错误: %d。累计不匹配: %d, 累计查询错误: %d", totalChecked, totalDocs, batchMismatches, batchErrors, totalMismatched, totalErrors)

		if len(docs) < batchSize {
			break
		}
	}

	log.WithContext(ctx).Infof("纯文本参考资料验证完成。总检查文档数: %d。", totalChecked)
	if totalErrors > 0 {
		log.WithContext(ctx).Errorf("在验证过程中发生 %d 个查询错误。", totalErrors)
	}
	if totalMismatched > 0 {
		log.WithContext(ctx).Errorf("发现 %d 个文档的旧 QaRef (id=0, text!='') 数量与新 TDocTextReference 数量不匹配。", totalMismatched)
	} else if totalErrors == 0 {
		log.WithContext(ctx).Info("所有已检查的文档数据一致，未发现不匹配项。")
	} else {
		log.WithContext(ctx).Warn("验证完成，但存在查询错误，请检查日志以获取详细信息。")
	}
}

// MigrateTextReferencesLogic 用于将纯文本参考资料迁移到文档的逻辑
type MigrateTextReferencesLogic struct {
	ctx context.Context
	db  *gorm.DB
}

// NewMigrateTextReferencesLogic 创建迁移逻辑实例
func NewMigrateTextReferencesLogic(ctx context.Context) *MigrateTextReferencesLogic {
	return &MigrateTextReferencesLogic{
		ctx: ctx,
		db:  model.NewConnection(ctx),
	}
}

// MigrateAll 迁移所有纯文本参考资料
func (l *MigrateTextReferencesLogic) MigrateAll(batchSize int, goroutines int) error {
	var totalDocs int64
	if err := l.db.Model(&model.TDoc{}).Where("ref IS NOT NULL").Count(&totalDocs).Error; err != nil {
		return err
	}

	log.WithContext(l.ctx).Infof("开始迁移纯文本参考资料，总文档数: %d", totalDocs)

	var lastID uint64 = 0
	batchProcessed := 0
	totalProcessed := 0

	for {
		var docs []*model.TDoc
		if err := l.db.Model(&model.TDoc{}).
			Where("id > ? AND ref IS NOT NULL", lastID).
			Select("id", "ref").
			Order("id ASC").
			Limit(batchSize).
			Find(&docs).Error; err != nil {
			return err
		}

		if len(docs) == 0 {
			break
		}

		lastID = docs[len(docs)-1].ID

		wg := xsync.NewGroup(l.ctx, xsync.GroupQuota(goroutines))
		for _, doc := range docs {
			doc := doc
			wg.Go(func(ctx2 context.Context) error {
				err := l.migrateDocReferences(doc)
				if err != nil {
					log.WithContext(l.ctx).Errorf("迁移文档 %d 的参考资料失败: %v", doc.ID, err)
				}
				return nil
			})
		}
		wg.Wait()

		batchProcessed += len(docs)
		totalProcessed += len(docs)

		log.WithContext(l.ctx).Infof("已处理 %d/%d 个文档", totalProcessed, totalDocs)

		if len(docs) < batchSize {
			break
		}
	}

	log.WithContext(l.ctx).Infof("纯文本参考资料迁移完成，共处理 %d 个文档", totalProcessed)
	return nil
}

// verifySingleDocConsistency checks a single document for consistency between
// its original QaRefs (ID 0, non-empty text) and the migrated TDocTextReference entries.
// Returns: (match bool, expectedOldCount int, actualNewCount int, error)
func (l *MigrateTextReferencesLogic) verifySingleDocConsistency(doc *model.TDoc) (bool, int, int, error) {
	if doc == nil {
		log.WithContext(l.ctx).Warn("verifySingleDocConsistency: 收到 nil 文档指针")
		return false, 0, 0, fmt.Errorf("nil document provided")
	}
	if doc.Ref == nil {
		var actualNewRefCount int64
		if err := l.db.Model(&model.TDocTextReference{}).Where("doc_id = ?", doc.ID).Count(&actualNewRefCount).Error; err != nil {
			log.WithContext(l.ctx).Errorf("验证错误 doc %d (Ref is nil): 查询 TDocTextReference 失败: %v", doc.ID, err)
			return false, 0, 0, err
		}
		if actualNewRefCount == 0 {
			log.WithContext(l.ctx).Infof("验证通过 doc %d: Ref 为 nil 且无 TDocTextReference 记录。", doc.ID)
			return true, 0, 0, nil
		}
		log.WithContext(l.ctx).Warnf("验证失败 doc %d: Ref 为 nil，但找到 %d 条 TDocTextReference 记录。", doc.ID, actualNewRefCount)
		return false, 0, int(actualNewRefCount), nil
	}

	expectedOldRefCount := 0
	for _, ref := range doc.Ref.QaRef {
		if ref.Id == 0 && ref.Text != "" {
			expectedOldRefCount++
		}
	}

	var actualNewRefCount int64
	if err := l.db.Model(&model.TDocTextReference{}).Where("doc_id = ?", doc.ID).Count(&actualNewRefCount).Error; err != nil {
		log.WithContext(l.ctx).Errorf("验证错误 doc %d: 查询 TDocTextReference 失败: %v", doc.ID, err)
		return false, expectedOldRefCount, 0, err
	}

	if expectedOldRefCount != int(actualNewRefCount) {
		log.WithContext(l.ctx).Warnf("验证失败 doc %d: 预期 %d 条纯文本 QaRef (id=0, text!=''), 实际找到 %d 条 TDocTextReference 记录。", doc.ID, expectedOldRefCount, actualNewRefCount)
		return false, expectedOldRefCount, int(actualNewRefCount), nil
	}

	log.WithContext(l.ctx).Infof("验证通过 doc %d: 预期 %d 条, 实际 %d 条纯文本参考资料均一致。", doc.ID, expectedOldRefCount, actualNewRefCount)
	return true, expectedOldRefCount, int(actualNewRefCount), nil
}

// migrateDocReferences 迁移单个文档的纯文本参考资料
func (l *MigrateTextReferencesLogic) migrateDocReferences(doc *model.TDoc) error {
	if doc == nil || doc.Ref == nil || len(doc.Ref.QaRef) == 0 {
		return nil
	}

	hasTextRefToMigrate := false
	for _, ref := range doc.Ref.QaRef {
		if ref.Id == 0 && ref.Text != "" {
			hasTextRefToMigrate = true
			break
		}
	}

	if !hasTextRefToMigrate {
		return nil
	}

	return model.Transaction(l.ctx, func(tx *gorm.DB) error {
		_, err := ExtractAndCreateTextRefDocs(tx, doc)
		if err != nil {
			return fmt.Errorf("事务中执行 ExtractAndCreateTextRefDocs 失败 (doc %d): %w", doc.ID, err)
		}
		return nil
	})
}

// ExtractAndCreateTextRefDocs 从文档的Ref.QaRef中提取纯文本参考资料并创建TDocTextReference记录
// 纯文本参考资料的特征是Id为0且Text不为空
func ExtractAndCreateTextRefDocs(tx *gorm.DB, doc *model.TDoc) ([]uint64, error) {
	if doc == nil || doc.Ref == nil || len(doc.Ref.QaRef) == 0 {
		return nil, nil
	}

	var textRefs []*model.TDocTextReference
	var newQaRefs []*model.QARef

	// 遍历QaRef，提取纯文本参考资料
	for _, ref := range doc.Ref.QaRef {
		if ref.Id == 0 && ref.Text != "" {
			// 创建纯文本参考资料记录
			textRef := &model.TDocTextReference{
				DocID: doc.ID,
				Text:  ref.Text,
			}
			textRefs = append(textRefs, textRef)
		} else {
			// 保留非纯文本的参考资料
			newQaRefs = append(newQaRefs, ref)
		}
	}

	// 如果没有纯文本参考资料，直接返回
	if len(textRefs) == 0 {
		return nil, nil
	}

	// 创建纯文本参考资料记录
	if err := tx.CreateInBatches(textRefs, 100).Error; err != nil {
		return nil, err
	}

	return nil, nil
}
