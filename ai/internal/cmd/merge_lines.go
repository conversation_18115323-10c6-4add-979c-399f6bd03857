package cmd

import (
	"context"
	"fmt"
	"os"
	"sync/atomic"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcrypto"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	docchunklogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

func mergeLines() {
	if !config.GetBool("tanlive.cmd.MERGELINES") {
		return
	}
	defer os.Exit(0)

	fmt.Println("merge lines start")

	var (
		ctx          = context.Background()
		parallels    = config.GetIntOr("PARALLELS", 5)
		lastId       uint64
		docID        = config.GetUint64("DOC")
		assistantID  = config.GetUint64("ASSISTANT")
		noMergeLines = config.GetBool("nomergelines")
		// 进度参数
		progressFinished atomic.Int32
		progressTicker   = time.NewTicker(config.GetDurationOr("PROGRESS.TICK", time.Minute))
		progressStop     = make(chan struct{})
	)

	newQuery := func(ctx context.Context, lastId, docID, assistantID uint64, parallels int, withRelation bool) *xorm.Query[model.TDoc] {
		query := model.NewQuery[model.TDoc](ctx).
			Select("id", "index_text", "index_text_md5", "rag_filename").
			OrderBy("id", false).
			Where("id > ? AND data_type IN ? AND index_text != ?",
				lastId, []aipb.DocType{aipb.DocType_DOCTYPE_FILE, aipb.DocType_DOCTYPE_TEXT}, "")

		if docID > 0 {
			query.Where("id = ?", docID)
		}

		if assistantID > 0 {
			query.Where("EXISTS (SELECT 1 FROM t_assistant_doc WHERE doc_id = t_doc.id AND assistant_id = ?)", assistantID)
		}

		if parallels > 0 {
			query.Limit(parallels)
		}

		if withRelation {
			query.With("AssistantDocs")
		}

		return query
	}

	totalCount, err := newQuery(ctx, lastId, docID, assistantID, 0, false).Count()
	if err != nil {
		fmt.Printf("[%v] count total docs failed: %v\n", time.Now(), err.Error())
		return
	}
	fmt.Printf("[%v] total docs: %d\n", time.Now(), totalCount)

	printProgress := func() {
		fmt.Printf("[%v] finished: %d, last doc: %d\n", time.Now(), progressFinished.Load(), lastId)
	}

	go func() {
		for {
			select {
			case <-progressStop:
				progressTicker.Stop()
				break
			case <-progressTicker.C:
				printProgress()
			}
		}
	}()

	for {
		query := newQuery(ctx, lastId, docID, assistantID, parallels, true)
		docs, err := query.Get()
		if err != nil {
			panic(err)
		}
		if len(docs) == 0 {
			break
		}

		g := xsync.NewGroup(ctx, xsync.GroupQuota(parallels))
		for _, doc := range docs {
			doc := doc
			g.Go(func(ctx context.Context) error {
				defer func() {
					progressFinished.Add(1)
				}()

				if !noMergeLines {
					doc.IndexText = rag.MergeLines(doc.IndexText)
					newMd5 := xcrypto.Md5(doc.IndexText)
					if newMd5 != doc.IndexTextMd5 {
						doc.IndexTextMd5 = newMd5
						if err := model.NewQuery[model.TDoc](ctx).Omit("update_date").
							Select("index_text", "index_text_md5").Save(doc); err != nil {
							fmt.Printf("save doc [%d]: %v\n", doc.ID, err)
							return nil
						}
					}
				}

				// 分段信息
				if len(doc.AssistantDocs) > 0 {
					assistantIDs := make([]uint64, 0, len(doc.AssistantDocs))
					for _, ad := range doc.AssistantDocs {
						assistantIDs = append(assistantIDs, ad.AssistantID)
					}
					acs, err := model.NewQuery[model.TAssistantCollection](ctx).With("Collection").
						GetBy("assistant_id IN ?", assistantIDs)
					if err != nil {
						fmt.Printf("save doc [%d]: %v\n", doc.ID, err)
						return nil
					}
					collections := make(map[uint64]*model.TCollection, len(acs))
					for _, ac := range acs {
						collections[ac.AssistantID] = ac.Collection
					}
					for _, assistantDoc := range doc.AssistantDocs {
						collection := collections[assistantDoc.AssistantID]
						if collection == nil {
							continue
						}
						assistant := &model.TAssistant{ID: assistantDoc.AssistantID}
						if err = new(docchunklogic.ChunkDocLogic).UpdateDocChunks(ctx, doc, collection, assistant); err != nil {
							fmt.Printf("upsert doc chunks [%d]: %v\n", doc.ID, err)
							return nil
						}
					}
				}

				return nil
			})
			lastId = doc.ID
		}
		g.Wait()
	}

	progressStop <- struct{}{}

	printProgress()

	fmt.Println("merge lines finished")
}
