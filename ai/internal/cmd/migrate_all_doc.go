package cmd

import (
	"context"
	"fmt"
	"os"
	"sync"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/publisher"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type TMigrateAllDoc struct {
	ID           uint64 `gorm:"primaryKey;column:id" json:"id"`
	DocID        uint64 `gorm:"column:doc_id" json:"doc_id"`
	CollectionID uint64 `gorm:"column:collection_id" json:"collection_id"`
	TaskID       string `gorm:"column:task_id" json:"task_id"`
	Success      int    `gorm:"column:success" json:"success"`
	IsDelete     bool   `gorm:"column:is_delete" json:"is_delete"`
	Error        string `gorm:"column:error" json:"error"`
}

type migrateAllDocStats struct {
	// 所有助手ID
	assistantIDs []uint64
	// 失败文档锁
	failedDocsLock sync.Mutex
	// 助手失败的文档ID
	assistantFailedDocs map[uint64][]uint64
	// 助手总数
	totalAssistants int
	// 文档总数
	totalDocs int
}

// 迁移所有的文档
// 按助手纬度，遍历每个助手下所有的文档进行迁移
func migrateAllDoc() {
	if !config.GetBool("tanlive.cmd.MIGRATEALLDOC") {
		return
	}
	defer os.Exit(0)

	ctx := context.Background()
	taskID := config.GetStringOr("TASK.ID", uuid.New().String())
	dryRun := config.GetBool("DRY.RUN")
	truncateChunks := config.GetBool("TRUNCATE.CHUNKS")
	onlyCreateCollection := config.GetBool("ONLY.CREATE.COLLECTION")

	fmt.Printf("开始文档迁移任务分发，任务ID：%s\n", taskID)

	// 清空分段
	if truncateChunks && !dryRun {
		if err := truncateAllChunks(ctx); err != nil {
			fmt.Println(err.Error())
			os.Exit(1)
		}
	}

	stats := dispatchMigrateAssistantsDocs(ctx, taskID, dryRun, onlyCreateCollection)

	fmt.Printf("完成文档迁移任务分发，任务ID：%s，助手总数：%d，文档总数：%d\n", taskID, stats.totalAssistants, stats.totalDocs)

	if len(stats.assistantFailedDocs) > 0 {
		fmt.Printf("以下助手有分发失败的文档：\n")
		for _, assistantID := range stats.assistantIDs {
			failedDocs := stats.assistantFailedDocs[assistantID]
			if len(failedDocs) > 0 {
				fmt.Printf("助手ID：%d，失败文档数：%d，失败文档ID：%v\n", assistantID, len(failedDocs), failedDocs)
			}
		}
	}
}

func truncateAllChunks(ctx context.Context) error {
	_, err := model.NewQuery[model.TAssistantDoc](ctx).UpdateBy(map[string]any{"chunk_detail_id": 0}, "1=1")
	if err != nil {
		return fmt.Errorf("truncate all chunks: %w", err)
	}
	return nil
}

func dispatchMigrateAssistantsDocs(ctx context.Context, taskID string, dryRun bool, onlyCreateCollection bool) *migrateAllDocStats {
	// 配置
	var (
		// 每次查询助手的批次大小
		batchSize = config.GetIntOr("ASSISTANT.BATCH.SIZE", 200)
		// 查询报错的最大重试次数
		maxRetry = config.GetIntOr("MAX.RETRY", 10)
	)

	var (
		// 统计
		stats = &migrateAllDocStats{
			assistantFailedDocs: make(map[uint64][]uint64),
		}
		// 助手上一批次最大的id
		lastMaxID uint64
		// 查询助手已尝试次数
		tried = 0
		// 协程组
		g = xsync.NewGroup(ctx, xsync.GroupQuota(config.GetIntOr("DOC.CONCURRENCY", 20)))
	)

	for {
		assistants, err := model.NewQuery[model.TAssistant](ctx).
			With("AssistantCollections", func(db *gorm.DB) *gorm.DB {
				return db.Select("assistant_id", "collection_id")
			}).
			With("AssistantCollections.Collection", func(db *gorm.DB) *gorm.DB {
				return db.Select("id", "rag_name", "lang")
			}).
			Select("id").
			Limit(batchSize).
			Where("id > ? and is_draft = ?", lastMaxID, 0).
			OrderBy("id", false).
			Get()
		if err != nil {
			tried++
			fmt.Printf("[查询助手列表] 已尝试次数：%d，失败原因：%v\n", tried, err)
			if tried >= maxRetry {
				os.Exit(1)
			}
			continue
		}
		tried = 0
		if len(assistants) == 0 {
			break
		}

		for _, assistant := range assistants {
			lastMaxID = assistant.ID

			// 没有collection的助手不需要迁移
			if len(assistant.AssistantCollections) == 0 ||
				assistant.AssistantCollections[0].Collection == nil ||
				assistant.AssistantCollections[0].Collection.RagName == "" {
				continue
			}

			if onlyCreateCollection {
				assistant := assistant
				g.SafeGo(func(ctx context.Context) error {
					// 创建collection（不用处理错误）
					client.GetRagClient().CreateCollection(ctx, &rag.ReqCreateCollection{
						CollectionName: assistant.AssistantCollections[0].Collection.RagName,
						Lang:           assistant.AssistantCollections[0].Collection.Lang,
					})
					return nil
				})
			} else {
				docsCount := migrateAssistantAllDoc(ctx, dryRun, taskID, g, stats, assistant.ID, assistant.AssistantCollections[0].CollectionID)
				if docsCount > 0 {
					stats.totalAssistants += 1
					stats.totalDocs += docsCount
					stats.assistantIDs = append(stats.assistantIDs, assistant.ID)
				}
			}
		}
	}

	g.Wait()

	return stats
}

func migrateAssistantAllDoc(ctx context.Context, dryRun bool, taskID string, g xsync.Group, stats *migrateAllDocStats, assistantID, collectionID uint64) int {
	fmt.Printf("开始助手[%d]的文档迁移任务发布，CollectionID：[%d]\n", assistantID, collectionID)

	var (
		// 上一批次最大的id
		lastMaxID uint64
		// 按助手维度迁移
		batchSize = config.GetIntOr("DOC.BATCH.SIZE", 1000)
		// 最大重试次数
		maxRetry = config.GetIntOr("MAX.RETRY", 10)
		// 尝试次数
		tried = 0
		// 文档总数
		total int
	)

	docExists := model.NewQuery[model.TDoc](ctx).Select("1").Where("t_doc.id = t_assistant_doc.doc_id")

	for {
		var docIDs []uint64
		err := model.NewQuery[model.TAssistantDoc](ctx).
			Where("doc_id > ? and assistant_id = ? and state = ?", lastMaxID, assistantID, 1).
			Where("EXISTS (?)", docExists.DB()).
			OrderBy("doc_id", false).
			Limit(batchSize).
			Pluck("doc_id", &docIDs)
		if err != nil {
			tried++
			fmt.Printf("[查询文档列表] 尝试次数：%d，失败原因：%v\n", tried, err)
			if tried >= maxRetry {
				os.Exit(1)
			}
			continue
		}
		tried = 0
		if len(docIDs) == 0 {
			break
		}

		// 过滤已执行过的任务
		var executedDocIDs []uint64
		err = model.NewQuery[TMigrateAllDoc](ctx).
			Where("doc_id in ? AND collection_id = ? AND task_id = ?",
				docIDs, collectionID, taskID).
			Pluck("doc_id", &executedDocIDs)
		if err != nil {
			fmt.Printf("[查询已执行过的任务] 尝试次数：%d，失败原因：%v\n", tried, err)
			os.Exit(1)
		}
		executedDocIDsMap := make(map[uint64]bool)
		for _, docID := range executedDocIDs {
			executedDocIDsMap[docID] = true
		}

		for _, docID := range docIDs {
			lastMaxID = docID

			if executedDocIDsMap[docID] {
				continue
			}

			if !dryRun {
				publishDocToCollectionEventAsync(g, assistantID, taskID, docID, collectionID, stats, false)
			}

			total += 1
		}
	}

	fmt.Printf("完成助手[%d]的文档迁移任务发布，CollectionID：[%d]，文档总数：%d\n", assistantID, collectionID, total)

	return total
}

func publishDocToCollectionEventAsync(g xsync.Group, assistantID uint64, taskID string, docID, collectionID uint64, stats *migrateAllDocStats, isDelete bool) {
	g.SafeGo(func(ctx context.Context) error {
		err := publisher.RagTopicPublisher.PublishDocToCollectionEvent(ctx, &eventspb.DocToCollection{
			TaskId:       taskID,
			DocId:        docID,
			CollectionId: collectionID,
			IsDelete:     isDelete,
		})
		if err != nil {
			stats.failedDocsLock.Lock()
			stats.assistantFailedDocs[assistantID] = append(stats.assistantFailedDocs[assistantID], docID)
			stats.failedDocsLock.Unlock()
			fmt.Printf("[发布文档迁移任务失败] 文档ID: %d，原因：%s\n", docID, err.Error())
		}
		return nil
	})
}

func migrateAllFailedDoc() {
	if !config.GetBool("tanlive.cmd.MIGRATEALLFAILEDDOC") {
		return
	}
	defer os.Exit(0)

	ctx := context.Background()
	taskID := config.GetString("TASK.ID")
	dryRun := config.GetBool("DRY.RUN")

	if taskID == "" {
		fmt.Println("任务ID不能为空")
		os.Exit(1)
	}

	failedTasks, err := model.NewQuery[TMigrateAllDoc](ctx).GetBy("task_id = ? AND success = ?", taskID, 0)
	if err != nil {
		fmt.Printf("查询失败任务时出错：%s\n", err.Error())
		os.Exit(1)
	}

	if len(failedTasks) == 0 {
		fmt.Printf("没有失败任务！\n")
		os.Exit(1)
	}

	fmt.Printf("找到%d条失败任务\n", len(failedTasks))

	if dryRun {
		os.Exit(0)
	}

	g := xsync.NewGroup(ctx, xsync.GroupQuota(config.GetIntOr("DOC.CONCURRENCY", 20)))
	stats := &migrateAllDocStats{assistantFailedDocs: make(map[uint64][]uint64)}

	for _, failedTask := range failedTasks {
		publishDocToCollectionEventAsync(g, 0, taskID, failedTask.DocID, failedTask.CollectionID, stats, failedTask.IsDelete)
	}

	g.Wait()

	fmt.Printf("发布失败任务完成！")
}

type TDocSync struct {
	ID           uint64 `gorm:"primaryKey;column:id"`
	DocID        uint64 `gorm:"column:doc_id"`
	CollectionID uint64 `gorm:"column:collection_id"`
	HasNew       int    `gorm:"column:has_new"`
}

func (m *TDocSync) TableName() string {
	return "t_doc_sync"
}

func migrateAllIncrementalDoc() {
	if !config.GetBool("tanlive.cmd.MIGRATEALLINCREMENTALDOC") {
		return
	}
	defer os.Exit(0)

	ctx := context.Background()
	taskID := config.GetString("TASK.ID")
	minID := config.GetUint64("MIN.ID")

	if taskID == "" {
		fmt.Println("任务ID不能为空")
		os.Exit(1)
	}
	if minID == 0 {
		fmt.Println("没有指定最小任务ID")
		os.Exit(1)
	}

	// 同样的doc_id和collection_id只处理最后一个任务就行，因此按ID排倒序
	var tasks []*TDocSync
	err := model.NewConnection(ctx).
		Select("id, doc_id, collection_id, CASE WHEN new IS NULL THEN 0 ELSE 1 END AS has_new").
		Where("id > ?", minID).Order("id desc").
		Find(&tasks).Error
	if err != nil {
		fmt.Printf("查询同步任务时出错：%s\n", err.Error())
		os.Exit(1)
	}

	g := xsync.NewGroup(ctx, xsync.GroupQuota(config.GetIntOr("DOC.CONCURRENCY", 20)))
	stats := &migrateAllDocStats{assistantFailedDocs: make(map[uint64][]uint64)}

	set := make(map[struct{ docId, collectionId uint64 }]bool)
	for _, task := range tasks {
		if set[struct{ docId, collectionId uint64 }{task.DocID, task.CollectionID}] {
			continue
		}
		set[struct{ docId, collectionId uint64 }{task.DocID, task.CollectionID}] = true

		// 没有新的就算删除，否则就算新建
		isDelete := task.HasNew != 1
		publishDocToCollectionEventAsync(g, 0, taskID, task.DocID, task.CollectionID, stats, isDelete)
	}

	g.Wait()

	fmt.Printf("完成文档迁移任务分发，任务ID：%s，助手总数：%d，文档总数：%d\n", taskID, stats.totalAssistants, stats.totalDocs)
}
