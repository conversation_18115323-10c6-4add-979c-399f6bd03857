package cmd

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/golang/glog"
	"github.com/pkg/errors"
	"gorm.io/gorm"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk/table"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

// ExtractDocTable 批量提取文档表格信息并检查表头长度
// 同时也会检查 qa 是否超长
func ExtractDocTable() {
	if !config.GetBool("tanlive.cmd.EXTRACTDOCTABLE") {
		return
	}
	defer os.Exit(0)

	fmt.Println("开始提取文档表格信息...")
	ctx := context.TODO()

	// 获取配置参数
	batch := config.GetIntOr("tanlive.cmd.EXTRACTDOCTABLE.BATCH", 100)
	workers := config.GetIntOr("tanlive.cmd.EXTRACTDOCTABLE.WORKERS", 10)
	dryRun := config.GetBoolOr("tanlive.cmd.EXTRACTDOCTABLE.DRYRUN", false)
	minDocID := config.GetUint64Or("tanlive.cmd.EXTRACTDOCTABLE.MINDOCID", 0)
	maxDocID := config.GetUint64Or("tanlive.cmd.EXTRACTDOCTABLE.MAXDOCID", 0)
	timeout := config.GetIntOr("tanlive.cmd.EXTRACTDOCTABLE.TIMEOUT", 30)
	// 分片大小，每次查询的文档ID数量
	shardSize := config.GetIntOr("tanlive.cmd.EXTRACTDOCTABLE.SHARDSIZE", 5000)

	start := time.Now()
	db := model.NewConnection(ctx)

	// 如果没有指定最大文档ID，则获取当前最大ID
	if maxDocID == 0 {
		var maxID struct {
			MaxID uint64 `gorm:"column:max_id"`
		}
		err := db.WithContext(ctx).Model(&model.TDoc{}).
			Select("MAX(id) as max_id").
			Where("deleted_at IS NULL").
			Scan(&maxID).Error

		if err != nil {
			fmt.Printf("获取最大文档ID失败: %v\n", err)
			return
		}

		if maxID.MaxID == 0 {
			fmt.Println("没有找到需要处理的文档")
			return
		}

		maxDocID = maxID.MaxID
	}

	// 计算总分片数
	totalShards := (int(maxDocID-minDocID) + shardSize - 1) / shardSize
	fmt.Printf("将处理ID范围从 %d 到 %d 的文档，共分为 %d 个分片\n", minDocID, maxDocID, totalShards)

	// 文档处理统计
	var totalSuccess, totalFailure int
	totalProcessed := 0

	// 按分片处理文档
	for shard := 0; shard < totalShards; shard++ {
		shardStart := minDocID + uint64(shard*shardSize)
		shardEnd := shardStart + uint64(shardSize-1)
		if shardEnd > maxDocID {
			shardEnd = maxDocID
		}

		fmt.Printf("\n开始处理第 %d/%d 个分片 (ID范围: %d-%d)\n", shard+1, totalShards, shardStart, shardEnd)

		// 获取当前分片的文档ID
		docIDs, err := getDocIDsInRange(ctx, db, shardStart, shardEnd)
		if err != nil {
			fmt.Printf("获取分片 %d 的文档ID失败: %v\n", shard+1, err)
			continue
		}

		if len(docIDs) == 0 {
			fmt.Printf("分片 %d 中没有需要处理的文档\n", shard+1)
			continue
		}

		fmt.Printf("分片 %d 中找到 %d 个需要处理的文档\n", shard+1, len(docIDs))

		// 对当前分片的文档ID进行处理
		success, failure := processDocIDs(ctx, db, docIDs, batch, workers, timeout, dryRun)

		// 更新总计数
		totalSuccess += success
		totalFailure += failure
		totalProcessed += len(docIDs)

		// 输出当前进度
		fmt.Printf("分片 %d 处理完成: 成功 %d, 失败 %d\n", shard+1, success, failure)
		fmt.Printf("总进度: %d/%d (%.2f%%)\n", shard+1, totalShards, float64(shard+1)/float64(totalShards)*100)
	}

	// 输出最终统计结果
	duration := time.Since(start)
	fmt.Printf("\n处理完成，总共耗时: %v\n", duration)
	fmt.Printf("总计处理文档: %d 个\n", totalProcessed)
	fmt.Printf("成功: %d, 失败: %d\n", totalSuccess, totalFailure)
}

// 获取指定ID范围内的文档ID
func getDocIDsInRange(ctx context.Context, db *gorm.DB, minID, maxID uint64) ([]uint64, error) {
	var docIDs []uint64

	// 查询指定ID范围内未删除且解析成功的文档
	err := db.WithContext(ctx).Model(&model.TDoc{}).
		Where("deleted_at IS NULL").
		Where("state <> ?", ai.DocState_DOC_STATE_PARES_FAILED).
		Where("id >= ? AND id <= ?", minID, maxID).
		Select("id").
		Order("id ASC").
		Pluck("id", &docIDs).Error

	if err != nil {
		return nil, errors.Wrap(err, "查询文档ID失败")
	}

	return docIDs, nil
}

// 处理一批文档ID
func processDocIDs(ctx context.Context, db *gorm.DB, docIDs []uint64, batchSize, workers, timeoutSec int, dryRun bool) (int, int) {
	// 初始化工作队列
	jobs := make(chan uint64, batchSize)
	results := make(chan struct {
		docID uint64
		err   error
	}, batchSize)

	// 创建工作线程
	var wg sync.WaitGroup
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			worker(ctx, db, jobs, results, dryRun, time.Duration(timeoutSec)*time.Second)
		}()
	}

	// 启动结果收集协程
	resultsClosed := make(chan struct{})
	success := 0
	failure := 0

	go func() {
		for res := range results {
			if res.err != nil {
				glog.Errorf("处理文档%d失败: %v", res.docID, res.err)
				failure++
			} else {
				success++
			}

			// 输出处理进度
			if (success+failure)%100 == 0 || success+failure == len(docIDs) {
				fmt.Printf("批次进度: %d/%d (成功: %d, 失败: %d)\n", success+failure, len(docIDs), success, failure)
			}
		}
		close(resultsClosed)
	}()

	// 发送任务到队列
	for _, docID := range docIDs {
		jobs <- docID
	}
	close(jobs)

	// 等待所有工作线程完成
	wg.Wait()
	close(results)

	// 等待结果处理完成
	<-resultsClosed

	return success, failure
}

// 工作线程函数，处理单个文档
func worker(ctx context.Context, db *gorm.DB, jobs <-chan uint64, results chan<- struct {
	docID uint64
	err   error
}, dryRun bool, timeout time.Duration) {
	for docID := range jobs {
		// 为每个任务设置超时
		taskCtx, cancel := context.WithTimeout(ctx, timeout)

		// 处理单个文档
		err := processDocument(taskCtx, db, docID, dryRun)

		// 发送结果
		results <- struct {
			docID uint64
			err   error
		}{docID: docID, err: err}

		cancel()
	}
}

// 处理单个文档
func processDocument(ctx context.Context, db *gorm.DB, docID uint64, dryRun bool) error {
	var doc model.TDoc
	if err := db.WithContext(ctx).Select("id", "data_type").First(&doc, docID).Error; err != nil {
		return errors.Wrapf(err, "获取文档%d失败", docID)
	}
	if dryRun {
		if err := db.WithContext(ctx).Select("id", "index_text", "data_type").First(&doc, docID).Error; err != nil {
			return errors.Wrapf(err, "获取文档%d失败", docID)
		}
		// 在模拟运行时，只获取文档和表格信息，但不修改数据库
		// 提取表格
		tables, _ := table.ExtractMarkdownTables(doc.IndexText)
		fmt.Printf("文档ID %d: 提取到 %d 个表格\n", docID, len(tables))

		// 获取文档绑定的助手
		var assistantDocs []model.TAssistantDoc
		if err := db.WithContext(ctx).Where("doc_id = ?", docID).Find(&assistantDocs).Error; err != nil {
			return errors.Wrap(err, "获取文档绑定的助手失败")
		}

		fmt.Printf("文档ID %d: 绑定了 %d 个助手\n", docID, len(assistantDocs))
		return nil
	}

	var err error
	if doc.DataType == uint32(ai.DocType_DOCTYPE_FILE) {
		docTableLogic := table.NewDocTableLogic(db)
		err = docTableLogic.StoreOversizedTablesAllAssistant(ctx, docID)
	} else if doc.DataType == uint32(ai.DocType_DOCTYPE_QA) {
		err = logic.NewQaQuestionOversizeLogic(ctx).StoreOverSizedQuestionAllAssistant(ctx, docID)
	}
	// 实际处理文档表格
	if err != nil {
		return errors.Wrapf(err, "处理文档%d的表格失败", docID)
	}

	return nil
}
