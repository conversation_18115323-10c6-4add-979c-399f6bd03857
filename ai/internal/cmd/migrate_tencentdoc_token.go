package cmd

import (
	"context"
	"log"
	"os"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
)

// 迁移腾讯文档Token
func MigrateTencentDocToken() {
	if !config.GetBool("tanlive.cmd.MIGRATE_TENCENTDOC_TOKEN") {
		return
	}
	defer os.Exit(0)
	ctx := context.Background()
	l := logic.NewTencentDocWrapper()
	err := l.MigrateAllOldTokens(ctx)
	if err != nil {
		log.Fatal(err)
	}
	log.Println("迁移腾讯文档Token完成")
}

// 刷新腾讯文档用户信息
func RefreshTencentDocUserInfo() {
	if !config.GetBool("tanlive.cmd.REFRESH_TENCENTDOC_USER_INFO") {
		return
	}
	defer os.Exit(0)
	ctx := context.Background()
	err := logic.RefreshAllTencentDocUserInfo(ctx)
	if err != nil {
		log.Fatal(err)
	}
	log.Println("刷新腾讯文档用户信息完成")
}
