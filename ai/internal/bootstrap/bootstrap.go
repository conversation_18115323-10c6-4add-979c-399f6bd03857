package bootstrap

import (
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/cmd"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/tasks"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/tasks/server"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/validation"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/metric"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/miniprogram"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/ratelimiter"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/asr"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/tmt"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	supportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support"
)

// Boot 启动
func Boot(configPath string, bootstraps ...boot.BootstrapFunc) *boot.Bootstrapper {
	b := &boot.Bootstrapper{
		ConfigFile: configPath,
		AppBootstraps: []boot.BootstrapFunc{
			hashids.BootHashids,
			workweixin.BootWorkWeiXin,
			metric.TCloudBootWrapper(tmt.MetricType, tmt.BootTmtClient),
			xcos.BootCos,
			validation.BootValidation,
			ratelimiter.BootRateLimiter,
			tasks.Boot,
			asr.BootAsr,
			miniprogram.BootMiniProgram,
		},
		BeforeStart: []boot.Hook{
			boot.SimpleHook(cmd.RegisterCmd),
		},
		AfterStart: []boot.Hook{
			boot.SimpleHook(cmd.SyncDocToRag),
			boot.SimpleHook(metric.RegisterService(func() supportpb.SupportService {
				return client.SupportClient
			})),
		},
		AfterStop: []boot.Hook{
			boot.SimpleHook(cmd.StopSyncDocToRag),
			boot.SimpleHook(server.StopTaskServer),
		},
	}
	if err := b.Boot(bootstraps...); err != nil {
		panic(err)
	}
	return b
}
