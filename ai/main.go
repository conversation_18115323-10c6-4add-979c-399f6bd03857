package main

import (
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/bootstrap"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/handler"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/publisher"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/subscriber"
)

func main() {
	b := bootstrap.Boot("./etc/ai.toml")

	svc := b.NewRPCService()

	client.RegisterClient(svc)
	handler.RegisterRPCHandler(svc)
	subscriber.RegisterSubscriber(svc)
	publisher.RegisterPublisher(svc)

	if err := svc.Run(); err != nil {
		panic(err)
	}
}
