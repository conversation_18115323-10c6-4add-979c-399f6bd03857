class Shape {

    name;
    sides;
    sideLength;
  
    constructor(name, sides, sideLength) {
      this.name = name;
      this.sides = sides;
      this.sideLength = sideLength;
    }

    calcPerimeter() {
        let total =  this.sides * this.sideLength;
        console.log(`${this.name}的周长为${total}`);
    }
  
  }
      
square = new Shape("square", 4, 5);
square.calcPerimeter();

triangle = new Shape("triangle", 3, 3);
triangle.calcPerimeter();