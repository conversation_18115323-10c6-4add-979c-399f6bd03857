## AI服务详细结构
1. AI服务采用go-micro微服务框架实现，主要提供GRPC服务
2. 项目目录结构：
   - `/internal`: 主要业务逻辑代码
     - `/handler`: GRPC接口实现，处理来自BFF层的请求
     - `/logic`: 核心业务逻辑实现
     - `/model`: 数据库模型定义
     - `/client`: 调用其他微服务的客户端
     - `/bootstrap`: 服务启动配置
     - `/subscriber`: 消息订阅处理
     - `/validation`: 请求参数验证
     - `/cmd`: 命令行工具
     - `/tasks`: 后台任务
   - `/pkg`: 可共享的库代码
   - `/util`: 工具函数
   - `/bin`: 可执行文件
   - `/test`: 测试代码
   - `/log`: 日志文件夹
   - `main.go`: 程序入口

3. 服务注册与启动流程：
   - 通过`main.go`启动服务
   - `bootstrap.Boot`初始化配置
   - `NewRPCService`创建GRPC服务实例
   - `RegisterClient`注册访问其他服务的客户端
   - `RegisterRPCHandler`注册本地GRPC处理器
   - `RegisterSubscriber`注册消息订阅处理器

4. 接口实现流程：
   - 在`proto`文件中定义接口(参数和返回值)
   - 在`internal/handler`包中实现接口逻辑，如`ListQA`、`CreateQA`等
   - handler调用`internal/logic`实现具体业务逻辑
   - 通过`internal/model`操作数据库

5. 数据流转流程：
   - 前端发送HTTP请求到BFF层(bff-web或bff-mgmt)
   - BFF层将HTTP请求转换为GRPC请求调用AI层
   - AI层处理请求并返回结果
   - BFF层再将结果转换为HTTP响应返回给前端

6. 跨服务通信：
   - 通过`internal/client`包调用其他微服务，如`ReviewClient`和`SupportClient`
   - 客户端通过go-micro框架将调用转发到对应服务

7. 数据存储：
   - 使用GORM框架进行数据库操作
   - 模型定义在`internal/model`中，如`TDoc`等

8. BFF层详细说明：
   - 项目包含两个BFF服务：
     - `bff-web`: 门户端BFF，为前端门户提供HTTP接口
     - `bff-mgmt`: 运营端BFF，为运营管理后台提供HTTP接口
   - BFF层目录结构：
     - `/internal`: 主要业务逻辑
       - `/handler`: HTTP接口实现，处理来自前端的请求
       - `/logic`: 调用逻辑处理
       - `/client`: 用于调用GRPC服务的客户端
       - `/bootstrap`: 服务启动配置
     - `main.go`: 程序入口
   - BFF服务职责：
     - 接收前端HTTP请求
     - 处理参数验证和格式转换
     - 通过GRPC客户端调用微服务
     - 封装微服务响应并返回给前端

9. Proto文件结构：
   - 项目有独立的proto仓库
   - 包含微服务、BFF、错误码、事件等协议定义
   - 主要目录：
     - `/tanlive/ai`: AI微服务协议定义
     - `/tanlive/bff-web/ai`: 门户端BFF的AI相关协议
     - `/tanlive/bff-mgmt/ai`: 运营端BFF的AI相关协议
   - 协议定义包括：
     - 请求和响应消息结构
     - 服务接口定义
     - 错误码

10. 完整开发流程：
    - 需求分析和接口设计
    - 编写proto文件，同时修改：
      - 微服务的proto文件（如`/tanlive/ai/service.proto`）
      - 对应BFF层的proto文件（如`/tanlive/bff-web/ai/bff.proto`）
    - 在proto所在路径执行`make proto`生成代码
    - 实现微服务层：
      - 在AI服务的`internal/handler`中实现接口逻辑
      - 在`internal/logic`中实现业务逻辑
      - 必要时添加数据库模型和操作
    - 实现BFF层：
      - 在BFF服务的`internal/handler`中实现HTTP接口
      - 调用生成的GRPC客户端访问微服务
    - 单元测试和集成测试
    - 部署上线

11. 接口添加步骤：
    - 在微服务proto文件中添加新的RPC方法和消息定义
    - 在对应的BFF proto文件中添加HTTP接口定义和消息结构
    - 生成代码：在proto目录执行`make proto`
    - 实现微服务逻辑：添加handler和logic代码
    - 实现BFF层逻辑：添加handler代码调用微服务
    - 测试验证接口功能
