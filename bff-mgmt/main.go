package main

import (
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/bootstrap"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/cmd"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler"
	_ "e.coding.net/tencent-ssv/tanlive/services/pkg/validation"
)

func main() {
	b := bootstrap.Boot("./etc/bff-mgmt.toml")
	svc := b.NewBFFService()

	client.RegisterClient(svc)
	handler.RegisterBffHandler(svc)
	cmd.RegisterCmd(svc)

	if err := svc.Run(); err != nil {
		panic(err)
	}
}
