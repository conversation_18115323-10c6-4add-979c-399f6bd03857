package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// LoadIdentityCards 加载身份卡片
func LoadIdentityCards(ctx context.Context, identities []*basepb.Identity) (users []*iampb.UserCard, teams []*teampb.TeamCard, mgmtUsers []*mgmtpb.UserCard, err error) {
	if len(identities) == 0 {
		return
	}

	var userIDs, teamIDs, mgmtUserIDs []uint64
	for _, identity := range identities {
		if identity == nil {
			continue
		}
		switch identity.IdentityType {
		case basepb.IdentityType_IDENTITY_TYPE_USER:
			userIDs = append(userIDs, identity.IdentityId)
		case basepb.IdentityType_IDENTITY_TYPE_TEAM:
			teamIDs = append(teamIDs, identity.IdentityId)
			if identity.ExtraId > 0 {
				userIDs = append(userIDs, identity.ExtraId)
			}
		case basepb.IdentityType_IDENTITY_TYPE_MGMT:
			mgmtUserIDs = append(mgmtUserIDs, identity.IdentityId)
		}
	}

	errs := make([]error, 3)
	g := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	g.SafeGo(func(ctx context.Context) error {
		var err error
		users, err = LoadUserCards(ctx, userIDs)
		if err != nil {
			errs[0] = err
		}
		return nil
	})

	g.SafeGo(func(ctx context.Context) error {
		var err error
		teams, err = LoadTeamCards(ctx, teamIDs)
		if err != nil {
			errs[1] = err
		}
		return nil
	})

	g.SafeGo(func(ctx context.Context) error {
		var err error
		mgmtUsers, err = LoadMgmtUserCards(ctx, mgmtUserIDs)
		if err != nil {
			errs[2] = err
		}
		return nil
	})

	g.Wait()

	for _, e := range errs {
		if e != nil {
			err = e
			return
		}
	}

	return
}
