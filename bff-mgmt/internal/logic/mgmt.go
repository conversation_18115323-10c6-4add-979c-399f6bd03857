package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
)

// GetMgmtUsersMapByKey 通过主键查询运营端用户映射
func GetMgmtUsersMapByKey(ctx context.Context, userIDs []uint64) (map[uint64]*mgmtpb.OpUser, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	rsp, err := client.MgmtClient.GetUsers(ctx, &mgmtpb.ReqGetUsers{
		Id:             userIDs,
		WithTotalCount: false,
	})
	if err != nil {
		return nil, err
	}

	m := make(map[uint64]*mgmtpb.OpUser, len(rsp.UserSet))
	for _, user := range rsp.UserSet {
		m[user.Id] = user
	}
	return m, nil
}

// LoadMgmtUserCards 加载运营端用户卡片
func LoadMgmtUserCards(ctx context.Context, userIDs []uint64) ([]*mgmtpb.UserCard, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	rsp, err := client.MgmtClient.GetUsers(ctx, &mgmtpb.ReqGetUsers{
		Id:             userIDs,
		WithTotalCount: false,
	})
	if err != nil {
		return nil, err
	}

	cards := make([]*mgmtpb.UserCard, 0, len(rsp.UserSet))
	for _, user := range rsp.UserSet {
		cards = append(cards, &mgmtpb.UserCard{
			Id:         user.Id,
			Username:   user.Username,
			RemarkName: user.RemarkName,
		})
	}
	return cards, nil
}
