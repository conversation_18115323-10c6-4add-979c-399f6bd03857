package logic

import (
	"errors"
	"fmt"

	"gopkg.in/yaml.v2"
)

// Task task
type Task struct {
	Name         string   `yaml:"name"`
	Content      string   `yaml:"content,omitempty"`
	Remark       string   `yaml:"remark,omitempty"`
	Command      string   `yaml:"command,omitempty"`
	Extra        string   `yaml:"extra,omitempty"`
	Event        []Event  `yaml:"event,omitempty"`
	File         []File   `yaml:"file,omitempty"`
	Dependencies []string `yaml:"dependencies,omitempty"`
}

// Event event
type Event struct {
	Type     string `yaml:"type"`
	NextTask string `yaml:"next_task"`
	Content  string `yaml:"content,omitempty"`
}

// File file
type File struct {
	Type    string `yaml:"type"`
	Content string `yaml:"content"`
}

// CheckCreatGraphYaml 检查yaml格式以及任务语法依赖关系
func CheckCreatGraphYaml(yamlConfig string) (bool, error) {
	var data map[string][]Task
	if err := yaml.Unmarshal([]byte(yamlConfig), &data); err != nil {
		return false, err
	}

	// 检查任务名和依赖关系的重名以及事件中的 next_task 是否合法
	names := make(map[string]bool)

	for _, tasks := range data["tasks"] {
		if tasks.Name == "" {
			return false, errors.New("任务名不能为空")
		}
		if names[tasks.Name] {
			return false, errors.New(fmt.Sprintf("任务名 %s 重复", tasks.Name))
		}
		names[tasks.Name] = true
		for _, dep := range tasks.Dependencies {
			if dep == tasks.Name {
				return false, errors.New(fmt.Sprintf("任务 %s 的依赖不能是自身", tasks.Name))
			}
			if !names[dep] {
				return false, errors.New(fmt.Sprintf("任务 %s 的依赖任务 %s 不存在", tasks.Name))
			}
		}
	}

	for _, tasks := range data["tasks"] {
		for _, event := range tasks.Event {
			if event.NextTask == "end" {
				continue
			}
			if _, ok := names[event.NextTask]; !ok {
				return false, errors.New(fmt.Sprintf("任务 %s 的事件中的 next_task %s 未定义", tasks.Name, event.NextTask))
			}
		}
	}

	return true, nil
}
