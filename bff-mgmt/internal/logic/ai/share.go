package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
)

// ListAssistantCanShareDoc 获取可分享的助手列表
func ListAssistantCanShareDoc(ctx context.Context, createBy uint64, name, lang string) (*aipb.RspListAssistantCanShareDoc, error) {
	reqSender := &aipb.ReqListAssistantCanShareDoc{
		CreateBy:  createBy,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		Name:      name,
		Language:  lang,
	}

	sharedAssistant, err := client.AiClient.ListAssistantCanShareDoc(ctx, reqSender)
	if err != nil {
		return nil, err
	}

	return sharedAssistant, err
}
