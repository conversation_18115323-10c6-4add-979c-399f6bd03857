package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
)

// DescribeUserChatRecords ...
func DescribeUserChatRecords(ctx context.Context, req *bffaipb.ReqGetChatDetail,
	rsp *bffaipb.RspGetChatDetail) error {
	records, err := client.AiClientByID(req.Id).DescribeUserChatRecords(ctx, &aipb.ReqDescribeUserChatRecords{
		ChatId:    req.Id,
		Page:      &base.Paginator{Offset: req.Offset, Limit: req.Limit},
		Keyword:   req.Keyword,
		SendRange: req.SendRange,
	})
	if err != nil {
		return err
	}
	rsp.ChatDetail.Records = records.Records
	rsp.TotalCount = records.TotalCount

	if len(records.Records) > 0 {
		rsp.ChatDetail.CreateDate = records.Records[0].SendDate
		rsp.ChatDetail.FinishDate = records.Records[len(records.Records)-1].SendDate
	}
	return nil
}
