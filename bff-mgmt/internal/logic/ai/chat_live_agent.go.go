package ai

import (
	"context"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
)

// CheckAndSwitchChatLiveAgent 检查并切换用户人工坐席
func CheckAndSwitchChatLiveAgent(ctx context.Context, liveAgentName, externalUserId string, assistantId uint64) (ai.SwitchChatState, error) {
	if assistantId == 0 || externalUserId == "" { // 会话信息是否异常
		log.WithContext(ctx).Errorw("SwitchChatLiveAgent chat info error",
			"assistantId", assistantId, "externalUserId", externalUserId)
		return ai.SwitchChatState_SWITCH_CHAT_STATE_CHAT_ERROR, nil
	}
	// 获取助手信息
	rsp, err := client.AiClient.GetAssistant(ctx, &ai.ReqGetAssistant{
		Id: assistantId,
	})
	if err != nil {
		return ai.SwitchChatState_SWITCH_CHAT_STATE_UNSPECIFIED, err
	}
	assistant := rsp.AssistantDetail
	if assistant == nil || assistant.Id == 0 {
		log.WithContext(ctx).Errorw("CheckAndSwitchChatLiveAgent Assistant is empty", "assistantId", assistantId)
		return ai.SwitchChatState_SWITCH_CHAT_STATE_CHAT_ERROR, nil
	}
	if len(assistant.AppCode) == 0 {
		log.WithContext(ctx).Errorw("CheckAndSwitchChatLiveAgent Assistant AppCode is empty", "assistant", assistant)
		return ai.SwitchChatState_SWITCH_CHAT_STATE_CHAT_ERROR, nil
	}

	// 调用微信接口
	msgEvent, err := workweixin.BuildNewClient(workweixin.Client, assistant.CorpId, LoadOpenWorkAccessToken).
		ChangeSessionStatus(assistant.AppCode, externalUserId, 3, liveAgentName)
	if err != nil {
		log.WithContext(ctx).Errorw("CheckAndSwitchChatLiveAgent ChangeSessionStatus error", "err", err)
		return ai.SwitchChatState_SWITCH_CHAT_STATE_UNSPECIFIED, err
	}
	if msgEvent.ErrCode != 0 {
		log.WithContext(ctx).Errorw("CheckAndSwitchChatLiveAgent ChangeSessionStatus msgEvent error",
			"err", err, "msgEvent", msgEvent)
		return ai.SwitchChatState_SWITCH_CHAT_STATE_LIVE_AGENT_OFFLINE, nil
	}
	return ai.SwitchChatState_SWITCH_CHAT_STATE_SUCCESS, nil
}

// ChatUsernameMask 对话用户名脱敏
/*
 如果大于2个字符，则显示一头一尾两个字符，中间*。如果等于2个字符，第二个字符变为*。如果等于1个字符，显示1个*
*/
func ChatUsernameMask(input string) string {
	runes := []rune(input)
	length := len(runes)

	if length == 0 {
		return ""
	}

	if length == 1 {
		return "*"
	} else if length == 2 {
		return string(runes[0]) + "*"
	} else {
		result := make([]rune, length)
		result[0] = runes[0]
		result[length-1] = runes[length-1]
		for i := 1; i < length-1; i++ {
			result[i] = '*'
		}
		return string(result)
	}
}

// LoadOpenWorkAccessToken 加载企业授权码
func LoadOpenWorkAccessToken(toFromUser string) string {

	if config.GetString("weixin.open.myCorpID") == toFromUser {
		return ""
	}
	ctx := context.Background()

	corpIdKey := fmt.Sprintf("%s:corpId", toFromUser)
	accessToken := xredis.Default.Get(ctx, corpIdKey).Val()
	if len(accessToken) == 0 {
		permanentCode, err := client.AiClient.DescribeOpenWechat(ctx, &ai.ReqDescribeOpenWechat{CorpId: toFromUser})
		if err != nil {
			return ""
		}

		suiteTicketCache := xredis.Default.Get(ctx, "WeixinSuiteAccessToken")

		service := workweixin.MessageService{}
		token, err := service.GetCorPIdAccessToken(suiteTicketCache.Val(), toFromUser, permanentCode.PermanentCode)
		if err != nil {
			log.WithContext(ctx).Errorw("LoadOpenWorkAccessToken GetCorPIdAccessToken err", "err", err)
			return ""
		}

		xredis.Default.Set(ctx, corpIdKey, token, 7000*time.Second)
		return token

	}

	return accessToken
}

// CheckAssistantEnable 助手是否启用
func CheckAssistantEnable(ctx context.Context, assistantId uint64) (*ai.AssistantDetail, error) {
	assistant, err := client.AiClient.GetAssistant(ctx, &ai.ReqGetAssistant{Id: assistantId})
	if err != nil {
		return nil, err
	}
	if assistant == nil || assistant.AssistantDetail == nil || !assistant.AssistantDetail.Enabled {
		return nil, xerrors.NewCode(errorspb.AiError_AiAssistantDisabled)
	}
	return assistant.AssistantDetail, nil
}

// CheckAssistantLiveAgentEnable 助手是否启用人工
func CheckAssistantLiveAgentEnable(ctx context.Context, assistantId uint64) (*ai.AssistantDetail, error) {
	assistant, err := CheckAssistantEnable(ctx, assistantId)
	if err != nil {
		return nil, err
	}
	if assistant.LiveAgentMsg == nil || !assistant.LiveAgentMsg.Enable {
		return nil, xerrors.NewCode(errorspb.AiError_AiAssistantFeatureDisabled)
	}
	return assistant, nil
}
