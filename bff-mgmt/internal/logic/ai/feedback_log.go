package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// GetFeedbackLogs 查询用户反馈日志列表
type GetFeedbackLogs struct {
}

// Get ...
func (l *GetFeedbackLogs) Get(ctx context.Context,
	req *bffaipb.ReqGetFeedbackLogs) ([]*aipb.FullFeedbackLog, uint32, error) {
	rsp, err := client.AiClientByRegion(req.Region).GetFeedbackLogs(ctx, &aipb.ReqGetFeedbackLogs{
		Filter: &aipb.ReqGetFeedbackLogs_Filter{
			FeedbackId:      req.FeedbackId,
			Action:          req.Action,
			CreateIdentity:  req.CreateIdentity,
			CreateDateRange: req.CreateDateRange,
		},
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Relation: &aipb.ReqGetFeedbackLogs_Relation{
			Feedback: true,
		},
		OrderBy:        l.formatOrderBy(req.OrderBy),
		WithTotalCount: true,
	})
	if err != nil {
		return nil, 0, err
	}

	return rsp.Logs, rsp.TotalCount, nil
}

// LoadIdentities ...
func (l *GetFeedbackLogs) LoadIdentities(ctx context.Context, logs []*aipb.FullFeedbackLog) ([]*iampb.UserCard, []*teampb.TeamCard, []*mgmtpb.UserCard, error) {
	var identities []*basepb.Identity
	for _, log := range logs {
		identities = append(identities, log.Log.CreateIdentity)
	}

	return logic.LoadIdentityCards(ctx, identities)
}

func (l *GetFeedbackLogs) formatOrderBy(orderBy []*basepb.OrderBy) []*basepb.OrderBy {
	formatted := make([]*basepb.OrderBy, 0, len(orderBy)+1)
	for _, o := range orderBy {
		if o.Column == "id" {
			continue
		}
		formatted = append(formatted, o)
	}
	formatted = append(formatted, &basepb.OrderBy{
		Column: "id",
		Desc:   true,
	})
	return formatted
}

// LoadFeedbackExpectedDocs 加载预期命中的文档
func LoadFeedbackExpectedDocs(ctx context.Context, feedbacks []*aipb.FullFeedback) error {
	docIDs := make([]uint64, 0)
	docSet := make(map[uint64]bool)
	for _, feedback := range feedbacks {
		for _, docID := range feedback.ExpectedDocId {
			if docID == 0 {
				continue
			}
			if docSet[docID] {
				continue
			}
			docSet[docID] = true
			docIDs = append(docIDs, docID)
		}
		for _, docID := range feedback.ExpectedMgmtDocId {
			if docID == 0 {
				continue
			}
			if docSet[docID] {
				continue
			}
			docSet[docID] = true
			docIDs = append(docIDs, docID)
		}
	}

	if len(docIDs) == 0 {
		return nil
	}

	rsp, err := client.AiClient.GetDocs(ctx, &aipb.ReqGetDocs{
		DocId: docIDs,
	})
	if err != nil {
		return err
	}

	m := make(map[uint64]*aipb.ChatMessageDoc, len(rsp.Docs))
	for _, doc := range rsp.Docs {
		m[doc.Id] = doc
	}

	for _, feedback := range feedbacks {
		feedback.ExpectedDocs = make([]*aipb.ChatMessageDoc, 0, len(feedback.ExpectedDocId))
		for _, docID := range feedback.ExpectedDocId {
			doc := m[docID]
			if doc != nil {
				feedback.ExpectedDocs = append(feedback.ExpectedDocs, doc)
			}
		}

		feedback.ExpectedMgmtDocs = make([]*aipb.ChatMessageDoc, 0, len(feedback.ExpectedMgmtDocId))
		for _, docID := range feedback.ExpectedMgmtDocId {
			doc := m[docID]
			if doc != nil {
				feedback.ExpectedMgmtDocs = append(feedback.ExpectedMgmtDocs, doc)
			}
		}
	}

	return nil
}
