package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
)

// CreateSystemDocCopyLogic ...
type CreateSystemDocCopyLogic struct {
}

// BatchCreate 批量创建
func (l *CreateSystemDocCopyLogic) BatchCreate(ctx context.Context,
	items []*bffaipb.ReqCreateSystemDocCopy_Item, createBy *mgmtpb.OpUser) []*bffaipb.RspCreateSystemDocCopy_Result {
	results := make([]*bffaipb.RspCreateSystemDocCopy_Result, len(items))

	g := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	for i, item := range items {
		i, item := i, item
		g.SafeGo(func(ctx context.Context) error {
			results[i] = l.Create(ctx, item, createBy)
			return nil
		})
	}

	g.Wait()

	return results
}

// Create 创建
func (l *CreateSystemDocCopyLogic) Create(ctx context.Context, item *bffaipb.ReqCreateSystemDocCopy_Item,
	createBy *mgmtpb.OpUser) *bffaipb.RspCreateSystemDocCopy_Result {
	_, err := client.AiClient.CreateSystemDocCopy(ctx, &aipb.ReqCreateSystemDocCopy{
		CopyFrom: item.DocId,
		CreateBy: &basepb.Identity{
			IdentityId:   createBy.Id,
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
		},
	})
	if err != nil {
		e := xerrors.FromError(err)
		return &bffaipb.RspCreateSystemDocCopy_Result{
			Code: e.Code,
		}
	}
	return nil
}

// EnableSystemDocLogic ...
type EnableSystemDocLogic struct {
}

// BatchEnable 批量启用
func (l *EnableSystemDocLogic) BatchEnable(ctx context.Context,
	docIDs []uint64, operator *mgmtpb.OpUser) []*bffaipb.RspEnableSystemDoc_Result {
	results := make([]*bffaipb.RspEnableSystemDoc_Result, len(docIDs))

	g := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	for i, docID := range docIDs {
		i, docID := i, docID
		g.SafeGo(func(ctx context.Context) error {
			results[i] = l.Enable(ctx, docID, operator)
			return nil
		})
	}

	g.Wait()

	return results
}

// Enable 启用
func (l *EnableSystemDocLogic) Enable(ctx context.Context,
	docID uint64, operator *mgmtpb.OpUser) *bffaipb.RspEnableSystemDoc_Result {
	_, err := client.AiClient.EnableSystemDoc(ctx, &aipb.ReqEnableSystemDoc{
		DocId: docID,
		Operator: &basepb.Identity{
			IdentityId:   operator.Id,
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
		},
	})
	if err != nil {
		e := xerrors.FromError(err)
		return &bffaipb.RspEnableSystemDoc_Result{
			Code: e.Code,
		}
	}
	return nil
}

// DisableSystemDocLogic ...
type DisableSystemDocLogic struct {
}

// BatchDisable 批量停用
func (l *DisableSystemDocLogic) BatchDisable(ctx context.Context,
	docIDs []uint64, operator *mgmtpb.OpUser) []*bffaipb.RspDisableSystemDoc_Result {
	results := make([]*bffaipb.RspDisableSystemDoc_Result, len(docIDs))

	g := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	for i, docID := range docIDs {
		i, docID := i, docID
		g.SafeGo(func(ctx context.Context) error {
			results[i] = l.Disable(ctx, docID, operator)
			return nil
		})
	}

	g.Wait()

	return results
}

// Disable 停用
func (l *DisableSystemDocLogic) Disable(ctx context.Context,
	docID uint64, operator *mgmtpb.OpUser) *bffaipb.RspDisableSystemDoc_Result {
	_, err := client.AiClient.DisableSystemDoc(ctx, &aipb.ReqDisableSystemDoc{
		DocId: docID,
		Operator: &basepb.Identity{
			IdentityId:   operator.Id,
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
		},
	})
	if err != nil {
		e := xerrors.FromError(err)
		return &bffaipb.RspDisableSystemDoc_Result{
			Code: e.Code,
		}
	}
	return nil
}

// DeleteSystemDocLogic ...
type DeleteSystemDocLogic struct {
}

// BatchDelete 批量删除
func (l *DeleteSystemDocLogic) BatchDelete(ctx context.Context,
	docIDs []uint64, operator *mgmtpb.OpUser) []*bffaipb.RspDeleteSystemDoc_Result {
	results := make([]*bffaipb.RspDeleteSystemDoc_Result, len(docIDs))

	g := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	for i, docID := range docIDs {
		i, docID := i, docID
		g.SafeGo(func(ctx context.Context) error {
			results[i] = l.Delete(ctx, docID, operator)
			return nil
		})
	}

	g.Wait()

	return results
}

// Delete 删除
func (l *DeleteSystemDocLogic) Delete(ctx context.Context,
	docID uint64, operator *mgmtpb.OpUser) *bffaipb.RspDeleteSystemDoc_Result {
	_, err := client.AiClient.DeleteSystemDoc(ctx, &aipb.ReqDeleteSystemDoc{
		DocId: docID,
		Operator: &basepb.Identity{
			IdentityId:   operator.Id,
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
		},
	})
	if err != nil {
		e := xerrors.FromError(err)
		return &bffaipb.RspDeleteSystemDoc_Result{
			Code: e.Code,
		}
	}
	return nil
}
