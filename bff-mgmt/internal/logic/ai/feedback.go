package ai

import (
	"context"
	"net/http"
	"sync"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// GetFeedbacks 查询用户反馈列表
type GetFeedbacks struct {
	AssistantIds map[uint64]struct{}
}

// Get ...
func (l *GetFeedbacks) Get(ctx context.Context, req *bffaipb.ReqGetFeedbacks) ([]*aipb.FullFeedback, uint32, error) {
	rsp, err := client.AiClientByRegion(req.Region).GetFeedbacks(ctx, &aipb.ReqGetFeedbacks{
		Filter: &aipb.ReqGetFeedbacks_Filter{
			CreateBy:        req.CreateBy,
			CreateDateRange: req.CreateDateRange,
			FeedbackId:      req.FeedbackId,
			AssistantIds:    req.AssistantIds,
			RegionCodes:     req.RegionCodes,
		},
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Relation: &aipb.ReqGetFeedbacks_Relation{
			References:       true,
			ExpectedDocs:     true,
			OriginalQuestion: true,
			OriginalAnswer:   true,
			ExpectedMgmtDocs: true,
		},
		WithTotalCount: true,
		OrderBy:        l.formatOrderBy(req.OrderBy),
	})
	if err != nil {
		return nil, 0, err
	}

	return rsp.Feedbacks, rsp.TotalCount, nil
}

// ToResponse 协议转换
func (l *GetFeedbacks) ToResponse(feedbacks []*aipb.FullFeedback, assistantInfo AssistantInfo) []*bffaipb.RspGetFeedbacks_Item {
	items := make([]*bffaipb.RspGetFeedbacks_Item, 0, len(feedbacks))
	for _, feedback := range feedbacks {
		feedback.Feedback.AssistantName = assistantInfo.GetName(feedback.Feedback.AssistantId)
		items = append(items, &bffaipb.RspGetFeedbacks_Item{
			Feedback:         feedback.Feedback,
			References:       feedback.References,
			OriginalQuestion: feedback.OriginalQuestion,
			OriginalAnswer:   feedback.OriginalAnswer,
			ExpectedDocs:     feedback.ExpectedDocs,
			ExpectedMgmtDocs: feedback.ExpectedMgmtDocs,
		})
	}
	return items
}

func (l *GetFeedbacks) formatOrderBy(orderBy []*basepb.OrderBy) []*basepb.OrderBy {
	formatted := make([]*basepb.OrderBy, 0, len(orderBy)+1)
	for _, o := range orderBy {
		if o.Column == "id" {
			continue
		}
		formatted = append(formatted, o)
	}
	formatted = append(formatted, &basepb.OrderBy{
		Column: "id",
		Desc:   true,
	})
	return formatted
}

// FindFeedback 查询用户反馈详情
type FindFeedback struct {
}

// Find ...
func (l *FindFeedback) Find(ctx context.Context, id uint64) (*aipb.FullFeedback, error) {
	rsp, err := client.AiClientByID(id).GetFeedbacks(ctx, &aipb.ReqGetFeedbacks{
		Filter: &aipb.ReqGetFeedbacks_Filter{
			FeedbackId: []uint64{id},
		},
		Relation: &aipb.ReqGetFeedbacks_Relation{
			References:       true,
			ExpectedDocs:     true,
			OriginalQuestion: true,
			OriginalAnswer:   true,
			ExpectedMgmtDocs: true,
		},
	})
	if err != nil {
		return nil, err
	}
	if len(rsp.Feedbacks) == 0 {
		return nil, xerrors.NotFoundError("feedback not found")
	}
	return rsp.Feedbacks[0], nil
}

// ReadFeedback 标记已读
func (l *FindFeedback) ReadFeedback(ctx context.Context, feedback *aipb.Feedback, readBy uint64) error {
	if feedback.State != aipb.FeedbackState_FEEDBACK_STATE_UNREAD {
		return nil
	}

	_, err := client.AiClientByID(feedback.Id).ReadFeedback(ctx, &aipb.ReqReadFeedback{
		FeedbackId: feedback.Id,
		UserType:   &aipb.ReqReadFeedback_MgmtReadBy{MgmtReadBy: readBy},
	})
	if err != nil {
		return err
	}

	return nil
}

// AcceptFeedback 采用用户反馈
type AcceptFeedback struct {
}

// BatchAccept 批量采用
func (l *AcceptFeedback) BatchAccept(ctx context.Context,
	feedbackIDs []uint64, operator *basepb.Identity) []*bffaipb.RspAcceptFeedback_Result {
	results := make([]*bffaipb.RspAcceptFeedback_Result, len(feedbackIDs))

	var wg sync.WaitGroup
	wg.Add(len(feedbackIDs))

	for i, feedbackID := range feedbackIDs {
		go func(i int, feedbackID uint64) {
			defer wg.Done()

			code, err := l.Accept(ctx, feedbackID, operator)
			if err != nil {
				log.WithContext(ctx).Errorw("accept feedback failed", "feedback", feedbackID, "err", err)
				code = http.StatusInternalServerError
			}
			results[i] = &bffaipb.RspAcceptFeedback_Result{
				FeedbackId: feedbackID,
				Code:       code,
			}
		}(i, feedbackID)
	}

	wg.Wait()

	return results
}

// Accept 采用
func (l *AcceptFeedback) Accept(ctx context.Context, feedbackID uint64, operator *basepb.Identity) (int32, error) {
	if feedbackID == 0 {
		return http.StatusNotFound, nil
	}

	_, err := client.AiClientByID(feedbackID).AcceptFeedback(ctx, &aipb.ReqAcceptFeedback{
		FeedbackId: feedbackID,
		Operator:   operator,
	})
	if err != nil {
		return 0, err
	}
	return 0, nil
}

// LoadFeedbackIdentityCards 加载身份卡片
func LoadFeedbackIdentityCards(ctx context.Context, feedbacks []*aipb.FullFeedback) ([]*iampb.UserCard, []*teampb.TeamCard, []*mgmtpb.UserCard, error) {
	var identities []*basepb.Identity
	for _, feedback := range feedbacks {
		identities = append(identities,
			feedback.Feedback.UserFeedbackBy,
			feedback.Feedback.OpFeedbackBy,
			feedback.Feedback.CreateIdentity,
			feedback.Feedback.UpdateIdentity,
			feedback.Feedback.MgmtFeedbackBy,
		)
	}
	return logic.LoadIdentityCards(ctx, identities)
}
