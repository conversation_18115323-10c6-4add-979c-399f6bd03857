package ai

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	"github.com/golang/protobuf/ptypes/timestamp"
)

// GetAssistantCollectionIds 获取助手对应的collection id
func GetAssistantCollectionIds(ctx context.Context, assistantIds ...uint64) (map[uint64][]uint64, []uint64, error) {
	rsp, err := client.AiClient.ListAssistant(ctx, &aipb.ReqListAssistant{
		Ids:            assistantIds,
		WithCollection: true,
	})
	if err != nil {
		return nil, nil, err
	}
	mp := make(map[uint64][]uint64)
	merged := make([]uint64, 0)

	for _, a := range rsp.Assistants {
		tmp := make([]uint64, 0, len(a.Collections))
		for _, v := range a.Collections {
			tmp = append(tmp, v.Id)
			merged = append(merged, v.Id)
		}
		mp[a.Id] = tmp
	}
	xslice.SetUint64UniqueNoZero(&merged, xslice.SortNot)
	return mp, merged, nil
}

// AssistantInfo ...
type AssistantInfo struct {
	//NameMap           map[uint64]string
	//EnNameMap         map[uint64]string
	//ChatIdleMinuteMap map[uint64]int32
	infos map[uint64]*aipb.RspGetAssistantInfoMap_Info
}

// GetAssistantInfo 获取助手信息
func GetAssistantInfo(ctx context.Context, ids ...uint64) AssistantInfo {
	a := AssistantInfo{}
	rsp, err := client.AiClient.GetAssistantInfoMap(ctx, &aipb.ReqGetAssistantInfoMap{
		Ids: ids,
	})
	if err != nil || rsp == nil {
		log.WithContext(ctx).Errorw("GetAssistantMap error", "err", err, "rsp", rsp)
		return a
	}
	if len(rsp.InfoMap) == 0 {
		return a
	}
	a.infos = rsp.InfoMap

	return a
}

// GetName 获取中文名称
func (a AssistantInfo) GetName(aId uint64) string {
	if v, ok := a.infos[aId]; ok {
		return v.Name
	}
	return ""
}

// GetNameEn 获取英文名称
func (a AssistantInfo) GetNameEn(aId uint64) string {
	if v, ok := a.infos[aId]; ok {
		return v.NameEn
	}
	return ""
}

// GetChatStatus 获取助手会话状态
func (a AssistantInfo) GetChatStatus(currentState aipb.ChatCurrentState, assistantId uint64, chatType aipb.ChatType,
	updateDate *timestamp.Timestamp) aipb.ChatCurrentState {
	if currentState == aipb.ChatCurrentState_CHAT_CURRENT_STATE_REPLACED {
		return aipb.ChatCurrentState_CHAT_CURRENT_STATE_REPLACED
	}

	if updateDate == nil {
		return aipb.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED
	}

	if v, ok := a.infos[assistantId]; ok {
		minutes := time.Duration(v.ChatIdleDuration) * time.Minute
		if (chatType == aipb.ChatType_CHAT_TYPE_WECHAT || chatType == aipb.ChatType_CHAT_TYPE_WHATSAPP) &&
			updateDate.AsTime().Before(time.Now().Add(-minutes)) {
			return aipb.ChatCurrentState_CHAT_CURRENT_STATE_TIMEOUT // 超过助手的最大持续时间，被标记为已结束
		}
	}

	return aipb.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED
}

// GetAssistantsPageLogic ...
type GetAssistantsPageLogic struct {
}

// Get 获取
func (l *GetAssistantsPageLogic) Get(ctx context.Context,
	req *bffaipb.ReqGetAssistantsPage) ([]*aipb.FullAssistant, uint32, error) {
	rsp, err := client.AiClient.GetAssistants(ctx, &aipb.ReqGetAssistants{
		Filter: &aipb.ReqGetAssistants_Filter{
			UserId:         req.UserId,
			TeamId:         req.TeamId,
			CreateDate:     req.CreateDate,
			Channel:        req.Channel,
			Enabled:        req.Enabled,
			CollectionLang: req.CollectionLang,
			TermsConfirmed: req.TermsConfirmed,
			AssistantId:    req.AssistantId,
			RoutePath:      req.RoutePath,
			AssistantName:  req.AssistantName,
			Model:          req.Model,
			SearchEngine:   req.SearchEngine,
			IsDraft:        req.IsDraft,
			BatchNo:        req.BatchNo,
			DocId:          req.DocId,
		},
		Relation: &aipb.ReqGetAssistants_Relation{
			Admin:             true,
			Collection:        true,
			LiveAgent:         true,
			TermsConfirmation: true,
			Allowlist:         true,
		},
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		OrderBy:        l.formatOrderBy(req.OrderBy),
		WithTotalCount: true,
	})
	if err != nil {
		return nil, 0, err
	}
	return rsp.Assistants, rsp.TotalCount, nil
}

// LoadIdentityCards 加载身份卡片
func (l *GetAssistantsPageLogic) LoadIdentityCards(ctx context.Context, assistants []*aipb.FullAssistant) (users []*iampb.UserCard, teams []*teampb.TeamCard, mgmtUsers []*mgmtpb.UserCard, err error) {
	var identities []*basepb.Identity
	for _, assistant := range assistants {
		if assistant.Assistant.CreateBy != nil {
			identities = append(identities, assistant.Assistant.CreateBy)
		}
		if assistant.Assistant.UpdateBy != nil {
			identities = append(identities, assistant.Assistant.UpdateBy)
		}
		identities = append(identities, assistant.Assistant.Config.Admins...)
	}

	users, teams, mgmtUsers, err = logic.LoadIdentityCards(ctx, identities)

	return
}

func (l *GetAssistantsPageLogic) formatOrderBy(orderBy []*basepb.OrderBy) []*basepb.OrderBy {
	if len(orderBy) == 0 {
		return []*basepb.OrderBy{
			{
				Column: "update_date",
				Desc:   true,
			},
		}
	}
	return orderBy
}

// GetAssistantLogsPageLogic ...
type GetAssistantLogsPageLogic struct {
}

// Get ...
func (l *GetAssistantLogsPageLogic) Get(ctx context.Context,
	req *bffaipb.ReqGetAssistantLogsPage) ([]*aipb.AssistantLog, uint32, error) {
	rsp, err := client.AiClient.GetAssistantLogs(ctx, &aipb.ReqGetAssistantLogs{
		Filter: &aipb.ReqGetAssistantLogs_Filter{
			AssistantId: []uint64{req.AssistantId},
			Action:      req.Action,
			CreateDate:  req.CreateDate,
		},
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		WithTotalCount: true,
	})
	if err != nil {
		return nil, 0, err
	}

	return rsp.Logs, rsp.TotalCount, nil
}

// LoadIdentityCards 加载身份卡片
func (l *GetAssistantLogsPageLogic) LoadIdentityCards(ctx context.Context, logs []*aipb.AssistantLog) (users []*iampb.UserCard, teams []*teampb.TeamCard, mgmtUsers []*mgmtpb.UserCard, err error) {
	var identities []*basepb.Identity
	for _, log := range logs {
		if log.CreateBy != nil {
			identities = append(identities, log.CreateBy)
		}
		if log.Changes != nil {
			if log.Changes.Old != nil {
				for _, identity := range log.Changes.Old.Admins {
					identities = append(identities, identity)
				}
			}
			if log.Changes.New != nil {
				for _, identity := range log.Changes.New.Admins {
					identities = append(identities, identity)
				}
			}
		}
	}

	users, teams, mgmtUsers, err = logic.LoadIdentityCards(ctx, identities)

	return
}

// LoadUserLabelConfig 加载标签
func (l *GetAssistantLogsPageLogic) LoadUserLabelConfig(ctx context.Context, logs []*aipb.AssistantLog) error {
	var tagIDs []uint64
	for _, log := range logs {
		if log.Changes == nil {
			continue
		}
		if log.Changes.Old != nil && log.Changes.Old.UserLabelConfig != nil {
			tagIDs = append(tagIDs, log.Changes.Old.UserLabelConfig.TagIds...)
		}
		if log.Changes.New != nil && log.Changes.New.UserLabelConfig != nil {
			tagIDs = append(tagIDs, log.Changes.New.UserLabelConfig.TagIds...)
		}
	}
	if len(tagIDs) == 0 {
		return nil
	}

	rsp, err := client.TagClient.GetTagInfo(ctx, &tagpb.ReqGetTagInfo{
		TagIds: tagIDs,
	})
	if err != nil {
		return err
	}
	m := make(map[uint64]string, len(rsp.Tags))
	for _, tag := range rsp.Tags {
		m[tag.Id] = tag.Name
	}

	for _, log := range logs {
		if log.Changes == nil {
			continue
		}
		if log.Changes.Old != nil && log.Changes.Old.UserLabelConfig != nil {
			log.Changes.Old.UserLabelConfig.TagNames = make([]string, len(log.Changes.Old.UserLabelConfig.TagIds))
			for i, tagID := range log.Changes.Old.UserLabelConfig.TagIds {
				log.Changes.Old.UserLabelConfig.TagNames[i] = m[tagID]
			}
		}
		if log.Changes.New != nil && log.Changes.New.UserLabelConfig != nil {
			log.Changes.New.UserLabelConfig.TagNames = make([]string, len(log.Changes.New.UserLabelConfig.TagIds))
			for i, tagID := range log.Changes.New.UserLabelConfig.TagIds {
				log.Changes.New.UserLabelConfig.TagNames[i] = m[tagID]
			}
		}
	}

	return nil
}

// GetAssistantUserLabelConfigs 查询助手的标签
func GetAssistantUserLabelConfigs(ctx context.Context,
	assistantIDs []uint64, withNames bool) (map[uint64]*aipb.AssistantUserLabelConfig, error) {
	if len(assistantIDs) == 0 {
		return nil, nil
	}

	rsp, err := client.TagClient.GetTagBinding(ctx, &tagpb.ReqGetTagBinding{
		DataIds:       assistantIDs,
		DataType:      basepb.DataType_DATA_TYPE_AI_ASSISTANT,
		TaggableTypes: []tagpb.TaggableType{tagpb.TaggableType_TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL},
		Language:      "zh",
	})
	if err != nil {
		return nil, err
	}

	cfgs := make(map[uint64]*aipb.AssistantUserLabelConfig, len(assistantIDs))
	for _, assistantID := range assistantIDs {
		var cfg *aipb.AssistantUserLabelConfig
		if tagBinding, ok := rsp.TagBinding[assistantID]; ok {
			if tags, ok := tagBinding.TagBindingMap[uint64(tagpb.TaggableType_TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL)]; ok {
				cfg = &aipb.AssistantUserLabelConfig{
					TagIds: make([]uint64, 0, len(tags.Tags)),
				}
				if withNames {
					cfg.TagNames = make([]string, 0, len(tags.Tags))
				}
				for _, tag := range tags.Tags {
					cfg.TagIds = append(cfg.TagIds, tag.Id)
					if withNames {
						cfg.TagNames = append(cfg.TagNames, tag.Name)
					}
				}
				cfgs[assistantID] = cfg
			}
		}
	}

	return cfgs, nil
}
