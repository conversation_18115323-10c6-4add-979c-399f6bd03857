package mgmt

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffmgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/mgmt"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
)

// GetRoles 查询角色列表逻辑
type GetRoles struct {
	roles     []*mgmtpb.OpRole
	userCount map[uint64]uint32
}

// GetFromRpc 从RPC服务查询
func (l *GetRoles) GetFromRpc(ctx context.Context, req *bffmgmtpb.ReqGetRoles) error {
	rsp, err := client.MgmtClient.GetRoles(ctx, &mgmtpb.ReqGetRoles{
		Filter: l.newFilter(req),
	})
	if err != nil {
		return err
	}

	l.roles = rsp.RoleSet
	l.userCount = rsp.UserCountMap

	return nil
}

// Sort 排序
func (l *GetRoles) Sort() *GetRoles {
	admin := make([]*mgmtpb.OpRole, 0)
	custom := make([]*mgmtpb.OpRole, 0)
	unclassified := make([]*mgmtpb.OpRole, 0)

	for _, role := range l.roles {
		switch role.Type {
		case mgmtpb.RoleType_ROLE_TYPE_ADMIN:
			admin = append(admin, role)
		case mgmtpb.RoleType_ROLE_TYPE_UNCLASSIFIED:
			unclassified = append(unclassified, role)
		default:
			custom = append(custom, role)
		}
	}

	l.roles = l.roles[:0]
	l.roles = append(l.roles, admin...)
	l.roles = append(l.roles, custom...)
	l.roles = append(l.roles, unclassified...)

	return l
}

// ToRsp 协议转换
func (l *GetRoles) ToRsp(rsp *bffmgmtpb.RspGetRoles) {
	roles := make([]*bffmgmtpb.OpRole, 0, len(l.roles))
	for _, role := range l.roles {
		roles = append(roles, &bffmgmtpb.OpRole{
			Id:             role.Id,
			RoleName:       role.RoleName,
			State:          uint64(role.State),
			Type:           uint64(role.Type),
			AccountCount:   uint64(l.userCount[role.Id]),
			CreateBy:       role.CreateBy,
			CreateDate:     logic.FormatTimestamppb(role.CreateDate),
			LastUpdateBy:   role.LastUpdateBy,
			LastUpdateDate: logic.FormatTimestamppb(role.LastUpdateDate),
			CouldDel:       l.userCount[role.Id] == 0 && role.Type == mgmtpb.RoleType_ROLE_TYPE_CUSTOM,
		})
	}
	rsp.Roles = roles
}

func (l *GetRoles) newFilter(req *bffmgmtpb.ReqGetRoles) *mgmtpb.ReqGetRoles_Filter {
	filter := &mgmtpb.ReqGetRoles_Filter{
		SearchName:    req.Name,
		WithUserCount: true,
	}
	if req.State > 0 {
		filter.State = []basepb.DisableState{basepb.DisableState(req.State)}
	}
	if req.Type > 0 {
		filter.Type = []mgmtpb.RoleType{mgmtpb.RoleType(req.Type)}
	}
	return filter
}
