package logic

import (
	"context"
	"sync"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

// GetUserByKey 通过主键获取用户信息
func GetUserByKey(ctx context.Context, userID uint64) (*iampb.UserInfo, error) {
	if userID == 0 {
		return nil, nil
	}
	userMap, err := GetUsersMapByKey(ctx, []uint64{userID})
	if err != nil {
		return nil, err
	}
	return userMap[userID], nil
}

// GetUsersMapByKey 通过主键查询用户映射
func GetUsersMapByKey(ctx context.Context, userIDs []uint64) (map[uint64]*iampb.UserInfo, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	var nationalErr, internationalErr error
	var nationalUsers, internationalUsers []*iampb.UserInfo

	nationalIDs, internationalIDs := client.SplitUserIDByRegion(userIDs)

	var wg sync.WaitGroup

	if len(nationalIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			rsp, err := client.IamNational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
				Id: nationalIDs,
			})
			if err != nil {
				nationalErr = err
				return
			}
			nationalUsers = rsp.UserSet
		}()
	}

	if len(internationalIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			rsp, err := client.IamInternational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
				Id: internationalIDs,
			})
			if err != nil {
				internationalErr = err
				return
			}
			internationalUsers = rsp.UserSet
		}()
	}

	wg.Wait()

	if nationalErr != nil {
		return nil, nationalErr
	}
	if internationalErr != nil {
		return nil, internationalErr
	}

	m := make(map[uint64]*iampb.UserInfo, len(nationalUsers)+len(internationalUsers))
	for _, user := range nationalUsers {
		m[user.Id] = user
	}
	for _, user := range internationalUsers {
		m[user.Id] = user
	}
	return m, nil
}
