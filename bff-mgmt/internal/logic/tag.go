package logic

import (
	"context"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bfftagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/tag"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
)

const (
	EnTagLanguage = "en"
	ZhTagLanguage = "zh"
)

// TransformTagBffToPb 转换bbf侧pb到tag服务的pb
func TransformTagBffToPb(old *bfftagpb.Tag) *tagpb.Tag {
	if old == nil {
		return nil
	}
	return &tagpb.Tag{
		//Id:         old.Id,
		Weight:     old.Weight,
		Type:       old.Type,
		Name:       old.Name,
		NotifyType: old.NotifyType,
		IsTmt:      old.IsTmt,
	}
}

// TransformOrderBy 转变OrderBy类型
func TransformOrderBy(orderBy []string) ([]*base.OrderBy, error) {
	if len(orderBy) == 0 {
		return nil, nil
	}

	// "column asc" ====>&base.OrderBy{
	//			Column: split[0],
	//			Desc:   split[1] == "desc",
	//		}
	var orders []*base.OrderBy
	for _, v := range orderBy {
		if len(v) == 0 {
			continue
		}
		split := strings.Split(v, " ")
		if len(split) != 2 {
			return nil, xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
		}
		orders = append(orders, &base.OrderBy{
			Column: split[0],
			Desc:   split[1] == "desc",
		})
	}
	return orders, nil
}

// FillAssistantTag ... 填充助手标签
func FillAssistantTag(ctx context.Context, assistant []*aipb.FullAssistant) error {
	if len(assistant) == 0 {
		return nil
	}
	dataIds := make([]uint64, len(assistant))
	for i, v := range assistant {
		dataIds[i] = v.Assistant.Id
	}
	tagMap, err := GetTagBinding(ctx, dataIds, base.DataType_DATA_TYPE_AI_ASSISTANT,
		tagpb.TaggableType_TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL)
	if err != nil {
		return err
	}
	for _, v := range assistant {
		if tags, ok := tagMap[v.Assistant.Id]; ok {
			labelConfig := &aipb.AssistantUserLabelConfig{
				TagIds:   make([]uint64, len(tags)),
				TagNames: make([]string, len(tags)),
			}
			for i, tag := range tags {
				labelConfig.TagIds[i] = tag.Id
				labelConfig.TagNames[i] = tag.Name
			}
			v.Assistant.Config.UserLabelConfig = labelConfig
		}
	}
	return nil
}

// GetTagBinding 获取标签绑定信息
func GetTagBinding(ctx context.Context, dataIds []uint64, dataType base.DataType,
	taggableType tagpb.TaggableType) (map[uint64][]*tagpb.BindingTag_Tag, error) {
	tagRsp, err := client.TagClient.GetTagBinding(ctx, &tagpb.ReqGetTagBinding{
		DataIds:       dataIds,
		DataType:      dataType,
		TaggableTypes: []tagpb.TaggableType{taggableType},
		Language:      "zh",
	})
	if err != nil {
		log.WithContext(ctx).Errorw("GetTagBinding ERROR", "err", err)
		return nil, err
	}
	if tagRsp == nil || len(tagRsp.TagBinding) == 0 {
		return nil, nil
	}

	result := make(map[uint64][]*tagpb.BindingTag_Tag)
	for dataId, tagBinding := range tagRsp.TagBinding {
		if v, ok1 := tagBinding.TagBindingMap[uint64(taggableType)]; ok1 {
			result[dataId] = v.Tags
		}
	}
	return result, nil
}

// AsyncDeleteAssistantTag ... 异步删除助手标签
func AsyncDeleteAssistantTag(ctx context.Context, assistantIds []uint64) {
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		_, err := client.TagClient.DeleteSystemTag(ctx, &tagpb.ReqDeleteSystemTag{
			TaggableType: tagpb.TaggableType_TAGGABLE_TYPE_AI_ASSISTANT,
			AssistantIds: assistantIds,
			ActionType:   tagpb.TagActionType_TAG_ACTION_TYPE_RELATION_DELETION,
		})
		if err != nil {
			log.WithContext(ctx).Errorw("AsyncDeleteAssistantTag ERROR", "err", err)
		}
		return nil
	}, boot.TraceGo(ctx))
}

// AsyncUpdateAssistantAutoLabel ... 助手管理员更新，异步更新自动打标关系
func AsyncUpdateAssistantAutoLabel(ctx context.Context, admins []*base.Identity, assistantId, UpdateBy uint64) {
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		data := make([]*tagpb.DataInfo, len(admins))
		for i, v := range admins {
			switch v.IdentityType {
			case base.IdentityType_IDENTITY_TYPE_USER:
				data[i] = &tagpb.DataInfo{
					DataId:   v.IdentityId,
					DataType: base.DataType_DATA_TYPE_USER,
				}
			case base.IdentityType_IDENTITY_TYPE_TEAM:
				data[i] = &tagpb.DataInfo{
					DataId:   v.IdentityId,
					DataType: base.DataType_DATA_TYPE_TEAM,
				}
			}
		}
		_, err := client.TagClient.AutoBindingAITag(ctx, &tagpb.ReqAutoBindingAITag{
			UserId:   UpdateBy,
			DataInfo: data,
			LabelInfo: &tagpb.LabelInfo{
				LabelType:   tagpb.AutoLabelType_AUTO_LABEL_TYPE_AI_ASSISTANT_ADMIN,
				TeamId:      0,
				AssistantId: assistantId,
			},
		})
		if err != nil {
			log.WithContext(ctx).Errorw("AsyncUpdateAutoLabel ERROR", "err", err)
		}
		return nil
	}, boot.TraceGo(ctx))
}

// UpdateAssistantTag 更新助手的标签
func UpdateAssistantTag(ctx context.Context, config *aipb.AssistantUserLabelConfig, assistantId, UpdateBy uint64) error {
	if config == nil {
		return nil
	}
	err := ReplaceTagBinding(
		ctx,
		&tagpb.DataInfo{
			DataId:   assistantId,
			DataType: base.DataType_DATA_TYPE_AI_ASSISTANT,
		},
		tagpb.TaggableType_TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL,
		config.TagIds,
		config.TagNames,
		UpdateBy,
	)
	if err != nil {
		log.WithContext(ctx).Errorw("UpdateAssistantTag ERROR", "err", err)
	}
	return err
}

// ReplaceTagBinding ...
func ReplaceTagBinding(ctx context.Context,
	data *tagpb.DataInfo,
	taggableType tagpb.TaggableType,
	tagIds []uint64,
	newTagNames []string,
	updateBy uint64) error {
	_, err := client.TagClient.CreateTagBinding(ctx, &tagpb.ReqCreateTagBinding{
		UserId:       updateBy,
		DataInfo:     data,
		TagIds:       tagIds,
		TagNames:     newTagNames,
		TaggableType: taggableType,
		Action:       tagpb.TaggableActionType_TAGGABLE_ACTION_TYPE_MGMT,
	})
	return err
}
