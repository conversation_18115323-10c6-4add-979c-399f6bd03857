package bootstrap

import (
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/captcha"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/tmt"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
)

// Boot 启动
func Boot(configPath string, bootstraps ...boot.BootstrapFunc) *boot.Bootstrapper {
	b := &boot.Bootstrapper{
		ConfigFile: configPath,
		AppBootstraps: []boot.BootstrapFunc{
			captcha.BootCaptcha,
			tmt.BootTmt,
			workweixin.BootWorkWeiXin,
			hashids.BootHashids,
		},
		OnConfigChange: []func(){
			captcha.ReloadSkipIPConfig,
		},
	}

	if err := b.<PERSON>ot(bootstraps...); err != nil {
		panic(err)
	}

	return b
}
