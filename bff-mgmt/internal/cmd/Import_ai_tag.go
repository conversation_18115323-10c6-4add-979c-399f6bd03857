package cmd

import (
	"context"
	"fmt"
	"os"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// ImportAiAssistantAdminTag  导入ai助手管理员 标签
func ImportAiAssistantAdminTag() {// TANLIVE_CMD_IMPORTAIASSISTANTADMINTAG=true
	if !config.GetBool("tanlive.cmd.IMPORTAIASSISTANTADMINTAG") {
		return
	}
	defer os.Exit(0)
	fmt.Println("\n import ai assistant admin tag start ***************************")

	ctx := context.TODO()

	a := &ImportAiInfo{}
	assistantId := config.GetUint64("tanlive.cmd.IMPORTAIASSISTANTADMINTAG.ASSISTANTID")
	if assistantId > 0 {
		a.labelAssistantAdmin(ctx, assistantId)
	} else {
		a.labelAssistantAdmin(ctx)
	}
	fmt.Println("\n import ai assistant admin tag success ***************************")
}

// ImportAiAssistantChatUserTag  导入ai对话 标签
func ImportAiAssistantChatUserTag() {// TANLIVE_CMD_IMPORTAIASSISTANTCHATUSERTAG=true
	if !config.GetBool("tanlive.cmd.IMPORTAIASSISTANTCHATUSERTAG") {
		return
	}
	defer os.Exit(0)
	fmt.Println("\n import ai assistant chat user tag start ***************************")

	ctx := context.TODO()

	a := &ImportAiInfo{}
	assistantId := config.GetUint64("tanlive.cmd.IMPORTAIASSISTANTCHATUSERTAG.ASSISTANTID")
	if assistantId > 0 {
		a.labelAIChatUser(ctx, assistantId)
	} else {
		a.labelAIChatUser(ctx)
	}
	fmt.Println("\n import ai assistant chat user tag success ***************************")
}

type ImportAiInfo struct {
	admins   []*aipb.RspGetAssistantAdmin_Info
	isAbroad bool
}

// getAllAssistantAdmin 加载所有的助手管理员信息
func (a *ImportAiInfo) getAllAssistantAdmin(ctx context.Context, assistantId ...uint64) error {
	rpcRsp, err := client.AiClient.GetAssistantAdmin(ctx, &aipb.ReqGetAssistantAdmin{
		AssistantIds: assistantId,
	})
	if err != nil {
		return err
	}
	if rpcRsp != nil && len(rpcRsp.Admins) > 0 {
		a.admins = rpcRsp.Admins
	}
	return nil
}

// labelAssistantAdmin 给ai助手管理员打标
func (a *ImportAiInfo) labelAssistantAdmin(ctx context.Context, assistantId ...uint64) {
	err := a.getAllAssistantAdmin(ctx, assistantId...)
	if err != nil {
		log.WithContext(ctx).Errorw("import ai tag labelAssistantAdmin getAllAssistantAdmin failed\\n", "err", err)
		return
	}
	log.WithContext(ctx).Infow("import ai tag labelAssistantTag begin ***************************\\n", "assistant count", len(a.admins))
	for _, v := range a.admins {
		dataInfo := make([]*tagpb.DataInfo, len(v.Admins))
		var teamIds []uint64
		for i, admin := range v.Admins {
			dataInfo[i] = &tagpb.DataInfo{}
			if admin.IdentityType == basepb.IdentityType_IDENTITY_TYPE_USER {
				dataInfo[i] = &tagpb.DataInfo{
					DataType: basepb.DataType_DATA_TYPE_USER,
					DataId:   admin.IdentityId,
				}
			} else if admin.IdentityType == basepb.IdentityType_IDENTITY_TYPE_TEAM {
				dataInfo[i] = &tagpb.DataInfo{
					DataType: basepb.DataType_DATA_TYPE_TEAM,
					DataId:   admin.IdentityId,
				}
				teamIds = append(teamIds, admin.IdentityId)
			}
		}
		if len(dataInfo) == 0 {
			continue
		}

		// 助手管理员自动打标
		_, err = client.TagClient.AutoBindingAITag(ctx, &tagpb.ReqAutoBindingAITag{
			DataInfo:  dataInfo,
			LabelInfo: &tagpb.LabelInfo{LabelType: tagpb.AutoLabelType_AUTO_LABEL_TYPE_AI_ASSISTANT_ADMIN, AssistantId: v.AssistantId},
		})
		if err != nil {
			log.WithContext(ctx).Errorw("import ai tag labelAssistantAdmin failed\\n", "assistantId", v.AssistantId, "err", err)
			continue
		} else {
			log.WithContext(ctx).Debugw("import ai tag labelAssistantAdmin success\\n", "assistantId", v.AssistantId)
		}

		a.labelAssistantTeamTag(ctx, v.AssistantId, teamIds)

		time.Sleep(2 * time.Second)
	}
}

func (a *ImportAiInfo) labelAssistantTeamTag(ctx context.Context, assistantId uint64,teamIds []uint64) {
	if len(teamIds) == 0 {
		return
	}
	// 获取团队的员工信息
	staffRsp, err := client.TeamClient.GetTeamStaff(ctx, &teampb.ReqGetTeamStaff{
		TeamIds: teamIds,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("import ai tag GetTeamStaff failed\\n", "teamIds", teamIds, "err", err)
		return
	}
	if staffRsp == nil || len(staffRsp.StaffMap) == 0 {
		return
	}
	for teamId, staff := range staffRsp.StaffMap {
		staffInfo := make([]*tagpb.DataInfo, len(staff.UserIds))
		for i, userId := range staff.UserIds {
			staffInfo[i] = &tagpb.DataInfo{
				DataType: basepb.DataType_DATA_TYPE_USER,
				DataId:   userId,
			}
		}
		_, err = client.TagClient.AutoBindingAITag(ctx, &tagpb.ReqAutoBindingAITag{
			DataInfo:  staffInfo,
			LabelInfo: &tagpb.LabelInfo{LabelType: tagpb.AutoLabelType_AUTO_LABEL_TYPE_ASSISTANT_TEAM, AssistantId: assistantId, TeamId: teamId},
		})
		if err != nil {
			log.WithContext(ctx).Errorw("import ai tag labelAssistantTeam failed\\n", "assistantId",
				assistantId, "staffInfo", staffInfo, "err", err)
		} else {
			log.WithContext(ctx).Debugw("import ai tag labelAssistantTeam success\\n", "assistantId",
				assistantId, "staffInfo", staffInfo)
		}
	}
}

// labelAIChatUser 给ai对话用户打标
func (a *ImportAiInfo) labelAIChatUser(ctx context.Context, assistantId ...uint64) {
	err := a.getAllAssistantAdmin(ctx, assistantId...)
	if err != nil {
		log.WithContext(ctx).Errorw("import ai tag labelAIChatUser getAllAssistantAdmin failed\\n", "err", err)
		return
	}
	log.WithContext(ctx).Infow("import ai tag labelAIChatUser begin ***************************\\n", "assistant count", len(a.admins))
	assistantIds := make([]uint64, len(a.admins))
	for i, v := range a.admins {
		assistantIds[i] = v.AssistantId
	}

	aiClient := client.AiClient
	if a.isAbroad {
		aiClient = client.AiInternational
	}

	rpcRsp, err := aiClient.GetAssistantChatUser(ctx, &aipb.ReqGetAssistantChatUser{
		AssistantIds: assistantIds,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("import ai tag labelAIChatUser labelAssistantAdmin failed\\n",
			"isAbroad", a.isAbroad, "assistantIds", assistantIds, "err", err)
		return
	}
	if rpcRsp == nil || len(rpcRsp.UserInfo) == 0 {
		return
	}

	for _, v := range rpcRsp.UserInfo {
		if len(v.UserId) == 0 {
			continue
		}

		dataInfo := make([]*tagpb.DataInfo, len(v.UserId))
		for i, userId := range v.UserId {
			dataInfo[i] = &tagpb.DataInfo{
				DataType: basepb.DataType_DATA_TYPE_USER,
				DataId:   userId,
			}
		}

		_, err = client.TagClient.AutoBindingAITag(ctx, &tagpb.ReqAutoBindingAITag{
			DataInfo:  dataInfo,
			LabelInfo: &tagpb.LabelInfo{LabelType: tagpb.AutoLabelType_AUTO_LABEL_TYPE_AI_CHAT, AssistantId: v.AssistantId},
		})
		fmt.Printf("*******import ai tag labelAIChatUser info:%v ********",v.UserId)
		fmt.Println()
		if err != nil {
			log.WithContext(ctx).Errorw("import ai tag labelAIChatUser failed\\n", "assistantId", v.AssistantId, "err", err)
			fmt.Printf("*******import ai tag labelAIChatUser failed:%v ********",err)
			fmt.Println()
		} else {
			log.WithContext(ctx).Debugw("import ai tag labelAIChatUser success\\n", "assistantId", v.AssistantId)
			fmt.Println("*******import ai tag labelAIChatUser success ********")
		}
		fmt.Println()
		time.Sleep(1 * time.Second)
	}
}
