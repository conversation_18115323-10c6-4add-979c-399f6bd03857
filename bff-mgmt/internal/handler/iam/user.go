package iam

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/iam"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

// DisableUser 冻结用户
func (i Iam) DisableUser(ctx context.Context, req *bffiampb.ReqDisableUser, rsp *bffiampb.RspDisableUser) error {
	// me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	result, err := client.IamNational.DisableUser(ctx, &iampb.ReqDisableUser{
		UserId: req.UserId,
	})
	if err != nil {
		return err
	}

	// TODO 运营端操作日志

	results := make([]*bffiampb.RspDisableUser_Result, 0, len(result.Results))
	for _, item := range result.Results {
		results = append(results, &bffiampb.RspDisableUser_Result{
			Code: item.Code,
		})
	}

	rsp.Results = results

	return nil
}

// EnableUser 解冻用户
func (i Iam) EnableUser(ctx context.Context, req *bffiampb.ReqEnableUser, rsp *bffiampb.RspEnableUser) error {
	// me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	result, err := client.IamNational.EnableUser(ctx, &iampb.ReqEnableUser{
		UserId: req.UserId,
	})
	if err != nil {
		return err
	}

	// TODO 运营端操作日志

	results := make([]*bffiampb.RspEnableUser_Result, 0, len(result.Results))
	for _, item := range result.Results {
		results = append(results, &bffiampb.RspEnableUser_Result{
			Code: item.Code,
		})
	}

	rsp.Results = results

	return nil
}
