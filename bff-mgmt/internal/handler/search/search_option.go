package search

import (
	"context"
	"strings"

	logger "e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/search"
	pb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/search"

	"github.com/golang/protobuf/ptypes/empty"
)

func doModifySearchOption(ctx context.Context,
	req *bffpb.ReqModifyTeamAtlasSearchOption, rsp *empty.Empty,
	targetType pb.SearchOptionTargetType, referType pb.SearchOptionReferType) error {
	option := &pb.SearchOption{
		TargetType: targetType,
		ReferType:  referType,
		Language:   req.GetOption().GetLanguage(),
		Name:       req.GetOption().GetName(),
		Weight:     uint32(req.GetOption().GetWeight()),
		ReferId:    req.GetOption().GetReferId(),
		Status:     req.GetOption().GetStatus(),
		Id:         req.GetOption().GetId(),
	}
	_, err := client.SearchClient.ModifySearchOption(ctx, &pb.ReqModifySearchOption{
		Option:    option,
		Operation: req.GetOperation(),
		UserId:    xsession.SessionFromContext(ctx).GetLoginUser().Uint64(),
	})
	if err != nil {
		logger.WithContext(ctx).Errorw("ModifyTeamAtlasSearchOption err", "req", req, "err", err)
		return err
	}
	return nil
}

// 解析前端传入的order by
func parseOrderBy(orders []string) (parsedOrders []*base.OrderBy) {
	columMapping := map[string]string{
		"atlas_bind_num": "refer_bind_cnt",
		"weight":         "weight",
	}
	for _, v := range orders {
		vv := strings.Split(strings.ToLower(v), " ")
		if len(vv) != 2 {
			continue
		}
		if column, ok := columMapping[vv[0]]; ok {
			parsedOrders = append(parsedOrders, &base.OrderBy{
				Column: column,
				Desc:   vv[1] == "desc",
			})
		}
	}
	return
}

func doDescribe(ctx context.Context,
	req *bffpb.ReqDescribeTeamAtlasSearchOptions, rsp *bffpb.RspDescribeTeamAtlasSearchOptions,
	targetType pb.SearchOptionTargetType, referType pb.SearchOptionReferType) error {
	dasReq := &pb.ReqDescribeSearchOptions{
		Offset: req.Offset,
		Limit:  req.Limit,
	}
	dasReq.OrderBy = parseOrderBy(req.OrderBy)
	dasReq.Filter = &pb.ReqDescribeSearchOptions_Filter{
		Language:   req.GetFilter().GetLanguage(),
		Status:     req.GetFilter().GetStatus(),
		ReferType:  referType,
		TargetType: targetType,
	}
	dasRsp, err := client.SearchClient.DescribeSearchOptions(ctx, dasReq)
	if err != nil {
		logger.WithContext(ctx).Errorw("DescribeSearchOptions err", "req", req, "err", err)
		return err
	}
	rsp.TotalCount = uint32(dasRsp.Total)
	for _, v := range dasRsp.Options {
		rsp.Options = append(rsp.Options, &bffpb.RspDescribeTeamAtlasSearchOptions_TeamSearchOption{
			Option: &bffpb.SearchOption{
				Language: v.GetOption().GetLanguage(),
				Name:     v.GetOption().GetName(),
				Weight:   int32(v.GetOption().GetWeight()),
				ReferId:  v.GetOption().GetReferId(),
				Status:   v.GetOption().GetStatus(),
				Id:       v.GetOption().GetId(),
			},
			AtlasBindNum: uint32(v.ReferBindCount),
			AtlasName:    v.ReferTitle,
			AtlasDeleted: v.IsDelete,
			AtlasDraftId: v.ReferDraftId,
			AtlasState:   v.ReferState,
		})
	}
	return nil
}

// ModifyTeamAtlasSearchOption 编辑团队-图谱筛选项
func (o *Search) ModifyTeamAtlasSearchOption(ctx context.Context,
	req *bffpb.ReqModifyTeamAtlasSearchOption, rsp *empty.Empty) error {
	return doModifySearchOption(ctx, req, rsp,
		pb.SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_TEAM,
		pb.SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_ATLAS,
	)
}

// DescribeTeamAtlasSearchOptions 查询团队-图谱筛选项
func (o *Search) DescribeTeamAtlasSearchOptions(ctx context.Context,
	req *bffpb.ReqDescribeTeamAtlasSearchOptions, rsp *bffpb.RspDescribeTeamAtlasSearchOptions) error {
	return doDescribe(ctx, req, rsp, pb.
		SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_TEAM,
		pb.SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_ATLAS)
}

// ModifyProductAtlasSearchOption 编辑产品-图谱筛选项
func (o *Search) ModifyProductAtlasSearchOption(ctx context.Context,
	req *bffpb.ReqModifyTeamAtlasSearchOption, rsp *empty.Empty) error {
	return doModifySearchOption(ctx, req, rsp,
		pb.SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_PRODUCT,
		pb.SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_ATLAS,
	)
}

// DescribeProductAtlasSearchOptions 查询产品-图谱筛选项
func (o *Search) DescribeProductAtlasSearchOptions(ctx context.Context,
	req *bffpb.ReqDescribeTeamAtlasSearchOptions, rsp *bffpb.RspDescribeTeamAtlasSearchOptions) error {
	return doDescribe(ctx, req, rsp, pb.
		SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_PRODUCT,
		pb.SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_ATLAS)
}
