package search

import (
	"context"

	logger "e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	bffpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/search"
	pb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/search"
	"github.com/golang/protobuf/ptypes/empty"
)

var (
	// SearchOptionTargets 搜索目标的类型中文名称
	SearchOptionTargets = [...]string{"未知", "团队列表", "产品列表", "资源星球"}
	// SearchOptionRefers 筛选项的类型中文名称
	SearchOptionRefers = [...]string{"未知", "图谱", "行业认可", "资源系列"}
	// SearchOptionLanguage 语言
	SearchOptionLanguage = map[string]string{
		"zh": "CN",
		"en": "EN",
	}
)

// ModifySearchPrompt ...
func (o *Search) ModifySearchPrompt(ctx context.Context,
	req *bffpb.ReqModifySearchPrompt, rsp *empty.Empty) error {
	_, err := client.SearchClient.ModifySearchPrompt(ctx, &pb.ReqModifySearchPrompt{
		Id:     uint64(req.Id),
		Prompt: req.Content,
		UserId: xsession.SessionFromContext(ctx).GetLoginUser().Uint64(),
	})
	if err != nil {
		logger.WithContext(ctx).Errorw("ModifySearchPrompt err", "req", req, "err", err)
		return err
	}
	return nil
}

// DescribeSearchPrompts ...
func (o *Search) DescribeSearchPrompts(ctx context.Context,
	req *bffpb.ReqDescribeSearchPrompts, rsp *bffpb.RspDescribeSearchPrompts) error {
	results, err := client.SearchClient.DescribeSearchPrompts(ctx, &pb.ReqDescribeSearchPrompts{Language: req.GetLanguage()})
	if err != nil {
		logger.WithContext(ctx).Errorw("DescribeSearchPrompts err", "req", req, "err", err)
		return err
	}
	for _, v := range results.GetPrompts() {
		rsp.Prompts = append(rsp.Prompts, &bffpb.SearchPrompt{
			TargetType: SearchOptionTargets[v.TargetType],
			ReferType:  SearchOptionRefers[v.ReferType],
			Language:   SearchOptionLanguage[v.Language],
			Id:         v.Id,
			Content:    v.Content,
		})
	}
	return nil
}
