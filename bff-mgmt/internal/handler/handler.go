// Package handler ...
package handler

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler/ai"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler/graph"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler/iam"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler/mgmt"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler/search"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler/support"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler/tag"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/handler/team"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	bffgraphpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/graph"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/iam"
	bffmgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/mgmt"
	bffsearchpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/search"
	supportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/support"
	bfftagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/tag"
	bffteampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/team"
	"github.com/asim/go-micro/v3"
)

// RegisterBffHandler 注册BFF handler
func RegisterBffHandler(svc micro.Service) {
	server := svc.Server().(bff.Server)

	// 全局wrapper
	router := server.BffOptions().Router
	router.SortMiddleware(middlewareSort)
	router.Middleware(translateErrorResponse, startSession, maintenance)

	// hook翻译
	router.SetHooks(xhttp.Hooks{
		OnNotFound:         translateErrorResponse.WrapHandler(bff.OnNotFound(server)),
		OnMethodNotAllowed: translateErrorResponse.WrapHandler(bff.OnMethodNotAllowed(server)),
		OnPanic:            translateErrorResponse.WrapPanicHandler(OnPanic(server)),
	})

	// 认证接口
	bffmgmtpb.RegisterAuthBff(server, &mgmt.Auth{})
	router.FindRoute("Auth.Logout").Middleware(authenticate)

	// 认证分组
	authGroup := server.Group(xhttp.GroupMiddleware(authenticate, authorize))
	// 运营接口
	bffmgmtpb.RegisterMgmtBff(authGroup, &mgmt.Mgmt{})
	// iam接口
	bffiampb.RegisterIamBff(authGroup, &iam.Iam{})
	// 团队接口
	bffteampb.RegisterTeamBff(authGroup, &team.Team{})
	// 图谱接口
	bffgraphpb.RegisterGraphBff(authGroup, &graph.Graph{})
	// 标签接口
	bfftagpb.RegisterTagBff(authGroup, &tag.Tag{})
	// 搜索接口
	bffsearchpb.RegisterSearchBff(authGroup, &search.Search{})
	// AI接口
	bffaipb.RegisterAiBff(authGroup, &ai.Ai{})

	authenticateGroup := server.Group(xhttp.GroupMiddleware(authenticate))
	supportpb.RegisterSupportBff(authenticateGroup, &support.Support{})
}

// OnPanic panic handler
func OnPanic(s bff.Server) xhttp.PanicHandler {
	return func(r *xhttp.Request, v any) xhttp.Response {
		log.WithContext(r.Context()).Errorw("bff panic", "error", v, "stacktrace", xsync.TakeStackTrace(0))
		return s.BffOptions().FormatResponse(xerrors.InternalServerError(v))
	}
}
