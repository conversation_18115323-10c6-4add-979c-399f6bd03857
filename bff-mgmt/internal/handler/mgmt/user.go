package mgmt

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	bffmgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/mgmt"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
)

// SearchUsers 搜索运营端用户
func (m *Mgmt) SearchUsers(ctx context.Context, req *bffmgmtpb.ReqSearchUsers, rsp *bffmgmtpb.RspSearchUsers) error {
	users, err := client.MgmtClient.GetUsers(ctx, &mgmtpb.ReqGetUsers{
		Offset:         req.Offset,
		Limit:          req.Limit,
		WithTotalCount: true,
		Keyword:        req.Keyword,
	})
	if err != nil {
		return err
	}

	rsp.Users = users.UserSet
	rsp.TotalCount = users.TotalCount

	return nil
}
