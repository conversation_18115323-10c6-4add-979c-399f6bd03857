package mgmt

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/tmt"
	bffmgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/mgmt"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
)

func (m *Mgmt) TextTranslate(ctx context.Context, req *bffmgmtpb.ReqTextTranslate,
	rsp *bffmgmtpb.RspTextTranslate) error {
	if req.TargetLanguage != logic.EnTagLanguage && req.TargetLanguage != logic.ZhTagLanguage {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	result, err := tmt.TextTranslate(ctx, &tmt.ReqTextTranslate{
		SourceText: req.Text,
		Source:     req.SourceLanguage,
		Target:     req.TargetLanguage,
	})
	if err != nil {
		return err
	}

	rsp.Text = result.TargetText
	return nil
}
