package mgmt

import (
	"context"

	mgmtlogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/mgmt"
	bffmgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/mgmt"
)

// GetRoles 查询角色列表
func (m *Mgmt) GetRoles(ctx context.Context, req *bffmgmtpb.ReqGetRoles, rsp *bffmgmtpb.RspGetRoles) error {
	l := mgmtlogic.GetRoles{}

	if err := l.GetFromRpc(ctx, req); err != nil {
		return err
	}
	l.Sort().ToRsp(rsp)

	return nil
}
