package mgmt

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcrypto"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/captcha"
	bffmgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/mgmt"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	"google.golang.org/protobuf/types/known/emptypb"
)

// Login 登录
func (a *Auth) Login(ctx context.Context, req *bffmgmtpb.ReqLogin, rsp *bffmgmtpb.RspLogin) error {
	result, err := captcha.DescribeCaptchaResult(ctx, &captcha.ReqDescribeCaptchaResult{
		Ticket:  req.TcloudCaptcha.Ticket,
		Randstr: req.TcloudCaptcha.Randstr,
		UserIp:  bff.RequestFromContext(ctx).Header.Get("X-Real-Ip"),
	})
	if err != nil {
		return err
	}
	if result.CaptchaCode != 1 {
		return xerrors.New(errorspb.BaseError_TcloudCaptchaError, result.CaptchaMsg)
	}

	password, err := xcrypto.Use("rsa").Decrypt(
		xstrings.ToBytes(req.Password),
		xcrypto.Encoding(xcrypto.Base64StdEncoding),
	)
	if err != nil {
		return err
	}

	user, err := client.MgmtClient.CheckLoginCredentials(ctx, &mgmtpb.ReqCheckLoginCredentials{
		Username: req.Username,
		Password: xstrings.FromBytes(password),
	})
	if err != nil {
		return err
	}

	session := xsession.SessionFromContext(ctx)
	session.SetLoginUser(user.User.Id)
	if err = xsession.Migrate(ctx, session, true); err != nil {
		return err
	}

	rsp.UserInfo = &bffmgmtpb.RspLogin_UserInfo{
		UserId:     user.User.Id,
		UserName:   user.User.Username,
		RemarkName: user.User.RemarkName,
	}
	return nil
}

// Logout 登出
func (a *Auth) Logout(ctx context.Context, _ *emptypb.Empty, _ *emptypb.Empty) error {
	session := xsession.SessionFromContext(ctx)
	session.Clear()
	if err := xsession.Migrate(ctx, session, true); err != nil {
		return err
	}
	return nil
}
