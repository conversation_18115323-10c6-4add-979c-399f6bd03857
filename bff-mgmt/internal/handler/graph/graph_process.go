package graph

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	bffgraphpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/graph"
	graphpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/graph"
	"google.golang.org/protobuf/types/known/emptypb"
)

// DescribeListProcess 流程配置列表
func (g Graph) DescribeListProcess(ctx context.Context, req *bffgraphpb.ReqDescribeListProcess,
	rsp *bffgraphpb.RspDescribeListProcess) error {
	data := &graphpb.ReqDescribeListProcess{
		SearchName: req.GetSearchName(),
		Id:         req.GetId(),
	}
	rpcRsp, err := client.GraphClient.DescribeListProcess(ctx, data)
	if err != nil {
		log.WithContext(ctx).Errorw("DescribeListProcess Error", "data", data, "err", err)
		return err
	}

	if rpcRsp == nil || len(rpcRsp.Processes) == 0 {
		return nil
	}

	// 从rpcRsp.Processes中获取用户id
	var userIds []uint64
	for _, v := range rpcRsp.Processes {
		userIds = append(userIds, v.CreateBy, v.LastUpdateBy)
	}
	userNames, err := logic.FetchOpUserName(ctx, userIds)
	if err != nil {
		return err
	}

	rsp.TotalCount = rpcRsp.TotalCount
	rsp.Processes = make([]*bffgraphpb.ProcessInfo, len(rpcRsp.Processes))

	for i, v := range rpcRsp.Processes {
		rsp.Processes[i] = &bffgraphpb.ProcessInfo{
			Id:              v.Id,
			ProcessName:     v.ProcessName,
			Category:        v.Category,
			YamlConfig:      v.YamlConfig,
			Remark:          v.Remark,
			Lang:            v.Lang,
			CreateDate:      v.CreateDate,
			UpdateDate:      v.LastUpdateDate,
			IsOnlineVersion: v.IsOnlineVersion,
			Creator:         userNames[v.CreateBy],
			UpdateUser:      userNames[v.LastUpdateBy],
		}
	}

	return nil
}

// CreateProcessEngine 流程引擎添加
func (g Graph) CreateProcessEngine(ctx context.Context, req *bffgraphpb.ReqCreateProcessEngine,
	rsp *emptypb.Empty) error {

	/*
		    暂时去后端校验
			yamlBool, err := logic.CheckCreatGraphYaml(req.GetYamlConfig())
			if err != nil {
				log.WithContext(ctx).Errorw("CheckCreatGraphYaml Error", "data", req.GetYamlConfig(), "err", err)
				return err
			}
			if !yamlBool {
				return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
			}*/

	uid := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()
	data := &graphpb.ReqCreateProcessEngine{
		ProcessName:  req.GetProcessName(),
		Category:     req.GetCategory(),
		YamlConfig:   req.GetYamlConfig(),
		Remark:       req.GetRemark(),
		Lang:         req.GetLang(),
		CreateBy:     uid,
		LastUpdateBy: uid,
	}
	_, err := client.GraphClient.CreateProcessEngine(ctx, data)
	if err != nil {
		log.WithContext(ctx).Errorw("CreateProcessEngine Error", "data", data, "err", err)
		return err
	}

	return nil
}

// ModifyProcessEngine 流程引擎编辑
func (g Graph) ModifyProcessEngine(ctx context.Context, req *bffgraphpb.ReqModifyProcessEngine,
	rsp *emptypb.Empty) error {

	uid := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()
	data := &graphpb.ReqModifyProcessEngine{
		ProcessName:  req.GetProcessName(),
		Category:     req.GetCategory(),
		YamlConfig:   req.GetYamlConfig(),
		Remark:       req.GetRemark(),
		Lang:         req.GetLang(),
		LastUpdateBy: uid,
		Id:           req.GetId(),
	}
	_, err := client.GraphClient.ModifyProcessEngine(ctx, data)
	if err != nil {
		log.WithContext(ctx).Errorw("ModifyProcessEngine Error", "data", data, "err", err)
		return err
	}

	return nil
}

// ModifyEnableVersion 版本启用
func (g Graph) ModifyEnableVersion(ctx context.Context, req *bffgraphpb.ReqModifyEnableVersion,
	rsp *emptypb.Empty) error {

	uid := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()
	data := &graphpb.ReqModifyEnableProcessEngine{
		LastUpdateBy:    uid,
		Id:              req.GetId(),
		IsOnlineVersion: req.GetIsOnlineVersion(),
	}
	_, err := client.GraphClient.ModifyEnableProcessEngine(ctx, data)
	if err != nil {
		log.WithContext(ctx).Errorw("ModifyEnableVersion Error", "data", data, "err", err)
		return err
	}

	return nil
}

// DeleteProcessEngine 删除流程引擎
func (g Graph) DeleteProcessEngine(ctx context.Context, req *bffgraphpb.ReqDeleteProcessEngine,
	rsp *emptypb.Empty) error {

	_, err := client.GraphClient.DeleteProcessEngine(ctx, &graphpb.ReqDeleteProcessEngine{Id: req.GetId()})
	if err != nil {
		log.WithContext(ctx).Errorw("DeleteProcessEngine Error", "data", req.GetId(), "err", err)
		return err
	}

	return nil
}
