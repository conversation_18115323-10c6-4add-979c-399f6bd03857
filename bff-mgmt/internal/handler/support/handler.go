package support

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	bffpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/support"
)

// Support ...
type Support struct{}

// GetHashIds 获取hashids
func (h *Support) GetHashIds(ctx context.Context, req *bffpb.ReqGetHashIds, rsp *bffpb.RspGetHashIds) error {
	rsp.HashIds = make([]string, len(req.Ids))
	for i, id := range req.Ids {
		encode, err := hashids.DefaultCodec.Encode(id)
		if err != nil {
			log.WithContext(ctx).Errorw("GetHashIds Encode error", "id", id, "err", err)
			return err
		}
		rsp.HashIds[i] = encode
	}
	return nil
}
