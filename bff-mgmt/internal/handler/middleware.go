package handler

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/middleware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot/bffmiddleware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot/httpmiddleware"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
)

var (
	// 加密cookie
	encryptCookies = &httpmiddleware.EncryptCookies{}

	// 启动会话
	startSession = &httpmiddleware.StartSession{CookieName: "tanlive-session"}

	// 校验CSRF令牌
	verifyCsrfToken = &bffmiddleware.VerifyCsrfToken{
		Rule: func(req *xhttp.Request) bool {
			return false
		},
		AddToCookie: true,
	}

	// 认证
	authenticate = &middleware.Authenticate{}

	// 鉴权
	authorize = &middleware.Authorize{}

	// 错误响应本地化
	translateErrorResponse = &bffmiddleware.TranslateErrorResponse{
		MessageMap: errorspb.GetErrorNamesMap(),
	}

	// 维护模式检测
	maintenance = &httpmiddleware.Maintenance{}
)

// 定义中间件顺序
var middlewareSort = []xhttp.Middleware{
	(*httpmiddleware.TraceHttp)(nil),
	(*httpmiddleware.LogRequest)(nil),
	(*bffmiddleware.TranslateErrorResponse)(nil),
	(*httpmiddleware.Maintenance)(nil),
	(*httpmiddleware.EncryptCookies)(nil),
	(*httpmiddleware.StartSession)(nil),
	(*bffmiddleware.VerifyCsrfToken)(nil),
	(*middleware.Authenticate)(nil),
	(*middleware.Authorize)(nil),
}
