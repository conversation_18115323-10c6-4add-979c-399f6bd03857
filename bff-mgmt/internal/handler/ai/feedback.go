package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	"golang.org/x/exp/maps"
)

// UpsertMgmtFeedback 创建/更新碳LIVE反馈
func (a *Ai) UpsertMgmtFeedback(ctx context.Context, req *bffaipb.ReqUpsertMgmtFeedback, rsp *bffaipb.RspCreateFeedback) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	var cli aipb.AiService
	para := &aipb.ReqUpsertMgmtFeedback{
		AnswerRating: req.AnswerRating,
		MgmtFeedback: req.MgmtFeedback,
		MgmtComment:  req.MgmtComment,
		MgmtDocId:    req.MgmtDocId,
		Operator: &basepb.Identity{
			IdentityId:   me.Id,
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
		},
	}
	if req.AnswerId > 0 {
		para.Cond = &aipb.ReqUpsertMgmtFeedback_ByAnswerId{
			ByAnswerId: req.AnswerId,
		}
		cli = client.AiClientByID(req.AnswerId)
	} else {
		para.Cond = &aipb.ReqUpsertMgmtFeedback_ByFeedbackId{
			ByFeedbackId: req.FeedbackId,
		}
		cli = client.AiClientByID(req.FeedbackId)
	}

	result, err := cli.UpsertMgmtFeedback(ctx, para)
	if err != nil {
		return err
	}

	rsp.FeedbackId = result.FeedbackId

	return nil
}

// GetFeedbacks 查询用户反馈列表
func (a *Ai) GetFeedbacks(ctx context.Context, req *bffaipb.ReqGetFeedbacks, rsp *bffaipb.RspGetFeedbacks) error {
	l := ailogic.GetFeedbacks{}

	feedbacks, totalCount, err := l.Get(ctx, req)
	if err != nil {
		return err
	}

	if err = ailogic.LoadFeedbackExpectedDocs(ctx, feedbacks); err != nil {
		return err
	}

	userCards, teamCards, mgmtUserCards, err := ailogic.LoadFeedbackIdentityCards(ctx, feedbacks)

	assistantMap := ailogic.GetAssistantInfo(ctx, maps.Keys(l.AssistantIds)...)

	rsp.Items = l.ToResponse(feedbacks, assistantMap)
	rsp.TotalCount = totalCount
	rsp.UserCards = userCards
	rsp.TeamCards = teamCards
	rsp.MgmtUserCards = mgmtUserCards

	return nil
}

// FindFeedback 查询用户反馈详情
func (a *Ai) FindFeedback(ctx context.Context, req *bffaipb.ReqFindFeedback, rsp *bffaipb.RspFindFeedback) error {
	l := ailogic.FindFeedback{}

	feedback, err := l.Find(ctx, req.FeedbackId)
	if err != nil {
		return err
	}

	if err = ailogic.LoadFeedbackExpectedDocs(ctx, []*aipb.FullFeedback{feedback}); err != nil {
		return err
	}

	userCards, teamCards, mgmtUserCards, err := ailogic.LoadFeedbackIdentityCards(ctx, []*aipb.FullFeedback{feedback})

	rsp.Feedback = feedback.Feedback
	rsp.References = feedback.References
	rsp.OriginalQuestion = feedback.OriginalQuestion
	rsp.OriginalAnswer = feedback.OriginalAnswer
	rsp.ExpectedDocs = feedback.ExpectedDocs
	rsp.UserCards = userCards
	rsp.TeamCards = teamCards
	rsp.MgmtUserCards = mgmtUserCards
	rsp.ExpectedMgmtDocs = feedback.ExpectedMgmtDocs

	return nil
}

// AcceptFeedback 采用反馈
func (a *Ai) AcceptFeedback(ctx context.Context,
	req *bffaipb.ReqAcceptFeedback, rsp *bffaipb.RspAcceptFeedback) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)
	operator := &basepb.Identity{
		IdentityId:   me.Id,
		IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
	}

	l := ailogic.AcceptFeedback{}
	results := l.BatchAccept(ctx, req.FeedbackIds, operator)

	rsp.Results = results
	return nil
}

// GetFeedbackLogs 查询用户反馈日志列表
func (a *Ai) GetFeedbackLogs(ctx context.Context,
	req *bffaipb.ReqGetFeedbackLogs, rsp *bffaipb.RspGetFeedbackLogs) error {
	l := ailogic.GetFeedbackLogs{}

	logs, totalCount, err := l.Get(ctx, req)
	if err != nil {
		return err
	}

	usersCards, teamCards, mgmtUserCards, err := l.LoadIdentities(ctx, logs)
	if err != nil {
		return err
	}

	rsp.Items = logs
	rsp.TotalCount = totalCount
	rsp.UserCards = usersCards
	rsp.TeamCards = teamCards
	rsp.MgmtUserCards = mgmtUserCards

	return nil
}

// DescribeFeedbackRegionCode ...
func (a *Ai) DescribeFeedbackRegionCode(ctx context.Context, req *bffaipb.ReqDescribeFeedbackRegionCode,
	rsp *bffaipb.RspDescribeFeedbackRegionCode) error {
	regionClient := client.AiClient
	if config.GetBoolOr("bff.abroad", true) {
		regionClient = client.AiClientByRegion(req.Region)
	}

	rpcRsp, err := regionClient.DescribeFeedbackRegionCode(ctx, &aipb.ReqDescribeFeedbackRegionCode{})
	if err != nil {
		return err
	}

	if rpcRsp == nil || len(rpcRsp.RegionCodes) == 0 {
		return nil
	}

	rsp.RegionCodes = rpcRsp.RegionCodes
	return nil
}
