package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	"google.golang.org/protobuf/types/known/emptypb"
)

// ListCustomLabel 获取对话标签列表
func (a *Ai) ListCustomLabel(ctx context.Context, req *bffaipb.ReqListCustomLabel, rsp *bffaipb.RspListCustomLabel) error {
	tenant, err := ailogic.GetTanliveOpCustomLabelTenant()
	if err != nil {
		return err
	}
	pbRsp, err := client.AiClient.ListCustomLabel(ctx, &ai.ReqListCustomLabel{
		TenantId: tenant,
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		ObjectType: req.ObjectType,
		Ids:        req.Id,
	})
	if err != nil {
		return err
	}
	rsp.Labels = pbRsp.Labels
	rsp.TotalCount = pbRsp.Total
	return nil
}

// ModifyCustomLabels 插入或更新对话标签
func (a *Ai) ModifyCustomLabels(ctx context.Context, req *bffaipb.ReqModifyCustomLabels, rsp *bffaipb.RspModifyCustomLabels) error {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	tenant, err := ailogic.GetTanliveOpCustomLabelTenant()
	if err != nil {
		return err
	}
	for _, v := range req.Labels {
		v.CreateBy = user.Id
		v.UpdateBy = user.Id
	}
	_, err = client.AiClient.UpsertCustomLabels(ctx, &ai.ReqUpsertCustomLabels{
		Labels:     req.Labels,
		TenantId:   tenant,
		ObjectType: req.ObjectType,
	})
	if err != nil {
		return err
	}
	return nil
}

// DeleteCustomLabels 删除对话标签
func (a *Ai) DeleteCustomLabels(ctx context.Context, req *bffaipb.ReqDeleteCustomLabels, rsp *emptypb.Empty) error {
	if len(req.Ids) == 0 {
		return nil
	}
	_, err := client.AiClient.DeleteCustomLabels(ctx, &ai.ReqDeleteCustomLabels{
		Ids: req.Ids,
	})
	if err != nil {
		return err
	}
	return nil
}

// UpdateObjectCustomLabels 更新对话标签
func (a *Ai) UpdateObjectCustomLabels(ctx context.Context, req *bffaipb.ReqUpdateObjectCustomLabels, rsp *emptypb.Empty) error {
	if len(req.Id) == 0 {
		return nil
	}
	rpcReq := &ai.ReqUpdateCustomChatLabels{
		ObjectId:   req.Id,
		Labels:     req.Labels,
		ObjectType: req.ObjectType,
	}
	switch req.ObjectType {
	case ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_CHAT:
		_, err := client.AiClientByID(req.Id[0]).UpdateUserChatLabels(ctx, rpcReq)
		if err != nil {
			return err
		}
	case ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA,
		ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_FILE,
		ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE,
		ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_SQL_FILE:
		// 知识库只在国内服务
		_, err := client.AiClient.UpdateDocLabels(ctx, rpcReq)
		if err != nil {
			return err
		}
	}

	return nil
}
