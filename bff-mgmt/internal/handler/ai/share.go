package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	"google.golang.org/protobuf/types/known/emptypb"
)

// ListAssistantCanShareDoc 查询可分享的助手列表
func (a *Ai) ListAssistantCanShareDoc(ctx context.Context, req *bffaipb.ReqListAssistantCanShareDoc,
	rsp *bffaipb.RspListAssistantCanShareDoc,
) error {
	reqSender := &aipb.ReqListAssistantCanShareDoc{
		CreateBy:  config.GetUint64("ai.share.team_id"),
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		DocId:     req.DocId,
		Name:      req.Name,
		Language:  req.Language,
	}

	sharedAssistant, err := client.AiClient.ListAssistantCanShareDoc(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	for _, assistant := range sharedAssistant.Assistants {
		rsp.Assistants = append(rsp.Assistants, &bffaipb.RspListAssistantCanShareDoc_SharedAssistant{
			Id: assistant.Id, Name: assistant.Name,
			IsSelected: assistant.IsSelected, NameEn: assistant.NameEn,
		})
	}
	return nil
}

// CreateAssistantShare 创建文档分享（支持助手、个人、团队）
func (a *Ai) CreateAssistantShare(ctx context.Context, req *bffaipb.ReqCreateAssistantShare,
	rsp *bffaipb.RspCreateDocShare,
) error {
	var adminType basepb.IdentityType
	var createBy uint64

	createBy = config.GetUint64("ai.share.team_id")
	adminType = basepb.IdentityType_IDENTITY_TYPE_TEAM

	rpcRsp, err := client.AiClient.CreateDocShare(ctx, &aipb.ReqCreateDocShare{
		DocId:         req.DocId,
		AssistantId:   req.AssistantId,
		UserId:        req.UserId,
		TeamId:        req.TeamId,
		CreateBy:      createBy,
		Operator:      &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: createBy},
		AdminType:     adminType,
		IsContributor: true,
		QueryId:       req.QueryId,
	})
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if rpcRsp != nil {
		rsp.Async = rpcRsp.Async
	}
	return nil
}

// CreateDocShareConfigSender 创建助手发送方设置
func (a *Ai) CreateDocShareConfigSender(ctx context.Context, req *bffaipb.ReqCreateDocShareConfigSender,
	empty *emptypb.Empty,
) error {
	reqSender := &aipb.ReqCreateDocShareConfigSender{
		ShareAssistantId: req.ShareAssistantId,
		ShareUserId:      req.ShareUserId,
		ShareTeamId:      req.ShareTeamId,
		UserId:           config.GetUint64("ai.share.team_id"),
		AdminType:        basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}

	_, err := client.AiClient.CreateDocShareConfigSender(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// ListeDocShareConfigSender 查询助手发送方设置
func (a *Ai) ListeDocShareConfigSender(ctx context.Context, req *bffaipb.ReqListeDocShareConfigSender,
	rsp *bffaipb.RspListeDocShareConfigSender,
) error {
	createBy := config.GetUint64("ai.share.team_id")
	reqSender := &aipb.ReqListeDocShareConfigSender{
		CreateBy:  createBy,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}

	assistants, err := ailogic.ListAssistantCanShareDoc(ctx, createBy, req.Name, req.Language)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	assistantSender, err := client.AiClient.ListeDocShareConfigSender(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	assistantSelectedMaps := make(map[uint64]bool)
	assistantRemoveMaps := make(map[uint64]bool)

	for _, assistant := range assistantSender.ShareAssistantId {
		assistantSelectedMaps[assistant] = true
	}
	for _, assistant := range assistantSender.MyAdminAssistantId {
		assistantRemoveMaps[assistant] = true
	}

	for _, assistant := range assistants.Assistants {
		if assistantRemoveMaps[assistant.Id] {
			continue
		}
		rsp.Assistants = append(rsp.Assistants, &bffaipb.RspListeDocShareConfigSender_SharedAssistant{
			Id: assistant.Id, Name: assistant.Name,
			IsSelected: assistantSelectedMaps[assistant.Id], NameEn: assistant.NameEn,
		})
	}
	return nil
}
