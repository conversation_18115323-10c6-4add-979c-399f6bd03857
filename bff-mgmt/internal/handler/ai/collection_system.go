package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
)

// CreateSystemDocCopy 创建系统文档副本（人工修改）
func (a *Ai) CreateSystemDocCopy(ctx context.Context,
	req *bffaipb.ReqCreateSystemDocCopy, rsp *bffaipb.RspCreateSystemDocCopy) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	l := ailogic.CreateSystemDocCopyLogic{}

	results := l.BatchCreate(ctx, req.Items, me)

	rsp.Results = results
	return nil
}

// EnableSystemDoc 启用系统文档
func (a *Ai) EnableSystemDoc(ctx context.Context,
	req *bffaipb.ReqEnableSystemDoc, rsp *bffaipb.RspEnableSystemDoc) error {
	user := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	l := ailogic.EnableSystemDocLogic{}

	results := l.BatchEnable(ctx, req.DocId, user)

	rsp.Results = results
	return nil
}

// DisableSystemDoc 停用系统文档
func (a *Ai) DisableSystemDoc(ctx context.Context,
	req *bffaipb.ReqDisableSystemDoc, rsp *bffaipb.RspDisableSystemDoc) error {
	user := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	l := ailogic.DisableSystemDocLogic{}

	results := l.BatchDisable(ctx, req.DocId, user)

	rsp.Results = results
	return nil
}

// DeleteSystemDoc 删除系统文档
func (a *Ai) DeleteSystemDoc(ctx context.Context,
	req *bffaipb.ReqDeleteSystemDoc, rsp *bffaipb.RspDeleteSystemDoc) error {
	user := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	l := ailogic.DeleteSystemDocLogic{}

	results := l.BatchDelete(ctx, req.DocId, user)

	rsp.Results = results
	return nil
}
