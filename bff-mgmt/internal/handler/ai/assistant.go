package ai

import (
	"context"
	"encoding/json"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/types/known/emptypb"
)

// ListAssistant 获取ai助手列表
func (a *Ai) ListAssistant(ctx context.Context, req *bffaipb.ReqListAssistant, rsp *bffaipb.RspListAssistant) error {
	pbRsp, err := client.AiClient.ListAssistant(ctx, &aipb.ReqListAssistant{
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Type:           req.Type,
		WithCollection: true,
		Name:           req.Name,
		Language:       req.Language,
	})
	if err != nil {
		return err
	}
	rsp.TotalCount = pbRsp.Total
	rsp.Assistants = pbRsp.Assistants
	return nil
}

// GetAssistantsPage 查询助手分页列表
func (a *Ai) GetAssistantsPage(ctx context.Context,
	req *bffaipb.ReqGetAssistantsPage, rsp *bffaipb.RspGetAssistantsPage) error {
	l := ailogic.GetAssistantsPageLogic{}

	assistants, totalCount, err := l.Get(ctx, req)
	if err != nil {
		return err
	}

	users, teams, mgmtUsers, err := l.LoadIdentityCards(ctx, assistants)
	if err != nil {
		return err
	}

	if err = logic.FillAssistantTag(ctx, assistants); err != nil {
		return err
	}

	rsp.Assistants = assistants
	rsp.TotalAccount = totalCount
	rsp.UserCards = users
	rsp.TeamCards = teams
	rsp.MgmtUserCards = mgmtUsers

	return nil
}

// CreateAssistant 创建助手
func (a *Ai) CreateAssistant(ctx context.Context,
	req *bffaipb.ReqCreateAssistant, rsp *bffaipb.RspCreateAssistant) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	result, err := client.AiClient.BatchCreateAssistant(ctx, &aipb.ReqBatchCreateAssistant{
		Configs: []*aipb.AssistantConfig{req.Config},
		CreateBy: &basepb.Identity{
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
			IdentityId:   me.Id,
		},
		IsDraft: req.IsDraft,
	})
	if err != nil {
		return err
	}

	if len(result.AssistantId) > 0 {
		rsp.AssistantId = result.AssistantId[0]
	}

	_ = logic.UpdateAssistantTag(ctx, req.Config.UserLabelConfig, rsp.AssistantId, me.Id)
	logic.AsyncUpdateAssistantAutoLabel(ctx, req.Config.Admins, rsp.AssistantId, me.Id)

	return nil
}

// BatchCreateAssistant 批量创建助手
func (a *Ai) BatchCreateAssistant(ctx context.Context,
	req *bffaipb.ReqBatchCreateAssistant, rsp *bffaipb.RspBatchCreateAssistant) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	result, err := client.AiClient.BatchCreateAssistant(ctx, &aipb.ReqBatchCreateAssistant{
		Configs: req.Configs,
		CreateBy: &basepb.Identity{
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
			IdentityId:   me.Id,
		},
		IsDraft: req.IsDraft,
	})
	if err != nil {
		return err
	}

	rsp.BatchNo = result.BatchNo
	rsp.AssistantId = result.AssistantId
	if req.IsDraft {
		return nil
	}
	for i, v := range req.Configs {
		assistantId := result.AssistantId[i]
		_ = logic.UpdateAssistantTag(ctx, v.UserLabelConfig, assistantId, me.Id)
		logic.AsyncUpdateAssistantAutoLabel(ctx, v.Admins, assistantId, me.Id)
	}

	return nil
}

// UpdateAssistant 更新助手
func (a *Ai) UpdateAssistant(ctx context.Context,
	req *bffaipb.ReqUpdateAssistant, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	// 标签变动
	var oldUserLabelConfig *aipb.AssistantUserLabelConfig
	if xslice.IsContainsString(req.Mask.Paths, "user_label_config") {
		oldUserLabelConfigs, err := ailogic.GetAssistantUserLabelConfigs(ctx, []uint64{req.AssistantId}, false)
		if err != nil {
			return err
		}
		oldUserLabelConfig = oldUserLabelConfigs[req.AssistantId]
	}

	_, err := client.AiClient.BatchUpdateAssistant(ctx, &aipb.ReqBatchUpdateAssistant{
		Items: []*aipb.ReqBatchUpdateAssistant_Item{
			{
				AssistantId:        req.AssistantId,
				Config:             req.Config,
				Mask:               req.Mask,
				OldUserLabelConfig: oldUserLabelConfig,
			},
		},
		UpdateBy: &basepb.Identity{
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
			IdentityId:   me.Id,
		},
		IsDraft: req.IsDraft,
	})
	if err != nil {
		return err
	}

	if xslice.IsContainsString(req.Mask.Paths, "user_label_config") { // 如果更新了用户打标信息则要异步同步到tag
		err = logic.UpdateAssistantTag(ctx, req.Config.UserLabelConfig, req.AssistantId, me.Id)
		if len(req.Mask.Paths) == 1 { // 如果只更新了团队标签,则把错误信息返回
			return err
		}
	}
	if xslice.IsContainsString(req.Mask.Paths, "admins") {
		logic.AsyncUpdateAssistantAutoLabel(ctx, req.Config.Admins, req.AssistantId, me.Id)
	}

	return nil
}

// BatchUpdateAssistant 批量更新助手
func (a *Ai) BatchUpdateAssistant(ctx context.Context,
	req *bffaipb.ReqBatchUpdateAssistant, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	hasUserLabelChangedAssistantIDs := make([]uint64, 0, len(req.Items))
	items := make([]*aipb.ReqBatchUpdateAssistant_Item, 0, len(req.Items))
	for _, item := range req.Items {
		if item.AssistantId > 0 && slices.Contains(item.Mask.Paths, "user_label_config") {
			hasUserLabelChangedAssistantIDs = append(hasUserLabelChangedAssistantIDs, item.AssistantId)
		}
		items = append(items, &aipb.ReqBatchUpdateAssistant_Item{
			AssistantId: item.AssistantId,
			Config:      item.Config,
			Mask:        item.Mask,
		})
	}

	if len(hasUserLabelChangedAssistantIDs) > 0 {
		oldUserLabelConfigs, err := ailogic.GetAssistantUserLabelConfigs(ctx, hasUserLabelChangedAssistantIDs, false)
		if err != nil {
			return err
		}
		for _, item := range items {
			item.OldUserLabelConfig = oldUserLabelConfigs[item.AssistantId]
		}
	}

	_, err := client.AiClient.BatchUpdateAssistant(ctx, &aipb.ReqBatchUpdateAssistant{
		Items: items,
		UpdateBy: &basepb.Identity{
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
			IdentityId:   me.Id,
		},
		IsDraft: req.IsDraft,
		BatchNo: req.BatchNo,
	})
	if err != nil {
		return err
	}

	for _, item := range req.Items {
		if xslice.IsContainsString(item.Mask.Paths, "user_label_config") {
			err = logic.UpdateAssistantTag(ctx, item.Config.UserLabelConfig, item.AssistantId, me.Id)
			if len(item.Mask.Paths) == 1 { // 如果只更新了团队标签,则把错误信息返回
				return err
			}
		}
		if xslice.IsContainsString(item.Mask.Paths, "admins") {
			logic.AsyncUpdateAssistantAutoLabel(ctx, item.Config.Admins, item.AssistantId, me.Id)
		}
	}

	return nil
}

// DeleteAssistant 删除助手
func (a *Ai) DeleteAssistant(ctx context.Context,
	req *bffaipb.ReqDeleteAssistant, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	deleteAssistantReq := &aipb.ReqDeleteAssistant{
		UpdateBy: &basepb.Identity{
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_MGMT,
			IdentityId:   me.Id,
		},
	}
	if len(req.AssistantId) > 0 {
		deleteAssistantReq.Condition = &aipb.ReqDeleteAssistant_ByAssistantId{
			ByAssistantId: &aipb.ReqDeleteAssistant_AssistantId{
				AssistantId: req.AssistantId,
			},
		}
	} else if req.BatchNo != "" {
		deleteAssistantReq.Condition = &aipb.ReqDeleteAssistant_ByBatchNo{
			ByBatchNo: req.BatchNo,
		}
	} else {
		return xerrors.ValidationError("condition is required")
	}

	_, err := client.AiClient.DeleteAssistant(ctx, deleteAssistantReq)
	if err != nil {
		return err
	}
	logic.AsyncDeleteAssistantTag(ctx, req.AssistantId)
	return nil
}

// GetAssistantLogsPage 查询助手日志分页列表
func (a *Ai) GetAssistantLogsPage(ctx context.Context,
	req *bffaipb.ReqGetAssistantLogsPage, rsp *bffaipb.RspGetAssistantLogsPage) error {
	l := ailogic.GetAssistantLogsPageLogic{}

	logs, totalCount, err := l.Get(ctx, req)
	if err != nil {
		return err
	}

	users, teams, mgmtUsers, err := l.LoadIdentityCards(ctx, logs)
	if err != nil {
		return err
	}

	if err = l.LoadUserLabelConfig(ctx, logs); err != nil {
		return err
	}

	rsp.Logs = logs
	rsp.TotalCount = totalCount
	rsp.UserCards = users
	rsp.TeamCards = teams
	rsp.MgmtUserCards = mgmtUsers

	return nil
}

// GetAssistantOptions 获取助手下拉选项
func (a *Ai) GetAssistantOptions(ctx context.Context, _ *emptypb.Empty, rsp *bffaipb.RspGetAssistantOptions) error {
	result, err := client.AiClient.GetAssistantOptions(ctx, &emptypb.Empty{})
	if err != nil {
		return err
	}

	rsp.ChatModel = result.ChatModel
	rsp.ChatModelV2 = result.ChatModelV2
	rsp.ChatOrSqlModel = result.ChatOrSqlModel
	rsp.GraphParseMode = result.GraphParseMode
	rsp.SearchEngine = result.SearchEngine
	rsp.SearchEngineV2 = result.SearchEngineV2
	rsp.InteractiveCode = result.InteractiveCode
	rsp.VisibleChain = result.VisibleChain
	rsp.AskSuggestionModel = result.AskSuggestionModel
	rsp.EmbeddingModel = result.EmbeddingModel
	rsp.MiniWhiteUrl = result.MiniWhiteUrl

	return nil
}

// UpdateAssistantNoticeConf 更新助手横幅提示
func (a *Ai) UpdateAssistantNoticeConf(ctx context.Context,
	req *bffaipb.ReqUpdateChatNoticeConf, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[mgmtpb.OpUser](ctx)
	if xslice.ContainsInt(config.GetIntSlice("ai.notice.allowUser"), int(me.Id)) < 0 {
		return xerrors.ForbiddenError("invalid user")
	}

	key := config.GetStringOr("ai.assistant.noticeConfKey", "ai:assistant:noticeConfig")

	if !req.Conf.Enable {
		return xredis.Default.Del(ctx, key).Err()
	}

	if req.Conf.Notice == nil || req.Conf.RangeTime == nil {
		return xerrors.ValidationError("notice is required")
	}

	if req.Conf.RangeTime == nil || req.Conf.RangeTime.Start == nil || req.Conf.RangeTime.End == nil {
		return xerrors.ValidationError("rangeTime is required")
	}

	value, err := json.Marshal(req.Conf)
	if err != nil {
		return err
	}

	end := req.Conf.RangeTime.End.AsTime()
	now := time.Now()
	if end.Before(now) {
		return xredis.Default.Del(ctx, key).Err()
	}

	return xredis.Default.Set(ctx, key, string(value), end.Sub(now)).Err()
}
