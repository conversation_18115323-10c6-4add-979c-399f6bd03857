package tag

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"google.golang.org/protobuf/types/known/emptypb"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bfftagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/tag"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
)

// UpdateSystemTag 更新标签内容
func (t Tag) UpdateSystemTag(ctx context.Context, req *bfftagpb.ReqUpdateSystemTag, _ *emptypb.Empty) error {
	userID := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()
	_, err := client.TagClient.UpdateSystemTag(ctx, &tagpb.ReqUpdateSystemTag{
		TagId:        req.TagId,
		Weight:       req.Weight,
		Type:         req.Type,
		TaggableType: req.TaggableType,
		NotifyType:   req.NotifyType,
		UserId:       userID,
	})
	return err
}

// EditSystemTag 编辑标签
func (t Tag) EditSystemTag(ctx context.Context, req *bfftagpb.ReqEditSystemTag, _ *emptypb.Empty) error {
	if req.ZhTag == nil && req.EnTag == nil {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	userID := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()
	_, err := client.TagClient.EditSystemTag(ctx, &tagpb.ReqEditSystemTag{
		IndexId:      req.IndexId,
		IndexName:    req.IndexName,
		TaggableType: req.TaggableType,
		UserId:       userID,
		ZhTag:        logic.TransformTagBffToPb(req.ZhTag),
		EnTag:        logic.TransformTagBffToPb(req.EnTag),
	})
	return err
}

// DeleteSystemTag 删除标签
func (t Tag) DeleteSystemTag(ctx context.Context, req *bfftagpb.ReqDeleteSystemTag, _ *emptypb.Empty) error {
	if len(req.TagIndexIds) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	_, err := client.TagClient.DeleteSystemTag(ctx, &tagpb.ReqDeleteSystemTag{
		TagIndexIds:  req.TagIndexIds,
		TaggableType: req.TaggableType,
		ActionType:   tagpb.TagActionType_TAG_ACTION_TYPE_NORMAL,
	})
	return err
}

// MergeSystemTags 合并标签
func (t Tag) MergeSystemTags(ctx context.Context, req *bfftagpb.ReqMergeSystemTags, _ *emptypb.Empty) error {
	if req.ZhTag == nil && req.EnTag == nil {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	userID := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()
	_, err := client.TagClient.MergeSystemTags(ctx, &tagpb.ReqMergeSystemTags{
		MergeIndexIds: req.MergeIndexIds,
		IndexId:       req.IndexId,
		TaggableType:  req.TaggableType,
		UserId:        userID,
		ZhTag:         logic.TransformTagBffToPb(req.ZhTag),
		EnTag:         logic.TransformTagBffToPb(req.EnTag),
	})
	return err
}

// DescribeSystemTagsByIndex 获取标签索引列表
func (t Tag) DescribeSystemTagsByIndex(ctx context.Context, req *bfftagpb.ReqDescribeSystemTagsByIndex,
	rsp *bfftagpb.RspDescribeSystemTagsByIndex) error {
	if req.Filter == nil {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	var order *base.OrderBy
	if len(req.OrderBy) > 0 {
		orderBy, err := logic.TransformOrderBy([]string{req.OrderBy})
		if err != nil {
			return err
		}
		if len(orderBy) > 0 {
			order = orderBy[0]
		}
	}

	rpcRsp, err := client.TagClient.DescribeSystemTagsByIndex(ctx, &tagpb.ReqDescribeSystemTagsByIndex{
		Offset: req.Offset,
		Limit:  req.Limit,
		Filter: &tagpb.ReqDescribeSystemTagsByIndex_Filter{
			TagName:      req.Filter.TagName,
			Language:     req.Filter.Language,
			Type:         req.Filter.Type,
			NotifyType:   req.Filter.NotifyType,
			TaggableType: req.Filter.TaggableType,
			IndexIds:     req.Filter.IndexIds,
		},
		OrderBy: order,
	})
	if err != nil {
		return err
	}

	if rpcRsp == nil || rpcRsp.TotalCount == 0 {
		return nil
	}

	rsp.TotalCount = rpcRsp.TotalCount
	rsp.TagSet = rpcRsp.TagSet
	return nil
}

// DescribeTeamTagBindingInfos 查询标签绑定团队信息
func (t Tag) DescribeTeamTagBindingInfos(ctx context.Context, req *bfftagpb.ReqDescribeTeamTagBindingInfos,
	rsp *bfftagpb.RspDescribeTeamTagBindingInfos) error {
	//TODO implement me
	panic("implement me")
}

// DescribeResourceTagBindingInfos 获取标签绑定资源信息
func (t Tag) DescribeResourceTagBindingInfos(ctx context.Context, req *bfftagpb.ReqDescribeResourceTagBindingInfos,
	rsp *bfftagpb.RspDescribeResourceTagBindingInfos) error {
	//TODO implement me
	panic("implement me")
}

// DescribeAtlasTagBindingInfos 获取标签绑定图谱信息
func (t Tag) DescribeAtlasTagBindingInfos(ctx context.Context, req *bfftagpb.ReqDescribeAtlasTagBindingInfos,
	rsp *bfftagpb.RspDescribeAtlasTagBindingInfos) error {
	//TODO implement me
	panic("implement me")
}

// DescribeProductTagBindingInfos 获取标签绑定产品信息
func (t Tag) DescribeProductTagBindingInfos(ctx context.Context, req *bfftagpb.ReqDescribeProductTagBindingInfos,
	rsp *bfftagpb.RspDescribeProductTagBindingInfos) error {
	//TODO implement me
	panic("implement me")
}

// BatchImportSystemTag 批量导入标签信息
func (t Tag) BatchImportSystemTag(ctx context.Context, req *bfftagpb.ReqBatchImportSystemTag, _ *emptypb.Empty) error {
	if len(req.TagIndex) == 0 || len(req.TagIndex) > 100 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	var tagIndex []*tagpb.ReqBatchImportSystemTag_TagIndex
	for _, v := range req.TagIndex {
		tagIndex = append(tagIndex, &tagpb.ReqBatchImportSystemTag_TagIndex{
			TagName:   v.TagName,
			IndexName: v.IndexName,
		})
	}

	userID := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()
	_, err := client.TagClient.BatchImportSystemTag(ctx, &tagpb.ReqBatchImportSystemTag{
		Type:         req.Type,
		NotifyType:   req.NotifyType,
		Remarks:      req.Remarks,
		TaggableType: req.TaggableType,
		UserId:       userID,
		TagIndex:     tagIndex,
	})

	return err
}

// GetSystemTags 获取系统标签列表
func (t Tag) GetSystemTags(ctx context.Context, req *bfftagpb.ReqGetSystemTags,
	rsp *bfftagpb.RspGetSystemTags) error {

	orderBy, err := logic.TransformOrderBy(req.OrderBy)
	if err != nil {
		return err
	}

	rpcRsp, err := client.TagClient.GetSystemTags(ctx, &tagpb.ReqGetSystemTags{
		TaggableType:         req.TaggableType,
		Language:             req.Language,
		OrderBy:              orderBy,
		NotifyType:           req.NotifyType,
		TagName:              req.TagName,
		WithAssistantUserTag: req.WithAssistantUserTag,
	})
	if err != nil {
		return err
	}
	if rpcRsp == nil || len(rpcRsp.TagSet) == 0 {
		log.WithContext(ctx).Debugw("Mgmt GetSystemTags result is nil", "req", req)
		return nil
	}

	rsp.TagSet = make([]*bfftagpb.RspGetSystemTags_Tag, len(rpcRsp.TagSet))
	for i, tag := range rpcRsp.TagSet {
		rsp.TagSet[i] = &bfftagpb.RspGetSystemTags_Tag{
			Id:   tag.Id,
			Name: tag.Name,
		}
	}
	return nil
}

// CreatePersonalTagBinding 创建个人用户绑定
func (t Tag) CreatePersonalTagBinding(ctx context.Context, req *bfftagpb.ReqCreatePersonalTagBinding, _ *emptypb.Empty) error {
	me := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()

	_, err := client.TagClient.CreateTagBinding(ctx, &tagpb.ReqCreateTagBinding{
		UserId: me,
		DataInfo: &tagpb.DataInfo{
			DataId:   req.UserId,
			DataType: base.DataType_DATA_TYPE_USER,
		},
		TagIds:       req.TagIds,
		TagNames:     req.TagNames,
		TaggableType: tagpb.TaggableType_TAGGABLE_TYPE_PERSONAL_USER_LABEL,
		Action:       tagpb.TaggableActionType_TAGGABLE_ACTION_TYPE_MGMT,
	})
	return err
}

// CreateTeamUserTagBinding 创建团队个人用户绑定
func (t Tag) CreateTeamUserTagBinding(ctx context.Context, req *bfftagpb.ReqCreateTeamUserTagBinding, _ *emptypb.Empty) error {
	me := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()

	_, err := client.TagClient.CreateTagBinding(ctx, &tagpb.ReqCreateTagBinding{
		UserId: me,
		DataInfo: &tagpb.DataInfo{
			DataId:   req.TeamId,
			DataType: base.DataType_DATA_TYPE_TEAM,
		},
		TagIds:       req.TagIds,
		TagNames:     req.TagNames,
		TaggableType: tagpb.TaggableType_TAGGABLE_TYPE_TEAM_USER_LABEL,
		Action:       tagpb.TaggableActionType_TAGGABLE_ACTION_TYPE_MGMT,
	})
	return err
}

// GetNotifyDataByUserLabel 获取被用户标签绑定的数据信息
func (t Tag) GetNotifyDataByUserLabel(ctx context.Context, req *bfftagpb.ReqGetNotifyDataByUserLabel,
	rsp *bfftagpb.RspGetNotifyDataByUserLabel) error {
	if req.TaggableType != tagpb.TaggableType_TAGGABLE_TYPE_TEAM_USER_LABEL &&
		req.TaggableType != tagpb.TaggableType_TAGGABLE_TYPE_PERSONAL_USER_LABEL {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	dataType := base.DataType_DATA_TYPE_USER
	if req.TaggableType == tagpb.TaggableType_TAGGABLE_TYPE_TEAM_USER_LABEL {
		dataType = base.DataType_DATA_TYPE_TEAM
	}
	rpcRsp, err := client.TagClient.GetDataByTag(ctx, &tagpb.ReqGetDataByTag{
		TagIds:       req.TagIds,
		TaggableType: req.TaggableType,
		DataType:     dataType,
	})
	if err != nil || rpcRsp == nil || len(rpcRsp.Ids) == 0 {
		return err
	}

	if req.TaggableType == tagpb.TaggableType_TAGGABLE_TYPE_PERSONAL_USER_LABEL {
		userMap, err := logic.GetUserByKeys(ctx, rpcRsp.Ids)
		if err != nil {
			return err
		}
		rsp.DataInfos = make([]*bfftagpb.RspGetNotifyDataByUserLabel_DataInfo, 0, len(userMap))
		for _, v := range userMap {
			rsp.DataInfos = append(rsp.DataInfos, &bfftagpb.RspGetNotifyDataByUserLabel_DataInfo{
				Id:              v.Id,
				Name:            v.Username,
				IsInternational: logic.IsInternationalUser(v.Id),
			})
		}
	} else {
		teams, err := logic.LoadTeamCards(ctx, rpcRsp.Ids)
		if err != nil {
			return err
		}
		rsp.DataInfos = make([]*bfftagpb.RspGetNotifyDataByUserLabel_DataInfo, len(teams))
		for i, v := range teams {
			rsp.DataInfos[i] = &bfftagpb.RspGetNotifyDataByUserLabel_DataInfo{
				Id:   v.Id,
				Name: v.ShortName,
			}
		}
	}

	return nil
}

// DescribeAssistantTagBindingInfos 查询助手-用户自动打标标签关联详情
func (t Tag) DescribeAssistantTagBindingInfos(ctx context.Context, req *bfftagpb.ReqDescribeAssistantTagBindingInfos,
	rsp *bfftagpb.RspDescribeAssistantTagBindingInfos) error {
	rpcRsp, err := client.TagClient.GetDataBoundByTag(ctx, &tagpb.ReqGetDataBoundByTag{
		TagIds:        []uint64{req.TagId},
		DataType:      base.DataType_DATA_TYPE_AI_ASSISTANT,
		TaggableTypes: []tagpb.TaggableType{tagpb.TaggableType_TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL},
		Offset:        req.Offset,
		Limit:         req.Limit,
		ActionType:    tagpb.TaggableActionType_TAGGABLE_ACTION_TYPE_MGMT,
	})
	if err != nil {
		return err
	}
	if len(rpcRsp.DataIds) == 0 {
		return nil
	}

	aRsp, err := client.AiClient.GetAssistantInfoMap(ctx, &aipb.ReqGetAssistantInfoMap{
		Ids: rpcRsp.DataIds,
	})
	if err != nil {
		return err
	}
	if aRsp == nil || len(aRsp.InfoMap) == 0 {
		return nil
	}

	rsp.BindingObjects = make([]*bfftagpb.RspDescribeAssistantTagBindingInfos_Info, 0, len(aRsp.InfoMap))
	rsp.TotalCount = rpcRsp.TotalCount
	for _, id := range rpcRsp.DataIds {
		if v, ok := aRsp.InfoMap[id]; ok {
			rsp.BindingObjects = append(rsp.BindingObjects, &bfftagpb.RspDescribeAssistantTagBindingInfos_Info{
				Id:         v.Id,
				Name:       v.Name,
				NameEn:     v.NameEn,
				CreateDate: v.CreateDate,
				CreateBy:   v.CreateBy,
				Channel:    v.Channel,
				Enabled:    v.Enabled,
				IsDraft:    v.IsDraft,
			})
		}
	}

	return nil
}
