package client

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

const (
	// InternationalMinUserID 国际用户信息表最小ID（2亿）
	InternationalMinUserID = 200000000
	// InternationalMinID 国际数据库最小ID（1亿亿）
	InternationalMinID = 10000000000000000
)

var (
	// IamInternational iam国际服务
	IamInternational iampb.IamService
	// AiInternational AI国际服务
	AiInternational aipb.AiService
)

// IsInternationalUserID 判断是否为国际用户的ID
func IsInternationalUserID(id uint64) bool {
	return id >= InternationalMinUserID
}

// IsInternationalID 判断是否为国际数据库的ID
func IsInternationalID(id uint64) bool {
	return id >= InternationalMinID
}

// SplitUserIDByRegion 通过地域分割UserID
func SplitUserIDByRegion(ids []uint64) (national, international []uint64) {
	for _, id := range ids {
		if IsInternationalUserID(id) {
			international = append(international, id)
		} else {
			national = append(national, id)
		}
	}
	return
}

// AiClientByRegion 通过地区获取AI客户端
func AiClientByRegion(region basepb.Region) aipb.AiService {
	if region == basepb.Region_REGION_INTERNATIONAL {
		return AiInternational
	}
	return AiClient
}

// AiClientByID 通过数据主键获取AI客户端
func AiClientByID(id uint64) aipb.AiService {
	if IsInternationalID(id) {
		return AiInternational
	}
	return AiClient
}

// IamClientByRegion 通过地区获取IAM客户端
func IamClientByRegion(region basepb.Region) iampb.IamService {
	if region == basepb.Region_REGION_INTERNATIONAL {
		return IamInternational
	}
	return IamNational
}

// IamClientByID 通过数据主键获取IAM客户端
func IamClientByID(id uint64) iampb.IamService {
	if IsInternationalUserID(id) {
		return IamInternational
	}
	return IamNational
}
