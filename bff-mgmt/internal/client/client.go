// Package client RPC客户端
package client

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	graphpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/graph"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	reviewpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	searchpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/search"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	"github.com/asim/go-micro/v3"
)

var (
	// IamNational iam服务（国内）
	IamNational iampb.IamService
	// MgmtClient 运营端服务
	MgmtClient mgmtpb.MgmtService
	// ReviewClient 审核服务
	ReviewClient reviewpb.ReviewService
	// TeamClient 团队服务
	TeamClient teampb.TeamService
	// GraphClient 图谱服务
	GraphClient graphpb.GraphService
	// TagClient 标签服务
	TagClient tagpb.TagService
	// SearchClient 搜索服务
	SearchClient searchpb.SearchService
	// AiClient ai服务
	AiClient aipb.AiService
)

// RegisterClient 注册客户端
func RegisterClient(svc micro.Service) {
	IamNational = iampb.NewIamService(
		config.GetStringOr("client.iam", "tanlive.v2.iam"), svc.Client())
	IamInternational = iampb.NewIamService(
		config.GetStringOr("client.iam-international", "tanlive.v2.iam-international"), svc.Client())
	MgmtClient = mgmtpb.NewMgmtService(
		config.GetStringOr("client.mgmt", "tanlive.v2.mgmt"), svc.Client())
	ReviewClient = reviewpb.NewReviewService(
		config.GetStringOr("client.review", "tanlive.v2.review"), svc.Client())
	TeamClient = teampb.NewTeamService(
		config.GetStringOr("client.team", "tanlive.v2.team"), svc.Client())
	GraphClient = graphpb.NewGraphService(
		config.GetStringOr("client.graph", "tanlive.v2.graph"), svc.Client())
	TagClient = tagpb.NewTagService(
		config.GetStringOr("client.tag", "tanlive.v2.tag"), svc.Client())
	SearchClient = searchpb.NewSearchService(
		config.GetStringOr("client.search", "tanlive.v2.search"), svc.Client())
	AiClient = aipb.NewAiService(
		config.GetStringOr("client.ai", "tanlive.v2.ai"), svc.Client())
	AiInternational = aipb.NewAiService(
		config.GetStringOr("client.ai-international", "tanlive.v2.ai-international"), svc.Client())
}
