#!/bin/bash
trap "" SIGPIPE
CUR_PATH=$(dirname $(readlink -f "$0"))/
MONLOG=monlog.log
SAFESH=safe_sh

MON_PID_FILE=.safe_sh.pid

RET=0
isRunning=0

cd $CUR_PATH
exe_pid="-1"

#check proscess's status
function has_process()
{
    EXE=$1
    RET=0
    if [ ${exe_pid} == "-1" ]; then
        [ -f ${EXE}.pid ] && exe_pid=`cat ${EXE}.pid`
        echo `date`" [$EXE] process id is " $exe_pid
    fi

    PROC=`ps awwx -o pid,ppid,cmd | grep ${CUR_PATH}${EXE} | grep -- $exe_pid | wc -l`
    if [ "$PROC" -ge 1 ]; then
        RET=0
    elif [ -f ${EXE}.pid ] || [ ${exe_pid} == "-1" ]; then
        echo `date`" [$EXE] no process ${EXE} exist!"
        RET=-1
    else
        ## if process is killed by other safe_sh, do not restart
        echo `date`" [$EXE] has been stopped by other safe_sh!"
    fi
}

#check monitor's status
function has_mon()
{
    EXE=$1
    if [ -f $MON_PID_FILE ]
    then
        echo `date`" [$EXE] other $0 is running, check "
        pid=`cat $MON_PID_FILE`
        isRunning=`ps awwx -o pid,cmd | grep $pid | grep safe_sh | wc -l`
    else
        echo `date`" [$EXE] no other $0 is running"
        isRunning=0
    fi
}

#start process
function start_process()
{
    EXE=$1
    ${CUR_PATH}${EXE} &>> `p=${CUR_PATH}log;[[ ! -d "${p}" ]] && mkdir -p ${p};echo ${p}/safe_sh.log` &
    exe_pid=$!
    echo $exe_pid > ${EXE}.pid
    echo `date`" [$EXE] process ${EXE} start finished! pid is "$exe_pid
}

#start monitor
function start_mon()
{
    EXE=$1
    has_mon $EXE
    if [ "$isRunning" -eq 1 ]; then
        echo `date`" [$EXE] other $0 is running, exit "
        return
    fi
    echo $$ > $MON_PID_FILE
    echo `date`" [$EXE] $0 moniter start..."
    while true
    do
        has_process $EXE
        if [ "${RET}" == "-1" ]; then
             echo `date`" [$EXE] process ${EXE} restart..."
             stop_process $EXE
             start_process $EXE
        fi
        sleep 5
    done
}

#stop process
function stop_process()
{
    EXE=$1
    [ -f ${EXE}.pid ] || return
    pid=`cat ${EXE}.pid`
    for i in `ps awwx -o pid,ppid,cmd | grep ${CUR_PATH}${EXE} | grep $pid | awk '{printf $1" "}'`
    do
        echo `date`" [$EXE] kill ${CUR_PATH}${EXE} process "$i
        kill $i
        sleep 2
        has_process ${EXE}
        if [ "${RET}" == "0" ]; then
            kill -9 $i
        fi
    done
    rm ${EXE}.pid
}

#kill process using SIGTERM
function kill_process()
{
    EXE=$1
    [ -f ${EXE}.pid ] || return
    pid=`cat ${EXE}.pid`
    for i in `ps awwx -o pid,ppid,cmd | grep ${CUR_PATH}${EXE} | grep $pid | awk '{printf $1" "}'`
    do
        echo `date`" [$EXE] kill SIGTERM ${CUR_PATH}${EXE} process "$i
        kill $i
    done
    rm ${EXE}.pid
}

function stop_mon()
{
    EXE=$1
    [ -f $MON_PID_FILE ] || return
    pid=`cat $MON_PID_FILE`
    for i in `ps awwx -o pid,cmd | grep ${SAFESH} | grep $pid  | awk '{printf $1" "}'`
    do
        echo `date`" [$SAFESH] kill moniter process "$i
        kill -9 $i
    done
    rm $MON_PID_FILE
}

function set_trap()
{
    trap "kill_process $1 && exit" SIGTERM SIGINT SIGQUIT SIGKILL
}

function Usage()
{
    echo "usage: $0 (start|stop) exe"
    echo "   eg: $0 start exe"
}

if [ "$#" -ne "2" ]
then
    Usage
    exit
fi

set_trap $2

if test "$1" == "start"; then
    ulimit -c unlimited
    start_mon $2
elif test "$1" == "stop"; then
    stop_mon $2
    stop_process $2
elif test "$1" == "kill"; then
    kill_process $2
else
    Usage
fi

exit