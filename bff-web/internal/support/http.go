package support

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
)

const (
	HeaderLang = "Lang"
)

const (
	DefaultLang = "zh"
)

// GetHeaderLang 从HTTP头中获取语言
func GetHeaderLang(ctx context.Context) string {
	req := bff.RequestFromContext(ctx)
	if req == nil {
		return DefaultLang
	}
	lang := req.Header.Get(HeaderLang)
	if lang == "" {
		return DefaultLang
	}
	return lang
}
