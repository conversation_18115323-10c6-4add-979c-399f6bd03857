package support

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	nu "net/url"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"github.com/go-redsync/redsync/v4"
	"github.com/hashicorp/go-uuid"
	"golang.org/x/net/html"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

const maxRetries = 2 // 设置最大重试次数

func generateHash(input string) string {
	hasher := sha256.New()
	hasher.Write([]byte(input))
	hashBytes := hasher.Sum(nil)
	return base64.RawURLEncoding.EncodeToString(hashBytes)
}

// FetchHtmlTitles 批量获取网页title
func FetchHtmlTitles(ctx context.Context, urls []string) map[string]string {
	var wg sync.WaitGroup
	titles := make(map[string]string)
	for _, url := range urls {
		wg.Add(1)
		url := url
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			defer wg.Done()
			titles[url] = fetchHtmlTitleWithDistributedLock(ctx, url)
			return nil
		}, boot.TraceGo(ctx))
	}

	wg.Wait()
	log.WithContext(ctx).Infof("html proxy url titles %+v", titles)
	return titles
}

// fetchHtmlTitleWithRetry 增加重试逻辑
func fetchHtmlTitleWithRetry(ctx context.Context, url string) string {
	var title string
	for attempt := 1; attempt <= maxRetries; attempt++ {
		title = fetchHtmlTitle(ctx, url)
		if title != url { // 当返回的不是URL本身时，说明获取到标题了
			break
		}
		time.Sleep(300 * time.Millisecond)
	}
	if title != url { // 没获取到再次使用host获取
		parsedUrl, err := nu.Parse(url)
		if err != nil {
			fmt.Println("Error parsing URL:", err)
			return title
		}
		hostTitle := fetchHtmlTitle(ctx, parsedUrl.Host)
		if hostTitle != parsedUrl.Host {
			return title
		}
		hostConfig := config.GetStringMap("html.proxy")
		if hostConfig[parsedUrl.Host] != nil && hostConfig[parsedUrl.Host].(string) != "" {
			return hostConfig[parsedUrl.Host].(string)
		}
	}

	return title
}

func fetchHtmlTitleWithDistributedLock(ctx context.Context, url string) string {
	urlHash := generateHash(url)
	lockName := fmt.Sprintf("support:proxy:htmlTitleCacheLock:%s", urlHash)
	dmtx := logic.NewDistributedLock(lockName,
		redsync.WithExpiry(30*time.Second),
		redsync.WithTries(3))

	err := dmtx.Lock()
	if err != nil {
		return url
	}
	defer dmtx.Unlock()

	cacheKey := fmt.Sprintf("support:proxy:htmlTitleCache:%s", urlHash)

	cachedTitle, err := xredis.Default.Get(ctx, cacheKey).Bytes()
	if err == nil {
		return string(cachedTitle)
	}

	// 获取title
	title := fetchHtmlTitleWithRetry(ctx, url)
	if title != url {
		xredis.Default.Set(ctx, cacheKey, title, 24*time.Hour)
	}

	return strings.TrimSpace(title)
}

// fetchHtmlTitle 获取页面标题，失败将返回url本身
func fetchHtmlTitle(ctx context.Context, url string) string {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return url
	}

	uid, _ := uuid.GenerateUUID()
	userAgent := fmt.Sprintf("Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 tanlive-%s", uid)
	req.Header.Set("User-Agent", userAgent)

	start := time.Now()
	client := &http.Client{
		Timeout: 3000 * time.Millisecond,
	}
	resp, err := client.Do(req)
	elapsed := time.Since(start)

	if err != nil {
		return url
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return url
	}
	// reader, _ := charset.NewReader(resp.Body, "utf-8")
	//
	// body, err := io.ReadAll(reader)
	// if err != nil {
	//	return url
	// }
	// // 解析HTML并提取<title>
	// doc, err := html.Parse(strings.NewReader(string(body)))

	doc, err := html.Parse(resp.Body)
	if err != nil {
		return url
	}

	var title string
	var ogTitle string
	var twitterTitle string

	var f func(*html.Node)
	f = func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "title" {
			if n.FirstChild != nil {
				title = n.FirstChild.Data
			}
		}
		// 查找<meta property="og:title" content="..."> 或 <meta name="twitter:title" content="...">
		if n.Type == html.ElementNode && n.Data == "meta" {
			var property string
			var content string
			for _, attr := range n.Attr {
				if attr.Key == "property" && (attr.Val == "og:title") {
					property = attr.Val
				}
				if attr.Key == "name" && (attr.Val == "twitter:title") {
					property = attr.Val
				}
				if attr.Key == "content" {
					content = attr.Val
				}
			}
			if property == "og:title" {
				ogTitle = content
			}
			if property == "twitter:title" {
				twitterTitle = content
			}
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			f(c)
		}
	}
	f(doc)

	var finalTitle string
	if title != "" {
		finalTitle = title
	} else if ogTitle != "" {
		finalTitle = ogTitle
	} else if twitterTitle != "" {
		finalTitle = twitterTitle
	}
	log.WithContext(ctx).Infof("FetchHtmlTitle request:\n%s\nget finalTitle:\n%s, cost(ms):%d", url, finalTitle, elapsed.Milliseconds())

	if finalTitle == "" {
		return url
	}

	if !utf8.ValidString(finalTitle) {
		// try gb18030
		reader := transform.NewReader(strings.NewReader(finalTitle), simplifiedchinese.GB18030.NewDecoder())
		gbStr, err := io.ReadAll(reader)
		if err != nil {
			return url
		}
		finalTitle = string(gbStr)
	}
	if !utf8.ValidString(finalTitle) {
		return url
	}
	if strings.Contains(finalTitle, "404") || strings.Contains(finalTitle, "loading") || strings.Contains(finalTitle, "页面找不到") {
		return url
	}
	return finalTitle
}
