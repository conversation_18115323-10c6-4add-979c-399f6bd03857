package support

import (
	"bytes"
	"encoding/json"
	"net/http"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
)

// LLMWeChatSendMsg 微信发送信息
var LLMWeChatSendMsg = func(msg string) {
	key := config.GetString("llm.wechat_send_msg_key")
	url := config.GetStringOr("llm.wechat_send_msg_url", "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=")
	message := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]string{
			"content": msg,
		},
	}
	jsonData, _ := json.Marshal(message)
	req, err := http.NewRequest("POST", url+key, bytes.NewBuffer(jsonData))
	if err != nil {
		return
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	_, _ = client.Do(req)
}
