package iam

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcrypto"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/iam"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
)

// Authenticator 认证器
type Authenticator interface {
	// ValidateCredentials 校验凭证
	ValidateCredentials(context.Context) (*iampb.UserInfo, error)
}

// LoginLogic 登录逻辑
type LoginLogic struct {
	Authenticator Authenticator
}

// Login 登录
func (l *LoginLogic) Login(ctx context.Context) (*iampb.UserInfo, error) {
	if err := l.callBefore(ctx); err != nil {
		return nil, err
	}

	user, err := l.Authenticator.ValidateCredentials(ctx)

	l.callAfter(ctx, err)

	return user, err
}

// BindThirdAccount 绑定第三方账号
func (l *LoginLogic) BindThirdAccount(ctx context.Context, user *iampb.UserInfo, ticket string) error {
	if user == nil || len(ticket) == 0 {
		return nil
	}

	thirdUser, err := loadThirdUserInfoFromCache(ctx, ticket)
	if err != nil {
		return err
	}

	_, err = client.IamClient.BindUserThirdIndex(ctx, &iampb.ReqBindUserThirdIndex{
		UserId:    user.Id,
		ThirdUser: thirdUser,
	})
	if err != nil {
		return err
	}

	if err = clearThirdAuthenticateInfo(ctx, ticket); err != nil {
		return err
	}

	return nil
}

// UpdateIPRegion 更新IP属地
func (l *LoginLogic) UpdateIPRegion(ctx context.Context, user *iampb.UserInfo) error {
	rsp, err := client.IamClient.GetIpRegion(ctx, &iampb.ReqGetIpRegion{
		Ip: bff.RequestFromContext(ctx).Header.Get("X-Real-Ip"),
	})
	if err != nil {
		return err
	}

	_, err = client.IamClient.UpdateUserInfo(ctx, &iampb.ReqUpdateUserInfo{
		User: &iampb.UserInfo{
			Id:       user.Id,
			Country:  rsp.IpRegion.Country,
			Province: rsp.IpRegion.Province,
			City:     rsp.IpRegion.City,
		},
		Mask: &fieldmaskpb.FieldMask{Paths: []string{"country", "province", "city"}},
	})
	if err != nil {
		return err
	}

	//_, err = client.TeamClient.UpdateTeamInfo(ctx, &teampb.ReqUpdateTeamInfo{
	//	Team: &teampb.TeamInfo{
	//		IpRegion: rsp.IpRegion.Country + "," + rsp.IpRegion.Province + "," + rsp.IpRegion.City,
	//	},
	//	Mask: &fieldmaskpb.FieldMask{Paths: []string{"ip_region"}},
	//	Condition: &teampb.ReqUpdateTeamInfo_ByHolderId{
	//		ByHolderId: user.Id,
	//	},
	//})
	//if err != nil {
	//	return err
	//}

	return nil
}

func (l *LoginLogic) callBefore(ctx context.Context) error {
	if before, ok := l.Authenticator.(interface {
		Before(context.Context) error
	}); ok {
		if err := before.Before(ctx); err != nil {
			return err
		}
	}
	return nil
}

func (l *LoginLogic) callAfter(ctx context.Context, err error) {
	success := true
	if err != nil {
		if _, ok := xerrors.IsInternalServerError(err); !ok {
			success = false
		}
	}

	if after, ok := l.Authenticator.(interface {
		After(context.Context, bool) error
	}); ok {
		if err := after.After(ctx, success); err != nil {
			log.WithContext(ctx).Errorw("after login", "err", err)
		}
	}
}

type usernameAuthenticator struct {
	credentials *bffiampb.ReqLogin_AccountCredentials
	username    string
	password    string
}

// NewUsernameAuthenticator 新建用户名登录认证器
func NewUsernameAuthenticator(credentials *bffiampb.ReqLogin_AccountCredentials) Authenticator {
	return &usernameAuthenticator{
		credentials: credentials,
	}
}

// Before 前置函数
func (a *usernameAuthenticator) Before(ctx context.Context) error {
	username, err := RsaDecrypt(a.credentials.Username)
	if err != nil {
		return xerrors.ValidationError("invalid username")
	}
	password, err := RsaDecrypt(a.credentials.Password)
	if err != nil {
		return xerrors.ValidationError("invalid password")
	}

	if err := checkLoginFailedCount(ctx, username); err != nil {
		return err
	}

	a.username = username
	a.password = password

	return nil
}

// ValidateCredentials 检查凭证
func (a *usernameAuthenticator) ValidateCredentials(ctx context.Context) (*iampb.UserInfo, error) {
	return validateLoginCredentials(ctx, &iampb.ReqValidateLoginCredentials{
		Identifier:     a.username,
		IdentifierType: iampb.UserIndexType_USER_INDEX_TYPE_USERNAME,
		Password:       a.password,
	})
}

// After 后置函数
func (a *usernameAuthenticator) After(ctx context.Context, success bool) error {
	if !success {
		return incrementLoginFailedCount(ctx, a.username)
	}

	return nil
}

type phoneAuthenticator struct {
	credentials *bffiampb.ReqLogin_PhoneCredentials
	phone       string
}

// NewPhoneAuthenticator 新建手机登录认证器
func NewPhoneAuthenticator(credentials *bffiampb.ReqLogin_PhoneCredentials) Authenticator {
	return &phoneAuthenticator{
		credentials: credentials,
	}
}

// Before 前置函数
func (a *phoneAuthenticator) Before(ctx context.Context) error {
	phone, err := RsaDecrypt(a.credentials.Phone)
	if err != nil {
		return xerrors.ValidationError("invalid phone")
	}

	if err := validateLoginOtp(ctx,
		bffiampb.OtpReceiverKind_OTP_RECEIVER_KIND_PHONE, phone, a.credentials.AuthCode); err != nil {
		return err
	}

	a.phone = phone

	return nil
}

// ValidateCredentials 检查凭证
func (a *phoneAuthenticator) ValidateCredentials(ctx context.Context) (*iampb.UserInfo, error) {
	return validateLoginCredentials(ctx, &iampb.ReqValidateLoginCredentials{
		Identifier:     strings.ReplaceAll(a.phone, "-", ""),
		IdentifierType: iampb.UserIndexType_USER_INDEX_TYPE_PHONE,
	})
}

type emailAuthenticator struct {
	credentials *bffiampb.ReqLogin_EmailCredentials
	email       string
}

// NewEmailAuthenticator 新建邮件登录认证器
func NewEmailAuthenticator(credentials *bffiampb.ReqLogin_EmailCredentials) Authenticator {
	return &emailAuthenticator{
		credentials: credentials,
	}
}

// Before 前置函数
func (a *emailAuthenticator) Before(ctx context.Context) error {
	email, err := RsaDecrypt(a.credentials.Email)
	if err != nil {
		return xerrors.ValidationError("invalid phone")
	}

	if err := validateLoginOtp(ctx,
		bffiampb.OtpReceiverKind_OTP_RECEIVER_KIND_EMAIL, email, a.credentials.AuthCode); err != nil {
		return err
	}

	a.email = email

	return nil
}

// ValidateCredentials 检查凭证
func (a *emailAuthenticator) ValidateCredentials(ctx context.Context) (*iampb.UserInfo, error) {
	return validateLoginCredentials(ctx, &iampb.ReqValidateLoginCredentials{
		Identifier:     a.email,
		IdentifierType: iampb.UserIndexType_USER_INDEX_TYPE_EMAIL,
	})
}

type thirdAuthenticator struct {
	credentials *bffiampb.ReqLogin_ThirdCredentials
	thirdTicket string
	info        *iampb.ThirdUserInfo
}

// NewThirdAuthenticator 新建第三方登录认证器
func NewThirdAuthenticator(credentials *bffiampb.ReqLogin_ThirdCredentials) Authenticator {
	return &thirdAuthenticator{
		credentials: credentials,
	}
}

// Before 前置函数
func (a *thirdAuthenticator) Before(ctx context.Context) error {
	thirdTicket, err := RsaDecrypt(a.credentials.ThirdTicket)
	if err != nil {
		return xerrors.ValidationError("invalid phone")
	}

	info, err := loadThirdUserInfoFromCache(ctx, thirdTicket)
	if err != nil {
		return err
	}

	a.thirdTicket = thirdTicket
	a.info = info

	return nil
}

// ValidateCredentials 检查凭证
func (a *thirdAuthenticator) ValidateCredentials(ctx context.Context) (*iampb.UserInfo, error) {
	return validateLoginCredentials(ctx, &iampb.ReqValidateLoginCredentials{
		Identifier:     a.info.UnionId,
		IdentifierType: a.info.IdentifierType,
	})
}

// After 后置函数
func (a *thirdAuthenticator) After(ctx context.Context, success bool) error {
	if success {
		return clearThirdAuthenticateInfo(ctx, a.thirdTicket)
	}

	return nil
}

func validateLoginCredentials(ctx context.Context, req *iampb.ReqValidateLoginCredentials) (*iampb.UserInfo, error) {
	rsp, err := client.IamClient.ValidateLoginCredentials(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp.User, nil
}

func validateLoginOtp(ctx context.Context,
	receiverKind bffiampb.OtpReceiverKind, receiverIdentity, password string) error {
	_, err := client.IamClient.ValidateOneTimePassword(ctx, &iampb.ReqValidateOneTimePassword{
		Scene:    iampb.OtpScene_OTP_SCENE_LOGIN,
		Receiver: formatLoginOtpReceiver(receiverKind, receiverIdentity),
		Otp:      password,
	})
	return err
}

func formatLoginOtpReceiver(kind bffiampb.OtpReceiverKind, identity string) *iampb.OtpReceiver {
	receiver := &iampb.OtpReceiver{}

	switch kind {
	case bffiampb.OtpReceiverKind_OTP_RECEIVER_KIND_PHONE:
		receiver.Receiver = &iampb.OtpReceiver_Phone{
			Phone: identity,
		}
	case bffiampb.OtpReceiverKind_OTP_RECEIVER_KIND_EMAIL:
		receiver.Receiver = &iampb.OtpReceiver_Email{
			Email: identity,
		}
	}

	return receiver
}

func loadThirdUserInfoFromCache(ctx context.Context, ticket string) (*iampb.ThirdUserInfo, error) {
	data, err := xredis.Default.Get(ctx, formatThirdUserInfoCacheKey(ticket)).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, xerrors.ValidationError("invalid third ticket")
		}
		return nil, xerrors.InternalServerError(err)
	}

	info := &iampb.ThirdUserInfo{}
	if err = protojson.Unmarshal(data, info); err != nil {
		return nil, xerrors.InternalServerError(err)
	}

	return info, nil
}

func clearThirdAuthenticateInfo(ctx context.Context, ticket string) error {
	return xredis.Default.Del(ctx, formatThirdUserInfoCacheKey(ticket)).Err()
}

func formatThirdUserInfoCacheKey(ticket string) string {
	return "STR:101:" + ticket
}

func checkLoginFailedCount(ctx context.Context, identifier string) error {
	failedCountKey := formatLoginFailedCountCacheKey(identifier)
	failedCount, err := xredis.Default.Get(ctx, failedCountKey).Int()
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	maxFailedCount := config.GetIntOr("login.maxFailedCount", 5)
	if failedCount >= maxFailedCount {
		return xerrors.TooManyRequestsError("too many requests")
	}

	return nil
}

func incrementLoginFailedCount(ctx context.Context, identifier string) error {
	failedCountKey := formatLoginFailedCountCacheKey(identifier)

	p := xredis.Default.Pipeline()
	p.Incr(ctx, failedCountKey)
	p.ExpireXX(ctx, failedCountKey, time.Hour*24)
	_, err := p.Exec(ctx)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// notice: 保存到redis的key格式需与旧版保持一致，迁移完成前切勿随意修改
func formatLoginFailedCountCacheKey(identifier string) string {
	hashed := xcrypto.Sha1(identifier)
	return "STR:login_failed_count_" + hashed
}

// IsNewUser 判断是否为新用户
// notice: 保存到redis的key格式需与旧版保持一致，迁移完成前切勿随意修改
func IsNewUser(ctx context.Context, user *iampb.UserInfo) bool {
	key := "user:login:new:status:" + strconv.FormatUint(user.Id, 10)
	exists, err := xredis.Default.Exists(ctx, key).Result()
	if err != nil {
		log.WithContext(ctx).Errorw("check is new user", "err", err)
		return false
	}
	return exists > 0
}

// RsaDecrypt RSA解密
func RsaDecrypt(s string) (string, error) {
	decrypted, err := xcrypto.Use("rsa").Decrypt(
		xstrings.ToBytes(s),
		xcrypto.Encoding(xcrypto.Base64StdEncoding),
	)
	if err != nil {
		return "", err
	}
	return xstrings.FromBytes(decrypted), nil
}
