package iam

import (
	"context"
	"strconv"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/iam"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"google.golang.org/protobuf/types/known/durationpb"
)

// SendAuthOtpLogic 发送认证验证码
type SendAuthOtpLogic struct {
	otpLogic
}

// FormatReceiver 格式化接收者
func (l *SendAuthOtpLogic) FormatReceiver(req *bffiampb.ReqSendAuthOtp) (*iampb.OtpReceiver, error) {
	receiver := &iampb.OtpReceiver{}

	switch req.ReceiverKind {
	case bffiampb.OtpReceiverKind_OTP_RECEIVER_KIND_PHONE:
		receiver.Receiver = &iampb.OtpReceiver_Phone{
			Phone: req.ReceiverPhone,
		}
	case bffiampb.OtpReceiverKind_OTP_RECEIVER_KIND_EMAIL:
		receiver.Receiver = &iampb.OtpReceiver_Email{
			Email: req.ReceiverEmail,
		}
	default:
		return nil, xerrors.ValidationError("unsupported receiver kind")
	}

	return receiver, nil
}

// SendTfaOtpLogic 发送二次验证验证码
type SendTfaOtpLogic struct {
	otpLogic
}

// FormatReceiver 格式化接收者
func (l *SendTfaOtpLogic) FormatReceiver(
	kind bffiampb.OtpReceiverKind, me *iampb.UserInfo) (*iampb.OtpReceiver, error) {
	receiver := &iampb.OtpReceiver{}

	switch kind {
	case bffiampb.OtpReceiverKind_OTP_RECEIVER_KIND_PHONE:
		receiver.Receiver = &iampb.OtpReceiver_PhoneByUserId{
			PhoneByUserId: me.Id,
		}
	case bffiampb.OtpReceiverKind_OTP_RECEIVER_KIND_EMAIL:
		receiver.Receiver = &iampb.OtpReceiver_EmailByUserId{
			EmailByUserId: me.Id,
		}
	default:
		return nil, xerrors.ValidationError("unsupported receiver kind")
	}

	return receiver, nil
}

type otpLogic struct {
}

// CreateOtp 创建验证码
func (l *otpLogic) CreateOtp(ctx context.Context, scene iampb.OtpScene,
	receiver *iampb.OtpReceiver, lang string) (*iampb.RspCreateOneTimePassword, error) {
	return client.IamClient.CreateOneTimePassword(ctx, &iampb.ReqCreateOneTimePassword{
		Scene:    scene,
		Receiver: receiver,
		Lang:     lang,
	})
}

// HandleRateLimitResult 处理限流结果
func (l *otpLogic) HandleRateLimitResult(ctx context.Context, result *basepb.RateLimitResult) error {
	if result == nil {
		return nil
	}

	bff.SetHeaders(ctx, map[string]string{
		"RateLimit-Limit":     strconv.FormatInt(int64(result.Limit), 10),
		"RateLimit-Remaining": strconv.FormatInt(int64(result.Remaining), 10),
		"RateLimit-Reset":     l.durationToSecondString(result.ResetAfter),
		"Retry-After":         l.durationToSecondString(result.RetryAfter),
	})
	if !result.Allowed {
		return xerrors.TooManyRequestsError("too many requests")
	}

	return nil
}

func (l *otpLogic) durationToSecondString(d *durationpb.Duration) string {
	return strconv.FormatInt(int64(d.AsDuration()/time.Second), 10)
}
