package iam

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// GetUserByKey 通过主键获取用户信息
func GetUserByKey(ctx context.Context, userID uint64) (*iampb.UserInfo, error) {
	if userID == 0 {
		return nil, nil
	}
	userMap, err := GetUsersMapByKey(ctx, []uint64{userID})
	if err != nil {
		return nil, err
	}
	return userMap[userID], nil
}

// GetUsersMapByKey 通过主键查询用户映射
func GetUsersMapByKey(ctx context.Context, userIDs []uint64) (map[uint64]*iampb.UserInfo, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	rsp, err := client.IamClient.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
		Id: userIDs,
	})
	if err != nil {
		return nil, err
	}

	m := make(map[uint64]*iampb.UserInfo, len(rsp.UserSet))
	for _, user := range rsp.UserSet {
		m[user.Id] = user
	}
	return m, nil
}

// IsTeamUser 当前用户是否是团队账户
func IsTeamUser(ctx context.Context) bool {
	req := bff.RequestFromContext(ctx)
	return req.Header.Get("As-Team") == "true"
}

// FetchUserName 通过ids获取门户端用户名
func FetchUserName(ctx context.Context, userIds []uint64) (map[uint64]string, error) {
	userNames := make(map[uint64]string, len(userIds))

	if len(userIds) == 0 {
		return userNames, nil
	}

	// 按地域分割用户ID
	nationalUserIds, internationalUserIds := client.SplitUserIDByRegion(userIds)

	// 获取国内用户信息
	if len(nationalUserIds) > 0 {
		users, err := client.IamNational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
			Id: nationalUserIds,
		})
		if err != nil {
			return nil, err
		}
		if len(users.UserSet) > 0 {
			for _, user := range users.UserSet {
				userNames[user.Id] = user.Username
			}
		}
	}

	// 获取国际用户信息
	if len(internationalUserIds) > 0 {
		users, err := client.IamInternational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
			Id: internationalUserIds,
		})
		if err != nil {
			return nil, err
		}
		if len(users.UserSet) > 0 {
			for _, user := range users.UserSet {
				userNames[user.Id] = user.Username
			}
		}
	}

	return userNames, nil
}

// FetchUserTeam 获取用户团队信息
func FetchUserTeam(ctx context.Context, userId uint64) (*teampb.TeamInfo, error) {
	teams, err := client.TeamClient.GetUserTeams(ctx, &teampb.ReqGetUserTeams{
		UserId: []uint64{userId},
	})
	if err != nil {
		return nil, err
	}
	return teams.GetTeams()[userId].GetTeamInfo(), nil
}

// FetchUserTeamName 获取用户对应团队简称
func FetchUserTeamName(ctx context.Context, userIds []uint64) (map[uint64]string, error) {
	teamNames := make(map[uint64]string, len(userIds))
	teams, err := client.TeamClient.GetUserTeams(ctx, &teampb.ReqGetUserTeams{
		UserId: userIds,
	})
	if err != nil {
		return nil, err
	}
	for user, v := range teams.GetTeams() {
		teamNames[user] = v.TeamInfo.ShortName
	}
	return teamNames, nil
}

// FetchTeamName 通过ids获取团队简称
func FetchTeamName(ctx context.Context, teamIds []uint64) (map[uint64]string, error) {
	teamNames := make(map[uint64]string, len(teamIds))
	teams, err := client.TeamClient.GetTeams(ctx, &teampb.ReqGetTeams{
		Filter: &teampb.ReqGetTeams_Filter{TeamId: teamIds},
	})
	if err != nil {
		return nil, err
	}
	for _, v := range teams.GetTeams() {
		teamNames[v.TeamInfo.Id] = v.TeamInfo.ShortName
	}
	return teamNames, nil
}

// FetchTeamInfo 通过ids获取团队信息
func FetchTeamInfo(ctx context.Context, teamIds []uint64) (map[uint64]*teampb.TeamInfo, error) {
	infos := make(map[uint64]*teampb.TeamInfo, len(teamIds))
	teams, err := client.TeamClient.GetTeams(ctx, &teampb.ReqGetTeams{
		Filter: &teampb.ReqGetTeams_Filter{TeamId: teamIds, WithDisbanded: true},
	})
	if err != nil {
		return nil, err
	}
	for _, v := range teams.GetTeams() {
		infos[v.TeamInfo.Id] = v.TeamInfo
	}
	return infos, nil
}

// GetUserIdByName 通过用户名获取用户id,完全匹配。没找到会返回 id = 0
func GetUserIdByName(ctx context.Context, userName string, region ...base.Region) (uint64, error) {
	// getUserIdFromIamClient 从指定的IAM客户端获取用户ID
	getUserIdFromIamClient := func(ctx context.Context, iamClient iampb.IamService, req *iampb.ReqGetUserIds) (uint64, error) {
		rsp, err := iamClient.GetUserIds(ctx, req)
		if err != nil {
			return 0, err
		}
		if len(rsp.Ids) > 0 {
			return rsp.Ids[0], nil
		}
		return 0, nil
	}

	req := &iampb.ReqGetUserIds{
		Username:   userName,
		ExactMatch: true,
	}

	// 如果指定了地区，直接从对应地区查询
	if len(region) > 0 {
		req.Region = region[0]
		return getUserIdFromIamClient(ctx, client.IamByRegion(region[0]), req)
	}

	// 先从国内查询
	req.Region = base.Region_REGION_NATIONAL
	if userId, err := getUserIdFromIamClient(ctx, client.IamNational, req); err != nil {
		return 0, err
	} else if userId > 0 {
		return userId, nil
	}

	// 国内未找到，再从国际查询
	req.Region = base.Region_REGION_INTERNATIONAL
	return getUserIdFromIamClient(ctx, client.IamInternational, req)
}
