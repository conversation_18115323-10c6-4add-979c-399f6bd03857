package cms

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	cmspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/cms"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

// HelpCenterTree 帮助中心目录树
type HelpCenterTree []*HelpCenterTreeNode

// HelpCenterTreeNode 帮助中心目录树节点
type HelpCenterTreeNode struct {
	Title      string              `json:"name"`
	Visibility cmspb.DocVisibility `json:"visibility"`
	Url        string              `json:"url"`
	Expand     bool                `json:"expand"`
	Children   HelpCenterTree      `json:"child"`
}

// HelpCenterUserIdentity 用户身份
type HelpCenterUserIdentity struct {
	IsGuest       bool
	IsUser        bool
	IsAiAssistant bool
}

type HelpCenterTreeLogic struct {
}

// Download 下载文件
func (l *HelpCenterTreeLogic) Download(ctx context.Context, httpReq *xhttp.Request) (HelpCenterTree, error) {
	domain := config.GetString("tcloud.cos.cdn")
	var path string
	if strings.ToLower(httpReq.Header.Get("Lang")) == "en" {
		path = config.GetStringOr("helpCenter.treeEn", "/helpcenterdoc/livedoc/en/tree.json")
	} else {
		path = config.GetStringOr("helpCenter.tree", "/helpcenterdoc/livedoc/tree.json")
	}
	url := strings.TrimSuffix(domain, "/") + path

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	rsp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, errors.New("send http request: " + err.Error())
	}
	defer rsp.Body.Close()

	if rsp.StatusCode != http.StatusOK {
		return nil, errors.New("invalid http response: " + strconv.Itoa(rsp.StatusCode))
	}

	rspBody, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, errors.New("read http response body: " + err.Error())
	}

	var tree HelpCenterTree
	if err = json.Unmarshal(rspBody, &tree); err != nil {
		return nil, errors.New("unmarshal tree: " + err.Error())
	}

	return tree, nil
}

// GetUserIdentity 获取用户身份
func (l *HelpCenterTreeLogic) GetUserIdentity(ctx context.Context,
	getAssistants func(context.Context) ([]uint64, error)) (HelpCenterUserIdentity, error) {
	identity := HelpCenterUserIdentity{}

	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	if me != nil {
		identity.IsUser = true

		assistantIDs, err := getAssistants(ctx)
		if err != nil {
			return identity, err
		}

		if len(assistantIDs) > 0 {
			identity.IsAiAssistant = true
		}
	}

	return identity, nil
}

// FilterVisibility 过滤可见性
func (l *HelpCenterTreeLogic) FilterVisibility(tree HelpCenterTree, identity HelpCenterUserIdentity) HelpCenterTree {
	newTree := make([]*HelpCenterTreeNode, 0, len(tree))
	for _, node := range tree {
		switch node.Visibility {
		case cmspb.DocVisibility_DOC_VISIBILITY_AI_ASSISTANT:
			if !identity.IsAiAssistant {
				continue
			}
		case cmspb.DocVisibility_DOC_VISIBILITY_USER:
			if !identity.IsUser {
				continue
			}
		}

		node.Children = l.FilterVisibility(node.Children, identity)
		newTree = append(newTree, node)
	}

	return newTree
}
