package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

// InternationalMinUserID 国际用户ID最小值
const InternationalMinUserID = 200000000

// IsInternationalUser 是否为国际用户
func IsInternationalUser(userID uint64) bool {
	return userID >= InternationalMinUserID
}

// SplitUserIDs 将用户ID分割为国内和国际的
func SplitUserIDs(userIDs []uint64) (national, international []uint64) {
	for _, userID := range userIDs {
		if userID == 0 {
			continue
		}
		if IsInternationalUser(userID) {
			international = append(international, userID)
		} else {
			national = append(national, userID)
		}
	}
	return
}

// FetchUserName 通过获取门户端用户名
func FetchUserName(ctx context.Context, userIDs []uint64) (map[uint64]string, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	users, err := GetUserByKeys(ctx, userIDs)
	if err != nil {
		return nil, err
	}

	userNames := make(map[uint64]string, len(userIDs))

	for _, user := range users {
		userNames[user.Id] = user.Username
	}

	return userNames, nil
}

// GetUserByKeys 通过主键查询用户映射
func GetUserByKeys(ctx context.Context, userIDs []uint64) (map[uint64]*iampb.UserInfo, error) {
	national, international := SplitUserIDs(userIDs)

	errs := make([]error, 2)
	results := make([][]*iampb.UserInfo, 2)
	g := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	if len(national) > 0 {
		g.SafeGo(func(ctx context.Context) error {
			rsp, err := client.IamNational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
				Id: national,
			})
			if err != nil {
				errs[0] = err
				return err
			}

			results[0] = rsp.UserSet

			return nil
		})
	}

	if len(international) > 0 {
		g.SafeGo(func(ctx context.Context) error {
			rsp, err := client.IamInternational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
				Id: international,
			})
			if err != nil {
				errs[1] = err
				return err
			}

			results[1] = rsp.UserSet

			return nil
		})
	}

	g.Wait()

	for _, err := range errs {
		if err != nil {
			return nil, err
		}
	}

	users := make(map[uint64]*iampb.UserInfo, len(results[0])+len(results[1]))
	for _, result := range results {
		for _, user := range result {
			users[user.Id] = user
		}
	}

	return users, nil
}

// LoadUserCards 加载用户卡片
func LoadUserCards(ctx context.Context, userIDs []uint64) ([]*iampb.UserCard, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	users, err := GetUserByKeys(ctx, userIDs)
	if err != nil {
		return nil, err
	}

	cards := make([]*iampb.UserCard, 0, len(users))
	for _, user := range users {
		cards = append(cards, &iampb.UserCard{
			Id:       user.Id,
			Username: user.Username,
			Image:    user.Image,
			Level:    user.Level,
		})
	}

	return cards, nil
}
