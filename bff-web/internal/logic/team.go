package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// FetchTeamName 通过ids获取团队简称
func FetchTeamName(ctx context.Context, teamIDs []uint64) (map[uint64]string, error) {
	if len(teamIDs) == 0 {
		return nil, nil
	}

	teamNames := make(map[uint64]string, len(teamIDs))
	teams, err := client.TeamClient.GetTeams(ctx, &teampb.ReqGetTeams{
		Filter: &teampb.ReqGetTeams_Filter{TeamId: teamIDs},
	})
	if err != nil {
		return nil, err
	}
	for _, v := range teams.GetTeams() {
		teamNames[v.TeamInfo.Id] = v.TeamInfo.ShortName
	}
	return teamNames, nil
}

// LoadTeamCards 加载团队卡片
func LoadTeamCards(ctx context.Context, teamIDs []uint64) ([]*teampb.TeamCard, error) {
	if len(teamIDs) == 0 {
		return nil, nil
	}

	rsp, err := client.TeamClient.GetTeams(ctx, &teampb.ReqGetTeams{
		Filter: &teampb.ReqGetTeams_Filter{TeamId: teamIDs},
	})
	if err != nil {
		return nil, err
	}

	cards := make([]*teampb.TeamCard, 0, len(rsp.Teams))
	for _, team := range rsp.Teams {
		cards = append(cards, &teampb.TeamCard{
			Id:          team.TeamInfo.Id,
			ShortName:   team.TeamInfo.ShortName,
			FullName:    team.TeamInfo.FullName,
			IsVerified:  team.TeamInfo.IsVerified,
			BriefIntro:  team.TeamInfo.BriefIntro,
			Level:       team.TeamInfo.Level,
			LogoUrl:     team.TeamInfo.LogoUrl,
			IsPublished: team.TeamInfo.IsPublished,
		})
	}

	return cards, nil
}
