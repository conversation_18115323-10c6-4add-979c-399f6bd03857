package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
)

// GetClientIP 获取客户端IP
func GetClientIP(ctx context.Context) string {
	if req := bff.RequestFromContext(ctx); req != nil {
		return req.Header.Get("X-Real-Ip")
	}
	return ""
}

// GetLang 从HEADER中获取语言
func GetLang(ctx context.Context) string {
	return bff.RequestFromContext(ctx).Header.Get("Lang")
}

// AsTeam 是否为团队请求
func AsTeam(ctx context.Context) bool {
	if req := bff.RequestFromContext(ctx); req != nil && req.Header.Get("As-Team") == "true" {
		return true
	}
	return false
}
