package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/captcha"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
)

// ValidateTcloudCaptcha 校验腾讯云验证码
func ValidateTcloudCaptcha(ctx context.Context, para *basepb.TcloudCaptcha, ip string) error {
	result, err := captcha.DescribeCaptchaResult(ctx, &captcha.ReqDescribeCaptchaResult{
		Ticket:  para.Ticket,
		Randstr: para.Randstr,
		UserIp:  ip,
	})
	if err != nil {
		return err
	}
	if result.CaptchaCode != 1 {
		return xerrors.New(errorspb.BaseError_TcloudCaptchaError, result.CaptchaMsg)
	}
	return nil
}
