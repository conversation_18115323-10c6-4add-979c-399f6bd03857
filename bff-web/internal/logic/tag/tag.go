package tag

import (
	"context"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
)

// TransformOrderBy 转变OrderBy类型
func TransformOrderBy(orderBy []string) ([]*base.OrderBy, error) {
	if len(orderBy) == 0 {
		return nil, nil
	}

	// "column asc" ====>&base.OrderBy{
	//			Column: split[0],
	//			Desc:   split[1] == "desc",
	//		}
	var orders []*base.OrderBy
	for _, v := range orderBy {
		split := strings.Split(v, " ")
		if len(split) != 2 {
			return nil, xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
		}
		orders = append(orders, &base.OrderBy{
			Column: split[0],
			Desc:   split[1] == "desc",
		})
	}
	return orders, nil
}

// AsyncUpdateAIChatAutoLabel ... 异步更新用户 “AI对话” 标签
func AsyncUpdateAIChatAutoLabel(ctx context.Context, userId, assistantId, UpdateBy uint64) {
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		_, err := client.TagClient.AutoBindingAITag(ctx, &tagpb.ReqAutoBindingAITag{
			UserId: UpdateBy,
			DataInfo: []*tagpb.DataInfo{
				{
					DataId:   userId,
					DataType: base.DataType_DATA_TYPE_USER,
				},
			},
			LabelInfo: &tagpb.LabelInfo{
				LabelType:   tagpb.AutoLabelType_AUTO_LABEL_TYPE_AI_CHAT,
				TeamId:      0,
				AssistantId: assistantId,
			},
		})
		if err != nil {
			log.WithContext(ctx).Errorw("AsyncUpdateAutoLabel ERROR", "err", err)
		}
		return nil
	}, boot.TraceGo(ctx))
}
