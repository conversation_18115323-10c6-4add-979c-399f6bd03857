package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
)

// FetchMgmtUserName 通过ids获取运营端用户名
func FetchMgmtUserName(ctx context.Context, userIDs []uint64) (map[uint64]string, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	userNames := make(map[uint64]string, len(userIDs))

	users, err := client.MgmtClient.GetUsers(ctx, &mgmtpb.ReqGetUsers{
		Id: userIDs,
	})
	if err != nil {
		return nil, err
	}

	if len(users.UserSet) == 0 {
		return userNames, nil
	}

	for _, user := range users.UserSet {
		userNames[user.Id] = user.Username
	}

	return userNames, nil
}
