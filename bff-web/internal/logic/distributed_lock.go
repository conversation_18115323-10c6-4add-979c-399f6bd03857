package logic

import (
	"sync"

	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	redsync "github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
)

var dlockOnce sync.Once
var redSyncPool *redsync.Redsync

// NewDistributedLock 新建一个锁实例
func NewDistributedLock(name string, opts ...redsync.Option) *redsync.Mutex {
	dlockOnce.Do(func() {
		if redSyncPool == nil {
			redSyncPool = redsync.New(goredis.NewPool(xredis.Default.Client()))
		}
	})
	return redSyncPool.NewMutex(name, opts...)
}
