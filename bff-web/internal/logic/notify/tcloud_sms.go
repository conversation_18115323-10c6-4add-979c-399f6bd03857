package notify

import (
	"context"
	"encoding/json"

	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp/response"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	notifypb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify"
)

type tcloudSmsCallbackResponse struct {
	Result int    `json:"result"`
	ErrMsg string `json:"errmsg"`
}

// TcloudSmsStatus 腾讯云短信状态
type TcloudSmsStatus struct {
	UserReceiveTime string `json:"user_receive_time"`
	Nationcode      string `json:"nationcode"`
	Mobile          string `json:"mobile"`
	ReportStatus    string `json:"report_status"`
	Errmsg          string `json:"errmsg"`
	Description     string `json:"description"`
	Sid             string `json:"sid"`
	Ext             string `json:"ext"`
}

// ToPb 协议转换
func (s *TcloudSmsStatus) ToPb() *notifypb.TcloudSmsStatus {
	return &notifypb.TcloudSmsStatus{
		UserReceiveTime: s.UserReceiveTime,
		Nationcode:      s.Nationcode,
		Mobile:          s.Mobile,
		ReportStatus:    s.ReportStatus,
		Errmsg:          s.Errmsg,
		Description:     s.Description,
		Sid:             s.Sid,
		Ext:             s.Ext,
	}
}

// TcloudSmsStatuses 腾讯云短信状态列表
type TcloudSmsStatuses []*TcloudSmsStatus

// ToPb 协议转换
func (s TcloudSmsStatuses) ToPb() []*notifypb.TcloudSmsStatus {
	pb := make([]*notifypb.TcloudSmsStatus, 0, len(s))
	for _, item := range s {
		pb = append(pb, item.ToPb())
	}
	return pb
}

// TcloudSmsCallbackLogic 腾讯云短信回调逻辑
type TcloudSmsCallbackLogic struct {
}

// ParseStatuses 解析状态数据
func (l *TcloudSmsCallbackLogic) ParseStatuses(req *xhttp.Request) (TcloudSmsStatuses, error) {
	body, err := req.ReadBody()
	if err != nil {
		return nil, err
	}

	statuses := make(TcloudSmsStatuses, 0)
	if err = json.Unmarshal(body, &statuses); err != nil {
		return nil, err
	}

	return statuses, nil
}

// UpdateStatuses 更新状态数据
func (l *TcloudSmsCallbackLogic) UpdateStatuses(ctx context.Context, statuses TcloudSmsStatuses) error {
	_, err := client.NotifyClient.UpdateTcloudSmsStatus(ctx, &notifypb.ReqUpdateTcloudSmsStatus{
		Statuses: statuses.ToPb(),
	})
	if err != nil {
		return err
	}
	return nil
}

// ResponseTcloudSmsCallbackError 响应错误
func (l *TcloudSmsCallbackLogic) ResponseTcloudSmsCallbackError(result int, msg string) xhttp.Response {
	return response.JSON(&tcloudSmsCallbackResponse{
		Result: result,
		ErrMsg: msg,
	})
}

// ResponseTcloudSmsCallbackOK 响应OK
func (l *TcloudSmsCallbackLogic) ResponseTcloudSmsCallbackOK() xhttp.Response {
	return response.JSON(&tcloudSmsCallbackResponse{
		Result: 0,
		ErrMsg: "",
	})
}
