package notify

import (
	"context"
	"encoding/json"

	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	notifypb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify"
)

var tcloudSesEvents = map[string]bool{
	"deferred":  true,
	"delivered": true,
	"dropped":   true,
	"bounce":    true,
}

// TcloudSesEvent 腾讯云邮件事件
type TcloudSesEvent struct {
	Event      string `json:"event"`
	Email      string `json:"email"`
	Link       string `json:"link"`
	BulkId     string `json:"bulkId"`
	Timestamp  int32  `json:"timestamp"`
	Reason     string `json:"reason"`
	BounceType string `json:"bounceType"`
	Username   string `json:"username"`
	From       string `json:"from"`
	FromDomain string `json:"fromDomain"`
	TemplateId int32  `json:"templateId"`
	Useragent  string `json:"useragent"`
}

// ToPb 协议转换
func (s *TcloudSesEvent) ToPb() *notifypb.TcloudSesEvent {
	return &notifypb.TcloudSesEvent{
		Event:      s.Event,
		Email:      s.Email,
		Link:       s.Link,
		BulkId:     s.BulkId,
		Timestamp:  s.Timestamp,
		Reason:     s.Reason,
		BounceType: s.BounceType,
		Username:   s.Username,
		From:       s.From,
		FromDomain: s.FromDomain,
		TemplateId: s.TemplateId,
		Useragent:  s.Useragent,
	}
}

// TcloudSesCallbackLogic 腾讯云邮件回调逻辑
type TcloudSesCallbackLogic struct {
}

// ParseEvent 解析事件数据
func (l *TcloudSesCallbackLogic) ParseEvent(req *xhttp.Request) (*TcloudSesEvent, error) {
	body, err := req.ReadBody()
	if err != nil {
		return nil, err
	}

	event := &TcloudSesEvent{}
	if err = json.Unmarshal(body, event); err != nil {
		return nil, err
	}

	return event, nil
}

// UpdateEvent 更新状态数据
func (l *TcloudSesCallbackLogic) UpdateEvent(ctx context.Context, event *TcloudSesEvent) error {
	if !tcloudSesEvents[event.Event] {
		return nil
	}
	_, err := client.NotifyClient.UpdateTcloudSesEvent(ctx, &notifypb.ReqUpdateTcloudSesEvent{
		Event: event.ToPb(),
	})
	if err != nil {
		return err
	}
	return nil
}
