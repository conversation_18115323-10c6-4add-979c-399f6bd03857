package ai

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// MatchQACollection ...
func MatchQACollection(ctx context.Context, req *aipb.ReqSearchCollectionOneShot) (*aipb.RspSearchCollection, aipb.DocMatchPattern, error) {
	if len(req.AssistantId) == 0 {
		return nil, aipb.DocMatchPattern_DOC_MATCH_PATTERN_UNSPECIFIED, nil
	}
	start := time.Now()
	qa, err := client.AiNational.DescribeMessageMatchQa(ctx, &aipb.ReqDescribeMessageMatchQa{
		Text:        req.Search,
		AssistantId: req.AssistantId[0],
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
	})
	if err != nil {
		return nil, aipb.DocMatchPattern_DOC_MATCH_PATTERN_UNSPECIFIED, err
	}
	if qa == nil || len(qa.Docs) == 0 {
		return nil, aipb.DocMatchPattern_DOC_MATCH_PATTERN_UNSPECIFIED, nil
	}

	rsp := &aipb.RspSearchCollection{
		TotalCount: qa.TotalCount,
		StartTime:  timestamppb.New(start),
		EndTime:    timestamppb.Now(),
		Items:      make([]*aipb.SearchCollectionItem, len(qa.Docs)),
	}
	for i, v := range qa.Docs {
		rsp.Items[i] = &aipb.SearchCollectionItem{
			Text:     v.Text,
			Question: v.IndexText,
			//RefName:     "",
			//RefUrl:      "",
			Contributor: v.Contributor,
			UpdateBy:    v.UpdateBy,
			//Id:          "",
			DocName:    v.FileName,
			DocType:    aipb.DocType_DOCTYPE_QA,
			DocId:      v.Id,
			DataSource: v.DataSource,
		}
	}
	return rsp, qa.MatchPattern, nil
}
