package ai

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tql"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
)

// ParseTQL 解析TQL为表达式
func ParseTQL(text string) (tql.Expression, error) {
	expr, err := tql.Parse(text)
	if err != nil {
		return nil, xerrors.NewCode(errorspb.BaseError_TqlGrammarError).WithLocalization(&xerrors.Localization{
			TemplateData: map[string]string{
				"ErrorMsg": err.Error(),
			},
		})
	}
	return expr, nil
}
