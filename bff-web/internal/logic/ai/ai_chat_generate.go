package ai

import (
	"context"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mclient "github.com/asim/go-micro/v3/client"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type ChatGenerateLogic struct {
	User          *iampb.UserInfo
	Assistant     *aipb.AssistantDetail
	question      *aipb.ChatMessage
	HashId        string
	Duration      int32
	CombineChunks bool
}

func CreateChatGenerateLogic(user *iamp<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON>, assistant *aipb.AssistantDetail, question *aipb.ChatMessage, reqBody *bffaipb.ReqChatSubscribe) *ChatGenerateLogic {
	return &ChatGenerateLogic{
		User:          user,
		Assistant:     assistant,
		question:      question,
		HashId:        reqBody.HashId,
		Duration:      reqBody.Duration,
		CombineChunks: reqBody.CombineChunks,
	}
}

func DescribeChatQuestion(ctx context.Context, user *iampb.UserInfo, questionId uint64) (*aipb.ChatMessage, error) {
	response, err := client.AiByUser(user).DescribeMessage(ctx, &aipb.ReqDescribeMessage{
		MessageId: questionId,
	})
	if err != nil {
		return nil, fmt.Errorf("[ChatGenerateLogic] DescribeMessage err: %s", err.Error())
	}
	return response.Message, nil
}

// PublishEmptySuggestions 发布空建议问题
func (c *ChatGenerateLogic) PublishEmptySuggestions(ctx context.Context, answer *aipb.ChatMessage) error {
	var suggestEventMsg = &aipb.EventChatMessage{
		ChatId:           answer.ChatId,
		QuestionId:       answer.QuestionId,
		SuggestQuestions: []string{},
		State:            aipb.ChatMessageState_CHAT_MESSAGE_STATE_SUGGESTION,
		AnswerIndex:      answer.AnswerIndex,
	}
	log.WithContext(ctx).Infow("[ChatGenerateLogic] PublishEmptySuggestions response", "suggestEventMsg", suggestEventMsg)
	return PublishChatEventHashMessage(ctx, suggestEventMsg, answer.CreateBy, 0, answer.PublishHashId)
}

// PublishAIMessageSuggestions 创建并推送建议问题
func (c *ChatGenerateLogic) PublishAIMessageSuggestions(ctx context.Context, assistant *aipb.AssistantDetail, answer *aipb.ChatMessage, questionText string) error {
	log.WithContext(ctx).Infow("[ChatGenerateLogic] PublishAIMessageSuggestions request", "questionText", questionText, "answer", answer)
	suggestions := c.getMessageSuggestions(ctx, assistant, answer, questionText)
	var suggestEventMsg = &aipb.EventChatMessage{
		ChatId:           answer.ChatId,
		QuestionId:       answer.QuestionId,
		SuggestQuestions: suggestions,
		State:            aipb.ChatMessageState_CHAT_MESSAGE_STATE_SUGGESTION,
		AnswerIndex:      answer.AnswerIndex,
	}
	log.WithContext(ctx).Infow("[ChatGenerateLogic] PublishAIMessageSuggestions response", "suggestEventMsg", suggestEventMsg)
	return PublishChatEventHashMessage(ctx, suggestEventMsg, answer.CreateBy, 0, answer.PublishHashId)
}

// GetQaMatchAnswers 获取qa匹配的回答
func (c *ChatGenerateLogic) GetQaMatchAnswers(ctx context.Context, question *aipb.ChatMessage, assistant *aipb.AssistantDetail, user *iampb.UserInfo, task *aipb.ChatAgentTask, startTime time.Time) (answer *aipb.ChatMessage) {
	var (
		currentTask        *aipb.ChatAgentTask
		targetMatchPattern aipb.DocMatchPattern
		matchRsp           *aipb.RspCreateQaMatchMessage
		err                error
	)

	if task != nil {
		currentTask = task
		targetMatchPattern = task.MatchPattern
		log.WithContext(ctx).Infow("[ChatGenerateLogic] GetQaMatchAnswers currentTask", "currentTask", currentTask, "targetMatchPattern", targetMatchPattern, "hashId", question.PublishHashId)
	}

	qa, err := client.AiNational.DescribeMessageMatchQa(ctx, &aipb.ReqDescribeMessageMatchQa{
		Text:         question.Text,
		AssistantId:  assistant.Id,
		MatchPattern: targetMatchPattern,
	})
	if err == nil && qa != nil && qa.Docs != nil && len(qa.Docs) > 0 { // 命中创建qa匹配模式回答
		endTime := time.Now()
		collection := c.GetCollectionSnapshot(ctx, qa.Docs, startTime, endTime)
		req := &aipb.ReqCreateQaMatchMessage{
			Question:           question,
			Docs:               qa.Docs,
			MatchPattern:       qa.MatchPattern,
			StartTime:          timestamppb.New(startTime),
			EndTime:            timestamppb.New(endTime),
			CollectionSnapshot: collection,
		}

		if currentTask != nil && currentTask.FetchType == aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_MATCH_QA {
			req.Task = &aipb.ChatMessageTask{
				PipelineId: currentTask.PipelineId,
				TaskId:     currentTask.Id,
				State:      aipb.PipelineTaskState_CHAT_AGENT_TASK_STATE_FINISHED,
			}
		}
		matchRsp, err = client.AiByUser(c.User).CreateQaMatchMessage(ctx, req)
		if err == nil && matchRsp != nil && matchRsp.Message != nil {
			time.Sleep(1 * time.Second)
			log.WithContext(ctx).Infow("[ChatGenerateLogic] GetQaMatchAnswers CreateQaMatchMessage success", "answer", matchRsp.Message)
			answer = matchRsp.Message
			return
		}
	}

	// 如果有当前任务，并且是匹配问答类，且有默认文本，没有匹配到，则直接返回默认文本
	if currentTask != nil && currentTask.FetchType == aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_MATCH_QA && currentTask.DefaultText != "" {
		l := MessageDbLogic{}
		answer, err = l.CreateSimpleTextAnswer(ctx, question, user, currentTask.DefaultText, aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER)
		if err != nil {
			log.WithContext(ctx).Errorf("[ChatGenerateLogic] GetQaMatchAnswers CreateSimpleTextAnswer err: %s", err.Error())
		} else {
			time.Sleep(2 * time.Second)
			log.WithContext(ctx).Infow("[ChatGenerateLogic] GetQaMatchAnswers CreateSimpleTextAnswer success", "answer", answer)
			return
		}
	}
	return
}

// getChatAgentTasks 获取chat agent任务
func (c *ChatGenerateLogic) getChatAgentTasks(ctx context.Context, user *iampb.UserInfo, question *aipb.ChatMessage, assistant *aipb.AssistantDetail) []*aipb.ChatAgentTask {
	var tasks = make([]*aipb.ChatAgentTask, 0)
	// 查询是否有起始任务
	taskRsp, err := client.AiNational.DescribeChatAgentTask(ctx, &aipb.ReqDescribeChatAgentTask{
		AssistantId:     assistant.Id,
		Prompt:          question.Text,
		NoNextOrderTask: true,
	})
	if err == nil && taskRsp != nil && len(taskRsp.Tasks) > 0 {
		tasks = taskRsp.Tasks
		log.WithContext(ctx).Errorw("[ChatGenerateLogic] DescribeChatAgentTask first task", "tasks", tasks, "hashId", question.PublishHashId)
		return tasks
	}

	// 查询前置回答是否命中task
	task, err := client.AiByUser(user).DescribePreMessageTask(ctx, &aipb.ReqDescribePreMessageTask{
		ChatId:    question.ChatId,
		MessageId: question.Id,
	})
	if err != nil {
		return tasks
	}

	// 通过前置task查询是否有当前需要执行的task
	rsp1, err := client.AiNational.DescribeChatAgentTaskByPreTask(ctx, &aipb.ReqDescribeChatAgentTaskByPreTask{
		PreTaskId: task.TaskId,
	})
	if err == nil && rsp1.Tasks != nil && len(rsp1.Tasks) > 0 {
		tasks = rsp1.Tasks
		log.WithContext(ctx).Errorw("[ChatGenerateLogic] DescribeChatAgentTask pre task", "tasks", tasks, "hashId", question.PublishHashId)
	}
	return tasks
}

func createMatchQaSuggestion(ctx context.Context, matchAnswer *aipb.ChatMessage, suggestionConfig *aipb.AssistantAskSuggestionConfig, question *aipb.ChatMessage, assistant *aipb.AssistantDetail) {
	cl := ChatGenerateLogic{}
	if suggestionConfig != nil {
		assistant.SuggestionConfig = suggestionConfig
		if err := cl.PublishAIMessageSuggestions(ctx, assistant, matchAnswer, question.Text); err != nil {
			log.WithContext(ctx).Infow("[ChatGenerateLogic] GetQaMatchAnswers PublishAIMessageSuggestions err")
		}
	} else {
		if err := cl.PublishEmptySuggestions(ctx, matchAnswer); err != nil {
			log.WithContext(ctx).Infow("[ChatGenerateLogic] GetQaMatchAnswers PublishAIMessageSuggestions err")
		}
	}
}

// HandleAiMsg 处理AI消息
func (c *ChatGenerateLogic) HandleAiMsg(ctx context.Context, user *iampb.UserInfo, question *aipb.ChatMessage, assistant *aipb.AssistantDetail) ([]*aipb.ChatMessage, error) {
	startTime := time.Now()
	// 获取tasks
	tasks := c.getChatAgentTasks(ctx, user, question, assistant)
	log.WithContext(ctx).Infow("[ChatGenerateLogic] HandleAiMsg getChatAgentTasks", "tasks", tasks, "hashId", question.PublishHashId)

	// 查询是否有qa匹配的回答
	var matchQATaskIds = make(map[uint64]struct{})
	if len(tasks) == 0 {
		matchAnswer := c.GetQaMatchAnswers(ctx, question, assistant, user, nil, startTime)
		if matchAnswer != nil {
			log.WithContext(ctx).Infow("[ChatGenerateLogic] HandleAiMsg only match", "answers", matchAnswer, "hashId", question.PublishHashId)
			go createMatchQaSuggestion(ctx, matchAnswer, assistant.SuggestionConfig, question, assistant)
			return []*aipb.ChatMessage{matchAnswer}, nil
		}
		// 进行AI消息处理
		sendRsp, err := client.AiByUser(user).CreateChatTaskMessage(ctx, &aipb.ReqCreateChatTaskMessage{
			Message:         question,
			AssistantDetail: assistant,
		}, mclient.WithRequestTimeout(300*time.Second))
		if err != nil {
			return nil, err
		}
		log.WithContext(ctx).Infow("[ChatGenerateLogic] HandleAiMsg only ai", "answers", sendRsp.Messages, "hashId", question.PublishHashId)
		return sendRsp.Messages, nil
	}

	var answers []*aipb.ChatMessage
	var mainTask []*aipb.ChatAgentTask

	for _, task := range tasks {
		if task.PreTaskId == 0 {
			mainTask = append(mainTask, task)
		}
	}

	for i, task := range mainTask {
		if task.FetchType == aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_MATCH_QA || task.FetchType == aipb.PipelineTaskFetchType_PIPELINE_TASK_FETCH_TYPE_TEXT {
			matchAnswer := c.GetQaMatchAnswers(ctx, question, assistant, user, task, startTime)
			if matchAnswer != nil {
				matchQATaskIds[task.Id] = struct{}{}
				if len(mainTask) > 1 {
					matchAnswer.AnswerIndex = int32(i + 1)
				}
				go createMatchQaSuggestion(ctx, matchAnswer, task.SuggestionConfig, question, assistant)
				answers = append(answers, matchAnswer)
			}
		}
	}

	// 过滤是否有送去ai的task
	var aiTasks []*aipb.ChatAgentTask
	for _, task := range tasks {
		if _, ok := matchQATaskIds[task.Id]; ok {
			continue
		}
		task.StartAnswerIndex = int32(len(matchQATaskIds))
		aiTasks = append(aiTasks, task)
	}

	if len(aiTasks) == 0 {
		log.WithContext(ctx).Infow("[ChatGenerateLogic] HandleAiMsg getTasks only match", "answers", answers, "hashId", question.PublishHashId)
		return answers, nil
	}
	// 进行AI消息处理
	sendRsp, err := client.AiByUser(user).CreateChatTaskMessage(ctx, &aipb.ReqCreateChatTaskMessage{
		Message:         question,
		AssistantDetail: assistant,
		Tasks:           aiTasks,
	}, mclient.WithRequestTimeout(300*time.Second))
	if err != nil {
		return nil, err
	}
	answers = append(answers, sendRsp.Messages...)
	log.WithContext(ctx).Infow("[ChatGenerateLogic] HandleAiMsg getTasks match and ai", "answers", answers, "hashId", question.PublishHashId)
	return answers, nil
}

func (c *ChatGenerateLogic) SendChatMessageSync(ctx context.Context, question *aipb.ChatMessage) ([]*aipb.ChatMessage, error) {
	var answers []*aipb.ChatMessage
	var err error
	user := c.User
	assistant := c.Assistant

	customAn, err := CustomQuestionDeal(ctx, user, question, assistant, c.question.Lang)
	if customAn != nil {
		return []*aipb.ChatMessage{customAn}, err
	}
	answers, err = c.HandleAiMsg(ctx, user, question, assistant)
	if err != nil {
		return nil, err
	}

	for _, answer := range answers {
		// review answer
		// TODO 保存review 消息
		if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH {
			answer.RejectReason = ReviewChatMessage(ctx, answer)
		}

		// 推送hash过的msg
		var docs []*aipb.ChatMessageDoc
		var err error
		if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION && len(answer.DocNames) > 0 {
			docs, err = DescribeMessagesDocs(ctx, []*aipb.ChatMessage{answer}, true)
			if err != nil {
				return nil, err
			}
		}
		answer.State = aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND
		eventMsg := TransformAIMessageToEventMessage(answer, docs)
		err = PublishChatEventHashMessage(ctx, eventMsg, user.Id, question.Id, question.PublishHashId)
		if err != nil {
			return nil, err
		}

		//// 生成建议问题
		//if err = c.PublishAIMessageSuggestions(ctx, assistant, answer, question.Text); err != nil {
		//	log.WithContext(ctx).Errorf("PublishAIMessageSuggestions failed: %v", err)
		//}

		// save collection snapshot
		if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION {
			xsync.SafeGo(context.Background(), func(ctx context.Context) error {
				if err := SaveCollectionSnapshotSync(ctx, answer, assistant.CleanChunks); err != nil {
					log.WithContext(ctx).Errorw("SendChatMessageSync SaveCollectionSnapshotSync err", "err", err)
					return err
				}
				return nil
			}, boot.TraceGo(ctx))

		}
	}

	return answers, nil
}

func (c *ChatGenerateLogic) getMessageSuggestions(ctx context.Context, assistant *aipb.AssistantDetail, answer *aipb.ChatMessage, questionText string) []string {
	var suggestions []string
	var suggestConfig = assistant.SuggestionConfig
	if suggestConfig == nil {
		return []string{}
	}
	if suggestConfig.Count == 0 || suggestConfig.Prompt == "" || answer.Text == "" || questionText == "" {
		log.WithContext(ctx).Infow("[ChatGenerateLogic] PublishAIMessageSuggestions break", "suggestCount", suggestConfig.Count, "suggestPrompt", suggestConfig.Prompt, "answer type", answer.Type, "answerText", answer.Text, "questionText", questionText)
	} else {
		if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER || answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION || answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY || answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH {
			suggestRsp, err := client.AiByID(answer.Id).CreateMessageSuggestQuestion(ctx, &aipb.ReqCreateMessageSuggestQuestion{
				MessageId:       answer.Id,
				QuestionText:    questionText,
				AnswerText:      answer.Text,
				AssistantDetail: assistant,
				CreateBy:        answer.CreateBy,
			}, mclient.WithRequestTimeout(60*time.Second))
			if err != nil || len(suggestRsp.SuggestQuestions) == 0 {
				log.WithContext(ctx).Errorw("PublishAIMessageSuggestions info", "msg", answer, "rsp", suggestRsp, "err", err)
			} else {
				suggestions = suggestRsp.SuggestQuestions
			}
		}
	}
	return suggestions
}

//func (c *ChatGenerateLogic) ParseRequestImagesToChatURl(ctx context.Context, imageUrls []string) ([]string, []string) {
//	var images, parseFiles, texts []string
//	var parsedExt = []string{"txt", "epub", "docx", "doc", "pptx", "ppt", "xlsx", "xls", "csv", "mp3", "wav", "m4a", "wma", "amr", "aac", "flac", "mp4", "3gp", "flv"}
//
//	for _, imgUrl := range imageUrls {
//		ext := strings.ToLower(getFileExtension(imgUrl))
//		if ext == "pdf" { // 处理pdf
//			newImagePath, err := GenerateLongImageFromPDF(imgUrl)
//			if err != nil {
//				continue
//			}
//			images = append(images, newImagePath)
//		}
//		if ext == "png" || ext == "jpeg" || ext == "jpg" {
//			images = append(images, imgUrl)
//		}
//		if i := xslice.Contains(parsedExt, ext); i != -1 {
//			parseFiles = append(parseFiles, imgUrl)
//		}
//	}
//	if len(parseFiles) > 0 {
//		docRsp, err := client.AiNational.ParseChatDoc(ctx, &aipb.ReqParseChatDoc{FileUrls: parseFiles})
//		if err != nil {
//			return images, texts
//		}
//		for _, text := range docRsp.Text {
//			texts = append(texts, text)
//		}
//	}
//	return images, texts
//}

// GetCollectionSnapshot 获取doc 拼接的CollectionSnapshot
func (c *ChatGenerateLogic) GetCollectionSnapshot(ctx context.Context, docs []*aipb.ChatMessageDoc, start, end time.Time) *aipb.MessageCollectionSnapshot {
	var snapshot = &aipb.MessageCollectionSnapshot{}
	snapshot.StartTime = timestamppb.New(start)
	snapshot.EndTime = timestamppb.New(end)

	var contributors []*aipb.Contributor
	for _, doc := range docs {
		contributors = append(contributors, doc.Contributor...)
	}
	_, err := GeAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		log.WithContext(ctx).Errorw("GetCollectionSnapshot get contributors err", "err", err)
	}

	var collectionItems []*aipb.SearchCollectionItem
	for _, v := range docs {
		collectionItems = append(collectionItems, &aipb.SearchCollectionItem{
			Text:        v.Text,
			Question:    v.IndexText,
			FileName:    v.FileName,
			Url:         v.Url,
			Contributor: v.Contributor,
			DocType:     aipb.DocType(v.DataType),
			DataSource:  v.DataSource,
			DocId:       v.Id,
		})
	}

	snapshot.Items = collectionItems
	//if len(snapshot.Items) > 0 {
	//	result, err := json.Marshal(snapshot)
	//	if err != nil {
	//		log.WithContext(ctx).Errorf("GetCollectionSnapshot marshal collection items failed: %v", err)
	//	}
	//	return string(result)
	//}
	return snapshot
}
