package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"text/template"
	"time"
	"unicode"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai/aware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/asr"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/ugc"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	mclient "github.com/asim/go-micro/v3/client"
	"github.com/go-redsync/redsync/v4"
	"golang.org/x/exp/maps"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// WechatAIMessage 微信公众号ai消息处理逻辑
type WechatAIMessage struct {
	aware.MessageAIConfig[workweixin.Message]
	token, openKfId           string // 用户提问token;客服ID
	currentCursor, nextCursor string // 当前游标值；下一个游标值
	customerMap               map[string]workweixin.Customer

	assistant *aipb.AssistantDetail

	welcomeMsg *aipb.AssistantWelcomeMsg // 欢迎信息

	defaultAnswer []aware.MessageAnswerAware[workweixin.Message] // 预设问题
}

// ReceiveUserChatMessageSync 接收并异步处理微信消息
func ReceiveUserChatMessageSync(ctx context.Context, toUsername, openKfid, token string) error {
	lockName := config.GetStringOr("workWeixin.ai.getMessageLock", "ai:workWeinxin:getMessageLock")
	dmtx := logic.NewDistributedLock(lockName, redsync.WithExpiry(time.Minute), redsync.WithTries(10))
	err := dmtx.Lock()
	if err != nil {
		return nil // 未获取到锁直接跳过
	}
	defer dmtx.Unlock()

	// 拿到助手信息
	rsp, err := client.AiNational.GetAssistant(ctx, &aipb.ReqGetAssistant{
		AppCode: openKfid,
		CorpId:  toUsername,
	})
	if err != nil || rsp == nil || rsp.AssistantDetail.Id == 0 {
		log.WithContext(ctx).Errorw("dealWorkWeixinMessage GetAssistantByAppCode", "err", err, "appCode", openKfid)
		return err
	}

	// 获取当前企业微信服务号的消息记录游标
	currentCursor, cursorKey, err := getWorkWeixinMessageCursor(ctx, rsp.AssistantDetail)
	if err != nil {
		return err
	}
	// 拉取消息列表
	maxReadMessageSize := config.GetIntOr("workWeixin.ai.maxReadMessageSize", 200)
	readMessages, nextCursor, _, err := workweixin.BuildNewClient(workweixin.Client, toUsername,
		aware.LoadOpenWorkAccessToken).ReadMessages(currentCursor, token, maxReadMessageSize, 0, openKfid)
	if err != nil {
		log.WithContext(ctx).Errorw("GetWeChatMessage ReadMessages", "err", err, "cursor", currentCursor)
		return nil
	}
	log.WithContext(ctx).Debugw("ReadMessages cursor info", "cursorKey", cursorKey,
		"nextCursor", nextCursor, "currentCursor", currentCursor)
	// 只要拉取消息列表没有报错，就更新游标
	xredis.Default.SetEx(ctx, cursorKey, nextCursor, 24*time.Hour)

	var validMessage []workweixin.Message
	externalUserIDs := make(map[string]struct{})
	for _, message := range readMessages { // 过滤出有用的消息，其他的消息丢弃
		log.WithContext(ctx).Debugw("ReceiveUserChatMessageSync messages", "message", message)

		switch message.Origin {
		case workweixin.WechatMessageOriginUser: // 用户消息
			if message.MsgType != workweixin.MsgTypeText && message.MsgType != workweixin.MsgTypeImage &&
				message.MsgType != workweixin.MsgTypeFile && message.MsgType != workweixin.MsgTypeVoice {
				continue // 暂时只处理文本、文件、图片、语音
			}
			if message.ExternalUserID != "" {
				validMessage = append(validMessage, message)
				externalUserIDs[message.ExternalUserID] = struct{}{}
			}

		case workweixin.WechatMessageOriginEvent: // 系统事件
			if message.Event.ExternalUserID != "" {
				externalUserIDs[message.Event.ExternalUserID] = struct{}{}
			}
			validMessage = append(validMessage, message)

		case workweixin.WechatMessageOriginKf: // 人工客服消息
			if message.MsgType == workweixin.MsgTypeText && message.ExternalUserID != "" { // 只记录文本消息
				validMessage = append(validMessage, message)
				externalUserIDs[message.ExternalUserID] = struct{}{}
			}
		}
	}

	if len(validMessage) == 0 {
		log.WithContext(ctx).Debugw("ReceiveUserChatMessageSync with no message to deal")
		return nil
	}

	var customers []workweixin.Customer
	if len(externalUserIDs) > 0 {
		customersRsp, err2 := workweixin.BuildNewClient(workweixin.Client, toUsername,
			aware.LoadOpenWorkAccessToken).BatchGetCustomers(maps.Keys(externalUserIDs))
		if err2 != nil || customersRsp == nil || customersRsp.ErrCode != 0 {
			log.WithContext(ctx).Errorw("GetWeChatMessage BatchGetCustomers", "err", err2,
				"externalUserIDs", externalUserIDs, "customersRsp", customersRsp)
			return nil
		}
		log.WithContext(ctx).Debugw("ReceiveUserChatMessageSync BatchGetCustomers", "customersRsp", customersRsp)
		customers = customersRsp.CustomerList
	}

	xsync.SafeGo(context.Background(), func(c context.Context) error {
		return DealWorkWeixinMessage(c, validMessage,
			token, currentCursor, nextCursor, rsp.AssistantDetail, customers)
	}, boot.TraceGo(ctx))

	return nil
}

// getWorkWeixinMessageCursor 获取企业微信消息记录游标
func getWorkWeixinMessageCursor(ctx context.Context, assistant *aipb.AssistantDetail) (string, string, error) {
	cursorKey := config.GetStringOr("workWeixin.ai.nextCursorRedisKey", "workWeixin:ai:nextCursorRedisKey")
	if len(assistant.CorpId) > 0 { // 获取服务商的凭证
		cursorKey = fmt.Sprintf("%s:%s", cursorKey, assistant.CorpId)
	}

	currentCursor := xredis.Default.Get(ctx, cursorKey)
	if currentCursor != nil && currentCursor.Val() != "" {
		log.WithContext(ctx).Debugw("ReceiveUserChatMessageSync got",
			"currentCursor", currentCursor.Val(), "cursorKey", cursorKey)
		return currentCursor.Val(), cursorKey, nil
	}
	cursorWechat, err := client.AiNational.GetMessageCursorWechat(ctx, &aipb.ReqGetMessageCursorWechat{
		AssistantId: assistant.Id,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("GetWeChatMessage getWorkWeixinMessageCursor", "err", err)
		return "", "", err
	}
	return cursorWechat.Cursor, cursorKey, err
}

// DealWorkWeixinMessage 处理企业微信消息逻辑
// 1 获取助手信息
// 2 校验微信消息是否重复
// 3 读取白名单配置，白名单校验
// 4 将message按照chatId组合为map，并创建新用户的chat会话
// 5 按照用户纬度分协程处理messageDeal.messageDeal
func DealWorkWeixinMessage(ctx context.Context, readMessages []workweixin.Message,
	token, currentCursor, nextCursor string, assistant *aipb.AssistantDetail, customers []workweixin.Customer) error {

	// 校验微信消息是否重复
	validMessage := make(map[string]workweixin.Message)
	for _, message := range readMessages {
		if len(message.MsgID) > 0 { // 只校验文本信息（有msgID）
			validMessage[message.MsgID] = message
		}
	}

	if !assistant.Enabled {
		sendAssistantDisableMessage(ctx, maps.Values(validMessage), assistant.CorpId)
		return nil
	}

	customerMap := make(map[string]workweixin.Customer)
	for _, customer := range customers {
		customerMap[customer.ExternalUserID] = customer
	}

	// 读取白名单配置，白名单校验
	if assistant.NoPermissionMsg != nil && assistant.NoPermissionMsg.Enable {
		checkWechatWhiteList(ctx, assistant.Id, validMessage, customerMap,
			assistant.NoPermissionMsg.Msg, assistant.CorpId)
	}

	if len(validMessage) == 0 {
		return nil
	}

	aware.DoMessageLogic[workweixin.Message](ctx, &WechatAIMessage{
		token:         token,
		currentCursor: currentCursor,
		nextCursor:    nextCursor,
		assistant:     assistant,
		customerMap:   customerMap,
	}, maps.Values(validMessage))
	return nil
}

// checkWechatWhiteList 校验白名单逻辑
func checkWechatWhiteList(ctx context.Context, assistantId uint64, validMessage map[string]workweixin.Message,
	customerMap map[string]workweixin.Customer, noPermissionAnswer, corpId string) {

	whiteList, err := client.AiNational.GetWhiteListWechat(ctx, &aipb.ReqGetWhiteListWechat{
		AssistantId: assistantId,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("checkWechatWhiteList error", "assistantId", assistantId, "err", err)
		return
	}
	if whiteList == nil || len(whiteList.Username) == 0 {
		log.WithContext(ctx).Errorw("checkWechatWhiteList dont have any one", "assistantId", assistantId)
		return
	}

	for key, message := range validMessage {
		if message.Origin == workweixin.WechatMessageOriginKf { // 客服发送的消息不校验白名单
			continue
		}
		if v, ok := customerMap[message.ExternalUserID]; ok {
			if !xslice.IsContainsString(whiteList.Username, v.Nickname) { // 用户昵称不在白名单中，则直接发送无权限信息
				sendWeixinTextMessageWithoutChat(ctx, message, noPermissionAnswer, corpId)
				delete(validMessage, key)
			}
		}
	}
}

func divideWorkWeixinMessage(ctx context.Context, validMessage []workweixin.Message, assistantId uint64,
	customerMap map[string]workweixin.Customer, existChatMap map[string]*aipb.ChatWeChat,
	liveAgentMap map[string]string) (map[*aipb.ChatWeChat][]workweixin.Message, []*aipb.ChatWeChat) {

	var welcomeMessage []workweixin.Message // 欢迎信息
	var newChats []*aipb.ChatWeChat         // 需要重新创建的会话
	// var newChatMessage []workweixin.Message // 需要重新创建会话的message
	messageMap := make(map[*aipb.ChatWeChat][]workweixin.Message)

	if existChatMap == nil || len(existChatMap) == 0 {
		existChatMap = make(map[string]*aipb.ChatWeChat)
	}

	for _, message := range validMessage {
		switch message.MsgType {
		case workweixin.MsgTypeEvent: // 事件消息
			switch message.Event.EventType {
			case workweixin.MsgEventEnterSession: // 用户进入会话
				if len(message.Event.WelcomeCode) > 0 {
					welcomeMessage = append(welcomeMessage, message) // 有欢迎code的需要发送欢迎信息
					// 用户第一次或者重再次进入对话页面，仅关闭上一次会话 用户需要通过再次提问创建新会话
					aware.FinishWeixinChat(ctx, 0, message.Event.ExternalUserID, assistantId, aipb.ChatType_CHAT_TYPE_WECHAT)
				}
			case workweixin.MsgEventSessionStatusChange: // 会话状态变更
				sessionStatusChange(ctx, existChatMap[message.Event.ExternalUserID], message.Event)
			case workweixin.MsgEventServicerStatusChange: // 接待人员接待状态变更事件
				liveAgentStatusChange(ctx, assistantId, message.Event)
			case workweixin.MsgEventMsgSendFail: // 消息发送失败回调
				messageSendFailEvent(ctx, message.Event)
			case workweixin.MsgEventUserRecallMsg: // 用户撤回消息
				messageRecallMsgEvent(ctx, message.Event)
			}

		case workweixin.MsgTypeText, workweixin.MsgTypeFile, workweixin.MsgTypeImage, workweixin.MsgTypeVoice: // 文本消息
			customer, exist := customerMap[message.ExternalUserID]
			if !exist {
				continue
			}
			chat, ok2 := existChatMap[customer.ExternalUserID]
			if !ok2 || chat.End { // 对于第一次对话或者上一次对话已经过期的用户重新创建会话
				// if message.Origin != workweixin.WechatMessageOriginKf { // 对于客服消息又找不到会话的消息不做处理
				newChat := &aipb.ChatWeChat{
					Title:          getChatTitleName(message.MsgType, message.Text.Content),
					Nickname:       customer.Nickname,
					AppId:          customer.UnionID,
					ExternalUserId: customer.ExternalUserID,
					SupportType:    aipb.ChatSupportType_CHAT_SUPPORT_TYPE_AI,
					ChatType:       aipb.ChatType_CHAT_TYPE_WECHAT,
				}
				if chat != nil { // 之前会话的信息要转移至新会话上
					newChat.DefaultAnswerRecord = chat.DefaultAnswerRecord
				}
				if name, ok3 := liveAgentMap[customer.ExternalUserID]; ok3 { // 之前为人工坐席的会话要保持其人工坐席的状态信息
					newChat.LiveAgentName = name
					newChat.SupportType = aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT
				}
				newChats = append(newChats, newChat)
				existChatMap[customer.ExternalUserID] = newChat
				messageMap[newChat] = []workweixin.Message{message}
				continue
				// }
			}

			if v, ok3 := messageMap[chat]; ok3 {
				messageMap[chat] = append(v, message)
			} else {
				messageMap[chat] = []workweixin.Message{message}
			}
		}
	}

	if len(welcomeMessage) > 0 { // 欢迎信息特殊处理，默认chatId = 0
		messageMap[&aipb.ChatWeChat{}] = welcomeMessage
	}

	return messageMap, newChats
}

func getChatTitleName(msgType, text string) string {
	if text != "" {
		return ugc.Limit(text, 100)
	}

	switch msgType {
	case workweixin.MsgTypeFile:
		return "[文件]"
	case workweixin.MsgTypeImage:
		return "[图片]"
	case workweixin.MsgTypeVoice:
		return "[语音]"
	default:
		return "[无标题]"
	}
}

// upsertWeixinChat 创建或者更新微信会话
func upsertWeixinChat(ctx context.Context, newChats []*aipb.ChatWeChat,
	assistantId uint64) (map[string]*aipb.ChatWeChat, error) {

	creatRsp, err1 := client.AiNational.CreateChatWechat(ctx, &aipb.ReqCreateChatWechat{
		Chats:       newChats,
		AssistantId: assistantId,
	})
	if err1 != nil || creatRsp == nil {
		log.WithContext(ctx).Errorw("dealWorkWeixinMessage CreateChatWechat", "err", err1, "creatRsp", creatRsp)
		return nil, xerrors.BadRequestError(err1)
	}

	m := make(map[string]*aipb.ChatWeChat)
	for _, chat := range newChats {
		if v, ok := creatRsp.ChatMap[chat.ExternalUserId]; ok {
			chat.Id = v
			chat.AssistantId = assistantId
			m[chat.ExternalUserId] = chat
		}
	}

	return m, nil
}

// sessionStatusChange 处理会话状态变更（目前只处理客服主动退出会话的情况）
func sessionStatusChange(ctx context.Context, chat *aipb.ChatWeChat, event workweixin.EventMessage) {
	if chat == nil || chat.Id == 0 {
		return
	}
	log.WithContext(ctx).Debugw("WechatAIMessage sessionStatusChange", "chatId", chat.Id, "event", event)

	var err error
	req := &aipb.ReqSwitchChatLiveAgent{
		ChatId:      chat.Id,
		SupportType: aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT,
	}
	switch event.ChangeType {
	case 1, 2, 4: // 1-从接待池接入会话 // 2-转接会话 // 4-重新接入已结束/已转接会话
		req.LiveAgentName = event.NewServicerUserid
	case 3: // 3-结束会话（清空会话的人工坐席信息）
		req.SupportType = aipb.ChatSupportType_CHAT_SUPPORT_TYPE_AI
	default:
		log.WithContext(ctx).Error("SessionStatusChange Error changeType is zero")
		return
	}
	_, err = client.AiNational.SwitchChatLiveAgent(ctx, req)
	if err != nil {
		log.WithContext(ctx).Errorw("WechatAIMessage sessionStatusChange err",
			"chatId", chat.Id, "err", err, "changeType", event.ChangeType)
	}
}

// liveAgentStatusChange 人工坐席的状态变更事件
func liveAgentStatusChange(ctx context.Context, assistantId uint64, event workweixin.EventMessage) {
	var status aipb.ChatLiveAgentStatus

	switch event.Status {
	case 1: // 1-接待中
		status = aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_CURRENTLY_SERVING
	case 2: // 2-停止接待
		if event.StopType == 0 { // 0:停止接待
			status = aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_STOPPED_SERVING
		} else if event.StopType == 1 { // 1:暂时挂起
			status = aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_STOPPED_TEMPORARILY_SUSPENDED
		}
	default:
		log.WithContext(ctx).Errorw("WechatAIMessage liveAgentStatusChange error status",
			"assistantId", assistantId, "status", event.Status, "change", event)
		return
	}

	if status == aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_UNSPECIFIED {
		log.WithContext(ctx).Errorw("WechatAIMessage liveAgentStatusChange error status",
			"assistantId", assistantId, "status", event.Status, "change", event)
		return
	}

	_, err := client.AiNational.LiveAgentStatusChange(ctx, &aipb.ReqLiveAgentStatusChange{
		AssistantId: assistantId,
		UserName:    event.ServicerUserid,
		Status:      status,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("WechatAIMessage liveAgentStatusChange err",
			"chatId", assistantId, "err", err, "change", event)
	}
}

func messageSendFailEvent(ctx context.Context, event workweixin.EventMessage) {
	var state int32 = 3      // 发送异常
	if event.FailType == 6 { // 6-超过5条限制
		state = 1 // 如果超过限制了，则标记为待发送
	}
	aware.WechatSendTimeWait() // 有时候回调事件非常的快，本地服务的数据都没入库，为了避免更新的时候数据还没有插入的情况，此处短暂延迟
	log.WithContext(ctx).Debugw("WechatAIMessage messageSendFailEvent", "event", event)
	_, err := client.AiNational.UpdateSendRecord(ctx, &aipb.ReqUpdateSendRecord{
		Records: []*aipb.AiSendResultRecord{
			{
				Uuid:  event.FailMsgId,
				Info:  fmt.Sprintf("{\"fail_type\":%d}", event.FailType),
				State: state, // 3 发送异常
			},
		},
	})
	if err != nil {
		log.WithContext(ctx).Errorw("afterSend UpdateSendRecord", "err", err)
	}
}

func messageRecallMsgEvent(ctx context.Context, event workweixin.EventMessage) {
	log.WithContext(ctx).Debugw("WechatAIMessage messageRecallMsgEvent", "event", event)
	_, err := client.AiNational.UpdateSendRecord(ctx, &aipb.ReqUpdateSendRecord{
		UserRecallMsgUuid: event.RecallMsgid,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("afterSend messageRecallMsgEvent", "err", err)
	}
}

// GetConf ...
func (w *WechatAIMessage) GetConf() aware.MessageAIConfig[workweixin.Message] {
	return w.MessageAIConfig
}

// Init ...
func (w *WechatAIMessage) Init() {
	w.MessageAIConfig = aware.MessageAIConfig[workweixin.Message]{
		Ctx:           w.Ctx,
		MaxRetryTimes: config.GetIntOr("workweixin.ai.questionMaxRetryTimes", 7),
		RetryInterval: time.Duration(config.GetIntOr("workweixin.ai.questionRetryInterval", 6)) * time.Second,
		TimeoutDefaultAnswer: config.GetStringOr("workweixin.ai.timeoutDefaultAnswer",
			"抱歉，我走神儿了，可以尝试重新提问。或者通过发送邮件至****************联系人工服务"),
		AuditErrorAnswer: config.GetStringOr("workweixin.ai.auditErrorAnswer",
			"对话中可能含有敏感信息, 作为一个AI助手, 我需要遵守相关规定, 无法回答您的问题。可以换个问题考考我。"),
		HelpCenterUrl:     config.GetString("workweixin.ai.helpCenterUrl"),
		CosPublicCdnUrl:   config.GetString("oss.publicCdn"),
		UgcJumpUrl:        config.GetString("workweixin.ai.ugcJumpUrl"),
		UgcAnswerTemplate: config.GetString("workweixin.ai.ugcAnswerTemplate"),
		ReadMore:          config.GetStringOr("workweixin.ai.readMore", "查看更多"),
		AnswerFrom:        config.GetStringOr("workweixin.ai.answerFrom", "\n\n答案整理自 "),

		ReferenceAnswerTemplate: config.GetString("workweixin.ai.referenceAnswerTemplate"),
		ReferenceDataFrom:       config.GetStringOr("workweixin.ai.referenceDataFrom", "\n参考资料\n"),
	}
	w.welcomeMsg = w.assistant.WelcomeMsg
	if w.welcomeMsg == nil || len(w.assistant.WelcomeMsg.HeadMsg) == 0 {
		log.WithContext(w.Ctx).Errorw("NewWechatAIMessage Unmarshal error", "assistant", w.assistant)
		w.welcomeMsg = &aipb.AssistantWelcomeMsg{
			HeadMsg: config.GetStringOr("workweixin.ai.welcomeTemplate", "Hi我是你的专属AI伙伴Tan~\n你可以试着问我碳中和相关的问题：\n"),
		}
	}
	w.defaultAnswer = []aware.MessageAnswerAware[workweixin.Message]{
		&aware.WechatMessageSetAnswer{ // 触发 继续回答 精确关键字
			MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
				ExactKeywords: config.GetStringSliceOr("workweixin.ai.continueAnswerKeywords", []string{aware.ContinueAnswerStr})},
			AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_CONTINUE,
			Model:   aware.AiProcessingModelContinue,
		},
	}
	w.GetWechatDefaultCmdAnswer().setDefaultAnswer().setFileDefaultAnswer()
}

// SetAssistant ...
func (w *WechatAIMessage) SetAssistant(a *aipb.AssistantDetail) *WechatAIMessage {
	w.assistant = a
	return w
}

// Do ...
func (w *WechatAIMessage) Do(chat *aipb.ChatWeChat, message workweixin.Message) (aware.DoResultRecord,
	aware.MessageAnswerAware[workweixin.Message]) {
	chat.CorpId = w.assistant.CorpId
	// 设置预设答案，审核问题
	question, defaultAnswer := w.messagePreprocessing(chat, message)
	if question == nil {
		return aware.DoResultRecord{}, nil
	}
	if defaultAnswer == nil {
		defaultAnswer = aware.GetWechatEmptyAnswer()
	}

	answerRsp, sender := w.getAiAnswer(question, chat, message, defaultAnswer)

	if answerRsp == nil || sender == nil { // 如果sender = nil 则后续逻辑不用执行了
		return aware.DoResultRecord{}, nil
	}
	resultRecord := aware.DoResultRecord{
		ChatId:          chat.Id,
		QuestionId:      answerRsp.QuestionId,
		FileQuestionIds: answerRsp.FileQuestionIds,
	}

	if len(answerRsp.AnswerSharding) > 0 { // 继续回答的答案分片数据不需要获取doc等逻辑
		resultRecord.AnswerSharding = answerRsp.AnswerSharding
		return resultRecord, sender
	}

	if answerRsp.QuestionType == aipb.RspGetAnswerWechat_QUESTION_TYPE_REPETITION {
		sender.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION
	}

	docs, err := DescribeMessagesDocs(w.Ctx, answerRsp.Answers, true) // 获取docs
	if err != nil {
		log.WithContext(w.Ctx).Errorw("WechatAIMessage DescribeMessagesDocs error", "chatId", chat.Id, "err", err)
	}

	for _, answer := range answerRsp.Answers {
		if answer == nil {
			continue
		}
		if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION {
			if err := SaveCollectionSnapshotSync(w.Ctx, answer, w.assistant.CleanChunks); err != nil {
				log.WithContext(w.Ctx).Errorw("WechatAIMessage SaveCollectionSnapshotSync error", "err", err)
			}
		}

		eventMessage := TransformAIMessageToEventMessage(answer, docs) // 获取docs的贡献者
		answerMessage := EventMessageTransformWithHash(eventMessage)   // 所有id转为hashID

		if defaultAnswer.GetAnswerString() == "" { // 预设回答为空时，审核回答
			reviewAnswerMessage(w.Ctx, answerMessage)
		}
		resultRecord.Records = append(resultRecord.Records, &aware.DoResult{
			QuestionId: answer.QuestionId,
			Answer:     answerMessage,
			AnswerId:   answer.Id,
			Reason:     answer.RejectReason,
		})
	}

	return resultRecord, sender
}

func (w *WechatAIMessage) getAiAnswer(question *aipb.ChatMessage, chat *aipb.ChatWeChat,
	message workweixin.Message, defaultAnswer aware.MessageAnswerAware[workweixin.Message]) (
	*aipb.RspGetAnswerWechat, *aware.WechatMessageSetAnswer) {

	var answerRsp *aipb.RspGetAnswerWechat
	var err error
	sender, ok := defaultAnswer.(*aware.WechatMessageSetAnswer)
	sec := w.RetryInterval * time.Duration(w.MaxRetryTimes)
	req := &aipb.ReqGetAnswerWechat{
		Message:              question,
		SetChatAnswer:        defaultAnswer.GetAnswerString(),
		CurrentCursor:        w.currentCursor,
		NextCursor:           w.nextCursor,
		ThirdRecordUuid:      message.MsgID,
		ExternalUserId:       message.ExternalUserID,
		AssistantDetail:      w.assistant,
		TimeoutDefaultAnswer: w.TimeoutDefaultAnswer,
		RecordId:             sender.GetWechatRecordId(w.Ctx, message),
	}

	if ok && sender.Model == aware.AiProcessingModelFile { // 调用获取文件相关的接口
		temp := xredis.Default.Get(w.Ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, question.ChatId))
		if temp != nil && temp.Val() != "" {
			if v, ok1 := aipb.AIFileProcessing_value[temp.Val()]; ok1 {
				req.FileCmd = aipb.AIFileProcessing(v)
			}
		}
		if req.FileCmd == aipb.AIFileProcessing_AI_FILE_PROCESSING_FILE_WITHOUT_CMD { // 之前处于指令待定状态的现在需要补充处理文件的指令
			switch question.Text {
			case aware.DecipherReport, aware.ReadReport:
				req.FileCmd = aipb.AIFileProcessing_AI_FILE_PROCESSING_REPORT_WITH_FILE
			case aware.ReadBomPicture, aware.ReadBom, aware.DecipherBom:
				req.FileCmd = aipb.AIFileProcessing_AI_FILE_PROCESSING_BOM_WITH_FILE
			default:
				if len(question.Text) > 0 { // 用户没有继续发图片，发送的是其他的指令
					req.FileCmd = aipb.AIFileProcessing_AI_FILE_PROCESSING_FILL_WITT_USER_CMD
				}
			}
		}
		if sender.NoPass {
			req.Message.State = 3 // 如果sender被标记为默认处理，则标记状态为3
		}
		answerRsp, err = client.AiNational.GetAnswerWechatForFile(w.Ctx, req,
			mclient.WithRequestTimeout(sec))
	} else if ok && sender.Model == aware.AiProcessingModelContinue && req.RecordId > 0 { // 获取继续回答的答案
		answerRsp, err = client.AiNational.GetAnswerWechatForContinue(w.Ctx, req,
			mclient.WithRequestTimeout(sec))
	} else {                                                                      // 直接询问ai问题并获取答案
		if req.Message.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL { // 如果是用户的正常问答，则需要先匹配一次QA
			startTime := time.Now()
			if qa, _ := client.AiNational.DescribeMessageMatchQa(w.Ctx, &aipb.ReqDescribeMessageMatchQa{
				Text:        message.Text.Content,
				AssistantId: w.assistant.Id,
			}); qa != nil && len(qa.Docs) > 0 {
				req.Docs = qa.Docs
				req.SetChatAnswer = qa.Docs[0].Text
				for i := 1; i < len(qa.Docs); i++ {
					req.SetChatAnswer += "\n\n" + qa.Docs[i].Text
				}
				req.MatchPattern = qa.MatchPattern
				req.AnswerStartTime = timestamppb.New(startTime)
				req.Message.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_MATCH_PATTERN
				endTime := time.Now()
				req.AnswerEndTime = timestamppb.New(endTime)
				req.CollectionSnapshot = new(ChatGenerateLogic).GetCollectionSnapshot(w.Ctx, qa.Docs, startTime, endTime)
			}
		}
		answerRsp, err = client.AiNational.GetAnswerWechat(w.Ctx, req, mclient.WithRequestTimeout(sec))
	}

	if err != nil {
		log.WithContext(w.Ctx).Errorw("messageDeal GetAnswerWechat error",
			"chatId", chat.Id, "err", err, "answerRsp", answerRsp)
		return answerRsp, sender
	}

	switch answerRsp.QuestionType {
	case aipb.RspGetAnswerWechat_QUESTION_TYPE_ID_REPETITIVE: // 重复消息不做处理
		log.WithContext(w.Ctx).Infow("WechatAIMessage messageDeal get repetitive message",
			"chatId", chat.Id, "message", message)
		return nil, nil
	case aipb.RspGetAnswerWechat_QUESTION_TYPE_CLEAR_FILE_CACHE:
		xredis.Default.Del(w.Ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, chat.Id))
	}

	if question.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT ||
		answerRsp.SupportType == aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT { // 人工回答保存了之后不需要发送微信
		log.WithContext(w.Ctx).Infow("WechatAIMessage messageDeal with live agent or picture",
			"chatId", chat.Id, "message", message)
		return nil, nil
	}

	return answerRsp, sender
}

// BeforeStart ...
func (w *WechatAIMessage) BeforeStart(ctx context.Context) bool {
	w.Ctx = ctx
	return true
}

// GetChatMessage 将message按照chatId组合为map 并创建新用户的chat会话
// 1 获取每个用户的chat_id
// 2 每条消息分别处理
//
//	2.1 事件消息-关闭当前会话，因为用户重新进入了微信客服，等待用户发送问题开启一个新的会话
//	2.2 事件消息-有welcomeCode 则发送欢迎信息
//	2.3 文本消息 每个消息按照chat_id 存入map，如果没有则创建chat
//
// 3 创建新的会话并按照chat_id把message 存入map，并更新旧数据的ExternalUserID
func (w *WechatAIMessage) GetChatMessage(
	validMessage []workweixin.Message) map[*aipb.ChatWeChat][]workweixin.Message {
	var externalUserIds []string
	for _, customer := range w.customerMap {
		externalUserIds = append(externalUserIds, customer.ExternalUserID)
	}

	var ChatIDRsp *aipb.RspGetChatWechat
	var err error
	if len(externalUserIds) > 0 {
		// 获取用户对应的chat_id
		ChatIDRsp, err = client.AiNational.GetChatWechat(w.Ctx, &aipb.ReqGetChatWechat{
			AssistantId:      w.assistant.Id,
			ExternalUserIds:  externalUserIds,
			ChatType:         aipb.ChatType_CHAT_TYPE_WECHAT,
			ChatIdleDuration: w.assistant.ChatIdleDuration,
		})
		if err != nil || ChatIDRsp == nil {
			log.WithContext(w.Ctx).Errorw("dealWorkWeixinMessage GetChatIDWechat", "err", err, "ChatIDRsp", ChatIDRsp)
			return nil
		}
	}
	if ChatIDRsp == nil {
		ChatIDRsp = &aipb.RspGetChatWechat{}
	}

	messageMap, newChats := divideWorkWeixinMessage(w.Ctx, validMessage, w.assistant.Id,
		w.customerMap, ChatIDRsp.ChatMap, ChatIDRsp.FinishedChatLiveAgentMap)

	if len(newChats) > 0 {
		// 创建或者更新微信chat
		_, err = upsertWeixinChat(w.Ctx, newChats, w.assistant.Id)
		if err != nil {
			log.WithContext(w.Ctx).Errorw("dealWorkWeixinMessage upsertWeixinChat", "err", err)
		}
	}

	return messageMap
}

// Send ...
func (w *WechatAIMessage) Send(doResult aware.DoResultRecord, original workweixin.Message,
	sender aware.MessageAnswerAware[workweixin.Message]) aware.SendResultRecord[workweixin.Message] {
	if len(doResult.AnswerSharding) > 0 { // 处理继续回答的答案分片
		return doSendAnswerSharding(w.Ctx, doResult, original, w.assistant.CorpId, w.CosPublicCdnUrl)
	}

	var err error
	wechatSender, ok := sender.(*aware.WechatMessageSetAnswer)
	log.WithContext(w.Ctx).Infow("CustomKeywordsFunc info", "ok", ok, "answerText", wechatSender.GetAnswerString(),
		"answerType", wechatSender.GetAnswerType(), "askType", wechatSender.AskType)
	if !ok {
		return aware.SendResultRecord[workweixin.Message]{}
	}

	aware.SetUnSendQuestionCount(w.Ctx, doResult.ChatId, int64(len(doResult.Records)), 0)

	for i, record := range doResult.Records {
		// 就是算是报错了，也需要执行这个方法里的defer，所以把err传进去调用
		err = w.doSendAnswerRecord(wechatSender, record, original, doResult.ChatId, err)
		if i < len(doResult.Records)-1 {
			aware.WechatSendTimeWait() // 执行每个record的发送逻辑时短暂延迟
		}
	}

	return aware.SendResultRecord[workweixin.Message]{
		Ctx:       w.Ctx,
		Err:       err,
		DoResult:  doResult,
		Sender:    wechatSender,
		CheckPass: !wechatSender.NoPass,
	}
}

// AfterSend 处理会话的暗号重复发送逻辑
func (w *WechatAIMessage) AfterSend(chat *aipb.ChatWeChat, msg workweixin.Message,
	record aware.SendResultRecord[workweixin.Message]) {
	if record.Err != nil {
		log.WithContext(record.Ctx).Errorw("afterSend error", "record", record)
		return
	}

	if record.Sender == nil {
		return
	}

	answer, ok := record.Sender.(*aware.WechatMessageSetAnswer) // 执行发送后的扩展逻辑
	if ok && answer.AfterSend != nil {
		v := record.DoResult.Records[0]
		record.SendFlag = answer.AfterSend(w.Ctx, v.AnswerId, chat, msg) && record.SendFlag
	}
	if answer.Model == aware.AiProcessingModelContinue { // 如果是继续回答则不需要执行后续逻辑
		return
	}
	// 判断用户是否开启了问题建议，开启了并且是用户的正常问答不是暗号 则推送建议问题 // 此处逻辑暂时调整到ai返回
	if needSuggestQuestion(w.assistant, record.Sender) {
		aware.RemainSuggestQuestionWechat(w.Ctx, msg, record, w.assistant)
	}

	if needRemainTurnManual(w.assistant, record.SendFlag) { // 触发转人工暗号提醒
		aware.RemainTurnManualWechat(w.Ctx, chat, msg, record, w.assistant.LiveAgentMsg.RemindPushMsg.Msg, w.assistant.CorpId)
	}
}

func (w *WechatAIMessage) setDefaultAnswer() *WechatAIMessage {
	if w.assistant.RatingScaleMsg != nil && w.assistant.RatingScaleMsg.Enable {
		w.defaultAnswer = append(w.defaultAnswer,
			&aware.WechatMessageSetAnswer{ // 触发 满意 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					ExactKeywords: []string{aware.RatingScaleSatisfiedZH},
					Extension:     updateWechatRateAiAnswer,
				},
				Content: aware.GetRatingScale(aware.RatingScaleSatisfiedZH, "感谢您的支持！", w.assistant.RatingScaleMsg),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET,
			},
			&aware.WechatMessageSetAnswer{ // 触发 一般 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					ExactKeywords: []string{aware.RatingScaleAverageZH},
					Extension:     updateWechatRateAiAnswer,
				},
				Content: aware.GetRatingScale(aware.RatingScaleAverageZH, "我会继续努力的！", w.assistant.RatingScaleMsg),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET,
			},
			&aware.WechatMessageSetAnswer{ // 触发 不满意 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					ExactKeywords: []string{aware.RatingScaleDissatisfiedZH},
					Extension:     updateWechatRateAiAnswer,
				},
				Content: aware.GetRatingScale(aware.RatingScaleDissatisfiedZH, "我会继续努力的！您可以去官网tanlive.com教教小海豹",
					w.assistant.RatingScaleMsg),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET,
			},
		)
	}
	if content := aware.WarpWechatCodeToString(w.assistant, false); content != "" {
		w.defaultAnswer = append(w.defaultAnswer, &aware.WechatMessageSetAnswer{ // 触发 暗号 精确关键字
			MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
				ExactKeywords: config.GetStringSliceOr("workweixin.ai.cmdKeywords", []string{"指令", "暗号", "互动暗号"})},
			Content: content,
			AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
		})
	}

	if w.assistant.LiveAgentMsg != nil && w.assistant.LiveAgentMsg.Enable { // 开启了人工配置 则添加人工相关的匹配项
		liveAgentMsg := "您的问题已收到，我们将在工作时间8小时内尽快给您答复"
		if w.assistant.LiveAgentMsg.Msg != "" {
			liveAgentMsg = w.assistant.LiveAgentMsg.Msg
		}
		w.defaultAnswer = append(w.defaultAnswer,
			&aware.WechatMessageSetAnswer{ // 触发人工服务回复精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					ExactKeywords: config.GetStringSliceOr("workweixin.ai.humanServiceExactKeywords",
						[]string{"人工", "customer service", "转人工客服", "manual", "Manual"}),
				},
				Content:   liveAgentMsg,
				AskType:   aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
				AfterSend: switchChatLiveAgent,
				Level:     aware.AiAnswerLevelFirst,
			},
			&aware.WechatMessageSetAnswer{ // 触发 包含人工服务 模糊关键词
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					FuzzyKeywords: config.GetStringSliceOr("workweixin.ai.humanServiceFuzzyKeywords",
						[]string{"切换人工", "转人工", "帮我转人工客服", "customer support"}),
				},
				HeadMessage: config.GetStringOr("workweixin.ai.humanServiceConfirm", "你是否想转人工客服？"),
				Click: aware.TranSliceToWechatClick(config.GetStringSliceOr("workweixin.ai.humanServiceConfirmClick",
					[]string{"转人工客服", "不，继续回答"}), ""),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
			},
			&aware.WechatMessageSetAnswer{ // 触发 不，继续回答 精确关键词
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					ExactKeywords: config.GetStringSliceOr("workweixin.ai.humanServiceConfirmClickNo", []string{"不，继续回答"}),
				},
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_CONTINUE,
			},
		)
	}
	return w
}

// GetWechatDefaultCmdAnswer 设置和指令相关的默认命令
func (w *WechatAIMessage) GetWechatDefaultCmdAnswer() *WechatAIMessage {
	if w.assistant == nil || w.assistant.WelcomeMsg == nil || w.assistant.WelcomeMsg.CodeConfig == nil {
		return w
	}
	for _, v := range w.assistant.WelcomeMsg.CodeConfig.Codes {
		switch v.InteractiveCode {
		case aipb.InteractiveCode_INTERACTIVE_CODE_ANSWER_AGAIN:
			w.defaultAnswer = append(w.defaultAnswer, &aware.WechatMessageSetAnswer{ // 触发 重新回答 精确关键词
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					ExactKeywords: config.GetStringSliceOr("workweixin.ai.answerAgainKeywords", []string{"重新回答"}),
				},
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION,
			})
		case aipb.InteractiveCode_INTERACTIVE_CODE_CLEAR_CONTEXT:
			w.defaultAnswer = append(w.defaultAnswer, &aware.WechatMessageSetAnswer{ // 触发 清空上下文 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					ExactKeywords: config.GetStringSliceOr("workweixin.ai.clearContextQuestion", []string{
						"清空上下文", "Clear context", "Clear the context", "清空", "清空聊天记录", "清空记录", "新对话"}),
					Extension: aware.FinishWeixinChatByClear,
				},
				Content: config.GetStringOr("workweixin.ai.clearContextAnswer", "好的，我已经清空了之前的上下文。如果你有其他问题可随时问我。"),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
				Level:   aware.AiAnswerLevelFirst,
			})
		case aipb.InteractiveCode_INTERACTIVE_CODE_CONTRIBUTE_KNOWLEDGE:
			w.defaultAnswer = append(w.defaultAnswer, &aware.WechatMessageSetAnswer{ // 触发 上传知识库 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
					ExactKeywords: config.GetStringSliceOr("workweixin.ai.contributeKnowledgeQuestion", []string{
						"贡献知识", "上传知识", "上传知识库"}),
				},
				Content: config.GetStringOr("workweixin.ai.contributeKnowledgeAnswer", "感谢贡献知识，请点击以下小程序上传文件哦"),
				AfterSend: func(ctx context.Context, answerId uint64, chat *aipb.ChatWeChat, message workweixin.Message) bool {
					sendWechatMiniProgram(ctx, answerId, chat.Id, w.assistant.CorpId, message)
					return false
				},
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
			})
		default:
			//case aipb.InteractiveCode_INTERACTIVE_CODE_MANUAL:  //人工除了暗号之外还有单独的配置
			//case aipb.InteractiveCode_INTERACTIVE_CODE_READ_BOM: // 读配料表 取决于是否配置了multimodal_prompt_prefix
			//case aipb.InteractiveCode_INTERACTIVE_CODE_READ_TEST_REPORT:// 读检测报告 取决于是否配置了multimodal_prompt_prefix
		}
	}
	return w
}

// 此处添加的默认问题顺序不能混乱
func (w *WechatAIMessage) setFileDefaultAnswer() *WechatAIMessage {
	add := false

	if w.assistant.MultimodalPrompt != nil {
		if w.assistant.DefaultPushMsg == nil {
			w.assistant.DefaultPushMsg = &aipb.AssistantConfigMsg{}
		}
		if w.assistant.MultimodalPrompt.ReadTestReport != "" && w.assistant.DefaultPushMsg.ReadBom == "" {
			w.assistant.DefaultPushMsg.ReadBom = "好的，请把配料表拍给我"
		}
		if w.assistant.MultimodalPrompt.ReadTestReport != "" && w.assistant.DefaultPushMsg.ReadTestReport == "" {
			w.assistant.DefaultPushMsg.ReadTestReport = "没问题，请发给我检测报告pdf或者照片"
		}

		if len(w.assistant.MultimodalPrompt.ReadTestReport) > 0 {
			add = true
			w.defaultAnswer = append(w.defaultAnswer,
				&aware.WechatMessageSetAnswer{ // 触发 读检测报告 精确关键字 默认回复
					MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
						CustomKeywordsFunc: func(question *aipb.ChatMessage) bool {
							if !xslice.IsContainsString([]string{aware.DecipherReport, aware.ReadReport}, question.Text) {
								return false
							}
							key := fmt.Sprintf(aware.AiFileProcessingRedisKey, question.ChatId)
							value := xredis.Default.Get(w.Ctx, key)
							if value != nil && value.Val() == aipb.AIFileProcessing_AI_FILE_PROCESSING_FILE_WITHOUT_CMD.String() {
								return false
							}
							return true
						},
					},
					Content: w.assistant.DefaultPushMsg.ReadTestReport,
					AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
					AfterSend: func(ctx context.Context, _ uint64, chat *aipb.ChatWeChat, _ workweixin.Message) bool {
						xredis.Default.SetEx(ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, chat.Id),
							aipb.AIFileProcessing_AI_FILE_PROCESSING_REPORT_WITHOUT_FILE.String(), time.Hour)
						return true
					},
				},
			)
		}
		if len(w.assistant.MultimodalPrompt.ReadBom) > 0 {
			add = true
			w.defaultAnswer = append(w.defaultAnswer,
				&aware.WechatMessageSetAnswer{ // 触发 读配料表 精确关键字 默认回复
					MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
						CustomKeywordsFunc: func(question *aipb.ChatMessage) bool {
							if !xslice.IsContainsString([]string{aware.ReadBomPicture, aware.ReadBom, aware.DecipherBom}, question.Text) {
								return false
							}
							value := xredis.Default.Get(w.Ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, question.ChatId))
							return value == nil || value.Val() != aipb.AIFileProcessing_AI_FILE_PROCESSING_FILE_WITHOUT_CMD.String()
						},
					},
					Content: w.assistant.DefaultPushMsg.ReadBom,
					AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
					AfterSend: func(ctx context.Context, _ uint64, chat *aipb.ChatWeChat, _ workweixin.Message) bool {
						xredis.Default.SetEx(ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, chat.Id),
							aipb.AIFileProcessing_AI_FILE_PROCESSING_BOM_WITHOUT_FILE.String(), time.Hour)
						return true
					},
				},
			)
		}
	}

	answer := &aware.WechatMessageSetAnswer{ // 图片、文件作为输入，只有第一次收到文件时需要发送消息 model=0
		MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
			CustomKeywordsFunc: func(question *aipb.ChatMessage) bool {
				if question.AskType != aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE &&
					question.AskType != aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE {
					return false
				}
				value := xredis.Default.Get(w.Ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, question.ChatId))
				return value == nil || value.Val() == ""
			},
			Extension: func(ctx context.Context, chat *aipb.ChatWeChat, _ *workweixin.Message) {
				xredis.Default.SetEx(ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, chat.Id),
					aipb.AIFileProcessing_AI_FILE_PROCESSING_FILE_WITHOUT_CMD.String(), time.Hour)
			},
		},
		// AskType:     aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
		Content: config.GetStringOr("workweixin.ai.fileCmdConfirm", "请告诉我需要我对这些文件/图片做什么呢？"),
		NoPass:  true,
	}
	if add { // 如果配置了多模态prompt，则要改变回复的内容
		answer.Content = ""
		answer.HeadMessage = config.GetStringOr("workweixin.ai.fileCmdConfirm", "请告诉我需要我对这些文件/图片做什么呢？")
		answer.TailMessage = config.GetStringOr("workweixin.ai.fileCmdConfirmTail", "或直接回复需要我做的事情")
		answer.Click = aware.TranSliceToWechatClick([]string{aware.ReadBom, aware.ReadReport}, "")
	}
	w.defaultAnswer = append(w.defaultAnswer,
		answer,
		&aware.WechatMessageSetAnswer{ // 第二次收到图片、文件作为输入，却不知道命令 model=aware.AiProcessingModelFile
			MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
				CustomKeywordsFunc: func(question *aipb.ChatMessage) bool {
					if question.AskType != aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE &&
						question.AskType != aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE {
						return false
					}
					value := xredis.Default.Get(w.Ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, question.ChatId))
					return value != nil && value.Val() == aipb.AIFileProcessing_AI_FILE_PROCESSING_FILE_WITHOUT_CMD.String()
				},
			},
			NoPass: true,
			Model:  aware.AiProcessingModelFile,
		},
		&aware.WechatMessageSetAnswer{ // 真正执行文件命令的逻辑,这个回答一定要放在上三个默认回答之后 model=aware.AiProcessingModelFile
			MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
				CustomKeywordsFunc: func(question *aipb.ChatMessage) bool {
					// 用户先输入了命令再输入文件，这种情况用户可能还要继续输入文件，后续暂时不清空当前文件处理agent的标识
					return question.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE ||
						question.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE
				},
			},
			Model: aware.AiProcessingModelFile,
		},
		&aware.WechatMessageSetAnswer{ // 真正执行文件命令的逻辑,这个回答一定要放在上三个默认回答之后 model=aware.AiProcessingModelFile
			MessageAwareImpl: aware.MessageAwareImpl[workweixin.Message]{
				CustomKeywordsFunc: func(question *aipb.ChatMessage) bool {
					// 此处原本只判断[]string{"读配料表", "读检测报告"}即可，考虑到用户可能手动输入，所以把可能的暗号都判断了
					value := xredis.Default.Get(w.Ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, question.ChatId))
					return value != nil && value.Val() == aipb.AIFileProcessing_AI_FILE_PROCESSING_FILE_WITHOUT_CMD.String()
					// return xslice.IsContainsString([]string{aware.DecipherReport, aware.ReadReport,
					//	aware.ReadBomPicture, aware.ReadBom, aware.DecipherBom}, question.Text)
				},
			},
			AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
			AfterSend: func(ctx context.Context, _ uint64, chat *aipb.ChatWeChat, msg workweixin.Message) bool {
				// 用户先输入了文件，然后再告知命令，用户不再输入文件了，则后续清空当前文件处理agent的标识
				// 清空当前文件处理agent的标识
				xredis.Default.Del(ctx, fmt.Sprintf(aware.AiFileProcessingRedisKey, chat.Id))
				return true
			},
			Model: aware.AiProcessingModelFile,
		},
	)
	return w
}

// messagePreprocessing 消息前置处理
func (w *WechatAIMessage) messagePreprocessing(chat *aipb.ChatWeChat,
	message workweixin.Message) (*aipb.ChatMessage, aware.MessageAnswerAware[workweixin.Message]) {
	if message.MsgType == workweixin.MsgTypeEvent { // 事件特殊处理
		switch message.Event.EventType {
		case workweixin.MsgEventEnterSession: // 用户进入会话
			w.sendWelcomeMessageByWechat(message.Event.WelcomeCode)
			// case workweixin.MsgEventSessionStatusChange: //会话状态变更
		}
		return nil, nil
	}

	var imageUrl string
	var err error
	question := &aipb.ChatMessage{
		ChatId:      chat.Id,
		Text:        message.Text.Content,
		Type:        aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER,
		AssistantId: w.assistant.Id,
	}
	switch message.MsgType {
	case workweixin.MsgTypeText:
		question.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL
	case workweixin.MsgTypeFile:
		question.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE
		imageUrl, err = TransferMediaToCosRes(workweixin.BuildNewClient(workweixin.Client, chat.CorpId,
			aware.LoadOpenWorkAccessToken), message.File.MediaID)
	case workweixin.MsgTypeImage:
		question.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE
		imageUrl, err = TransferMediaToCosRes(workweixin.BuildNewClient(workweixin.Client, chat.CorpId,
			aware.LoadOpenWorkAccessToken), message.Image.MediaID)
	case workweixin.MsgTypeVoice:
		question.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_VOICE // 语音要识别为文字
		imageUrl, err = TransferMediaToCosRes(workweixin.BuildNewClient(workweixin.Client, chat.CorpId,
			aware.LoadOpenWorkAccessToken), message.Voice.MediaID)
		if len(imageUrl) == 0 || err != nil {
			break
		}
		resp, err1 := asr.DescribeRecTaskByUrl(w.Ctx, asr.ModelTypeZh, imageUrl, 3)
		if err1 != nil || len(resp) == 0 {
			log.WithContext(w.Ctx).Errorw("DescribeRecTaskByUrl error", "err", err, "url", imageUrl, "resp", resp)
			question.Text = "语音识别为空"
			question.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET
			return question, aware.GetWechatContentAnswer("哎呀 我听不懂你说的呢 请用普通话再试试")
		}
		imageUrl = ""
		question.Text, message.Text.Content = resp, resp // 将语音识别的结果填充到问题和message里
	default:
		return nil, nil
	}
	if err != nil {
		log.WithContext(w.Ctx).Errorw("TransferMediaToCosRes error", "err", err, "message", message)
		return question, aware.GetWechatContentAnswer(w.TimeoutDefaultAnswer)
	}
	if len(imageUrl) > 0 {
		question.ImageUrl = []string{imageUrl}
	}

	if message.Origin == workweixin.WechatMessageOriginKf { // 客服消息
		question.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT
		return question, nil
	}
	if chat.SupportType == aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT { // 在进行客服聊天时不做任何暗号处理
		return question, nil
	}
	if notice := getAssistantNotice(w.Ctx, w.assistant.Channel); notice != "" {
		question.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET
		return question, aware.GetWechatContentAnswer(notice)
	}

	if answer := aware.GetWechatAnswer(question, w.defaultAnswer); answer != nil {
		answer.DoExtension(w.Ctx, chat, &message)
		question.AskType = answer.GetAnswerType(question.AskType)
		if question.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET {
			question.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER
		}
		return question, answer
	}
	// 审核问题
	question.RejectReason = ReviewChatMessage(w.Ctx, question)
	return question, nil
}

func switchChatLiveAgent(ctx context.Context, _ uint64, chat *aipb.ChatWeChat, message workweixin.Message) bool {
	var liveAgentName string
	serviceState := 2 // serviceState 2 待接入池排队中

	liveAgent, err := client.AiNational.ListChatLiveAgent(ctx, &aipb.ReqListChatLiveAgent{
		AssistantIds: []uint64{chat.AssistantId},
		Page:         &basepb.Paginator{Limit: 1},
		Status:       aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_CURRENTLY_SERVING,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("UpdateChatMessageRecordWechat ListChatLiveAgent error", "err", err)
	}
	if liveAgent != nil && len(liveAgent.ChatLiveAgents) > 0 && len(liveAgent.ChatLiveAgents[0].Username) > 0 {
		liveAgentName = liveAgent.ChatLiveAgents[0].Username
		serviceState = 3 // serviceState 3 人工
	}

	log.WithContext(ctx).Debugw("switchChatLiveAgent info", "liveAgentName", liveAgentName, "serviceState", serviceState)
	msgEvent, err := workweixin.BuildNewClient(workweixin.Client, chat.CorpId,
		aware.LoadOpenWorkAccessToken).ChangeSessionStatus(message.OpenKFID, message.ExternalUserID,
		serviceState, liveAgentName)
	if err != nil {
		log.WithContext(ctx).Errorw("switchChatLiveAgent ChangeSessionStatus error", "err", err)
		return true
	}
	if msgEvent.ErrCode != 0 {
		log.WithContext(ctx).Errorw("CheckAndSwitchChatLiveAgent ChangeSessionStatus msgEvent error",
			"err", err, "msgEvent", msgEvent)
		return true
	}

	// chat 发送了转人工，则记录需要将个位 置0
	chat.DefaultAnswerRecord = chat.DefaultAnswerRecord - chat.DefaultAnswerRecord%10

	_, err = client.AiByID(chat.Id).SwitchChatLiveAgent(ctx, &aipb.ReqSwitchChatLiveAgent{
		LiveAgentName:             liveAgentName,
		ChatId:                    chat.Id,
		SupportType:               aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT,
		UpdateDefaultAnswerRecord: true,
		DefaultAnswerRecord:       chat.DefaultAnswerRecord,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("wechat SwitchChatLiveAgent error", "chatId", chat.Id, "err", err)
	}
	return false
}

func updateWechatRateAiAnswer(ctx context.Context, chat *aipb.ChatWeChat, message *workweixin.Message) {
	if len(message.Text.MenuID) == 0 {
		return
	}
	answerId, err := strconv.ParseUint(message.Text.MenuID, 10, 64)
	if err != nil {
		log.WithContext(ctx).Errorw("updateRateAiAnswer messageId illegal",
			"chatId", chat.Id, "answerId", answerId)
	}
	aware.UpdateRateAiAnswer(ctx, chat, message.Text.Content, answerId)
}

// getAssistantNotice ...
func getAssistantNotice(ctx context.Context, channel aipb.AssistantChannel) string {
	key := config.GetStringOr("ai.assistant.noticeCacheKey", "ai:assistant:noticeConfig")
	var conf aipb.AiAssistantNoticeConf
	value := xredis.Use("ai").Get(ctx, key)
	if value == nil || value.Val() == "" {
		return ""
	}
	if err := json.Unmarshal([]byte(value.Val()), &conf); err != nil {
		log.WithContext(ctx).Errorw("getAssistantNotice Unmarshal Error", "err", err)
		return ""
	}
	if !conf.Enable {
		return ""
	}
	if conf.Notice == nil {
		log.WithContext(ctx).Error("getAssistantNotice with empty conf notice")
		return ""
	}
	if xslice.Contains(conf.Channel, channel) < 0 {
		return ""
	}
	now := time.Now()
	if now.Before(conf.RangeTime.Start.AsTime()) || now.After(conf.RangeTime.End.AsTime()) {
		// 当前时间不在持续时间内
		return ""
	}

	if channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP {
		return conf.Notice.En
	}
	return conf.Notice.Zh
}

// sendWelcomeMessageByWechat 发送欢迎信息
func (w *WechatAIMessage) sendWelcomeMessageByWechat(welcomeCode string) {
	log.WithContext(w.Ctx).Debugw("WechatAIMessage sendWelcomeMessageByWechat", "welcomeCode", welcomeCode)
	if welcomeCode == "" {
		return
	}

	var opts []workweixin.Option
	if notice := getAssistantNotice(w.Ctx, w.assistant.Channel); notice != "" { // 如果有横幅提示则发送横幅提示
		opts = []workweixin.Option{workweixin.AddMenuContent(notice, "")}
	} else if !w.assistant.Enabled { // 助手禁用了则发送助手已下线消息
		msg := config.GetStringOr("workweixin.ai.offlineAssistantAnswer", "小助手已下线，请联系管理员")
		opts = []workweixin.Option{workweixin.AddMenuContent(msg, "")}
	} else {
		opts = []workweixin.Option{workweixin.AddMenuContent(w.welcomeMsg.HeadMsg,
			aware.WarpWechatCodeToString(w.assistant, true))}
		for _, q := range w.welcomeMsg.Question {
			opts = append(opts, workweixin.AddClink(welcomeCode, q))
		}
	}
	event, err := workweixin.BuildNewClient(workweixin.Client, w.assistant.CorpId,
		aware.LoadOpenWorkAccessToken).SendMsgOnEvent(welcomeCode, "", opts...)
	if err != nil || event.ErrCode != 0 {
		log.WithContext(w.Ctx).Errorw("sendWelcomeMessageByWechat", "event", event, "err", err)
	}
}

func sendAssistantDisableMessage(ctx context.Context, messages []workweixin.Message, cropId string) {
	msg := config.GetStringOr("workweixin.ai.offlineAssistantAnswer", "小助手已下线，请联系管理员")
	for _, v := range messages {
		sendWeixinTextMessageWithoutChat(ctx, v, msg, cropId)
	}
}

// sendWeixinTextMessageWithoutChat 没有会话时直接发送微信文本消息
func sendWeixinTextMessageWithoutChat(ctx context.Context, message workweixin.Message, answer, corpId string) {
	answerMessageId, err := workweixin.BuildNewClient(workweixin.Client, corpId,
		aware.LoadOpenWorkAccessToken).SendTextMessage(message.ExternalUserID, message.OpenKFID, answer)
	if err != nil {
		log.WithContext(ctx).Errorw("sendWeixinTextMessageWithoutChat error", "err", err,
			"message", message, "answer", answer)
		return
	}
	log.WithContext(ctx).Debugw("sendWeixinTextMessageWithoutChat success", "answerMessageId", answerMessageId)
}

// PrepareUgcMessageByWechat ...
func (w *WechatAIMessage) PrepareUgcMessageByWechat(chatMessage *aipb.EventChatHashMessage) string {
	// 创建模板对象
	tmpl, err := template.New("UgcTemplate").Parse(w.UgcAnswerTemplate)
	if err != nil {
		log.WithContext(w.Ctx).Errorw("prepareMessageByWechat UgcAnswerTemplate err",
			"err", err, "template", w.UgcAnswerTemplate)
		return chatMessage.Text
	}
	// 拼接UGC信息
	argGroup := make([]map[string]interface{}, 0, len(chatMessage.Ugcs))
	var viewMores []string
	for _, cu := range chatMessage.Ugcs {
		arg := map[string]interface{}{}
		var ugcInfo, searchFilter []string
		filterArray := make(map[string]struct{})
		ugcJumpLink := "<a href=\"%s\">%s</a>"
		ugcJumpUrl := getUgcJumpPathByEnum(cu.UgcType, w.UgcJumpUrl)
		ugcDetailJumpLink := "【" + ugcJumpLink + "】"
		ugcDetailJumpUrl := ugcJumpUrl + "/detail/"

		for _, v := range cu.Cards {
			ugcInfo = append(ugcInfo, fmt.Sprintf(ugcDetailJumpLink, ugcDetailJumpUrl+v.Id, v.Name))
		}
		arg["UgcData"] = strings.Join(ugcInfo, "\n")

		// 拼接过滤条件
		for _, item := range cu.Filter {
			if len(item.Tags) > 0 {
				for _, tag := range item.Tags {
					filterArray[tag.Name] = struct{}{}
					searchFilter = append(searchFilter, fmt.Sprintf("%s=%s", item.Field, tag.Name))
				}
			} else {
				filterArray[item.Value] = struct{}{}
				searchFilter = append(searchFilter, fmt.Sprintf("%s=%s", item.Field, item.Value))
			}
		}
		if len(filterArray) > 0 {
			arg["Filter"] = strings.Join(maps.Keys(filterArray), ",") + "的" // 有条件的时候拼接`的`
		} else {
			arg["Filter"] = ""
		}

		// 拼接UGC类型
		ugcType := getUgcDataTypeNameByEnum(cu.UgcType)
		arg["UgcType"] = ugcType

		// 拼接查看更多
		if cu.IsUgcLink {
			viewMores = append(viewMores,
				fmt.Sprintf(ugcJumpLink, ugcJumpUrl+"?"+strings.Join(searchFilter, "&"), w.ReadMore+ugcType))
		}
		argGroup = append(argGroup, arg)
	}
	if len(argGroup) == 0 {
		return chatMessage.Text
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, map[string]interface{}{
		"ViewMores": viewMores,
		"Args":      argGroup,
	})
	if err != nil {
		log.WithContext(w.Ctx).Errorw("prepareMessageByWechat ugcAnswerTemplate err", "err", err,
			"template", w.UgcAnswerTemplate)
		return chatMessage.Text
	}
	log.WithContext(w.Ctx).Debugw("prepareUgcMessageByWechat message", "text", buf.String())
	return buf.String()
}

// GetMessageId 获取消息ID
func (w *WechatAIMessage) GetMessageId(t workweixin.Message) string {
	return t.MsgID
}

// PrepareSearchMessageByWechat 准备搜索微信消息
func (w *WechatAIMessage) PrepareSearchMessageByWechat(chatMessage *aipb.EventChatHashMessage) string {
	// 创建模板对象
	tmpl, err := template.New("ReferenceTemplate").Parse(w.ReferenceAnswerTemplate)
	if err != nil {
		log.WithContext(w.Ctx).Errorw("prepareMessageByWechat ReferenceAnswerTemplate err",
			"err", err, "template", w.ReferenceAnswerTemplate)
		return chatMessage.Text
	}
	args := map[string]interface{}{"Text": chatMessage.Text}

	contributors := make(map[string]string)
	ugcDetailJumpUrl := "<a href=\"%s/detail/%s\">%s</a>"

	for _, doc := range chatMessage.Docs {
		for _, v := range doc.Contributor { // 拼接贡献者
			switch v.Type {
			case basepb.IdentityType_IDENTITY_TYPE_TEAM:
				if v.IsPublished {
					teamJumpUrl := getUgcJumpPathByEnum(basepb.DataType_DATA_TYPE_TEAM, w.UgcJumpUrl)
					contributors[v.Text] = fmt.Sprintf(ugcDetailJumpUrl, teamJumpUrl, v.Id, v.Text)
					continue
				}
				fallthrough
			case basepb.IdentityType_IDENTITY_TYPE_CUSTOM:
				contributors[v.Text] = v.Text
			}
		}
	}
	if len(contributors) > 0 {
		args["Contributor"] = w.AnswerFrom + strings.Join(maps.Values(contributors), " ")
	} else {
		args["Contributor"] = "" // 没有共享者则默认填空，避免填充模版报错
	}

	// 拼接参考资料
	reference := FetchTitleUtil{
		DocJumpLink:     "%d.【<a href=\"%s\">%s</a>】",
		LinkJumpLink:    "%d.<a href=\"%s\">%s</a>",
		HelpCenterUrl:   w.HelpCenterUrl,
		CosPublicCdnUrl: w.CosPublicCdnUrl,
		format: func(link, name, url string, num int) string {
			return fmt.Sprintf(link, num, url, name)
		},
		Assistant: w.assistant,
	}.FetchTitle(w.Ctx, chatMessage)
	if len(reference) > 0 {
		args["Reference"] = w.ReferenceDataFrom + strings.Join(reference, "\n")
	} else {
		args["Reference"] = ""
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, args)
	if err != nil {
		log.WithContext(w.Ctx).Errorw("prepareMessageByWechat referenceAnswerTemplate err", "err", err,
			"template", w.ReferenceAnswerTemplate)
		return chatMessage.Text
	}
	log.WithContext(w.Ctx).Debugw("prepareMessageByWechat message", "text", buf.String())
	return buf.String()
}

// FetchTitleUtil ...
type FetchTitleUtil struct {
	DocJumpLink     string // doc 文档跳转地址
	LinkJumpLink    string // 链接跳转地址
	HelpCenterUrl   string // 帮助中心跳转地址
	CosPublicCdnUrl string // CDN地址
	format          func(link, name, url string, num int) string
	Assistant       *aipb.AssistantDetail
}

// FetchTitle 获取标题，拼接CDN地址
func (f FetchTitleUtil) FetchTitle(ctx context.Context, chatMessage *aipb.EventChatHashMessage) []string {
	var reference []string
	var num int

	titleMap := make(map[int]string)

	isUrl := func(path string, withHttp bool) bool {
		_, err := url.ParseRequestURI(path)
		if withHttp && err == nil {
			return strings.HasPrefix(path, "http")
		}
		return err == nil
	}

	var urls []string

	for _, doc := range chatMessage.Docs {
		if len(doc.Reference) == 0 {
			continue
		}
		if doc.UgcType == basepb.DataType_DATA_TYPE_HELP_CENTER {
			num++
			reference = append(reference, fmt.Sprintf(
				f.DocJumpLink, num, fmt.Sprintf(f.HelpCenterUrl, doc.UgcId), CleanTitleInvalidString(doc.Reference[0].Name)))
		}
		for _, ref := range doc.Reference {
			num++
			if len(ref.Url) > 0 && isUrl(ref.Url, true) { // 有URL显示超链接
				reference = append(reference, f.format(f.DocJumpLink, CleanTitleInvalidString(ref.Name), ref.Url, num))

			} else if len(ref.Url) > 0 && isUrl(ref.Url, false) { // 有url，且是cos地址
				reference = append(reference, f.format(f.DocJumpLink, CleanTitleInvalidString(ref.Name), f.CosPublicCdnUrl+ref.Url, num))

			} else if len(ref.Name) > 0 { // 有名称则直接显示文本
				reference = append(reference, fmt.Sprintf("%d. %s", num, ref.Name))

			} else if len(ref.Text) > 0 { // 有文本则直接显示文本
				if isUrl(ref.Text, true) {
					titleMap[num] = ref.Text
					urls = append(urls, ref.Text)
				}
				reference = append(reference, fmt.Sprintf("%d. %s", num, ref.Text))
			}
		}
	}

	if len(chatMessage.Link) > 0 {
		links := strings.Split(chatMessage.Link, "\n")
		for _, link := range links {
			if !strings.HasPrefix(link, "http") { // 如果不是链接则跳过
				continue
			}
			num++
			titleMap[num] = link
			urls = append(urls, link)
			reference = append(reference, fmt.Sprintf("%d. %s", num, link))
		}
	}

	if len(urls) > 0 {
		urlTitles, err := FetchChatHtmlTitle(ctx, urls, f.Assistant)
		if err != nil {
			log.WithContext(ctx).Errorw("wechat FetchTitle error", "err", err)
		}
		for k, v := range titleMap {
			index := k
			path := v
			fetchTitle := TitleTruncate(CleanTitleInvalidString(urlTitles[path]), 20, 40)

			if fetchTitle == "" || path == fetchTitle { // 如果链接和标题相同，则直接显示链接
				reference[index-1] = fmt.Sprintf("%d. %s", index, path)
				continue
			}
			reference[index-1] = f.format(f.LinkJumpLink, fetchTitle, path, index)
		}
	}

	return reference
}

// TitleTruncate 根据限制截断字符串
/**
20中文字 40个英文字符内，且英文单词不截断。超出的+ ...
@param input 输入字符串
@param chineseLimit 中文字符限制
@param englishLimit 英文字符限制
*/
// TitleTruncate 根据限制截断字符串
/**
20中文字 40个英文字符内，且英文单词不截断。超出的+ ...
@param input 输入字符串
@param chineseLimit 中文字符限制
@param englishLimit 英文字符限制
*/
func TitleTruncate(input string, chineseLimit int, englishLimit int) string {
	var length int         // 当前字符串长度
	var runeCount int      // 字符计数
	lastTruncateIndex := 0 // 上一次可截断的索引

	if chineseLimit == 0 {
		chineseLimit = 20
	}

	if englishLimit == 0 {
		englishLimit = 40
	}

	for i, r := range input {
		if unicode.Is(unicode.Han, r) {
			length += 2 // 中文字符宽度
		} else if r >= 'a' && r <= 'z' || r >= 'A' && r <= 'Z' || unicode.IsDigit(r) {
			length++ // 英文字母或数字宽度
		} else {
			length++ // 其他字符宽度
		}

		// 记录空格或其他截断点
		//if r == ' ' || r == '|' || r == '：' || r == ':' {
		//	lastSpaceIndex = i
		//}

		// 超过限制
		if length > chineseLimit*2 || runeCount >= englishLimit {
			// 优先按照空格或其他截断点截断
			lastTruncateIndex = i
			return input[:lastTruncateIndex] + "..."
		}

		runeCount++
	}

	return input
}

func getUgcDataTypeNameByEnum(ugcType basepb.DataType) string {
	switch ugcType {
	case basepb.DataType_DATA_TYPE_TEAM:
		return "团队"
	case basepb.DataType_DATA_TYPE_PRODUCT:
		return "产品"
	case basepb.DataType_DATA_TYPE_RESOURCE:
		return "资源"
	case basepb.DataType_DATA_TYPE_GRAPH:
		return "图谱"
	}
	return ""
}

// https://pre.tanlive.tencent.com/zh/solutions/detail/zdnkjpy9jgel
func getUgcJumpPathByEnum(ugcType basepb.DataType, url string) string {
	switch ugcType {
	case basepb.DataType_DATA_TYPE_TEAM:
		return url + "innovators"
	case basepb.DataType_DATA_TYPE_PRODUCT:
		return url + "solutions"
	case basepb.DataType_DATA_TYPE_RESOURCE:
		return url + "programs"
	case basepb.DataType_DATA_TYPE_GRAPH:
		return url + "knowledge"
	}
	return url
}

func (w *WechatAIMessage) doSendAnswerRecord(wechatSender *aware.WechatMessageSetAnswer,
	record *aware.DoResult, original workweixin.Message, chatId uint64, err error) error {
	defer aware.SetUnSendQuestionCount(w.Ctx, chatId, 0, 1)

	if err != nil {
		return err
	}
	if record.Answer == nil {
		record.Answer = &aipb.EventChatHashMessage{
			Type: aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR,
			Text: w.TimeoutDefaultAnswer,
		}
	} else if record.Answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR ||
		record.Answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR {
		if record.Answer.Text == "" {
			wechatSender = &aware.WechatMessageSetAnswer{Content: w.TimeoutDefaultAnswer}
		} else {
			wechatSender = &aware.WechatMessageSetAnswer{Content: record.Answer.Text}
		}
	}

	log.WithContext(w.Ctx).Debugw("WechatAIMessage sendMessageByWechat", "questionId", record.Answer.QuestionId,
		"message", record.Answer, "userId", original.ExternalUserID, "sender", wechatSender.GetAnswerString())
	wechatSender.CdnUrl = w.CosPublicCdnUrl

	var sendRsp []*aipb.AiSendResultRecord
	splitSend := aware.SplitSend{
		SetAnswer: wechatSender,
		CorpId:    w.assistant.CorpId,
		Msg:       original,
		DoResult:  record,
		ChatId:    chatId,
	}

	switch record.Answer.Type {
	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION, aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH,
		aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION: // 文本和doc link
		menuId := strconv.FormatUint(record.AnswerId, 10)
		aware.SetWechatDefaultAnswer(wechatSender, menuId, w.PrepareSearchMessageByWechat(record.Answer), record.Answer.Think, w.assistant)
		sendRsp, err = splitSend.Do(w.Ctx, menuId, needSuggestQuestion(w.assistant, wechatSender))

	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY: // 返回UGC
		wechatSender.Content = w.PrepareUgcMessageByWechat(record.Answer)
		sendRsp, err = splitSend.Do(w.Ctx, "", needSuggestQuestion(w.assistant, wechatSender))

	default: // 默认直接发送消息
		menuId := strconv.FormatUint(record.AnswerId, 10)
		if wechatSender.Content == "" && wechatSender.HeadMessage == "" {
			if record.Answer.Text == "" {
				wechatSender.Content = w.TimeoutDefaultAnswer
			} else {
				wechatSender.Content = record.Answer.Text
			}
		}
		sendRsp, err = splitSend.Do(w.Ctx, menuId, false)
	}
	record.AnswerSharding = sendRsp
	if err != nil {
		log.WithContext(w.Ctx).Errorw("sendMessageByWechat SendTextMessage ", "err", err,
			"sendRsp", sendRsp)
	}
	return err
}

func doSendAnswerSharding(ctx context.Context, doResult aware.DoResultRecord, original workweixin.Message,
	corpId, cdnUrl string) aware.SendResultRecord[workweixin.Message] {

	var err error
	f := func(id uint64) workweixin.Clink {
		return workweixin.Clink{
			Id:      fmt.Sprintf(aware.RecordPrefixFmt, id),
			Content: aware.ContinueAnswerStr,
		}
	}
	for i, v := range doResult.AnswerSharding {
		var fileType string
		sender := aware.GetWechatEmptyAnswer()
		menuId := strconv.FormatUint(v.MessageId, 10)

		switch v.Type {
		case aipb.AiRecordType_AI_RECORD_TYPE_AI_TEXT:
			if v.ContinueAnswering {
				sender.HeadMessage = v.Content
				sender.Click = append(sender.Click, f(v.Id))
			} else {
				sender.Content = v.Content
			}
		case aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU:
			aware.SetWechatDefaultSearchAnswer(sender, menuId, v.Content)
			if v.ContinueAnswering {
				sender.Click = append(sender.Click, f(v.Id))
			}
		case aipb.AiRecordType_AI_RECORD_TYPE_LIVE_AGENT_MENU:
			sender.HeadMessage = v.Content
			if v.ContinueAnswering {
				sender.Click = append(sender.Click, workweixin.Clink{Id: menuId, Content: aware.CustomerServiceAnswerStr})
			}
		case aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU:
			sender.HeadMessage = v.Content
			sender.Click = append(sender.Click, aware.TranSliceToWechatClick(v.SuggestQuestions, menuId)...) // 因为菜单 默认需要给一个menuId,所以此处把问题的msgId丢进去，没有实际的意义，只是不报错而已
			if v.ContinueAnswering {
				sender.Click = append(sender.Click, f(v.Id))
			}
		case aipb.AiRecordType_AI_RECORD_TYPE_IMAGE:
			sender.CdnUrl = cdnUrl
			menuId = v.ExtraId
			fileType = workweixin.MediaTypeImage
		case aipb.AiRecordType_AI_RECORD_TYPE_VOICE:
			sender.CdnUrl = cdnUrl
			menuId = v.ExtraId
			fileType = workweixin.MediaTypeVoice
		case aipb.AiRecordType_AI_RECORD_TYPE_VIDEO:
			sender.CdnUrl = cdnUrl
			menuId = v.ExtraId
			fileType = workweixin.MediaTypeVideo
		case aipb.AiRecordType_AI_RECORD_TYPE_FILE:
			sender.CdnUrl = cdnUrl
			menuId = v.ExtraId
			fileType = workweixin.MediaTypeFile
		}
		sendRsp, err := sender.Send(original, corpId, menuId, fileType)
		doResult.AnswerSharding[i] = aware.TransformSendResult(v.Id, sendRsp)
		if err != nil {
			log.WithContext(ctx).Errorw("sendMessageByWechat doSendAnswerSharding ", "err", err,
				"sendRsp", sendRsp)
			break
		}
		if i < len(doResult.AnswerSharding)-1 {
			aware.WechatSendTimeWait()
		}
	}

	return aware.SendResultRecord[workweixin.Message]{
		Ctx:       ctx,
		Err:       err,
		DoResult:  doResult,
		CheckPass: true,
	}
}

// sendWechatMiniProgram 发送小程序消息
func sendWechatMiniProgram(ctx context.Context, messageId, chatId uint64, corpId string, msg workweixin.Message) {
	if corpId == "" {
		log.WithContext(ctx).Errorw("send wechat mini program fail,corpId is nil", "chatId", chatId, "msg", msg)
		return
	}
	mediaId := getMiniProgramMediaId(ctx, corpId)
	if mediaId == "" {
		return
	}
	para := map[string]string{
		"title":          config.GetStringOr("workweixin.ai.knowledgeMiniProgramTitle", "移动端上传和管理知识库"),
		"appid":          config.GetString("miniprogram.appID"),
		"thumb_media_id": mediaId,
		"pagepath":       config.GetStringOr("workweixin.ai.knowledgeMiniProgramPagePath", "/pages/collection/collection.html?channel=kf"),
	}
	sendRsp, err := workweixin.BuildNewClient(workweixin.Client, corpId, aware.LoadOpenWorkAccessToken).
		SendMsgMiniProgram(msg.ExternalUserID, msg.OpenKFID, para)
	if err != nil || sendRsp.ErrCode != 0 {
		log.WithContext(ctx).Errorw("send wechat mini program fail", "err", err, "para", para, "sendRsp", sendRsp)
		return
	}

	record := aware.TransformEventSendResult(sendRsp)
	record.Content = para["title"]
	record.MessageId = messageId
	record.ChatId = chatId
	record.Type = aipb.AiRecordType_AI_RECORD_TYPE_KNOWLEDGE_MINI_PROGRAM
	now := time.Now()
	record.CreateDate = timestamppb.New(now)
	record.SendDate = timestamppb.New(now)
	_, err = client.AiByID(messageId).InsertAssistantMessageRecord(ctx, &aipb.ReqInsertAssistantMessageRecord{
		Records: []*aipb.AiChatSendRecord{record},
	})
	if err != nil {
		log.WithContext(ctx).Errorw("send wechat mini program UpdateSendRecord fail",
			"err", err, "record", record)
	}
}

// getMiniProgramMediaId
//
//	@Description:
//
// 首先从redis中获取有效的小程序封面的缓存mediaId
//
//	1 获取到直接返回
//	2 将cos上的封面下载到本地，并上传到微信上，返回微信返回的mediaId并缓存
func getMiniProgramMediaId(ctx context.Context, corpId string) string {
	key := fmt.Sprintf(
		config.GetStringOr("workweixin.ai.MiniProgramMediaIdKey", "{corpId:%s}:workweixin:ai:mediaKey"), corpId)
	result := xredis.Default.Get(ctx, key)
	if result != nil && result.Val() != "" {
		return result.Val()
	}
	cosUrl := config.GetStringOr("workweixin.ai.MiniProgramMediaCosUrl", "/ai/workweixin/miniprogram/miniprogram.png")
	uploadRsp, err := workweixin.BuildNewClient(workweixin.Client, corpId, aware.LoadOpenWorkAccessToken).
		UploadPublicCosMedia(cosUrl, workweixin.MsgTypeImage)
	if err != nil {
		log.WithContext(ctx).Errorw("getMiniProgramMediaId fail", "err", err, "corpId", corpId, "cosUrl", cosUrl)
		return ""
	}
	xredis.Default.SetEx(ctx, key, uploadRsp.MediaId, time.Duration(71)*time.Hour) // 默认71小时
	return uploadRsp.MediaId
}

func needSuggestQuestion(assistant *aipb.AssistantDetail, sender aware.MessageAnswerAware[workweixin.Message]) bool {
	// 判断用户是否开启了问题建议，开启了并且是用户的正常问答不是暗号 则推送建议问题
	return assistant.SuggestionConfig != nil && assistant.SuggestionConfig.Count > 0 && len(assistant.SuggestionConfig.Prompt) > 0 && assistant.SuggestionConfig.Enabled &&
		(sender.GetAnswerType() == aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL || // 正常问答
			sender.GetAnswerType() == aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION || // 重新问答
			sender.GetAnswerType() == aipb.QuestionAskType_QUESTION_ASK_TYPE_VOICE) // 语音问答
}

func needRemainTurnManual(assistant *aipb.AssistantDetail, sendFlag bool) bool {
	return sendFlag && assistant != nil && assistant.LiveAgentMsg != nil && assistant.LiveAgentMsg.Enable &&
		assistant.LiveAgentMsg.RemindPushMsg != nil && assistant.LiveAgentMsg.RemindPushMsg.Enable
}

// EventMessageTransformWithHash 将所有id转为hashID
func EventMessageTransformWithHash(m *aipb.EventChatMessage) *aipb.EventChatHashMessage {
	pbMessage := &aipb.EventChatHashMessage{
		ChatId:           handleHashId(m.ChatId),
		Id:               handleHashId(m.Id),
		CreateDate:       m.CreateDate,
		RatingScale:      m.RatingScale,
		Type:             m.Type,
		QuestionId:       handleHashId(m.QuestionId),
		Ugcs:             make([]*aipb.EventChatHashMessage_EventMessageUgc, 0),
		Text:             m.Text,
		Link:             m.Link,
		State:            int32(m.State),
		Lang:             m.Lang,
		AssistantId:      handleHashId(m.AssistantId),
		ShowType:         m.ShowType,
		Think:            m.Think,
		SuggestQuestions: m.SuggestQuestions,
		SuggestCount:     m.SuggestCount,
		WaitAnswer:       m.WaitAnswer,
		DocMatchPattern:  m.DocMatchPattern,
		ThinkDuration:    m.ThinkDuration,
		AnswerIndex:      m.AnswerIndex,
		PromptType:       m.PromptType,
		DocFinalQuery:    m.DocFinalQuery,
	}
	// 处理docs
	for _, v := range m.Docs {
		pbDoc := &aipb.EventChatHashMessage_EventMessageDoc{
			UgcId:       v.UgcId,
			UgcType:     v.UgcType,
			Id:          v.Id,
			RagFilename: v.RagFilename,
			DataType:    v.DataType,
			Reference:   v.Reference,
			FileName:    v.FileName,
		}
		for _, contributor := range v.Contributor {
			pbDoc.Contributor = append(pbDoc.Contributor, &aipb.EventChatHashMessage_EventContributor{
				Id:          handleHashId(contributor.Id),
				Text:        contributor.Text,
				Type:        contributor.Type,
				Level:       contributor.Level,
				IsPublished: contributor.IsPublished,
				FullName:    contributor.FullName,
			})
		}
		pbMessage.Docs = append(pbMessage.Docs, pbDoc)
	}
	// 处理ugc
	for _, messageUgc := range m.Ugcs {
		pbUgc := &aipb.EventChatHashMessage_EventMessageUgc{
			UgcType:   messageUgc.UgcType,
			IsUgcLink: messageUgc.IsUgcLink,
		}
		for _, ugc := range messageUgc.Cards {
			pbUgc.Cards = append(pbUgc.Cards, &aipb.EventChatHashMessage_EventMessageUgcCard{
				Id:      handleHashId(ugc.Id),
				Name:    ugc.Name,
				LogoUrl: ugc.LogoUrl,
			})
		}
		eventFilter := make([]*aipb.EventChatHashMessage_EventMessageFilter, 0, len(messageUgc.Filter))
		for _, item := range messageUgc.Filter {
			eventTag := make([]*aipb.EventChatHashMessage_EventMessageTag, 0, len(item.Tags))
			for _, tag := range item.Tags {
				eventTag = append(eventTag, &aipb.EventChatHashMessage_EventMessageTag{
					Id:           handleHashId(tag.Id),
					TaggableType: tag.TaggableType,
					Name:         tag.Name,
					Type:         tag.Type,
				})
			}
			eventFilter = append(eventFilter, &aipb.EventChatHashMessage_EventMessageFilter{
				Field: item.Field,
				Value: item.Value,
				Tags:  eventTag,
			})
		}
		pbUgc.Filter = eventFilter
		pbMessage.Ugcs = append(pbMessage.Ugcs, pbUgc)
	}
	return pbMessage
}

func handleHashId(id uint64) string {
	if id == 0 {
		return ""
	}
	encode, err := hashids.DefaultCodec.Encode(id)
	if err != nil {
		return ""
	}
	return encode
}

func reviewAnswerMessage(ctx context.Context, answer *aipb.EventChatHashMessage) {
	if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR ||
		answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR ||
		answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER {
		return //  预设的问答、系统错误、超时的回复内容则不用审核
	}

	message := &aipb.ChatMessage{
		Type: answer.Type,
		Text: answer.Text,
	}
	// 审核问题
	answer.RejectReason = ReviewChatMessage(ctx, message)
	// answer.Type = message.Type
}

// CleanTitleInvalidString 清理字符串
func CleanTitleInvalidString(input string) string {
	input = strings.ReplaceAll(input, "\n", " ")
	input = strings.ReplaceAll(input, "\\", " ")

	input = strings.ReplaceAll(input, "<", "")
	input = strings.ReplaceAll(input, ">", "")

	// 使用正则表达式将连续的两次空格替换为一个空格
	re := regexp.MustCompile(`\s{2,}`)
	input = re.ReplaceAllString(input, " ")

	input = strings.TrimSpace(input)

	return input
}
