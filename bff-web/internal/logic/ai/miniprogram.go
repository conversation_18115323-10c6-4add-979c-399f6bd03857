package ai

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"time"
	"unicode/utf8"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcrypto"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	uuid2 "github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
)

// StreamData 推流数据
type StreamData struct {
	Type        string `json:"type"`
	UnionID     string `json:"union_id"`
	UUID        string `json:"uuid"`
	Phone       string `json:"phone"`
	FullPhone   string `json:"full_phone"`
	OldUserName string `json:"old_username"`
	UserID      uint64 `json:"user_id"`
	IP          string `json:"ip"`
}

// UserSession 用户会话信息
type UserSession struct {
	LoginUser string `json:"LoginUser"`
}

// userResponse userResponse
type userResponse struct {
	Result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"result"`
	Data struct {
		TesJwtToken     string `json:"tes_jwttoken"`
		HasTeam         bool   `json:"has_team"`
		HasDocAuth      bool   `json:"has_doc_auth"`
		HasDocAuthRead  bool   `json:"has_doc_auth_read"`
		HasDocAuthWrite bool   `json:"has_doc_auth_write"`
		HasAssistant    bool   `json:"has_assistant"`
		HasOnceBindTip  bool   `json:"has_once_bind_tip"`
		User            struct {
			UserImage  string `json:"user_image"`
			UserHashId string `json:"user_hashid"`
			TimeZone   string `json:"time_zone"`
			AuthID     string `json:"auth_id"`
			UinToken   string `json:"uin_token"`
		} `json:"user"`
	} `json:"data"`
}

// RequestBody RequestBody
type RequestBody struct {
	Code string `json:"code"`
}

// InitUserDataResponse InitUserDataResponse
func InitUserDataResponse() userResponse {

	DataResonse := userResponse{
		Result: struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
		}{
			Msg: "",
		},
		Data: struct {
			TesJwtToken     string `json:"tes_jwttoken"`
			HasTeam         bool   `json:"has_team"`
			HasDocAuth      bool   `json:"has_doc_auth"`
			HasDocAuthRead  bool   `json:"has_doc_auth_read"`
			HasDocAuthWrite bool   `json:"has_doc_auth_write"`
			HasAssistant    bool   `json:"has_assistant"`
			HasOnceBindTip  bool   `json:"has_once_bind_tip"`
			User            struct {
				UserImage  string `json:"user_image"`
				UserHashId string `json:"user_hashid"`
				TimeZone   string `json:"time_zone"`
				AuthID     string `json:"auth_id"`
				UinToken   string `json:"uin_token"`
			} `json:"user"`
		}{
			TesJwtToken: "",

			User: struct {
				UserImage  string `json:"user_image"`
				UserHashId string `json:"user_hashid"`
				TimeZone   string `json:"time_zone"`
				AuthID     string `json:"auth_id"`
				UinToken   string `json:"uin_token"`
			}{
				UserImage: "",
			},
		},
	}

	return DataResonse
}

// authResponse authResponse
type authResponse struct {
	Result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"result"`
	Data struct {
		Token string `json:"token"`
	} `json:"data"`
}

// InitTokenResponse InitTokenResponse
func InitTokenResponse() authResponse {

	authResonse := authResponse{
		Result: struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
		}{
			Msg: "",
		},
		Data: struct {
			Token string `json:"token"`
		}{
			Token: "",
		},
	}

	return authResonse
}

// RequestWebViewBody RequestWebViewBody
type RequestWebViewBody struct {
	ActionType uint64 `json:"action_type"`
	Token      string `json:"token"`
	UinToken   string `json:"uin_token"`
}

// webviewResponse webviewResponse
type webviewResponse struct {
	Result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"result"`
	Data struct {
		HasUin     bool   `json:"has_uin"`
		HasUinSame bool   `json:"has_uin_same"`
		UserName   string `json:"user_name"`
	} `json:"data"`
}

// InitWebviewTokenResponse InitWebviewTokenResponse
func InitWebviewTokenResponse() webviewResponse {

	webviewResponse := webviewResponse{
		Result: struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
		}{
			Msg: "",
		},
		Data: struct {
			HasUin     bool   `json:"has_uin"`
			HasUinSame bool   `json:"has_uin_same"`
			UserName   string `json:"user_name"`
		}{
			HasUin:     false,
			HasUinSame: false,
		},
	}

	return webviewResponse
}

// sessionRouteConfig sessionRouteConfig
type sessionRouteConfig struct {
	AllowType       string   `json:"allow_type"`
	AllowPrefixFlag bool     `json:"allow_prefix_flag"`
	AllowPrefixURLs []string `json:"allow_prefix_urls"`
}

const (
	RedisSet = 1
	RedisGet = 2
)

// ConfuseString 混淆字符串
/*
  非敏感数据用，支持小程序端使用
*/
func ConfuseString(plainText string) string {
	shift := 3 // 固定移位值
	encrypted := make([]byte, len(plainText))

	for i := 0; i < len(plainText); i++ {
		encrypted[i] = plainText[i] + byte(shift) // 每个字符加固定值
	}

	return base64.StdEncoding.EncodeToString(encrypted)
}

// DecConfuseString 解码混淆字符串
func DecConfuseString(encryptedText string) (string, error) {

	shift := 3
	encrypted, err := base64.StdEncoding.DecodeString(encryptedText)
	if err != nil {
		return "", err
	}

	decrypted := make([]byte, len(encrypted))
	for i := 0; i < len(encrypted); i++ {
		decrypted[i] = encrypted[i] - byte(shift)
	}

	return string(decrypted), nil
}

// ConfuseStringByKey 混淆字符串，支持key
/*
  非敏感数据用，支持小程序端使用
*/
func ConfuseStringByKey(plaintext, key string) string {
	keyBytes := []byte(key)
	plaintextBytes := []byte(plaintext)

	encrypted := make([]byte, len(plaintextBytes))
	for i := range plaintextBytes {
		encrypted[i] = plaintextBytes[i] ^ keyBytes[i%len(keyBytes)]
	}

	return base64.URLEncoding.EncodeToString(encrypted)
}

// DecConfuseStringByKey 混淆解码字符串，支持key
/*
  非敏感数据用，支持小程序端使用
*/
func DecConfuseStringByKey(ciphertextBase64, key string) (string, error) {
	encrypted, err := base64.URLEncoding.DecodeString(ciphertextBase64)
	if err != nil {
		return "", err
	}

	keyBytes := []byte(key)

	decrypted := make([]byte, len(encrypted))
	for i := range encrypted {
		decrypted[i] = encrypted[i] ^ keyBytes[i%len(keyBytes)]
	}

	return string(decrypted), nil
}

func UnionIDRedis(ctx context.Context, unionID string, action int) string {

	cryptoUnionID := xcrypto.Md5(xcrypto.Md5(unionID))
	key := fmt.Sprintf("miniprogram:unionid:%s", cryptoUnionID)

	switch action {
	case RedisSet:
		xredis.Default.Set(ctx, key, unionID, 7300*time.Second)
	case RedisGet:
		key = fmt.Sprintf("miniprogram:unionid:%s", unionID)
		return xredis.Default.Get(ctx, key).Val()
	}

	return cryptoUnionID
}

// GetAssistants 获取用户助手
/**
  has_team: bool
  has_assistant: bool
*/
func GetAssistants(ctx context.Context, userId uint64, teamId uint64) (bool, bool) {

	if teamId > 0 {
		result, err := client.AiNational.GetAssistants(ctx, &aipb.ReqGetAssistants{
			Filter: &aipb.ReqGetAssistants_Filter{
				TeamId: []uint64{teamId},
			},
		})
		if err != nil {
			log.WithContext(ctx).Infow("GetMiniProgramAuth GetAssistants err")
		}

		if len(result.Assistants) > 0 {
			return true, true
		} else {
			return true, false
		}

	}

	if userId > 0 {
		result, err := client.AiNational.GetAssistants(ctx, &aipb.ReqGetAssistants{
			Filter: &aipb.ReqGetAssistants_Filter{
				UserId: []uint64{userId},
			},
		})
		if err != nil {
			log.WithContext(ctx).Infow("GetMiniProgramAuth GetAssistants err")
		}

		if len(result.Assistants) > 0 {
			return false, true
		}

	}

	return false, false

}

// BindUserPhoneReplaceTempAccount 绑定手机号，替换临时账号
/*
  @param phone 新手机号（要绑定的账号
  @countryCode 国家码
  @param userName 要替换的临时用户名

  @return userId 用户ID
  @return bool 账号是否原来存在，true 存在
  @return string 用户名
*/
func BindUserPhoneReplaceTempAccount(ctx context.Context, phone string, countryCode string, userName string) (uint64, bool, string, error) {

	uuidStr := uuid2.New().String()

	log.WithContext(ctx).Infow("BindUserPhoneReplaceTempAccount data ", "phone", phone,
		"countryCode", countryCode, "userName", userName, "uuid", uuidStr)

	if len(phone) == 0 {
		return 0, false, "", errors.New("phone is empty")
	}

	if len(countryCode) == 0 {
		countryCode = "86"
	}
	fullPhone := fmt.Sprintf("+%s-%s", countryCode, phone)

	data := StreamData{
		Type:        "CreateMiniProgramPhoneTempUser",
		UUID:        uuidStr,
		Phone:       phone,
		OldUserName: userName,
		FullPhone:   fullPhone,
		IP:          logic.GetClientIP(ctx),
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return 0, false, "", errors.New("json.Marshal err")
	}

	// 推送手机号注册
	PushV1Call(ctx, string(jsonData))

	// 获取用户ID
	userId, err := GetV1PushDataUserId(ctx, uuidStr)
	if err != nil {
		return userId, false, "", errors.New(err.Error())
	}

	// 获取用户状态，是否用户已经存在
	userOldExist, err := GetV1PushDataUserStatus(ctx, uuidStr)
	if err != nil {
		return userId, false, "", errors.New(err.Error())
	}

	if userOldExist {
		userData, err := client.IamClient.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{Id: []uint64{userId}})
		if err != nil {
			return userId, userOldExist, "", err
		}

		return userId, userOldExist, userData.UserSet[0].Username, nil
	}

	return userId, userOldExist, "", nil

}

// BindUserAccountReplaceTempAccount 绑定tanlive正常用户，替换临时账号
/*
  @param userId tanlive正常用户ID
  @param userName 要替换的临时用户名

  @return userId 用户ID
  @return string uuid
*/
func BindUserAccountReplaceTempAccount(ctx context.Context, userId uint64, userName string) (uint64, string, error) {

	uuidStr := uuid2.New().String()

	log.WithContext(ctx).Infow("BindUserAccountReplaceTempAccount data ", "userId", userId,
		"userName", userName, "uuid", uuidStr)

	data := StreamData{
		Type:        "CreateMiniProgramAccountTempUser",
		UUID:        uuidStr,
		UserID:      userId,
		OldUserName: userName,
		IP:          logic.GetClientIP(ctx),
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return 0, uuidStr, errors.New("json.Marshal err")
	}

	PushV1Call(ctx, string(jsonData))

	userId, err = GetV1PushDataUserId(ctx, uuidStr)
	if err != nil {
		return userId, uuidStr, err
	}

	return userId, uuidStr, nil

}

// LoadUserInfoCreateTempAccount 加载用户信息，如果没有则创建临时账号
func LoadUserInfoCreateTempAccount(ctx context.Context, unionID string) (*iampb.UserInfo, error) {
	user, err := client.IamClient.GetUserByUnionID(ctx, &iampb.ReqGetUserByUnionID{
		UnionId: unionID,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("LoadUserInfo GetUsersPage err", "err", err)
		return nil, errors.New("internal GetUsersPage")
	}

	uuidStr := uuid2.New().String()

	log.WithContext(ctx).Infow("LoadUserInfoCreateTempAccount", "unionID", unionID, "uuid", uuidStr)

	// 用户不存在则创建临时账号
	// 推送v1处理
	if user == nil || user.UserSet == nil {

		data := StreamData{
			Type:    "CreateWeixinTempAccount",
			UnionID: unionID,
			UUID:    uuidStr,
			IP:      logic.GetClientIP(ctx),
		}

		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, errors.New("json.Marshal err")
		}

		PushV1Call(ctx, string(jsonData))

		userId, err := GetV1PushDataUserId(ctx, uuidStr)
		if err != nil {
			return nil, errors.New(err.Error())
		}

		userData, err := client.IamClient.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{Id: []uint64{userId}})
		if err != nil {
			return nil, err
		}

		return userData.UserSet[0], nil
	}

	return user.UserSet, nil
}

// LoadUserInfo 加载用户信息
func LoadUserInfo(ctx context.Context, unionID string) (*iampb.UserInfo, error) {
	user, err := client.IamClient.GetUserByUnionID(ctx, &iampb.ReqGetUserByUnionID{
		UnionId: unionID,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("LoadUserInfo GetUsersPage err", "err", err)
		return nil, errors.New("internal GetUsersPage")
	}

	if user == nil || user.UserSet == nil {
		log.WithContext(ctx).Errorw("LoadUserInfo  user nil err", "unionID", unionID)
		return nil, errors.New("user nil")
	}

	return user.UserSet, nil
}

func LoadUserInfoById(ctx context.Context, userID uint64) (*iampb.UserInfo, error) {
	user, err := client.IamClient.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
		Id: []uint64{userID},
	})
	if err != nil {
		log.WithContext(ctx).Errorw("LoadUserInfoById GetUsersByKey err", "err", err)
		return nil, errors.New("internal GetUsersPage")
	}

	if user == nil || user.UserSet == nil {
		log.WithContext(ctx).Infow("LoadUserInfoById  nil err", "userID", userID)
		return nil, nil
	}

	return user.UserSet[0], nil
}

// CheckUserHavePhone 检查用户是否绑定手机
func CheckUserHavePhone(ctx context.Context, userId uint64) (bool, error) {

	phoneData, err := client.IamClient.CheckUserHavePhone(ctx, &iampb.ReqCheckUserHavePhone{UserId: userId})
	if err != nil {
		return phoneData.HavePhone, err
	}

	return phoneData.HavePhone, nil
}

// BindUserPhone 绑定用户手机号
/*
  @return uint64 用户ID
  @return boolean 是否绑定手机号
*/
func BindUserPhone(ctx context.Context, userId uint64, phone string) (uint64, bool, error) {

	uuidStr := uuid2.New().String()
	log.WithContext(ctx).Infow("BindUserPhone data ", "phone", phone, "userId", userId, "uuid", uuidStr)

	data := StreamData{
		Type:   "ModifyMiniProgramAccountPhone",
		UUID:   uuidStr,
		Phone:  phone,
		UserID: userId,
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return 0, false, errors.New("json.Marshal err")
	}

	// 推送手机号修改
	PushV1Call(ctx, string(jsonData))

	userId, err = GetV1PushDataUserId(ctx, uuidStr)
	if err != nil {
		return userId, false, errors.New(err.Error())
	}

	// 获取用户手机状态是否存在
	userOldExist, err := GetV1PushDataUserStatus(ctx, uuidStr)
	if err != nil {
		return userId, false, errors.New(err.Error())
	}

	if userOldExist {
		return userId, true, nil
	}

	return userId, false, nil
}

func UpdateUserInfoUnionId(ctx context.Context, userId uint64, unionId string) error {

	log.WithContext(ctx).Infow("UpdateUserInfoUnionId data", "unionId", unionId, "userId", userId)

	if len(unionId) == 0 {
		return nil
	}

	IamClient := client.IamClient

	if client.IsInternationalUserID(userId) {
		IamClient = client.IamInternational
	}

	_, err := IamClient.UpdateUserInfo(ctx, &iampb.ReqUpdateUserInfo{
		User: &iampb.UserInfo{
			Id:      userId,
			UnionId: unionId,
		},
		Mask: &fieldmaskpb.FieldMask{Paths: []string{"weixin_union_id"}},
	})

	if err != nil {
		return err
	}

	return nil
}

func LoadSession(ctx context.Context, sessionID string, userId uint64) error {

	// 创建带路由限制的特殊session，用于限制小程序路由， 暂时设置v1相关的敏感接口
	session, err := xsession.Start(ctx, sessionID)
	if err != nil {
		log.WithContext(ctx).Errorw("LoadSession xsession.Start err", "err", err)
		return errors.New("LoadSession xsession.Start err")
	}

	session.Set("allow_prefix", sessionRouteConfig{
		AllowType:       "miniprogram",
		AllowPrefixFlag: config.GetBool("miniprogram.allow_prefix"),
		AllowPrefixURLs: config.GetStringSlice("miniprogram.allow_prefix_urls"),
	})

	session.SetLoginUser(userId)

	if err := xsession.Save(ctx, session); err != nil {
		log.WithContext(ctx).Errorw("LoadSession xsession.Save err", "err", err)
		return errors.New("LoadSession xsession.Save err")
	}

	log.WithContext(ctx).Infow("LoadSession xsession.Save success", "session", session)

	return nil
}

// LoadUnionIdBindTip 加载unionId绑定提示
/*
  unionId未被其他账号绑定，则返回true
  当前unionID是临时账号使用

  @return bool unionId未被其他账号绑定
  @return bool 当前unionID是临时账号使用
*/
func LoadUnionIdBindTip(ctx context.Context, userData *iampb.UserInfo, unionId string) (bool, bool) {

	// 当前账号已经存在unionId，则无需绑定
	if len(userData.UnionId) > 0 {
		return false, false
	}

	// 查找unionId未被其他账号绑定， 如果为空，则提示绑定true
	user, err := client.IamClient.GetUserByUnionID(ctx, &iampb.ReqGetUserByUnionID{UnionId: unionId})
	if err != nil {
		return false, false
	}

	if user == nil || user.UserSet == nil {
		return true, false
	}

	if user.UserSet.Id == 0 {
		return true, false
	}

	// 临时账号
	if IsWechatUnionID(user.UserSet.UnionId) && IsWechatUnionID(user.UserSet.Username) {
		return false, true
	}

	return false, false
}

// LoadEditReadDocAuth 加载编辑知识库权限
func LoadEditReadDocAuth(ctx context.Context, userId, teamId uint64) bool {
	// 个人账号默认有权限
	if teamId == 0 && userId > 0 {
		return true
	}

	return loadAIDocApiPermission(ctx, "/ai/collection/import_qas", userId, teamId)

}

// LoadEditViewDocAuth 加载查看知识库权限
func LoadEditViewDocAuth(ctx context.Context, userId, teamId uint64) bool {
	// 个人账号默认有权限
	if teamId == 0 && userId > 0 {
		return true
	}

	return loadAIDocApiPermission(ctx, "/ai/collection/list_qa", userId, teamId)

}

func loadAIDocApiPermission(ctx context.Context, path string, userID, teamID uint64) bool {
	result, err := client.IamClient.CheckApiPermission(ctx, &iampb.ReqCheckApiPermission{
		UserId: userID,
		Path:   "/ai/collection/import_qas",
		AsTeam: true,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("loadEditViewDocAuth err", "err", err)
		return false
	}
	if !result.Allowed {
		log.WithContext(ctx).Infow("loadEditViewDocAuth not allowed", "userId", userID, "teamId", teamID,
			"result", result)
		return false
	}

	return true
}

// PushV1Call 推送v1消费
func PushV1Call(ctx context.Context, pushJson string) {
	args := &redis.XAddArgs{
		Stream: config.GetStringOr("miniprogram.redis_stream", "miniprogram_stream"),
		Values: map[string]interface{}{
			"data": pushJson,
		},
	}

	xredis.Default.XAdd(ctx, args)
}

// GetV1PushDataUserId 获取v1处理结果，用户ID
func GetV1PushDataUserId(ctx context.Context, uuid string) (uint64, error) {

	for i := 0; i < 20; i++ {
		// 200毫秒间隔询问数据
		time.Sleep(time.Millisecond * 200)

		userId, err := xredis.Default.Get(ctx, uuid).Uint64()

		if userId == 0 {
			userId, err = xredis.Use("ai").Get(ctx, uuid).Uint64()
		}

		if err != nil {
			log.WithContext(ctx).Errorw("GetV1PushDataUserId", "err", err, "uuid", uuid)
		}

		if userId > 0 {
			return userId, nil
		}

	}

	return 0, errors.New("GetV1PushDataUserId timeout")
}

// GetV1PushDataUserStatus 获取v1处理结果，用户状态
/*
   如果有数据，则说明原来存在此账号
*/
func GetV1PushDataUserStatus(ctx context.Context, uuid string) (bool, error) {

	userId, err := xredis.Default.Get(ctx, fmt.Sprintf("%s_status", uuid)).Uint64()

	if userId == 0 {
		userId, err = xredis.Use("ai").Get(ctx, fmt.Sprintf("%s_status", uuid)).Uint64()
	}

	if err != nil {
		log.WithContext(ctx).Errorw("GetV1PushDataUserStatus", "err", err, "uuid", uuid)
	}

	if userId > 0 {
		return true, nil
	}

	return false, nil
}

// GetUserDataByToken 通过token获取用户ID
func GetUserDataByToken(ctx context.Context, token string) (uint64, error) {

	log.WithContext(ctx).Infow("GetUserDataByToken Redis Pool Stats", "defaultPool", xredis.Default.PoolStats(),
		"aiPool", xredis.Use("ai").PoolStats(),
	)

	userString, _ := xredis.Default.Get(ctx, fmt.Sprintf("bff-web:%s", token)).Result()

	if len(userString) == 0 {
		userString, _ = xredis.Use("ai").Get(ctx, fmt.Sprintf("bff-web:%s", token)).Result()
	}

	log.WithContext(ctx).Infow("GetUserDataByToken", "userString", userString, "key", fmt.Sprintf("bff-web:%s", token))

	if len(userString) > 0 {
		var userData UserSession

		if err := json.Unmarshal([]byte(userString), &userData); err != nil {
			return 0, err
		}

		if userData.LoginUser == "" {
			return 0, nil
		}

		userId, err := strconv.ParseUint(userData.LoginUser, 10, 64)
		if err != nil {
			return 0, err
		}

		return userId, nil
	}

	return 0, nil
}

// LoadUserAssistantLimitCache 加载用户手机号是否在助手中限制
/*
   优先加载缓存
   大多数助手无需配置白名单，仅缓存未限制，提供缓存命中率
   如果手机号为空，默认加载默认手机号

   @userId 用户ID
   @assistantId 助手ID

   @return bool true 可继续下个步骤
*/
func LoadUserAssistantLimitCache(ctx context.Context, userId uint64, assistantId uint64) (bool, error) {
	if userId == 0 || assistantId == 0 {
		return false, errors.New("userId or assistantId is empty")
	}

	// 先从redis中获取，如果存在，则直接返回true
	key := fmt.Sprintf("user_assistant_limit:%d:%d", assistantId, userId)
	if xredis.Default.Exists(ctx, key).Val() > 0 {
		return true, nil
	}

	user, err := LoadUserInfoById(ctx, userId)
	if err != nil {
		return false, err
	}

	if len(user.Phone) == 0 {
		user.Phone = config.GetStringOr("miniprogram.default_phone", "+86-18888888888")
	}

	allow, err := client.AiNational.CheckAssistantAllowlist(ctx, &aipb.ReqCheckAssistantAllowlist{
		AssistantId: assistantId,
		TypePara: &aipb.ReqCheckAssistantAllowlist_PhoneTypePara{
			PhoneTypePara: &aipb.ReqCheckAssistantAllowlist_PhonePara{
				Phone: user.Phone,
			},
		},
	})
	if err != nil {
		return false, err
	}

	if allow.Allowed {
		xredis.Default.Set(ctx, key, 1, config.GetDurationOr("miniprogram.assistant_expire_time", 600)*time.Second)
	}

	log.WithContext(ctx).Infow("LoadUserAssistantLimitCache data", "Allowed", allow.Allowed, "userId", userId,
		"assistantId", assistantId, "phone", user.Phone)

	return allow.Allowed, nil
}

// LoadUserPhoneAssistantLimit 加载用户手机号是否在助手中限制
/*
  如果手机号为空，默认加载默认手机号
*/
func LoadUserPhoneAssistantLimit(ctx context.Context, userId uint64, assistantId uint64, phone string) (bool, error) {

	if len(phone) == 0 {
		phone = config.GetStringOr("miniprogram.default_phone", "+86-18888888888")
	}

	allow, err := client.AiNational.CheckAssistantAllowlist(ctx, &aipb.ReqCheckAssistantAllowlist{
		AssistantId: assistantId,
		TypePara: &aipb.ReqCheckAssistantAllowlist_PhoneTypePara{
			PhoneTypePara: &aipb.ReqCheckAssistantAllowlist_PhonePara{
				Phone: phone,
			},
		},
	})
	if err != nil {
		return false, err
	}

	log.WithContext(ctx).Infow("LoadUserPhoneAssistantLimit data", "Allowed", allow.Allowed, "userId", userId,
		"assistantId", assistantId, "phone", phone)

	return allow.Allowed, nil
}

// SetUserIdNewLoginToken 设置用户ID新的登录态
func SetUserIdNewLoginToken(ctx context.Context, userId uint64) (string, error) {
	sessionID := xsession.NewID()
	session, err := xsession.Start(ctx, sessionID)
	if err != nil {
		return "", err
	}
	session.SetLoginUser(userId)

	if err := xsession.Save(ctx, session); err != nil {
		log.WithContext(ctx).Errorw("BindSelectedAccount xsession.Save err", "err", err)
		return "", err
	}

	return sessionID, nil
}

var (
	wechatMiniprogramUnionRegex    = regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
)

// IsWechatUnionID 判断是否是微信 union_id
func IsWechatUnionID(unionID string) bool {
	// 检查长度
	if len(unionID) < 26 || len(unionID) > 34 {
		return false
	}

	// 检查是否只包含字母、数字和_-
	matched := wechatMiniprogramUnionRegex.MatchString(unionID)
	if  !matched {
		return false
	}

	return true
}

// AccountPhoneDesensitize 通用脱敏函数
func AccountPhoneDesensitize(input string) string {
	length := utf8.RuneCountInString(input)

	if length >= 6 && length <= 11 {
		return input[:2] + "****" + input[len(input)-4:]
	}

	if length >= 4 && length <= 32 {
		runes := []rune(input)
		return string(runes[0]) + "***" + string(runes[length-2:])
	}

	// 如果不符合规则，返回原值
	return input
}
