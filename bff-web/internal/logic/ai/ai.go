package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp/response"
	rp "e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai/aware"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/support"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	reviewpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	mclient "github.com/asim/go-micro/v3/client"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	AIChatPromptTypeSimple  string = "simple"
	AIChatPromptTypeComplex string = "complex"
	AIChatPromptTypeChat    string = "chat"
)

type RequestChatSubscribeBody struct {
	AssistantId string `json:"assistant_id"`
	Text        string `json:"text"`
	ChatId      string `json:"chat_id"`
}

// ChatSubscribeResponse ...
type ChatSubscribeResponse struct {
	Result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"result"`
	Data struct {
		ChatId     uint64 `json:"chat_id"`
		MessageId  uint64 `json:"message_id"`
		QuestionId uint64 `json:"question_id"`
	} `json:"data"`
}

func ChatSubscribeResponseError(msg string, chatId uint64, questionId uint64) xhttp.Response {
	var code int
	if msg != "" {
		code = 500
	}
	cr := ChatSubscribeResponse{
		Result: struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
		}{
			Code: code,
			Msg:  msg,
		},
		Data: struct {
			ChatId     uint64 `json:"chat_id"`
			MessageId  uint64 `json:"message_id"`
			QuestionId uint64 `json:"question_id"`
		}{
			ChatId:     chatId,
			MessageId:  0,
			QuestionId: questionId,
		},
	}

	return response.JSON(cr)
}

// CustomQuestionDeal 命令消息处理
func CustomQuestionDeal(ctx context.Context, user *iampb.UserInfo, question *aipb.ChatMessage,
	assistant *aipb.AssistantDetail, lang string) (*aipb.ChatMessage, error) {
	var (
		customerServiceFlag bool
		clearHistoryFlag    bool
		answerText          string
		liveTexts           = config.GetStringSliceOr("llm.set.default.customerServiceQuestion",
			[]string{"人工", "customer service", "转人工客服", "Customer service", "Customer Service", "manual", "Manual"})
		clearTexts = config.GetStringSliceOr("llm.set.default.clearHistoryQuestion",
			[]string{"清空上下文", "Clear context", "Clear the context", "清空", "清空聊天记录", "清空记录", "新对话"})
		cmdTexts = config.GetStringSliceOr("llm.set.default.cmdKeywords",
			[]string{"指令", "暗号", "互动暗号", "interactive keywords", "Interactive Keywords", "Interactive keywords"})
	)
	var answer *aipb.ChatMessage
	var err error

	if xslice.IsContainsString(clearTexts, question.Text) {
		clearHistoryFlag = true
	} else if xslice.IsContainsString(liveTexts, question.Text) {
		customerServiceFlag = true
	}

	//if !customerServiceFlag { // 没有命中关键词，再判断会话是否已经转人工 // 2.11 web端和小程序端不能转人工了
	//	chat, err := client.AiByID(question.ChatId).GetChatDetail(ctx, &aipb.ReqGetChatDetail{
	//		Id: question.ChatId,
	//	})
	//	if err != nil {
	//		return nil, err
	//	}
	//
	//	if chat.ChatDetail.SupportType == aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT {
	//		customerServiceFlag = true
	//	}
	//}

	msgType := aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT_CODE

	if customerServiceFlag /*&& assistant.LiveAgentMsg != nil && assistant.LiveAgentMsg.Enable*/ { //  命中转人工
		answerText = getCustomerServiceMsg(user, assistant, lang)
	} else if clearHistoryFlag { // 命中清空上下文
		answerText = config.GetStringOr("workweixin.ai.clearContextAnswer", "好的，我已经清空了之前的上下文。如果你有其他问题可随时问我。")
		msgType = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_CLEAR_HISTORY
	}
	if xslice.IsContainsString(cmdTexts, question.Text) { // 命中暗号
		if answerText = aware.WarpWechatCodeToString(assistant, false); len(answerText) > 0 {
			msgType = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER
		}
	}
	log.WithContext(ctx).Debugw("CustomServiceDeal Info", "chatID", question.ChatId, "customerServiceFlag", customerServiceFlag, "clearHistoryFlag", clearHistoryFlag, "answerText", answerText)
	l := MessageDbLogic{}
	if answerText != "" {
		answer, err = l.CreateSimpleTextAnswer(ctx, question, user, answerText, msgType)
		if err != nil {
			return nil, err
		}
		// publish answer
		time.Sleep(2 * time.Second)
		eventMsg := TransformAIMessageToEventMessage(answer, nil)
		err = PublishChatEventHashMessage(ctx, eventMsg, user.Id, question.Id, question.PublishHashId)
	}
	return answer, err
}

// getCustomerServiceMsg 获取回复人工的消息
func getCustomerServiceMsg(user *iampb.UserInfo, assistant *aipb.AssistantDetail, lang string) string {
	//m := config.GetStringMapString("llm.set.default.customerServiceAnswerMap")
	//if v, ok := m[fmt.Sprintf("%d", assistant.Id)]; ok && miniProgram && len(v) > 0 {
	//	answerText = v
	//} else {
	//	answerText = config.GetStringOr("llm.set.default.customerServiceAnswer",
	//		"嘿嘿，我可不是人类哟~如果你需要人工客服，请打开微信扫一扫下方二维码，我们的客服小C会尽快回复你哒！")
	//}
	if assistant == nil || assistant.LiveAgentMsg == nil {
		return ""
	}
	var reply *aipb.AssistantKefuReply_Reply
	if assistant.LiveAgentMsg.Reply == nil { //  此配置如果为空则需要填充默认值
		qcPictureUrl := config.GetStringOr("llm.set.default.customerServiceQcUrl",
			"/static/sites/qrcode/chat.png")
		assistant.LiveAgentMsg.Reply = &aipb.AssistantKefuReply{
			MainlandZh: &aipb.AssistantKefuReply_Reply{
				ReplyMessage: "嘿嘿，我可不是人类哟~如果你需要人工客服，请打开微信扫一扫下方二维码，我们的客服会尽快回复你哒！",
				ImgUrl:       qcPictureUrl,
			},
			MainlandEn: &aipb.AssistantKefuReply_Reply{
				ReplyMessage: "Hey, I'm not a human~If you need human customer service, please open WeChat and " +
					"scan the QR code below, and our customer support team will reply to you as soon as possible!",
				ImgUrl: qcPictureUrl,
			},
			NonMainlandZh: &aipb.AssistantKefuReply_Reply{
				ReplyMessage: "嘿嘿，我可不是人类哟~如果你需要人工客服，请联系****************，我们的客服会尽快回复你哒！",
				ImgUrl:       "",
			},
			NonMainlandEn: &aipb.AssistantKefuReply_Reply{
				ReplyMessage: "Hey, please note that I'm not a human.If you need human customer support, please " +
					"contact <NAME_EMAIL>. Our customer support team will respond to you as soon as possible.",
				ImgUrl: "",
			},
			EnableCustom: false,
		}
	}

	if client.IsInternationalUserID(user.Id) { // 国际用户
		if lang == "en" {
			reply = assistant.LiveAgentMsg.Reply.NonMainlandEn
		} else {
			reply = assistant.LiveAgentMsg.Reply.NonMainlandZh
		}
	} else { // 国内用户
		if lang == "en" {
			reply = assistant.LiveAgentMsg.Reply.MainlandEn
		} else {
			reply = assistant.LiveAgentMsg.Reply.MainlandZh
		}
	}
	return fillingCustomerServiceMsg(reply, assistant.LiveAgentMsg.Reply.Custom,
		assistant.LiveAgentMsg.Reply.EnableCustom, user.Id)
}

// fillingCustomerServiceMsg ...
func fillingCustomerServiceMsg(reply *aipb.AssistantKefuReply_Reply, custom []*aipb.AssistantKefuReply_Custom,
	enable bool, userId uint64) string {
	result := reply.ReplyMessage

	if enable && len(custom) > 0 { // 替换自定义超链接
		url := "<a href=\"%s\">%s</a>"
		for _, v := range custom {
			result = strings.ReplaceAll(result, v.Url, fmt.Sprintf(url, v.Url, v.Text))
		}
	}

	if len(reply.ImgUrl) == 0 {
		return result
	}

	imgUrl := config.GetString("oss.publicCdn") + reply.ImgUrl
	sceneUserId := fmt.Sprintf("scene_param=%d", userId)
	// 追加人工二维码 // 拼接参数
	if strings.Contains(imgUrl, "?") {
		imgUrl += "&" + sceneUserId
	} else {
		imgUrl += "?" + sceneUserId
	}
	result = result + "\n" + fmt.Sprintf("<img src=\"%s\" class=\"t-qrcode-img\">", imgUrl)

	return result
}

func marshalRejectReason(rejectReason *reviewpb.RejectReason) string {
	if rejectReason == nil {
		return ""
	}
	data, _ := json.Marshal(rejectReason)
	return xstrings.FromBytes(data)
}

// DescribeMessagesDocs 批量获取messages docs
func DescribeMessagesDocs(ctx context.Context, messages []*aipb.ChatMessage, hitCount bool) ([]*aipb.ChatMessageDoc, error) {
	docNames := ExtractMessageDocNames(messages)
	var docs []*aipb.ChatMessageDoc
	if len(docNames) > 0 {
		docRsp, err := client.AiNational.DescribeMessageDocs(ctx, &aipb.ReqDescribeMessageDocs{DocNames: strings.Join(docNames, ","), HitCount: hitCount})
		if err != nil {
			return docs, err
		}
		docs = docRsp.Docs
		var contributors []*aipb.Contributor
		for _, doc := range docs {
			contributors = append(contributors, doc.Contributor...)
		}
		_, err = GeAiDoctContributorShowInfo(ctx, contributors...)
		if err != nil {
			return nil, err
		}
	}
	return docs, nil
}

// publishToTopic 推送topic
func publishToTopic(ctx context.Context, hashId string, event *aipb.ChatPushMessage) error {
	jsonData, err := json.Marshal(event)
	if err != nil {
		return err
	}
	topic := GetChatChannelTopic(hashId)
	rp.Default.Publish(ctx, topic, string(jsonData))
	log.WithContext(ctx).Infow("bff publishChatEventHashMessage msg", "topic", topic, "hashId", hashId, "msg", string(jsonData))
	return nil
}

// PublishChatEventHashMessage 推送EventHashMessage（保存后的回答、建议问题、命中暗号生成的回答）
func PublishChatEventHashMessage(ctx context.Context, msg *aipb.EventChatMessage, userId uint64, questionId uint64, hashId string) error {
	hashMessage := EventMessageTransformWithHash(msg)
	r, err := json.Marshal(hashMessage)
	if err != nil {
		return err
	}

	if err = publishToTopic(ctx, hashId, &aipb.ChatPushMessage{
		Content:     string(r),
		HashId:      hashId,
		AnswerIndex: msg.AnswerIndex,
		IsHashEvent: true,
	}); err != nil {
		return err
	}

	// 更新question状态
	if questionId > 0 {
		_, err = client.AiByID(questionId).UpdateChatMessage(ctx, &aipb.ReqUpdateChatMessage{
			Id: questionId, State: uint32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND)})
	}
	return nil
}

//func PublishHashMsgToTopic(ctx context.Context, answer *aipb.ChatMessage) error {
//
//	return PublishChatEventHashMessage(ctx, eventMsg, answer.CreateBy, answer.QuestionId, answer.PublishHashId)
//}

// ReviewChatMessage 审核ChatMessage信息
func ReviewChatMessage(ctx context.Context, message *aipb.ChatMessage) string {
	if len(message.Text) == 0 {
		return ""
	}
	review, err := client.ReviewClient.CreateTextReview(ctx, &reviewpb.ReqCreateTextReview{
		Text:       message.Text,
		Strategy:   reviewpb.ReviewStrategy_REVIEW_STRATEGY_TEXT_AI_CHAT,
		BucketType: reviewpb.CosBucketType_COS_BUCKET_TYPE_PUBLIC,
	})
	if err != nil {
		return ""
	}
	return marshalRejectReason(review.RejectReason)
}

func GetChatScribeReqBody(req *xhttp.Request) (*bffaipb.ReqChatSubscribe, error) {
	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		return nil, err
	}

	reqBody := &bffaipb.ReqChatSubscribe{}
	if err := json.Unmarshal(body, &reqBody); err != nil {
		return nil, err
	}
	if reqBody.HashId == "" {
		return nil, fmt.Errorf("hashId is empty")
	}

	return reqBody, nil
}

// ExtractMessageDocNames 从消息中提取所有文档名称
func ExtractMessageDocNames(messages []*aipb.ChatMessage) []string {
	docNamesSet := make(map[string]struct{})
	for _, m := range messages {
		if m.QuestionId > 0 {
			names := m.DocNames
			for _, name := range names {
				if name != "" {
					docNamesSet[name] = struct{}{}
				}
			}
		}
	}
	docNames := make([]string, 0, len(docNamesSet))
	for name := range docNamesSet {
		docNames = append(docNames, name)
	}
	return docNames
}

func TransformAIMessageToEventMessage(m *aipb.ChatMessage, docs []*aipb.ChatMessageDoc) *aipb.EventChatMessage {
	return TransformAIMessageToEventMessageWithDocText(m, docs, false)
}

func TransformAIMessageToEventMessageWithDocText(m *aipb.ChatMessage, docs []*aipb.ChatMessageDoc, withDocText bool) *aipb.EventChatMessage {
	messageDocs := make([]*aipb.ChatMessageDoc, 0, len(m.DocNames))
	referenceMap := make(map[string]*aipb.DocReference)
	for _, doc := range docs {
		if xstrings.In(doc.RagFilename, m.DocNames) {
			md := &aipb.ChatMessageDoc{
				UgcType:     doc.UgcType,
				UgcId:       doc.UgcId,
				Contributor: doc.Contributor,
				DataType:    doc.DataType,
				FileName:    doc.FileName,
				DataSource:  doc.DataSource,
			}
			if withDocText {
				md.IndexText = doc.IndexText
				md.Text = doc.Text
			}
			var newReference []*aipb.DocReference
			for _, reference := range doc.Reference {
				if reference.Id > 0 {
					if referenceMap[fmt.Sprintf("%d", reference.Id)] != nil {
						continue
					}
					referenceMap[fmt.Sprintf("%d", reference.Id)] = reference
				} else if len(reference.Name) > 0 {
					if referenceMap[reference.Name] != nil {
						continue
					}
					referenceMap[reference.Name] = reference
				} else if len(reference.Text) > 0 {
					if referenceMap[reference.Text] != nil {
						continue
					}
					referenceMap[reference.Text] = reference
				}
				newReference = append(newReference, reference)
			}
			md.Reference = newReference
			messageDocs = append(messageDocs, md)
		}
	}

	eventMsg := &aipb.EventChatMessage{
		Id:          m.Id,
		ChatId:      m.ChatId,
		Text:        m.Text,
		Type:        m.Type,
		RatingScale: m.RatingScale,
		Docs:        messageDocs,
		Link:        m.Link,
		QuestionId:  m.QuestionId,
		Ugcs:        m.Ugcs,
		CreateDate:  m.CreateDate,
		Lang:        m.Lang,
		AssistantId: m.AssistantId,
		State:       m.State,
		ProcessTime: &basepb.TimeRange{
			Start: m.StartTime,
			End:   m.EndTime,
		},
		SqlQuery:          m.SqlQuery,
		AskType:           m.AskType,
		SuggestQuestions:  m.SuggestQuestion,
		DocFinalQuery:     m.FinalQuery,
		StartTime:         m.StartTime,
		EndTime:           m.EndTime,
		ImageUrl:          m.ImageUrl,
		ShowType:          m.ShowType,
		DocMatchPattern:   m.DocMatchPattern,
		SuggestionMode:    m.SuggestionMode,
		Think:             m.Think,
		WaitAnswer:        m.WaitAnswer,
		AnswerIndex:       m.AnswerIndex,
		PromptType:        m.PromptType,
		ThinkDuration:     m.ThinkDuration,
		LastOperationType: m.LastOperationType,
		FeedbackId:        m.FeedbackId,
		HasUserFeedback:   m.HasUserFeedback,
		IsFileReady:       m.IsFileReady,
		Files:             m.Files,
	}
	if m.Task != nil {
		eventMsg.IsAgentCommand = true
	}
	return eventMsg
}

type CollectionSnapshot struct {
	StartTime   time.Time                    `json:"startTime,omitempty"`
	EndTime     time.Time                    `json:"endTime,omitempty"`
	Items       []*aipb.SearchCollectionItem `json:"items,omitempty"`
	CleanChunks bool                         `json:"cleanChunks,omitempty"`
}

// SaveCollectionSnapshotSync 保存问题对应的collection快照
func SaveCollectionSnapshotSync(ctx context.Context, answer *aipb.ChatMessage, cleanChunks bool) error {
	log.WithContext(ctx).Infow("SaveCollectionSnapshotSync request", "answer", answer)
	if answer.DocMatchPattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_FULL_MATCH ||
		answer.DocMatchPattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_IGNORE_MARK_MATCH ||
		answer.DocMatchPattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_CONTAINS ||
		answer.FinalQuery == "" || answer.Id == 0 || answer.PromptType == AIChatPromptTypeChat {
		log.WithContext(ctx).Infow("SaveCollectionSnapshotSync request err, no need to search collection", "answer", answer)
		return nil
	}

	const maxRetries = 3
	const retryDelay = time.Second

	var startTime = time.Now()
	var pbRsp *aipb.RspSearchCollectionOneShot
	var err error
	for i := 0; i < maxRetries; i++ {
		pbRsp, err = client.AiNational.SearchCollectionOneShot(ctx, &aipb.ReqSearchCollectionOneShot{
			Search:      answer.FinalQuery,
			AssistantId: []uint64{answer.AssistantId},
			TimeRecord:  true,
		}, mclient.WithRequestTimeout(60*time.Second))
		if err == nil {
			break
		}

		log.WithContext(ctx).Errorf("search collection attempt %d failed: %v", i+1, err)
		time.Sleep(retryDelay)
	}

	if err != nil {
		log.WithContext(ctx).Errorf("search collection err: %v, messageID: %d", err, answer.Id)
		weChatErrorSend(err)
		return err
	}
	if pbRsp == nil {
		log.WithContext(ctx).Errorf("search collection err: pbRsp == nil, messageID: %d", answer.Id)
		return nil
	}
	var contributors []*aipb.Contributor
	for _, v := range pbRsp.SearchItems {
		contributors = append(contributors, v.Contributor...)
	}
	for _, v := range pbRsp.SearchTextItems {
		contributors = append(contributors, v.Contributor...)
	}
	_, err = GeAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		log.WithContext(ctx).Errorw("SaveCollectionSnapshotSync get contributors err", "err", err)
	}

	collectionItems := append(pbRsp.SearchItems, pbRsp.SearchTextItems...)
	collectionDocNames := make(map[string]struct{})
	for _, v := range collectionItems {
		collectionDocNames[v.DocName] = struct{}{}
	}
	for _, ref := range answer.RefFileNames {
		if _, ok := collectionDocNames[ref]; !ok { // chat命中的doc在search collection中没查到
			weChatErrorSend(fmt.Errorf("search collection items docName mismatch,doc name: %s, messageID: %d, chatID: %d", ref, answer.Id, answer.ChatId))
		}
	}

	var snapshot = &aipb.ReqUpdateChatMessageCollections{}
	snapshot.MessageId = answer.Id
	snapshot.StartTime = timestamppb.New(startTime)
	snapshot.EndTime = timestamppb.New(time.Now())
	snapshot.Items = collectionItems
	snapshot.CleanChunks = cleanChunks
	_, err = client.AiByID(answer.Id).UpdateChatMessageCollections(ctx, snapshot)
	if err != nil {
		log.WithContext(ctx).Errorw("failed to update collection snapshot", "err", err)
		return err
	}

	log.WithContext(ctx).Infow("SaveCollectionSnapshotSync success", "hash Id", answer.PublishHashId, "doc names", answer.RefFileNames, "collection len", len(collectionItems), "match len", len(collectionItems))
	//if len(snapshot.Items) > 0 {
	//	result, err := json.Marshal(snapshot)
	//	if err != nil {
	//		log.WithContext(ctx).Errorf("marshal collection items failed: %v", err)
	//		return err
	//	}
	//	_, err = client.AiByID(answer.Id).UpdateChatMessage(ctx, &aipb.ReqUpdateChatMessage{
	//		Id: answer.Id, CollectionSnapshot: string(result), Mask: &fieldmaskpb.FieldMask{
	//			Paths: []string{"collection_snapshot"},
	//		}})
	//	if err != nil {
	//		log.WithContext(ctx).Errorw("failed to update collection snapshot", "err", err)
	//		return err
	//	}
	//}

	return nil

}

func weChatErrorSend(err error) {
	env := config.GetStringOr("llm.env", "dev")
	support.LLMWeChatSendMsg(fmt.Sprintf("【环境：%s】AI 保存问题对应的collection快照 ，请检查，url: %s", env, err))
}

// GetWorkWeixinAccountAvatar 获取企业微信助手账号头像
func GetWorkWeixinAccountAvatar(ctx context.Context, openKfid, corpId string) string {
	if openKfid == "" {
		log.WithContext(ctx).Error("GetWorkWeixinAccountAvatar openKfid is nil")
		return ""
	}
	if config.GetBoolOr("workWeixin.enable", true) {
		return ""
	}

	accounts, err := workweixin.BuildNewClient(workweixin.Client, corpId,
		aware.LoadOpenWorkAccessToken).BatchGetKfAccounts()
	if err != nil {
		log.WithContext(ctx).Errorw("GetWorkWeixinAccountAvatar error", "err", err, "openKfid", openKfid)
		return ""
	}
	if len(accounts) == 0 {
		log.WithContext(ctx).Errorw("GetWorkWeixinAccountAvatar cant find account", "openKfid", openKfid)
		return ""
	}

	for _, account := range accounts {
		if account.OpenKfid == openKfid {
			return account.Avatar
		}
	}
	log.WithContext(ctx).Errorw("GetWorkWeixinAccountAvatar cant find account again", "openKfid", openKfid)
	return ""
}

func DescribeMessageLogs(ctx context.Context, messageId uint64, assistant *aipb.AssistantDetail) (*aipb.RspDescribeMessageLog, error) {
	// 获取log
	res, err := client.AiByID(messageId).DescribeMessageLogs(ctx, &aipb.ReqDescribeMessageLog{MessageId: messageId, AssistantDetail: assistant})
	if err != nil {
		return nil, err
	}
	return res, nil
}

func FetchChatHtmlTitle(ctx context.Context, urls []string, assistant *aipb.AssistantDetail) (map[string]string, error) {
	var urlWithTitles map[string]string
	if assistant.SearchEngine == config.GetStringOr("llm.internatonal.engine", "google") {
		res, err := client.AiInternational.FetchHtmlTitles(ctx, &aipb.ReqFetchHtmlTitles{Urls: urls}, mclient.WithRequestTimeout(60*time.Second))
		if err != nil {
			return nil, err
		}
		urlWithTitles = res.UrlWithTitles
	} else {
		res, err := client.AiNational.FetchHtmlTitles(ctx, &aipb.ReqFetchHtmlTitles{Urls: urls}, mclient.WithRequestTimeout(60*time.Second))
		if err != nil {
			return nil, err
		}
		urlWithTitles = res.UrlWithTitles
	}
	return urlWithTitles, nil
}

// CoverQuestionTextByAskType 根据用户提供类型覆盖问题文本
func CoverQuestionTextByAskType(question *aipb.ChatMessage, origin aipb.ChatType) string {
	var answerSlice []string

	switch origin {
	case aipb.ChatType_CHAT_TYPE_WECHAT:
		switch question.AskType {
		case aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION:
			answerSlice = config.GetStringSliceOr("workweixin.ai.answerAgainKeywords", []string{"重新回答"})
		case aipb.QuestionAskType_QUESTION_ASK_TYPE_CONTINUE:
			answerSlice = config.GetStringSliceOr("workweixin.ai.humanServiceConfirmClickNo", []string{"不，继续回答"})
		}

	case aipb.ChatType_CHAT_TYPE_WHATSAPP:
		if question.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION {
			answerSlice = config.GetStringSliceOr("whatsapp.ai.answerAgainKeywords", []string{"Try again"})
		}
	default:
		return question.Text
	}
	if len(answerSlice) > 0 {
		return answerSlice[0]
	}
	return question.Text
}

// GetAllUserAndTeamId 获取所有用户和团队id
func GetAllUserAndTeamId(ctx context.Context, search string) ([]uint64, []uint64, error) {
	if len(search) == 0 {
		return nil, nil, nil
	}
	var userIds []uint64

	userNationRsp, err := client.IamNational.GetUserIds(ctx, &iampb.ReqGetUserIds{Username: search})
	if err != nil {
		return nil, nil, err
	}
	userIds = userNationRsp.Ids

	//userInternationRsp, err := client.IamInternational.GetUserIds(ctx, &iampb.ReqGetUserIds{Username: search})
	//if err != nil {
	//	return nil, nil, err
	//}
	//userIds = append(userIds, userInternationRsp.Ids...) todo

	teamRsp, err := client.TeamClient.GetTeamIds(ctx, &teampb.ReqGetTeamIds{TeamName: search})
	if err != nil {
		return nil, nil, err
	}

	return userIds, teamRsp.Ids, nil
}

// GetRecentlyUsedAssistantIds 获取最近使用过的助手
func GetRecentlyUsedAssistantIds(ctx context.Context, user *iampb.UserInfo, chatType aipb.ChatType) ([]uint64, error) {
	// 获取所有用户涉及的助手id
	rpcRsp, err := client.AiByUser(user).GetRecentlyUsedAssistantIds(ctx, &aipb.ReqGetRecentlyUsedAssistantIds{
		UserId:   user.Id,
		ChatType: chatType,
	})
	if err != nil {
		return nil, err
	}
	return rpcRsp.Ids, nil
}
