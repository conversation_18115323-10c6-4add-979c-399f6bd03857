package ai

import (
	"context"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	cmspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/cms"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// AiServiceTermsLogic AI服务协议逻辑
type AiServiceTermsLogic struct{}

// UserHasAssistants 判断用户是否开通助手
func (l *AiServiceTermsLogic) UserHasAssistants(ctx context.Context) (bool, error) {
	assistantIDs, err := GetManagedAssistants(ctx)
	if err != nil {
		return false, err
	}
	return len(assistantIDs) > 0, nil
}

// GetUserIdentity 获取用户身份
func (l *AiServiceTermsLogic) GetUserIdentity(ctx context.Context,
	user *iampb.UserInfo) (identity *basepb.Identity, team *teampb.TeamInfo, err error) {
	identity = &basepb.Identity{
		IdentityId:   user.Id,
		IdentityType: basepb.IdentityType_IDENTITY_TYPE_USER,
	}
	if user.IdentitySet == basepb.IdentityType_IDENTITY_TYPE_TEAM {
		fullTeam, err := getUserTeam(ctx, user.Id)
		if err != nil {
			return nil, nil, err
		}
		if fullTeam != nil {
			team = fullTeam.TeamInfo
			identity.IdentityId = team.Id
			identity.IdentityType = basepb.IdentityType_IDENTITY_TYPE_TEAM
		}
	}
	return identity, team, nil
}

// GetUserTermsType 获取用户应该同意的协议类型
func (l *AiServiceTermsLogic) GetUserTermsType(
	user *iampb.UserInfo, team *teampb.TeamInfo, lang string) cmspb.TermsType {
	if user.IdentitySet == basepb.IdentityType_IDENTITY_TYPE_TEAM && team != nil {
		return l.getTeamAiServiceTermsType(team, lang)
	}
	return l.getUserAiServiceTermsType(user)
}

// Confirm 确认协议
func (l *AiServiceTermsLogic) Confirm(ctx context.Context,
	user *iampb.UserInfo, identity *basepb.Identity, termsType cmspb.TermsType, isAgreed bool) error {
	_, err := client.CmsClient.ConfirmTerms(ctx, &cmspb.ReqConfirmTerms{
		Identity:  identity,
		TermsType: termsType,
		IsAgreed:  isAgreed,
		CreateBy:  user.Id,
	})
	if err != nil {
		return err
	}
	return nil
}

// GetConfirmation 获取协议确认信息
func (l *AiServiceTermsLogic) GetConfirmation(ctx context.Context,
	identity *basepb.Identity, termsType cmspb.TermsType) (bool, string, error) {
	termsTypes := []cmspb.TermsType{
		cmspb.TermsType_TERMS_TYPE_AI_SERVICE_CN,
		cmspb.TermsType_TERMS_TYPE_AI_SERVICE_OVERSEA,
	}
	rsp, err := client.CmsClient.GetTermsConfirmations(ctx, &cmspb.ReqGetTermsConfirmations{
		Identity:   identity,
		TermsTypes: termsTypes,
	})
	if err != nil {
		return false, "", err
	}

	m := map[cmspb.TermsType]*cmspb.RspGetTermsConfirmations_Confirmation{}
	for _, confirmation := range rsp.Confirmations {
		m[confirmation.TermsType] = confirmation
	}

	var (
		isAgreed bool
		docPath  string
	)
	for _, t := range termsTypes {
		if confirmation := m[t]; confirmation != nil {
			if confirmation.IsAgreed {
				isAgreed = true
			}
			if confirmation.TermsType == termsType {
				docPath = confirmation.DocPath
			}
		}
	}

	return isAgreed, docPath, nil
}

func (l *AiServiceTermsLogic) getTeamAiServiceTermsType(team *teampb.TeamInfo, lang string) cmspb.TermsType {
	// 已认证的团队通过主体国家区分
	if team.IsVerified && team.PrincipalCountry != "" {
		if team.PrincipalCountry == "CN" {
			return cmspb.TermsType_TERMS_TYPE_AI_SERVICE_CN
		}
		return cmspb.TermsType_TERMS_TYPE_AI_SERVICE_OVERSEA
	}

	// 未认证团队根据当前语言区分
	if strings.ToLower(lang) == "zh" {
		return cmspb.TermsType_TERMS_TYPE_AI_SERVICE_CN
	}
	return cmspb.TermsType_TERMS_TYPE_AI_SERVICE_OVERSEA
}

func (l *AiServiceTermsLogic) getUserAiServiceTermsType(user *iampb.UserInfo) cmspb.TermsType {
	if client.IsInternationalUserID(user.Id) {
		return cmspb.TermsType_TERMS_TYPE_AI_SERVICE_OVERSEA
	}
	return cmspb.TermsType_TERMS_TYPE_AI_SERVICE_CN
}

func getUserTeam(ctx context.Context, userId uint64) (*teampb.FullTeam, error) {
	rsp, err := client.TeamClient.GetUserTeams(ctx, &teampb.ReqGetUserTeams{
		UserId: []uint64{userId},
	})
	if err != nil {
		return nil, err
	}
	return rsp.Teams[userId], nil
}
