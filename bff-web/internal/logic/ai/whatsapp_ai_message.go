package ai

import (
	"bytes"
	"context"
	"fmt"
	"strconv"
	"strings"
	"text/template"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai/aware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/ugc"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/whatsapp"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	mclient "github.com/asim/go-micro/v3/client"
	"golang.org/x/exp/maps"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// WhatsappAIMessage whatsapp处理逻辑
type WhatsappAIMessage struct {
	aware.MessageAIConfig[whatsapp.WhatsappInboundMessage]
	defaultAnswer []aware.MessageAnswerAware[whatsapp.WhatsappInboundMessage] // 预设问题
	websiteRoute  string
	assistant     *aipb.AssistantDetail
	//welcomeSender aware.WhatsappMessageSetAnswer
}

// SetAssistant ...
func (w *WhatsappAIMessage) SetAssistant(a *aipb.AssistantDetail) *WhatsappAIMessage {
	w.assistant = a
	return w
}

func (w *WhatsappAIMessage) AfterSend(chat *aipb.ChatWeChat, t whatsapp.WhatsappInboundMessage,
	s aware.SendResultRecord[whatsapp.WhatsappInboundMessage]) {
	// 空实现，没有需要后置执行的逻辑
}

func (w *WhatsappAIMessage) messagePreprocessing(chat *aipb.ChatWeChat,
	msg whatsapp.WhatsappInboundMessage) (*aipb.ChatMessage, aware.MessageAnswerAware[whatsapp.WhatsappInboundMessage]) {

	question := &aipb.ChatMessage{
		ChatId:      chat.Id,
		Type:        aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER,
		AssistantId: w.assistant.Id,
		AskType:     aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL,
	}

	switch msg.Type {
	case "request_welcome":
		log.WithContext(w.Ctx).Debugw("WhatsappAIMessage messagePreprocessing", "request_welcome", msg.From)
		return nil, nil
	case "interactive":
		if msg.Interactive == nil || msg.Interactive.ButtonReply == nil {
			return nil, nil
		}
		question.Text = msg.Interactive.ButtonReply.Title
		//if len(msg.Interactive.ButtonReply.ID) > 0 {
		//	menuId, err := strconv.ParseUint(msg.Interactive.ButtonReply.ID, 10, 64)
		//	if err == nil {
		//		log.WithContext(w.Ctx).Errorw("messagePreprocessing ParseUint error", "err", err)
		//	}
		//	question.Id = menuId
		//}
	case "text":
		if msg.Text == nil || msg.Text.Body == "" {
			return nil, nil
		}
		question.Text = msg.Text.Body
	}
	if notice := getAssistantNotice(w.Ctx, w.assistant.Channel); notice != "" {
		question.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET
		return question, aware.GetWhatsappContentAnswer(notice)
	}

	if answer := aware.GetWhatsappAnswer(question, w.defaultAnswer); answer != nil {
		answer.DoExtension(w.Ctx, chat, &msg)
		question.AskType = answer.GetAnswerType()
		if question.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET {
			question.Type = aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER
		}
		return question, answer
	}

	// 审核问题
	question.RejectReason = ReviewChatMessage(w.Ctx, question)
	return question, nil
}

// GetChatMessage 由具体实现类覆盖
func (w *WhatsappAIMessage) GetChatMessage(messages []whatsapp.WhatsappInboundMessage) map[*aipb.ChatWeChat][]whatsapp.WhatsappInboundMessage {
	msg := messages[0]
	// 拿到助手信息
	rsp, err := client.AiNational.GetAssistant(w.Ctx, &aipb.ReqGetAssistant{AppCode: msg.WabaID})
	if err != nil || rsp == nil || rsp.AssistantDetail.Id == 0 {
		log.WithContext(w.Ctx).Errorw("WhatsappAIMessage GetAssistantByAppCode", "err", err, "appCode", msg.WabaID)
		return nil
	}
	w.assistant = rsp.AssistantDetail
	w.setDefaultAnswer()
	// 创建chat
	var title string
	switch msg.Type {
	case "interactive":
		if msg.Interactive != nil && msg.Interactive.ButtonReply != nil &&
			len(msg.Interactive.ButtonReply.Title) > 0 {
			title = ugc.Limit(msg.Interactive.ButtonReply.Title, 100)
		}
	case "text":
		if msg.Text != nil {
			title = ugc.Limit(msg.Text.Body, 100)
		}
	case "request_welcome": // 发送欢迎语
		if w.assistant.WelcomeMsg == nil || w.assistant.WelcomeMsg.HeadMsg == "" {
			w.assistant.WelcomeMsg = &aipb.AssistantWelcomeMsg{
				HeadMsg: config.GetStringOr("whatsapp.ai.welcomeTemplate",
					"Hi~ I am Tan raised by tanlive.com with AI technologies.\\n\\nYou can try asking me:\n"),
			}
		}
		log.WithContext(w.Ctx).Debugw("whatsapp send welcomeMsg", "msg", msg, "welcomeMsg", w.assistant.WelcomeMsg)
		if err = aware.WhatsappAnswerSendWelcome(msg, w.assistant); err != nil {
			log.WithContext(w.Ctx).Errorw("WhatsappAnswerSendWelcome error", "err", err)
		}
		return nil
	}

	if !w.assistant.Enabled {
		sender := &aware.WhatsappMessageSetAnswer{
			Content: config.GetStringOr("whatsapp.ai.offlineAssistantAnswer",
				"The assistant is offline. Please contact the administrator."),
		}
		if _, err := sender.Send(msg); err != nil {
			log.WithContext(w.Ctx).Errorw("messagePreprocessing send message failed", "err", err)
		}
		return nil
	}

	// 获取用户对应的chat_id
	ChatIDRsp, err := client.AiInternational.GetChatWechat(w.Ctx, &aipb.ReqGetChatWechat{
		AssistantId:      w.assistant.Id,
		ExternalUserIds:  []string{msg.From},
		ChatType:         aipb.ChatType_CHAT_TYPE_WHATSAPP,
		ChatIdleDuration: w.assistant.ChatIdleDuration,
	})
	if err != nil || ChatIDRsp == nil {
		log.WithContext(w.Ctx).Errorw("WhatsappAIMessage GetChatIDWechat", "err", err, "ChatIDRsp", ChatIDRsp)
		return nil
	}
	chat := ChatIDRsp.ChatMap[msg.From]
	if chat != nil && !chat.End {
		return map[*aipb.ChatWeChat][]whatsapp.WhatsappInboundMessage{
			chat: {msg},
		}
	}

	newChat := &aipb.ChatWeChat{
		AppId:          msg.From,
		ExternalUserId: msg.From,
		Nickname:       msg.CustomerProfile.Name,
		Title:          title,
		SupportType:    aipb.ChatSupportType_CHAT_SUPPORT_TYPE_AI,
		ChatType:       aipb.ChatType_CHAT_TYPE_WHATSAPP,
	}
	if chat != nil { // 之前会话的信息要转移至新会话上
		newChat.DefaultAnswerRecord = chat.DefaultAnswerRecord
	}

	creatRsp, err1 := client.AiInternational.CreateChatWechat(w.Ctx, &aipb.ReqCreateChatWechat{
		Chats:       []*aipb.ChatWeChat{newChat},
		AssistantId: rsp.AssistantDetail.Id,
	})
	if err1 != nil || creatRsp == nil || len(creatRsp.ChatMap) == 0 {
		log.WithContext(w.Ctx).Errorw("WhatsappAIMessage CreateChatWechat", "err", err1, "creatRsp", creatRsp)
		return nil
	}
	newChat.Id = creatRsp.ChatMap[msg.From]
	return map[*aipb.ChatWeChat][]whatsapp.WhatsappInboundMessage{
		newChat: {msg},
	}
}

// GetConf ...
func (w *WhatsappAIMessage) GetConf() aware.MessageAIConfig[whatsapp.WhatsappInboundMessage] {
	return w.MessageAIConfig
}

// Init 初始化
func (w *WhatsappAIMessage) Init() {
	w.MessageAIConfig = aware.MessageAIConfig[whatsapp.WhatsappInboundMessage]{
		Ctx:           w.Ctx,
		MaxRetryTimes: config.GetIntOr("whatsapp.ai.questionMaxRetryTimes", 5),
		RetryInterval: time.Duration(config.GetIntOr("whatsapp.ai.questionRetryInterval", 6)) * time.Second,
		TimeoutDefaultAnswer: config.GetStringOr("whatsapp.ai.timeoutDefaultAnswer", "Sorry, I was absent-minded "+
			"just now. You can try to ask the question again or contact our support <NAME_EMAIL>"),
		AuditErrorAnswer: config.GetStringOr("whatsapp.ai.auditErrorAnswer",
			"对话中可能含有敏感信息, 作为一个AI助手, 我需要遵守相关规定, 无法回答您的问题。可以换个问题考考我。"),
		HelpCenterUrl:     config.GetString("whatsapp.ai.helpCenterUrl"),
		CosPublicCdnUrl:   config.GetString("oss.publicCdn"),
		UgcJumpUrl:        config.GetString("whatsapp.ai.ugcJumpUrl"),
		UgcAnswerTemplate: config.GetString("whatsapp.ai.ugcAnswerTemplate"),
		ReadMore:          config.GetStringOr("whatsapp.ai.readMore", "View more"),
		AnswerFrom:        config.GetStringOr("whatsapp.ai.answerFrom", "\n\nAnswers compiled by "),

		ReferenceAnswerTemplate: config.GetString("whatsapp.ai.referenceAnswerTemplate"),
		ReferenceDataFrom:       config.GetStringOr("whatsapp.ai.referenceDataFrom", "\nReference\n"),
	}

	w.GetWhatsappDefaultCmdAnswer()

	if content := aware.WarpWhatsappCodeToString(w.assistant, false); content != "" {
		w.defaultAnswer = append(w.defaultAnswer, &aware.WhatsappMessageSetAnswer{ // 触发 暗号 精确关键字
			MessageAwareImpl: aware.MessageAwareImpl[whatsapp.WhatsappInboundMessage]{
				FoldExactKeywords: config.GetStringSliceOr("whatsapp.ai.answerInteractiveKeywords", []string{"Interactive Keywords"}),
			},
			Content: content,
			AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
		})
	}
}

// GetWhatsappDefaultCmdAnswer 设置和指令相关的默认命令
func (w *WhatsappAIMessage) GetWhatsappDefaultCmdAnswer() *WhatsappAIMessage {
	if w.assistant == nil || w.assistant.WelcomeMsg == nil || w.assistant.WelcomeMsg.CodeConfig == nil {
		return w
	}
	for _, v := range w.assistant.WelcomeMsg.CodeConfig.Codes {
		switch v.InteractiveCode {
		case aipb.InteractiveCode_INTERACTIVE_CODE_ANSWER_AGAIN:
			w.defaultAnswer = append(w.defaultAnswer, &aware.WhatsappMessageSetAnswer{ // 触发 重新回答 精确关键词
				MessageAwareImpl: aware.MessageAwareImpl[whatsapp.WhatsappInboundMessage]{
					FoldExactKeywords: config.GetStringSliceOr("whatsapp.ai.answerAgainKeywords", []string{"Try again", "retry"}),
				},
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION,
			})
		case aipb.InteractiveCode_INTERACTIVE_CODE_CLEAR_CONTEXT:
			w.defaultAnswer = append(w.defaultAnswer, &aware.WhatsappMessageSetAnswer{ // 触发 清空上下文 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[whatsapp.WhatsappInboundMessage]{
					FoldExactKeywords: config.GetStringSliceOr("whatsapp.ai.clearContextQuestion", []string{
						"Clear context", "Clear the context"}),
					Extension: aware.FinishWhatsappChatByClear,
				},
				Content: config.GetStringOr("whatsapp.ai.clearContextAnswer",
					"OK, I have cleared the context. Feel free to continue asking me questions."),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
			})
		default:
			//case aipb.InteractiveCode_INTERACTIVE_CODE_MANUAL:  //人工除了暗号之外还有单独的配置
			//case aipb.InteractiveCode_INTERACTIVE_CODE_READ_BOM: // 读配料表 取决于是否配置了multimodal_prompt_prefix
			//case aipb.InteractiveCode_INTERACTIVE_CODE_READ_TEST_REPORT:// 读检测报告 取决于是否配置了multimodal_prompt_prefix
			//case aipb.InteractiveCode_INTERACTIVE_CODE_CONTRIBUTE_KNOWLEDGE: // whatsapp的贡献知识暂时没有支持
		}
	}
	return w
}

func (w *WhatsappAIMessage) setDefaultAnswer() {
	if w.assistant == nil {
		return
	}
	if w.assistant.RatingScaleMsg != nil && w.assistant.RatingScaleMsg.Enable {
		w.defaultAnswer = append(w.defaultAnswer,
			&aware.WhatsappMessageSetAnswer{ // 触发 满意 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[whatsapp.WhatsappInboundMessage]{
					ExactKeywords: []string{aware.RatingScaleSatisfiedEN},
					Extension:     updateWhatsappRateAiAnswer,
				},
				Content: aware.GetRatingScale(aware.RatingScaleSatisfiedEN, "Thank you for your support!", w.assistant.RatingScaleMsg),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET,
			},
			&aware.WhatsappMessageSetAnswer{ // 触发 一般 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[whatsapp.WhatsappInboundMessage]{
					ExactKeywords: []string{aware.RatingScaleAverageEN},
					Extension:     updateWhatsappRateAiAnswer,
				},
				Content: aware.GetRatingScale(aware.RatingScaleAverageEN, "I will continue to work hard!", w.assistant.RatingScaleMsg),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET,
			},
			&aware.WhatsappMessageSetAnswer{ // 触发 不满意 精确关键字
				MessageAwareImpl: aware.MessageAwareImpl[whatsapp.WhatsappInboundMessage]{
					ExactKeywords: []string{aware.RatingScaleDissatisfiedEN},
					Extension:     updateWhatsappRateAiAnswer,
				},
				Content: aware.GetRatingScale(aware.RatingScaleDissatisfiedEN, fmt.Sprintf("I will continue to work hard!"+
					" You can go to the official website %s%s  and click the upper right corner of my conversation panel to teach me。",
					strings.TrimSuffix(w.UgcJumpUrl, "/"), w.websiteRoute), w.assistant.RatingScaleMsg),
				AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET,
			},
		)
	}
}

// BeforeStart ...
func (w *WhatsappAIMessage) BeforeStart(ctx context.Context) bool {
	w.Ctx = ctx
	return true
}

// Do do
func (w *WhatsappAIMessage) Do(chat *aipb.ChatWeChat, message whatsapp.WhatsappInboundMessage) (aware.DoResultRecord,
	aware.MessageAnswerAware[whatsapp.WhatsappInboundMessage]) {
	// 设置预设答案，审核问题
	question, defaultAnswer := w.messagePreprocessing(chat, message)
	if question == nil {
		return aware.DoResultRecord{}, nil
	}
	if defaultAnswer == nil {
		defaultAnswer = &aware.WhatsappMessageSetAnswer{}
	}

	req := &aipb.ReqGetAnswerWechat{
		Message:         question,
		ThirdRecordUuid: message.ID,
		SetChatAnswer:   defaultAnswer.GetAnswerString(),
		AssistantDetail: w.assistant,
	}
	if question.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL { // 如果是用户的正常问答，则需要先匹配一次QA
		startTime := time.Now()
		if qa, _ := client.AiNational.DescribeMessageMatchQa(w.Ctx, &aipb.ReqDescribeMessageMatchQa{
			Text:        question.Text,
			AssistantId: w.assistant.Id,
		}); qa != nil && len(qa.Docs) > 0 {
			question.AskType = aipb.QuestionAskType_QUESTION_ASK_TYPE_MATCH_PATTERN
			req.Docs = qa.Docs
			req.SetChatAnswer = qa.Docs[0].Text
			req.MatchPattern = qa.MatchPattern
			req.AnswerStartTime = timestamppb.New(startTime)
			for i := 1; i < len(qa.Docs); i++ {
				req.SetChatAnswer += "\n\n" + qa.Docs[i].Text
			}
			endTime := time.Now()
			req.AnswerEndTime = timestamppb.New(endTime)
			req.CollectionSnapshot = new(ChatGenerateLogic).GetCollectionSnapshot(w.Ctx, qa.Docs, startTime, endTime)
		}
	}
	sec := w.RetryInterval * time.Duration(w.MaxRetryTimes)
	// 保存问题并获取答案
	answerRsp, err := client.AiInternational.GetAnswerWechat(w.Ctx, req, mclient.WithRequestTimeout(sec))
	if answerRsp == nil || err != nil {
		log.WithContext(w.Ctx).Errorw("messageDeal GetAnswerWechat error",
			"chatId", chat.Id, "err", err, "answerRsp", answerRsp)
		return aware.DoResultRecord{}, nil
	}
	if answerRsp.QuestionType == aipb.RspGetAnswerWechat_QUESTION_TYPE_ID_REPETITIVE { // 重复消息不做处理
		log.WithContext(w.Ctx).Infow("WhatsappAIMessage messageDeal get repetitive message",
			"chatId", chat.Id, "message", message)
		return aware.DoResultRecord{}, nil
	}

	if question.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT ||
		answerRsp.SupportType == aipb.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT { // 人工回答保存了之后不需要发送微信
		log.WithContext(w.Ctx).Infow("messageDeal with live agent", "chatId", chat.Id, "message", message)
		return aware.DoResultRecord{}, nil
	}

	sender, ok := defaultAnswer.(*aware.WhatsappMessageSetAnswer)
	if !ok {
		return aware.DoResultRecord{}, nil
	}

	docs, err := DescribeMessagesDocs(w.Ctx, answerRsp.Answers, true) // 获取docs
	if err != nil {
		log.WithContext(w.Ctx).Errorw("WechatAIMessage DescribeMessagesDocs error", "chatId", chat.Id, "err", err)
	}

	resultRecord := aware.DoResultRecord{
		Records: make([]*aware.DoResult, len(answerRsp.Answers)),
		ChatId:  chat.Id,
	}
	for i, answer := range answerRsp.Answers {
		if answer == nil {
			continue
		}
		if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION {
			if err := SaveCollectionSnapshotSync(w.Ctx, answer, w.assistant.CleanChunks); err != nil {
				log.WithContext(w.Ctx).Errorw("WechatAIMessage SaveCollectionSnapshotSync error", "err", err)
			}
		}

		eventMessage := TransformAIMessageToEventMessage(answer, docs) // 获取docs的贡献者
		answerMessage := EventMessageTransformWithHash(eventMessage)   // 所有id转为hashID

		if defaultAnswer.GetAnswerString() == "" { // 预设回答为空时，审核回答
			reviewAnswerMessage(w.Ctx, answerMessage)
		}

		resultRecord.Records[i] = &aware.DoResult{
			//QuestionId: answer.QuestionId,
			Answer:   answerMessage,
			AnswerId: answer.Id,
			Reason:   answer.RejectReason,
		}
	}

	return resultRecord, sender
}

// Send 由具体实现类覆盖
func (w *WhatsappAIMessage) Send(doResult aware.DoResultRecord, original whatsapp.WhatsappInboundMessage,
	sender aware.MessageAnswerAware[whatsapp.WhatsappInboundMessage]) aware.SendResultRecord[whatsapp.WhatsappInboundMessage] {

	whatsappSender, ok := sender.(*aware.WhatsappMessageSetAnswer)
	log.WithContext(w.Ctx).Infow("CustomKeywordsFunc whatsapp", "ok", ok, "answerText", whatsappSender.GetAnswerString(),
		"answerType", whatsappSender.GetAnswerType(), "askType", whatsappSender.AskType)
	if !ok {
		return aware.SendResultRecord[whatsapp.WhatsappInboundMessage]{}
	}

	var target, answerMessageId string
	var err error
	for _, record := range doResult.Records {
		if record.Answer == nil || record.Answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR ||
			record.Answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR {
			record.Answer = &aipb.EventChatHashMessage{Type: aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR}
			whatsappSender = &aware.WhatsappMessageSetAnswer{
				Content: w.TimeoutDefaultAnswer,
			}
		}

		switch record.Answer.Type {
		case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION, aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH: // 文本和doc link
			menuId := strconv.FormatUint(record.AnswerId, 10)
			aware.SetWhatsappDefaultAnswer(menuId, whatsappSender, w.assistant)
			whatsappSender.Content = strings.TrimLeft(w.PrepareSearchMessage(record.Answer), "\n") // 截取句首的换行符
			answerMessageId, err = whatsappSender.Send(original, menuId)
		case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY: // 返回UGC
			target = w.PrepareUgcMessage(record.Answer)
			whatsappSender.Content = target
			answerMessageId, err = whatsappSender.Send(original, "")

		default: // 默认直接发送消息
			menuId := strconv.FormatUint(record.AnswerId, 10)
			if len(record.Answer.Text) > 0 {
				whatsappSender.Content = record.Answer.Text
			}
			answerMessageId, err = whatsappSender.Send(original, menuId)
		}

		messageType := aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL
		if whatsappSender.GetAnswerType() == aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET {
			messageType = aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER
		}
		// 消息发送记录
		_, _ = client.AiByID(record.AnswerId).CreateSendRecord(w.Ctx, &aipb.ReqCreateSendRecord{
			ChatId:    doResult.ChatId,
			MessageId: record.AnswerId,
			ChatType:  aipb.ChatType_CHAT_TYPE_WHATSAPP,
			WhatsAppRecord: &aipb.AiSendResultRecord{
				Content:     whatsappSender.Content,
				Uuid:        answerMessageId,
				State:       2, // 发送成功
				Type:        aipb.AiRecordType_AI_RECORD_TYPE_AI_TEXT,
				MessageType: messageType,
			},
		})
		if err != nil {
			log.WithContext(w.Ctx).Errorw("sendMessageByWechat SendTextMessage ", "err", err,
				"answerMessageId", answerMessageId)
			break
		}
	}
	return aware.SendResultRecord[whatsapp.WhatsappInboundMessage]{
		Ctx:      w.Ctx,
		Err:      err,
		DoResult: doResult,
		Sender:   whatsappSender,
	}
}

// GetMessageId 获取消息ID
func (w *WhatsappAIMessage) GetMessageId(t whatsapp.WhatsappInboundMessage) string {
	return t.ID
}

func (w *WhatsappAIMessage) PrepareUgcMessage(chatMessage *aipb.EventChatHashMessage) string {
	// 创建模板对象
	tmpl, err := template.New("UgcTemplate").Parse(w.UgcAnswerTemplate)
	if err != nil {
		log.WithContext(w.Ctx).Errorw("prepareUgcMessage UgcAnswerTemplate err",
			"err", err, "template", w.UgcAnswerTemplate)
		return chatMessage.Text
	}
	// 拼接UGC信息
	argGroup := make([]map[string]interface{}, 0, len(chatMessage.Ugcs))
	var viewMores []string
	for _, cu := range chatMessage.Ugcs {
		arg := map[string]interface{}{}
		var ugcInfo, searchFilter []string
		filterArray := make(map[string]struct{})
		ugcJumpLink := "【%s】%s"
		ugcJumpUrl := getUgcJumpPathByEnum(cu.UgcType, w.UgcJumpUrl)
		ugcDetailJumpUrl := ugcJumpUrl + "/detail/"

		for _, v := range cu.Cards {
			ugcInfo = append(ugcInfo, fmt.Sprintf(ugcJumpLink, v.Name, ugcDetailJumpUrl+v.Id))
		}
		arg["UgcData"] = strings.Join(ugcInfo, "\n")

		// 拼接过滤条件
		for _, item := range cu.Filter {
			if len(item.Tags) > 0 {
				for _, tag := range item.Tags {
					filterArray[tag.Name] = struct{}{}
					searchFilter = append(searchFilter, fmt.Sprintf("%s=%s", item.Field, tag.Name))
				}
			} else {
				filterArray[item.Value] = struct{}{}
				searchFilter = append(searchFilter, fmt.Sprintf("%s=%s", item.Field, item.Value))
			}
		}
		if len(filterArray) > 0 {
			arg["Filter"] = strings.Join(maps.Keys(filterArray), ",") + " " // 有条件的时候拼接空格
		} else {
			arg["Filter"] = ""
		}

		// 拼接UGC类型
		ugcType := getUgcDataTypeEnNameByEnum(cu.UgcType)
		arg["UgcType"] = ugcType

		// 拼接查看更多
		if cu.IsUgcLink {
			viewMores = append(viewMores,
				fmt.Sprintf(ugcJumpLink, w.ReadMore, ugcJumpUrl+"?"+strings.Join(searchFilter, "&")))
		}
		argGroup = append(argGroup, arg)
	}
	if len(argGroup) > 0 {
		var buf bytes.Buffer
		err = tmpl.Execute(&buf, map[string]interface{}{
			"ViewMores": viewMores,
			"Args":      argGroup,
		})
		if err != nil {
			log.WithContext(w.Ctx).Errorw("prepareMessageByWechat ugcAnswerTemplate err", "err", err,
				"template", w.UgcAnswerTemplate)
			return chatMessage.Text
		}
		log.WithContext(w.Ctx).Debugw("prepareUgcMessageByWechat message", "text", buf.String())
		return buf.String()
	}
	return chatMessage.Text
}

// PrepareSearchMessage 准备搜索微信消息
func (w *WhatsappAIMessage) PrepareSearchMessage(chatMessage *aipb.EventChatHashMessage) string {
	// 创建模板对象
	tmpl, err := template.New("ReferenceTemplate").Parse(w.ReferenceAnswerTemplate)
	if err != nil {
		log.WithContext(w.Ctx).Errorw("prepareSearchMessage ReferenceAnswerTemplate err",
			"err", err, "template", w.ReferenceAnswerTemplate)
		return chatMessage.Text
	}
	args := map[string]interface{}{"Text": chatMessage.Text}

	contributors := make(map[string]string)

	for _, doc := range chatMessage.Docs {
		for _, v := range doc.Contributor { // 拼接贡献者
			if v.Type == basepb.IdentityType_IDENTITY_TYPE_TEAM ||
				v.Type == basepb.IdentityType_IDENTITY_TYPE_CUSTOM { // 团队 或自定义文本
				contributors[v.Text] = v.Text
			} // 其他不显示
		}
	}
	if len(contributors) > 0 {
		args["Contributor"] = w.AnswerFrom + strings.Join(maps.Values(contributors), ",")
	} else {
		args["Contributor"] = "" // 没有共享者则默认填空，避免填充模版报错
	}

	// 拼接参考资料
	reference := FetchTitleUtil{
		DocJumpLink:     "%d.【%s】%s",
		LinkJumpLink:    "%d. %s: %s",
		HelpCenterUrl:   w.HelpCenterUrl,
		CosPublicCdnUrl: w.CosPublicCdnUrl,
		format: func(link, name, url string, num int) string {
			return fmt.Sprintf(link, num, name, url)
		},
		Assistant: w.assistant,
	}.FetchTitle(w.Ctx, chatMessage)
	if len(reference) > 0 {
		args["Reference"] = w.ReferenceDataFrom + strings.Join(reference, "\n")
	} else {
		args["Reference"] = ""
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, args)
	if err != nil {
		log.WithContext(w.Ctx).Errorw("prepareMessageByWechat referenceAnswerTemplate err", "err", err,
			"template", w.ReferenceAnswerTemplate)
		return chatMessage.Text
	}
	log.WithContext(w.Ctx).Debugw("prepareMessageByWechat message", "text", buf.String())
	return buf.String()
}

func updateWhatsappRateAiAnswer(ctx context.Context, chat *aipb.ChatWeChat, message *whatsapp.WhatsappInboundMessage) {
	if message.Interactive == nil || message.Interactive.ButtonReply == nil ||
		len(message.Interactive.ButtonReply.ID) == 0 || len(message.Interactive.ButtonReply.Title) == 0 {
		return
	}

	trim := strings.Split(message.Interactive.ButtonReply.ID, aware.ReplaySeparator)
	if len(trim) == 0 {
		log.WithContext(ctx).Errorw("updateRateAiAnswer menuId illegal",
			"menuId", message.Interactive.ButtonReply.ID)
		return
	}

	answerId, err := strconv.ParseUint(trim[0], 10, 64)
	if err != nil {
		log.WithContext(ctx).Errorw("updateRateAiAnswer messageId illegal",
			"chatId", chat.Id, "messageId", answerId)
	}

	aware.UpdateRateAiAnswer(ctx, chat, message.Interactive.ButtonReply.Title, answerId)
}

func getUgcDataTypeEnNameByEnum(ugcType basepb.DataType) string {
	switch ugcType {
	case basepb.DataType_DATA_TYPE_TEAM:
		return "innovators"
	case basepb.DataType_DATA_TYPE_PRODUCT:
		return "solutions"
	case basepb.DataType_DATA_TYPE_RESOURCE:
		return "programs"
	case basepb.DataType_DATA_TYPE_GRAPH:
		return "knowledge"
	}
	return ""
}
