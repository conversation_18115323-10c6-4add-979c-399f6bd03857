package ai

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/tag"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/ugc"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	reviewpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	"github.com/google/uuid"
	"github.com/tencentyun/cos-go-sdk-v5"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type MessageDbLogic struct{}

func (l *MessageDbLogic) DescribeChatShareMessages(ctx context.Context, chatId uint64, messageIds []uint64) ([]*aipb.EventChatMessage, error) {
	// 获取消息
	rpcRsp, err := client.AiByID(chatId).GetChatShareMessages(ctx, &aipb.ReqGetChatShareMessages{
		ChatId:     chatId,
		MessageIds: messageIds,
	})
	if err != nil || rpcRsp == nil {
		return nil, xerrors.InternalServerError(err)
	}

	var questions, answers []*aipb.ChatMessage
	for _, msg := range rpcRsp.Messages {
		if msg.QuestionId == 0 {
			questions = append(questions, msg)
		} else {
			answers = append(answers, msg)
		}
	}
	// 获取docs
	docs, err := DescribeMessagesDocs(ctx, answers, false)
	if err != nil {
		return nil, err
	}
	messages := combineQuestionAndAnswer(questions, answers, docs)

	return messages, nil

}

func handleAnswerStopText(answer *aipb.ChatMessage) *aipb.ChatMessage {
	// 对已停止回答的回答进行返回内容处理
	if answer.LastOperator != nil && (answer.LastOperator.OperationType == aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_THINK || answer.LastOperator.OperationType == aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_TEXT) {
		answer.Link = ""
		answer.DocNames = []string{}
		answer.Ugcs = []*aipb.MessageUgc{}
		answer.SuggestQuestion = []string{}
		if answer.LastOperator.StopText != "" {
			answer.Text = answer.LastOperator.StopText
		}
		if answer.LastOperator.StopThink != "" {
			answer.Think = answer.LastOperator.StopThink
		}
	}
	return answer
}

func combineQuestionAndAnswer(questions, answers []*aipb.ChatMessage, docs []*aipb.ChatMessageDoc) []*aipb.EventChatMessage {
	var messages []*aipb.EventChatMessage
	for _, question := range questions {
		pbQuestion := TransformAIMessageToEventMessage(question, nil)
		messages = append(messages, pbQuestion)
		var lastAnswer *aipb.ChatMessage
		var pbLastAnswer *aipb.EventChatMessage
		var combineAnswers []*aipb.EventChatMessage
		// 遍历问题的回答
		for _, answer := range answers {
			if answer == nil || answer.QuestionId != question.Id {
				continue
			}
			if lastAnswer == nil || answer.Id > lastAnswer.Id {
				lastAnswer = answer
			}
		}
		if lastAnswer != nil {
			pbLastAnswer = TransformAIMessageToEventMessage(handleAnswerStopText(lastAnswer), docs)
		}
		for _, answer := range answers {
			if answer == nil || answer.QuestionId != question.Id {
				continue
			}

			if lastAnswer != nil && answer.PublishHashId != lastAnswer.PublishHashId { // 重新回答的answer
				if pbLastAnswer != nil {
					pbAnswer := TransformAIMessageToEventMessage(handleAnswerStopText(answer), docs)
					pbLastAnswer.Answers = append(pbLastAnswer.Answers, pbAnswer)
				}
			} else { // 多个回答的answer
				if pbLastAnswer != nil && pbLastAnswer.Id == answer.Id {
					combineAnswers = append(combineAnswers, pbLastAnswer)
				} else {
					combineAnswers = append([]*aipb.EventChatMessage{TransformAIMessageToEventMessage(handleAnswerStopText(answer), docs)}, combineAnswers...)
				}
			}
		}

		messages = append(messages, combineAnswers...)
	}
	return messages
}

func (l *MessageDbLogic) DescribeChatQuestionAnswersByPage(ctx context.Context, chatId, questionID uint64, limit, offset uint32, isReverse, withDocs bool) ([]*aipb.EventChatMessage, uint32, error) {
	// 获取message
	rpcRsp, err := client.AiByID(chatId).DescribeChatQuestionAnswersByPage(ctx, &aipb.ReqDescribeChatQuestionAnswersByPage{
		ChatId:     chatId,
		Offset:     offset,
		Limit:      limit,
		IsReverse:  isReverse,
		QuestionId: questionID,
	})
	if err != nil || rpcRsp == nil {
		return nil, 0, err
	}

	// 获取docs
	var docs []*aipb.ChatMessageDoc
	if withDocs {
		docs, err = DescribeMessagesDocs(ctx, rpcRsp.Answers, false)
		if err != nil {
			return nil, 0, err
		}
	}

	// 把回答拼接到question的数组里
	messages := combineQuestionAndAnswer(rpcRsp.Questions, rpcRsp.Answers, docs)
	return messages, rpcRsp.TotalCount, nil
}

func (l *MessageDbLogic) CreateQuestion(
	ctx context.Context, user *iampb.UserInfo, assistantId uint64,
	req *aipb.ChatMessage,
) (*aipb.ChatMessage, error) {
	question := &aipb.ChatMessage{
		ChatId:        req.ChatId,
		Type:          aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER,
		Text:          req.Text,
		CreateBy:      user.Id,
		ImageUrl:      req.ImageUrl,
		AssistantId:   req.AssistantId,
		PublishHashId: req.PublishHashId,
		Files:         req.Files,
	}
	question.RejectReason = ReviewChatMessage(ctx, question)
	res, err := client.AiByUser(user).CreateChatMessage(ctx, &aipb.ReqCreateChatMessage{Message: question, AssistantId: assistantId})
	if err != nil {
		return nil, xerrors.InternalServerError(err)
	}
	return res.Message, nil
}

// CreateSimpleTextAnswer 创建答案
func (l *MessageDbLogic) CreateSimpleTextAnswer(ctx context.Context, question *aipb.ChatMessage, user *iampb.UserInfo, text string, messageType aipb.ChatMessageType) (*aipb.ChatMessage, error) {
	if question.Id == 0 {
		return nil, xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	endTime := time.Now()
	answer := &aipb.ChatMessage{
		PublishHashId: question.PublishHashId,
		ChatId:        question.ChatId,
		Type:          messageType,
		Text:          text,
		CreateBy:      user.Id,
		QuestionId:    question.Id,
		EndTime:       timestamppb.New(endTime),
		AssistantId:   question.AssistantId,
	}
	res, err := client.AiByUser(user).CreateChatMessage(ctx, &aipb.ReqCreateChatMessage{Message: answer, AssistantId: question.AssistantId})
	if err != nil {
		return nil, xerrors.InternalServerError(err)
	}

	return res.Message, nil
}

func (l *MessageDbLogic) CreateAnswer(ctx context.Context, user *iampb.UserInfo, answer *aipb.ChatMessage) (*aipb.ChatMessage, error) {
	res, err := client.AiByUser(user).CreateChatMessage(ctx, &aipb.ReqCreateChatMessage{Message: answer, AssistantId: answer.AssistantId})
	if err != nil {
		return nil, xerrors.InternalServerError(err)
	}
	return res.Message, nil
}

func (l *MessageDbLogic) CreateReceiveQuestion(ctx context.Context, req *bffaipb.ReqReceiveChatMessage, user *iampb.UserInfo) (*aipb.ChatMessage, error) {
	m := &aipb.ChatMessage{
		ChatId:        req.ChatId,
		Type:          aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER,
		Text:          req.Text,
		CreateBy:      user.Id,
		ImageUrl:      req.ImageUrls,
		AssistantId:   req.AssistantId,
		PublishHashId: req.PublishHashId,
	}
	question, err := l.CreateQuestion(ctx, user, req.AssistantId, m)
	if err != nil {
		return nil, xerrors.InternalServerError(err)
	}
	return question, nil
}

func (l *MessageDbLogic) CreateChatAndQuestion(ctx context.Context, req *bffaipb.ReqCreateChat, assistant *aipb.AssistantDetail, user *iampb.UserInfo) (*aipb.ChatMessage, error) {
	title := ugc.Limit(req.Title, 60)
	chatType := aipb.ChatType_CHAT_TYPE_WEB
	if req.MiniProgram {
		chatType = aipb.ChatType_CHAT_TYPE_MINIPROGRAM
	}
	createRsp, err := client.AiByUser(user).CreateUserChat(ctx, &aipb.ReqCreateUserChat{
		Title:       title,
		CreateBy:    user.Id,
		AssistantId: assistant.Id,
		Type:        chatType,
		RegionCode:  user.RegionCode,
	})
	if err != nil {
		return nil, xerrors.InternalServerError(err)
	}
	tag.AsyncUpdateAIChatAutoLabel(ctx, user.Id, assistant.Id, user.Id)
	m := &aipb.ChatMessage{
		ChatId:        createRsp.Id,
		Type:          aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER,
		Text:          req.Title,
		CreateBy:      user.Id,
		ImageUrl:      req.ImageUrls,
		AssistantId:   assistant.Id,
		PublishHashId: req.PublishHashId,
	}
	question, err := l.CreateQuestion(ctx, user, assistant.Id, m)
	if err != nil {
		return nil, xerrors.InternalServerError(err)
	}
	return question, nil
}

// CheckMessageFilesAllReady 检查文件是否都解析完成
func CheckMessageFilesAllReady(files []*aipb.ChatMessageFile) bool {
	for _, file := range files {
		if file.State == aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSING {
			return false
		}
	}
	return true
}

func parsePdfToImageUrl(url string) (string, error) {
	cosPath, err := extractPathFromURL(url)
	if err != nil {
		return "", err
	}

	const docFilePathPattern = "chat_doc"
	dirPath, err := os.MkdirTemp("./", docFilePathPattern) // ./chat_doc123456789
	if err != nil {
		return "", err
	}
	originFilePath := path.Join(dirPath, filepath.Base(cosPath))
	defer os.RemoveAll(dirPath)

	if _, err := xcos.Client("public").Object.GetToFile(context.Background(),
		cosPath, originFilePath, nil); err != nil {
		return "", err
	}

	newImagePath, err := GenerateLongImageFromPDF(originFilePath)
	if err != nil {
		return "", err
	}

	return newImagePath, nil
}

const CosUploadPATH = "/ai/chat/"

func extractPathFromURL(fullURL string) (string, error) {
	// 解析URL
	u, err := url.Parse(fullURL)
	if err != nil {
		return "", err
	}
	// 去掉开头的斜杠（如果有）
	if len(u.Path) > 0 && u.Path[0] == '/' {
		u.Path = u.Path[1:]
	}
	// 返回路径部分
	return u.Path, nil
}

func parseFileToCosUrl(fileUrl string) (string, error) {
	var savePath string
	//如果文件类型是pdf，则需要转换为长图
	ext := strings.ToLower(getFileExtension(fileUrl))
	if ext == "pdf" {
		newImagePath, err := parsePdfToImageUrl(fileUrl)
		if err != nil {
			return "", err
		}

		savePath = newImagePath
	}
	if ext == "png" || ext == "jpeg" || ext == "jpg" {
		tempPath, err2 := compressImage(savePath)
		if err2 != nil {
			return "", err2
		}

		savePath = tempPath
	}
	if savePath == "" {
		return "", xerrors.InternalServerError(fmt.Errorf("invalid cos save path: %s", fileUrl))
	}

	uploadPath := path.Join(CosUploadPATH, uuid.NewString()+"."+getFileExtension(savePath))
	if _, err := cosClient(reviewpb.CosBucketType_COS_BUCKET_TYPE_PUBLIC).Object.PutFromFile(context.Background(),
		uploadPath, savePath, new(cos.ObjectPutOptions)); err != nil {
		return "", err
	}

	if err := os.Remove(savePath); err != nil {
		return "", err
	}
	return config.GetString("oss.publicCdn") + uploadPath, nil
}

func getImageUrlsFromMessageFiles(fileUrls []string) ([]string, []string) {
	var images, parseFileUrls []string
	for _, url := range fileUrls {
		ext := strings.ToLower(getFileExtension(url))
		if ext == "png" || ext == "jpeg" || ext == "jpg" {
			images = append(images, url)
		} else {
			parseFileUrls = append(parseFileUrls, url)
		}
	}
	return images, parseFileUrls
}

// handlePdfFileParsing 处理单个 PDF 文件的解析，包括超时控制
func handlePdfFileParsing(ctx context.Context, user *iampb.UserInfo, questionId uint64, file *aipb.ChatMessageFile, wg *sync.WaitGroup) {
	defer wg.Done()

	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(config.GetIntOr("llm.file_pdf_parsing_timeout", 10000))*time.Millisecond)
	defer cancel()

	resultChan := make(chan struct {
		parsedUrl string
		err       error
	}, 1)
	go func() {
		parsedUrl, err := parseFileToCosUrl(file.Url)
		resultChan <- struct {
			parsedUrl string
			err       error
		}{parsedUrl, err}
	}()

	select {
	case res := <-resultChan:
		if res.err != nil {
			file.State = aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS
			log.WithContext(ctx).Errorf("parse file to cos url error: %v", res.err)
		} else {
			file.ParsedUrl = imageWithSuffix(res.parsedUrl)
			file.State = aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS
		}
	case <-timeoutCtx.Done():
		file.State = aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS
		log.WithContext(ctx).Errorf("parse file to cos url timeout after 30s for url: %s", file.Url)
	}
}

func imageWithSuffix(url string) string {
	if url == "" {
		return url
	}

	// 检查URL是否包含参数
	if idx := strings.Index(url, "?"); idx != -1 {
		// URL包含参数，替换所有参数
		baseURL := url[:idx]
		return baseURL + "?imageMogr2/format/jpg"
	}

	// URL不包含参数，直接添加后缀
	return url + "?imageMogr2/format/jpg"
}

func (l *MessageDbLogic) CreateChatSubscribeQuestion(ctx context.Context, req *bffaipb.ReqCreateChatQuestion, user *iampb.UserInfo, assistant *aipb.AssistantDetail, isMiniProgram bool) (*aipb.ChatMessage, error) {
	var err error
	var question *aipb.ChatMessage
	var chatId = req.ChatId
	if chatId == 0 {
		chatTitle := ugc.Limit(req.Text, 60)
		chatType := aipb.ChatType_CHAT_TYPE_WEB
		if isMiniProgram {
			chatType = aipb.ChatType_CHAT_TYPE_MINIPROGRAM
		}
		createRsp, err := client.AiByUser(user).CreateUserChat(ctx, &aipb.ReqCreateUserChat{
			Title:       chatTitle,
			CreateBy:    user.Id,
			AssistantId: assistant.Id,
			Type:        chatType,
			RegionCode:  user.RegionCode,
		})
		if err != nil {
			return nil, xerrors.InternalServerError(err)
		}
		chatId = createRsp.Id
	}

	tag.AsyncUpdateAIChatAutoLabel(ctx, user.Id, assistant.Id, user.Id)

	imageUrls, parseFileUrls := getImageUrlsFromMessageFiles(req.FileUrls)
	var files []*aipb.ChatMessageFile
	for _, fileUrl := range req.FileUrls {
		file := &aipb.ChatMessageFile{
			Url:   fileUrl,
			State: aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSING,
		}
		if xslice.Contains(imageUrls, fileUrl) != -1 {
			file.ParsedUrl = fileUrl
			file.State = aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS
		}
		files = append(files, file)
	}
	m := &aipb.ChatMessage{
		ChatId:      chatId,
		Type:        aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER,
		Text:        req.Text,
		CreateBy:    user.Id,
		AssistantId: assistant.Id,
		ImageUrl:    imageUrls,
		Files:       files,
	}
	question, err = l.CreateQuestion(ctx, user, assistant.Id, m)
	if err != nil {
		return nil, xerrors.InternalServerError(err)
	}

	if len(parseFileUrls) == 0 { // 没有需要解析的文件
		question.IsFileReady = true
	}
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		parseRequestImagesToChatURl(ctx, user, question.Id, question.Files)
		return nil
	}, boot.TraceGo(ctx))

	return question, nil
}

// CreateDraftAnswer 生成draft answer
func CreateDraftAnswer(ctx context.Context, user *iampb.UserInfo, assistantId uint64, question *aipb.ChatMessage, hashId string) (uint64, error) {
	l := &MessageDbLogic{}
	answer, err := l.CreateAnswer(ctx, user, &aipb.ChatMessage{
		ChatId:        question.ChatId,
		AssistantId:   assistantId,
		QuestionId:    question.Id,
		PublishHashId: hashId,
		Type:          aipb.ChatMessageType_CHAT_MESSAGE_TYPE_DRAFT,
	})

	if err != nil {
		return 0, err
	}
	return answer.Id, nil
}

func parseRequestImagesToChatURl(ctx context.Context, user *iampb.UserInfo, questionId uint64, files []*aipb.ChatMessageFile) {
	log.WithContext(ctx).Infow("parseRequestImagesToChatURl in", "questionId", questionId, "files", files)
	if len(files) == 0 {
		return
	}

	// Use a WaitGroup to wait for all goroutines to finish
	var wg sync.WaitGroup

	for _, file := range files {
		fileUrl := file.Url
		if fileUrl == "" {
			continue
		}
		ext := strings.ToLower(getFileExtension(fileUrl))

		switch ext {
		case "pdf": // pdf在bff进行解析，解析成图片
			wg.Add(1)
			go handlePdfFileParsing(ctx, user, questionId, file, &wg)
		case "png", "jpeg", "jpg": // 图片直接存储成功状态
			file.ParsedUrl = imageWithSuffix(fileUrl)
			file.State = aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS
		default:
			file.State = aipb.ChatMessageFileState_CHAT_MESSAGE_FILE_STATE_PARSING
		}
	}

	// Wait for all PDF parsing goroutines to complete
	wg.Wait()

	log.WithContext(ctx).Infow("parseRequestImagesToChatURl request", "questionId", questionId, "files", files)
	_, err := client.AiByUser(user).ParseChatDoc(ctx, &aipb.ReqParseChatDoc{
		MessageId: questionId,
		Files:     files,
	})
	if err != nil {
		log.WithContext(ctx).Errorf("parse chat message error: %v", err)
	}
}
