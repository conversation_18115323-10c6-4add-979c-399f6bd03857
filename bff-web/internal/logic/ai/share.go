package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	micro "github.com/asim/go-micro/v3/client"
	"golang.org/x/sync/errgroup"
)

// ListDocShareConfigReceiverAssistantUserTeam 获取设置的用户、团队信息
/*
  @return: userNames, teamNames, error
*/
func ListDocShareConfigReceiverAssistantUserTeam(ctx context.Context, assistantReceiver *aipb.RspListDocShareConfigReceiverAssistant) (map[uint64]string,
	map[uint64]string, error,
) {
	var err error
	var teamIds []uint64
	var userIds []uint64

	var teamNames map[uint64]string
	var userNames map[uint64]string

	for _, receiver := range assistantReceiver.UserShares {
		teamIds = append(teamIds, receiver.TeamId...)
		userIds = append(userIds, receiver.UserId...)
	}

	teamIds = filterZero(teamIds)
	userIds = filterZero(userIds)

	wg, ctx := errgroup.WithContext(ctx)
	if len(userIds) > 0 {
		wg.Go(func() error {
			userNames, err = iamlogic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			return nil
		})
	}

	if len(teamIds) > 0 {
		wg.Go(func() error {
			teamNames, err = iamlogic.FetchTeamName(ctx, teamIds)
			if err != nil {
				return err
			}
			return nil
		})
	}

	err = wg.Wait()
	if err != nil {
		return nil, nil, err
	}

	return userNames, teamNames, err
}

// ListDocShareConfigReceiverUserTeamUserTeam 获取个人/团队接收方设置的用户、团队信息
/*
  @return: userNames, teamNames, error
*/
func ListDocShareConfigReceiverUserTeamUserTeam(ctx context.Context, userTeamReceiver *aipb.RspListDocShareConfigReceiverUserTeam) (map[uint64]string,
	map[uint64]string, error,
) {
	var err error
	var teamIds []uint64
	var userIds []uint64

	var teamNames map[uint64]string
	var userNames map[uint64]string

	for _, receiver := range userTeamReceiver.UserShares {
		teamIds = append(teamIds, receiver.TeamId...)
		userIds = append(userIds, receiver.UserId...)
	}

	teamIds = filterZero(teamIds)
	userIds = filterZero(userIds)

	wg, ctx := errgroup.WithContext(ctx)

	if len(userIds) > 0 {
		wg.Go(func() error {
			userNames, err = iamlogic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			return nil
		})
	}

	if len(teamIds) > 0 {
		wg.Go(func() error {
			teamNames, err = iamlogic.FetchTeamName(ctx, teamIds)
			if err != nil {
				return err
			}
			return nil
		})
	}

	err = wg.Wait()
	if err != nil {
		return nil, nil, err
	}

	return userNames, teamNames, err
}

func filterZero(numbers []uint64) []uint64 {
	var result []uint64

	for _, num := range numbers {
		if num != 0 {
			result = append(result, num)
		}
	}

	return result
}

// ListAssistantCanShareDoc 获取可分享的助手列表
func ListAssistantCanShareDoc(ctx context.Context, name, lang string) (*aipb.RspListAssistantCanShareDoc, error) {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqListAssistantCanShareDoc{
		CreateBy:  user.Id,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_USER,
		Name:      name,
		Language:  lang,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	sharedAssistant, err := client.AiNational.ListAssistantCanShareDoc(ctx, reqSender)
	if err != nil {
		return nil, err
	}

	return sharedAssistant, err
}

// CreateDocShare 创建分享， 从handler拷贝过来的， 用于logic包复用
func CreateDocShare(ctx context.Context, req *bffaipb.ReqCreateAssistantShare,
	rsp *bffaipb.RspCreateAssistantShare, opts ...micro.CallOption,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	var adminType basepb.IdentityType
	var createBy uint64

	createBy = user.Id
	adminType = basepb.IdentityType_IDENTITY_TYPE_USER

	if iamlogic.IsTeamUser(ctx) {
		adminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
		createBy = user.FirmId
	}
	operator, err := GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}

	if req.DocId > 0 {
		// 权限校验
		err := CheckDocEditPermission(ctx, req.DocId)
		if err != nil {
			return xerrors.ForbiddenError("")
		}
	}
	if len(req.DocIds) != 0 {
		err := CheckDocEditPermission(ctx, req.DocIds...)
		if err != nil {
			return xerrors.ForbiddenError("")
		}
	}

	rpcRsp, err := client.AiNational.CreateDocShare(ctx, &aipb.ReqCreateDocShare{
		DocId:       req.DocId,
		DocIds:      req.DocIds,
		AssistantId: req.AssistantId,
		UserId:      req.UserId,
		TeamId:      req.TeamId,
		CreateBy:    createBy,
		AdminType:   adminType,
		QueryId:     req.QueryId,
		Operator:    operator,
	}, opts...)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if rpcRsp != nil {
		rsp.Async = rpcRsp.Async
	}

	return nil
}
