package ai

import (
	"context"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tql"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"golang.org/x/xerrors"
)

var (
	// NormalUserLabelIdOffset 个人用户租户起始值5万亿
	NormalUserLabelIdOffset = uint64(5000000000000)
	TeamUserLabelIdOffset   = 0
)

var (
	ErrCustomLabelCircled = xerrors.New("custom label circled")
)

// CustomLabelValidateLogic 自定义列校验逻辑
type CustomLabelValidateLogic struct{}

// Validate 校验
func (l *CustomLabelValidateLogic) Validate(label *aipb.CustomLabel) error {
	var err error
	err = l.Circled(label)
	return err
}

func (l *CustomLabelValidateLogic) Circled(label *aipb.CustomLabel) error {
	if label.GetNextLabelId() == label.GetId() && label.GetId() != 0 {
		return ErrCustomLabelCircled
	}
	return nil
}

// GetCustomLabelTenantId 获取自定义标签的租户id
func GetCustomLabelTenantId(ctx context.Context) (uint64, error) {
	if iam.IsTeamUser(ctx) {
		return GetUserTeamId(ctx)
	} else {
		user := xsession.UserFromContext[iampb.UserInfo](ctx)
		return user.Id + NormalUserLabelIdOffset, nil
	}
}

func unhashLabel(field *tql.StandardField) error {
	if strings.HasPrefix(field.Name, "label.") {
		hashed := strings.TrimPrefix(field.Name, "label.")
		intid, err := hashids.Decode(hashed)
		if err != nil {
			return err
		}
		field.Name = "label." + strconv.Itoa(int(intid))
	}
	return nil
}

// UnHashTQLLabelId 解码标签的hashid
func UnHashTQLLabelId(exp tql.Expression) error {
	for _, v := range exp {
		switch e := v.(type) {
		case *tql.Condition:
			filed := e.Field
			switch f := filed.(type) {
			case *tql.StandardField:
				err := unhashLabel(f)
				if err != nil {
					return err
				}
			case *tql.FieldFunc:
				for _, v := range f.Fields {
					err := unhashLabel(v)
					if err != nil {
						return err
					}
				}

			}
		case *tql.Group:
			err := UnHashTQLLabelId(e.Expression)
			if err != nil {
				return err
			}
		}
	}
	return nil
}
