package ai

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"sync"
	"syscall"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/pkg/aisub"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	"github.com/redis/go-redis/v9"
)

type PublishMessage struct {
	Content          string                     `json:"content,omitempty"`
	State            int32                      `json:"state,omitempty"`
	SuggestQuestions []string                   `json:"suggest_questions,omitempty"`
	QuestionId       string                     `json:"question_id,omitempty"`
	HashMsg          *aipb.EventChatHashMessage `json:"hash_msg,omitempty"`
	HashId           string                     `json:"hash_id,omitempty"`
	AnswerIndex      int32                      `json:"answer_index,omitempty"`
	Type             aipb.ChatMessageType       `json:"type,omitempty"`
	ThinkDuration    int                        `json:"think_duration,omitempty"`
	Id               string                     `json:"id,omitempty"`
	ChatId           string                     `json:"chat_id,omitempty"`
}

type WriteMsg struct {
	id  int
	msg []byte
}

type ChatResponseWriter struct {
	w                           http.ResponseWriter
	flusher                     http.Flusher
	userId                      uint64
	WriteDuration               int32
	writeChan                   *aisub.Chan[WriteMsg]
	writeChanOnce               sync.Once
	closeChanOnce               sync.Once
	closeChan                   chan struct{}
	ctx                         context.Context
	redisChan                   *aisub.Chan[*redis.Message]
	HashId                      string
	pubSub                      *redis.PubSub
	hashQuestionId              string
	question                    *aipb.ChatMessage
	assistant                   *aipb.AssistantDetail
	refMsg, suggestMsg, doneMsg *PublishMessage
	errMsg                      *PublishMessage
	thinkDurationOnce           sync.Once
	hasThink                    bool
	nextMsgChan                 *aisub.Chan[*redis.Message]
	nextMsgChanOnce             sync.Once
	currentAnswerIndex          int32
	shouldBreakCheckResult      bool
	repeatErrText               string
	thinkDuration               int
	requestStart                time.Time
	chunkIdMark                 bool

	currentID int
	IdMux     sync.Mutex

	pushMsgMutex sync.Mutex

	writerDestroyed bool

	// 用于缓存相同state的消息
	lastState     int32
	cachedContent string
	cachedMsg     *PublishMessage
	contentMutex  sync.Mutex
	combineChunks bool
}

func (c *ChatResponseWriter) monitorChannelUsage(ctx context.Context, ch <-chan *redis.Message, bufferSize int) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	var readings []int
	//env := config.GetStringOr("llm.env", "dev")
	for {
		select {
		case <-ticker.C:
			currentLen := len(ch)
			readings = append(readings, currentLen)

			// 保留最近的读数
			if len(readings) > 30 {
				readings = readings[1:]
			}

			usagePercent := float64(currentLen) / float64(bufferSize) * 100

			switch {
			case usagePercent > 90:
				//support.LLMWeChatSendMsg(fmt.Sprintf("【环境%s】严重: PubSub channel buffer is nearly full: %.2f%% used，hashId:%s", env, usagePercent, c.HashId))
				log.WithContext(ctx).Errorf("CRITICAL: PubSub channel buffer is nearly full: %.2f%% used，hashId:%s", usagePercent, c.HashId)
			case usagePercent > 80:
				//support.LLMWeChatSendMsg(fmt.Sprintf("【环境%s】告警: PubSub channel buffer is nearly full: %.2f%% used，hashId:%s", env, usagePercent, c.HashId))
				log.WithContext(ctx).Warnf("WARNING: PubSub channel buffer is heavily used: %.2f%% used，hashId:%s", usagePercent, c.HashId)
			case usagePercent > 70:
				//support.LLMWeChatSendMsg(fmt.Sprintf("【环境%s】提醒: PubSub channel buffer is nearly full: %.2f%% used，hashId:%s", env, usagePercent, c.HashId))
				log.WithContext(ctx).Infof("NOTICE: PubSub channel buffer usage is high: %.2f%% used，hashId:%s", usagePercent, c.HashId)
			}

		case <-ctx.Done():
			return
		}
	}
}

// CreateRedisPubSub 创建消息订阅
func (c *ChatResponseWriter) CreateRedisPubSub(ctx context.Context) {
	// 用于定期检查msg是否推送完成跳出循环
	ticker := time.NewTicker(time.Duration(config.GetIntOr("llm.chat_stream_check_msg_received", 50)) * time.Millisecond)
	defer ticker.Stop()
	// 用于定期发送心跳保活
	heartbeatTicker := time.NewTicker(2 * time.Second)
	// 检查15秒内没有新消息推送退出循环
	//timeout := time.Duration(config.GetIntOr("llm.chat_stream_gap_max", 15)) * time.Second
	//var timer = time.NewTimer(0)
	//defer timer.Stop()
	//if !timer.Stop() {
	//	<-timer.C
	//}

	c.ctx = ctx

	defer func() {
		// 发送state=8的msg通知前端断开连接
		c.sendLastMsg()
		c.afterRedisConsume()
	}()

	// 启动异步写入协程
	go c.asyncWriter(ctx)

	c.requestStart = time.Now()

	xsync.SafeGo(ctx, func(ctx context.Context) error {
		c.redisChan.Consume(func(message *redis.Message) {
			//fmt.Printf("~~redisChan receive:%s, time: %v\n", message.Payload, time.Now())
			//c.writeChan.Send(WriteMsg{
			//	id:  1,
			//	msg: []byte(message.Payload),
			//})
			c.handleRedisMsg(ctx, message, false)
		})
		return nil
	}, boot.TraceGo(ctx))

	c.publishHeartbeat(c.question)

	// 订阅rub
	bufferSize := config.GetIntOr("llm.pubsub_buffer_size", 500) // 默认值200
	ch := c.createPubSub(ctx, bufferSize)

	// pubSub缓存区告警监控
	go c.monitorChannelUsage(ctx, ch, bufferSize)

	// 创建批处理器
	batchProcessor := NewMessageBatchProcessor(
		config.GetIntOr("llm.batch_size", 10),                                     // 默认批处理10条消息
		time.Duration(config.GetIntOr("llm.batch_timeout", 100))*time.Millisecond, // 默认100ms超时
		func(messages []*redis.Message) error {
			for _, msg := range messages {
				c.redisChan.Send(msg)
			}
			return nil
		},
	)

	// 启动批处理器
	batchProcessor.Start(ctx)

	breakCount := 0

	errChan := make(chan error, 1)

	// 修改消息处理逻辑
	for {
		select {
		case msg, ok := <-ch:
			if !ok {
				return
			}
			// 将消息发送到批处理器
			batchProcessor.messages <- msg
			heartbeatTicker.Stop()
		case <-heartbeatTicker.C:
			c.publishHeartbeat(c.question)
		case <-ctx.Done():
			return
		case err := <-errChan:
			if err != nil {
				return // 如果有错误发生，直接返回
			}
		case <-ticker.C:
			if !c.shouldBreakCheckResult {
				c.shouldBreakCheckResult = c.shouldBreak(ctx)
			} else {
				if breakCount > 1 {
					return // 如果已经跳出两次，直接返回
				}
				breakCount++
				// c.shouldBreakCheckResult = true
				if c.nextMsgChan != nil { // 如果hashNext为true，必须等待  c.nextMsgChan赋值
					c.nextMsgChanOnce.Do(func() {
						xsync.SafeGo(ctx, func(ctx context.Context) error {
							c.resetShouldBreak()
							c.requestStart = time.Now()
							errChan <- c.nextMsgChan.Consume(func(message *redis.Message) {
								c.handleRedisMsg(ctx, message, true)
							})
							return nil
						}, boot.TraceGo(ctx))
					})

				} else {
					log.WithContext(ctx).Infow("CreateRedisPubSub shouldBreak, closing connection.")
					return
				}
			}
			//case <-timer.C:
			//	log.WithContext(ctx).Infow("CreateRedisPubSub No message received for 15 seconds, closing connection.")
			//	return
		}
	}
}

// SendUnSendChunks 发送因客户端断开未发送的消息
func (c *ChatResponseWriter) SendUnSendChunks(ctx context.Context, startId int, reqBody *bffaipb.ReqChatSubscribe) {
	store := NewChunksSubscriber(reqBody.HashId)
	log.Infow("ChatSubscribe SendUnSendChunks start", "hashId", c.HashId, "startId", startId)

	receiveTimeout := time.Duration(config.GetIntOr("llm.chat_channel_receive_timeout", 8)) * time.Second
	timer := time.NewTimer(receiveTimeout)
	heartbeatTicker := time.NewTicker(30 * time.Second) // 心跳检测
	defer heartbeatTicker.Stop()

	var messageCount int
	var lastMessageTime time.Time

	xsync.SafeGo(ctx, func(ctx context.Context) error {
		var index int
		store.chunksChan.Consume(func(s string) {
			if index > startId {
				if err := c.flushWrite(ctx, s); err != nil {
					log.WithContext(ctx).Errorw("chunksChan consume err", "hashId", c.HashId, "err", err, "finalData", s)
				}

				// 重置超时定时器
				if !timer.Stop() {
					<-timer.C
				}

				// 根据消息类型设置不同的超时时间
				if strings.Contains(s, "[EOF]") {
					timer.Reset(3 * time.Second) // EOF后给更多时间
					log.WithContext(ctx).Infow("Received EOF, extending timeout", "hashId", c.HashId)
				} else {
					timer.Reset(receiveTimeout)
				}

				messageCount++
				lastMessageTime = time.Now()
			}
			index++
		})
		return nil
	}, boot.TraceGo(ctx))

	// 先启动订阅，确保不遗漏消息
	go store.Subscribe(ctx)

	log.WithContext(ctx).Infow("ChatSubscribe chunksChan consume started", "hashId", c.HashId)

	for {
		select {
		case <-ctx.Done():
			store.Stop()
			c.destroyWriter("[ChatSubscribe] Context cancelled")
			return
		case <-timer.C:
			store.Stop()
			store.chunksChan.Close()
			c.writeChan.Close()
			log.WithContext(ctx).Infow("ChatSubscribe timeout", "hashId", c.HashId, "messageCount", messageCount, "lastMessageTime", lastMessageTime)
			c.destroyWriter("[ChatSubscribe] SendUnSendChunks timeout")
			c.CloseClientConnect()
			return
		case <-heartbeatTicker.C:
			// 定期心跳检测
			log.WithContext(ctx).Infow("ChatSubscribe heartbeat", "hashId", c.HashId, "messageCount", messageCount)
		}
	}
}

// NewWriter 生成新的writer实例
func NewWriter(ctx context.Context, creator *ChatGenerateLogic, closeChan chan struct{}, w http.ResponseWriter) (*ChatResponseWriter, error) {
	var err error
	cw := &ChatResponseWriter{}
	cw, err = setFlusher(cw, w)
	if err != nil {
		return nil, err
	}

	cw.closeChan = closeChan
	cw.ctx = ctx
	cw.userId = creator.User.Id
	cw.writeChan = aisub.NewChan[WriteMsg]()
	cw.redisChan = aisub.NewChan[*redis.Message]()
	cw.HashId = creator.HashId
	cw.assistant = creator.Assistant
	cw.WriteDuration = creator.Duration
	cw.chunkIdMark = true
	cw.combineChunks = creator.CombineChunks

	if cw.combineChunks {
		cw.WriteDuration = int32(config.GetIntOr("llm.chat_channel_write_timeout", 200))
	}

	return cw, nil
}

func (c *ChatResponseWriter) SetQuestion(question *aipb.ChatMessage) {
	c.question = question
	c.hashQuestionId, _ = hashids.Encode(c.question.Id)
}

// setFlusher 设置流式相关的请求头和writer
func setFlusher(c *ChatResponseWriter, w http.ResponseWriter) (*ChatResponseWriter, error) {
	// 设置流式响应头（SSE 格式）
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Credentials", "true")
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Transfer-Encoding", "chunked")
	//w.Header().Set("Content-Type", "text/plain")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")

	// 确保支持 Flush
	flusher, ok := w.(http.Flusher)
	if !ok {
		return nil, errors.New("ChatSubscribe Streaming not supported")
	}
	c.flusher = flusher
	c.w = w
	return c, nil
}

func (c *ChatResponseWriter) handleRefAndSendMsg(ctx context.Context, redisMessage *aipb.ChatPushMessage) {
	xsync.SafeGo(ctx, func(ctx context.Context) error {
		var pushMsg *PublishMessage
		if redisMessage.IsHashEvent {
			var hashMsg *aipb.EventChatHashMessage
			log.WithContext(ctx).Infow("[ChatResponseWriter] getPublishMsg IsHashEvent", "hashId", c.HashId, "redisMessage.Content", redisMessage.Content)
			if err := json.Unmarshal([]byte(redisMessage.Content), &hashMsg); err == nil { // state=2的回答和建议问题
				pushMsg = c.toPublishMessage(hashMsg, c.hashQuestionId)
			}
			// 直接发一个text chunk，包括（未命中doc的默认回复、命中QA的回复、未走流式的回复、MESSAGE_TYPE_SYSTEM_ERROR（type=5）、MESSAGE_TYPE_TIMEOUT_ERROR（type=7）、预设回复MESSAGE_TYPE_SET_CHART_ANSWER(type = 9)
			if pushMsg != nil && pushMsg.HashMsg != nil && (pushMsg.HashMsg.WaitAnswer || pushMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT_CODE ||
				pushMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR || pushMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR || pushMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER ||
				pushMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION && pushMsg.HashMsg.DocMatchPattern != aipb.DocMatchPattern_DOC_MATCH_PATTERN_LARGE_MODEL_RECALL) &&
				pushMsg.HashMsg.Text != "" {
				chunk := &PublishMessage{
					Content:       pushMsg.HashMsg.Text,
					State:         int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK),
					QuestionId:    c.hashQuestionId,
					HashId:        c.HashId,
					AnswerIndex:   pushMsg.HashMsg.AnswerIndex,
					ThinkDuration: 1,
				}
				c.pushToWrite(chunk)
			}
		} else {
			msg := redisMessage.HashMsg
			var docs []*aipb.ChatMessageDoc
			if msg == nil {
				pushMsg = &PublishMessage{
					Type: aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR,
				}
			} else {
				if msg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION && len(msg.DocNames) > 0 {
					if d, err := DescribeMessagesDocs(ctx, []*aipb.ChatMessage{msg}, true); err != nil {
						log.WithContext(ctx).Errorw("ChatResponseWriter DescribeMessagesDocs error", "err", err)
						return nil
					} else {
						docs = d
					}
				}
				em := TransformAIMessageToEventMessage(msg, docs)
				eventMessage := EventMessageTransformWithHash(em)
				pushMsg = c.toPublishMessage(eventMessage, c.hashQuestionId)
			}
		}

		if pushMsg != nil {
			c.SetSpecialMsg(pushMsg)
			c.pushToWrite(pushMsg)
		}
		return nil
	})
}

// getPublishMsg 获取带推送的msg
func (c *ChatResponseWriter) getPublishMsg(ctx context.Context, redisMessage *aipb.ChatPushMessage) *PublishMessage {
	if redisMessage.State == aipb.ChatMessageState_CHAT_MESSAGE_STATE_SUGGESTION {
		pushMsg := &PublishMessage{
			State:            int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SUGGESTION),
			HashId:           c.HashId,
			SuggestQuestions: redisMessage.Suggests,
		}
		c.SetSpecialMsg(pushMsg)
		return pushMsg
	}

	if redisMessage.State == aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK {
		if !c.hasThink {
			c.hasThink = true
		}
		pushMsg := &PublishMessage{
			Content:     redisMessage.Content,
			State:       int32(redisMessage.State),
			HashId:      c.HashId,
			AnswerIndex: redisMessage.AnswerIndex,
		}
		return pushMsg
	}

	if redisMessage.State == aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK {
		if c.hasThink {
			c.thinkDurationOnce.Do(func() {
				thinkDuration := int(time.Since(c.requestStart).Seconds())
				pushMsg := &PublishMessage{
					Content:       "",
					State:         int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK),
					QuestionId:    c.hashQuestionId,
					HashId:        c.HashId,
					ThinkDuration: thinkDuration,
				}
				c.thinkDuration = thinkDuration
				c.pushToWrite(pushMsg)
			})
		}

		pushMsg := &PublishMessage{
			Content:     redisMessage.Content,
			State:       int32(redisMessage.State),
			HashId:      c.HashId,
			AnswerIndex: redisMessage.AnswerIndex,
		}
		return pushMsg
	}

	if redisMessage.State == aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK_DONE { // state=10, chunk推送完成，text="[DONE]"
		log.WithContext(ctx).Infow("ChatResponseWriter handleRedisMsg receive done chunk", "chunk", redisMessage.Content)
		pushMsg := &PublishMessage{
			State:       int32(redisMessage.State),
			HashId:      c.HashId,
			AnswerIndex: redisMessage.AnswerIndex,
		}
		c.SetSpecialMsg(pushMsg)
		return pushMsg
	}
	return nil

}

// handleRedisMsg 处理redis消息
func (c *ChatResponseWriter) handleRedisMsg(ctx context.Context, msg *redis.Message, isNextWriter bool) {
	var redisMessage *aipb.ChatPushMessage
	var err error

	if err = json.Unmarshal([]byte(msg.Payload), &redisMessage); err != nil {
		return
	}
	if c.HashId != redisMessage.HashId {
		return
	}

	if redisMessage.AnswerIndex > 0 && c.nextMsgChan == nil {
		c.nextMsgChan = aisub.NewChan[*redis.Message](aisub.WithTimeout[*redis.Message](time.After(5 * time.Second)))
	}

	if !isNextWriter && redisMessage.AnswerIndex == 2 {
		c.nextMsgChan.Send(msg)
		return
	}

	if redisMessage.IsHashEvent || redisMessage.State == aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_REF {
		c.handleRefAndSendMsg(ctx, redisMessage)
		return
	}

	pushMsg := c.getPublishMsg(ctx, redisMessage)
	if pushMsg == nil {
		log.WithContext(ctx).Infow("[ChatResponseWriter] getPublishMsg fail", "hashId", c.HashId, "msg", redisMessage)
		return
	}

	if c.isRepeatError(pushMsg) {
		return
	}

	if c.combineChunks {
		c.pushToWriteCombine(pushMsg)
	} else {
		c.pushToWrite(pushMsg)
	}
}

func (c *ChatResponseWriter) SetSpecialMsg(pushMsg *PublishMessage) {
	c.pushMsgMutex.Lock()
	defer c.pushMsgMutex.Unlock()
	switch pushMsg.State {
	case int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND):
		c.refMsg = pushMsg
	case int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SUGGESTION):
		c.suggestMsg = pushMsg
	case int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK_DONE):
		c.doneMsg = pushMsg
	}
}

// isRepeatError 检查是否是重复的err msg
func (c *ChatResponseWriter) isRepeatError(pushMsg *PublishMessage) bool {
	if pushMsg.Type != aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR {
		return false
	}
	if c.repeatErrText == "" {
		if pushMsg.HashMsg != nil {
			c.repeatErrText = pushMsg.HashMsg.Text
		} else {
			c.repeatErrText = pushMsg.Content
		}
		return false
	} else {
		if c.repeatErrText == pushMsg.Content || (pushMsg.HashMsg != nil && pushMsg.HashMsg.Text == c.repeatErrText) {
			return true
		}
	}

	return false
}

func (c *ChatResponseWriter) resetShouldBreak() {
	c.currentAnswerIndex = 0
	c.errMsg = nil
	c.refMsg = nil
	c.doneMsg = nil
	c.shouldBreakCheckResult = false
}

func (c *ChatResponseWriter) createPubSub(ctx context.Context, bufferSize int) <-chan *redis.Message {
	topic := GetChatChannelTopic(c.HashId)
	c.pubSub = xredis.Default.Subscribe(ctx, topic)
	ch := c.pubSub.Channel(redis.WithChannelSize(bufferSize))
	return ch
}

func (c *ChatResponseWriter) shouldBreak(ctx context.Context) bool {
	var (
		errMsg     = c.errMsg
		refMsg     = c.refMsg
		doneMsg    = c.doneMsg
		suggestMsg = c.suggestMsg
	)

	// 判断是否跳出流式推送
	if errMsg != nil {
		return true
	}
	if refMsg != nil && refMsg.HashMsg != nil {
		if refMsg.HashMsg.DocMatchPattern == aipb.DocMatchPattern_DOC_MATCH_PATTERN_MISS_MATCH {
			return true
		}
		if suggestMsg != nil && refMsg.HashMsg.WaitAnswer {
			return true
		}
		if refMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY {
			log.WithContext(ctx).Infow("ChatResponseWriter ChatSubscribe break: receive sql query msg", "msg", refMsg)
			return true
		}
		if suggestMsg != nil && (refMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION && refMsg.HashMsg.DocMatchPattern != aipb.DocMatchPattern_DOC_MATCH_PATTERN_LARGE_MODEL_RECALL) {
			log.WithContext(ctx).Infow("ChatResponseWriter ChatSubscribe break: receive doc and suggest match msg", "msg", refMsg, "suggestMsg", suggestMsg)
			return true
		}
		if refMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT_CODE || refMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_CLEAR_HISTORY || refMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER {
			log.WithContext(ctx).Infow("ChatResponseWriter ChatSubscribe break: receive an hao msg", "msg", refMsg)
			return true
		}

		if doneMsg != nil && suggestMsg != nil {
			log.WithContext(ctx).Infow("ChatResponseWriter ChatSubscribe all msg published", "doneMsg", doneMsg, "refMsg", refMsg, "suggestMsg", suggestMsg)
			return true
		}

		if refMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR || refMsg.HashMsg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR {
			return true
		}
	}
	return false
}

func (c *ChatResponseWriter) flush(ctx context.Context, eventID int, batchBuffer bytes.Buffer) {
	if batchBuffer.Len() == 0 {
		return
	}
	var finalData string
	if eventID > 0 {
		finalData = fmt.Sprintf("id: %d\n%s", eventID, batchBuffer.String())
	} else {
		finalData = batchBuffer.String()
	}

	if !c.writerDestroyed {
		if err := c.flushWrite(ctx, finalData); err != nil {
			log.WithContext(ctx).Errorw("Async write failed", "hashId", c.HashId, "err", err, "finalData", finalData)
		}
	}

	StoreChatChunk(c.ctx, c.HashId, finalData)
	batchBuffer.Reset()
}

func (c *ChatResponseWriter) getBuffer(by []byte) bytes.Buffer {
	var batchBuffer bytes.Buffer
	if strings.Contains(string(by), "data:") {
		batchBuffer.Write(by)
	} else {
		batchBuffer.WriteString("data: ")
		batchBuffer.Write(by)
		batchBuffer.WriteString("\n\n")
	}
	return batchBuffer
}

// 异步写入协程（包含批量合并逻辑）
func (c *ChatResponseWriter) asyncWriter(ctx context.Context) {
	var (
		logCount = 0
	)

	handler := func(data WriteMsg) {
		finalData := string(data.msg)
		if logCount < 20 || strings.Contains(finalData, "suggest_questions") || strings.Contains(finalData, "hash_msg") || strings.Contains(finalData, "[EOF]") || strings.Contains(finalData, "\"state\":8") {
			log.WithContext(c.ctx).Infow("flush write buffer", "hashId", c.HashId, "data", finalData)
			logCount++
		}

		//fmt.Printf("~~writeChan receive:%s, time: %v\n", string(data.msg), time.Now())
		batchBuffer := c.getBuffer(data.msg)
		time.Sleep(time.Duration(c.WriteDuration) * time.Millisecond)
		c.flush(ctx, data.id, batchBuffer)
	}

	c.writeChan.Consume(handler)
}

func (c *ChatResponseWriter) flushWrite(ctx context.Context, str string) error {
	const (
		maxRetries   = 2
		retryDelay   = 100 * time.Millisecond
		writeTimeout = 10 * time.Second
	)

	data := []byte(str)
	retries := 0
	if c.w == nil {
		return errors.New("c.w http.ResponseWriter is nil")
	}

	// 分块写入 + 自动重试剩余数据
	for len(data) > 0 {
		n, err := c.w.Write(data) // w.Write 逐步写入数据，即使因缓冲区满或客户端处理慢导致部分写入，循环也会继续处理剩余数据
		//fmt.Printf("## flushWrite write:%s\n", string(str))

		if err != nil {
			if isClosedConnectionError(err) {
				// 客户端已断开，无需重试
				log.WithContext(c.ctx).Infow("ChatResponseWriter flush write failed, client is closed connection", "hashId", c.HashId, "err", err)
				c.destroyWriter("ChatResponseWriter flush write failed, client is closed connection")
				return err
			}

			// 可恢复错误，重试write
			if retries < maxRetries && isRecoverableError(err) {
				log.WithContext(c.ctx).Infow("ChatResponseWriter flush write failed, write retry", "retries", retries, "err", err)
				time.Sleep(retryDelay)
				retries++
				continue
			}
			return err
		}
		data = data[n:] // 跳过已成功写入的部分
	}

	c.safeFlush()
	return nil
}

// 安全刷新
func (c *ChatResponseWriter) safeFlush() {
	if c.w == nil {
		return
	}
	flusher, ok := c.w.(http.Flusher)
	if !ok {
		return
	}
	if flusher != nil {
		flusher.Flush()
	}
}

func (c *ChatResponseWriter) send(by []byte) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Recovered from panic:", r)
		}
	}()

	var eventID int
	c.IdMux.Lock()
	c.currentID++
	eventID = c.currentID
	c.IdMux.Unlock()
	c.writeChan.Send(WriteMsg{
		id:  eventID,
		msg: by,
	})

}

func (c *ChatResponseWriter) pushToWrite(pushMsg *PublishMessage) {
	by := c.TransMessageToByte(pushMsg)
	//fmt.Printf("!!pushToWrite pushMsg:%s\n", string(by))
	c.send(by)
	if pushMsg.State != int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK) && pushMsg.State != int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK) {
		log.WithContext(c.ctx).Infow("ChatResponseWriter publish refMsg", "msg", pushMsg)
	}
}

func (c *ChatResponseWriter) pushToWriteCombine(pushMsg *PublishMessage) {
	c.contentMutex.Lock()
	defer c.contentMutex.Unlock()

	// 如果是流式chunk或think状态，尝试合并消息
	if pushMsg.State == int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK) ||
		pushMsg.State == int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK) {

		// 如果是相同state且缓存不为空
		if c.lastState == pushMsg.State && c.cachedMsg != nil {
			newContent := c.cachedContent + pushMsg.Content
			// 如果合并后长度不超过30，则进行合并
			if len(newContent) <= 30 {
				c.cachedContent = newContent
				c.cachedMsg.Content = newContent
				return
			} else {
				// 超过30，先发送缓存的消息
				by := c.TransMessageToByte(c.cachedMsg)
				c.send(by)
				// 重置缓存，存储新消息
				c.cachedContent = pushMsg.Content
				c.cachedMsg = pushMsg
				c.lastState = pushMsg.State
				return
			}
		}

		// 不同state或首次收到消息，直接缓存
		c.cachedContent = pushMsg.Content
		c.cachedMsg = pushMsg
		c.lastState = pushMsg.State
		return
	}

	// 非流式消息，先发送缓存的消息（如果有）
	if c.cachedMsg != nil {
		by := c.TransMessageToByte(c.cachedMsg)
		c.send(by)
		c.cachedMsg = nil
		c.cachedContent = ""
		c.lastState = 0
	}

	// 发送当前消息
	by := c.TransMessageToByte(pushMsg)
	c.send(by)
	if pushMsg.State != int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK) &&
		pushMsg.State != int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK) {
		log.WithContext(c.ctx).Infow("ChatResponseWriter publish refMsg", "msg", pushMsg)
	}
}

func (c *ChatResponseWriter) destroyWriter(reason string) {
	log.WithContext(c.ctx).Infow("ChatResponseWriter destroyWriter in", "reason", reason, "hashId", c.HashId)
	c.writeChanOnce.Do(func() {
		if closer, ok := c.w.(io.Closer); ok {
			_ = closer.Close()
		}
		c.w = nil // 防止悬空引用
		c.writerDestroyed = true
	})
}

func (c *ChatResponseWriter) sendLastMsg() {
	lastMsg := &PublishMessage{ // state=8, 全部推送完成
		QuestionId: c.hashQuestionId,
		Content:    "state",
		State:      int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_DONE),
		HashId:     c.HashId,
	}
	c.pushToWrite(lastMsg)
	event := fmt.Sprintf("event: heartbeat\ndata: %s\n\n", "")
	c.send([]byte(event))
	c.send([]byte(event))
	c.send([]byte("data: [EOF]\n\n"))
}

func (c *ChatResponseWriter) updateMessage(id uint64) {
	if c.thinkDuration == 0 {
		c.thinkDuration = int(time.Since(c.requestStart).Seconds())
	}
	if id > 0 {
		_, err := client.AiByID(id).UpdateChatMessageThink(c.ctx, &aipb.ReqUpdateChatMessageThink{MessageId: id, Duration: int32(c.thinkDuration)})
		if err != nil {
			log.WithContext(c.ctx).Errorw("UpdateChatMessageThink err", "err", err)
		}
	}
}

func (c *ChatResponseWriter) CloseClientConnect() {
	if c.closeChan != nil {
		c.closeChanOnce.Do(func() {
			close(c.closeChan)
		})
	}
}

func (c *ChatResponseWriter) afterRedisConsume() {
	log.Infow("ChatResponseWriter afterRedisConsume in", "hashId", c.HashId)
	if c.question.AnswerDraftId > 0 {
		c.updateMessage(c.question.AnswerDraftId)
	}

	time.Sleep(time.Duration(config.GetIntOr("llm.chat.pubSub_close_delay", 1000)) * time.Millisecond)
	c.redisChan.Close()
	err := c.pubSub.Close()
	if err != nil {
		log.WithContext(c.ctx).Errorw("pub/sub close", "err", err)
		return
	}

	c.writeChan.Close()
	c.destroyWriter("ChatResponseWriter after redisConsume end")
	c.CloseClientConnect()

	log.Infow("ChatResponseWriter afterRedisConsume done", "hashId", c.HashId)
}

func (c *ChatResponseWriter) TransMessageToByte(msg *PublishMessage) []byte {
	var by []byte
	if b, err := json.Marshal(msg); err != nil {
		return by
	} else {
		by = b
	}
	return by
}

func (c *ChatResponseWriter) publishHeartbeat(question *aipb.ChatMessage) {
	var str string
	if question != nil {
		qId, _ := hashids.Encode(question.Id)
		aId, _ := hashids.Encode(question.AnswerDraftId)
		chatId, _ := hashids.Encode(question.ChatId)

		msg := &PublishMessage{
			QuestionId: qId,
			Id:         aId,
			HashId:     c.HashId,
			ChatId:     chatId,
		}
		by := c.TransMessageToByte(msg)
		str = string(by)
	}
	event := fmt.Sprintf("event: heartbeat\ndata: %s\n\n", str)
	c.send([]byte(event))
}

func isRecoverableError(err error) bool {
	var netErr net.Error
	if errors.As(err, &netErr) && netErr.Timeout() {
		return true // 超时错误可重试
	}
	return errors.Is(err, syscall.EAGAIN) || // 资源暂时不可用
		strings.Contains(err.Error(), "temporary failure")
}

func isClosedConnectionError(err error) bool {
	if errors.Is(err, net.ErrClosed) {
		return true
	}
	var opErr *net.OpError
	if errors.As(err, &opErr) {
		// 检查常见错误：Broken Pipe、Connection Reset
		if strings.Contains(opErr.Err.Error(), "broken pipe") ||
			strings.Contains(opErr.Err.Error(), "connection reset") {
			return true
		}
	}
	// 检查系统级错误（跨平台）
	if errors.Is(err, syscall.EPIPE) ||
		errors.Is(err, syscall.ECONNRESET) ||
		strings.Contains(err.Error(), "use of closed network connection") {
		return true
	}
	return false
}

func (c *ChatResponseWriter) toPublishMessage(eventMessage *aipb.EventChatHashMessage, question string) *PublishMessage {
	var pushMsg = &PublishMessage{
		Content:     eventMessage.Text,
		State:       int32(eventMessage.State),
		QuestionId:  question,
		HashId:      c.HashId,
		AnswerIndex: eventMessage.AnswerIndex,
		Type:        eventMessage.Type,
	}

	if eventMessage.State == int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_SEND) || eventMessage.State == int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_REF) {
		pushMsg.Content = "" // TODO 后面开放[n]要新增一个state来返回过滤后的[n]全文本
		pushMsg.HashMsg = &aipb.EventChatHashMessage{
			Id:              eventMessage.Id,
			Type:            eventMessage.Type,
			Docs:            eventMessage.Docs,
			Link:            eventMessage.Link,
			QuestionId:      eventMessage.QuestionId,
			Ugcs:            eventMessage.Ugcs,
			State:           eventMessage.State,
			Lang:            eventMessage.Lang,
			SuggestCount:    eventMessage.SuggestCount,
			ChatId:          eventMessage.ChatId,
			Text:            eventMessage.Text,
			WaitAnswer:      eventMessage.WaitAnswer,
			DocMatchPattern: eventMessage.DocMatchPattern,
			Think:           eventMessage.Think,
			AnswerIndex:     eventMessage.AnswerIndex,
		}
	}

	// 运营端展示字段
	//if isMgmt {
	//  pushMsg.HashMsg.ShowType = eventMessage.ShowType
	//	pushMsg.HashMsg.DocMatchPattern = eventMessage.DocMatchPattern
	//	pushMsg.HashMsg.ProcessTime = eventMessage.ProcessTime
	//	pushMsg.HashMsg.CustomQuestionId = eventMessage.CustomQuestionId
	//	pushMsg.HashMsg.SqlQuery = eventMessage.SqlQuery
	//	pushMsg.HashMsg.DocFinalQuery = eventMessage.DocFinalQuery
	//	pushMsg.HashMsg.Think = eventMessage.Think
	//}

	if eventMessage.SuggestQuestions != nil {
		pushMsg.SuggestQuestions = eventMessage.SuggestQuestions
	}
	return pushMsg
}

func CreateChatHashId() string {
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	hash := md5.Sum([]byte(timestamp))
	return hex.EncodeToString(hash[:8]) // 只取前 8 个字节
}
