package aware

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"github.com/go-redsync/redsync/v4"
	"github.com/redis/go-redis/v9"
)

const (
	// AiDealQuestionCountKey 当前正在处理的问题计数(一个问题可能会有多个答案，答案的数量也会记录在此)
	AiDealQuestionCountKey = "{chat:%d}:ai:deal:questionCount"
	// AiDealQuestionListKey 当前正在处理的问题队列
	AiDealQuestionListKey = "{chat:%d}:ai:deal:questionList"
	// AiDealQuestionCancelKey 当前所有增在处理的问题取消（可能已经转人工了或者开启新会话了）
	AiDealQuestionCancelKey = "{chat:%d}:ai:deal:questionCancel"
)

// MessageAnswerAware 处理判断逻辑
type MessageAnswerAware[T any] interface {
	handled(question *aipb.ChatMessage) bool
	DoExtension(context.Context, *aipb.ChatWeChat, *T) // 附加扩展逻辑
	GetAnswerString() string
	GetAnswerType(askType ...aipb.QuestionAskType) aipb.QuestionAskType
	GetAnswerLevel() int
}

// MessageAwareImpl ...
type MessageAwareImpl[T any] struct {
	ExactKeywords      []string                                    `json:"exact_keywords"`      // 精确匹配关键词
	FoldExactKeywords  []string                                    `json:"fold_exact_keywords"` // 忽略大小写精确匹配关键词
	FuzzyKeywords      []string                                    `json:"fuzzy_keywords"`      // 模糊匹配关键词
	CustomKeywordsFunc func(*aipb.ChatMessage) bool                // 自定义匹配规则
	Extension          func(context.Context, *aipb.ChatWeChat, *T) // 满足匹配规则后执行的扩展内容
}

// DoExtension 执行扩展内容
func (a *MessageAwareImpl[T]) DoExtension(ctx context.Context, chat *aipb.ChatWeChat, t *T) {
	if a.Extension != nil {
		log.WithContext(ctx).Debugw("send wechat do extension", "chat", chat, "message", t)
		a.Extension(ctx, chat, t)
	}
}

// GetAnswerString 获取答案文本
func (a *MessageAwareImpl[T]) GetAnswerString() string {
	return ""
}

// GetAnswerLevel 获取答案等级
func (a *MessageAwareImpl[T]) GetAnswerLevel() int {
	return 0
}

func (a *MessageAwareImpl[T]) handled(question *aipb.ChatMessage) bool {
	if xslice.IsContainsString(a.ExactKeywords, question.Text) {
		return true
	}

	for _, keyword := range a.FoldExactKeywords {
		if strings.EqualFold(question.Text, keyword) {
			return true
		}
	}

	for _, keyword := range a.FuzzyKeywords {
		if strings.Contains(question.Text, keyword) {
			return true
		}
	}

	if a.CustomKeywordsFunc != nil {
		return a.CustomKeywordsFunc(question)
	}

	return false
}

// GetAnswerType 获取答案类型
func (a *MessageAwareImpl[T]) GetAnswerType(_ ...aipb.QuestionAskType) aipb.QuestionAskType {
	return aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET
}

// MessageAiInterface ...
type MessageAiInterface[T any] interface {
	// Init 初始化配置信息等
	Init()
	GetConf() MessageAIConfig[T]
	// BeforeStart 执行获取答案的逻辑之前的前置逻辑
	BeforeStart(context.Context) bool
	// Do 执行获取答案的逻辑
	Do(*aipb.ChatWeChat, T) (DoResultRecord, MessageAnswerAware[T])
	// GetChatMessage 获取对话要处理信息，筛选出需要处理的消息；返回按照会话分组的消息以及消息的锁id
	GetChatMessage([]T) map[*aipb.ChatWeChat][]T
	// Send 执行发送逻辑
	Send(DoResultRecord, T, MessageAnswerAware[T]) SendResultRecord[T]
	// AfterSend 执行发送之后的后置逻辑
	AfterSend(*aipb.ChatWeChat, T, SendResultRecord[T])
	// GetMessageId 获取处理的消息ID
	GetMessageId(T) string
}

// SendResultRecord ...
type SendResultRecord[T any] struct {
	Ctx        context.Context
	Err        error
	DoResult   DoResultRecord
	Sender     MessageAnswerAware[T]
	ChatRecord uint32
	SendFlag   bool // 是否需要计算入人工提醒的消息发送次数
	CheckPass  bool
}

// DoResultRecord 执行用户问题之后的答案记录
type DoResultRecord struct {
	ChatId     uint64 // 会话id
	QuestionId uint64 // 用户问题id
	Records    []*DoResult
	// 处理的图片问题的id（用户如果是先发送的图片然后给的非指令回复，此时并非每个图片都有一个回答，但是要从默认回复状态更新为已处理状态）
	FileQuestionIds []uint64
	AnswerSharding  []*aipb.AiSendResultRecord // 这个字段针对继续回答时
}

// DoResult 执行ai问答之后的单个答案的记录
type DoResult struct {
	QuestionId uint64 // 当前回答的问题id，此处的questionId主要是会出现一个问题涉及到多个回答
	AnswerId   uint64
	//AnswerRsp      string                     // 发送消息后的返回信息，如报错等
	Answer         *aipb.EventChatHashMessage // 原始答案
	AnswerSharding []*aipb.AiSendResultRecord // 答案可能被分块了，分别每一条recordId 对应一个第三方的uuid
	Reason         string
}

// MessageAIConfig ai配置结构体
type MessageAIConfig[T any] struct {
	Ctx           context.Context
	MessageAi     MessageAiInterface[T]
	MaxRetryTimes int           // 最大重试次数
	RetryInterval time.Duration // 重试时间间隔

	TimeoutDefaultAnswer string // 超时默认回复
	AuditErrorAnswer     string // 敏感信息审核错误回复

	UgcAnswerTemplate string // ugc回复模版 "我在tanlive上为你找到"
	UgcJumpUrl        string // ugc跳转链接
	ReadMore          string // readMore="More %s can be found here"
	AnswerFrom        string // answerFrom="\n\n答案整理自 "

	ReferenceAnswerTemplate string // collection信息模版
	ReferenceDataFrom       string // referenceDataFrom="参考资料\n"

	HelpCenterUrl   string // 帮助中心地址
	CosPublicCdnUrl string // cos CDN地址
}

// messageAILogic 执行ai整体逻辑的结构体
type messageAILogic[T any] struct {
	conf      MessageAIConfig[T]    // 配置信息
	chat      *aipb.ChatWeChat      // 当前会话的chat信息
	message   T                     // 消息
	doResult  DoResultRecord        // ai执行结果
	sender    MessageAnswerAware[T] // 发送消息的sender
	messageId string                // 消息Id
}

func (m *messageAILogic[T]) timingDo() error {
	maxInterval := time.NewTimer(m.conf.RetryInterval * time.Duration(m.conf.MaxRetryTimes)) // 最大时间执行时间计时器 = 重试间隔 * 最大重试次数
	defer maxInterval.Stop()

	if m.chat.Id > 0 {
		SetUnSendQuestionCount(m.conf.Ctx, m.chat.Id, 1, 0)        // 增加处理的问题数量
		defer SetUnSendQuestionCount(m.conf.Ctx, m.chat.Id, 0, 1)  // 减少问题数量
		PushAiDealQuestionList(m.conf.Ctx, m.chat.Id, m.messageId) // 将问题放入队尾，弹出则有执行发送逻辑的协程完成
	}

	m.doResult, m.sender = m.conf.MessageAi.Do(m.chat, m.message)
	log.WithContext(m.conf.Ctx).Debugw("timingDo info", "message", m.message, "doResult", m.doResult, "chat", m.chat)
	if len(m.doResult.Records) == 0 && len(m.doResult.AnswerSharding) == 0 {
		if m.chat.Id > 0 {
			PopAiDealQuestionList(m.conf.Ctx, m.chat.Id, m.messageId) // 此处主要是处理 某些特殊的如发送微信欢迎语的事件：不需要执行后续逻辑，但是消息已经进入队列了
		}
		return nil
	}

	ctx1, cancel := context.WithCancel(m.conf.Ctx)
	ch := make(chan struct{})
	xsync.SafeGo(ctx1, func(c context.Context) error {       // 在最大执行时间内 查询上一个结果是否已发送
		if m.sender.GetAnswerLevel() == AiAnswerLevelFirst { // 需要优先发送的信息
			m.send(true)
			ch <- struct{}{}
			return nil
		}

		retryInterval := time.NewTimer(m.conf.RetryInterval + 50*time.Millisecond) // 重试计时器
		defer retryInterval.Stop()
		for retryTime := 0; m.conf.MaxRetryTimes > retryTime; retryTime++ {
			time.Sleep(30 * time.Millisecond)

			log.WithContext(c).Debugw("SendMessageByWechat retry", "times", retryTime+1)
			if m.sendWithLock() {
				break
			}

			select {
			case <-c.Done(): // 超过最大时间
				m.send(true)
				return nil
			case <-retryInterval.C: // 重置重试时间
				retryInterval.Reset(m.conf.RetryInterval)
			}
		}
		ch <- struct{}{}
		return nil
	}, boot.TraceGo(m.conf.Ctx))

	select {
	case <-maxInterval.C:
		log.WithContext(m.conf.Ctx).Debugw("SendMessageByWechat over maxInterval", "chatId", m.chat.Id)
	case <-ch:
	}
	cancel()
	return nil
}

// sendWithLock
//
//	@Description: 加锁成功之后，判断当前消息是否在发送队列队首，如果不在则放弃锁，等待重试；如果在则直接发送
//	@receiver m
//	@return bool 是否处理了消息
func (m *messageAILogic[T]) sendWithLock() bool {
	//lockName := config.GetStringOr("workWeixin.ai.sendMessageLock", "ai:workWeinxin:sendMessageLock:%d")
	dmtx := logic.NewDistributedLock(fmt.Sprintf("{chatId:%d}:ai:sendMessageLock", m.chat.Id),
		redsync.WithExpiry(time.Minute), redsync.WithTries(10))

	err := dmtx.Lock()
	if err != nil {
		log.WithContext(m.conf.Ctx).Errorw("sendWithLock fail", "err", err)
		return false
	}
	defer dmtx.Unlock()

	msgId := PopAiDealQuestionList(m.conf.Ctx, m.chat.Id, m.messageId)
	if msgId == "-1" { // 已经被取消执行了
		return true
	} else if msgId != m.messageId { // 当前队首的元素还没执行完，继续等待
		log.WithContext(m.conf.Ctx).Debugw("sendWithLock await", "msgId", msgId, "messageId", m.messageId)
		return false
	}

	checkRsp, err1 := client.AiByID(m.chat.Id).GetLastQuestionStateWechat(m.conf.Ctx,
		&aipb.ReqGetLastQuestionStateWechat{
			ChatId:     m.chat.Id,
			QuestionId: m.doResult.QuestionId,
		})
	// 查询在此问题之前是否还存在未被回复的问题
	if err1 != nil {
		log.WithContext(m.conf.Ctx).Errorw("GetLastQuestionStateWechat get err", "err1", err1, "chatId", m.chat.Id)
	} else if checkRsp != nil && (checkRsp.State == 0 || checkRsp.State == 2 || checkRsp.State == 3) { // 上一条消息为已发送或者默认发送
		// 如果state=0 表示没有上一条消息或者上一条已经超时了;2 上一条已发送;3 上一条消息发送了默认信息
		m.send(false)
		return true
	}
	return false

}

func (m *messageAILogic[T]) send(unlocked bool) {
	defer func() {
		if unlocked { // 如果是没上锁执行则要将对应消息弹出，如果弹出后消息为空了则删除队列
			PopAiDealQuestionListUnlock(m.conf.Ctx, m.chat.Id, m.messageId)
		}
		if r := recover(); r != nil {
			log.WithContext(m.conf.Ctx).Errorw("SendMessageByWechat panic", "err", r, "stack", xsync.TakeStackTrace(0))
			m.afterSend(m.conf.MessageAi.Send(m.doResult, m.message, nil)) // 发送超时信息
		}
	}()
	if m.sender.GetAnswerLevel() == AiAnswerLevelFirst { // 需要优先发送的信息，设置标记
		SetAiDealQuestionCancel(m.conf.Ctx, m.chat.Id, false)
		defer SetAiDealQuestionCancel(m.conf.Ctx, m.chat.Id, true)
	}

	m.afterSend(m.conf.MessageAi.Send(m.doResult, m.message, m.sender))
}

func (m *messageAILogic[T]) afterSend(result SendResultRecord[T]) {
	if result.Err != nil {
		log.WithContext(result.Ctx).Errorw("AfterSend err", "err", result.Err, "sender is nil", result.Sender == nil)
		return
	}

	if result.Sender == nil {
		updateMessageState(result, m.chat.Id)
		m.conf.MessageAi.AfterSend(m.chat, m.message, result)
		return
	}

	// 处理正常有sender的返回结果逻辑
	result.SendFlag = false
	chatRecord := m.chat.DefaultAnswerRecord
	// chatRecord 每当为10的倍数时则触发默认回答，常规最多触发三次，最大为30（重新回答除外，每次将记录设置为30）
	switch result.Sender.GetAnswerType() {
	case aipb.QuestionAskType_QUESTION_ASK_TYPE_CONTINUE, aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET,
		aipb.QuestionAskType_QUESTION_ASK_TYPE_IMAGE, aipb.QuestionAskType_QUESTION_ASK_TYPE_FILE:
		break
	case aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION: // 触发【重新回答】规则
		chatRecord = chatRecord - chatRecord%10 // 重置个位数为0,超过最大限制时重置为30
		result.SendFlag = true
	default:
		if chatRecord%10 == 9 { // 如果之前已经到9了，那么当前就是第10条，需要发送逻辑
			result.SendFlag = true
		}
		chatRecord++ // 累加常规问题数量
	}
	result.ChatRecord = chatRecord
	if len(result.DoResult.Records) > 0 {
		updateMessageState(result, m.chat.Id)
	}

	m.conf.MessageAi.AfterSend(m.chat, m.message, result)
}

// GetMediaUrl ...
func (d *DoResult) GetMediaUrl(prefix string) (urls []string) {
	if d.Answer == nil || len(d.Answer.Docs) == 0 {
		return nil
	}
	for _, doc := range d.Answer.Docs {
		for _, v := range doc.Reference {
			if len(v.Url) == 0 || v.ShowType != aipb.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND {
				continue // 没有url或者类型不为直接发送
			}
			_, err := url.ParseRequestURI(v.Url)
			if err == nil && strings.HasPrefix(v.Url, "http") {
				continue // 是一个完整的url地址
			}

			urls = append(urls, prefix+v.Url)
		}
	}
	return
}

// DoMessageLogic 执行
func DoMessageLogic[T any](ctx context.Context, a MessageAiInterface[T], msg []T) {
	if !a.BeforeStart(ctx) {
		return
	}
	//conf := MessageAIConfig[T]{
	//	MessageAi: a,
	//	Ctx:       ctx,
	//}
	a.Init()
	conf := a.GetConf()
	conf.MessageAi = a

	chatMassageMap := conf.MessageAi.GetChatMessage(msg)
	if len(chatMassageMap) == 0 {
		log.WithContext(ctx).Debug("DoMessageLogic no chat massage to be deal")
		return
	}

	f := func(chat *aipb.ChatWeChat, message T) *messageAILogic[T] {
		return &messageAILogic[T]{
			conf:      conf,
			chat:      chat,
			message:   message,
			messageId: a.GetMessageId(message),
		}
	}
	for k, messages := range chatMassageMap {
		chat := k
		for _, v := range messages {
			message := v
			xsync.SafeGo(context.Background(), func(c context.Context) error {
				m := f(chat, message)
				if m == nil {
					return nil
				}

				if err := m.timingDo(); err != nil {
					log.WithContext(ctx).Errorw("DoMessageLogic timingDo", "err", err)
				}
				return nil
			}, boot.TraceGo(ctx))
		}
	}

}

// UpdateRateAiAnswer 更新评价信息
func UpdateRateAiAnswer(ctx context.Context, chat *aipb.ChatWeChat, content string, answerId uint64) {

	var scale aipb.RatingScale
	switch content {
	case RatingScaleSatisfiedZH, RatingScaleSatisfiedEN:
		scale = aipb.RatingScale_RATING_SCALE_SATISFIED
	case RatingScaleAverageZH, RatingScaleAverageEN:
		scale = aipb.RatingScale_RATING_SCALE_AVERAGE
	case RatingScaleDissatisfiedZH, RatingScaleDissatisfiedEN:
		scale = aipb.RatingScale_RATING_SCALE_DISSATISFIED
		// chat 不满意中发送了转人工，则记录需要将个位 置0
		chat.DefaultAnswerRecord = chat.DefaultAnswerRecord - chat.DefaultAnswerRecord%10
	default:
		log.WithContext(ctx).Errorw("updateRateAiAnswer ratingScale illegal", "chatId", chat.Id,
			"ratingScale", content, "answerId", answerId)
		return
	}

	if answerId == 0 {
		log.WithContext(ctx).Debugw("updateRateAiAnswer RateAiAnswerWechat answerId is nil", "chatId", chat.Id,
			"ratingScale", content)
		return
	}
	_, err := client.AiByID(chat.Id).RateAiAnswerWechat(ctx, &aipb.ReqRateAiAnswerWechat{
		ChatId:      chat.Id,
		AnswerId:    answerId,
		RatingScale: scale,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("updateRateAiAnswer RateAiAnswerWechat err", "chatId", chat.Id,
			"ratingScale", content, "messageId", answerId, "err", err)
	}
}

// updateMessageState 更新消息状态
func updateMessageState[T any](s SendResultRecord[T], chatId uint64) {
	if len(s.DoResult.Records) == 0 && len(s.DoResult.AnswerSharding) == 0 {
		log.WithContext(s.Ctx).Debugw("updateMessageState questionId is 0 ", "chatId", chatId)
		return
	}

	req := &aipb.ReqUpdateChatMessageStateWechat{
		NormalReply:     s.CheckPass,
		QuestionId:      s.DoResult.QuestionId,
		FileQuestionIds: s.DoResult.FileQuestionIds,
	}

	if len(s.DoResult.Records) > 0 { // 更新正常的AI询问
		req.Infos = make([]*aipb.ReqUpdateChatMessageStateWechat_Info, len(s.DoResult.Records))
		for i, v := range s.DoResult.Records {
			req.Infos[i] = &aipb.ReqUpdateChatMessageStateWechat_Info{
				QuestionId:   v.QuestionId,
				RejectReason: v.Reason,
				AnswerId:     v.AnswerId,
				Records:      v.AnswerSharding,
			}
		}

		if !s.SendFlag && s.ChatRecord <= 30 { // 正常的AI询问需要累加问题数量，如果sendFlag为true则要发送成功之后更新
			req.DefaultAnswerRecord = s.ChatRecord
			req.ChatId = chatId
		}
	}

	if len(s.DoResult.AnswerSharding) > 0 { // 更新 继续回答的结果
		req.Infos = []*aipb.ReqUpdateChatMessageStateWechat_Info{{
			QuestionId:   s.DoResult.QuestionId,
			RejectReason: "",
			AnswerId:     0,
			Records:      s.DoResult.AnswerSharding,
		}}
	}

	_, err := client.AiByID(chatId).UpdateChatMessageStateWechat(s.Ctx, req)

	if err != nil {
		log.WithContext(s.Ctx).Errorw("updateMessageState error", "err", err, "req", req)
	}
}

// FinishWeixinChat 结束用户会话
func FinishWeixinChat(ctx context.Context, chatId uint64, externalUserId string,
	assistantId uint64, chatType aipb.ChatType) {
	_, err := client.AiByID(chatId).FinishChatWechat(ctx, &aipb.ReqFinishChatWechat{
		ExternalUserId: externalUserId,
		AssistantId:    assistantId,
		ChatType:       chatType,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("UpdateChatType err", "err", err,
			"externalUserId", externalUserId, "assistantId", assistantId)
	}
}

// SetUnSendQuestionCount 设置未被处理的问题数量
func SetUnSendQuestionCount(ctx context.Context, chatId uint64, add, subtract int64) {
	key := fmt.Sprintf(AiDealQuestionCountKey, chatId)
	if add > 0 {
		xredis.Default.IncrBy(ctx, key, add)
		return
	}

	// 自减并在值为 0 时删除键
	luaScript := `
		local question = redis.call('GET', KEYS[1])  -- 获取键的当前值
		local arg = tonumber(ARGV[1])  -- 获取传入的 subtract
		if question then
			question = tonumber(question)  -- 转换为数字
			question = question - arg  -- 自减 arg
			if question <= 0 then
				redis.call('DEL', KEYS[1])  -- 如果值为 0，删除该键
			else
				redis.call('SET', KEYS[1], question)  -- 否则更新值
			end
		else
			return "key not exists"  -- 如果键不存在，返回
		end
	`
	result, err := xredis.Default.Eval(ctx, luaScript, []string{key}, subtract).Result()
	if !errors.Is(err, redis.Nil) {
		log.WithContext(ctx).Errorw("bff ReSetUnSendRecordCount Error", "err", err, "sub", subtract, "result", result)
	}
}

// PushAiDealQuestionList 在队列尾部添加元素
func PushAiDealQuestionList(ctx context.Context, chatId uint64, messageId string) {
	key := fmt.Sprintf(AiDealQuestionListKey, chatId)

	result, err := xredis.Default.RPush(ctx, key, messageId).Result()
	if err != nil {
		log.WithContext(ctx).Errorw("bff PushAiDealQuestionList Error",
			"err", err, "messageId", messageId, "result", result)
	}
}

// PopAiDealQuestionList 在队列首部弹出元素,如果弹出后为空了则删除问题队列
func PopAiDealQuestionList(ctx context.Context, chatId uint64, messageId string) string {
	key := fmt.Sprintf(AiDealQuestionListKey, chatId)
	cancelKey := fmt.Sprintf(AiDealQuestionCancelKey, chatId)

	luaScript := `
		-- 弹出队列第一个元素
		local value = redis.call('LPOP', KEYS[1])
		local message = ARGV[1]  -- 获取传入的 messageId

		local flag = redis.call('GET', KEYS[2])  -- 获取cancelKey的当前值，判断是否被取消了
		if flag then
			flag = tonumber(flag)  -- 转换为数字
		else
			flag = 0
		end

		if flag > 0 then -- 问题队列已经被取消了
			value = "-1"
		elseif value ~= message then
			redis.call('LPUSH', KEYS[1],value)
			return value
		end

		-- 如果队列为空，删除队列
		if redis.call('LLEN', KEYS[1]) == 0 then
			redis.call('DEL', KEYS[1])
			redis.call('DEL', KEYS[2])
		end
		
		return value
	`

	// 执行 Lua 脚本
	result, err := xredis.Default.Eval(ctx, luaScript, []string{key, cancelKey}, messageId).Result()
	if err != nil /*&& !errors.Is(err, redis.Nil)*/ {
		log.WithContext(ctx).Errorw("bff PopAiDealQuestionList Error",
			"err", err, "messageId", messageId, "key", key, "cancelKey", cancelKey)
	}

	return fmt.Sprintf("%v", result)
}

// SetAiDealQuestionCancel
//
//	@Description: 添加标记；取消标记-取消后续messageId的处理,并且如果只剩下当前协程的问题要处理，则删除标记,
//	@param ctx
//	@param chatId
//	@param cancel 是否删除取消标识
func SetAiDealQuestionCancel(ctx context.Context, chatId uint64, del bool) {
	cancelKey := fmt.Sprintf(AiDealQuestionCancelKey, chatId)

	if del {
		key := fmt.Sprintf(AiDealQuestionCountKey, chatId)
		luaScript := `
			local value = redis.call('GET', KEYS[1])
			if value == false then
				return "question count is nil"
			elseif tonumber(value) > 1 then -- 排除当前问题
				return "no delete"
			else
				redis.call('DEL', KEYS[2])
				return "deleted"
			end
		`
		result, err := xredis.Default.Eval(ctx, luaScript, []string{key, cancelKey}).Result()
		if err != nil {
			log.WithContext(ctx).Errorw("bff PushAiDealQuestionCancel", "err", err, "chatId", chatId, "result", result)
			return
		}
		log.WithContext(ctx).Debugw("bff SetAiDealQuestionCancel", "result", result)
		return
	}
	err := xredis.Default.SetEx(ctx, cancelKey, chatId, 24*time.Hour).Err()
	if err != nil {
		log.WithContext(ctx).Errorw("bff PushAiDealQuestionCancel Error", "err", err, "chatId", chatId)
	}
}

// PopAiDealQuestionListUnlock 发送时，把处理的当前消息及其之前的消息全部清除，
func PopAiDealQuestionListUnlock(ctx context.Context, chatId uint64, messageId string) {
	key := fmt.Sprintf(AiDealQuestionListKey, chatId)
	cancelKey := fmt.Sprintf(AiDealQuestionCancelKey, chatId)
	// Lua 脚本：检查列表中是否存在值，并弹出该值以及之前的元素，若队列为空则删除队列
	luaScript := `
		local value = ARGV[1]
		local list = KEYS[1]

		-- 获取列表中的所有元素
		local list_len = redis.call('LLEN', list)
		local items = redis.call('LRANGE', list, 0, list_len - 1)

		-- 遍历列表中的元素
		for i, item in ipairs(items) do
		    if item == value then
		        -- 如果找到了目标元素，则弹出该元素及其前面的所有元素
		        redis.call('LTRIM', list, i, -1)
		        -- 检查队列是否为空，如果为空则删除队列
		        local new_len = redis.call('LLEN', list)
		        if new_len == 0 then
		            redis.call('DEL', list)  -- 删除队列
					redis.call('DEL', KEYS[2])  -- 删除 标记
		            return "Queue was empty after removal, and was deleted"
		        end
		        return "Value found and removed"
		    end
		end

		-- 如果没有找到目标元素，返回不做处理
		return "Value not found, no changes"
	`

	// 执行 Lua 脚本，传递队列名称和目标值
	result, err := xredis.Default.Eval(ctx, luaScript, []string{key, cancelKey}, messageId).Result()
	if err != nil {
		log.WithContext(ctx).Errorw("bff PopAiDealQuestionListUnlock error", "err", err, "chatId", chatId, "result", result)
		return
	}
	log.WithContext(ctx).Debugw("bff PopAiDealQuestionListUnlock info", "result", result)
}
