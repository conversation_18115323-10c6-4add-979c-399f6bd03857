package aware

import (
	"context"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
)

const (
	workWeixinSuiteTicket      = "WorkWeixinSuiteTicket"
	workWeixinSuiteAccessToken = "WeixinSuiteAccessToken"

	AddOpenKfId = "AddOpenKfId"
	DelOpenKfId = "DelOpenKfId"
)

// FetchEnterpriseAuthCode 获取企业授权码
func FetchEnterpriseAuthCode(ctx context.Context, authCode string, suiteTicket string) error {

	if len(authCode) > 0 {

		suiteTicketCache := xredis.Default.Get(ctx, workWeixinSuiteTicket)

		for i := 0; i < 600; i++ {
			if suiteTicketCache != nil && suiteTicketCache.Val() != "" {
				break
			}
			// 值不存在，等待 1 秒后重试
			time.Sleep(1 * time.Second)
		}

		service := workweixin.MessageService{}
		suiteAccessToken, err := service.GetSuiteToken(config.GetString("weixin.open.suiteId"), config.GetString("weixin.open.suiteSecret"),
			suiteTicketCache.Val())
		if err != nil {
			return err
		}

		log.WithContext(ctx).Infow("FetchEnterpriseAuthCode data", "suiteAccessToken", suiteAccessToken)

		accessToken, permanentCode, corpId, err := service.GetPermanentCode(suiteAccessToken, authCode)
		if err != nil {
			return err
		}
		xredis.Default.Set(ctx, workWeixinSuiteAccessToken, suiteAccessToken, 7000*time.Second)

		_, err = client.AiNational.CreateOpenWechat(ctx, &aipb.ReqCreateOpenWechat{PermanentCode: permanentCode, CorpId: corpId})
		if err != nil {
			return err
		}

		corpIdKey := fmt.Sprintf("%s:corpId", corpId)
		xredis.Default.Set(ctx, corpIdKey, accessToken, 7000*time.Second)
	}

	if len(suiteTicket) > 0 {
		xredis.Default.Set(ctx, workWeixinSuiteTicket, suiteTicket, 1600*time.Second)

		service := workweixin.MessageService{}
		suiteAccessToken, err := service.GetSuiteToken(config.GetString("weixin.open.suiteId"), config.GetString("weixin.open.suiteSecret"),
			suiteTicket)
		if err != nil {
			return err
		}

		xredis.Default.Set(ctx, workWeixinSuiteAccessToken, suiteAccessToken, 7000*time.Second)
	}
	return nil
}

// LoadOpenWorkAccessToken 加载企业授权码
func LoadOpenWorkAccessToken(toFromUser string) string {

	if config.GetString("weixin.open.myCorpID") == toFromUser {
		return ""
	}
	ctx := context.Background()

	corpIdKey := fmt.Sprintf("%s:corpId", toFromUser)
	accessToken := xredis.Default.Get(ctx, corpIdKey).Val()
	if len(accessToken) == 0 {
		permanentCode, err := client.AiNational.DescribeOpenWechat(ctx, &aipb.ReqDescribeOpenWechat{CorpId: toFromUser})
		if err != nil {
			return ""
		}

		suiteTicketCache := xredis.Default.Get(ctx, workWeixinSuiteAccessToken)

		service := workweixin.MessageService{}
		token, err := service.GetCorPIdAccessToken(suiteTicketCache.Val(), toFromUser, permanentCode.PermanentCode)
		if err != nil {
			log.WithContext(ctx).Errorw("LoadOpenWorkAccessToken GetCorPIdAccessToken err", "err", err)
			return ""
		}

		xredis.Default.Set(ctx, corpIdKey, token, 7000*time.Second)
		return token

	}

	return accessToken
}

// CreateOpenKeIdAction 创建open_ke_id 记录
func CreateOpenKeIdAction(ctx context.Context, toFromUser string, authAddOpenKfId string, authDelOpenKfId string) error {

	if len(authAddOpenKfId) == 0 && len(authDelOpenKfId) == 0 {
		return nil
	}

	action := AddOpenKfId
	OpenKfId := authAddOpenKfId
	if len(authDelOpenKfId) > 0 {
		action = DelOpenKfId
		OpenKfId = authDelOpenKfId
	}

	if _, err := client.AiNational.CreateOpenWechat(ctx, &aipb.ReqCreateOpenWechat{
		OpenKfid: OpenKfId, Action: action, CorpId: toFromUser}); err != nil {
		return err
	}

	return nil
}
