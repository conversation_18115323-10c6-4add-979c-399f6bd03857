package aware

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	mclient "github.com/asim/go-micro/v3/client"
)

const (
	RatingScaleSatisfiedZH    = "满意"
	RatingScaleAverageZH      = "一般"
	RatingScaleDissatisfiedZH = "不满意"

	AiFileProcessingRedisKey = "{chat:%d}:workWeixin:ai:cmdFileRedisKey"

	AiProcessingModelFile     = 1 // 文件处理模式
	AiProcessingModelContinue = 2 // 继续回答

	ReadBom        = "读配料表"
	DecipherBom    = "解读配料表"
	ReadBomPicture = "读配料表照片"

	ReadReport     = "读检测报告"
	DecipherReport = "解读检测报告"

	AiAnswerLevelFirst = 1

	ContinueAnswerStr        = "继续回答"
	CustomerServiceAnswerStr = "人工"

	RecordPrefix    = "record:"
	RecordPrefixFmt = "record:%d"

	WechatClickMaxSize = 128
)

// WechatMessageSetAnswer ...
type WechatMessageSetAnswer struct {
	MessageAwareImpl[workweixin.Message]
	HeadMessage string             `json:"head_message"`
	TailMessage string             `json:"tail_message"`
	Click       []workweixin.Clink `json:"click"`
	//View          []string `json:"view"`
	Content    string `json:"content"`
	Think      string `json:"think"`
	AskType    aipb.QuestionAskType
	BeforeSend func(*WechatMessageSetAnswer, workweixin.Message)
	AfterSend  func(context.Context, uint64, *aipb.ChatWeChat, workweixin.Message) bool // 发送后置执行，用于控制是否发送人工提醒，为true时则满足发送条件时会发送，false则一定不会推送人工提醒
	Model      int32                                                                    // 0 常规 1 文件处理 2 继续回答（纯文本）
	NoPass     bool                                                                     // 是否标记为未正常处理，标记为未正常处理后后续可以补充逻辑处理
	Text       []string                                                                 `json:"text"`
	Level      int                                                                      `json:"level"` //执行等级，为1的话会不管其他问题直接发送
	CdnUrl     string
}

// Answers 预设的问答回复
var defaultWechatAnswers []MessageAnswerAware[workweixin.Message]
var defaultWechatEmoji map[string]string
var wechatEmojiReg = regexp.MustCompile(`^(\[[^\]]+\]\s*)+$`)
var wechatEmojiFindReg = regexp.MustCompile(`\[[^\]]+\]`)
var wechatFileSuffix []string

func init() {
	defaultWechatEmoji = map[string]string{
		"[强]":     "谢谢你的点赞，小海豹会努力哒 ❤",
		"[爱心]":   "我也爱你哦~",
		"[旺柴]":   "狗头保命！",
		"[微笑]":   "[大笑]",
		"[撇嘴]":   "哎呀，是不是我哪里做得不够好，让你不满意了？去tanlive.com“教教小海豹”，狠狠教育我，我一定改，还会把你作为贡献者留在我的知识库里！",
		"[色]":     "你是不是看到了什么让你心动的东西？快告诉我，让我也分享一下你的快乐！",
		"[叹气]":   "是不是需要一点正能量？我这里有满满的正能量，分你一些！",
		"[嘴唇]":   "我就知道，你一定是在享受我们的聊天。我也是，希望我们的友谊能像这个emoji一样甜蜜！",
		"[胜利]":   "耶！这是你今天的胜利手势吗？恭喜你！我也为你感到高兴！",
		"[烟花]":   "让我们的对话像烟花一样绚烂！",
		"[猪头]":   "宝宝你是一只小猪，除了我，其他人接近你都是为了把你变成脆皮五花肉~",
		"[右哼哼]": "别这样嘛，我可是个聊天的好伙伴，我们还有很多话题可以聊呢！",
		"[流泪]":   "不要哭嘛，我的肩膀借你靠一靠，有什么不开心的事情，告诉我，我们一起解决！",
		"[捂脸]":   "是不是我刚才的笑话太冷了？我再给你讲个热乎的！",
		"[发呆]":   "是不是被我的机智回答震撼到了？这只是开始，精彩还在后头！",
		"[裂开]":   "你怎么裂开了。别担心，我这里有胶水，帮你粘回去！",
	}
	wechatFileSuffix = config.GetStringSliceOr("workweixin.ai.wechatFileSuffix",
		[]string{".pdf", ".jpg", ".jpeg", ".gif", ".png"})

	defaultWechatAnswers = []MessageAnswerAware[workweixin.Message]{
		&WechatMessageSetAnswer{ // emoji匹配
			MessageAwareImpl: MessageAwareImpl[workweixin.Message]{
				CustomKeywordsFunc: func(question *aipb.ChatMessage) bool {
					if !wechatEmojiReg.MatchString(question.Text) {
						return false
					}
					emoji := wechatEmojiFindReg.FindString(question.Text)
					if len(emoji) == 0 {
						return false
					}
					_, ok := defaultWechatEmoji[emoji]
					return ok
				},
			},
			BeforeSend: func(answer *WechatMessageSetAnswer, message workweixin.Message) {
				emoji := wechatEmojiFindReg.FindString(message.Text.Content)
				if len(emoji) == 0 {
					return
				}
				answer.Content = defaultWechatEmoji[emoji]
			},
			AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
		},
		&WechatMessageSetAnswer{
			MessageAwareImpl: MessageAwareImpl[workweixin.Message]{
				CustomKeywordsFunc: func(question *aipb.ChatMessage) bool {
					for _, suffix := range wechatFileSuffix {
						if strings.HasSuffix(question.Text, suffix) {
							return true
						}
					}
					return false
				},
			},
			Content: config.GetStringOr("workweixin.ai.wechatFileSuffixAnswer",
				"由于微信限制，我只能读到2MB以下的图片和语音和20MB以下的文件哦"),
			AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
		},
	}
}

// Send 发送消息
func (a *WechatMessageSetAnswer) Send(msg workweixin.Message, args ...string) (workweixin.SendResp, error) {
	var opts []workweixin.Option
	var menuId, mediaId, corpId, fileType string

	if len(args) > 0 {
		corpId = args[0]
	}
	if len(args) == 2 {
		menuId = args[1]
	}
	if len(args) > 2 {
		mediaId = args[1]
		fileType = args[2]
	}

	if a.BeforeSend != nil {
		a.BeforeSend(a, msg)
	}

	if len(a.Text) > 0 {
		for _, v := range a.Text {
			opts = append(opts, workweixin.AddText(v))
		}
	}

	if len(a.HeadMessage) > 0 || len(a.TailMessage) > 0 {
		opts = append(opts, workweixin.AddMenuContent(a.HeadMessage, a.TailMessage))
	}

	for _, v := range a.Click {
		if v.Id == "" {
			opts = append(opts, workweixin.AddClink(menuId, v.Content))
		} else {
			opts = append(opts, workweixin.AddClink(v.Id, v.Content))
		}
	}

	if len(fileType) > 0 && len(a.CdnUrl) > 0 {
		return workweixin.BuildNewClient(workweixin.Client, corpId, LoadOpenWorkAccessToken).
			SendMediaMessage(msg.ExternalUserID, msg.OpenKFID, mediaId, fileType)
	}
	return workweixin.BuildNewClient(workweixin.Client, corpId,
		LoadOpenWorkAccessToken).SendMessageV2(msg.ExternalUserID, msg.OpenKFID, a.Content, "", opts...)
}

func (a *WechatMessageSetAnswer) GetAnswerString() string {
	if len(a.Content) > 0 {
		return a.Content
	}
	return a.HeadMessage
}

// GetAnswerLevel 获取答案等级
func (a *WechatMessageSetAnswer) GetAnswerLevel() int {
	return a.Level
}

func (a *WechatMessageSetAnswer) GetAnswerType(askType ...aipb.QuestionAskType) aipb.QuestionAskType {
	if a.AskType != aipb.QuestionAskType_QUESTION_ASK_TYPE_UNSPECIFIED {
		return a.AskType
	}
	if len(askType) > 0 {
		return askType[0]
	}
	return aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET
}

// GetWechatRecordId 获取微信记录的菜单id
func (a *WechatMessageSetAnswer) GetWechatRecordId(ctx context.Context, message workweixin.Message) uint64 {
	if message.Text.MenuID == "" {
		return 0
	}

	if strings.HasPrefix(message.Text.MenuID, RecordPrefix) {
		recordId, err := strconv.ParseUint(strings.TrimPrefix(message.Text.MenuID, RecordPrefix), 10, 64)
		if err != nil {
			log.WithContext(ctx).Errorw("messagePreprocessing ParseUint error", "err", err)
		}
		return recordId
	}
	return 0
}

// GetWechatAnswer 获取问答
func GetWechatAnswer(question *aipb.ChatMessage,
	setAnswer []MessageAnswerAware[workweixin.Message]) MessageAnswerAware[workweixin.Message] {
	for _, answer := range setAnswer { // 先处理预设的问答
		if answer.handled(question) {
			return answer
		}
	}

	for _, answer := range defaultWechatAnswers { // 处理默认问答
		if answer.handled(question) {
			return answer
		}
	}

	return nil
}

// GetWechatEmptyAnswer 获取空回答
func GetWechatEmptyAnswer() *WechatMessageSetAnswer {
	return &WechatMessageSetAnswer{AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL}
}

// GetWechatContentAnswer 获取默认字符的回复
func GetWechatContentAnswer(content string) *WechatMessageSetAnswer {
	return &WechatMessageSetAnswer{Content: content, AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET}
}

// SetWechatDefaultAnswer 设置默认回答
func SetWechatDefaultAnswer(w *WechatMessageSetAnswer, menuId, content, think string, assistant *aipb.AssistantDetail) {
	content = strings.TrimLeft(content, "\n") // 替换了句首的换行符
	if assistant.ShowThink {
		content = config.GetStringOr("workweixin.ai.thinkAnswer", "【回答】\n") + content
		if len(think) > 0 {
			w.Think = config.GetStringOr("workweixin.ai.thinkAbout", "【思考过程】\n") + strings.TrimLeft(think, "\n")
		}
	}

	if assistant.RatingScaleMsg != nil && assistant.RatingScaleMsg.Enable {
		SetWechatDefaultSearchAnswer(w, menuId, content)
		return
	}
	w.Content = content
}

// SetWechatDefaultSearchAnswer 默认的搜索问答
func SetWechatDefaultSearchAnswer(w *WechatMessageSetAnswer, Id, content string) {
	w.HeadMessage = content
	w.Content = ""
	w.Text = []string{"您对以上回答是否满意？\\n回复："}
	w.Click = []workweixin.Clink{
		{Id: Id, Content: RatingScaleSatisfiedZH},
		{Id: Id, Content: RatingScaleAverageZH},
		{Id: Id, Content: RatingScaleDissatisfiedZH},
	}
}

type SplitSend struct {
	SetAnswer *WechatMessageSetAnswer // 预设答案结构
	CorpId    string
	ChatId    uint64
	Msg       workweixin.Message // 原消息
	DoResult  *DoResult          // 执行结果
}

// Do 将信息拆分后发送，可能会追加继续回答
func (s SplitSend) Do(ctx context.Context, menuId string, moreToCome bool) ([]*aipb.AiSendResultRecord, error) {
	var menuOpts, continueAnswerOpts []workweixin.Option
	if s.SetAnswer.BeforeSend != nil {
		s.SetAnswer.BeforeSend(s.SetAnswer, s.Msg)
	}

	var totalPieces, thinkPieces []string // 答案的全部分片，思考过程的分片
	var recordType aipb.AiRecordType
	var pieces []*aipb.ReqCreateSendRecord_Piece
	messageType := aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL

	if s.DoResult.Answer.ShowType == 2 { // 2 表示数据为重复回答
		messageType = aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_REPEAT_ANSWER
	} else if s.SetAnswer.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET {
		messageType = aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER
	}

	if len(s.SetAnswer.Think) > 0 { // 如果有思考过程 则添加到切片中
		thinkPieces = workweixin.SplitStringWithATag(s.SetAnswer.Think, 1024, 20)
	}

	if len(s.SetAnswer.Content) > 0 { // 拆分全文本的答案
		totalPieces = workweixin.SplitStringWithATag(s.SetAnswer.Content, 1024, 20)
		recordType = aipb.AiRecordType_AI_RECORD_TYPE_AI_TEXT
	} else { // 拆分菜单答案
		totalPieces = workweixin.SplitStringWithATag(s.SetAnswer.HeadMessage, 1024, 20)
		recordType = aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU
	}
	if len(totalPieces) == 0 {
		log.WithContext(ctx).Errorw("workWeixin SplitSend faild",
			"msg", s.Msg, "content", s.SetAnswer.Content, "headMsg", s.SetAnswer.HeadMessage)
		return nil, xerrors.NewCode(errors.BaseError_UnprocessableEntity)
	}

	wechatClient := workweixin.BuildNewClient(workweixin.Client, s.CorpId, LoadOpenWorkAccessToken)
	pieces = doSplit(thinkPieces, messageType, aipb.AiRecordType_AI_RECORD_TYPE_AI_TEXT) // 追加思考中的数据
	pieces = append(pieces, doSplit(totalPieces, messageType, recordType)...)            // 追加回复
	pieces = append(pieces, doSplitMedia(ctx, wechatClient,
		s.DoResult.GetMediaUrl(s.SetAnswer.CdnUrl), messageType, s.SetAnswer.CdnUrl)...) // 追加要发送的文件media信息

	// 消息发送记录
	rpcRsp, err := client.AiByID(s.ChatId).CreateSendRecord(ctx, &aipb.ReqCreateSendRecord{
		ChatId:     s.ChatId,
		MessageId:  s.DoResult.AnswerId,
		Pieces:     pieces,
		ChatType:   aipb.ChatType_CHAT_TYPE_WECHAT,
		MoreToCome: moreToCome,
	})
	if err != nil {
		return nil, err
	}
	if len(rpcRsp.Records) == 0 { // 没有可以发送的消息，直接返回
		return nil, nil
	}

	if recordType == aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU {
		for _, v := range s.SetAnswer.Click { // 添加满意度的菜单点击项
			menuOpts = append(menuOpts, workweixin.AddClink(menuId, v.Content))
		}
		for _, v := range s.SetAnswer.Text {
			menuOpts = append(menuOpts, workweixin.AddText(v))
		}
	}
	if rpcRsp.ContinueAnswering { // 追加继续问答，并转为菜单信息
		continueAnswerOpts = append(continueAnswerOpts, workweixin.AddClink(
			fmt.Sprintf(RecordPrefixFmt, rpcRsp.Records[len(rpcRsp.Records)-1].RecordId), ContinueAnswerStr))
	}

	var rsp []*aipb.AiSendResultRecord
	for i, v := range rpcRsp.Records { // 如果存在了错误的数据，则接受到的recordId和piece就对不上了，此处不能使用
		/**
		**发送菜单信息
			1-只发送继续回答菜单
				1.1-在发送第五条数据为媒体文件时需要追加，则将当前消息直接转为菜单消息发送
				1.2-发送文本消息时，发现需要追加继续回答，则将当前消息转为菜单消息，只追加继续回答菜单
			2-只发送满意度菜单
			3-满意度菜单和继续回答菜单一起发送
				在发送菜单消息时发现需要追加继续回答则直接把继续回答当作一个menu追加即可
		**转菜单消息
			1-媒体消息转为菜单消息  转为仅有一个按钮（继续回答）的菜单消息
			2-文本消息转为菜单消息  转为携带部分文本信息，且仅有一个按钮（继续回答）的菜单消息
		 -- 如果原本就是菜单消息，要追加继续回答，则只需增加一个按钮（继续回答）
		*/
		var sendRsp workweixin.SendResp
		switch v.Type {
		case aipb.AiRecordType_AI_RECORD_TYPE_IMAGE, aipb.AiRecordType_AI_RECORD_TYPE_VOICE,
			aipb.AiRecordType_AI_RECORD_TYPE_VIDEO, aipb.AiRecordType_AI_RECORD_TYPE_FILE: // 发送媒体文件
			sendRsp, err = wechatClient.SendMediaMessage(s.Msg.ExternalUserID, s.Msg.OpenKFID,
				v.ExtraId, getMediaTypeFromRecord(v.Type))
		case aipb.AiRecordType_AI_RECORD_TYPE_AI_TEXT: // 发送纯文本信息
			if rpcRsp.ContinueAnswering && i == len(rpcRsp.Records)-1 { // 如果纯文本需要需要追加继续推送，则需要转为菜单消息;
				continueAnswerOpts = append(continueAnswerOpts,
					workweixin.AddMenuContent(v.Content, s.SetAnswer.TailMessage))
				sendRsp, err = wechatClient.SendTextMessageV2(s.Msg.ExternalUserID, s.Msg.OpenKFID, "", continueAnswerOpts...)
				break
			}
			sendRsp, err = wechatClient.SendTextMessageV2(s.Msg.ExternalUserID, s.Msg.OpenKFID, v.Content)
		case aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU:
			if v.Content == "" && rpcRsp.ContinueAnswering { // 如果内容为空，则没有满意度，只发送继续回答的菜单
				sendRsp, err = wechatClient.SendTextMessageV2(s.Msg.ExternalUserID, s.Msg.OpenKFID, "", continueAnswerOpts...)
				break
			}
			// 如果是菜单信息，则将能发送的最后一片信息填入headContent
			menuOpts = append(menuOpts, workweixin.AddMenuContent(v.Content, s.SetAnswer.TailMessage))
			if rpcRsp.ContinueAnswering && i == len(rpcRsp.Records)-1 { // 3-满意度菜单和继续回答菜单一起发送
				menuOpts = append(menuOpts, continueAnswerOpts...)
			}
			sendRsp, err = wechatClient.SendTextMessageV2(s.Msg.ExternalUserID, s.Msg.OpenKFID, "", menuOpts...)
		}
		if err != nil {
			log.WithContext(ctx).Errorw("SplitSend Wechat ERROR", "error", err)
			rsp = append(rsp, TransformSendResult(v.RecordId, workweixin.SendResp{ErrCode: -1, ErrMsg: err.Error()}))
		} else {
			WechatSendTimeWait()
			rsp = append(rsp, TransformSendResult(v.RecordId, sendRsp))
		}
	}

	return rsp, nil
}

// TransformSendResult ...
func TransformSendResult(recordId uint64, sendRsp workweixin.SendResp) *aipb.AiSendResultRecord {
	result := &aipb.AiSendResultRecord{
		Id:    recordId,
		Uuid:  sendRsp.MsgID,
		State: 2, // 默认为2 发送成功
		//Type:  aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU,
	}
	if sendRsp.ErrCode == 0 {
		return result
	}

	temp, _ := json.Marshal(sendRsp)
	if sendRsp.ErrCode == workweixin.MsgEventSendCountLimitErrorCode {
		result.Info = string(temp)
	} else {
		result.State = 3 // 3 发送异常
		result.Info = string(temp)
	}
	return result
}

// TransformEventSendResult ...
func TransformEventSendResult(sendRsp *workweixin.MsgOnEventResponse) *aipb.AiChatSendRecord {
	temp, _ := json.Marshal(sendRsp)
	result := &aipb.AiChatSendRecord{
		Uuid:        sendRsp.Msgid,
		State:       2, // 默认为2 发送成功
		MessageType: aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL,
	}
	if sendRsp.ErrCode != 0 {
		result.State = 3 // 3 发送异常
		result.Info = string(temp)
	}
	return result
}

// RemainTurnManualWechat 微信提醒转人工
func RemainTurnManualWechat(ctx context.Context, chat *aipb.ChatWeChat, msg workweixin.Message,
	result SendResultRecord[workweixin.Message], alertMsg, corpId string) {
	sender := &WechatMessageSetAnswer{
		HeadMessage: alertMsg,
		Click:       []workweixin.Clink{{Content: CustomerServiceAnswerStr}},
	}
	if len(sender.HeadMessage) == 0 {
		sender.HeadMessage = "您可以对我说“人工”，来请人工客服回答哦"
	}

	rpcRsp, err := client.AiNational.UpdateChatMessageRecordWechat(result.Ctx, &aipb.ReqUpdateChatMessageRecordWechat{
		ChatId:              chat.Id,
		QuestionId:          result.DoResult.QuestionId,
		AnswerText:          sender.HeadMessage,
		DefaultAnswerRecord: result.ChatRecord,
		ExternalUserId:      chat.ExternalUserId,
		AssistantId:         chat.AssistantId,
		SendRecordType:      aipb.AiRecordType_AI_RECORD_TYPE_LIVE_AGENT_MENU,
	})
	if err != nil {
		log.WithContext(result.Ctx).Errorw("UpdateChatMessageRecordWechat afterSend error", "result", result, "err", err)
		return
	}

	var sendRsp workweixin.SendResp
	var infos []*aipb.ReqUpdateChatMessageStateWechat_Info
	if rpcRsp.RecordId > 0 {
		WechatSendTimeWait()
		if rpcRsp.ContinueAnswering {
			sender.Click = append(sender.Click,
				workweixin.Clink{Id: fmt.Sprintf(RecordPrefixFmt, rpcRsp.RecordId), Content: ContinueAnswerStr})
		}

		sendRsp, err = sender.Send(msg, corpId, msg.MsgID)
		if err != nil {
			log.WithContext(result.Ctx).Errorw("SendMessage afterSend fail", "err", err)
			return
		}
		infos = []*aipb.ReqUpdateChatMessageStateWechat_Info{
			{
				AnswerId: rpcRsp.AnswerId,
				Records:  []*aipb.AiSendResultRecord{TransformSendResult(rpcRsp.RecordId, sendRsp)},
			},
		}
	} else {
		infos = []*aipb.ReqUpdateChatMessageStateWechat_Info{{AnswerId: rpcRsp.AnswerId}}
	}

	_, err = client.AiNational.UpdateChatMessageStateWechat(result.Ctx, &aipb.ReqUpdateChatMessageStateWechat{
		Infos:       infos,
		NormalReply: sendRsp.ErrCode == 0,
	})
	if err != nil {
		log.WithContext(result.Ctx).Errorw("SendMessage afterSend UpdateChatMessageStateWechat fail", "err", err)

	}
}

// RemainSuggestQuestionWechat 微信提醒用户建议问题
func RemainSuggestQuestionWechat(ctx context.Context, msg workweixin.Message,
	result SendResultRecord[workweixin.Message], assistant *aipb.AssistantDetail) {
	if len(result.DoResult.Records) == 0 {
		return
	}
	maybeWantAsk := config.GetStringOr("workweixin.ai.suggestAnswerText", "你可能还想问：")
	v := result.DoResult.Records[0]
	rsp, err := client.AiByID(v.AnswerId).CreateMessageSuggestQuestion(ctx, &aipb.ReqCreateMessageSuggestQuestion{
		MessageId:       v.AnswerId,
		QuestionText:    msg.Text.Content,
		AnswerText:      v.Answer.Text,
		AssistantDetail: assistant,
		ChatId:          result.DoResult.ChatId,
		RecordInfo:      maybeWantAsk,
		RecordType:      aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU,
	}, mclient.WithRequestTimeout(60*time.Second))

	if err != nil || len(rsp.SuggestQuestions) == 0 {
		log.WithContext(ctx).Errorw("RemainSuggestQuestionWechat info", "msg", msg, "rsp", rsp, "err", err)
		return
	}

	if rsp.RecordId == 0 {
		log.WithContext(ctx).Debug("RemainSuggestQuestionWechat Over max send size")
		return
	}

	WechatSendTimeWait()
	sender := &WechatMessageSetAnswer{HeadMessage: maybeWantAsk}
	menuId := msg.MsgID // 因为菜单 默认需要给一个menuId,所以此处把问题的msgId丢进去，没有实际的意义，只是不报错而已
	for _, question := range rsp.SuggestQuestions {
		// 微信的click 的content最大长度128字节
		sender.Click = append(sender.Click, workweixin.Clink{Content: TrimClickByByteLimit(question), Id: menuId})
	}
	if rsp.ContinueAnswering {
		sender.Click = append(sender.Click, workweixin.Clink{Content: ContinueAnswerStr, Id: menuId})
		menuId = fmt.Sprintf(RecordPrefixFmt, rsp.RecordId)
	}

	sendRsp, err := sender.Send(msg, assistant.CorpId, menuId)
	if err != nil {
		log.WithContext(ctx).Errorw("SendMessage afterSend remainSuggest fail", "sendRsp", sendRsp, "err", err)
		return
	}
	_, err = client.AiByID(v.AnswerId).UpdateSendRecord(ctx, &aipb.ReqUpdateSendRecord{
		Records: []*aipb.AiSendResultRecord{TransformSendResult(rsp.RecordId, sendRsp)},
	})
	if err != nil {
		log.WithContext(ctx).Errorw("afterSend UpdateSendRecord", "sendRsp", sendRsp, "err", err)
	}
}

// GetRatingScale ...
func GetRatingScale(ratingScale, setAnswer string, scaleMsg *aipb.AssistantRatingScaleMsg) (result string) {
	if scaleMsg == nil || !scaleMsg.Enable {
		return setAnswer
	}
	switch ratingScale {
	case RatingScaleSatisfiedEN, RatingScaleSatisfiedZH:
		result = scaleMsg.Satisfied
	case RatingScaleAverageEN, RatingScaleAverageZH:
		result = scaleMsg.Average
	case RatingScaleDissatisfiedEN, RatingScaleDissatisfiedZH:
		result = scaleMsg.Dissatisfied
	}
	if len(result) == 0 {
		result = setAnswer
	}
	return
}

// WechatSendTimeWait  因为发送微信的时候可能会导致后发的消息先推送给用户，所以此处短暂延迟
func WechatSendTimeWait() {
	interval := config.GetInt64Or("workWeixin.splitMessageSendTimeInterval", 700)
	time.Sleep(time.Duration(interval) * time.Millisecond) // 如果直接发送，微信可能会顺序错乱
}

// TranSliceToWechatClick ...
func TranSliceToWechatClick(ss []string, id string) []workweixin.Clink {
	var click []workweixin.Clink
	for _, v := range ss {
		click = append(click, workweixin.Clink{
			Id:      id,
			Content: TrimClickByByteLimit(v),
		})
	}
	return click
}

// TrimClickByByteLimit 返回字节数不大于128 的字符串
func TrimClickByByteLimit(str string) string {
	// 检查字节数是否大于 128
	if len(str) > WechatClickMaxSize {
		// 获取前 30 个字符
		runes := []rune(str) // 将字符串转换为字符切片
		if len(runes) > 30 {
			return string(runes[:30]) // 返回前 30 个字符
		}
		return str // 如果字符数量小于或等于 30，则直接返回原字符串
	}
	return str // 如果字节数 <= 128，则返回原字符串
}

// interactiveCodeWechat 暗号配置
var interactiveCodeWechat = "%d.  %s\n"

// WarpWechatCodeToString 将暗号转变为string
func WarpWechatCodeToString(assistant *aipb.AssistantDetail, welcome bool) string {
	if assistant == nil || assistant.WelcomeMsg == nil || assistant.WelcomeMsg.CodeConfig == nil {
		return ""
	}
	if !assistant.WelcomeMsg.CodeConfig.SendInteractiveCode && !welcome { // 不开启发送暗号,且不是欢迎语
		return ""
	}
	if len(assistant.WelcomeMsg.CodeConfig.Codes) == 0 {
		return ""
	}
	index := 1
	result := "互动暗号：\n"
	if assistant.WelcomeMsg.CodeConfig.Codes[0].Lang == "en" {
		result = "Interactive Keywords:\n"
	}
	for _, v := range assistant.WelcomeMsg.CodeConfig.Codes {
		if welcome && !v.ShowInWelcome { // 不在欢迎语中显示的 跳过
			continue
		}
		switch v.InteractiveCode {
		case aipb.InteractiveCode_INTERACTIVE_CODE_MANUAL, aipb.InteractiveCode_INTERACTIVE_CODE_ANSWER_AGAIN,
			aipb.InteractiveCode_INTERACTIVE_CODE_CLEAR_CONTEXT, aipb.InteractiveCode_INTERACTIVE_CODE_CONTRIBUTE_KNOWLEDGE:
			result += fmt.Sprintf(interactiveCodeWechat, index, v.Content)
		default:
			//case aipb.InteractiveCode_INTERACTIVE_CODE_READ_BOM:
			//case aipb.InteractiveCode_INTERACTIVE_CODE_READ_TEST_REPORT:
			continue
		}
		index++
	}
	if assistant.MultimodalPrompt != nil && assistant.MultimodalPrompt.ComplianceMark != "" &&
		assistant.MultimodalPrompt.ReadTestReport != "" && assistant.MultimodalPrompt.AskMark != "" {
		result += fmt.Sprintf(interactiveCodeWechat, index, config.GetStringOr(
			"workweixin.ai.readIngredientList", "回复“读配料表”，解读配料表信息"))
		index++
	}
	if assistant.MultimodalPrompt != nil && assistant.MultimodalPrompt.ComplianceMark != "" &&
		assistant.MultimodalPrompt.ReadTestReport != "" && assistant.MultimodalPrompt.AskMark != "" {
		result += fmt.Sprintf(interactiveCodeWechat, index, config.GetStringOr(
			"workweixin.ai.readTestReport", "回复“读检测报告”，解读检测报告"))
	}
	return result
}

// FinishWeixinChatByClear 结束微信会话
func FinishWeixinChatByClear(ctx context.Context, chat *aipb.ChatWeChat, _ *workweixin.Message) {
	FinishWeixinChat(ctx, chat.Id, chat.ExternalUserId, chat.AssistantId, aipb.ChatType_CHAT_TYPE_WECHAT)
}

func getRecordTypeFromMedia(resp *workweixin.UploadMediaResp) aipb.AiRecordType {
	if resp == nil {
		return aipb.AiRecordType_AI_RECORD_TYPE_UNSPECIFIED
	}
	switch resp.Type {
	case workweixin.MediaTypeImage:
		return aipb.AiRecordType_AI_RECORD_TYPE_IMAGE
	case workweixin.MediaTypeVoice:
		return aipb.AiRecordType_AI_RECORD_TYPE_VOICE
	case workweixin.MediaTypeVideo:
		return aipb.AiRecordType_AI_RECORD_TYPE_VIDEO
	case workweixin.MediaTypeFile:
		return aipb.AiRecordType_AI_RECORD_TYPE_FILE
	default:
		return aipb.AiRecordType_AI_RECORD_TYPE_UNSPECIFIED
	}
}

func getMediaTypeFromRecord(recordType aipb.AiRecordType) string {
	switch recordType {
	case aipb.AiRecordType_AI_RECORD_TYPE_IMAGE:
		return workweixin.MediaTypeImage
	case aipb.AiRecordType_AI_RECORD_TYPE_VOICE:
		return workweixin.MediaTypeVoice
	case aipb.AiRecordType_AI_RECORD_TYPE_VIDEO:
		return workweixin.MediaTypeVideo
	default:
		return workweixin.MediaTypeFile
	}
}

func doSplitMedia(ctx context.Context, service *workweixin.MessageService, totalPieces []string,
	messageType aipb.AiRecordMessageType, cosPrefix string) []*aipb.ReqCreateSendRecord_Piece {
	if len(totalPieces) == 0 {
		return nil
	}

	var pieces []*aipb.ReqCreateSendRecord_Piece

	for _, url := range totalPieces { // 对于媒体文件要先获取媒体ID
		mediaRsp, err := UploadWechatMediaMessage(service, url, cosPrefix)
		v := &aipb.ReqCreateSendRecord_Piece{
			Type:        getRecordTypeFromMedia(mediaRsp),
			MessageType: messageType,
			Url:         url,
		}
		if err != nil || mediaRsp == nil {
			log.WithContext(ctx).Errorw("UploadPublicCosMedia Error", "err", err, "mediaRsp", mediaRsp)
			v.ExtraInfo = err.Error()
			v.MessageType = aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_EXCEPTION
			pieces = append(pieces, v)
			continue
		}
		info, _ := json.Marshal(mediaRsp)
		v.ExtraId = mediaRsp.MediaId

		if mediaRsp.Errcode > 0 {
			v.ExtraInfo = string(info)
			v.MessageType = aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_EXCEPTION
		}
		pieces = append(pieces, v)
	}
	return pieces
}

func doSplit(totalPieces []string,
	messageType aipb.AiRecordMessageType, recordType aipb.AiRecordType) []*aipb.ReqCreateSendRecord_Piece {
	if len(totalPieces) == 0 {
		return nil
	}

	var pieces []*aipb.ReqCreateSendRecord_Piece
	for i := 1; i < len(totalPieces); i += 2 { // 封装分片信息，如果分片数为单数，则需要单独处理
		v := &aipb.ReqCreateSendRecord_Piece{
			One:         totalPieces[i-1],
			Two:         totalPieces[i],
			Type:        aipb.AiRecordType_AI_RECORD_TYPE_AI_TEXT,
			MessageType: messageType,
		}
		pieces = append(pieces, v)
		// 分片结果是双数的情况下，最后一片如果是发送的菜单消息的话，由于1024的限制 要重新追加一片，存储菜单
		if i != len(totalPieces)-1 {
			continue
		}
		if recordType == aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU {
			v.Two = ""
			pieces = append(pieces, &aipb.ReqCreateSendRecord_Piece{
				One:         totalPieces[i],
				Type:        recordType,
				MessageType: messageType,
			})
		}
	}
	if len(totalPieces)%2 == 1 { // 分片结果是单数的情况下，把最后一片处理了
		pieces = append(pieces, &aipb.ReqCreateSendRecord_Piece{
			One:         totalPieces[len(totalPieces)-1],
			Type:        recordType,
			MessageType: messageType,
		})
	}

	return pieces
}

// UploadWechatMediaMessage ...
func UploadWechatMediaMessage(service *workweixin.MessageService,
	cosUrl, cosPrefix string) (*workweixin.UploadMediaResp, error) {
	if len(cosPrefix) > 0 {
		cosUrl = strings.TrimPrefix(cosUrl, cosPrefix)
	}
	return service.UploadPublicCosMedia(cosUrl, "")
}
