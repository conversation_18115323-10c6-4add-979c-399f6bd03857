package aware

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/whatsapp"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
)

const (
	ReplaySeparator           = "::"
	RatingScaleSatisfiedEN    = "Good job！"
	RatingScaleAverageEN      = "Just OK."
	RatingScaleDissatisfiedEN = "Wrong answer."
)

// WhatsappMessageSetAnswer ...
type WhatsappMessageSetAnswer struct {
	MessageAwareImpl[whatsapp.WhatsappInboundMessage]
	Buttons []whatsapp.Button
	Content string
	AskType aipb.QuestionAskType
}

// defaultWhatsappAnswers 预设的问答回复
var defaultWhatsappAnswers []MessageAnswerAware[whatsapp.WhatsappInboundMessage]

func init() {
	defaultWhatsappAnswers = []MessageAnswerAware[whatsapp.WhatsappInboundMessage]{
		&WhatsappMessageSetAnswer{ // 触发 暗号 精确关键字
			MessageAwareImpl: MessageAwareImpl[whatsapp.WhatsappInboundMessage]{
				ExactKeywords: config.GetStringSliceOr("whatsapp.ai.cmdKeywords", []string{"指令", "暗号", "互动暗号"})},
			Content: config.GetStringOr("whatsapp.ai.welcomeTailTemplate",
				"互动暗号：\n1. 回复“人工”，转人工客服\n2. 回复“重新回答”，AI重新回答\n3. 回复“清空上下文”，开始新对话"),
			AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET,
		},
	}
}

// Send 发送信息
func (w WhatsappMessageSetAnswer) Send(msg whatsapp.WhatsappInboundMessage, _ ...string) (string, error) {
	var rsp whatsapp.NormalResponse
	var err error

	if len(w.Buttons) > 0 {
		rsp, err = whatsapp.BusinessClient.SendTextMessageByButton(msg.From, w.Content, w.Buttons)
	} else {
		rsp, err = whatsapp.BusinessClient.SendTextMessage(msg.From, w.Content)
	}

	if err != nil {
		return "", err
	}

	return rsp.ID, nil
}

// GetAnswerString 获取答案文本
func (w WhatsappMessageSetAnswer) GetAnswerString() string {
	return w.Content
}

// GetAnswerType 获取答案类型
func (w WhatsappMessageSetAnswer) GetAnswerType(_ ...aipb.QuestionAskType) aipb.QuestionAskType {
	return w.AskType
}

//// GetWhatsappMessageSetAnswer 获取空回答
//func GetWhatsappMessageSetAnswer() ai.MessageAnswerAware[any] {
//	return WhatsappMessageSetAnswer{
//		AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_NORMAL,
//	}
//}

// GetAnswerLevel 获取答案等级
func (w WhatsappMessageSetAnswer) GetAnswerLevel() int {
	return 0
}

// GetWhatsappAnswer 获取问答
func GetWhatsappAnswer(question *aipb.ChatMessage,
	setAnswer []MessageAnswerAware[whatsapp.WhatsappInboundMessage]) MessageAnswerAware[whatsapp.WhatsappInboundMessage] {
	for _, answer := range setAnswer { // 先处理预设的问答
		if answer.handled(question) {
			return answer
		}
	}

	for _, answer := range defaultWhatsappAnswers { // 处理默认问答
		if answer.handled(question) {
			return answer
		}
	}

	return nil
}

// SetWhatsappDefaultAnswer ...
func SetWhatsappDefaultAnswer(menuId string, w *WhatsappMessageSetAnswer, assistant *aipb.AssistantDetail) {
	if assistant.RatingScaleMsg != nil && assistant.RatingScaleMsg.Enable {
		SetWhatsappDefaultSearchAnswer(menuId, w)
	}
}

// SetWhatsappDefaultSearchAnswer 默认的搜索问答
func SetWhatsappDefaultSearchAnswer(menuId string, w *WhatsappMessageSetAnswer) {

	w.Buttons = []whatsapp.Button{
		{
			Type: "reply",
			Reply: whatsapp.ButtonReply{
				ID:    menuId + ReplaySeparator + RatingScaleSatisfiedEN,
				Title: RatingScaleSatisfiedEN,
			},
		},
		{
			Type: "reply",
			Reply: whatsapp.ButtonReply{
				ID:    menuId + ReplaySeparator + RatingScaleAverageEN,
				Title: RatingScaleAverageEN,
			},
		},
		{
			Type: "reply",
			Reply: whatsapp.ButtonReply{
				ID:    menuId + ReplaySeparator + RatingScaleDissatisfiedEN,
				Title: RatingScaleDissatisfiedEN,
			},
		},
	}
}

// WhatsappAnswerSendWelcome 发送欢迎语
func WhatsappAnswerSendWelcome(message whatsapp.WhatsappInboundMessage, assistant *aipb.AssistantDetail) error {
	welcomeWhatsappMessage := &WhatsappMessageSetAnswer{
		Content: assistant.WelcomeMsg.HeadMsg,
	}
	if !assistant.Enabled {
		msg := config.GetStringOr("whatsapp.ai.offlineAssistantAnswer",
			"The assistant was offline. Please contact the administrator.")
		welcomeWhatsappMessage.Content = msg
	} else {
		if assistant.WelcomeMsg == nil {
			return xerrors.New(errorspb.BaseError_UnprocessableEntity, "WhatsappAnswerSendWelcome err:welcomeMsg is nil ")
		}
		welcomeWhatsappMessage.Content += "\n" + WarpWhatsappCodeToString(assistant, true) // 把暗号的内容和欢迎语拼接在一起

		if len(assistant.WelcomeMsg.Question) > 0 {
			welcomeWhatsappMessage.Buttons = make([]whatsapp.Button, len(assistant.WelcomeMsg.Question))
			for i, v := range assistant.WelcomeMsg.Question {
				welcomeWhatsappMessage.Buttons[i] = whatsapp.Button{
					Type: "reply",
					Reply: whatsapp.ButtonReply{
						ID:    message.ID + ReplaySeparator + v,
						Title: v,
					},
				}
			}
		}
	}

	_, err := welcomeWhatsappMessage.Send(message)
	return err
}

// interactiveCodeWhatsapp 暗号配置
var interactiveCodeWhatsapp = "%d. %s\n"

// WarpWhatsappCodeToString 将暗号转变为string
func WarpWhatsappCodeToString(assistant *aipb.AssistantDetail, welcome bool) string {
	if assistant == nil || assistant.WelcomeMsg == nil || assistant.WelcomeMsg.CodeConfig == nil {
		return ""
	}
	if !assistant.WelcomeMsg.CodeConfig.SendInteractiveCode && !welcome { // 不开启发送暗号,且不是欢迎语
		return ""
	}
	index := 1
	result := "Interactive Keywords:\n"
	for _, v := range assistant.WelcomeMsg.CodeConfig.Codes {
		if welcome && !v.ShowInWelcome { // 不在欢迎语中显示的 跳过
			continue
		}
		switch v.InteractiveCode {
		case aipb.InteractiveCode_INTERACTIVE_CODE_MANUAL, aipb.InteractiveCode_INTERACTIVE_CODE_ANSWER_AGAIN,
			aipb.InteractiveCode_INTERACTIVE_CODE_CLEAR_CONTEXT,
			aipb.InteractiveCode_INTERACTIVE_CODE_CONTRIBUTE_KNOWLEDGE:
			result += fmt.Sprintf(interactiveCodeWhatsapp, index, v.Content)
		case aipb.InteractiveCode_INTERACTIVE_CODE_READ_BOM:
			if assistant.MultimodalPrompt != nil && assistant.MultimodalPrompt.ReadBom != "" &&
				assistant.MultimodalPrompt.BomHazards != "" {
				result += fmt.Sprintf(interactiveCodeWhatsapp, index, v.Content)
			}
		case aipb.InteractiveCode_INTERACTIVE_CODE_READ_TEST_REPORT:
			if assistant.MultimodalPrompt != nil && assistant.MultimodalPrompt.ComplianceMark != "" &&
				assistant.MultimodalPrompt.ReadTestReport != "" && assistant.MultimodalPrompt.AskMark != "" {
				result += fmt.Sprintf(interactiveCodeWhatsapp, index, v.Content)
			}
		default:
			continue
		}
		index++
	}
	return result
}

// FinishWhatsappChatByClear ...
func FinishWhatsappChatByClear(ctx context.Context, chat *aipb.ChatWeChat, _ *whatsapp.WhatsappInboundMessage) {
	FinishWeixinChat(ctx, chat.Id, chat.ExternalUserId, chat.AssistantId, aipb.ChatType_CHAT_TYPE_WHATSAPP)
}

// GetWhatsappContentAnswer 获取默认字符的回复
func GetWhatsappContentAnswer(content string) *WhatsappMessageSetAnswer {
	return &WhatsappMessageSetAnswer{Content: content, AskType: aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_SET}
}
