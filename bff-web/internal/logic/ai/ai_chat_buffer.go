package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	rp "e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/pkg/aisub"
	"github.com/redis/go-redis/v9"
)

var (
	chatChannelTopicPrefix = config.GetStringOr("llm.chat_channel_topic", "tanlive:channel:ai:")
	chatCacheTopicPrefix   = config.GetStringOr("llm.chat_cache_topic", "tanlive:channel:ai:cache:")
)

// GetChatChannelTopic ai消息pub/sub topic
func GetChatChannelTopic(hashId string) string {
	return fmt.Sprintf("%s%s", chatChannelTopicPrefix, hashId)
}

// GetChatCacheSubTopic bff缓存消息pub/sub topic
func GetChatCacheSubTopic(hashId string) string {
	return fmt.Sprintf("%s%s", chatCacheTopicPrefix, hashId)
}

// GetChatChunksCacheKey bff缓存chunks存储key
func GetChatChunksCacheKey(hashId string) string {
	return fmt.Sprintf("chat:chunks:%s", hashId)
}

// MessageBatchProcessor 消息批处理器
type MessageBatchProcessor struct {
	batchSize    int           // 批处理大小
	batchTimeout time.Duration // 批处理超时时间
	messages     chan *redis.Message
	processor    func([]*redis.Message) error
	wg           sync.WaitGroup
}

// NewMessageBatchProcessor 创建批处理器
func NewMessageBatchProcessor(batchSize int, batchTimeout time.Duration, processor func([]*redis.Message) error) *MessageBatchProcessor {
	return &MessageBatchProcessor{
		batchSize:    batchSize,
		batchTimeout: batchTimeout,
		messages:     make(chan *redis.Message, batchSize*2),
		processor:    processor,
	}
}

// Start 启动批处理
func (bp *MessageBatchProcessor) Start(ctx context.Context) {
	bp.wg.Add(1)
	go func() {
		defer bp.wg.Done()
		batch := make([]*redis.Message, 0, bp.batchSize)
		timer := time.NewTimer(bp.batchTimeout)
		defer timer.Stop()

		for {
			select {
			case msg := <-bp.messages:
				batch = append(batch, msg)
				if len(batch) >= bp.batchSize {
					if err := bp.processor(batch); err != nil {
						log.WithContext(ctx).Errorw("Batch processing error", "err", err)
					}
					batch = batch[:0]
				}
			case <-timer.C: // 1. 处理最后一批不完整的消息 2. 处理长时间没有集齐batchSize的剩余数据
				if len(batch) > 0 {
					if err := bp.processor(batch); err != nil {
						log.WithContext(ctx).Errorw("Batch processing error", "err", err)
					}
					batch = batch[:0]
				}
				timer.Reset(bp.batchTimeout)
			case <-ctx.Done():
				return
			}
		}
	}()
}

// 初始化Redis客户端
func getRedisClient() rp.Manager {
	return rp.Default
}

// StoreChatChunk 存储单个
func StoreChatChunk(ctx context.Context, clientHashId string, chunk string) {
	if clientHashId == "" {
		return
	}
	var DefaultExpiration = time.Duration(config.GetIntOr("llm.store_chunks_default_expiration", 60)) * time.Minute

	cacheKey := GetChatChunksCacheKey(clientHashId)
	aiClient := getRedisClient()
	if aiClient == nil {
		return
	}

	// cache chunks
	aiClient.RPush(ctx, cacheKey, chunk)
	aiClient.Expire(ctx, cacheKey, DefaultExpiration)

	// cache chunks pub/sub
	topic := GetChatCacheSubTopic(clientHashId)
	aiClient.Publish(ctx, topic, chunk)
}

type ChunksSubscriber struct {
	clientHashId  string
	redisClient   rp.Manager
	chunksChan    *aisub.Chan[string]
	done          chan struct{}
	mu            sync.Mutex
	receiveChunks map[string]bool
	redisChan     *aisub.Chan[string]
	stopOnce      sync.Once
}

// NewChunksSubscriber redis store chunks订阅
func NewChunksSubscriber(clientHashId string) *ChunksSubscriber {
	redisClient := getRedisClient()
	return &ChunksSubscriber{
		clientHashId:  clientHashId,
		redisClient:   redisClient,
		chunksChan:    aisub.NewChan[string](), // 缓冲channel
		done:          make(chan struct{}),
		receiveChunks: make(map[string]bool),
		redisChan:     aisub.NewChan[string](),
	}
}

func DecodeRedisMsg(input string) *PublishMessage {
	// 提取JSON部分
	lines := strings.Split(input, "\n")
	var jsonStr string
	for _, line := range lines {
		if strings.HasPrefix(line, "data: ") {
			jsonStr = strings.TrimPrefix(line, "data: ")
			break
		}
	}

	// 解析JSON
	var pushMsg *PublishMessage
	err := json.Unmarshal([]byte(jsonStr), &pushMsg)
	if err != nil {
		return nil
	}
	return pushMsg
}

func CheckChatStoreExist(clientHashId string) bool {
	if clientHashId == "" {
		return false
	}

	ctx := context.Background()
	cacheKey := GetChatChunksCacheKey(clientHashId)

	aiClient := getRedisClient()
	if aiClient == nil {
		return false
	}

	exists, err := aiClient.Exists(ctx, cacheKey).Result()
	if err != nil {
		return false
	}

	return exists > 0
}

func (cs *ChunksSubscriber) GetRedisExistChunks(ctx context.Context) []string {
	cacheKey := GetChatChunksCacheKey(cs.clientHashId)
	existingChunks, err := cs.redisClient.LRange(ctx, cacheKey, 0, -1).Result()
	if err != nil {
		return []string{}
	}
	return existingChunks
}

func (cs *ChunksSubscriber) Subscribe(ctx context.Context) {
	// 1. 先建立订阅
	if cs.redisClient == nil {
		return
	}

	// 使用独立的缓存topic
	subTopic := GetChatCacheSubTopic(cs.clientHashId)
	pubsub := cs.redisClient.Subscribe(ctx, subTopic)
	ch := pubsub.Channel(redis.WithChannelSize(500))

	// 如果8秒内没有订阅到chunk则退出订阅
	receiveTimeout := time.Duration(config.GetIntOr("llm.chat_channel_receive_timeout", 8)) * time.Second

	go func() {
		timer := time.NewTimer(receiveTimeout)
		defer func() {
			timer.Stop()
			pubsub.Close()
		}()

		for {
			select {
			case <-cs.done:
				return
			case <-timer.C:
				// 超时，自动断开订阅
				cs.Stop()
				return
			case msg, ok := <-ch:
				if !ok {
					fmt.Println("&&pubsub channel closed")
					return
				}
				if !timer.Stop() {
					<-timer.C
				}
				timer.Reset(receiveTimeout)
				cs.redisChan.Send(msg.Payload)
			}
		}
	}()

	time.Sleep(50 * time.Millisecond)

	existedChunks := cs.GetRedisExistChunks(ctx)
	for _, chunk := range existedChunks {
		cs.chunkChanSafeSend(chunk)
	}

	log.WithContext(ctx).Infow("[ChunksSubscriber] Subscribe existedChunks handle end", "hashId", cs.clientHashId, "existedChunks len", len(existedChunks))

	if cs.redisChan != nil {
		cs.redisChan.Consume(func(s string) {
			cs.chunkChanSafeSend(s)
		})
	}
}

func (cs *ChunksSubscriber) chunkChanSafeSend(chunk string) {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	if !cs.receiveChunks[chunk] {
		cs.receiveChunks[chunk] = true
		cs.chunksChan.Send(chunk)
	}
}

// Stop 停止订阅
func (cs *ChunksSubscriber) Stop() {
	cs.stopOnce.Do(func() {
		close(cs.done)
		if cs.redisChan != nil {
			cs.redisChan.Close()
		}
		if cs.chunksChan != nil {
			cs.chunksChan.Close()
		}
	})
}

// Chunks 获取chunks的channel
func (cs *ChunksSubscriber) Chunks() *aisub.Chan[string] {
	return cs.chunksChan
}
