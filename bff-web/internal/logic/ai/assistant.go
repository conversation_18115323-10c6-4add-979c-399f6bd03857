package ai

import (
	"context"
	"fmt"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcache"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	errpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	"github.com/golang/protobuf/ptypes/timestamp"
	"golang.org/x/exp/slices"
	"golang.org/x/sync/errgroup"
)

// MemCache 内存缓存
var MemCache = func() xcache.Cache {
	return xcache.Use("memory")
}

// GetManagedAssistants 获取当前用户管理的所有ai助手
func GetManagedAssistants(ctx context.Context) ([]uint64, error) {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	pbReq := &aipb.ReqListAssistant{}
	var result []uint64
	isTeam := iamlogic.IsTeamUser(ctx)

	ckey := fmt.Sprintf("managedAssistant:%d%v", user.Id, isTeam)
	err := MemCache().GetJSON(ctx, ckey, &result)
	if err == nil {
		return result, nil
	}
	if !isTeam {
		pbReq.Admins = append(pbReq.Admins, &aipb.AssistantAdmin{
			Id:   user.Id,
			Type: basepb.IdentityType_IDENTITY_TYPE_USER,
		})
	} else {
		team, err := GetUserTeamId(ctx)
		if err != nil {
			return nil, err
		}
		pbReq.Admins = append(pbReq.Admins, &aipb.AssistantAdmin{
			Id:   team,
			Type: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		})
	}
	pbRsp, err := client.AiNational.ListAssistant(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	for _, v := range pbRsp.Assistants {
		result = append(result, v.Id)
	}
	_ = MemCache().SetJSON(ctx, ckey, result, time.Second*5)
	return result, nil
}

// RewriteAssistantIds 根据用户管理的助手，重写传入的助手id
// 如果len(ids)=0 或者 ids=0，获取用户管理的所有助手
// len(ids)!=0 && ids[i] !=0，判断ids是否在用户管理的助手列表中
func RewriteAssistantIds(ctx context.Context, ids ...uint64) ([]uint64, error) {
	var temp []uint64
	for _, id := range ids {
		if id != 0 {
			temp = append(temp, id)
		}
	}
	if len(temp) == 0 {
		return GetManagedAssistants(ctx)
	}
	managed, err := GetManagedAssistants(ctx)
	if err != nil {
		return nil, err
	}
	for _, id := range temp {
		if !slices.Contains(managed, id) {
			return nil, xerrors.ForbiddenError("")
		}
	}
	return temp, nil
}

// GetUserManagedCollectionIds 获取用户管理助手对应的collection id
// assistantIds 可传入assistantIds指定助手，否则查询所有
// 返回值长度为0时，代表无管理的collection
func GetUserManagedCollectionIds(ctx context.Context, assistantIds ...uint64) ([]uint64, error) {
	aids, err := RewriteAssistantIds(ctx, assistantIds...)
	if err != nil {
		return nil, err
	}
	if len(aids) == 0 {
		return nil, nil
	}
	collections, err := client.AiNational.ListCollection(ctx, &aipb.ReqListCollection{AssistantId: aids})
	if err != nil {
		return nil, err
	}
	if len(collections.Collections) == 0 {
		return nil, nil
	}
	cids := make([]uint64, 0, len(collections.Collections))
	for _, v := range collections.Collections {
		cids = append(cids, v.Id)
	}
	return cids, nil
}

// GetUserAsOperator 获取当前
// 个人用户，存个人用户 id
// 团队用户，存当前个人用户id和团队id
func GetUserAsOperator(ctx context.Context, fetchTeam ...bool) (*aipb.Operator, error) {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	if !iamlogic.IsTeamUser(ctx) {
		return &aipb.Operator{
			Id:   user.Id,
			Type: base.IdentityType_IDENTITY_TYPE_USER,
		}, nil
	} else {
		var team uint64
		var err error
		if len(fetchTeam) > 0 && fetchTeam[0] {
			team, err = GetUserTeamId(ctx)
			if err != nil {
				return nil, err
			}
		}
		return &aipb.Operator{
			UserId: user.Id,
			Type:   base.IdentityType_IDENTITY_TYPE_TEAM,
			Id:     team,
		}, nil
	}
}

// GetUserAsContributor 获取当前用户作为贡献者
func GetUserAsContributor(ctx context.Context) (*aipb.Contributor, error) {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	if !iamlogic.IsTeamUser(ctx) {
		return &aipb.Contributor{
			Id:   user.Id,
			Type: base.IdentityType_IDENTITY_TYPE_USER,
		}, nil
	} else {
		team, err := GetUserTeamId(ctx)
		if err != nil {
			return nil, err
		}
		return &aipb.Contributor{
			Id:   team,
			Type: base.IdentityType_IDENTITY_TYPE_TEAM,
		}, nil
	}
}

// GeAiDoctContributorShowInfo 获取知识库贡献者的显示信息
// 会重写入参中contributors的值
func GeAiDoctContributorShowInfo(ctx context.Context, contributors ...*aipb.Contributor) (
	map[*aipb.Contributor]*aipb.Contributor, error,
) {
	if len(contributors) == 0 {
		return map[*aipb.Contributor]*aipb.Contributor{}, nil
	}
	ret := make(map[*aipb.Contributor]*aipb.Contributor, len(contributors))
	var userIds []uint64
	var teamIds []uint64
	for _, v := range contributors {
		switch v.GetType() {
		case base.IdentityType_IDENTITY_TYPE_TEAM:
			teamIds = append(teamIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_USER:
			userIds = append(userIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_CUSTOM:
		}
		ret[v] = v
	}
	g := &errgroup.Group{}
	if len(userIds) > 0 {
		g.Go(func() error {
			names, err := iamlogic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			for _, v := range contributors {
				name := names[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_USER && name != "" {
					v.Text = name
				}
			}
			return nil
		})
	}
	if len(teamIds) > 0 {
		g.Go(func() error {
			infos, err := iamlogic.FetchTeamInfo(ctx, teamIds)
			if err != nil {
				return err
			}
			for _, v := range contributors {
				info := infos[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_TEAM && info != nil {
					v.Text = info.ShortName
					v.Level = info.Level
					v.IsPublished = info.IsPublished
					v.FullName = info.FullName
				}
			}
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		return nil, err
	}
	return ret, nil
}

// GetUserTeamId 获取ai租户, 即获取用户对应的团队
func GetUserTeamId(ctx context.Context) (uint64, error) {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	// team, err := iamlogic.FetchUserTeam(ctx, user.Id)
	// if err != nil {
	// 	return 0, err
	// }
	return user.FirmId, nil
}

// GetAiDocOperatorsShowInfo 获取知识库操作者的显示信息
// 规则：如果是团队用户，当前用户所在的团队才返回用户名称和 id，否则只返回团队名称和id
func GetAiDocOperatorsShowInfo(ctx context.Context, operators ...*aipb.Operator) (
	map[*aipb.Operator]*bffaipb.DocOperator, error,
) {
	if len(operators) == 0 {
		return nil, nil
	}
	currentTeamId, _ := GetUserTeamId(ctx)
	ret := make(map[*aipb.Operator]*bffaipb.DocOperator, len(operators))
	mtx := sync.Mutex{}
	var userIds []uint64
	var opUserIds []uint64
	var teamUserIds []uint64
	var teamTeamIds []uint64
	var teamIds []uint64
	for _, v := range operators {
		switch v.GetType() {
		case base.IdentityType_IDENTITY_TYPE_MGMT:
			opUserIds = append(opUserIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_USER:
			userIds = append(userIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_TEAM:
			if v.GetUserId() == 0 {
				teamIds = append(teamIds, v.Id)
			} else {
				teamTeamIds = append(teamTeamIds, v.Id)
				teamUserIds = append(teamUserIds, v.UserId)
			}
		}
	}
	xslice.SetUint64Unique(&userIds, 0)
	xslice.SetUint64Unique(&opUserIds, 0)
	xslice.SetUint64Unique(&teamIds, 0)
	xslice.SetUint64Unique(&teamUserIds, 0)
	xslice.SetUint64Unique(&teamTeamIds, 0)
	g := &errgroup.Group{}
	if len(opUserIds) > 0 {
		g.Go(func() error {
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range operators {
				if v.Type == base.IdentityType_IDENTITY_TYPE_MGMT {
					ret[v] = &bffaipb.DocOperator{Type: v.Type, Id: v.Id}
				}
			}
			return nil
		})
	}
	if len(userIds) > 0 {
		g.Go(func() error {
			names, err := iamlogic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range operators {
				name := names[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_USER && name != "" {
					ret[v] = &bffaipb.DocOperator{Type: v.Type, Id: v.Id, Username: name}
				}
			}
			return nil
		})
	}

	if len(teamIds) > 0 {
		g.Go(func() error {
			infos, err := iamlogic.FetchTeamInfo(ctx, teamIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range operators {
				teamName := infos[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_TEAM && v.UserId == 0 && teamName != nil {
					ret[v] = &bffaipb.DocOperator{Type: v.Type, Id: v.Id, TeamName: teamName.ShortName}
				}
			}
			return nil
		})
	}

	if len(teamUserIds) > 0 {
		g.Go(func() error {
			userNames, err := iamlogic.FetchUserName(ctx, teamUserIds)
			if err != nil {
				return err
			}
			teamNames, err := iamlogic.FetchTeamInfo(ctx, teamTeamIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range operators {
				userName := userNames[v.UserId]
				teamName := teamNames[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_TEAM && v.UserId != 0 && userName != "" && teamName != nil {
					if v.Id == currentTeamId {
						ret[v] = &bffaipb.DocOperator{Type: v.Type, Id: v.Id, UserId: v.UserId, Username: userName, TeamName: teamName.ShortName}
					} else {
						ret[v] = &bffaipb.DocOperator{Type: v.Type, Id: v.Id, TeamName: teamName.ShortName}
					}
				}
			}
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		return nil, err
	}
	return ret, nil
}

// SpiltDocAssistantsByShared 通过判断助手是否为分享接受的 doc
func SpiltDocAssistantsByShared(contributor *aipb.Contributor, managedAssistants []uint64,
	contributors []*aipb.Contributor, allStates []*aipb.DocAssistantState, state *aipb.DocState) (
	owned, shared []*aipb.DocAssistantState, isContributor bool,
) {
	owned = make([]*aipb.DocAssistantState, 0)
	shared = make([]*aipb.DocAssistantState, 0)
	for _, v := range allStates {
		if slices.Contains(managedAssistants, v.AssistantId) {
			if v.State == aipb.DocState_DOC_STATE_UNBOUNDED {
				continue
			}
			owned = append(owned, v)
		} else {
			// 贡献出去的知识，如果删除了，显示为已禁用
			if v.State == aipb.DocState_DOC_STATE_DELETING || v.State == aipb.DocState_DOC_STATE_UNBOUNDED {
				v.State = aipb.DocState_DOC_STATE_DISABLED
			}
			if v.IsShared == 1 {
				continue
			}
			shared = append(shared, v)
		}
	}
	// 如果管理的所有助手中，状态都是删除中，设置 doc 的整体状态为删除中
	numDeleting := 0
	for _, v := range owned {
		if v.State == aipb.DocState_DOC_STATE_DELETING {
			numDeleting++
		}
	}
	if numDeleting != 0 && numDeleting == len(owned) {
		*state = aipb.DocState_DOC_STATE_DELETING
	}

	isContributor = false
	for _, v := range contributors {
		if v.Type == contributor.Type && v.Id == contributor.Id {
			isContributor = true
		}
	}
	// 不是贡献者，不显示已经分享至的助手
	if !isContributor {
		shared = nil
	}

	return owned, shared, isContributor
}

// IsDocContributor 检查当前用户是否是 doc 的贡献值
func IsDocContributor(ctx context.Context, docId uint64) (bool, error) {
	pbRsp, err := client.AiNational.ListContributor(ctx, &aipb.ReqListContributor{
		DcoId: []uint64{docId},
	})
	if err != nil {
		return false, err
	}
	user, err := GetUserAsContributor(ctx)
	if err != nil {
		return false, err
	}
	for _, v := range pbRsp.Contributors {
		if v.Type == user.Type && v.Id == user.Id {
			return true, nil
		}
	}
	return false, nil
}

// IsDocContributorBulk 批量检查当前用户是否是 doc 的贡献值
func IsDocContributorBulk(ctx context.Context, docId []uint64) ([]bool, error) {
	r := make([]bool, len(docId))
	pbRsp, err := client.AiNational.ListContributor(ctx, &aipb.ReqListContributor{
		DcoId: docId,
	})
	if err != nil {
		return nil, err
	}
	user, err := GetUserAsContributor(ctx)
	if err != nil {
		return nil, err
	}
	for i, id := range docId {
		cs := pbRsp.ContributorMap[id].GetContributors()
		for _, v := range cs {
			if v.Type == user.Type && v.Id == user.Id {
				r[i] = true
			}
		}
	}
	return r, nil
}

// SplitDocsByEditPermission doc
func SplitDocsByEditPermission(ctx context.Context, docId ...uint64) (viewable, editable []uint64, err error) {
	if len(docId) == 0 {
		return
	}
	// view := make([]uint64, len(docId))
	// edit := make([]uint64, len(docId))
	// errg := errgroup.Group{}
	// for i, v := range docId {
	// 	i := i
	// 	v := v
	// 	errg.Go(func() error {
	// 		is, err := IsDocContributor(ctx, v)
	// 		if err != nil {
	// 			return err
	// 		}
	// 		if !is {
	// 			view[i] = v
	// 		} else {
	// 			edit[i] = v
	// 		}
	// 		return nil
	// 	})
	// }
	// err = errg.Wait()
	is, err := IsDocContributorBulk(ctx, docId)
	if err != nil {
		return nil, nil, err
	}
	for i, v := range is {
		if !v {
			viewable = append(viewable, docId[i])
		} else {
			editable = append(editable, docId[i])
		}
	}
	// for _, v := range view {
	// 	if v != 0 {
	// 		viewable = append(viewable, v)
	// 	}
	// }
	// for _, v := range edit {
	// 	if v != 0 {
	// 		editable = append(editable, v)
	// 	}
	// }
	return
}

// CheckDocEditPermission 检查是否有 doc 编辑权限
// 逻辑：检查是否是 doc 的贡献者
func CheckDocEditPermission(ctx context.Context, docId ...uint64) error {
	// errg := errgroup.Group{}
	// for _, v := range docId {
	// 	v := v
	// 	errg.Go(func() error {
	// 		is, err := IsDocContributor(ctx, v)
	// 		if err != nil {
	// 			return err
	// 		}
	// 		if !is {
	// 			return xerrors.ForbiddenError("")
	// 		}
	// 		return nil
	// 	})
	// }
	// err := errg.Wait()
	// if err != nil {
	// 	return err
	// }
	is, err := IsDocContributorBulk(ctx, docId)
	if err != nil {
		return err
	}
	for _, v := range is {
		if !v {
			return xerrors.NewCode(errpb.AiError_AiNotDocContributor)
		}
	}
	return nil
}

// CheckDocChangeAssistant 检查更改 doc 所在助手的参数
func CheckDocChangeAssistant(ctx context.Context, managedAssistants []uint64, assistants []*aipb.DocAssistantState) error {
	if len(assistants) == 0 {
		return nil
	}
	for _, v := range assistants {
		if !slices.Contains(managedAssistants, v.AssistantId) {
			return xerrors.ForbiddenError("")
		}
		// 如果状态不是启用/禁用，设置为禁用
		if v.State != aipb.DocState_DOC_STATE_ENABLED && v.State != aipb.DocState_DOC_STATE_DISABLED {
			v.State = aipb.DocState_DOC_STATE_DISABLED
		}
	}
	return nil
}

// AssistantInfo ...
type AssistantInfo struct {
	infos map[uint64]*aipb.RspGetAssistantInfoMap_Info
}

// GetAssistantInfo 获取助手信息
func GetAssistantInfo(ctx context.Context, ids ...uint64) AssistantInfo {
	a := AssistantInfo{}
	rsp, err := client.AiNational.GetAssistantInfoMap(ctx, &aipb.ReqGetAssistantInfoMap{
		Ids: ids,
	})
	if err != nil || rsp == nil {
		log.WithContext(ctx).Errorw("GetAssistantMap error", "err", err, "rsp", rsp)
		return a
	}
	if len(rsp.InfoMap) == 0 {
		return a
	}
	a.infos = rsp.InfoMap

	return a
}

// GetName 获取中文名称
func (a AssistantInfo) GetName(aId uint64) string {
	if v, ok := a.infos[aId]; ok {
		return v.Name
	}
	return ""
}

// GetNameEn 获取英文名称
func (a AssistantInfo) GetNameEn(aId uint64) string {
	if v, ok := a.infos[aId]; ok {
		return v.NameEn
	}
	return ""
}

// GetChatStatus 获取助手会话状态
func (a AssistantInfo) GetChatStatus(currentState aipb.ChatCurrentState, assistantId uint64, chatType aipb.ChatType,
	updateDate *timestamp.Timestamp,
) aipb.ChatCurrentState {
	if currentState == aipb.ChatCurrentState_CHAT_CURRENT_STATE_REPLACED {
		return aipb.ChatCurrentState_CHAT_CURRENT_STATE_REPLACED
	}

	if updateDate == nil {
		return aipb.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED
	}

	if v, ok := a.infos[assistantId]; ok {
		minutes := time.Duration(v.ChatIdleDuration) * time.Minute
		if (chatType == aipb.ChatType_CHAT_TYPE_WECHAT || chatType == aipb.ChatType_CHAT_TYPE_WHATSAPP) &&
			updateDate.AsTime().Before(time.Now().Add(-minutes)) {
			return aipb.ChatCurrentState_CHAT_CURRENT_STATE_TIMEOUT // 超过助手的最大持续时间，被标记为已结束
		}
	}

	return aipb.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED
}

type tanliveAppConfig struct {
	ID            uint64
	UseRegionCode string
}

// GetTanliveAppConfig 获取碳LIVE应用配置
func GetTanliveAppConfig() ([]uint64, map[uint64]string, error) {
	var tanliveApps []tanliveAppConfig
	if err := config.Unmarshal("assistant.tanliveApp", &tanliveApps); err != nil {
		return nil, nil, fmt.Errorf("unmarshal assistant.tanliveApp failed %w", err)
	}

	ids := make([]uint64, 0, len(tanliveApps))
	codes := make(map[uint64]string, len(tanliveApps))
	for _, app := range tanliveApps {
		ids = append(ids, app.ID)
		codes[app.ID] = app.UseRegionCode
	}

	return ids, codes, nil
}

// GetTanliveMiniprogramAssistantIDs 获取碳LIVE小程序助手ID
func GetTanliveMiniprogramAssistantIDs() ([]uint64, error) {
	var ids []uint64
	if err := config.Unmarshal("assistant.tanliveMiniprogram", &ids); err != nil {
		return nil, fmt.Errorf("unmarshal assistant.tanliveMiniprogram failed %w", err)
	}
	return ids, nil
}

// UniqueListDocOperator 更新人/创建人去除
func UniqueListDocOperator(ctx context.Context, ops []*bffaipb.DocOperator) ([]*bffaipb.DocOperator, error) {
	self, err := GetUserAsOperator(ctx, true)
	if err != nil {
		return ops, err
	}

	uniqueList := make([]*bffaipb.DocOperator, 0, len(ops))
	hasMgmt := false
	for _, v := range ops {
		if v.Type == base.IdentityType_IDENTITY_TYPE_MGMT {
			if !hasMgmt {
				hasMgmt = true
				uniqueList = append(uniqueList, v)
			}
			continue
		}
		skip := false
		// 非当前团队的更新人/创建人，按照团队进行聚合
		if self.Type == base.IdentityType_IDENTITY_TYPE_TEAM && v.Type == base.IdentityType_IDENTITY_TYPE_TEAM && v.Id != self.Id {
			for _, vv := range uniqueList {
				if vv.Type == base.IdentityType_IDENTITY_TYPE_TEAM && vv.Id == v.Id {
					skip = true
				} else {
					v.UserId = 0
					v.Username = ""
				}
			}
		}
		if !skip {
			uniqueList = append(uniqueList, v)
		}
	}
	return uniqueList, nil
}

// LoadAssistantsIdentityName 加载助手的身份名称
func LoadAssistantsIdentityName(ctx context.Context, assistants []*aipb.FullAssistant) error {
	var identities []*base.Identity
	for _, assistant := range assistants {
		if assistant.Assistant.CreateBy != nil {
			identities = append(identities, assistant.Assistant.CreateBy)
		}
		if assistant.Assistant.UpdateBy != nil {
			identities = append(identities, assistant.Assistant.UpdateBy)
		}
		identities = append(identities, assistant.Assistant.Config.Admins...)
	}

	if err := logic.LoadIdentityNames(ctx, identities); err != nil {
		return err
	}
	return nil
}

// LoadAssistantAdminCard 加载助手管理员的卡片
func LoadAssistantAdminCard(ctx context.Context, assistants []*aipb.FullAssistant) ([]*iampb.UserCard, []*teampb.TeamCard, error) {
	var identities []*base.Identity
	for _, assistant := range assistants {
		identities = append(identities, assistant.Assistant.Config.Admins...)
	}
	return logic.LoadIdentityCards(ctx, identities)
}

// GetDocShareTeamUserShowInfo 加载分享的团队和用户信息
func GetDocShareTeamUserShowInfo(ctx context.Context, receivers []*aipb.DocShareReceiver) (map[*aipb.DocShareReceiver]*bffaipb.DocShareTeamReceiver, map[*aipb.DocShareReceiver]*bffaipb.DocShareUserReceiver, error) {
	teamMap := make(map[*aipb.DocShareReceiver]*bffaipb.DocShareTeamReceiver)
	userMap := make(map[*aipb.DocShareReceiver]*bffaipb.DocShareUserReceiver)

	teamIds := make([]uint64, 0, len(receivers))
	userIds := make([]uint64, 0, len(receivers))
	for _, v := range receivers {
		if v.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM {
			teamIds = append(teamIds, v.Id)
		} else {
			userIds = append(userIds, v.Id)
		}
	}
	if len(teamIds) == 0 && len(userIds) == 0 {
		return teamMap, userMap, nil
	}
	xslice.SetUint64Unique(&teamIds, 0)
	xslice.SetUint64Unique(&userIds, 0)

	g := errgroup.Group{}
	mtx := sync.Mutex{}

	if len(userIds) > 0 {
		g.Go(func() error {
			names, err := iamlogic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range receivers {
				if v.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER {
					userMap[v] = &bffaipb.DocShareUserReceiver{Id: v.Id, Name: names[v.Id]}
				}
			}
			return nil
		})
	}

	if len(teamIds) > 0 {
		g.Go(func() error {
			infos, err := iamlogic.FetchTeamInfo(ctx, teamIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range receivers {
				if v.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM {
					teamMap[v] = &bffaipb.DocShareTeamReceiver{Id: v.Id, Name: infos[v.Id].ShortName}
				}
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, nil, err
	}
	return teamMap, userMap, nil
}

// BuildDocShareReceiversConditional 根据是否为贡献者条件性构建分享接收者信息
func BuildDocShareReceiversConditional(
	isContributor bool,
	shareReceivers []*aipb.DocShareReceiver,
	sharedTeams map[*aipb.DocShareReceiver]*bffaipb.DocShareTeamReceiver,
	sharedUsers map[*aipb.DocShareReceiver]*bffaipb.DocShareUserReceiver,
) (team []*bffaipb.DocShareTeamReceiver, user []*bffaipb.DocShareUserReceiver) {
	// 只有当前租户是贡献者时才填充分享信息
	if !isContributor {
		return nil, nil
	}

	team = make([]*bffaipb.DocShareTeamReceiver, 0, len(shareReceivers))
	user = make([]*bffaipb.DocShareUserReceiver, 0, len(shareReceivers))

	for _, receiver := range shareReceivers {
		if receiver.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM {
			if teamReceiver, exists := sharedTeams[receiver]; exists {
				team = append(team, teamReceiver)
			}
		} else {
			if userReceiver, exists := sharedUsers[receiver]; exists {
				user = append(user, userReceiver)
			}
		}
	}

	return team, user
}

// CheckReceivedShare 检查当前用户/团队是否收到了指定知识的分享
// 根据分享定义：
// 1. 用户收到分享：t_doc_share 表中存在 share_type=USER 且 target_id=用户ID 的记录
// 2. 团队收到分享：t_doc_share 表中存在 share_type=TEAM 且 target_id=团队ID 的记录
// 3. 助手收到分享：t_assistant_doc 表中存在 is_shared=2 的记录
func CheckReceivedShare(ctx context.Context, isContributor bool, contributor *aipb.Contributor, managedAssistants []uint64, shareReceivers []*aipb.DocShareReceiver, sharedStates []*aipb.DocAssistantState) bool {
	// 贡献者不算收到分享
	if isContributor {
		return false
	}

	// 1. 检查用户/团队是否直接收到分享
	for _, receiver := range shareReceivers {
		if receiver.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER &&
			contributor.Type == basepb.IdentityType_IDENTITY_TYPE_USER &&
			receiver.Id == contributor.Id {
			return true
		}
		if receiver.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM &&
			contributor.Type == basepb.IdentityType_IDENTITY_TYPE_TEAM &&
			receiver.Id == contributor.Id {
			return true
		}
	}

	// 2. 检查用户管理的助手是否直接收到分享（is_shared=2）
	for _, state := range sharedStates {
		// 检查是否是当前用户管理的助手
		for _, managedID := range managedAssistants {
			if state.AssistantId == managedID && state.IsShared == 2 {
				return true
			}
		}
	}

	return false
}
