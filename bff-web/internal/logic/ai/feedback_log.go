package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// GetFeedbackLogs 查询用户反馈日志列表
type GetFeedbackLogs struct {
}

// Get ...
func (l *GetFeedbackLogs) Get(ctx context.Context,
	req *bffaipb.ReqGetFeedbackLogs, managedAssistantIDs []uint64) ([]*aipb.FullFeedbackLog, uint32, error) {
	rsp, err := client.AiByRegion(req.Region).GetFeedbackLogs(ctx, &aipb.ReqGetFeedbackLogs{
		Filter: &aipb.ReqGetFeedbackLogs_Filter{
			FeedbackId:      req.FeedbackId,
			CreateIdentity:  req.CreateIdentity,
			Action:          req.Action,
			CreateDateRange: req.CreateDateRange,
			AssistantId:     managedAssistantIDs,
		},
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Relation: &aipb.ReqGetFeedbackLogs_Relation{
			Feedback: true,
		},
		OrderBy:        l.formatOrderBy(req.OrderBy),
		WithTotalCount: true,
	})
	if err != nil {
		return nil, 0, err
	}

	return rsp.Logs, rsp.TotalCount, nil
}

// LoadIdentities ...
func (l *GetFeedbackLogs) LoadIdentities(ctx context.Context, logs []*aipb.FullFeedbackLog) ([]*iampb.UserCard, []*teampb.TeamCard, error) {
	var identities []*basepb.Identity
	for _, log := range logs {
		identities = append(identities, log.Log.CreateIdentity)
	}

	return logic.LoadIdentityCards(ctx, identities)
}

func (l *GetFeedbackLogs) formatOrderBy(orderBy []*basepb.OrderBy) []*basepb.OrderBy {
	formatted := make([]*basepb.OrderBy, 0, len(orderBy)+1)
	for _, o := range orderBy {
		if o.Column == "id" {
			continue
		}
		formatted = append(formatted, o)
	}
	formatted = append(formatted, &basepb.OrderBy{
		Column: "id",
		Desc:   true,
	})
	return formatted
}
