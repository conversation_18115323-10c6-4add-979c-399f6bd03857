package ai

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"image/draw"
	"image/jpeg"
	"image/png"
	"log"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strconv"
	"strings"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	reviewpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	"github.com/google/uuid"
	"github.com/nfnt/resize"
	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// TransferMediaToCosRes 转换媒体文件为cos url
func TransferMediaToCosRes(ms *workweixin.MessageService, mediaId string) (string, error) {
	tempDir := "temp"

	err := os.MkdirAll(tempDir, os.ModePerm)
	if err != nil {
		return "", fmt.Errorf("无法创建临时目录: %v", err)
	}

	savePath, err := ms.DownloadMedia(mediaId, tempDir)
	if err != nil {
		return "", err
	}

	//如果文件类型是pdf，则需要转换为长图
	ext := strings.ToLower(getFileExtension(savePath))
	if ext == "pdf" {
		newImagePath, err := GenerateLongImageFromPDF(savePath)
		if err != nil {
			return "", err
		}

		savePath = newImagePath
	}

	if ext == "png" || ext == "jpeg" || ext == "jpg" {
		tempPath, err2 := compressImage(savePath)
		if err2 != nil {
			return "", err2
		}

		savePath = tempPath
	}

	uploadPath := path.Join(workweixin.CosUploadPATH, uuid.NewString()+"."+getFileExtension(savePath))

	if _, err = cosClient(reviewpb.CosBucketType_COS_BUCKET_TYPE_PUBLIC).Object.PutFromFile(context.Background(),
		uploadPath, savePath, new(cos.ObjectPutOptions)); err != nil {
		return "", err
	}

	if err := os.Remove(savePath); err != nil {
		return "", err
	}

	return config.GetString("oss.publicCdn") + uploadPath, nil
}

// getFileExtension 返回字符串中的文件扩展名
func getFileExtension(url string) string {
	// 1. 先移除查询参数
	if idx := strings.Index(url, "?"); idx != -1 {
		url = url[:idx]
	}

	// 2. 获取最后一个点后的扩展名
	ext := path.Ext(url)
	if ext != "" {
		// 移除点号
		return ext[1:]
	}
	return ""
}

// GenerateLongImageFromPDF 生成长图并返回图像路径
/*
   调用系统命令 pdftoppm
   https://www.xpdfreader.com/pdftoppm-man.html
   为了处理像素过大的图片，同时resize 图片 + pdftoppm -r 分辨率80
*/
func GenerateLongImageFromPDF(inputPDF string) (string, error) {
	dirUUID := uuid.New().String()
	tempDir := fmt.Sprintf("pdf/%s", dirUUID)

	// 创建临时目录
	err := os.MkdirAll(tempDir, os.ModePerm)
	if err != nil {
		return "", fmt.Errorf("无法创建临时目录: %v", err)
	}
	defer os.RemoveAll(tempDir)

	maxPages := config.GetIntOr("weixin.open.pdfpage", 20)

	pdfCount, err := api.PageCountFile(inputPDF)
	if err != nil {
		return "", err
	}

	if pdfCount < maxPages {
		maxPages = pdfCount
	}

	// 使用 pdftoppm 将PDF的1到maxPages页一次性转换为图片
	outputPattern := fmt.Sprintf("%s/page", tempDir)
	// mac brew 路径 /opt/homebrew/bin/pdftoppm
	pdfTopPm := fmt.Sprintf("%spdftoppm", config.GetStringOr("weixin.open.pdftoppm", "/usr/bin/"))
	cmd := exec.Command(pdfTopPm, "-r", fmt.Sprintf("%d", config.GetIntOr("llm.pdfToImageResolution", 300)), "-f", "1", "-l", fmt.Sprintf("%d", maxPages), "-jpeg", inputPDF, outputPattern)
	err = cmd.Run()
	if err != nil {
		return "", fmt.Errorf("PDF转换为图片时出错: %v", err)
	}

	// 收集生成的图片路径
	var outputImages []string
	for i := 1; i <= maxPages; i++ {
		outputImages = append(outputImages, fmt.Sprintf("%s-%s.jpg", outputPattern, formatNumber(i, pdfCount)))
	}
	// 合并所有图片并返回合成的长图路径
	mergedImagePath := fmt.Sprintf("pdf/%s_merged.jpg", dirUUID)

	if err := combineImages(outputImages, mergedImagePath); err != nil {
		return "", fmt.Errorf("合并图片时出错: %v", err)
	}

	return mergedImagePath, nil
}

// combineImages 合并图片为一个长图
func combineImages(imagePaths []string, outputPath string) error {
	var images []image.Image
	totalHeight := 0
	maxWidth := 0

	for _, imgPath := range imagePaths {
		imgFile, err := os.Open(imgPath)
		if err != nil {
			return fmt.Errorf("打开图片文件时出错: %v", err)
		}
		img, _, err := image.Decode(imgFile)
		imgFile.Close()
		if err != nil {
			return fmt.Errorf("解码图片时出错: %v", err)
		}
		images = append(images, img)

		bounds := img.Bounds()
		totalHeight += bounds.Dy()
		if bounds.Dx() > maxWidth {
			maxWidth = bounds.Dx()
		}
	}

	const maxHeight = 20000
	scaleFactor := 1.0
	if totalHeight > maxHeight {
		scaleFactor = float64(maxHeight) / float64(totalHeight)
		totalHeight = maxHeight
		maxWidth = int(float64(maxWidth) * scaleFactor)
	}

	finalImage := image.NewRGBA(image.Rect(0, 0, maxWidth, totalHeight))
	offsetY := 0
	for _, img := range images {
		bounds := img.Bounds()
		// 根据缩放比例调整每个图片的宽高
		scaledWidth := int(float64(bounds.Dx()) * scaleFactor)
		scaledHeight := int(float64(bounds.Dy()) * scaleFactor)
		scaledImg := resize.Resize(uint(scaledWidth), uint(scaledHeight), img, resize.Lanczos3)

		// 绘制缩放后的图片
		draw.Draw(finalImage, image.Rect(0, offsetY, scaledWidth, offsetY+scaledHeight), scaledImg, scaledImg.Bounds().Min, draw.Over)
		offsetY += scaledHeight
	}

	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("创建输出文件时出错: %v", err)
	}
	defer outputFile.Close()

	options := &jpeg.Options{Quality: config.GetIntOr("llm.pdfToImageQuality", 60)}
	if err := jpeg.Encode(outputFile, finalImage, options); err != nil {
		return fmt.Errorf("编码最终图片时出错: %v", err)
	}

	return nil
}

// extractFilenameWithoutExtension 提取文件名并去掉后缀
func extractFilenameWithoutExtension(filePath string) string {
	fileName := filepath.Base(filePath)
	extIndex := strings.LastIndex(fileName, ".")

	if extIndex == -1 {
		return fileName
	}

	fileNameWithoutExtension := fileName[:extIndex]
	return fileNameWithoutExtension
}

// formatNumber 接收一个整数 num 和最大页数 maxPages，根据 maxPages 的值格式化 num 并返回字符串
func formatNumber(num int, maxPages int) string {
	str := strconv.Itoa(num)

	switch {
	case maxPages < 10:
		// 如果 maxPages 小于 10，不需要加零
		return str
	case maxPages < 100:
		// 如果 maxPages 在 10 到 99 之间，一位数前面加一个零
		if len(str) == 1 {
			return "0" + str
		}
		return str
	default:
		// 如果 maxPages 是三位数或更多，一位数前面加两个零
		if len(str) == 1 {
			return "00" + str
		} else if len(str) == 2 {
			return "0" + str
		}
		return str
	}
}

// compressImage 压缩图片到1M内
func compressImage(savePath string) (string, error) {
	file, err := os.Open(savePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return "", err
	}
	if fileInfo.Size() < 200*1024 {
		return savePath, nil
	}

	img, format, err := image.Decode(file)
	if err != nil {
		return "", err
	}

	// 检查格式
	if format != "jpeg" && format != "png" && format != "jpg" {
		return savePath, nil
	}

	// 宽最多600像素
	width := uint(600)
	img = resize.Resize(width, 0, img, resize.Lanczos3)

	quality := 50
	var buf bytes.Buffer

	for {
		buf.Reset()
		if format == "png" {
			err = png.Encode(&buf, img)
		} else {
			err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
		}
		if err != nil {
			return "", err
		}

		sizeKB := buf.Len() / 1024
		log.Printf("Current size: %dKB, quality: %d, dimensions: %dx%d",
			sizeKB, quality, img.Bounds().Dx(), img.Bounds().Dy())
		if sizeKB < 950 {
			break
		}

		// 大于950k继续压缩
		if sizeKB >= 950 {
			quality -= 5
		} else {
			img = resize.Resize(uint(img.Bounds().Dx()*8/10), 0, img, resize.Lanczos3)
		}

		if quality <= 20 {
			img = resize.Resize(uint(img.Bounds().Dx()*8/10), 0, img, resize.Lanczos3)
		}
	}

	outputPath := filepath.Join(filepath.Dir(savePath), "compressed_"+filepath.Base(savePath))
	outFile, err := os.Create(outputPath)
	if err != nil {
		return "", err
	}
	defer outFile.Close()

	_, err = outFile.Write(buf.Bytes())
	if err != nil {
		return "", err
	}

	if err := os.Remove(savePath); err != nil {
		return "", err
	}

	return outputPath, nil
}
