package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// LoadIdentityNames 加载identity名称
func LoadIdentityNames(ctx context.Context, identities []*basepb.Identity) error {
	if len(identities) == 0 {
		return nil
	}

	var userIDs, teamIDs, opUserIDs []uint64
	for _, identity := range identities {
		switch identity.IdentityType {
		case basepb.IdentityType_IDENTITY_TYPE_USER:
			userIDs = append(userIDs, identity.IdentityId)
		case basepb.IdentityType_IDENTITY_TYPE_TEAM:
			teamIDs = append(teamIDs, identity.IdentityId)
		case basepb.IdentityType_IDENTITY_TYPE_MGMT:
			opUserIDs = append(opUserIDs, identity.IdentityId)
		}
	}

	users, err := FetchUserName(ctx, userIDs)
	if err != nil {
		return err
	}
	teams, err := FetchTeamName(ctx, teamIDs)
	if err != nil {
		return err
	}
	mgmtUsers, err := FetchMgmtUserName(ctx, opUserIDs)
	if err != nil {
		return nil
	}

	for _, identity := range identities {
		switch identity.IdentityType {
		case basepb.IdentityType_IDENTITY_TYPE_USER:
			identity.Name = users[identity.IdentityId]
		case basepb.IdentityType_IDENTITY_TYPE_TEAM:
			identity.Name = teams[identity.IdentityId]
		case basepb.IdentityType_IDENTITY_TYPE_MGMT:
			identity.Name = mgmtUsers[identity.IdentityId]
		}
	}

	return nil
}

// LoadIdentityCards 加载身份卡片
func LoadIdentityCards(ctx context.Context, identities []*basepb.Identity) ([]*iampb.UserCard, []*teampb.TeamCard, error) {
	if len(identities) == 0 {
		return nil, nil, nil
	}

	var userIDs, teamIDs []uint64
	for _, identity := range identities {
		if identity == nil {
			continue
		}
		switch identity.IdentityType {
		case basepb.IdentityType_IDENTITY_TYPE_USER:
			userIDs = append(userIDs, identity.IdentityId)
		case basepb.IdentityType_IDENTITY_TYPE_TEAM:
			teamIDs = append(teamIDs, identity.IdentityId)
			if identity.ExtraId > 0 {
				userIDs = append(userIDs, identity.ExtraId)
			}
		}
	}

	users, err := LoadUserCards(ctx, userIDs)
	if err != nil {
		return nil, nil, err
	}
	teams, err := LoadTeamCards(ctx, teamIDs)
	if err != nil {
		return nil, nil, err
	}

	return users, teams, nil
}

// IsTeamUser 当前用户是否是团队账户
func IsTeamUser(ctx context.Context) bool {
	req := bff.RequestFromContext(ctx)
	return req.Header.Get("As-Team") == "true"
}

// NewUserIdentity 新建用户身份
func NewUserIdentity(ctx context.Context) *basepb.Identity {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	return &basepb.Identity{
		IdentityType: basepb.IdentityType_IDENTITY_TYPE_USER,
		IdentityId:   me.Id,
	}
}

// NewTeamIdentity 新建团队身份
func NewTeamIdentity(ctx context.Context) *basepb.Identity {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	if IsTeamUser(ctx) {
		return nil
	}
	return &basepb.Identity{
		IdentityType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		IdentityId:   me.FirmId,
		ExtraId:      me.Id,
	}
}

// AutoIdentity 自动检测身份
func AutoIdentity(ctx context.Context) *basepb.Identity {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	identity := &basepb.Identity{
		IdentityType: basepb.IdentityType_IDENTITY_TYPE_USER,
		IdentityId:   me.Id,
	}
	if IsTeamUser(ctx) {
		identity.IdentityType = basepb.IdentityType_IDENTITY_TYPE_TEAM
		identity.IdentityId = me.FirmId
		identity.ExtraId = me.Id
	}
	return identity
}
