// Package client RPC客户端
package client

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	cmspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/cms"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	reviewpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	"github.com/asim/go-micro/v3"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	notifypb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify"
	searchpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/search"
	supportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

var (
	// IamClient iam服务（不建议用）
	IamClient iampb.IamService
	// IamNational iam服务（国内）
	IamNational iampb.IamService
	// IamInternational iam服务（国际）
	IamInternational iampb.IamService
	// TeamClient team服务
	TeamClient teampb.TeamService
	// NotifyClient notify服务
	NotifyClient notifypb.NotifyService
	// TagClient 标签服务
	TagClient tagpb.TagService
	// SearchClient 搜索服务
	SearchClient searchpb.SearchService
	// SupportClient support服务
	SupportClient supportpb.SupportService
	// SupportInternationalClient support服务（仅国际）
	SupportInternationalClient supportpb.SupportService
	// ReviewClient 审核服务
	ReviewClient reviewpb.ReviewService
	// CmsClient CMS服务
	CmsClient cmspb.CmsService
	// MgmtClient mgmt服务
	MgmtClient mgmtpb.MgmtService
)

// RegisterClient 注册客户端
func RegisterClient(svc micro.Service) {
	iamService := config.GetStringOr("client.iam", "tanlive.v2.iam")
	IamClient = iampb.NewIamService(iamService, svc.Client())
	IamNational = iampb.NewIamService(config.GetStringOr("client.iam-national", "tanlive.v2.iam"), svc.Client())
	IamInternational = iampb.NewIamService(config.GetStringOr("client.iam-international", "tanlive.v2.iam-international"), svc.Client())

	teamService := config.GetStringOr("client.team", "tanlive.v2.team")
	TeamClient = teampb.NewTeamService(teamService, svc.Client())

	notifyService := config.GetStringOr("client.notify", "tanlive.v2.notify")
	NotifyClient = notifypb.NewNotifyService(notifyService, svc.Client())

	tagService := config.GetStringOr("client.tag", "tanlive.v2.tag")
	TagClient = tagpb.NewTagService(tagService, svc.Client())

	SearchClient = searchpb.NewSearchService(
		config.GetStringOr("client.search", "tanlive.v2.search"), svc.Client())

	supportService := config.GetStringOr("client.support", "tanlive.v2.support")
	SupportClient = supportpb.NewSupportService(supportService, svc.Client())

	supportInternationalService := config.GetStringOr("client.support-international",
		"tanlive.v2.support-international")
	SupportInternationalClient = supportpb.NewSupportService(supportInternationalService, svc.Client())

	AiNational = aipb.NewAiService(config.GetStringOr("client.ai-national",
		"tanlive.v2.ai"), svc.Client())
	AiInternational = aipb.NewAiService(config.GetStringOr("client.ai-international",
		"tanlive.v2.ai-international"), svc.Client())

	ReviewClient = reviewpb.NewReviewService(
		config.GetStringOr("client.review", "tanlive.v2.review"), svc.Client())

	CmsClient = cmspb.NewCmsService(
		config.GetStringOr("client.cms", "tanlive.v2.cms"), svc.Client())

	MgmtClient = mgmtpb.NewMgmtService(config.GetStringOr("client.mgmt", "tanlive.v2.mgmt"), svc.Client())
}
