package client

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

const (
	// MinInternationalUserID 国际用户最小ID（2亿）
	MinInternationalUserID = 200000000
	// MinInternationalID 国际数据库最小ID（1亿亿）
	MinInternationalID = 10000000000000000
)

var (
	// AiNational ai服务（仅国内）
	AiNational aipb.AiService
	// AiInternational ai服务（仅国外）
	AiInternational aipb.AiService
)

// IsInternationalUserID 判断是否为国际用户ID
func IsInternationalUserID(id uint64) bool {
	return id >= MinInternationalUserID
}

// IsInternationalID 判断是否为国际数据库ID
func IsInternationalID(id uint64) bool {
	return id >= MinInternationalID
}

// SplitUserIDByRegion 通过地域分割用户ID
func SplitUserIDByRegion(ids []uint64) (national, international []uint64) {
	for _, id := range ids {
		if IsInternationalUserID(id) {
			international = append(international, id)
		} else {
			national = append(national, id)
		}
	}
	return
}

// AiByRegion 通过地区获取AI客户端
func AiByRegion(region basepb.Region) aipb.AiService {
	if region == basepb.Region_REGION_INTERNATIONAL {
		return AiInternational
	}
	return AiNational
}

// AiByUser 通过用户获取AI客户端
func AiByUser(user *iampb.UserInfo) aipb.AiService {
	return AiByUserID(user.Id)
}

// AiByUserID 通过用户ID获取AI客户端
func AiByUserID(userID uint64) aipb.AiService {
	if IsInternationalUserID(userID) {
		return AiInternational
	}
	return AiNational
}

// AiByID 通过ID获取AI客户端
func AiByID(id uint64) aipb.AiService {
	if IsInternationalID(id) {
		return AiInternational
	}
	return AiNational
}

// IamByRegion 通过地区获取IAM客户端
func IamByRegion(region basepb.Region) iampb.IamService {
	if region == basepb.Region_REGION_INTERNATIONAL {
		return IamInternational
	}
	return IamNational
}

// GetCustomLabelClient 获取label的ai客户端
// 知识库标签只在国内服务
// 对话标签，国内和国外都有
// 如果objectType 未知，可根据label的id来区分
func GetCustomLabelClient(ctx context.Context, objectType aipb.CustomLabelObjectType, labelIds ...uint64) aipb.AiService {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	switch objectType {
	case aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_CHAT:
		return AiByUserID(user.Id)
	case aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA,
		aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_FILE:
		// 知识库只在国内服务
		return AiNational
	default:
		// 根据指定的label id路由
		if labelIds != nil {
			return AiByID(labelIds[0])
		}
		return AiNational
	}
}
