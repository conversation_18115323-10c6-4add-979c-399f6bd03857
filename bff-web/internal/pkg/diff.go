package pkg

import "golang.org/x/exp/constraints"

// DifferenceSet arr1 - arr2 差集
func DifferenceSet[T constraints.Ordered](arr1, arr2 []T) []T {
	// 用 map 存储 arr2 中的元素，方便快速查找
	exists := make(map[T]struct{})
	for _, v := range arr2 {
		exists[v] = struct{}{}
	}

	// 遍历 arr1，找出不存在于 arr2 中的元素
	var result []T
	for _, v := range arr1 {
		if _, found := exists[v]; !found {
			result = append(result, v)
		}
	}

	return result
}
