package search

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	pbase "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	pbbase "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	pbff "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/search"
	pb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/search"
)

// GetReqHeaderLang 获取http请求头中设定的语言
func GetReqHeaderLang(ctx context.Context) string {
	return bff.RequestFromContext(ctx).Header.Get("Lang")
}

// DescribeAtlasSearchOptions 查询所有的图谱搜索筛选项
func (g Guest) DescribeAtlasSearchOptions(ctx context.Context,
	req *pbff.ReqDescribeAtlasSearchOptions, rsp *pbff.RspDescribeAtlasSearchOptions) error {
	results, err := client.SearchClient.DescribeSearchOptions(ctx, &pb.ReqDescribeSearchOptions{
		Offset:  0,
		Limit:   200, // 先简单设置最多获取200个
		OrderBy: []*pbbase.OrderBy{{Column: "weight", Desc: true}},
		Filter: &pb.ReqDescribeSearchOptions_Filter{
			Language:   GetReqHeaderLang(ctx),
			Status:     pbase.DisableState_ENABLED,
			ReferType:  pb.SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_ATLAS,
			TargetType: req.GetTargetType(),
		},
	})
	if err != nil {
		log.WithContext(ctx).Errorw("DescribeAtlasSearchOptions err", "req", req, "err", err.Error())
		return err
	}
	for _, v := range results.Options {
		rsp.Options = append(rsp.Options, &pbff.SearchOption{
			Name:    v.GetOption().GetName(),
			ReferId: v.GetOption().GetReferId(),
		})
	}
	return nil
}

// DescribeSearchPrompts 查询搜索提示语
func (g Guest) DescribeSearchPrompts(ctx context.Context,
	req *pbff.ReqDescribeSearchPrompts, rsp *pbff.RspDescribeSearchPrompts) error {
	results, err := client.SearchClient.DescribeSearchPrompts(ctx, &pb.ReqDescribeSearchPrompts{
		ReferType:  req.ReferType,
		TargetType: req.TargetType,
		Language:   GetReqHeaderLang(ctx),
	})
	if err != nil {
		log.WithContext(ctx).Errorw("DescribeSearchPrompts err", "req", req, "err", err.Error())
		return err
	}
	if len(results.Prompts) == 0 {
		return nil
	}
	rsp.Prompts = &pbff.SearchPrompt{
		Content: results.Prompts[0].Content,
	}
	return nil
}
