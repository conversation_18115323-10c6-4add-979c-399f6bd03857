package team

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	bffteampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/team"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	"google.golang.org/protobuf/types/known/emptypb"
)

// UpdateUgcCustomRoute 创建团队
func (t *Team) UpdateUgcCustomRoute(ctx context.Context, req *bffteampb.ReqUpdateUgcCustomRoute, _ *emptypb.Empty) error {
	_, err := client.TeamClient.UpdateUgcCustomRouteByUgcId(ctx, &teampb.CustomRoute{
		UgcId: req.UgcId,
		Route: req.UgcRoute,
	})
	if err != nil {
		return err
	}
	return nil
}
