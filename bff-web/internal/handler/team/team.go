// Package team 团队服务
package team

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffteampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/team"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// SearchTeamsInAiShareSetting 搜索团队（AI知识分享默认设置场景）
func (t *Team) SearchTeamsInAiShareSetting(ctx context.Context,
	req *bffteampb.ReqSearchTeamsInAiShareSetting, rsp *bffteampb.RspSearchTeamsInAiShareSetting) error {
	data, err := client.TeamClient.GetTeams(ctx, &teampb.ReqGetTeams{
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Filter: &teampb.ReqGetTeams_Filter{
			SearchScene:   teampb.ReqGetTeams_SEARCH_SCENE_AI_SHARE_SETTING,
			SearchKeyword: req.Keyword,
		},
		WithTotalCount: true,
	})
	if err != nil {
		return err
	}

	teams := make([]*bffteampb.FullTeam, 0, len(data.Teams))
	for _, team := range data.Teams {
		teams = append(teams, &bffteampb.FullTeam{
			TeamInfo: team.TeamInfo,
		})
	}

	rsp.Teams = teams
	rsp.TotalCount = data.TotalCount

	return nil
}
