package tag

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	logic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/tag"
	bfftagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/tag"
	tagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
)

// GetSystemTags 获取全量系统标签
func (t Tag) GetSystemTags(ctx context.Context, req *bfftagpb.ReqGetSystemTags,
	rsp *bfftagpb.RspGetSystemTags) error {

	orderBy, err := logic.TransformOrderBy(req.OrderBy)
	if err != nil {
		return err
	}

	rpcRsp, err := client.TagClient.GetSystemTags(ctx, &tagpb.ReqGetSystemTags{
		TaggableType:      req.TaggableType,
		Language:          req.Language,
		OrderBy:           orderBy,
		NotifyType:        req.NotifyType,
		TagName:           req.TagName,
		IsDirectDisplayId: req.IsDirectDisplayId,
	})
	if err != nil {
		return err
	}
	if rpcRsp == nil || len(rpcRsp.TagSet) == 0 {
		log.WithContext(ctx).Debugw("Web GetSystemTags result is nil", "req", req)
		return nil
	}

	rsp.TagSet = make([]*bfftagpb.RspGetSystemTags_Tag, len(rpcRsp.TagSet))
	for i, tag := range rpcRsp.TagSet {
		rsp.TagSet[i] = &bfftagpb.RspGetSystemTags_Tag{
			Id:              tag.Id,
			Name:            tag.Name,
			DirectDisplayId: tag.DirectDisplayId,
		}
	}
	return nil
}
