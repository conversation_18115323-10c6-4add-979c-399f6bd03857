package support

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	bffsupportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/support"
	supportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support"
)

// SendMapRequest 发送地图请求
func (g Guest) SendMapRequest(ctx context.Context,
	req *bffsupportpb.ReqSendMapRequest, rsp *bffsupportpb.RspSendMapRequest) error {
	var (
		err    error
		maprsp *supportpb.MapResponse
	)

	switch req.Platform {
	case supportpb.MapPlatform_MAP_PLATFORM_GOOGLE:
		var googlersp *supportpb.RspProxyGoogleMap
		googlersp, err = client.SupportInternationalClient.ProxyGoogleMap(ctx, &supportpb.ReqProxyGoogleMap{
			Request: req.Request,
			Client:  req.Platform1Client,
		})
		if err == nil {
			maprsp = googlersp.Response
		}
	case supportpb.MapPlatform_MAP_PLATFORM_TENCENT:
		var tencentrsp *supportpb.RspProxyTencentMap
		tencentrsp, err = client.SupportClient.ProxyTencentMap(ctx, &supportpb.ReqProxyTencentMap{
			Request: req.Request,
		})
		if err == nil {
			maprsp = tencentrsp.Response
		}
	}
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	rsp.Response = maprsp
	return nil
}
