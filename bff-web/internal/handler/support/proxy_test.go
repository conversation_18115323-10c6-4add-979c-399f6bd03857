package support

import (
	"context"
	"testing"

	bffpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/support"
)

func TestSupport_Proxy(t *testing.T) {
	req := &bffpb.ReqProxy{
		Urls: []string{"https://m.sohu.com/coo/sg/226902823_210858", "https://3g.163.com/dy/article_v2/IE1FHSDS0512ES8F.html", "https://m.pcbaby.com.cn/x/jtyezn/etjjaq/1102/985595.html", "http://baby.sina.com.cn/health/bbjk/hyieq/2017-01-20/doc-ifxzuswq2556333.shtml", "http://m.youjiao.com/e/20171129/5a1e6c7313a7b.shtml"},
	}
	rsp := &bffpb.RspProxy{}
	err := (&Guest{}).Proxy(context.TODO(), req, rsp)
	if err != nil {
		t.Error(err)
	}
}
