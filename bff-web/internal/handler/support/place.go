package support

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/support"
	bffsupportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/support"
	supportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support"
)

// GetPlaceSelector 查询地点选择器数据
func (g Guest) GetPlaceSelector(ctx context.Context,
	req *bffsupportpb.ReqGetPlaceSelector, rsp *bffsupportpb.RspGetPlaceSelector) error {
	reqGetPlaceSelector := &supportpb.ReqGetPlaceSelector{
		Lang: support.GetHeaderLang(ctx),
	}
	switch cond := req.Condition.(type) {
	case *bffsupportpb.ReqGetPlaceSelector_ByDataType:
		reqGetPlaceSelector.Condition = &supportpb.ReqGetPlaceSelector_ByDataType{
			ByDataType: cond.ByDataType,
		}
	case *bffsupportpb.ReqGetPlaceSelector_ByPlaceId:
		reqGetPlaceSelector.Condition = &supportpb.ReqGetPlaceSelector_ByPlaceId{
			ByPlaceId: cond.ByPlaceId,
		}
	}
	data, err := client.SupportClient.GetPlaceSelector(ctx, reqGetPlaceSelector)
	if err != nil {
		return err
	}
	rsp.Places = data.Places
	return nil
}

// SearchPlaces 搜索地点
func (g Guest) SearchPlaces(ctx context.Context,
	req *bffsupportpb.ReqSearchPlaces, rsp *bffsupportpb.RspSearchPlaces) error {
	data, err := client.SupportClient.SearchPlaces(ctx, &supportpb.ReqSearchPlaces{
		Name:      req.Name,
		Lang:      support.GetHeaderLang(ctx),
		DataType:  req.DataType,
		DataField: req.DataField,
	})
	if err != nil {
		return err
	}
	rsp.Places = data.Places
	return nil
}
