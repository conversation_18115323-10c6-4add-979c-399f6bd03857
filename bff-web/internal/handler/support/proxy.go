package support

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/support"
	bffpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/support"
)

// Proxy 代理网站
func (g Guest) Proxy(ctx context.Context, req *bffpb.ReqProxy, rsp *bffpb.RspProxy) error {
	contents := make([]*bffpb.RspProxy_Content, 0, len(req.Urls))

	if len(req.Urls) > 0 {
		urlTitles := support.FetchHtmlTitles(ctx, req.Urls)
		for url, title := range urlTitles {
			contents = append(contents, &bffpb.RspProxy_Content{Url: url, Title: title})
		}
		rsp.Contents = contents
	}

	return nil
}
