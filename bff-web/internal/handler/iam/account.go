package iam

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/iam"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"google.golang.org/protobuf/types/known/emptypb"
)

// ModifyMyUsername 修改我的用户名
func (i Iam) ModifyMyUsername(ctx context.Context, req *bffiampb.ReqModifyMyUsername, _ *emptypb.Empty) error {
	username, err := iamlogic.RsaDecrypt(req.Username)
	if err != nil {
		return fmt.Errorf("decrypt username: %w", err)
	}

	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err = client.IamClient.ModifyUserUsername(ctx, &iampb.ReqModifyUserUsername{
		UserId:   me.Id,
		Username: username,
		TfaToken: req.TfaToken,
	})
	if err != nil {
		return err
	}

	return nil
}

// ModifyMyPassword 修改我的密码
func (i Iam) ModifyMyPassword(ctx context.Context, req *bffiampb.ReqModifyMyPassword, _ *emptypb.Empty) error {
	password, err := iamlogic.RsaDecrypt(req.Password)
	if err != nil {
		return fmt.Errorf("decrypt password: %w", err)
	}

	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err = client.IamClient.ModifyUserPassword(ctx, &iampb.ReqModifyUserPassword{
		UserId:   me.Id,
		Password: password,
		TfaToken: req.TfaToken,
	})
	if err != nil {
		return err
	}

	return nil
}

// ModifyMyPhone 修改我的手机号
func (i Iam) ModifyMyPhone(ctx context.Context, req *bffiampb.ReqModifyMyPhone, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.IamClient.ModifyUserPhone(ctx, &iampb.ReqModifyUserPhone{
		UserId:   me.Id,
		Phone:    req.Phone,
		TfaToken: req.TfaToken,
		Otp:      req.Otp,
	})
	if err != nil {
		return err
	}

	return nil
}

// ModifyMyEmail 修改我的邮箱
func (i Iam) ModifyMyEmail(ctx context.Context, req *bffiampb.ReqModifyMyEmail, rsp *bffiampb.RspModifyMyEmail) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	lang := logic.GetLang(ctx)
	ip := logic.GetClientIP(ctx)

	// 防水码
	err := logic.ValidateTcloudCaptcha(ctx, req.TcloudCaptcha, ip)
	if err != nil {
		return err
	}

	result, err := client.IamClient.ModifyUserEmail(ctx, &iampb.ReqModifyUserEmail{
		UserId:   me.Id,
		Email:    req.Email,
		TfaToken: req.TfaToken,
		Lang:     lang,
	})
	if err != nil {
		return err
	}

	rsp.TaskNo = result.Result.TaskId
	rsp.SdkError = result.Result.SdkError
	return nil
}

// ActiveMyEmail 激活我的邮箱
func (i Iam) ActiveMyEmail(ctx context.Context, req *bffiampb.ReqActiveMyEmail, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.IamClient.ActiveUserEmail(ctx, &iampb.ReqActiveUserEmail{
		UserId: me.Id,
		Value:  req.Value,
	})
	if err != nil {
		return err
	}

	return nil
}

// CreateBindMyWeixinQrcode 创建绑定我的微信二维码
func (i Iam) CreateBindMyWeixinQrcode(ctx context.Context,
	req *bffiampb.ReqCreateBindMyWeixinQrcode, rsp *bffiampb.RspCreateBindMyWeixinQrcode) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	lang := logic.GetLang(ctx)

	result, err := client.IamClient.CreateBindUserWeixinQrcode(ctx, &iampb.ReqCreateBindUserWeixinQrcode{
		UserId:   me.Id,
		TfaToken: req.TfaToken,
		Lang:     lang,
	})
	if err != nil {
		return err
	}

	rsp.Qrcode = result.Qrcode
	return nil
}

// GetBindMyWeixinState 创建绑定我的微信二维码
func (i Iam) GetBindMyWeixinState(ctx context.Context,
	req *bffiampb.ReqGetBindMyWeixinState, rsp *bffiampb.RspGetBindMyWeixinState) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.GetBindUserWeixinState(ctx, &iampb.ReqGetBindUserWeixinState{
		UserId:   me.Id,
		SceneStr: req.SceneStr,
	})
	if err != nil {
		return err
	}

	rsp.State = result.State
	return nil
}

// BindMyWeixinByOauth2 绑定我的微信（在微信浏览器中）
func (i Iam) BindMyWeixinByOauth2(ctx context.Context,
	req *bffiampb.ReqBindMyWeixinByOauth2, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.IamClient.BindUserWeixinByOauth2(ctx, &iampb.ReqBindUserWeixinByOauth2{
		UserId:   me.Id,
		Code:     req.Code,
		TfaToken: req.TfaToken,
	})
	if err != nil {
		return err
	}

	return nil
}

// UnbindMyWeixin 解绑我的微信
func (i Iam) UnbindMyWeixin(ctx context.Context,
	req *bffiampb.ReqUnbindMyWeixin, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.IamClient.UnbindUserWeixin(ctx, &iampb.ReqUnbindUserWeixin{
		UserId:   me.Id,
		TfaToken: req.TfaToken,
	})
	if err != nil {
		return err
	}

	return nil
}

// BindMyGoogle 绑定我的谷歌账号
func (i Iam) BindMyGoogle(ctx context.Context, req *bffiampb.ReqBindMyGoogle, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.IamClient.BindUserGoogle(ctx, &iampb.ReqBindUserGoogle{
		UserId:   me.Id,
		Code:     req.Code,
		TfaToken: req.TfaToken,
	})
	if err != nil {
		return err
	}

	return nil
}

// UnbindMyGoogle 解绑我的谷歌账号
func (i Iam) UnbindMyGoogle(ctx context.Context,
	req *bffiampb.ReqUnbindMyGoogle, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.IamClient.UnbindUserGoogle(ctx, &iampb.ReqUnbindUserGoogle{
		UserId:   me.Id,
		TfaToken: req.TfaToken,
	})
	if err != nil {
		return err
	}

	return nil
}
