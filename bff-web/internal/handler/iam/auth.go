package iam

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/iam"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"google.golang.org/protobuf/types/known/emptypb"
)

// Login 登入
func (g Guest) Login(ctx context.Context, req *bffiampb.ReqLogin, rsp *bffiampb.RspLogin) error {
	// 防水码
	ip := logic.GetClientIP(ctx)
	err := logic.ValidateTcloudCaptcha(ctx, req.TcloudCaptcha, ip)
	if err != nil {
		return err
	}

	// 初始化认证器
	var authenticator iamlogic.Authenticator
	switch req.LoginType {
	case bffiampb.LoginType_LOGIN_TYPE_USERNAME:
		authenticator = iamlogic.NewUsernameAuthenticator(req.AccountCredentials)
	case bffiampb.LoginType_LOGIN_TYPE_PHONE:
		authenticator = iamlogic.NewPhoneAuthenticator(req.PhoneCredentials)
	case bffiampb.LoginType_LOGIN_TYPE_EMAIL:
		authenticator = iamlogic.NewEmailAuthenticator(req.EmailCredentials)
	case bffiampb.LoginType_LOGIN_TYPE_THIRD:
		authenticator = iamlogic.NewThirdAuthenticator(req.ThirdCredentials)
	default:
		return xerrors.ValidationError("unsupported login_type")
	}

	l := iamlogic.LoginLogic{
		Authenticator: authenticator,
	}

	// 执行登录
	user, err := l.Login(ctx)
	if err != nil {
		return err
	}

	// 绑定第三方账号
	go func() {
		ctx := context.Background()
		if err := l.BindThirdAccount(ctx, user, req.BindThirdTicket); err != nil {
			log.WithContext(ctx).Errorw("bind third account", "err", err)
		}
	}()

	// 更新IP地域
	go func() {
		ctx := context.Background()
		if err := l.UpdateIPRegion(ctx, user); err != nil {
			log.WithContext(ctx).Errorw("bind third account", "err", err)
		}
	}()

	// 设置session
	session := xsession.SessionFromContext(ctx)
	session.SetLoginUser(user.Id)
	if err = xsession.Migrate(ctx, session, true); err != nil {
		return err
	}

	rsp.NewUser = iamlogic.IsNewUser(ctx, user)
	rsp.UserInfo = &bffiampb.RspLogin_UserInfo{
		Image:       user.Image,
		IdentitySet: user.IdentitySet,
		Level:       user.Level,
		UserName:    user.Username,
	}

	return nil
}

// SendAuthOtp 发送认证验证码
func (g Guest) SendAuthOtp(ctx context.Context, req *bffiampb.ReqSendAuthOtp, rsp *bffiampb.RspSendAuthOtp) error {
	ip := bff.RequestFromContext(ctx).Header.Get("X-Real-Ip")
	lang := logic.GetLang(ctx)

	err := logic.ValidateTcloudCaptcha(ctx, req.TcloudCaptcha, ip)
	if err != nil {
		return err
	}

	l := iamlogic.SendAuthOtpLogic{}

	receiver, err := l.FormatReceiver(req)
	if err != nil {
		return err
	}

	result, err := l.CreateOtp(ctx, req.Scene, receiver, lang)
	if err != nil {
		return err
	}

	err = l.HandleRateLimitResult(ctx, result.RateLimitResult)
	if err != nil {
		return err
	}

	rsp.TaskNo = result.Result.TaskId
	rsp.SdkError = result.Result.SdkError

	return nil
}

// Logout 登出
func (i Iam) Logout(ctx context.Context, _ *emptypb.Empty, _ *emptypb.Empty) error {
	session := xsession.SessionFromContext(ctx)
	session.Clear()

	if err := xsession.Migrate(ctx, session, true); err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// GetGoogleAuthUrl 获取谷歌认证URL
func (g Guest) GetGoogleAuthUrl(ctx context.Context,
	req *bffiampb.ReqGetGoogleAuthUrl, rsp *bffiampb.RspGetGoogleAuthUrl) error {
	result, err := client.IamClient.GetGoogleAuthUrl(ctx, &iampb.ReqGetGoogleAuthUrl{
		Scene: req.Scene,
		State: req.State,
	})
	if err != nil {
		return err
	}
	rsp.Url = result.Url
	return nil
}
