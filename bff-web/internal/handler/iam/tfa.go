package iam

import (
	"context"
	"fmt"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/iam"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"google.golang.org/protobuf/types/known/emptypb"
)

// GetMyTfa 查询我的的二次验证
func (i Iam) GetMyTfa(ctx context.Context, _ *emptypb.Empty, rsp *bffiampb.RspGetMyTfa) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.GetUserTfa(ctx, &iampb.ReqGetUserTfa{
		UserId: me.Id,
	})
	if err != nil {
		return err
	}

	rsp.TfaToken = result.TfaToken

	return nil
}

// SendTfaOtp 发送二次验证的验证码
func (i Iam) SendTfaOtp(ctx context.Context, req *bffiampb.ReqSendTfaOtp, rsp *bffiampb.RspSendTfaOtp) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	ip := bff.RequestFromContext(ctx).Header.Get("X-Real-Ip")
	lang := logic.GetLang(ctx)

	err := logic.ValidateTcloudCaptcha(ctx, req.TcloudCaptcha, ip)
	if err != nil {
		return err
	}

	l := iamlogic.SendTfaOtpLogic{}

	receiver, err := l.FormatReceiver(req.ReceiverKind, me)
	if err != nil {
		return err
	}

	result, err := l.CreateOtp(ctx, iampb.OtpScene_OTP_SCENE_2FA, receiver, lang)
	if err != nil {
		return err
	}

	err = l.HandleRateLimitResult(ctx, result.RateLimitResult)
	if err != nil {
		return err
	}

	rsp.TaskNo = result.Result.TaskId
	rsp.SdkError = result.Result.SdkError

	return nil
}

// CreatePhoneTfa 创建手机二次验证
func (i Iam) CreatePhoneTfa(ctx context.Context,
	req *bffiampb.ReqCreatePhoneTfa, rsp *bffiampb.RspCreateTfa) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.CreateUserTfa(ctx, &iampb.ReqCreateUserTfa{
		UserId: me.Id,
		Para: &iampb.ReqCreateUserTfa_PhoneTfa{
			PhoneTfa: &iampb.ReqCreateUserTfa_PhonePara{
				Otp: req.Otp,
			},
		},
	})
	if err != nil {
		return err
	}

	rsp.TfaToken = result.TfaToken

	return nil
}

// CreatePasswordTfa 创建密码二次验证
func (i Iam) CreatePasswordTfa(ctx context.Context,
	req *bffiampb.ReqCreatePasswordTfa, rsp *bffiampb.RspCreateTfa) error {
	password, err := iamlogic.RsaDecrypt(req.Password)
	if err != nil {
		return fmt.Errorf("decrypt password: %w", err)
	}

	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.CreateUserTfa(ctx, &iampb.ReqCreateUserTfa{
		UserId: me.Id,
		Para: &iampb.ReqCreateUserTfa_PasswordTfa{
			PasswordTfa: &iampb.ReqCreateUserTfa_PasswordPara{
				Password: password,
			},
		},
	})
	if err != nil {
		return err
	}

	rsp.TfaToken = result.TfaToken

	return nil
}

// CreateEmailTfa 创建邮箱二次验证
func (i Iam) CreateEmailTfa(ctx context.Context,
	req *bffiampb.ReqCreateEmailTfa, rsp *bffiampb.RspCreateTfa) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.CreateUserTfa(ctx, &iampb.ReqCreateUserTfa{
		UserId: me.Id,
		Para: &iampb.ReqCreateUserTfa_EmailTfa{
			EmailTfa: &iampb.ReqCreateUserTfa_EmailPara{
				Otp: req.Otp,
			},
		},
	})
	if err != nil {
		return err
	}

	rsp.TfaToken = result.TfaToken

	return nil
}

// CreateWeixinTfaQrcode 创建微信二次验证二维码
func (i Iam) CreateWeixinTfaQrcode(ctx context.Context,
	_ *emptypb.Empty, rsp *bffiampb.RspCreateWeixinTfaQrcode) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	lang := logic.GetLang(ctx)

	result, err := client.IamClient.CreateWeixinTfaQrcode(ctx, &iampb.ReqCreateWeixinTfaQrcode{
		UserId: me.Id,
		Lang:   lang,
	})
	if err != nil {
		return err
	}

	rsp.Qrcode = result.Qrcode

	return nil
}

// GetCreateWeixinTfaState 创建绑定我的微信二维码
func (i Iam) GetCreateWeixinTfaState(ctx context.Context,
	req *bffiampb.ReqGetCreateWeixinTfaState, rsp *bffiampb.RspGetCreateWeixinTfaState) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.GetCreateWeixinTfaState(ctx, &iampb.ReqGetCreateWeixinTfaState{
		UserId:   me.Id,
		SceneStr: req.SceneStr,
	})
	if err != nil {
		return err
	}

	rsp.State = result.State
	rsp.TfaToken = result.TfaToken

	return nil
}

// CreateWeixinBrowserTfa 创建微信浏览器二次验证
func (i Iam) CreateWeixinBrowserTfa(ctx context.Context,
	req *bffiampb.ReqCreateWeixinBrowserTfa, rsp *bffiampb.RspCreateTfa) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.CreateUserTfa(ctx, &iampb.ReqCreateUserTfa{
		UserId: me.Id,
		Para: &iampb.ReqCreateUserTfa_WeixinBrowserTfa{
			WeixinBrowserTfa: &iampb.ReqCreateUserTfa_WeixinBrowserPara{
				Code: req.Code,
			},
		},
	})
	if err != nil {
		return err
	}

	rsp.TfaToken = result.TfaToken

	return nil
}

// CreateGoogleTfa 创建谷歌二次认证
func (i Iam) CreateGoogleTfa(ctx context.Context,
	req *bffiampb.ReqCreateGoogleTfa, rsp *bffiampb.RspCreateTfa) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.CreateUserTfa(ctx, &iampb.ReqCreateUserTfa{
		UserId: me.Id,
		Para: &iampb.ReqCreateUserTfa_GoogleTfa{
			GoogleTfa: &iampb.ReqCreateUserTfa_GooglePara{
				Code: req.Code,
			},
		},
	})
	if err != nil {
		return err
	}

	rsp.TfaToken = result.TfaToken

	return nil
}
