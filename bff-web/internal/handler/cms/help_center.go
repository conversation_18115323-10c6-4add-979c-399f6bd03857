package cms

import (
	"net/http"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp/response"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	cmslogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/cms"
)

var internalServerResponse = func() xhttp.Response {
	rsp := xhttp.NewResponse()
	rsp.SetStatusCode(http.StatusInternalServerError)
	return rsp
}()

// GetHelpCenterTree 获取帮助中心菜单
// 下载tree.json过滤
func GetHelpCenterTree(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	// 兼容bff的Context
	ctx = bff.RequestToContext(ctx, req)

	l := cmslogic.HelpCenterTreeLogic{}

	tree, err := l.Download(ctx, req)
	if err != nil {
		log.WithContext(ctx).Errorw("download tree file", "err", err)
		return internalServerResponse
	}

	identity, err := l.GetUserIdentity(ctx, ailogic.GetManagedAssistants)
	if err != nil {
		log.WithContext(ctx).Errorw("get user identity", "err", err)
		return internalServerResponse
	}

	tree = l.FilterVisibility(tree, identity)

	return response.JSON(tree)
}
