package ai

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mclient "github.com/asim/go-micro/v3/client"
)

// CreateChatShare 创建聊天分享
func (a Ai) CreateChatShare(ctx context.Context, req *bffaipb.ReqCreateChatShare, rsp *bffaipb.RspCreateChatShare) error {
	// 获取当前用户信息
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	// 检查会话权限
	chatRsp, err := client.AiByUser(user).GetChatDetail(ctx, &aipb.ReqGetChatDetail{
		Id: req.ChatId,
	})
	if err != nil {
		return err
	}

	if chatRsp.ChatDetail.CreateBy != user.Id {
		return xerrors.ForbiddenError("chat not created by user")
	}

	aiResp, err := client.AiNational.CreateChatShare(ctx, &aipb.ReqCreateChatShare{
		ChatId:      req.ChatId,
		MessageIds:  req.MessageIds,
		UserId:      user.Id,
		ShareType:   aipb.ShareType(req.ShareType),
		ExpireDays:  req.ExpireDays,
		AssistantId: chatRsp.ChatDetail.AssistantId,
	})
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	// 返回分享信息
	rsp.ShareId = aiResp.ShareId
	return nil
}

// ContinueChatFromShare 从分享继续聊天
func (a Ai) ContinueChatFromShare(ctx context.Context, req *bffaipb.ReqContinueChatFromShare, rsp *bffaipb.RspContinueChatFromShare) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	// 获取分享记录
	recordRsp, err := client.AiNational.GetChatShareRecord(ctx, &aipb.ReqGetChatShareRecord{ShareId: req.ShareId, RecordAccess: true, UserId: user.Id})
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	if recordRsp.AssistantId != req.AssistantId {
		return xerrors.NotFoundError("assistant not matched with share")
	}

	// 获取messages
	msgRsp, err := client.AiByID(recordRsp.ChatId).GetChatShareMessages(ctx, &aipb.ReqGetChatShareMessages{ChatId: recordRsp.ChatId, MessageIds: recordRsp.MessageIds})
	if err != nil {
		return err
	}

	if len(msgRsp.GetMessages()) == 0 {
		return xerrors.BadRequestError("no messages found in share")
	}

	// 继续回答
	chatRsp, err := client.AiByUser(user).ContinueChatFromShare(ctx, &aipb.ReqContinueChatFromShare{
		Messages:    msgRsp.Messages,
		UserId:      user.Id,
		ShareId:     req.ShareId,
		ChatType:    req.ChatType,
		AssistantId: recordRsp.AssistantId,
		RegionCode:  user.RegionCode,
	}, mclient.WithRequestTimeout(60*time.Second))
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	// 记录分享访问
	_, err = client.AiNational.RecordChatShareAccess(ctx, &aipb.ReqRecordChatShareAccess{
		ShareId:     req.ShareId,
		UserId:      user.Id,
		NewChatId:   chatRsp.ChatId,
		IsContinued: true,
	})
	if err != nil {
		return err
	}

	rsp.ChatId = chatRsp.ChatId
	rsp.Title = chatRsp.Title
	rsp.AssistantId = recordRsp.AssistantId
	return nil
}

// GetPublicChatShare 获取公开分享详情
func (g Guest) GetPublicChatShare(ctx context.Context, req *bffaipb.ReqGetPublicChatShare, rsp *bffaipb.RspGetPublicChatShare) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	recordRsp, err := client.AiNational.GetChatShareRecord(ctx, &aipb.ReqGetChatShareRecord{ShareId: req.ShareId, RecordAccess: true, UserId: 0})
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	if recordRsp == nil || recordRsp.ChatId == 0 {
		return xerrors.BadRequestError("no chat record found for share ID")
	}

	l := ailogic.MessageDbLogic{}
	messages, err := l.DescribeChatShareMessages(ctx, recordRsp.ChatId, recordRsp.MessageIds)
	if err != nil {
		return err
	}

	var userId uint64
	if user != nil {
		userId = user.Id
	}
	_, err = client.AiNational.RecordChatShareAccess(ctx, &aipb.ReqRecordChatShareAccess{
		ShareId:   req.ShareId,
		UserId:    userId,
		NewChatId: recordRsp.ChatId,
	})
	if err != nil {
		return err
	}

	// 填充返回数据
	rsp.ShareId = req.ShareId
	rsp.ChatId = recordRsp.ChatId
	rsp.AssistantId = recordRsp.AssistantId
	rsp.ShareType = bffaipb.ShareType(recordRsp.ShareType)
	rsp.ShareStatus = bffaipb.ShareStatus(recordRsp.ShareStatus)
	rsp.SharedBy = recordRsp.SharedBy
	rsp.AccessCount = recordRsp.AccessCount
	rsp.ShareDate = recordRsp.ShareDate
	rsp.ExpireDate = recordRsp.ExpireDate
	rsp.LastAccessTime = recordRsp.LastAccessTime
	rsp.Messages = messages

	return nil
}
