package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	logic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/tag"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mclient "github.com/asim/go-micro/v3/client"
	"github.com/hashicorp/go-uuid"
	"github.com/redis/go-redis/v9"
	"golang.org/x/exp/maps"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (a Ai) StopQuestionReply(ctx context.Context, req *bffaipb.ReqStopQuestionReply, rsp *bffaipb.RspStopQuestionReply) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	
	// 查询停止回复时已发送的chunks
	lastEventId := bff.RequestFromContext(ctx).Header.Get("Last-Event-Id")
	lastID, err := strconv.Atoi(lastEventId)
	if err != nil {
		log.WithContext(ctx).Infow("get last event id error", "lastEventId", lastEventId)
		return nil
	}

	if req.HashId == "" || lastID == 0 { // 还没来得及推送chunk就停止了
		_, err = client.AiByUser(user).StopAnswerReply(ctx, &aipb.ReqStopAnswerReply{
			MessageId:      req.Id,
			HashId:         req.HashId,
			StopChunkState: int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK),
		})
		return nil
	}

	store := ailogic.NewChunksSubscriber(req.HashId)
	chunks := store.GetRedisExistChunks(ctx)
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		var think, text string
		var state int32

		sendChunks := chunks[:lastID]
		for index, chunk := range sendChunks {
			pushMsg := ailogic.DecodeRedisMsg(chunk)
			if pushMsg == nil {
				continue
			}
			if index == lastID-1 {
				state = pushMsg.State
			}
			if pushMsg.State == int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_THINK) {
				think += pushMsg.Content
			}
			if pushMsg.State == int32(aipb.ChatMessageState_CHAT_MESSAGE_STATE_STREAM_CHUNK) {
				text += pushMsg.Content
			}
		}

		_, err = client.AiByUser(user).StopAnswerReply(ctx, &aipb.ReqStopAnswerReply{
			MessageId:      req.Id,
			HashId:         req.HashId,
			StopChunkState: state,
			StopText:       text,
			StopThink:      think,
		})
		if err != nil {
			return xerrors.InternalServerError(err)
		}
		return nil
	}, boot.TraceGo(ctx))

	return nil
}

// ReceiveChatMessage 接收会话消息
func (a Ai) ReceiveChatMessage(ctx context.Context, req *bffaipb.ReqReceiveChatMessage, rsp *bffaipb.RspReceiveChatMessage) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	assistant, err := ailogic.CheckAssistantEnable(ctx, req.AssistantId)
	if err != nil {
		return err
	}
	question, err := new(ailogic.MessageDbLogic).CreateReceiveQuestion(ctx, req, user)
	if err != nil {
		return err
	}
	l := ailogic.CreateChatGenerateLogic(user, assistant, question, &bffaipb.ReqChatSubscribe{HashId: "", Duration: 0})
	answers, err := l.SendChatMessageSync(ctx, question)
	if err != nil {
		return err
	}
	if answer := answers[0]; answer != nil {
		rsp.MessageId = question.Id
		rsp.AnswerId = answer.Id
		if req.ShowAnswer {
			rsp.Answer = answer
		}
	}

	return nil
}

// CreateChat 创建会话
func (a Ai) CreateChat(ctx context.Context, req *bffaipb.ReqCreateChat, rsp *bffaipb.RspCreateChat) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	assistant, err := ailogic.CheckAssistantEnable(ctx, req.AssistantId)
	if err != nil {
		return err
	}

	question, err := new(ailogic.MessageDbLogic).CreateChatAndQuestion(ctx, req, assistant, user)
	if err != nil {
		return err
	}

	l := &ailogic.ChatGenerateLogic{
		Assistant: assistant,
		User:      user,
	}
	answers, err := l.SendChatMessageSync(ctx, question)
	if err != nil {
		return err
	}
	if answer := answers[0]; answer != nil {
		rsp.MessageId = question.Id
		rsp.ChatId = question.ChatId
		rsp.AnswerId = answer.Id
		if req.ShowAnswer {
			rsp.Answer = answer
		}
	}

	return nil
}

// DescribeChatMessages 获取会话消息
func (a Ai) DescribeChatMessages(ctx context.Context, req *bffaipb.ReqDescribeChatMessages, rsp *bffaipb.RspDescribeChatMessages) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	if _, err := client.AiByUser(user).CheckChatPermission(ctx, &aipb.ReqCheckChatPermission{
		ChatId: req.ChatId,
		UserId: user.Id,
	}); err != nil {
		return xerrors.ForbiddenError("chat is not accessible")
	}

	// 获取messages
	l := ailogic.MessageDbLogic{}
	messages, totalCount, err := l.DescribeChatQuestionAnswersByPage(ctx, req.ChatId, req.QuestionId, req.Limit, req.Offset, false, !req.WithoutDocs)
	if err != nil {
		return err
	}
	rsp.ChatMessages = messages
	rsp.TotalCount = totalCount
	return nil
}

// DescribeChats 获取会话列表
func (a Ai) DescribeChats(ctx context.Context, req *bffaipb.ReqDescribeChats, rsp *bffaipb.RspDescribeChats) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	response, err := client.AiByUser(user).DescribeUserChats(ctx, &aipb.ReqDescribeUserChats{
		UserId:      user.Id,
		Offset:      req.Offset,
		Limit:       req.Limit,
		AssistantId: req.AssistantId,
	})
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	rsp.Chats = make([]*bffaipb.Chat, len(response.Chat))
	for k, m := range response.Chat {
		rsp.Chats[k] = &bffaipb.Chat{
			Id:         m.Id,
			CreateBy:   m.CreateBy,
			Title:      m.Title,
			CreateDate: m.CreateDate,
			IsShared:   m.IsShared,
		}
	}
	rsp.TotalCount = response.TotalCount
	return nil
}

// DeleteChat 删除会话
func (a Ai) DeleteChat(ctx context.Context, req *bffaipb.ReqDeleteChat, rsp *emptypb.Empty) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	if _, err := client.AiByUser(user).CheckChatPermission(ctx, &aipb.ReqCheckChatPermission{
		ChatId: req.ChatId,
		UserId: user.Id,
	}); err != nil {
		return xerrors.ForbiddenError("chat is not accessible")
	}
	_, err := client.AiByUser(user).DeleteUserChat(ctx, &aipb.ReqDeleteUserChat{
		UserId: user.Id,
		ChatId: req.ChatId,
	})
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	return nil
}

// ResendChatMessage web端重发消息
func (a Ai) ResendChatMessage(ctx context.Context, req *bffaipb.ReqResendChatMessage, rsp *emptypb.Empty) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	if req.MessageId == 0 {
		return xerrors.ParseError("question id is empty")
	}
	// get assistant
	assistant, err := client.AiNational.GetAssistant(ctx, &aipb.ReqGetAssistant{Id: req.AssistantId})
	if err != nil {
		return err
	}

	q, err := client.AiByUser(user).DescribeMessage(ctx, &aipb.ReqDescribeMessage{
		MessageId: req.MessageId,
	})
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	question := q.Message

	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		sendRsp, err := client.AiByUser(user).ResendMessageSync(ctx, &aipb.ReqResendMessageSync{
			QuestionId:      req.MessageId,
			AssistantDetail: assistant.AssistantDetail,
			ClearAnswer:     true,
			PublishHashId:   req.PublishHashId,
		}, mclient.WithRequestTimeout(180*time.Second))
		if err != nil {
			return err
		}

		if len(sendRsp.Message) == 0 || sendRsp.Message[0].Id == 0 {
			return xerrors.InternalServerError("empty answer")
		}
		answer := sendRsp.Message[0]
		var docs []*aipb.ChatMessageDoc
		// 推送消息
		if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION && len(answer.DocNames) > 0 {
			docs, err = ailogic.DescribeMessagesDocs(ctx, []*aipb.ChatMessage{answer}, true)
			if err != nil {
				return err
			}
		}
		eventMsg := ailogic.TransformAIMessageToEventMessage(answer, docs)
		err = ailogic.PublishChatEventHashMessage(ctx, eventMsg, user.Id, question.Id, question.PublishHashId)
		if err != nil {
			return err
		}

		// 推送建议问题
		l := ailogic.ChatGenerateLogic{}
		if err := l.PublishAIMessageSuggestions(ctx, assistant.AssistantDetail, answer, question.Text); err != nil {
			log.WithContext(ctx).Infow("ResendChatMessage PublishAIMessageSuggestions err")
		}

		// save collection snapshot
		if answer.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION {
			if err := ailogic.SaveCollectionSnapshotSync(ctx, answer, assistant.AssistantDetail.CleanChunks); err != nil {
				log.WithContext(ctx).Errorw("ResendChatMessage SaveCollectionSnapshotSync err", "err", err)
				return err
			}
		}

		return nil

	}, boot.TraceGo(ctx))

	return nil
}

// SearchChatUsers 搜索AI对话的用户列表
func (a *Ai) SearchChatUsers(ctx context.Context,
	req *bffaipb.ReqSearchChatUsers, rsp *bffaipb.RspSearchChatUsers) error {
	assistantIds, err := ailogic.RewriteAssistantIds(ctx, req.AssistantIds...)
	if err != nil {
		return err
	}

	creators, err := client.AiByRegion(req.Region).GetAssistantChatCreators(ctx, &aipb.ReqGetAssistantChatCreators{
		AssistantIds: assistantIds,
		UserIds:      req.UserIds,
	})
	if err != nil {
		return err
	}
	if len(creators.UserId) == 0 {
		return nil
	}

	users, err := client.IamByRegion(req.Region).GetUsersPage(ctx, &iampb.ReqGetUsersPage{
		Region: req.Region,
		Filter: &iampb.ReqGetUsersPage_Filter{
			WithinUserId: creators.UserId,
			Keyword:      req.Keyword,
		},
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		WithTotalCount: true,
	})
	if err != nil {
		return err
	}

	rsp.Users = users.Users
	rsp.TotalCount = users.TotalCount

	return nil
}

// ListChat chat对话列表
func (a *Ai) ListChat(ctx context.Context, req *bffaipb.ReqListChat, rsp *bffaipb.RspListChat) error {
	var err error
	aids, err := ailogic.RewriteAssistantIds(ctx, req.AssistantIds...)
	if err != nil {
		return err
	}

	// 没有管理的ai助手
	if len(aids) == 0 {
		return nil
	}

	labelTenantId, err := ailogic.GetCustomLabelTenantId(ctx)
	if err != nil {
		return err
	}

	rpcReq := &aipb.ReqListChat{
		Offset:          req.Offset,
		Limit:           req.Limit,
		CreateDateRange: req.CreateDateRange,
		UpdateDateRange: req.UpdateDateRange,
		Labels:          req.Labels,
		AssistantIds:    aids,
		LabelTenant:     labelTenantId,
		OrderByLabel:    req.OrderByLabel,
		Ids:             req.Ids,
	}
	if req.Filter != nil {
		rpcReq.Filter = &aipb.ReqListChat_Filter{
			UserIds:         req.Filter.UserIds,
			ChatTitles:      req.Filter.ChatTitles,
			ChatType:        req.Filter.ChatType,
			Nicknames:       req.Filter.Nicknames,
			RejectJobResult: req.Filter.RejectJobResult,
			RegionCodes:     req.Filter.RegionCodes,
			IsManual:        req.Filter.IsManual,
		}
	}

	if len(req.OrderBy) > 0 {
		rpcReq.OrderBy, err = logic.TransformOrderBy(req.OrderBy)
		if err != nil {
			return err
		}
	} else { // 默认按照更新时间倒序
		rpcReq.OrderBy = []*basepb.OrderBy{{Column: "update_date", Desc: true}}
	}

	rpcRsp, err := client.AiByRegion(req.Filter.Region).ListChat(ctx, rpcReq)
	if err != nil {
		return err
	}
	if rpcRsp == nil || rpcRsp.TotalCount == 0 {
		return nil
	}

	userIDs := make([]uint64, 0, len(rpcRsp.Chats))
	assistantIds := make(map[uint64]struct{})
	for _, v := range rpcRsp.Chats {
		assistantIds[v.AssistantId] = struct{}{}
		if v.CreateBy > 0 {
			userIDs = append(userIDs, v.CreateBy)
		}
	}
	userMap, err := iamlogic.GetUsersMapByKey(ctx, userIDs)
	if err != nil {
		return err
	}
	for _, v := range userMap {
		if ailogic.IsWechatUnionID(v.Username) { // 小程序的临时用户要显示用户昵称，但是不太方便返回给前端让前端判断显示，此处直接username设置为空，前端显示nickname
			v.Username = ""
			v.NickName = ailogic.ChatUsernameMask(v.NickName)
		}
	}

	rsp.Chats = make([]*bffaipb.ChatInfo, 0, len(rpcRsp.Chats))
	rsp.TotalCount = rpcRsp.TotalCount
	assistantMap := ailogic.GetAssistantInfo(ctx, maps.Keys(assistantIds)...)

	for _, v := range rpcRsp.Chats {
		chat := &bffaipb.ChatInfo{
			Id:              v.Id,
			Title:           v.Title,
			CreateBy:        userMap[v.CreateBy],
			CreateDate:      v.CreateDate,
			UpdateDate:      v.UpdateDate,
			ChatType:        v.ChatType,
			AssistantId:     v.AssistantId,
			AssistantName:   assistantMap.GetName(v.AssistantId),
			ChatState:       assistantMap.GetChatStatus(v.ChatState, v.AssistantId, v.ChatType, v.UpdateDate),
			SupportType:     v.SupportType,
			QuestionCnt:     v.QuestionCnt,
			Labels:          v.Labels,
			DocHits:         v.DocHits,
			AvgDuration:     v.AvgDuration,
			RejectJobResult: v.RejectJobResult,
			RegionCode:      v.RegionCode,
			IsManual:        v.IsManual,
		}

		if v.RatingScale > 8 {
			chat.RatingScale = aipb.RatingScale_RATING_SCALE_SATISFIED
		} else if v.RatingScale == 0 {
			chat.RatingScale = aipb.RatingScale_RATING_SCALE_UNSPECIFIED
		} else if v.RatingScale <= 3 {
			chat.RatingScale = aipb.RatingScale_RATING_SCALE_DISSATISFIED
		} else {
			chat.RatingScale = aipb.RatingScale_RATING_SCALE_AVERAGE
		}

		if len(v.Nickname) > 0 {
			chat.CreateBy = &iampb.UserInfo{Username: ailogic.ChatUsernameMask(v.Nickname)}
		}

		rsp.Chats = append(rsp.Chats, chat)
	}

	return nil
}

// GetChatDetail chat对话详情
func (a *Ai) GetChatDetail(ctx context.Context, req *bffaipb.ReqGetChatDetail,
	rsp *bffaipb.RspGetChatDetail) error {
	aids, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}
	if len(aids) == 0 {
		return nil
	}
	// 获取chat
	chat, err := client.AiByID(req.Id).GetChatDetail(ctx, &aipb.ReqGetChatDetail{
		AssistantId: aids,
		Id:          req.Id,
	})
	if err != nil || chat == nil {
		log.WithContext(ctx).Errorw("GetChatDetail GetChatDetail", "err", err)
		return err
	}

	var userInfo *iampb.UserInfo
	detail := chat.ChatDetail
	if detail.CreateBy == 0 {
		userInfo = &iampb.UserInfo{
			Username: ailogic.ChatUsernameMask(detail.Nickname),
			// Image:    chat.ChatDetail.UserImage,
		}
	} else {
		userInfo, err = iamlogic.GetUserByKey(ctx, detail.CreateBy)
		if err != nil {
			log.WithContext(ctx).Errorw("GetChatDetail GetUserByKey", "err", err)
			return err
		}
		if ailogic.IsWechatUnionID(userInfo.Username) { // 小程序的临时用户要显示用户昵称，但是不太方便返回给前端让前端判断显示，此处直接username设置为空，前端显示nickname
			userInfo.Username = ""
			userInfo.NickName = ailogic.ChatUsernameMask(userInfo.NickName)
		}
	}

	assistantInfo := ailogic.GetAssistantInfo(ctx, detail.AssistantId)
	rsp.ChatDetail = &bffaipb.ChatDetail{
		Id:          detail.Id,
		Title:       detail.Title,
		CreateBy:    userInfo,
		ChatState:   assistantInfo.GetChatStatus(detail.ChatState, detail.AssistantId, detail.ChatType, detail.UpdateDate),
		ChatType:    detail.ChatType,
		CreateDate:  detail.CreateDate,
		FinishDate:  detail.UpdateDate,
		SupportType: detail.SupportType,
		AssistantId: detail.AssistantId,
	}

	if detail.AssistantId > 0 {
		assistant, err1 := client.AiNational.GetAssistant(ctx, &aipb.ReqGetAssistant{Id: detail.AssistantId})
		if err1 != nil || assistant == nil {
			log.WithContext(ctx).Errorw("GetChatDetail GetAssistant error", "err", err1)
		} else {
			rsp.ChatDetail.AssistantAvatar = ailogic.GetWorkWeixinAccountAvatar(ctx,
				assistant.AssistantDetail.AppCode, assistant.AssistantDetail.CorpId)
		}
	}

	if detail.ChatType == aipb.ChatType_CHAT_TYPE_WECHAT || detail.ChatType == aipb.ChatType_CHAT_TYPE_WHATSAPP {
		return ailogic.DescribeUserChatRecords(ctx, req, rsp)
	}

	// 获取messages
	l := ailogic.MessageDbLogic{}
	messages, totalCount, err := l.DescribeChatQuestionAnswersByPage(ctx, req.Id, req.QuestionId, req.Limit, req.Offset, false, true)
	if err != nil {
		return err
	}

	rsp.ChatDetail.Messages = messages
	rsp.TotalCount = totalCount
	if len(messages) > 0 {
		rsp.ChatDetail.CreateDate = messages[0].CreateDate
		rsp.ChatDetail.FinishDate = messages[len(messages)-1].CreateDate
	}

	return nil
}

// GetChatMessageDetail doc搜索详细信息
func (a *Ai) GetChatMessageDetail(ctx context.Context, req *bffaipb.ReqGetChatMessageDetail, rsp *bffaipb.RspGetChatMessageDetail) error {
	aids, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if len(aids) == 0 {
		return nil
	}
	// 获取答案
	response, err := client.AiByID(req.Id).DescribeMessage(ctx, &aipb.ReqDescribeMessage{
		MessageId:      req.Id,
		WithFeedback:   true,
		WithDocs:       true,
		WithCollection: true,
	})
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if response == nil || response.Message == nil {
		return xerrors.InternalServerError(err)
	}
	chatId := response.Message.ChatId
	// 获取chat
	chat, err := client.AiByID(req.Id).GetChatDetail(ctx, &aipb.ReqGetChatDetail{
		AssistantId: aids,
		Id:          chatId,
	})
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if chat == nil || chat.GetChatDetail().GetId() != chatId {
		return nil
	}

	// 获取docs
	docs, err := ailogic.DescribeMessagesDocs(ctx, []*aipb.ChatMessage{response.Message}, false)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	message := ailogic.TransformAIMessageToEventMessage(response.Message, docs)
	rsp.Message = ailogic.EventMessageTransformWithHash(message) // 所有id转为hashID

	// 获取log
	assistant, err := client.AiNational.GetAssistant(ctx, &aipb.ReqGetAssistant{Id: response.Message.AssistantId})
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	res, err := ailogic.DescribeMessageLogs(ctx, response.Message.Id, assistant.AssistantDetail)
	if err != nil {
		return err
	}
	rsp.Logs = res.MessageLogs
	// 获取CollectionSnapshot
	//qres, err := client.AiByID(req.Id).DescribeMessage(ctx, &aipb.ReqDescribeMessage{
	//	MessageId: req.Id,
	//})
	//if err != nil {
	//	return xerrors.InternalServerError(err)
	//}
	//
	if response.Message.CollectionSnapshot != nil {
		// 修复历史collections数据缺失的datasource和docId
		snapshot := response.Message.CollectionSnapshot
		rsp.CollectionTime = &basepb.TimeRange{
			Start: snapshot.StartTime,
			End:   snapshot.EndTime,
		}
		rsp.CleanChunks = snapshot.CleanChunks
		itemsRsp, err := client.AiNational.FixSearchCollectionItems(ctx, &ai.ReqFixSearchCollectionItems{
			Items: snapshot.Items,
		})
		if err == nil {
			snapshot.Items = itemsRsp.Items
			_, err = client.AiByID(req.Id).UpdateChatMessageCollections(ctx, &ai.ReqUpdateChatMessageCollections{
				MessageId:   response.Message.Id,
				Items:       snapshot.Items,
				StartTime:   snapshot.StartTime,
				EndTime:     snapshot.EndTime,
				CleanChunks: snapshot.CleanChunks,
			})
			if err != nil {
				log.WithContext(ctx).Errorf("marshal collection items failed: %v", err)
				return err
			}
		}

		// get contributor..
		var contributors []*ai.Contributor
		for _, doc := range snapshot.Items {
			contributors = append(contributors, doc.Contributor...)
		}
		_, err = ailogic.GeAiDoctContributorShowInfo(ctx, contributors...)
		// get operators
		var operators []*ai.Operator
		for _, v := range snapshot.Items {
			if v.UpdateBy != nil {
				operators = append(operators, v.UpdateBy)
			}
		}
		operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
		if err != nil {
			return err
		}
		for _, v := range snapshot.Items {
			operator := operatorsToShow[v.UpdateBy]

			ci := &bffaipb.SearchCollectionItem{
				Id:          v.Id,
				Text:        v.Text,
				Question:    v.Question,
				Score:       v.Score,
				FileName:    v.FileName,
				Url:         v.Url,
				Contributor: v.Contributor,
				UpdateBy:    operator,
				Type:        v.Type,
				IsRelated:   v.IsRelated,
				DocType:     v.DocType,
				DocId:       v.DocId,
				DataSource:  v.DataSource,
				DocName:     v.DocName,
			}

			// oneshot兼容上线前的历史数据
			if v.Type == ai.SearchCollectionType_SEARCH_COLLECTION_TYPE_UNSPECIFIED {
				ci.Type = ai.SearchCollectionType_SEARCH_COLLECTION_TYPE_VECTOR
			}

			if v.Question != "" {
				ci.DocType = ai.DocType_DOCTYPE_QA
			} else if v.FileName != "" && v.Url != "" {
				ci.DocType = ai.DocType_DOCTYPE_FILE
			} else {
				ci.DocType = ai.DocType_DOCTYPE_TEXT
			}

			rsp.CollectionItems = append(rsp.CollectionItems, ci)

		}

	}

	return nil
}

// SearchChat chat测试
func (a *Ai) SearchChat(ctx context.Context, req *bffaipb.ReqSearchChat, rsp *bffaipb.RspSearchChat) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	var answer *aipb.ChatMessage
	var docs []*ai.ChatMessageDoc

	f := func(ctx context.Context) error {
		var err error
		if answer.Type == ai.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION && len(answer.DocNames) > 0 {
			docs, err = ailogic.DescribeMessagesDocs(ctx, []*ai.ChatMessage{answer}, false)
		}
		return err
	}
	startTime := time.Now()
	if qa, err := client.AiNational.DescribeMessageMatchQa(ctx, &aipb.ReqDescribeMessageMatchQa{Text: req.Text, AssistantId: req.AssistantId}); err == nil && len(qa.Docs) > 0 {
		docNames := []string{qa.Docs[0].RagFilename}
		answerText := qa.Docs[0].Text
		for i := 1; i < len(qa.Docs); i++ {
			answerText += "\n\n" + qa.Docs[i].Text
			docNames = append(docNames, qa.Docs[i].RagFilename)
		}
		answer = &aipb.ChatMessage{
			Type:            aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION,
			QuestionId:      req.QuestionId,
			AssistantId:     req.AssistantId,
			Text:            answerText,
			DocMatchPattern: qa.MatchPattern,
			DocNames:        docNames,
			FinalQuery:      req.Text,
			StartTime:       timestamppb.New(startTime),
			EndTime:         timestamppb.Now(),
		}
		if err = f(ctx); err != nil {
			return err
		}
		rsp.Message = ailogic.TransformAIMessageToEventMessageWithDocText(answer, docs, true)
		rsp.UserId = user.Id
		rsp.IsOnlySearch = true
	} else {
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			// get assistant
			aRsp, err := client.AiNational.GetAssistant(ctx, &aipb.ReqGetAssistant{Id: req.AssistantId})
			if err != nil {
				return err
			}
			assistant := aRsp.AssistantDetail
			if req.TopN > 0 {
				assistant.SearchTopN = int32(req.TopN)
				assistant.DocTopN = int32(req.TopN)
			}
			assistant.Threshold = req.Threshold
			assistant.TextRecallTopN = req.TextRecallTopN
			assistant.Temperature = req.Temperature
			assistant.CleanChunks = req.CleanChunks
			assistant.TextRecallQuery = req.TextRecallQuery
			assistant.TextRecallSlop = req.TextRecallSlop
			assistant.TextRecallPattern = req.TextRecallPattern
			sendRsp, err := client.AiByUser(user).SendMessageWithoutSaveSync(ctx, &ai.ReqSendMessageWithoutSaveSync{
				Text:            req.Text,
				QuestionId:      req.QuestionId,
				AssistantDetail: assistant,
				WaitAnswer:      true,
			}, mclient.WithRequestTimeout(180*time.Second))
			if err != nil {
				return err
			}
			answer = sendRsp.Message

			answer.QuestionId = req.QuestionId
			if err = f(ctx); err != nil {
				return err
			}
			eventMsg := ailogic.TransformAIMessageToEventMessage(answer, docs)
			if _, err = client.AiNational.PublishChatMessage(ctx, &ai.ReqPublishChatMessage{
				Message:      eventMsg,
				UserId:       user.Id,
				IsOnlySearch: true,
			}); err != nil {
				return err
			}
			return nil
		}, boot.TraceGo(ctx))
	}

	return nil
}

func (a *Ai) ProxyChatHtmlUrl(ctx context.Context, req *bffaipb.ReqProxyChatHtmlUrl, rsp *bffaipb.RspProxyChatHtmlUrl) error {
	newCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()
	var urlWithTitles map[string]string
	assistant, err := client.AiNational.GetAssistant(ctx, &aipb.ReqGetAssistant{Id: req.AssistantId})
	if err != nil {
		return err
	}
	urlWithTitles, err = ailogic.FetchChatHtmlTitle(newCtx, req.Urls, assistant.AssistantDetail)
	if err != nil {
		return err
	}
	contents := make([]*bffaipb.RspProxyChatHtmlUrl_Content, 0, len(req.Urls))
	for url, title := range urlWithTitles {
		contents = append(contents, &bffaipb.RspProxyChatHtmlUrl_Content{Url: url, Title: title})
	}
	rsp.Contents = contents
	return nil
}

// DescribeChatRegionCode 获取所有会话的地区编码
func (a Ai) DescribeChatRegionCode(ctx context.Context, req *bffaipb.ReqDescribeChatRegionCode,
	rsp *bffaipb.RspDescribeChatRegionCode) error {
	aids, err := ailogic.RewriteAssistantIds(ctx, req.AssistantIds...)
	if err != nil {
		return err
	}
	if len(aids) == 0 {
		return nil
	}
	regionClient := client.AiNational
	if config.GetBoolOr("bff.abroad", true) {
		regionClient = client.AiByRegion(req.Region)
	}

	rpcRsp, err := regionClient.DescribeChatRegionCode(ctx, &aipb.ReqDescribeChatRegionCode{
		AssistantIds: aids,
	})
	if err != nil {
		return err
	}

	if rpcRsp == nil || len(rpcRsp.RegionCodes) == 0 {
		return nil
	}

	rsp.RegionCodes = rpcRsp.RegionCodes
	return nil
}

// CreateChatQuestion 创建会话问题
func (a Ai) CreateChatQuestion(ctx context.Context, req *bffaipb.ReqCreateChatQuestion, rsp *bffaipb.RspCreateChatQuestion) error {
	var user *iampb.UserInfo
	if user = xsession.UserFromContext[iampb.UserInfo](ctx); user == nil {
		return xerrors.InternalServerError("user is nil")
	}
	// 检查assistant
	assistant, err := ailogic.CheckAssistantEnable(ctx, req.AssistantId)
	if err != nil {
		return xerrors.InternalServerError("assistant enable error")
	}
	userAgent := bff.RequestFromContext(ctx).Header.Get("Tanlive-Agent")
	userAgent = strings.ToLower(userAgent)

	var isMiniProgram bool
	if strings.Contains(userAgent, "miniprogram") {
		isMiniProgram = true
	}

	l := &ailogic.MessageDbLogic{}
	question, err := l.CreateChatSubscribeQuestion(ctx, req, user, assistant, isMiniProgram)
	if err != nil {
		return err
	}
	rsp.QuestionId = question.Id
	rsp.ChatId = question.ChatId
	rsp.Lang = question.Lang
	rsp.IsFileReady = question.IsFileReady
	return nil
}

// DescribeMessageFileState 查询消息文件状态
func (a Ai) DescribeMessageFileState(ctx context.Context, req *bffaipb.ReqDescribeMessageFileState, rsp *bffaipb.RspDescribeMessageFileState) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	fileRsp, err := client.AiByUser(user).DescribeChatMessageFileState(ctx, &aipb.ReqDescribeChatMessageFileState{
		MessageId: req.MessageId,
	})
	if err != nil {
		return err
	}
	rsp.IsFileReady = ailogic.CheckMessageFilesAllReady(fileRsp.Files)
	rsp.Files = fileRsp.Files
	return nil
}

// ChatSubscribe 订阅AI对话消息
func ChatSubscribe(req *xhttp.Request) xhttp.Response {
	return CreateReplySubscribe(req, false)
}

// ResendChatSubscribe 重新发送
func ResendChatSubscribe(req *xhttp.Request) xhttp.Response {
	return CreateReplySubscribe(req, true)
}

func CreateReplySubscribe(req *xhttp.Request, isResend bool) xhttp.Response {
	var (
		err       error
		cw        *ailogic.ChatResponseWriter
		closeChan = make(chan struct{})
		chatId    uint64
	)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	// 解析请求参数
	w := xhttp.ResponseWriterFromContext(req.Context())
	reqBody, err := ailogic.GetChatScribeReqBody(req)
	if err != nil {
		return ailogic.ChatSubscribeResponseError(fmt.Sprintf("GetChatScribeReqBody err %s", err.Error()), chatId, 0)
	}
	// 获取用户信息
	var user *iampb.UserInfo
	if user = xsession.UserFromContext[iampb.UserInfo](req.Context()); user == nil {
		return ailogic.ChatSubscribeResponseError("user is nil", chatId, 0)
	}
	ctx = xsession.UserToContext(ctx, user)

	// 查询question
	questionId, _ := strconv.ParseUint(reqBody.QuestionId, 10, 64)
	question, err := ailogic.DescribeChatQuestion(ctx, user, questionId)
	if err != nil {
		return ailogic.ChatSubscribeResponseError("DescribeChatQuestion err", chatId, questionId)
	}

	// 检查assistant
	assistant, err := ailogic.CheckAssistantEnable(ctx, question.AssistantId)
	if err != nil {
		return ailogic.ChatSubscribeResponseError("assistant enable error", chatId, questionId)
	}

	// 创建logic实例
	creator := ailogic.CreateChatGenerateLogic(user, assistant, question, reqBody)

	// 检查是否有已存在订阅
	wg := xsync.NewGroup(context.Background(), xsync.GroupGoOption(boot.TraceGo(ctx)))
	storeExit := ailogic.CheckChatStoreExist(reqBody.HashId)
	log.WithContext(ctx).Infow("CheckChatStoreExist result", "storeExit", storeExit, "hashId", reqBody.HashId)

	if storeExit { // 存在已有订阅
		// 创建writer
		cw, err = ailogic.NewWriter(ctx, creator, closeChan, w)
		if err != nil {
			return ailogic.ChatSubscribeResponseError("ChatSubscribe http write create error", chatId, questionId)
		}

		// 获取起始推送chunk id
		lastEventId := req.Header.Get("Last-Event-Id")
		lastID, _ := strconv.Atoi(lastEventId)

		// 发送缓存的chunk
		wg.SafeGo(func(ctx context.Context) error {
			cw.SendUnSendChunks(ctx, lastID, reqBody)
			return nil
		})
	} else {
		// 存储新订阅
		ailogic.StoreChatChunk(ctx, reqBody.HashId, "")

		// 创建writer
		cw, err = ailogic.NewWriter(ctx, creator, closeChan, w)
		if err != nil {
			return ailogic.ChatSubscribeResponseError("ChatSubscribe http write create error", chatId, questionId)
		}

		// 创建问题草稿
		answerId, err := ailogic.CreateDraftAnswer(ctx, user, assistant.Id, question, reqBody.HashId)
		if err != nil {
			return ailogic.ChatSubscribeResponseError("CreateAnswerDraft err", chatId, questionId)
		}
		question.AnswerDraftId = answerId
		question.PublishHashId = reqBody.HashId
		cw.SetQuestion(question)

		// 如果是resend，记录answer操作
		if isResend {
			_, err = client.AiByUser(user).CreateChatOperation(ctx, &aipb.ReqCreateChatOperation{
				MessageId:     answerId,
				HashId:        reqBody.HashId,
				ChatId:        question.ChatId,
				QuestionId:    questionId,
				OperationType: aipb.ChatOperationType_CHAT_OPERATION_TYPE_RESEND,
			})
			if err != nil {
				return ailogic.ChatSubscribeResponseError("CreateChatOperation err", chatId, questionId)
			}
		}

		// 订阅redis
		wg.SafeGo(func(ctx context.Context) error {
			cw.CreateRedisPubSub(ctx)
			return nil
		})

		// 生成答案
		wg.SafeGo(func(ctx context.Context) error {
			if isResend { // 重新回答时多轮对话忽略上一个问题
				question.HistoryIgnoreId = questionId
			}
			_, err = creator.SendChatMessageSync(ctx, question) // 开始生成回答
			if err != nil {
				cancel()
				log.WithContext(ctx).Errorf("ChatSubscribe SendChatMessageSync err: %s", err.Error())
				return err
			}
			return nil
		})
	}
	for {
		select {
		case _, ok := <-closeChan:
			if !ok {
				return ailogic.ChatSubscribeResponseError("", 0, 0)
			}
		case <-ctx.Done():
			return ailogic.ChatSubscribeResponseError("", 0, 0)
		}
	}
}

// SearchChatProxy 搜索chat
func SearchChatProxy(req *xhttp.Request) xhttp.Response {
	// 获取用户信息
	var answer *ai.ChatMessage
	hashId, _ := uuid.GenerateUUID()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		return nil
	}
	reqBody := &bffaipb.ReqSearchChatStream{}
	if err := json.Unmarshal(body, &reqBody); err != nil {
		log.WithContext(req.Context()).Errorw("Unmarshal err", "err", err, "body", string(body))
		return ailogic.ChatSubscribeResponseError(string("Unmarshal request body err"), 0, 0)
	}

	w := xhttp.ResponseWriterFromContext(req.Context())
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	var ch <-chan *redis.Message

	if reqBody.Stream {
		// 设置流式响应头（SSE 格式）
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		w.Header().Set("Content-Type", "text/event-stream")
		w.Header().Set("Transfer-Encoding", "chunked")
		//w.Header().Set("Content-Type", "text/plain")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("Connection", "keep-alive")

		// 确保支持 Flush
		_, ok := w.(http.Flusher)
		if !ok {
			return ailogic.ChatSubscribeResponseError("flush init err", 0, 0)
		}
		topic := ailogic.GetChatChannelTopic(hashId)
		pubSub := xredis.Default.Subscribe(ctx, topic)
		ch = pubSub.Channel()
		defer pubSub.Close()
	}

	var logCount int
	writeByte := func(w http.ResponseWriter, data string) error {
		if w == nil {
			return nil
		}
		event := fmt.Sprintf("data: %s\n\n", data)
		if logCount < 10 {
			log.WithContext(ctx).Infow("writeByte", "data", data, "hashId", hashId)
			logCount++
		}

		_, err := w.Write([]byte(event))
		if err != nil {
			log.WithContext(ctx).Errorw("writeByte err", "err", err)
			return err
		}

		// 强制刷新
		if flusher, ok := w.(http.Flusher); ok {
			flusher.Flush()
		}

		return nil
	}

	err = writeByte(w, "heartbeat")
	if err != nil {
		log.WithContext(ctx).Errorw("writeByte err", "err", err)
		return ailogic.ChatSubscribeResponseError("writeByte err", 0, 0)
	}

	// get assistant
	aRsp, err := client.AiNational.GetAssistant(ctx, &ai.ReqGetAssistant{Id: reqBody.AssistantId})
	if err != nil {
		return ailogic.ChatSubscribeResponseError("get assistant err", 0, 0)
	}
	assistant := aRsp.AssistantDetail
	if reqBody.TopN > 0 {
		assistant.SearchTopN = int32(reqBody.TopN)
		assistant.DocTopN = int32(reqBody.TopN)
	}
	assistant.Threshold = reqBody.Threshold
	assistant.TextRecallTopN = reqBody.TextRecallTopN
	assistant.Temperature = reqBody.Temperature
	assistant.CleanChunks = reqBody.CleanChunks

	answerChan := make(chan *ai.ChatMessage, 1)
	wg := xsync.NewGroup(context.Background(), xsync.GroupGoOption(boot.TraceGo(ctx)))
	wg.SafeGo(func(ctx context.Context) error {
		sendRsp, err := client.AiNational.SendMessageWithoutSaveSync(ctx, &ai.ReqSendMessageWithoutSaveSync{
			Text:            reqBody.Text,
			QuestionId:      1,
			AssistantDetail: assistant,
			HashId:          hashId,
			WaitAnswer:      !reqBody.Stream,
		}, mclient.WithRequestTimeout(180*time.Second))
		if err != nil {
			return err
		}
		answer = sendRsp.Message
		answerChan <- answer
		return nil
	})

	if reqBody.Stream {
		for {
			select {
			case msg, ok := <-ch:
				if !ok {
					return nil
				}
				if w != nil {
					err := writeByte(w, msg.Payload)
					if err != nil {
						return nil
					}
					if strings.Contains(msg.Payload, "\"state\\\":10") {
						return ailogic.ChatSubscribeResponseError(string(msg.Payload), 0, 0)
					}
				}
			case msg, _ := <-answerChan:
				if msg != nil && (msg.Type == ai.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR || msg.Type == ai.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR) {
					marshal, err := json.Marshal(msg)
					if err != nil {
						return ailogic.ChatSubscribeResponseError("Marshal answer err", 0, 0)
					}
					writeByte(w, string(marshal))
					return ailogic.ChatSubscribeResponseError(string(marshal), 0, 0)
				}
			case <-ctx.Done():
				return ailogic.ChatSubscribeResponseError("context deadline", 0, 0)
			}
		}
	} else {
		wg.Wait()
		for message := range answerChan {
			marshal, err := json.Marshal(message)
			if err != nil {
				return ailogic.ChatSubscribeResponseError("Marshal answer err", 0, 0)
			}
			return ailogic.ChatSubscribeResponseError(string(marshal), 0, 0)
		}
	}
	return ailogic.ChatSubscribeResponseError("un receive message", 0, 0)
}

// ChatTextProxy 搜索chat
func ChatTextProxy(req *xhttp.Request) xhttp.Response {
	// 获取用户信息
	hashId, _ := uuid.GenerateUUID()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		return nil
	}
	reqBody := &bffaipb.ReqSearchChatStream{}
	if err := json.Unmarshal(body, &reqBody); err != nil {
		log.WithContext(req.Context()).Errorw("Unmarshal err", "err", err, "body", string(body))
		return ailogic.ChatSubscribeResponseError(string("Unmarshal request body err"), 0, 0)
	}

	w := xhttp.ResponseWriterFromContext(req.Context())
	if reqBody.Stream {
		// 设置流式响应头（SSE 格式）
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		w.Header().Set("Content-Type", "text/event-stream")
		w.Header().Set("Transfer-Encoding", "chunked")
		//w.Header().Set("Content-Type", "text/plain")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("Connection", "keep-alive")

		// 确保支持 Flush
		_, ok := w.(http.Flusher)
		if !ok {
			return ailogic.ChatSubscribeResponseError("flush init err", 0, 0)
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	var logCount int
	writeByte := func(w http.ResponseWriter, data string) error {
		if w == nil {
			return nil
		}

		event := fmt.Sprintf("data: %s\n\n", data)
		if logCount < 10 {
			log.WithContext(ctx).Infow("writeByte", "data", data, "hashId", hashId)
			logCount++
		}

		_, err := w.Write([]byte(event))
		if err != nil {
			log.WithContext(ctx).Errorw("writeByte err", "err", err)
			return err
		}

		// 强制刷新
		if flusher, ok := w.(http.Flusher); ok {
			flusher.Flush()
		}

		return nil
	}

	for i := 0; i < 20; i++ {
		err := writeByte(w, fmt.Sprintf("{\"type\":\"chat\",\"data\":\"%d\"}", i))
		if err != nil {
			continue
		}
	}
	return ailogic.ChatSubscribeResponseError("un receive message", 0, 0)
}
