package ai

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mclient "github.com/asim/go-micro/v3/client"
)

// GetDocChunks 查询文档分段信息
func (a *Ai) GetDocChunks(ctx context.Context, req *bffaipb.ReqGetDocChunks, rsp *bffaipb.RspGetDocChunks) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	admin := &base.Identity{
		IdentityType: base.IdentityType_IDENTITY_TYPE_USER,
		IdentityId:   user.Id,
	}
	if logic.AsTeam(ctx) {
		admin.IdentityType = base.IdentityType_IDENTITY_TYPE_TEAM
		admin.IdentityId = user.FirmId
		admin.ExtraId = user.Id
	}

	result, err := client.AiNational.GetDocChunks(ctx, &aipb.ReqGetDocChunks{
		DocId:       req.DocId,
		AssistantId: req.AssistantId,
		Admin:       admin,
	}, newChunkRequestTimeoutCallOption())
	if err != nil {
		return err
	}

	rsp.AssistantChunks = result.AssistantChunks
	return nil
}

// AutoChunkDoc 自动文档分段
func (a *Ai) AutoChunkDoc(ctx context.Context, req *bffaipb.ReqAutoChunkDoc, rsp *bffaipb.RspAutoChunkDoc) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	createBy := &base.Identity{
		IdentityType: base.IdentityType_IDENTITY_TYPE_USER,
		IdentityId:   user.Id,
	}
	if logic.AsTeam(ctx) {
		createBy.IdentityType = base.IdentityType_IDENTITY_TYPE_TEAM
		createBy.IdentityId = user.FirmId
		createBy.ExtraId = user.Id
	}

	result, err := client.AiNational.ChunkDoc(ctx, &aipb.ReqChunkDoc{
		DocId:    req.DocId,
		CreateBy: createBy,
		ChunkPara: &aipb.ReqChunkDoc_AutoPara{
			AutoPara: req.AutoPara,
		},
		NewText: req.NewText,
	}, newChunkRequestTimeoutCallOption())
	if err != nil {
		return err
	}

	rsp.Chunks = result.Chunks
	return nil
}

// ManualChunkDoc 手动文档分段
func (a *Ai) ManualChunkDoc(ctx context.Context, req *bffaipb.ReqManualChunkDoc, rsp *bffaipb.RspManualChunkDoc) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	createBy := &base.Identity{
		IdentityType: base.IdentityType_IDENTITY_TYPE_USER,
		IdentityId:   user.Id,
	}
	if logic.AsTeam(ctx) {
		createBy.IdentityType = base.IdentityType_IDENTITY_TYPE_TEAM
		createBy.IdentityId = user.FirmId
		createBy.ExtraId = user.Id
	}

	result, err := client.AiNational.ChunkDoc(ctx, &aipb.ReqChunkDoc{
		DocId:    req.DocId,
		CreateBy: createBy,
		ChunkPara: &aipb.ReqChunkDoc_ManualPara{
			ManualPara: req.ManualPara,
		},
		NewText: req.NewText,
	}, newChunkRequestTimeoutCallOption())
	if err != nil {
		return err
	}

	rsp.TaskId = result.TaskId
	return nil
}

// GetChunkDocTasks 查询文档分段任务列表
func (a *Ai) GetChunkDocTasks(ctx context.Context, req *bffaipb.ReqGetChunkDocTasks, rsp *bffaipb.RspGetChunkDocTasks) error {
	result, err := client.AiNational.GetChunkDocTasks(ctx, &aipb.ReqGetChunkDocTasks{
		DocId: req.DocId,
	})
	if err != nil {
		return err
	}

	rsp.Tasks = result.Tasks
	return nil
}

// GetDocEmbeddingModels 查询文档的向量化模型
func (a *Ai) GetDocEmbeddingModels(ctx context.Context, req *bffaipb.ReqGetDocEmbeddingModels, rsp *bffaipb.RspGetDocEmbeddingModels) error {
	var userIDs, teamIDs []uint64

	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	if logic.AsTeam(ctx) {
		teamIDs = []uint64{me.FirmId}
	} else {
		userIDs = []uint64{me.Id}
	}

	result, err := client.AiNational.GetDocEmbeddingModels(ctx, &aipb.ReqGetDocEmbeddingModels{
		DocId:       req.DocId,
		AdminTeamId: teamIDs,
		AdminUserId: userIDs,
	})
	if err != nil {
		return err
	}

	rsp.EmbeddingModels = result.EmbeddingModels
	return nil
}

func newChunkRequestTimeoutCallOption() mclient.CallOption {
	return mclient.WithRequestTimeout(config.GetDurationOr("chunk.requestTimeout", time.Minute*3))
}
