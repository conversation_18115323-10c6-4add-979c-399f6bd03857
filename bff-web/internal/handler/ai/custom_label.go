package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"google.golang.org/protobuf/types/known/emptypb"
)

// ListCustomLabel 获取AI自定义标签
func (a Ai) ListCustomLabel(ctx context.Context, req *bffaipb.ReqListCustomLabel, rsp *bffaipb.RspListCustomLabel) error {
	tenant, err := ailogic.GetCustomLabelTenantId(ctx)
	if err != nil {
		return nil
	}
	labels, err := client.GetCustomLabelClient(ctx, req.ObjectType).ListCustomLabel(ctx, &aipb.ReqListCustomLabel{
		TenantId: tenant,
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Ids:        req.Id,
		ObjectType: req.ObjectType,
	})
	if err != nil {
		return err
	}
	rsp.Labels = labels.Labels
	rsp.TotalCount = labels.Total
	return nil
}

// ModifyCustomLabels 插入或更新自定义标签
func (a Ai) ModifyCustomLabels(ctx context.Context, req *bffaipb.ReqModifyCustomLabels, rsp *bffaipb.RspModifyCustomLabels) error {
	for _, label := range req.Labels {
		err := new(ailogic.CustomLabelValidateLogic).Validate(label)
		if err != nil {
			return xerrors.BadRequestError("")
		}
	}

	tenant, err := ailogic.GetCustomLabelTenantId(ctx)
	if err != nil {
		return nil
	}
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	_, err = client.GetCustomLabelClient(ctx, req.ObjectType).UpsertCustomLabels(ctx, &aipb.ReqUpsertCustomLabels{
		TenantId:   tenant,
		UserId:     user.Id,
		Labels:     req.Labels,
		ObjectType: req.ObjectType,
	})
	if err != nil {
		return err
	}
	return nil
}

// DeleteCustomLabels 删除ai自定义标签
func (a Ai) DeleteCustomLabels(ctx context.Context, req *bffaipb.ReqDeleteCustomLabels, rsp *emptypb.Empty) error {
	if len(req.Ids) == 0 {
		return nil
	}
	_, err := client.GetCustomLabelClient(ctx, aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_UNSPECIFIED, req.Ids...).
		DeleteCustomLabels(ctx, &aipb.ReqDeleteCustomLabels{
			Ids: req.Ids,
		})
	if err != nil {
		return err
	}
	return nil
}

// UpdateObjectCustomLabels 更新对象绑定的自定义标签
// 只支持单一区域（国内或者国外，不能混合）
func (a *Ai) UpdateObjectCustomLabels(ctx context.Context, req *bffaipb.ReqUpdateObjectCustomLabels, rsp *emptypb.Empty) error {
	if len(req.Id) == 0 {
		return nil
	}
	rpcReq := &aipb.ReqUpdateCustomChatLabels{
		ObjectId:   req.Id,
		Labels:     req.Labels,
		ObjectType: req.ObjectType,
	}
	switch req.ObjectType {
	case aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_CHAT:
		_, err := client.AiByID(req.Id[0]).UpdateUserChatLabels(ctx, rpcReq)
		if err != nil {
			return err
		}
	case aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA,
		aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_FILE,
		aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE,
		aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_SQL_FILE:
		// 知识库只在国内服务
		_, err := client.AiNational.UpdateDocLabels(ctx, rpcReq)
		if err != nil {
			return err
		}
	}
	return nil
}

// GetCustomLabelValueTopN 获取标签值top
func (a *Ai) GetCustomLabelValueTopN(ctx context.Context, req *bffaipb.ReqGetCustomLabelValueTopN, rsp *bffaipb.RspGetCustomLabelValueTopN) error {
	rpcRsp, err := client.GetCustomLabelClient(ctx, aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_UNSPECIFIED, req.Id).
		GetCustomLabelValueTopN(ctx, &aipb.ReqGetCustomLabelValueTopN{Id: req.GetId(), TopN: 10})
	if err != nil {
		return err
	}
	rsp.Values = rpcRsp.Values
	return nil
}

// ConvertCustomLabel 转换标签类型
func (a *Ai) ConvertCustomLabel(ctx context.Context, req *bffaipb.ReqConvertCustomLabel, rsp *bffaipb.RspConvertCustomLabel) error {
	rpcRsp, err := client.GetCustomLabelClient(ctx, aipb.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_UNSPECIFIED, req.Id).
		ConvertCustomLabel(ctx, &aipb.ReqConvertCustomLabel{Id: req.GetId(), DryRun: req.DryRun, Target: req.Target})
	if err != nil {
		return err
	}
	rsp.Reserved = rpcRsp.Reserved
	rsp.Deleted = rpcRsp.Deleted
	return nil
}
