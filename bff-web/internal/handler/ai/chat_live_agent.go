package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	logic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

// ListChatLiveAgent 获取会话人工坐席列表
func (a *Ai) ListChatLiveAgent(ctx context.Context, req *bffaipb.ReqListChatLiveAgent,
	rsp *bffaipb.RspListChatLiveAgent) error {
	chat, err := client.AiByID(req.ChatId).GetChatDetail(ctx, &ai.ReqGetChatDetail{
		Id: req.ChatId,
	})
	if err != nil {
		return err
	}
	if chat == nil || chat.ChatDetail == nil || chat.ChatDetail.Id == 0 {
		log.WithContext(ctx).Debugw("ListChatLiveAgent chats is empty", "ChatId", req.ChatId)
		return nil
	}
	if chat.ChatDetail.AssistantId == 0 {
		return nil
	}
	rsp.ChatLiveAgent = &ai.ChatLiveAgentInfo{
		ChatId: req.ChatId,
	}

	if _, err = logic.CheckAssistantLiveAgentEnable(ctx, chat.ChatDetail.AssistantId); err != nil {
		log.WithContext(ctx).Errorw("ListChatLiveAgent failed", "err", err, "id", chat.ChatDetail.AssistantId)
		return nil
	}

	liveAgents, err := client.AiNational.ListChatLiveAgent(ctx, &ai.ReqListChatLiveAgent{
		AssistantIds: []uint64{chat.ChatDetail.AssistantId},
		Status:       ai.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_CURRENTLY_SERVING,
	})
	if err != nil {
		return err
	}
	if liveAgents == nil || len(liveAgents.ChatLiveAgents) == 0 {
		return nil
	}
	rsp.ChatLiveAgent.LiveAgents = make([]*ai.ChatLiveAgentInfo_LiveAgentInfo, 0, len(liveAgents.ChatLiveAgents))
	for _, v := range liveAgents.ChatLiveAgents {
		if v.Username == chat.ChatDetail.LiveAgentName { // 把当前人工的username替换为nickname
			rsp.ChatLiveAgent.CurrentLiveAgent = &ai.ChatLiveAgentInfo_LiveAgentInfo{
				Id:       v.Id,
				Nickname: v.Nickname,
			}
		}
		rsp.ChatLiveAgent.LiveAgents = append(rsp.ChatLiveAgent.LiveAgents, &ai.ChatLiveAgentInfo_LiveAgentInfo{
			Id:       v.Id,
			Nickname: v.Nickname,
		})
	}
	return nil
}

// SwitchChatLiveAgent 切换会话人工客服
func (a *Ai) SwitchChatLiveAgent(ctx context.Context, req *bffaipb.ReqSwitchChatLiveAgent,
	rsp *bffaipb.RspSwitchChatLiveAgent) error {
	// 校验会话信息
	chat, err := client.AiByID(req.ChatId).GetChatDetail(ctx, &ai.ReqGetChatDetail{
		Id: req.ChatId,
	})
	if err != nil {
		return err
	}
	if chat == nil || chat.ChatDetail == nil || chat.ChatDetail.Id == 0 { // 会话信息是否存在
		log.WithContext(ctx).Debugw("ListChatLiveAgent chats is empty", "ChatId", req.ChatId)
		rsp.State = ai.SwitchChatState_SWITCH_CHAT_STATE_CHAT_ERROR
		return nil
	} else if chat.ChatDetail.ChatState != ai.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED { // 会话是否已结束
		rsp.State = ai.SwitchChatState_SWITCH_CHAT_STATE_CHAT_FINISHED
		return nil
	}

	if _, err = logic.CheckAssistantLiveAgentEnable(ctx, chat.ChatDetail.AssistantId); err != nil {
		return err
	}

	// 校验人工坐席信息
	liveAgents, err := client.AiNational.ListChatLiveAgent(ctx, &ai.ReqListChatLiveAgent{
		AssistantIds: []uint64{chat.ChatDetail.AssistantId},
		Status:       ai.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_CURRENTLY_SERVING,
		LiveAgentIds: []uint64{req.LiveAgentId},
	})
	if err != nil {
		rsp.State = ai.SwitchChatState_SWITCH_CHAT_STATE_UNSPECIFIED
		return err
	} else if liveAgents == nil || len(liveAgents.ChatLiveAgents) == 0 {
		rsp.State = ai.SwitchChatState_SWITCH_CHAT_STATE_LIVE_AGENT_NOT_EXIST
		return nil
	}
	liveAgentUserName := liveAgents.ChatLiveAgents[0].Username

	if config.GetBoolOr("workWeixin.enable", true) && chat.ChatDetail.ChatType == ai.ChatType_CHAT_TYPE_WECHAT {
		state, err := logic.CheckAndSwitchChatLiveAgent(ctx, liveAgentUserName,
			chat.ChatDetail.ExternalUserId, chat.ChatDetail.AssistantId)
		if err != nil {
			return err
		}
		rsp.State = state
	}

	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err = client.AiByID(req.ChatId).SwitchChatLiveAgent(ctx, &ai.ReqSwitchChatLiveAgent{
		LiveAgentName: liveAgentUserName,
		ChatId:        req.ChatId,
		UpdateBy:      user.Id,
		SupportType:   ai.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT,
	})

	rsp.State = ai.SwitchChatState_SWITCH_CHAT_STATE_SUCCESS
	return err
}
