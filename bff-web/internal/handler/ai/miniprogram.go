package ai

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp/response"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	logic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/miniprogram"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"google.golang.org/protobuf/types/known/emptypb"
)

// GetMiniProgramAuth 获取小程序文档相关权限
func (a Ai) GetMiniProgramAuth(ctx context.Context, req *bffaipb.ReqGetMiniProgramAuth, rsp *bffaipb.RspGetMiniProgramAuth) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	userData, err := logic.LoadUserInfoById(ctx, user.Id)
	if err != nil {
		log.WithContext(ctx).Infow("GetMiniProgramAuth loadUserInfo err")
		return nil
	}

	// authID流程
	if len(req.AuthId) > 0 {
		rsp.HasBindWx = true
		unionID := logic.UnionIDRedis(ctx, req.AuthId, logic.RedisGet)

		if len(unionID) > 0 {
			userData, err = logic.LoadUserInfo(ctx, unionID)
			if err != nil {
				log.WithContext(ctx).Infow("GetMiniProgramAuth loadUserInfo err")
				rsp.HasBindWx = false
				return nil
			} else {
				if userData.Id == 0 {
					rsp.HasBindWx = false
					return nil
				}

				if user.Id != userData.Id {
					rsp.HasBindWx = false
				}
			}
		}

	}

	rsp.HasTeam, rsp.HasAssistant = logic.GetAssistants(ctx, user.Id, user.FirmId)
	rsp.HasDocAuth = logic.LoadEditViewDocAuth(ctx, user.Id, user.FirmId)
	rsp.HasDocAuthRead = logic.LoadEditViewDocAuth(ctx, user.Id, user.FirmId)
	rsp.HasDocAuthWrite = logic.LoadEditReadDocAuth(ctx, user.Id, user.FirmId)

	if len(userData.UnionId) > 0 {
		rsp.UinToken = logic.ConfuseStringByKey(userData.UnionId, config.GetString("miniprogram.wxacode.confusekey"))
	}

	return nil
}

// GetMiniProgramLoginURL 获取小程序登录URL
/*
  将sessionid混淆后存储到redis中 24小时ttl
  1、pc 生成小程序二维码，返回base64图片
  2、h5 生成跳转url Scheme和link方式

  key = miniprogram + scene ; value = refuseSessionId
*/
func (a Ai) GetMiniProgramLoginURL(ctx context.Context, req *bffaipb.ReqGetMiniProgramLoginURL,
	rsp *bffaipb.RspGetMiniProgramLoginURL) error {

	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	/*
		sessionID := xsession.NewID()
		if err := logic.LoadSession(ctx, sessionID, me.Id); err != nil {
			log.WithContext(ctx).Errorw("GetWebViewToMiniProgramToken LoadSession err", "err", err)
			return nil
		}

		refuseSessionId := logic.ConfuseString(sessionID)*/

	scene := fmt.Sprintf("%d%s", time.Now().Unix(), xstrings.Random(6))
	key := fmt.Sprintf("%s%s", "miniprogram", scene)

	xredis.Default.Set(ctx, key, me.Id, 24*3600*time.Second)

	envVersion := config.GetString("miniprogram.wxacode.env_version")
	path := config.GetString("miniprogram.wxacode.path")
	page := config.GetString("miniprogram.wxacode.page")

	switch req.Type {
	case uint32(bffaipb.MiniProgramSourceType_SourceTypeH5Scheme):
		scene = fmt.Sprintf("a=%s&b=%s", scene, "h5")
		scheme, err := miniprogram.MPClient.GetSURLScheme(scene, path, envVersion)
		if err != nil {
			return err
		}
		rsp.Url = scheme
	case uint32(bffaipb.MiniProgramSourceType_SourceTypeH5Url):
		scene = fmt.Sprintf("a=%s&b=%s", scene, "h5")
		url, err := miniprogram.MPClient.GetURLLink(scene, path, envVersion)
		if err != nil {
			return err
		}
		rsp.Url = url
	default:
		scene = fmt.Sprintf("a=%s&b=%s", scene, "pc")
		checkPath := config.GetBool("miniprogram.wxacode.check_path")

		resp, err := miniprogram.MPClient.GetWXACodeUnlimit(miniprogram.QRCoder{
			Page:       page,
			CheckPath:  &checkPath,
			Scene:      scene,
			EnvVersion: envVersion,
			Path:       path,
		})
		if err != nil {
			return err
		}
		imageBase64 := base64.StdEncoding.EncodeToString(resp)
		rsp.Url = fmt.Sprintf("data:image/png;base64,%s", imageBase64)
	}

	return nil
}

// GetWebViewToMiniProgramToken 获取webview 迁移至小程序token
/*
  token：sessionid
  webview_uni_token： 如果存在，webview带过来的unionid
*/
func (a Ai) GetWebViewToMiniProgramToken(ctx context.Context, req *bffaipb.ReqGetWebViewToMiniProgramToken,
	rsp *bffaipb.RspGetWebViewToMiniProgramToken) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	// 新生成用户session_id至小程序， 不影响pc登录态
	sessionID := xsession.NewID()
	if err := logic.LoadSession(ctx, sessionID, me.Id); err != nil {
		log.WithContext(ctx).Errorw("GetWebViewToMiniProgramToken LoadSession err", "err", err)
		return nil
	}
	refuseSessionId := logic.ConfuseString(sessionID)

	key := fmt.Sprintf("%s:%s", refuseSessionId, xstrings.Random(10))
	xredis.Default.Set(ctx, key, me.Id, 600*time.Second)

	rsp.Token = key

	user, err := logic.LoadUserInfoById(ctx, me.Id)
	if err != nil {
		return err
	}

	if user != nil && len(user.UnionId) > 0 {
		rsp.WebviewUniToken = logic.ConfuseStringByKey(user.UnionId, config.GetString("miniprogram.wxacode.confusekey"))
	}

	return nil
}

// BindUnitokenByCode 绑定用户小程序临时映射unionid
func (a Ai) BindUnitokenByCode(ctx context.Context, req *bffaipb.ReqBindUnitokenByCode, empty *emptypb.Empty) error {

	log.WithContext(ctx).Infow("BindUnitokenByCode data", "req", req)

	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	token, _ := miniprogram.MPClient.GetAccessToken()
	log.WithContext(ctx).Infow("BindUnitokenByCode GetAccessToken data", "access_token", token)

	unionID, _, err := miniprogram.MPClient.GetUnionID(req.Code)
	if err != nil {
		log.WithContext(ctx).Errorw("BindUnitokenByCode GetUnionID err", "err", err)
		return nil
	}

	if len(unionID) > 0 {
		key := fmt.Sprintf("%d:%s", me.Id, "temp_uni_token")
		xredis.Default.Set(ctx, key, unionID, 2*3600*time.Second)
	}

	return nil
}

// BindMiniProgramUniID BindMiniProgramUniID
func (a Ai) BindMiniProgramUniID(ctx context.Context, req *bffaipb.ReqBindMiniProgramUniID, empty *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	log.WithContext(ctx).Infow("BindMiniProgramUniID", "req", req)

	unionId, err := logic.DecConfuseStringByKey(req.UinToken, config.GetString("miniprogram.wxacode.confusekey"))
	if err != nil {
		log.WithContext(ctx).Errorw("BindMiniProgramUniID DecConfuseStringByKey err", "req", req)
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	if len(unionId) == 0 {
		log.WithContext(ctx).Infow("BindMiniProgramUniID unionId is empty", "req", req)
		return nil
	}

	if err := logic.UpdateUserInfoUnionId(ctx, me.Id, unionId); err != nil {
		return err
	}

	return nil
}

// GetMiniProgramDocUser 处理来自小程序的文档用户检查
/**
  通过code获取微信用户unionid，通过unionid查询tanlive侧用户存在否
  给出用户属性：个人、团队
  给出用户权限：操作知识库
  给出用户团队是否拥有小助手
*/
func GetMiniProgramDocUser(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	dataResponse := logic.InitUserDataResponse()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.WithContext(ctx).Errorw("GetMiniProgramDocUser ReadAll err", "err", err)
		return response.JSON(dataResponse)
	}

	reqBody := logic.RequestBody{}
	if err = json.Unmarshal(body, &reqBody); err != nil {
		log.WithContext(ctx).Errorw("GetMiniProgramDocUser Unmarshal err", "err", err)
		return response.JSON(dataResponse)
	}

	token, _ := miniprogram.MPClient.GetAccessToken()
	log.WithContext(ctx).Infow("GetMiniProgramDocUser GetAccessToken data", "access_token", token)

	unionID, _, err := miniprogram.MPClient.GetUnionID(reqBody.Code)
	if err != nil {
		log.WithContext(ctx).Errorw("GetMiniProgramDocUser GetUnionID err", "err", err)
		return response.JSON(dataResponse)
	}

	if len(unionID) > 0 {
		dataResponse.Data.User.UinToken = logic.ConfuseStringByKey(unionID, config.GetString("miniprogram.wxacode.confusekey"))
	}

	userData, err := logic.LoadUserInfoCreateTempAccount(ctx, unionID)
	if err != nil {
		return response.JSON(dataResponse)
	}

	sessionID := xsession.NewID()

	if err := logic.LoadSession(ctx, sessionID, userData.Id); err != nil {
		return response.JSON(dataResponse)
	}

	userHashID, _ := hashids.DefaultCodec.Encode(userData.Id)

	// 加载用户基础信息
	dataResponse.Data.TesJwtToken = sessionID
	dataResponse.Data.User.UserImage = userData.Image
	dataResponse.Data.User.UserHashId = userHashID
	dataResponse.Data.User.TimeZone = userData.Timezone
	dataResponse.Data.User.AuthID = logic.UnionIDRedis(ctx, unionID, logic.RedisSet)

	dataResponse.Data.HasTeam, dataResponse.Data.HasAssistant = logic.GetAssistants(ctx, userData.Id, userData.FirmId)

	// 加载用户权限信息
	dataResponse.Data.HasDocAuth = logic.LoadEditViewDocAuth(ctx, userData.Id, userData.FirmId)
	dataResponse.Data.HasDocAuthRead = logic.LoadEditViewDocAuth(ctx, userData.Id, userData.FirmId)
	dataResponse.Data.HasDocAuthWrite = logic.LoadEditReadDocAuth(ctx, userData.Id, userData.FirmId)
	noUserUnionID, tempBindUnionID := logic.LoadUnionIdBindTip(ctx, userData, unionID)
	if noUserUnionID || tempBindUnionID {
		dataResponse.Data.HasOnceBindTip = true
	}

	log.WithContext(ctx).Infow("GetMiniProgramDocUser GetUnionID data", "unionID", unionID, "username", userData.Username,
		"userId", userData.Id, "FirmId", userData.FirmId, "userData", userData, "dataResponse", dataResponse)

	return response.JSON(dataResponse)
}

// GetMiniProgramCode2Token 小程序code换token
/**
  通过code换取混淆token，用户从外部h5、pc来源设置登录态
*/
func GetMiniProgramCode2Token(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	authResponse := logic.InitTokenResponse()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.WithContext(ctx).Errorw("GetMiniProgramCode2Token ReadAll err", "err", err)
		return response.JSON(authResponse)
	}

	reqBody := logic.RequestBody{}
	if err = json.Unmarshal(body, &reqBody); err != nil {
		log.WithContext(ctx).Errorw("GetMiniProgramCode2Token Unmarshal err", "err", err)
		return response.JSON(authResponse)
	}

	key := fmt.Sprintf("%s%s", "miniprogram", reqBody.Code)

	token := xredis.Default.Get(ctx, key).Val()
	if len(token) > 0 {

		userId, err := xredis.Default.Get(ctx, key).Uint64()
		if err != nil {
			return nil
		}

		sessionID := xsession.NewID()
		if err := logic.LoadSession(ctx, sessionID, userId); err != nil {
			log.WithContext(ctx).Errorw("GetMiniProgramCode2Token LoadSession err", "err", err)
			return nil
		}

		refuseSessionId := logic.ConfuseString(sessionID)
		authResponse.Data.Token = refuseSessionId

		return response.JSON(authResponse)
	}

	authResponse.Result.Code = int(errorspb.AiError_AiMiniProgramNoCodeLogin)

	return response.JSON(authResponse)
}

// GetUserBindWebviewToken 获取小程序webview用户绑定状态
func GetUserBindWebviewToken(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	tokenResponse := logic.InitWebviewTokenResponse()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.WithContext(ctx).Errorw("GetUserBindWebviewToken ReadAll err", "err", err)
		return response.JSON(tokenResponse)
	}

	reqBody := logic.RequestWebViewBody{}
	if err := json.Unmarshal(body, &reqBody); err != nil {
		log.WithContext(ctx).Errorw("GetUserBindWebviewToken Unmarshal err", "err", err)
		return response.JSON(tokenResponse)
	}

	userID, err := xredis.Default.Get(ctx, reqBody.Token).Int64()
	if err != nil {
		log.WithContext(ctx).Errorw("GetUserBindWebviewToken xredis.Get err", "err", err)
		return nil
	}

	user, err := logic.LoadUserInfoById(ctx, uint64(userID))
	if err != nil {
		return nil
	}

	if user != nil && len(user.UnionId) > 0 {
		tokenResponse.Data.HasUin = true
		tokenResponse.Data.UserName = user.Username

		userUnion := logic.ConfuseStringByKey(user.UnionId, config.GetString("miniprogram.wxacode.confusekey"))
		if userUnion == reqBody.UinToken {
			tokenResponse.Data.HasUinSame = true
		}
	}

	return response.JSON(tokenResponse)
}

// BindOnceUserMiniProgram 一键绑定用户和小程序unionid
func (a Ai) BindOnceUserMiniProgram(ctx context.Context, req *bffaipb.ReqBindOnceUserMiniProgram,
	rsp *bffaipb.RspBindOnceUserMiniProgram) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	token, _ := miniprogram.MPClient.GetAccessToken()
	log.WithContext(ctx).Infow("BindOnceUserMiniProgram GetAccessToken data", "access_token", token)

	unionID, _, err := miniprogram.MPClient.GetUnionID(req.Code)
	if err != nil {
		log.WithContext(ctx).Errorw("BindOnceUserMiniProgram GetUnionID err", "err", err)
		return nil
	}

	userData, err := logic.LoadUserInfoById(ctx, me.Id)
	if err != nil {
		log.WithContext(ctx).Errorw("GetAccountOnceBindStatus LoadUserInfoById err", "err", err)
		return err
	}

	noUserUnionID, tempBindUnionID := logic.LoadUnionIdBindTip(ctx, userData, unionID)

	// 普通绑定，仅修改用户union_id case
	if noUserUnionID {
		if err := logic.UpdateUserInfoUnionId(ctx, me.Id, unionID); err != nil {
			log.WithContext(ctx).Errorw("BindOnceUserMiniProgram UpdateUserInfoUnionId err", "err", err)
			return nil
		}
	}

	// 临时账号替绑定case
	if tempBindUnionID {

		tempUser, err := logic.LoadUserInfo(ctx, unionID)
		if err != nil {
			log.WithContext(ctx).Errorw("BindOnceUserMiniProgram UpdateUserInfoUnionId err", "err", err)
			return err
		}

		log.WithContext(ctx).Infow("BindOnceUserMiniProgram data", "userId", me.Id, "userName", tempUser.Username)

		userId, uuid, err := logic.BindUserAccountReplaceTempAccount(ctx, me.Id, tempUser.Username)
		if err != nil {
			log.WithContext(ctx).Errorw("BindOnceUserMiniProgram BindUserAccountReplaceTempAccount err", "err", err,
				"uuid", uuid)
			return err
		}

		sessionID, err := logic.SetUserIdNewLoginToken(ctx, userId)
		if err != nil {
			log.WithContext(ctx).Errorw("BindOnceUserMiniProgram SetUserIdNewLoginToken err", "err", err)
			return err
		}

		rsp.Token = sessionID
	}

	return nil
}

// BindMiniProgramPhoneAccount 绑定用户小程序手机号
func (a Ai) BindMiniProgramPhoneAccount(ctx context.Context, req *bffaipb.ReqBindMiniProgramPhoneAccount,
	rsp *bffaipb.RspBindMiniProgramPhoneAccount) error {

	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	// 获取带区号的手机号码
	phoneNumber, countryCode, purePhoneNumber, err := miniprogram.MPClient.GetMobileByCode(req.Code)
	if err != nil {
		log.WithContext(ctx).Errorw("BindMiniProgramPhoneAccount GetMobileByCode err", "err", err)
		return err
	}
	log.WithContext(ctx).Infow("BindMiniProgramPhoneAccount GetMobileByCode data", "phoneNumber", phoneNumber,
		"countryCode", countryCode, "purePhoneNumber", purePhoneNumber)

	userId, userOldExist, _, err := logic.BindUserPhoneReplaceTempAccount(ctx, phoneNumber, countryCode, me.Username)
	if err != nil {
		log.WithContext(ctx).Errorw("BindMiniProgramPhoneAccount BindUserPhoneReplaceTempAccount err", "err", err)
		return err
	}

	// 如果是新注册用户才展示token，存在的用户toke为空
	if !userOldExist {
		sessionID, err := logic.SetUserIdNewLoginToken(ctx, userId)
		if err != nil {
			log.WithContext(ctx).Errorw("BindMiniProgramPhoneAccount SetUserIdNewLoginToken err", "err", err)
			return err
		}

		rsp.Token = sessionID
	}

	user, err := logic.LoadUserInfoById(ctx, userId)
	if err != nil {
		log.WithContext(ctx).Errorw("BindMiniProgramPhoneAccount LoadUserInfoById err", "err", err)
		return err
	}

	rsp.IsHavePhone = userOldExist
	rsp.UserName = logic.AccountPhoneDesensitize(user.Username)
	rsp.Phone = logic.AccountPhoneDesensitize(phoneNumber)

	return nil
}

// BindMiniProgramNormalAccount 绑定用户小程序普通账号
/*
 通过webview登录后的绑定普通账号
*/
func (a Ai) BindMiniProgramNormalAccount(ctx context.Context, req *bffaipb.ReqBindMiniProgramNormalAccount,
	rsp *bffaipb.RspBindMiniProgramNormalAccount) error {

	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	userID, err := xredis.Default.Get(ctx, req.Token).Int64()
	if err != nil {
		log.WithContext(ctx).Errorw("BindMiniProgramNormalAccount xredis.Get err", "err", err)
		return nil
	}

	// code仅使用一次
	if userID > 0 {
		xredis.Default.Del(ctx, req.Token)
	}

	userId, uuid, err := logic.BindUserAccountReplaceTempAccount(ctx, uint64(userID), me.Username)
	if err != nil {
		log.WithContext(ctx).Errorw("BindMiniProgramNormalAccount BindUserAccountReplaceTempAccount err", "err", err,
			"uuid", uuid)
		return err
	}

	sessionID, err := logic.SetUserIdNewLoginToken(ctx, userId)
	if err != nil {
		log.WithContext(ctx).Errorw("BindMiniProgramNormalAccount SetUserIdNewLoginToken err", "err", err)
		return err
	}

	rsp.Token = sessionID
	return nil
}

// GetAccountUnionIdStatus 获取用户绑定状态
func (a Ai) GetAccountUnionIdStatus(ctx context.Context, req *bffaipb.ReqGetAccountUnionIdStatus,
	rsp *bffaipb.RspGetAccountUnionIdStatus) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	token, _ := miniprogram.MPClient.GetAccessToken()
	log.WithContext(ctx).Infow("GetAccountUnionIdStatus GetAccessToken data", "access_token", token)

	unionID, _, err := miniprogram.MPClient.GetUnionID(req.Code)
	if err != nil {
		log.WithContext(ctx).Errorw("GetAccountUnionIdStatus GetUnionID err", "err", err)
		return nil
	}

	userData, err := logic.LoadUserInfoById(ctx, me.Id)
	if userData.UnionId == unionID {
		rsp.BindAccountStatus = true
	}

	return nil
}

// BindSelectedAccount 通过选择账号绑定小程序，替换临时账号
func (a Ai) BindSelectedAccount(ctx context.Context, req *bffaipb.ReqGetMiniProgramUserInfo, rsp *bffaipb.RspGetMiniProgramUserInfo) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	// 通过token获取即将要替换的tanlive账号
	userID, err := logic.GetUserDataByToken(ctx, req.SelectToken)

	if err != nil {
		log.WithContext(ctx).Errorw("BindSelectedAccount GetUserData err", "err", err)
		return nil
	}

	if userID == 0 {
		log.WithContext(ctx).Infow("BindSelectedAccount userID == 0 err")
		return nil
	}

	log.WithContext(ctx).Infow("BindSelectedAccount data", "userID", userID, "userName", me.Username)

	userId, uuid, err := logic.BindUserAccountReplaceTempAccount(ctx, userID, me.Username)
	if err != nil {
		log.WithContext(ctx).Errorw("BindSelectedAccount BindUserAccountReplaceTempAccount err", "err", err, "uuid", uuid)
		return err
	}

	sessionID, err := logic.SetUserIdNewLoginToken(ctx, userId)
	if err != nil {
		log.WithContext(ctx).Errorw("BindSelectedAccount SetUserIdNewLoginToken err", "err", err)
		return err
	}

	rsp.Token = sessionID

	return nil
}

// GetAccountOnceBindStatus 获取当前用户是否一键绑定提示
/*
   1、当前unionID没被使用过
   2、当前unionID是临时账号使用
*/
func (a Ai) GetAccountOnceBindStatus(ctx context.Context, req *bffaipb.ReqGetAccountOnceBindStatus,
	rsp *bffaipb.RspGetAccountOnceBindStatus) error {

	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	token, _ := miniprogram.MPClient.GetAccessToken()
	log.WithContext(ctx).Infow("GetAccountOnceBindStatus GetAccessToken data", "access_token", token)

	unionID, _, err := miniprogram.MPClient.GetUnionID(req.Code)
	if err != nil {
		log.WithContext(ctx).Errorw("GetAccountOnceBindStatus GetUnionID err", "err", err)
		return nil
	}

	userData, err := logic.LoadUserInfoById(ctx, me.Id)
	if err != nil {
		log.WithContext(ctx).Errorw("GetAccountOnceBindStatus LoadUserInfoById err", "err", err)
		return err
	}

	log.WithContext(ctx).Infow("GetAccountOnceBindStatus LoadUserInfoById data", "userData", userData, "unionID", unionID)

	noUserUnionID, tempBindUnionID := logic.LoadUnionIdBindTip(ctx, userData, unionID)
	if noUserUnionID || tempBindUnionID {
		rsp.HasOnceBind = true
	}
	return nil
}

// GetMiniProgramAssistantLimit 获取小程序助手限制
/*

  // 如果限制，需要检测手机号，true 空手机， false 不空；
  bool empty_phone = 2;
  // 是否不可使用 true 不可使用， false 可使用
  bool no_use = 3;
*/
func (a Ai) GetMiniProgramAssistantLimit(ctx context.Context, req *bffaipb.ReqGetMiniProgramAssistantLimit,
	rsp *bffaipb.RspGetMiniProgramAssistantLimit) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	// 在白名单之内
	canAllow, err := logic.LoadUserAssistantLimitCache(ctx, user.Id, req.AssistantId)
	if err != nil {
		log.WithContext(ctx).Infow("GetMiniProgramAssistantLimit LoadUserPhoneAssistantLimit err", "err", err)
		return nil
	}
	rsp.CanIn = canAllow

	havePhone, err := logic.CheckUserHavePhone(ctx, user.Id)
	if err != nil {
		log.WithContext(ctx).Infow("GetMiniProgramAssistantLimit CheckUserHavePhone err")
		return nil
	}
	rsp.EmptyPhone = !havePhone

	return nil
}

// BindUserPhoneByCode 绑定用户手机号
func (a Ai) BindUserPhoneByCode(ctx context.Context, req *bffaipb.ReqBindUserPhoneByCode,
	rsp *bffaipb.RspBindUserPhoneByCode) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	havePhone, err := logic.CheckUserHavePhone(ctx, user.Id)
	if err != nil {
		log.WithContext(ctx).Infow("BindUserPhoneByCode CheckUserHavePhone err")
		return nil
	}

	if havePhone {
		log.WithContext(ctx).Infow("BindUserPhoneByCode havePhone exist", "userId", user.Id)
		return nil
	}

	// 获取带区号的手机号码
	phoneNumber, countryCode, purePhoneNumber, err := miniprogram.MPClient.GetMobileByCode(req.Code)
	if err != nil {
		log.WithContext(ctx).Errorw("BindUserPhoneByCode GetMobileByCode err", "err", err)
		return err
	}

	log.WithContext(ctx).Infow("BindUserPhoneByCode GetMobileByCode data", "phoneNumber", phoneNumber,
		"countryCode", countryCode, "purePhoneNumber", purePhoneNumber)

	if len(purePhoneNumber) == 0 {
		return nil
	}

	weiXinPhone := fmt.Sprintf("+%s-%s", countryCode, purePhoneNumber)
	userId, userOldExist, err := logic.BindUserPhone(ctx, user.Id, weiXinPhone)
	if err != nil {
		log.WithContext(ctx).Errorw("BindUserPhoneByCode BindUserPhone err", "err", err)
		return err
	}

	if req.AssistantId > 0 {
		canAllow, err := logic.LoadUserPhoneAssistantLimit(ctx, user.Id, req.AssistantId, weiXinPhone)
		if err != nil {
			log.WithContext(ctx).Infow("GetMiniProgramAssistantLimit LoadUserPhoneAssistantLimit err", "err", err)
		}
		rsp.CanIn = canAllow
	}

	if userOldExist {
		rsp.IsHavePhone = true
		rsp.BindOtherAccount = true

		user, _ = logic.LoadUserInfoById(ctx, userId)
		rsp.UserName = logic.AccountPhoneDesensitize(user.Username)
	}

	return nil
}

// BatchUserAssistantLimit 批量获取小程序助手限制
func (a Ai) BatchUserAssistantLimit(ctx context.Context, req *bffaipb.ReqBatchUserAssistantLimit,
	rsp *bffaipb.RspBatchUserAssistantLimit) error {

	var wg sync.WaitGroup
	var mu sync.Mutex

	limits := make([]*bffaipb.RspBatchUserAssistantLimit_UserLimit, 0)

	for _, token := range req.Token {
		wg.Add(1)

		xsync.SafeGo(ctx, func(ctx context.Context) error {
			defer wg.Done()

			limit := &bffaipb.RspBatchUserAssistantLimit_UserLimit{}

			userId, err := logic.GetUserDataByToken(ctx, token)
			if err != nil {
				log.WithContext(ctx).Errorw("BatchUserAssistantLimit GetUserDataByToken err", "err", err, "token", token)
				return err
			}

			if userId == 0 {
				log.WithContext(ctx).Infow("BatchUserAssistantLimit userId == 0 err", "token", token)
				return nil
			}

			// 在白名单之内
			canAllow, err := logic.LoadUserAssistantLimitCache(ctx, userId, req.AssistantId)
			if err != nil {
				log.WithContext(ctx).Errorw("BatchUserAssistantLimit LoadUserPhoneAssistantLimit err", "err", err)
				return err
			}
			limit.CanIn = canAllow

			// 检测手机号
			havePhone, err := logic.CheckUserHavePhone(ctx, userId)
			if err != nil {
				log.WithContext(ctx).Errorw("BatchUserAssistantLimit CheckUserHavePhone err", "err", err, "userId", userId)
				return err
			}
			limit.EmptyPhone = !havePhone

			limit.Token = token

			mu.Lock()
			limits = append(limits, limit)
			mu.Unlock()

			return nil
		}, boot.TraceGo(ctx))
	}

	wg.Wait()

	rsp.UserLimits = limits

	return nil
}
