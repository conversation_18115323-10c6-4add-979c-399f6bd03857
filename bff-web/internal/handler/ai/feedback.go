package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"golang.org/x/exp/maps"
	"google.golang.org/protobuf/types/known/emptypb"
)

// RateAiAnswer 评价AI回答
func (a Ai) RateAiAnswer(ctx context.Context, req *bffaipb.ReqRateAiAnswer, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.AiByUser(me).RateAiAnswer(ctx, &aipb.ReqRateAiAnswer{
		ChatMessageId: req.MessageId,
		RatingScale:   req.RatingScale,
		UpdateBy:      me.Id,
	})
	if err != nil {
		return err
	}

	return nil
}

// CreateFeedback 创建用户反馈（助手维度）
func (a Ai) CreateFeedback(ctx context.Context, req *bffaipb.ReqCreateFeedback, rsp *bffaipb.RspCreateFeedback) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	para := &aipb.ReqUpsertUserFeedback{
		Cond: &aipb.ReqUpsertUserFeedback_ByAssistantId{
			ByAssistantId: req.AssistantId,
		},
		Question:   req.Question,
		Answer:     req.Answer,
		References: ailogic.ToFeedbackTypedReferences(req.References),
		Operator:   logic.NewUserIdentity(ctx),
		RegionCode: me.RegionCode,
	}

	result, err := client.AiByUser(me).UpsertUserFeedback(ctx, para)
	if err != nil {
		return err
	}

	rsp.FeedbackId = result.FeedbackId

	return nil
}

// SaveUserFeedbackByQuestion 保存用户反馈（问题维度）
func (a Ai) SaveUserFeedbackByQuestion(ctx context.Context, req *bffaipb.ReqSaveUserFeedbackByQuestion, rsp *bffaipb.RspCreateFeedback) error {
	result, err := client.AiByID(req.AnswerId).UpsertUserFeedback(ctx, &aipb.ReqUpsertUserFeedback{
		Cond: &aipb.ReqUpsertUserFeedback_ByAnswerId{
			ByAnswerId: req.AnswerId,
		},
		Answer:     req.Answer,
		References: ailogic.ToFeedbackTypedReferences(req.References),
		Operator:   logic.NewUserIdentity(ctx),
	})
	if err != nil {
		return err
	}

	rsp.FeedbackId = result.FeedbackId

	return nil
}

// SaveOpFeedback 保存运营反馈
func (a Ai) SaveOpFeedback(ctx context.Context, req *bffaipb.ReqSaveOpFeedback, rsp *bffaipb.RspCreateFeedback) error {
	managedAssistantIDs, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	var cli aipb.AiService
	para := &aipb.ReqUpsertOpFeedback{
		AnswerRating:        req.AnswerRating,
		HitExpectedDoc:      req.HitExpectedDoc,
		DocId:               req.DocId,
		OpComment:           req.OpComment,
		Operator:            logic.AutoIdentity(ctx),
		OperatorAssistantId: managedAssistantIDs,
	}
	if req.AnswerId > 0 {
		para.Cond = &aipb.ReqUpsertOpFeedback_ByAnswerId{
			ByAnswerId: req.AnswerId,
		}
		cli = client.AiByID(req.AnswerId)
	} else {
		para.Cond = &aipb.ReqUpsertOpFeedback_ByFeedbackId{
			ByFeedbackId: req.FeedbackId,
		}
		cli = client.AiByID(req.FeedbackId)
	}

	result, err := cli.UpsertOpFeedback(ctx, para)
	if err != nil {
		return err
	}

	rsp.FeedbackId = result.FeedbackId

	return nil
}

// GetFeedbacks 查询用户反馈列表
func (a *Ai) GetFeedbacks(ctx context.Context, req *bffaipb.ReqGetFeedbacks, rsp *bffaipb.RspGetFeedbacks) error {
	managedAssistantIDs, err := ailogic.RewriteAssistantIds(ctx, req.AssistantIds...)
	if err != nil {
		return err
	}

	if len(managedAssistantIDs) == 0 {
		return nil
	}

	l := ailogic.GetFeedbacks{}

	feedbacks, totalCount, err := l.Get(ctx, req, managedAssistantIDs)
	if err != nil {
		return err
	}

	if err = ailogic.LoadFeedbackExpectedDocs(ctx, feedbacks); err != nil {
		return err
	}

	userCards, teamCards, err := ailogic.LoadFeedbackIdentityCards(ctx, feedbacks)
	if err != nil {
		return err
	}

	assistantMap := ailogic.GetAssistantInfo(ctx, maps.Keys(l.AssistantIds)...)

	rsp.Items = l.ToResponse(feedbacks, assistantMap)
	rsp.TotalCount = totalCount
	rsp.UserCards = userCards
	rsp.TeamCards = teamCards

	return nil
}

// FindFeedback 查询用户反馈详情
func (a *Ai) FindFeedback(ctx context.Context, req *bffaipb.ReqFindFeedback, rsp *bffaipb.RspFindFeedback) error {
	l := ailogic.FindFeedback{}

	feedback, err := l.Find(ctx, req.FeedbackId)
	if err != nil {
		return err
	}

	if err = ailogic.LoadFeedbackExpectedDocs(ctx, []*aipb.FullFeedback{feedback}); err != nil {
		return err
	}

	userCards, teamCards, err := ailogic.LoadFeedbackIdentityCards(ctx, []*aipb.FullFeedback{feedback})
	if err != nil {
		return err
	}

	rsp.Feedback = feedback.Feedback
	rsp.References = feedback.References
	rsp.OriginalQuestion = feedback.OriginalQuestion
	rsp.OriginalAnswer = feedback.OriginalAnswer
	rsp.ExpectedDocs = feedback.ExpectedDocs
	rsp.ExpectedMgmtDocs = feedback.ExpectedMgmtDocs
	rsp.UserCards = userCards
	rsp.TeamCards = teamCards

	return nil
}

// AcceptFeedback 采用反馈
func (a *Ai) AcceptFeedback(ctx context.Context,
	req *bffaipb.ReqAcceptFeedback, rsp *bffaipb.RspAcceptFeedback) error {
	l := ailogic.AcceptFeedback{}

	operator := logic.AutoIdentity(ctx)
	results := l.BatchAccept(ctx, req.FeedbackIds, operator)

	rsp.Results = results
	return nil
}

// GetFeedbackLogs 查询用户反馈日志列表
func (a *Ai) GetFeedbackLogs(ctx context.Context,
	req *bffaipb.ReqGetFeedbackLogs, rsp *bffaipb.RspGetFeedbackLogs) error {
	managedAssistantIDs, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	if len(managedAssistantIDs) == 0 {
		return nil
	}

	l := ailogic.GetFeedbackLogs{}

	logs, totalCount, err := l.Get(ctx, req, managedAssistantIDs)
	if err != nil {
		return err
	}

	userCards, teamCards, err := l.LoadIdentities(ctx, logs)
	if err != nil {
		return err
	}

	rsp.Items = logs
	rsp.TotalCount = totalCount
	rsp.UserCards = userCards
	rsp.TeamCards = teamCards

	return nil
}

// DescribeFeedbackRegionCode 获取所有教学反馈的地区编码
func (a *Ai) DescribeFeedbackRegionCode(ctx context.Context, req *bffaipb.ReqDescribeFeedbackRegionCode,
	rsp *bffaipb.RspDescribeFeedbackRegionCode) error {
	regionClient := client.AiNational
	if config.GetBoolOr("bff.abroad", true) {
		regionClient = client.AiByRegion(req.Region)
	}

	rpcRsp, err := regionClient.DescribeFeedbackRegionCode(ctx, &aipb.ReqDescribeFeedbackRegionCode{})
	if err != nil {
		return err
	}

	if rpcRsp == nil || len(rpcRsp.RegionCodes) == 0 {
		return nil
	}

	rsp.RegionCodes = rpcRsp.RegionCodes
	return nil
}
