package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"google.golang.org/protobuf/types/known/emptypb"
)

// ConfirmAiServiceTerms 确认AI服务协议
func (a Ai) ConfirmAiServiceTerms(ctx context.Context, req *bffaipb.ReqConfirmAiServiceTerms, _ *emptypb.Empty) error {
	l := ailogic.AiServiceTermsLogic{}

	// 未开通协议则无需确认协议
	hasAssistants, err := l.UserHasAssistants(ctx)
	if err != nil {
		return err
	}
	if !hasAssistants {
		return nil
	}

	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	lang := bff.RequestFromContext(ctx).Header.Get("Lang")

	identity, team, err := l.GetUserIdentity(ctx, me)
	if err != nil {
		return err
	}

	termsType := l.GetUserTermsType(me, team, lang)

	if err = l.Confirm(ctx, me, identity, termsType, req.IsAgreed); err != nil {
		return err
	}

	// 保存弹窗状态
	session := xsession.SessionFromContext(ctx)
	session.Set("ai_service_terms_popup", true)

	return nil
}

// GetMyAiServiceTermsConfirmation 获取我的AI服务协议确认情况
func (a Ai) GetMyAiServiceTermsConfirmation(ctx context.Context,
	_ *emptypb.Empty, rsp *bffaipb.RspGetMyAiServiceTermsConfirmation) error {
	l := ailogic.AiServiceTermsLogic{}

	// 未开通协议则无需确认协议
	hasAssistants, err := l.UserHasAssistants(ctx)
	if err != nil {
		return err
	}
	if !hasAssistants {
		return nil
	}

	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	lang := bff.RequestFromContext(ctx).Header.Get("Lang")

	identity, team, err := l.GetUserIdentity(ctx, me)
	if err != nil {
		return err
	}
	assistantIdentity := int32(identity.IdentityType)

	// 如果已弹窗，不再继续获取协议信息
	popup := xsession.SessionFromContext(ctx).Get("ai_service_terms_popup").Bool()
	if popup {
		rsp.AssistantIdentity = assistantIdentity
		rsp.Popup = popup
		return nil
	}

	termsType := l.GetUserTermsType(me, team, lang)

	isAgreed, docPath, err := l.GetConfirmation(ctx, identity, termsType)
	if err != nil {
		return err
	}

	var isVerified bool
	if team != nil {
		isVerified = team.IsVerified
	}

	rsp.AssistantIdentity = assistantIdentity
	rsp.Popup = popup
	rsp.IsAgreed = isAgreed
	rsp.TermsType = termsType
	rsp.TermsDoc = docPath
	rsp.IsVerified = isVerified

	return nil
}
