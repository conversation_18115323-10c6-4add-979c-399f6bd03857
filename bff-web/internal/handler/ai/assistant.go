package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/pkg"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"google.golang.org/protobuf/types/known/emptypb"
)

// ListAssistant 获取AI助手列表
func (a Ai) ListAssistant(ctx context.Context, req *bffaipb.ReqListAssistant, rsp *bffaipb.RspListAssistant) error {
	if req.Language == "" {
		req.Language = bff.RequestFromContext(ctx).Header.Get("Lang")
	}
	pbReq := &aipb.ReqListAssistant{
		Page:           &basepb.Paginator{Limit: req.Limit, Offset: req.Offset},
		Type:           req.Type,
		WithCollection: true,
		Name:           req.Name,
		Language:       req.Language,
	}
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	if !iamlogic.IsTeamUser(ctx) {
		pbReq.Admins = []*aipb.AssistantAdmin{{
			Id:   user.Id,
			Type: basepb.IdentityType_IDENTITY_TYPE_USER,
		}}
	} else {
		team, err := iamlogic.FetchUserTeam(ctx, user.Id)
		if err != nil {
			return err
		}
		if team.Id != 0 {
			pbReq.Admins = append(pbReq.Admins, &aipb.AssistantAdmin{
				Id:   team.Id,
				Type: basepb.IdentityType_IDENTITY_TYPE_TEAM,
			})
		} else {
			return xerrors.BadRequestError("")
		}
	}
	pbRsp, err := client.AiNational.ListAssistant(ctx, pbReq)
	if err != nil {
		return err
	}
	rsp.Assistants = pbRsp.Assistants
	rsp.TotalCount = pbRsp.Total
	return nil
}

// GetMyAssistants 获取我的助手列表
func (a Ai) GetMyAssistants(ctx context.Context, req *bffaipb.ReqGetMyAssistants, rsp *bffaipb.RspGetMyAssistants) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	scene := aipb.ReqGetAssistants_VISIBLE_SCENE_IN_CONSOLE_LIST
	if len(req.AssistantId) > 0 {
		scene = aipb.ReqGetAssistants_VISIBLE_SCENE_IN_CONSOLE_DETAIL
	}

	result, err := client.AiNational.GetAssistants(ctx, &aipb.ReqGetAssistants{
		Filter: &aipb.ReqGetAssistants_Filter{
			UserId:      []uint64{me.Id},
			AssistantId: req.AssistantId,
			IsDraft:     basepb.BoolEnum_BOOL_ENUM_FALSE,
		},
		OrderBy: []*basepb.OrderBy{
			{Column: "update_date", Desc: true},
		},
		Relation: &aipb.ReqGetAssistants_Relation{
			Collection: true,
			LiveAgent:  true,
			Admin:      true,
			Allowlist:  true,
		},
		Page: &basepb.Paginator{
			Limit:  req.Limit,
			Offset: req.Offset,
		},
		WithTotalCount: true,
		VisibleScene:   scene,
	})
	if err != nil {
		return err
	}

	if err = ailogic.LoadAssistantsIdentityName(ctx, result.Assistants); err != nil {
		return err
	}

	rsp.Assistants = result.Assistants
	rsp.TotalCount = result.TotalCount

	return nil
}

// GetMyTeamAssistants 获取我团队的助手列表
func (a Ai) GetMyTeamAssistants(ctx context.Context, req *bffaipb.ReqGetMyAssistants, rsp *bffaipb.RspGetMyAssistants) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	if me.FirmId == 0 {
		return xerrors.ForbiddenError("user has no team")
	}

	scene := aipb.ReqGetAssistants_VISIBLE_SCENE_IN_CONSOLE_LIST
	if len(req.AssistantId) > 0 {
		scene = aipb.ReqGetAssistants_VISIBLE_SCENE_IN_CONSOLE_DETAIL
	}

	result, err := client.AiNational.GetAssistants(ctx, &aipb.ReqGetAssistants{
		Filter: &aipb.ReqGetAssistants_Filter{
			TeamId:      []uint64{me.FirmId},
			AssistantId: req.AssistantId,
			IsDraft:     basepb.BoolEnum_BOOL_ENUM_FALSE,
		},
		OrderBy: []*basepb.OrderBy{
			{Column: "update_date", Desc: true},
		},
		Relation: &aipb.ReqGetAssistants_Relation{
			Collection: true,
			LiveAgent:  true,
			Admin:      true,
			Allowlist:  true,
		},
		Page: &basepb.Paginator{
			Limit:  req.Limit,
			Offset: req.Offset,
		},
		WithTotalCount: true,
		VisibleScene:   scene,
	})
	if err != nil {
		return err
	}

	if err = ailogic.LoadAssistantsIdentityName(ctx, result.Assistants); err != nil {
		return err
	}

	rsp.Assistants = result.Assistants
	rsp.TotalCount = result.TotalCount

	return nil
}

// GetAssistantChunkConfig 获取助手分段配置
func (a Ai) GetAssistantChunkConfig(ctx context.Context, req *bffaipb.ReqGetAssistantChunkConfig, rsp *bffaipb.RspGetAssistantChunkConfig) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	var userIDs, teamIDs []uint64
	if logic.AsTeam(ctx) {
		teamIDs = append(teamIDs, me.FirmId)
	} else {
		userIDs = append(userIDs, me.Id)
	}

	assistants, err := client.AiNational.GetAssistants(ctx, &aipb.ReqGetAssistants{
		Filter: &aipb.ReqGetAssistants_Filter{
			UserId:         userIDs,
			TeamId:         teamIDs,
			CollectionLang: []string{req.CollectionLang},
			DocId:          req.DocId,
			IsDraft:        basepb.BoolEnum_BOOL_ENUM_FALSE,
			Enabled:        basepb.BoolEnum_BOOL_ENUM_TRUE,
		},
		Relation: &aipb.ReqGetAssistants_Relation{
			Collection: true,
		},
		OrderBy: []*basepb.OrderBy{
			{Column: "update_date", Desc: true},
		},
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		VisibleScene: aipb.ReqGetAssistants_VISIBLE_SCENE_IN_COLLECTION_MANAGE,
	})
	if err != nil {
		return err
	}

	rsp.Assistants = assistants.Assistants
	rsp.TotalCount = assistants.TotalCount

	return nil
}

// UpdateMyAssistant 更新我的助手
func (a Ai) UpdateMyAssistant(ctx context.Context, req *bffaipb.ReqUpdateMyAssistant, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.AiNational.BatchUpdateAssistant(ctx, &aipb.ReqBatchUpdateAssistant{
		Items: []*aipb.ReqBatchUpdateAssistant_Item{
			{
				AssistantId: req.AssistantId,
				Config:      req.Config,
				Mask:        req.Mask,
			},
		},
		UpdateBy: &basepb.Identity{
			IdentityId:   me.Id,
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_USER,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

// UpdateMyTeamAssistant 更新我团队的助手
func (a Ai) UpdateMyTeamAssistant(ctx context.Context, req *bffaipb.ReqUpdateMyAssistant, _ *emptypb.Empty) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	if me.FirmId == 0 {
		return xerrors.ForbiddenError("user has no team")
	}

	_, err := client.AiNational.BatchUpdateAssistant(ctx, &aipb.ReqBatchUpdateAssistant{
		Items: []*aipb.ReqBatchUpdateAssistant_Item{
			{
				AssistantId: req.AssistantId,
				Config:      req.Config,
				Mask:        req.Mask,
			},
		},
		UpdateBy: &basepb.Identity{
			IdentityId:   me.FirmId,
			IdentityType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
			ExtraId:      me.Id,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

// GetAssistantOptions 获取助手下拉选项
func (g Guest) GetAssistantOptions(ctx context.Context, _ *emptypb.Empty, rsp *bffaipb.RspGetAssistantOptions) error {
	result, err := client.AiNational.GetAssistantOptions(ctx, &emptypb.Empty{})
	if err != nil {
		return err
	}

	rsp.ChatModel = result.ChatModel
	rsp.ChatModelV2 = result.ChatModelV2
	rsp.ChatOrSqlModel = result.ChatOrSqlModel
	rsp.GraphParseMode = result.GraphParseMode
	rsp.SearchEngine = result.SearchEngine
	rsp.SearchEngineV2 = result.SearchEngineV2
	rsp.InteractiveCode = result.InteractiveCode
	rsp.VisibleChain = result.VisibleChain
	rsp.AskSuggestionModel = result.AskSuggestionModel
	rsp.EmbeddingModel = result.EmbeddingModel
	rsp.QuickActions = result.QuickActions
	rsp.MiniWhiteUrl = result.MiniWhiteUrl

	return nil
}

// GetAssistantConfig 获取助手配置
func (g Guest) GetAssistantConfig(ctx context.Context, req *bffaipb.ReqGetAssistantConfig, rsp *bffaipb.RspGetAssistantConfig) error {
	var (
		err             error
		assistantIDs    []uint64
		routePath       string
		enabled         basepb.BoolEnum
		tanliveAppCodes map[uint64]string
		appId           string
	)
	switch {
	case req.OnlyTanliveApp:
		enabled = basepb.BoolEnum_BOOL_ENUM_TRUE
		if assistantIDs, tanliveAppCodes, err = ailogic.GetTanliveAppConfig(); err != nil {
			return err
		}
	case req.RoutePath != "":
		enabled = basepb.BoolEnum_BOOL_ENUM_TRUE
		routePath = req.RoutePath
	case len(req.AssistantId) > 0:
		enabled = basepb.BoolEnum_BOOL_ENUM_UNSPECIFIED
		assistantIDs = req.AssistantId
	case len(req.AppId) > 0:
		enabled = basepb.BoolEnum_BOOL_ENUM_TRUE
		appId = req.AppId
	case req.OnlyTanliveMiniprogram:
		enabled = basepb.BoolEnum_BOOL_ENUM_TRUE
		if assistantIDs, err = ailogic.GetTanliveMiniprogramAssistantIDs(); err != nil {
			return err
		}
	default:
		return xerrors.ValidationError("invalid params")
	}

	result, err := client.AiNational.GetAssistants(ctx, &aipb.ReqGetAssistants{
		Filter: &aipb.ReqGetAssistants_Filter{
			AssistantId: assistantIDs,
			RoutePath:   routePath,
			AppId:       appId,
			Enabled:     enabled,
			IsDraft:     basepb.BoolEnum_BOOL_ENUM_FALSE,
		},
		Relation: &aipb.ReqGetAssistants_Relation{
			Collection: true,
			LiveAgent:  true,
			Admin:      true,
			Pipelines:  true,
		},
		VisibleScene: aipb.ReqGetAssistants_VISIBLE_SCENE_IN_WEB,
	})
	if err != nil {
		return err
	}

	// 填充UseRegionCode
	for _, assistant := range result.Assistants {
		if assistant.Assistant == nil || assistant.Assistant.Config == nil {
			continue
		}
		if regionCode, ok := tanliveAppCodes[assistant.Assistant.Id]; ok {
			assistant.Assistant.Config.UseRegionCode = regionCode
		}
	}

	// 加载admin卡片信息
	userCards, teamCards, err := ailogic.LoadAssistantAdminCard(ctx, result.Assistants)
	if err != nil {
		return err
	}

	rsp.Assistants = result.Assistants
	rsp.UserCards = userCards
	rsp.TeamCards = teamCards

	return nil
}

// GetAssistantsMiniprogram 获取小程序的助手列表
func (a Ai) GetAssistantsMiniprogram(ctx context.Context, req *bffaipb.ReqGetAssistantsMiniprogram,
	rsp *bffaipb.RspGetAssistantsMiniprogram) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	aids, err := ailogic.GetRecentlyUsedAssistantIds(ctx, user, aipb.ChatType_CHAT_TYPE_MINIPROGRAM)
	if err != nil {
		return err
	}
	// 如果是只查看最近使用的，则需要填充配置中的助手数据
	if req.OnlyRecentlyUse && len(rsp.Assistants) < int(req.Limit) {
		defaultIds, err := ailogic.GetTanliveMiniprogramAssistantIDs()
		if err != nil {
			return err
		}
		aids = append(aids, pkg.DifferenceSet(defaultIds, aids)...)
	}

	rpcReq := &aipb.ReqGetRecentlyUsedAssistants{
		UseAssistantIds: aids,
		Page:            &basepb.Paginator{Offset: req.Offset, Limit: req.Limit},
		Search:          req.Search,
		Channel:         aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM,
	}

	if !req.OnlyRecentlyUse { // 其他列表查询需要满足只在小程序助手列表显示的查询条件
		rpcReq.ShowInList = basepb.BoolEnum_BOOL_ENUM_TRUE
		userIds, teamIds, err := ailogic.GetAllUserAndTeamId(ctx, req.Search)
		if err != nil {
			return err
		}
		rpcReq.UserIds = userIds
		rpcReq.TeamIds = teamIds
	}

	rpcRsp, err := client.AiNational.GetRecentlyUsedAssistants(ctx, rpcReq)
	if err != nil {
		return err
	}

	// 加载admin卡片信息
	userCards, teamCards, err := ailogic.LoadAssistantAdminCard(ctx, rpcRsp.Assistants)
	if err != nil {
		return err
	}

	rsp.UserCards = userCards
	rsp.TeamCards = teamCards
	rsp.TotalCount = rpcRsp.TotalCount
	rsp.Assistants = rpcRsp.Assistants

	return nil
}
