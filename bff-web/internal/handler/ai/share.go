package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

// CreateAssistantShare 建知识库同步or取消至助手
func (a Ai) CreateAssistantShare(ctx context.Context, req *bffaipb.ReqCreateAssistantShare,
	rsp *bffaipb.RspCreateAssistantShare,
) error {
	return ailogic.CreateDocShare(ctx, req, rsp)
}

// ListAssistantCanShareDoc 查询可分享的助手列表
func (a Ai) ListAssistantCanShareDoc(ctx context.Context, req *bffaipb.ReqListAssistantCanShareDoc,
	rsp *bffaipb.RspListAssistantCanShareDoc,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	if req.Language == "" {
		req.Language = bff.RequestFromContext(ctx).Header.Get("Lang")
	}
	reqSender := &aipb.ReqListAssistantCanShareDoc{
		CreateBy:  user.Id,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_USER,
		DocId:     req.DocId,
		Name:      req.Name,
		Language:  req.Language,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	sharedAssistant, err := client.AiNational.ListAssistantCanShareDoc(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	for _, assistant := range sharedAssistant.Assistants {
		rsp.Assistants = append(rsp.Assistants, &bffaipb.RspListAssistantCanShareDoc_SharedAssistant{
			Id: assistant.Id, Name: assistant.Name,
			IsSelected: assistant.IsSelected, NameEn: assistant.NameEn,
		})
	}
	return nil
}

// CreateDocShareConfigSender 创建知识库接收者设置，知识分享小助手设置
func (a Ai) CreateDocShareConfigSender(ctx context.Context, req *bffaipb.ReqCreateDocShareConfigSender,
	rsp *bffaipb.RspCreateDocShareConfigSender,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqCreateDocShareConfigSender{
		ShareAssistantId: req.ShareAssistantId,
		ShareUserId:      req.ShareUserId,
		ShareTeamId:      req.ShareTeamId,
		UserId:           user.Id,
		AdminType:        basepb.IdentityType_IDENTITY_TYPE_USER,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.UserId = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}
	_, err := client.AiNational.CreateDocShareConfigSender(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// ListeDocShareConfigSender 查看创建知识库接收者设置
func (a Ai) ListeDocShareConfigSender(ctx context.Context, req *bffaipb.ReqListeDocShareConfigSender,
	rsp *bffaipb.RspListeDocShareConfigSender,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	if req.Language == "" {
		req.Language = bff.RequestFromContext(ctx).Header.Get("Lang")
	}
	reqSender := &aipb.ReqListeDocShareConfigSender{
		CreateBy:  user.Id,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_USER,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	assistants, err := ailogic.ListAssistantCanShareDoc(ctx, req.Name, req.Language)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	assistantSender, err := client.AiNational.ListeDocShareConfigSender(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	assistantSelectedMaps := make(map[uint64]bool)
	assistantRemoveMaps := make(map[uint64]bool)

	for _, assistant := range assistantSender.ShareAssistantId {
		assistantSelectedMaps[assistant] = true
	}
	for _, assistant := range assistantSender.MyAdminAssistantId {
		assistantRemoveMaps[assistant] = true
	}

	for _, assistant := range assistants.Assistants {
		if assistantRemoveMaps[assistant.Id] {
			continue
		}
		rsp.Assistants = append(rsp.Assistants, &bffaipb.RspListeDocShareConfigSender_SharedAssistant{
			Id: assistant.Id, Name: assistant.Name,
			IsSelected: assistantSelectedMaps[assistant.Id], NameEn: assistant.NameEn,
		})
	}

	var teamIds, userIds []uint64
	for _, v := range assistantSender.ShareTeamId {
		teamIds = append(teamIds, v)
	}
	for _, v := range assistantSender.ShareUserId {
		userIds = append(userIds, v)
	}

	if len(teamIds) != 0 {
		teams, err := iamlogic.FetchTeamName(ctx, teamIds)
		if err != nil {
			return err
		}
		for id, name := range teams {
			rsp.Teams = append(rsp.Teams, &bffaipb.RspListeDocShareConfigSender_SharedUserTeam{
				Id:         id,
				Name:       name,
				IsSelected: true,
			})
		}
	}
	if len(userIds) != 0 {
		users, err := iamlogic.FetchUserName(ctx, userIds)
		if err != nil {
			return err
		}
		for id, name := range users {
			rsp.Users = append(rsp.Users, &bffaipb.RspListeDocShareConfigSender_SharedUserTeam{
				Id:         id,
				Name:       name,
				IsSelected: true,
			})
		}
	}
	return nil
}

// CreateDocShareConfigReceiverAssistant 创建知识库发送者设置
func (a Ai) CreateDocShareConfigReceiverAssistant(ctx context.Context, req *bffaipb.ReqCreateDocShareConfigReceiverAssistant,
	rsp *bffaipb.RspCreateDocShareConfigReceiverAssistant,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqCreateDocShareConfigReceiverAssistant{
		CreateBy:      user.Id,
		AdminType:     basepb.IdentityType_IDENTITY_TYPE_USER,
		AssistantId:   req.AssistantId,
		ReceiverState: req.ReceiverState,
		OtherState:    req.OtherState,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	var userShareData []*aipb.DocReceiveConfigBySender
	for _, userShare := range req.UserShares {
		u := &aipb.DocReceiveConfigBySender{
			UserId: userShare.UserId,
			TeamId: userShare.TeamId,
			State:  userShare.State,
		}
		userShareData = append(userShareData, u)
	}

	reqSender.UserShares = userShareData

	_, err := client.AiNational.CreateDocShareConfigReceiverAssistant(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// ListDocShareConfigReceiverAssistant 查看创建知识库发送者设置
func (a Ai) ListDocShareConfigReceiverAssistant(ctx context.Context, req *bffaipb.ReqListDocShareConfigReceiverAssistant,
	rsp *bffaipb.RspListDocShareConfigReceiverAssistant,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqListDocShareConfigReceiverAssistant{
		AssistantId: req.AssistantId,
		CreateBy:    user.Id,
		AdminType:   basepb.IdentityType_IDENTITY_TYPE_USER,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	assistantReceiver, err := client.AiNational.ListDocShareConfigReceiverAssistant(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	if assistantReceiver == nil {
		return nil
	}

	rsp.AssistantId = assistantReceiver.AssistantId
	rsp.ReceiverState = assistantReceiver.ReceiverState
	rsp.OtherState = assistantReceiver.OtherState

	userInfos, teamInfos, err := ailogic.ListDocShareConfigReceiverAssistantUserTeam(ctx, assistantReceiver)
	if err != nil {
		return err
	}
	for _, share := range assistantReceiver.UserShares {

		var uss []*bffaipb.RspListDocShareConfigReceiverAssistant_Members
		if len(share.UserId) > 0 {
			for _, u := range share.UserId {
				us := &bffaipb.RspListDocShareConfigReceiverAssistant_Members{}
				us.Id = u
				us.Name = userInfos[u]
				uss = append(uss, us)
			}
		}

		var teams []*bffaipb.RspListDocShareConfigReceiverAssistant_Members
		if len(share.TeamId) > 0 {
			for _, u := range share.TeamId {
				team := &bffaipb.RspListDocShareConfigReceiverAssistant_Members{}
				team.Id = u
				team.Name = teamInfos[u]
				teams = append(teams, team)
			}
		}

		userShare := &bffaipb.RspListDocShareConfigReceiverAssistant_UserShare{}
		userShare.State = share.State
		userShare.GroupId = share.GroupId

		userShare.Teams = teams
		userShare.Users = uss

		rsp.UserShares = append(rsp.UserShares, userShare)

	}

	return nil
}

// ListMyAssistantIds 查询已经设置并开启知识库接收的助手
func (a Ai) ListMyAssistantIds(ctx context.Context, req *bffaipb.ReqListMyAssistantIds,
	rsp *bffaipb.RspListMyAssistantIds,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqListMyAssistantIds{
		CreateBy:  user.Id,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_USER,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	myAssistantIds, err := client.AiNational.ListMyAssistantIds(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	rsp.ShareAssistantIds = append(rsp.ShareAssistantIds, myAssistantIds.ShareAssistantIds...)

	return nil
}

// CreateDocShareConfigReceiverUserTeam 创建个人/团队接收方设置(黑名单)
func (a Ai) CreateDocShareConfigReceiverUserTeam(ctx context.Context, req *bffaipb.ReqCreateDocShareConfigReceiverUserTeam,
	rsp *bffaipb.RspCreateDocShareConfigReceiverUserTeam,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqCreateDocShareConfigReceiverUserTeam{
		CreateBy:      user.Id,
		AdminType:     basepb.IdentityType_IDENTITY_TYPE_USER,
		ReceiverState: aipb.DocShareAcceptState_DOC_SHARE_ACCEPT_STATE_ENABLED,
		OtherState:    aipb.DocShareState_DOC_SHARE_STATE_DISABLED,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	var userShareData []*aipb.DocReceiveConfigBySender
	if len(req.TeamId) != 0 {
		userShareData = append(userShareData, &aipb.DocReceiveConfigBySender{
			TeamId: req.TeamId,
			State:  aipb.DocShareState_DOC_SHARE_STATE_NOT_ACCEPTED,
		})
	}
	if len(req.UserId) != 0 {
		userShareData = append(userShareData, &aipb.DocReceiveConfigBySender{
			UserId: req.UserId,
			State:  aipb.DocShareState_DOC_SHARE_STATE_NOT_ACCEPTED,
		})
	}

	reqSender.UserShares = userShareData

	_, err := client.AiNational.CreateDocShareConfigReceiverUserTeam(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// ListDocShareConfigReceiverUserTeam 查询个人/团队接收方设置(黑名单)
func (a Ai) ListDocShareConfigReceiverUserTeam(ctx context.Context, req *bffaipb.ReqListDocShareConfigReceiverUserTeam,
	rsp *bffaipb.RspListDocShareConfigReceiverUserTeam,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqListDocShareConfigReceiverUserTeam{
		CreateBy:  user.Id,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_USER,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	userTeamReceiver, err := client.AiNational.ListDocShareConfigReceiverUserTeam(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	if userTeamReceiver == nil {
		return nil
	}

	userInfos, teamInfos, err := ailogic.ListDocShareConfigReceiverUserTeamUserTeam(ctx, userTeamReceiver)
	if err != nil {
		return err
	}

	for _, share := range userTeamReceiver.UserShares {
		var uss []*bffaipb.RspListDocShareConfigReceiverUserTeam_Members
		if len(share.UserId) > 0 {
			for _, u := range share.UserId {
				us := &bffaipb.RspListDocShareConfigReceiverUserTeam_Members{}
				us.Id = u
				us.Name = userInfos[u]
				uss = append(uss, us)
			}
		}

		var teams []*bffaipb.RspListDocShareConfigReceiverUserTeam_Members
		if len(share.TeamId) > 0 {
			for _, u := range share.TeamId {
				team := &bffaipb.RspListDocShareConfigReceiverUserTeam_Members{}
				team.Id = u
				team.Name = teamInfos[u]
				teams = append(teams, team)
			}
		}
		rsp.Users = append(rsp.Users, uss...)
		rsp.Teams = append(rsp.Teams, teams...)
	}

	return nil
}

// ListTeamCanShareDoc 查询可分享的团队列表
func (a Ai) ListTeamCanShareDoc(ctx context.Context, req *bffaipb.ReqListTeamCanShareDoc,
	rsp *bffaipb.RspListTeamCanShareDoc,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqListTeamCanShareDoc{
		CreateBy:  user.Id,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_USER,
		Name:      req.Name,
		Offset:    req.Offset,
		Limit:     req.Limit,
		DocId:     req.DocId,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	teamList, err := client.AiNational.ListTeamCanShareDoc(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	for _, team := range teamList.Teams {
		rsp.Teams = append(rsp.Teams, &bffaipb.RspListTeamCanShareDoc_Teams{
			Id:         team.Id,
			Name:       team.Name,
			NameEn:     team.NameEn,
			IsSelected: team.IsSelected,
		})
	}
	return nil
}

// ListUserCanShareDoc 查询可分享的个人列表
func (a Ai) ListUserCanShareDoc(ctx context.Context, req *bffaipb.ReqListUserCanShareDoc,
	rsp *bffaipb.RspListUserCanShareDoc,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	userToShare, err := iamlogic.GetUserIdByName(ctx, req.Name)
	if err != nil {
		return err
	}
	// 没有找到用户
	if userToShare == 0 {
		return nil
	}
	reqSender := &aipb.ReqListUserCanShareDoc{
		CreateBy:  user.Id,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_USER,
		UserId:    userToShare,
		DocId:     req.DocId,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.CreateBy = user.FirmId
		reqSender.AdminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
	}

	userList, err := client.AiNational.ListUserCanShareDoc(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	for _, user := range userList.Users {
		rsp.Users = append(rsp.Users, &bffaipb.RspListUserCanShareDoc_Users{
			Id:         user.Id,
			Name:       req.Name, // 因为是名称全匹配，直接用请求的 name 即可
			IsSelected: user.IsSelected,
		})
	}
	return nil
}
