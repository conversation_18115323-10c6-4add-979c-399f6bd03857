package ai

import (
	"context"
	"encoding/json"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	logic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	"github.com/golang/protobuf/ptypes/empty"
)

// CreateQaExportTask 创建qa导出任务
func (a *Ai) CreateQaExportTask(ctx context.Context, req *bffaipb.ReqCreateQaExportTask, rsp *bffaipb.RspExportTask) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	var task *aipb.ExportTask
	var err error
	if task, err = logic.CreateExportTask(ctx, user.Id, aipb.ExportTaskType_EXPORT_TASK_TYPE_QA, req.Fields, req.Filter); err != nil {
		return err
	}
	rsp.TaskId = task.Id

	logic.ExportTaskFactory(ctx, req, func(ctx context.Context, req *bffaipb.ReqCreateQaExportTask, rsp *bffaipb.RspListQA) error {
		return a.ListQA(ctx, req.Filter, rsp)
	}, task)
	return nil
}

// CreateFileExportTask 创建fileText导出任务
func (a *Ai) CreateFileExportTask(ctx context.Context, req *bffaipb.ReqCreateFileExportTask, rsp *bffaipb.RspExportTask) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	var task *aipb.ExportTask
	var err error
	if task, err = logic.CreateExportTask(ctx, user.Id, aipb.ExportTaskType_EXPORT_TASK_TYPE_FILE, req.Fields, req.Filter); err != nil {
		return err
	}
	rsp.TaskId = task.Id

	logic.ExportTaskFactory(ctx, req, func(ctx context.Context, req *bffaipb.ReqCreateFileExportTask, rsp *bffaipb.RspListTextFiles) error {
		return logic.ListTextFiles(ctx, req.Filter, rsp, false)
	}, task)
	return nil
}

// DescribeExportTasks 查询导出任务列表
func (a *Ai) DescribeExportTasks(ctx context.Context, req *bffaipb.ReqDescribeExportTasks, rsp *bffaipb.RspDescribeExportTasks) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	res, err := client.AiNational.DescribeExportTasks(ctx, &aipb.ReqDescribeExportTasks{
		UserId:        user.Id,
		Type:          req.Type,
		OperationType: req.OperationType,
	})
	if err != nil {
		return err
	}
	rsp.Tasks = res.Tasks
	return nil
}

// CreateChatExportTask 创建会话导出任务
func (a *Ai) CreateChatExportTask(ctx context.Context, req *bffaipb.ReqCreateChatExportTask, rsp *bffaipb.RspExportTask) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	var task *aipb.ExportTask
	var err error
	if task, err = logic.CreateExportTask(ctx, user.Id, aipb.ExportTaskType_EXPORT_TASK_TYPE_CHAT, req.Fields, req.Filter); err != nil {
		return err
	}
	rsp.TaskId = task.Id

	logic.ExportTaskFactory(ctx, req, func(ctx context.Context, req *bffaipb.ReqCreateChatExportTask, rsp *bffaipb.RspListChat) error {
		return a.ListChat(ctx, req.Filter, rsp)
	}, task)
	return nil
}

// CreateChatMessageExportTask 创建会话消息导出任务
func (a *Ai) CreateChatMessageExportTask(ctx context.Context, req *bffaipb.ReqCreateChatMessageExportTask, rsp *bffaipb.RspExportTask) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	var task *aipb.ExportTask
	var err error
	if task, err = logic.CreateExportTask(ctx, user.Id, aipb.ExportTaskType_EXPORT_TASK_TYPE_MESSAGE, req.Fields, req.Filter); err != nil {
		return err
	}
	rsp.TaskId = task.Id
	logic.ExportTaskFactory(ctx, req, func(ctx context.Context, req *bffaipb.ReqCreateChatMessageExportTask, rsp *bffaipb.RspListChat) error {
		return a.ListChat(ctx, req.Filter, rsp)
	}, task)
	return nil
}

// RestartExportTask 重试导出任务
func (a *Ai) RestartExportTask(ctx context.Context, req *bffaipb.ReqRestartExportTask, _ *empty.Empty) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	var err error
	tasks, err := client.AiNational.DescribeExportTasks(ctx, &aipb.ReqDescribeExportTasks{
		Ids:    []uint64{req.TaskId},
		UserId: user.Id,
	})
	if err != nil || len(tasks.Tasks) == 0 {
		return xerrors.NotFoundError("failed to describe export tasks")
	}
	task := tasks.Tasks[0]

	unmarshalSnapshots := func(filterSnapshotStr, fieldsSnapshotStr string, filter interface{}, fields *[]*bffaipb.ExportField) error {
		if err := json.Unmarshal([]byte(filterSnapshotStr), filter); err != nil {
			return xerrors.InternalServerError("failed to unmarshal filter snapshot")
		}
		if err := json.Unmarshal([]byte(fieldsSnapshotStr), fields); err != nil {
			return xerrors.InternalServerError("failed to unmarshal fields snapshot")
		}
		return nil
	}

	switch task.Type {
	case aipb.ExportTaskType_EXPORT_TASK_TYPE_FILE:
		filterSnapshot := &bffaipb.ReqListTextFiles{}
		var fieldsSnapshot []*bffaipb.ExportField
		err := unmarshalSnapshots(task.FilterSnapshot, task.FieldsSnapshot, filterSnapshot, &fieldsSnapshot)
		if err != nil {
			return err
		}
		logic.ExportTaskFactory(ctx, &bffaipb.ReqCreateFileExportTask{
			Filter: filterSnapshot,
			Fields: fieldsSnapshot,
		}, func(ctx context.Context, req *bffaipb.ReqCreateFileExportTask, rsp *bffaipb.RspListTextFiles) error {
			return logic.ListTextFiles(ctx, req.Filter, rsp, false)
		}, task)
	case aipb.ExportTaskType_EXPORT_TASK_TYPE_QA:
		filterSnapshot := &bffaipb.ReqListQA{}
		var fieldsSnapshot []*bffaipb.ExportField
		err := unmarshalSnapshots(task.FilterSnapshot, task.FieldsSnapshot, filterSnapshot, &fieldsSnapshot)
		if err != nil {
			return err
		}
		logic.ExportTaskFactory(ctx, &bffaipb.ReqCreateQaExportTask{
			Filter: filterSnapshot,
			Fields: fieldsSnapshot,
		}, func(ctx context.Context, req *bffaipb.ReqCreateQaExportTask, rsp *bffaipb.RspListQA) error {
			return a.ListQA(ctx, req.Filter, rsp)
		}, task)
	case aipb.ExportTaskType_EXPORT_TASK_TYPE_CHAT:
		filterSnapshot := &bffaipb.ReqListChat{}
		var fieldsSnapshot []*bffaipb.ExportField
		err := unmarshalSnapshots(task.FilterSnapshot, task.FieldsSnapshot, filterSnapshot, &fieldsSnapshot)
		if err != nil {
			return err
		}
		logic.ExportTaskFactory(ctx, &bffaipb.ReqCreateChatExportTask{
			Filter: filterSnapshot,
			Fields: fieldsSnapshot,
		}, func(ctx context.Context, req *bffaipb.ReqCreateChatExportTask, rsp *bffaipb.RspListChat) error {
			return a.ListChat(ctx, req.Filter, rsp)
		}, task)
	case aipb.ExportTaskType_EXPORT_TASK_TYPE_MESSAGE:
		filterSnapshot := &bffaipb.ReqListChat{}
		var fieldsSnapshot []*bffaipb.ExportField
		err := unmarshalSnapshots(task.FilterSnapshot, task.FieldsSnapshot, filterSnapshot, &fieldsSnapshot)
		if err != nil {
			return err
		}
		logic.ExportTaskFactory(ctx, &bffaipb.ReqCreateChatMessageExportTask{
			Filter: filterSnapshot,
			Fields: fieldsSnapshot,
		}, func(ctx context.Context, req *bffaipb.ReqCreateChatMessageExportTask, rsp *bffaipb.RspListChat) error {
			return a.ListChat(ctx, req.Filter, rsp)
		}, task)
	}
	return nil
}
