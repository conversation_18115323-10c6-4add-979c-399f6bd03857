package ai

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	logic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mclient "github.com/asim/go-micro/v3/client"
	"google.golang.org/protobuf/types/known/emptypb"
)

var DocRpcTimeOut = func() time.Duration {
	d, err := time.ParseDuration(config.GetStringOr("llm.collection.external.timeout", "2m"))
	if err == nil {
		return d
	}
	return time.Second * 60 * 2
}

// CreateTencentDocAuthUrl 创建腾讯文档授权链接
func (a Ai) CreateTencentDocAuthUrl(ctx context.Context, req *bffaipb.ReqCreateTencentDocAuthUrl,
	rsp *bffaipb.RspCreateTencentDocAuthUrl,
) error {
	rspData, err := client.AiNational.CreateTencentDocAuthUrl(ctx, &aipb.ReqCreateTencentDocAuthUrl{})
	if err != nil {
		return err
	}

	rsp.Url = rspData.Url

	return nil
}

// DescribeDocList 获取腾讯文档列表
func (a Ai) DescribeDocList(ctx context.Context, req *bffaipb.ReqDescribeDocList, rsp *bffaipb.RspDescribeDocList) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqDescribeTencentDocList{
		UserId:          user.Id,
		FolderId:        req.FolderId,
		Search:          req.Search,
		Start:           req.Start,
		State:           req.State,
		AccountId:       user.Id,
		AdminType:       uint32(basepb.IdentityType_IDENTITY_TYPE_USER),
		EncryptedOpenid: req.HashUserId,
	}

	if iamlogic.IsTeamUser(ctx) {
		reqSender.AccountId = user.FirmId
		reqSender.AdminType = uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM)
	}

	list, err := client.AiNational.DescribeDocList(ctx, reqSender, mclient.WithRequestTimeout(DocRpcTimeOut()))
	if err != nil {
		return err
	}

	for _, doc := range list.Docs {
		rsp.Docs = append(rsp.Docs, &bffaipb.TencentDoc{
			Url:            doc.Url,
			Title:          doc.Title,
			FileName:       doc.FileName,
			FileId:         doc.FileId,
			FileCreateUser: doc.FileCreateUser,
			FileOwnerName:  doc.FileOwnerName,
			FileBrowseTime: doc.FileBrowseTime,
			FileUrl:        doc.FileUrl,
			FileModifyTime: doc.FileModifyTime,
			FileType:       doc.FileType,
			FileCreateTime: doc.FileCreateTime,
		})
	}

	rsp.Next = list.Next

	return nil
}

// ImportTencentDoc 导入腾讯文档
func (a Ai) ImportTencentDoc(ctx context.Context, req *bffaipb.ReqImportTencentDoc, rsp *bffaipb.RspImportTencentDoc) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	customLabelTenantId, err := logic.GetCustomLabelTenantId(ctx)
	if err != nil {
		return err
	}
	reqSender := &aipb.ReqImportTencentDoc{
		UserId:              user.Id,
		FileIds:             req.FileIds,
		AccountId:           user.Id,
		AdminType:           uint32(basepb.IdentityType_IDENTITY_TYPE_USER),
		EncryptedOpenid:     req.HashUserId,
		FilePath:            req.Path,
		Language:            bff.RequestFromContext(ctx).Header.Get("Lang"),
		CustomLabelTenantId: customLabelTenantId,
	}
	if reqSender.Language == "" {
		reqSender.Language = "zh"
	}
	if iamlogic.IsTeamUser(ctx) {
		reqSender.AccountId = user.FirmId
		reqSender.AdminType = uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM)
	}

	rspData, err := client.AiNational.ImportTencentDoc(ctx, reqSender)
	if err != nil {
		return err
	}

	rsp.Uuid = rspData.Uuid

	return nil
}

// ReimportTencentDoc 重新导入腾讯文档
func (a Ai) ReimportTencentDoc(ctx context.Context, req *bffaipb.ReqReimportTencentDoc, rsp *bffaipb.RspReimportTencentDoc) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	err := ailogic.CheckDocEditPermission(ctx, req.DocIds...)
	if err != nil {
		return err
	}

	reqSender := &aipb.ReqReimportTencentDoc{
		DocIds:    req.DocIds,
		AccountId: user.Id,
		UserId:    user.Id,
		AdminType: uint32(basepb.IdentityType_IDENTITY_TYPE_USER),
	}
	if iamlogic.IsTeamUser(ctx) {
		reqSender.AccountId = user.FirmId
		reqSender.AdminType = uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM)
	}

	rspData, err := client.AiNational.ReimportTencentDoc(ctx, reqSender)
	if err != nil {
		return err
	}
	for _, fail := range rspData.Failed {
		// 不返回腾讯文档的用户id
		fail.User.UserId = ""
		rsp.Failed = append(rsp.Failed, &bffaipb.RspReimportTencentDoc_FailInfo{
			DocId:    fail.DocId,
			FileName: fail.FileName,
			User:     fail.User,
		})
	}

	return nil
}

// ModifyDocTab 修改文档标签
func (a Ai) ModifyDocTab(ctx context.Context, req *bffaipb.ReqModifyDocTab, rsp *bffaipb.RspModifyDocTab) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqModifyDocTab{
		Id:        req.Id,
		Name:      req.Name,
		Type:      req.Type,
		AccountId: user.Id,
		AdminType: uint32(basepb.IdentityType_IDENTITY_TYPE_USER),
	}
	if iamlogic.IsTeamUser(ctx) {
		reqSender.AccountId = user.FirmId
		reqSender.AdminType = uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM)
	}

	_, err := client.AiNational.ModifyDocTab(ctx, reqSender)
	if err != nil {
		return err
	}

	return nil
}

// DescribeDocTab 获取文档标签
func (a Ai) DescribeDocTab(ctx context.Context, req *bffaipb.ReqDescribeDocTab, rsp *bffaipb.RspDescribeDocTab) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqDescribeDocTab{
		AccountId: user.Id,
		AdminType: uint32(basepb.IdentityType_IDENTITY_TYPE_USER),
	}
	if iamlogic.IsTeamUser(ctx) {
		reqSender.AccountId = user.FirmId
		reqSender.AdminType = uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM)
	}

	rspData, err := client.AiNational.DescribeDocTab(ctx, reqSender)
	if err != nil {
		return err
	}

	for _, docExternalTab := range rspData.Tabs {
		rsp.Tabs = append(rsp.Tabs, &bffaipb.RspDescribeDocTab_DocTab{
			Id:         docExternalTab.Id,
			Name:       docExternalTab.Name,
			Type:       docExternalTab.Type,
			HasExpired: docExternalTab.HasExpired,
			IsShow:     docExternalTab.IsShow,
		})
	}

	return nil
}

// CreateGTBDocText 创建GTB文档
func (a Ai) CreateGTBDocText(ctx context.Context, req *bffaipb.ReqCreateGTBDocText, rsp *bffaipb.RspCreateGTBDocText) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqCreateGTBText{
		IsPullAll: req.IsPullAll,
		AccountId: user.Id,
		UserId:    user.Id,
		AdminType: uint32(basepb.IdentityType_IDENTITY_TYPE_USER),
	}
	if iamlogic.IsTeamUser(ctx) {
		reqSender.AccountId = user.FirmId
		reqSender.AdminType = uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM)
	}

	rspData, err := client.AiNational.CreateGTBText(ctx, reqSender)
	if err != nil {
		return err
	}

	rsp.Uuid = rspData.Uuid
	return nil
}

// DescribeAccountIsGTB 查询是否为绿技行用户
func (a Ai) DescribeAccountIsGTB(ctx context.Context, req *bffaipb.ReqDescribeAccountIsGTB, rsp *bffaipb.RspDescribeAccountIsGTB) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	if iamlogic.IsTeamUser(ctx) {
		rsp.IsGtb = user.FirmId == config.GetUint64("llm.collection.external.gtb_team_id")
	}

	return nil
}

// AuthTencentCode 获取腾讯文档授权码
func (a Ai) AuthTencentCode(ctx context.Context, req *bffaipb.ReqAuthTencentCode,
	rsp *bffaipb.RspAuthTencentCode,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	_, err := client.AiNational.AuthTencentCode(ctx, &aipb.ReqAuthTencentCode{
		UserId: user.Id, Path: req.Path, Code: req.Code,
	})
	if err != nil {
		return xerrors.InternalServerError("code err")
	}

	return nil
}

// DescribeTencentToken 查询腾讯文档token是否为空
func (a Ai) DescribeTencentToken(ctx context.Context, req *bffaipb.ReqDescribeTencentToken,
	rsp *bffaipb.RspDescribeTencentToken,
) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	token, err := client.AiNational.DescribeTokenIsEmpty(ctx, &aipb.ReqDescribeTokenIsEmpty{UserId: user.Id, EncryptedOpenid: req.HashUserId})
	if err != nil {
		return err
	}

	rsp.IsEmpty = token.IsEmpty

	return nil
}

// DescribeMyDoc 查询我的文档
func (a Ai) DescribeMyDoc(ctx context.Context, req *bffaipb.ReqDescribeMyDoc, rsp *bffaipb.RspDescribeMyDoc) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	reqSender := &aipb.ReqDescribeMyDoc{
		AccountId: user.Id,
		AdminType: uint32(basepb.IdentityType_IDENTITY_TYPE_USER),
	}
	if iamlogic.IsTeamUser(ctx) {
		reqSender.AccountId = user.FirmId
		reqSender.AdminType = uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM)
	}

	rspData, err := client.AiNational.DescribeMyDoc(ctx, reqSender)
	if err != nil {
		return err
	}

	rsp.FileIds = rspData.FileIds
	return nil
}

// DescribeTencentDocTask 查询腾讯文档任务
func (a Ai) DescribeTencentDocTask(ctx context.Context, req *bffaipb.ReqDescribeTencentDocTask, rsp *bffaipb.RspDescribeTencentDocTask) error {
	task, err := client.AiNational.DescribeTencentDocTask(ctx, &aipb.ReqDescribeTencentDocTask{
		UserId: xsession.UserFromContext[iampb.UserInfo](ctx).Id,
	})
	if err != nil {
		return err
	}

	rsp.IsRunning = task.IsRunning

	return nil
}

// DelTencentDocAuth 删除腾讯文档授权
func (a Ai) DelTencentDocAuth(ctx context.Context, req *bffaipb.ReqDelTencentDocAuth, rsp *emptypb.Empty) error {
	_, err := client.AiNational.DelTencentDocAuth(ctx, &aipb.ReqDelTencentDocAuth{
		UserId:          xsession.UserFromContext[iampb.UserInfo](ctx).Id,
		EncryptedOpenid: req.HashUserId,
	})
	if err != nil {
		return err
	}

	return nil
}

// ListExternalSourceUser 查询腾讯文档授权列表
func (a Ai) ListExternalSourceUser(ctx context.Context, req *bffaipb.ReqListExternalSourceUser, rsp *bffaipb.RspListExternalSourceUser) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	users, err := client.AiNational.ListExternalSourceUser(ctx, &aipb.ReqListExternalSourceUser{
		UserId: user.Id,
	})
	if err != nil {
		return err
	}
	for _, v := range users.Users {
		rsp.Users = append(rsp.Users, &bffaipb.ExternalSourceUser{
			HashUserId: v.UserId,
			Nickname:   v.Nickname,
			Avatar:     v.Avatar,
			AuthState:  v.AuthState,
			AuthSource: v.AuthSource,
		})
	}
	return nil
}

// ImportTencentDocWebClip 导入腾讯文档网页剪辑
func (a Ai) ImportTencentDocWebClip(ctx context.Context, req *bffaipb.ReqImportTencentDocWebClip, rsp *bffaipb.RspImportTencentDocWebClip) error {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	customLabelTenantId, err := logic.GetCustomLabelTenantId(ctx)
	if err != nil {
		return err
	}
	reqSender := &aipb.ReqImportTencentDocWebClip{
		AfterTime:           req.AfterTime,
		EncryptedOpenid:     req.HashUserId,
		AccountId:           user.Id,
		UserId:              user.Id,
		AdminType:           uint32(basepb.IdentityType_IDENTITY_TYPE_USER),
		CustomLabelTenantId: customLabelTenantId,
		Language:            bff.RequestFromContext(ctx).Header.Get("Lang"),
	}
	if reqSender.Language == "" {
		reqSender.Language = "zh"
	}
	if iamlogic.IsTeamUser(ctx) {
		reqSender.AccountId = user.FirmId
		reqSender.AdminType = uint32(basepb.IdentityType_IDENTITY_TYPE_TEAM)
	}

	rspData, err := client.AiNational.ImportTencentDocWebClip(ctx, reqSender, mclient.WithRequestTimeout(DocRpcTimeOut()))
	if err != nil {
		return err
	}

	for _, doc := range rspData.Docs {
		// 暂时只返回 id和文件名
		rsp.Docs = append(rsp.Docs, &bffaipb.TencentDoc{
			// 文件名
			FileName: doc.FileName,
			// 文件id
			FileId: doc.FileId,
		})
	}

	return nil
}
