package notify

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp/response"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	logic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai/aware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/whatsapp"
)

// HandleWhatsappCallbackHandler 处理whatsapp回调事件处理，处理逻辑
func HandleWhatsappCallbackHandler(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleWhatsappCallbackHandler ReadAll err", "err", err)
		return response.Text("")
	}

	log.WithContext(ctx).Infow("Successfully receive message", "body", string(body))

	var msgContent whatsapp.WebhookPayload
	if err = json.Unmarshal(body, &msgContent); err != nil {
		log.WithContext(ctx).Errorw("HandleWhatsappCallbackHandler Unmarshal err", "err", err)
		return response.Text("")
	}

	log.WithContext(ctx).Infow("Successfully data", "msgContent", msgContent)

	// todo From 谁发送的，即要回复给谁； body是用户询问的内容
	fmt.Println(msgContent.Data.From, msgContent.Data.Body)

	return response.Text("")
}

// HandleWhatsappBusinessCallbackHandler 处理whatsapp商业号回调事件处理，处理逻辑
func HandleWhatsappBusinessCallbackHandler(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleWhatsappBusinessCallbackHandler ReadAll err", "err", err)
		return response.Text("")
	}

	log.WithContext(ctx).Infow("Successfully receive message", "body", string(body))

	// 解析 JSON 数据
	var event whatsapp.WebhookEvent
	err = json.Unmarshal(body, &event)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleWhatsappBusinessCallbackHandler Unmarshal err", "err", err)
		return response.Text("")
	}

	msg := event.WhatsappInboundMessage
	switch msg.Type {
	case "interactive":
		if msg.Interactive != nil && msg.Interactive.ButtonReply != nil {
			//todo msg.Interactive.ButtonReply.ID 按钮id， msg.From 谁发送的，即要回复给谁
			fmt.Println(msg.Interactive.ButtonReply.ID, msg.From)
		}
	case "text":
		if msg.Text != nil {
			//todo msg.Text.Body 用户发送的内容， msg.From 谁发送的，即要回复给谁
			fmt.Println(msg.Text.Body, msg.From)
		}
	case "audio":
		fmt.Println(msg.Audio.Link)
	case "request_welcome":
		fmt.Println(msg.From)
	default:
		log.WithContext(ctx).Warnw("HandleWhatsappBusinessCallbackHandler Unknown message type", "type", msg.Type)
	}
	xsync.SafeGo(context.Background(), func(c context.Context) error {
		aware.DoMessageLogic[whatsapp.WhatsappInboundMessage](c,
			&logic.WhatsappAIMessage{}, []whatsapp.WhatsappInboundMessage{msg})
		return nil
	}, boot.TraceGo(ctx))

	return response.Text("")
}
