package notify

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp/response"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	notifylogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/notify"
	bffnotifypb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/notify"
	notifypb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify"
)

// HandleTcloudSmsCallback 处理腾讯云短信回调
func HandleTcloudSmsCallback(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	l := notifylogic.TcloudSmsCallbackLogic{}
	statuses, err := l.ParseStatuses(req)
	if err != nil {
		log.WithContext(ctx).Errorw("parse tcloud sms statuses", "err", err)
		return l.ResponseTcloudSmsCallbackError(1, err.Error())
	}

	if err = l.UpdateStatuses(ctx, statuses); err != nil {
		log.WithContext(ctx).Errorw("update tcloud sms statuses", "err", err)
		return l.ResponseTcloudSmsCallbackError(2, "internal server error")
	}

	return l.ResponseTcloudSmsCallbackOK()
}

// HandleTcloudSesCallback 处理腾讯云邮件回调
func HandleTcloudSesCallback(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	l := notifylogic.TcloudSesCallbackLogic{}
	event, err := l.ParseEvent(req)
	if err != nil {
		log.WithContext(ctx).Errorw("parse tcloud ses event", "err", err)
		return response.Text("")
	}

	if err = l.UpdateEvent(ctx, event); err != nil {
		log.WithContext(ctx).Errorw("update tcloud ses event", "err", err)
		return response.Text("")
	}

	return response.Text("")
}

// GetSmsSendStatus 查询短信发送状态
func (g Guest) GetSmsSendStatus(ctx context.Context,
	req *bffnotifypb.ReqGetSmsSendStatus, rsp *bffnotifypb.RspGetSmsSendStatus) error {
	status, err := client.NotifyClient.GetTcloudSmsStatus(ctx, &notifypb.ReqGetTcloudSmsStatus{
		Sid: req.TaskNo,
	})
	if err != nil {
		return err
	}

	rsp.ReportStatus = status.Status.ReportStatus
	rsp.Errmsg = status.Status.Errmsg
	return nil
}

// GetEmailSendStatus 查询邮件发送状态
func (g Guest) GetEmailSendStatus(ctx context.Context,
	req *bffnotifypb.ReqGetEmailSendStatus, rsp *bffnotifypb.RspGetEmailSendStatus) error {
	event, err := client.NotifyClient.GetTcloudSesEvent(ctx, &notifypb.ReqGetTcloudSesEvent{
		BulkId: req.TaskNo,
	})
	if err != nil {
		return err
	}

	rsp.Event = event.Event.Event
	rsp.Timestamp = event.Event.Timestamp
	return nil
}
