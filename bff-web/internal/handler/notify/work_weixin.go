package notify

import (
	"context"
	"encoding/xml"
	"io"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp/response"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	logic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai/aware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"github.com/sbzhu/weworkapi_golang/wxbizmsgcrypt"
)

// MsgContent 企业微信回调消息体
type MsgContent struct {
	ToUsername      string `xml:"ToUserName"`
	FromUsername    string `xml:"FromUserName"`
	CreateTime      uint32 `xml:"CreateTime"`
	MsgType         string `xml:"MsgType"`
	Content         string `xml:"Content"`
	Msgid           string `xml:"MsgId"`
	Agentid         uint32 `xml:"AgentId"`
	Token           string `xml:"Token"`
	OpenKfId        string `xml:"OpenKfId"`
	Event           string `xml:"Event"`
	AuthAddOpenKfId string `xml:"AuthAddOpenKfId"`
	AuthDelOpenKfId string `xml:"AuthDelOpenKfId"`
}

func initWx(token string, encodingAeskey string, receiverId string) *wxbizmsgcrypt.WXBizMsgCrypt {

	return wxbizmsgcrypt.NewWXBizMsgCrypt(token, encodingAeskey, receiverId, wxbizmsgcrypt.XmlType)
}

// HandleWecomCallback 处理企业微信回调check
func HandleWecomCallback(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	verifyMsgSign := req.Query("msg_signature")
	verifyTimestamp := req.Query("timestamp")
	verifyNonce := req.Query("nonce")
	verifyEchoStr := req.Query("echostr")

	echoStr, err := initWx(config.GetString("weixin.token"), config.GetString("weixin.encodingAeskey"), config.GetString("weixin.receiverId")).
		VerifyURL(verifyMsgSign.String(), verifyTimestamp.String(),
			verifyNonce.String(), verifyEchoStr.String())
	if err != nil {
		log.WithContext(ctx).Errorw("Failed to verify URL", "err", err)
		return response.Text("Failed to verify URL")
	}

	return response.Text(string(echoStr))
}

// HandleWecomCallbackHandler 处理企业微信回调事件处理，处理逻辑
func HandleWecomCallbackHandler(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleWecomCallbackHandler ReadAll err", "err", err)
		return response.Text("")
	}

	verifyMsgSign := req.Query("msg_signature")
	verifyTimestamp := req.Query("timestamp")
	verifyNonce := req.Query("nonce")

	msg, cryptErr := initWx(config.GetString("weixin.token"), config.GetString("weixin.encodingAeskey"), config.GetString("weixin.receiverId")).
		DecryptMsg(verifyMsgSign.String(), verifyTimestamp.String(), verifyNonce.String(), body)
	if cryptErr != nil {
		log.WithContext(ctx).Errorw("HandleWecomCallbackHandler DecryptMsg err", "err", err)
		return response.Text("")
	}

	log.WithContext(ctx).Infow("Successfully decrypted message", "body", string(body))

	var msgContent MsgContent
	if err = xml.Unmarshal(msg, &msgContent); err != nil {
		log.WithContext(ctx).Errorw("HandleWecomCallbackHandler Unmarshal err", "err", err)
		return response.Text("")
	}

	log.WithContext(ctx).Infow("HandleWecomCallbackHandler Successfully data", "msgContent", msgContent)

	err = logic.ReceiveUserChatMessageSync(ctx, msgContent.ToUsername, msgContent.OpenKfId, msgContent.Token)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleWecomCallbackHandler ReceiveUserChatMessage err", "err", err)
	}
	return response.Text("")
}

// HandleWecomCallback 处理企业微信回调check
func HandleWecomDataCallback(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	verifyMsgSign := req.Query("msg_signature")
	verifyTimestamp := req.Query("timestamp")
	verifyNonce := req.Query("nonce")
	verifyEchoStr := req.Query("echostr")

	echoStr, err := initWx(config.GetString("weixin.open.token"), config.GetString("weixin.open.encodingAeskey"), config.GetString("weixin.open.receiverId")).
		VerifyURL(verifyMsgSign.String(), verifyTimestamp.String(),
			verifyNonce.String(), verifyEchoStr.String())
	if err != nil {
		log.WithContext(ctx).Errorw("Failed to verify URL", "err", err)
		return response.Text("Failed to verify URL")
	}

	return response.Text(string(echoStr))
}

// HandleWecomDataCallbackHandler 处理企业微信回调check-用于接收托管企业微信应用的用户消息
func HandleWecomDataCallbackHandler(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleWecomDataCallbackHandler ReadAll err", "err", err)
		return response.Text("")
	}

	verifyMsgSign := req.Query("msg_signature")
	verifyTimestamp := req.Query("timestamp")
	verifyNonce := req.Query("nonce")

	msg, cryptErr := initWx(config.GetString("weixin.open.token"), config.GetString("weixin.open.encodingAeskey"), config.GetString("weixin.open.receiverId")).
		DecryptMsg(verifyMsgSign.String(), verifyTimestamp.String(), verifyNonce.String(), body)
	if cryptErr != nil {
		log.WithContext(ctx).Errorw("HandleWecomDataCallbackHandler DecryptMsg err", "err", err)
		return response.Text("")
	}

	log.WithContext(ctx).Infow("HandleWecomDataCallbackHandler Successfully decrypted message", "msg", string(msg))

	var msgContent MsgContent
	if err = xml.Unmarshal(msg, &msgContent); err != nil {
		log.WithContext(ctx).Errorw("HandleWecomDataCallbackHandler Unmarshal err", "err", err)
		return response.Text("")
	}

	// 企微服务商客服授权事件
	if msgContent.Event == "kf_account_auth_change" {
		err = aware.CreateOpenKeIdAction(ctx, msgContent.ToUsername, msgContent.AuthAddOpenKfId, msgContent.AuthDelOpenKfId)
		if err != nil {
			log.WithContext(ctx).Errorw("HandleWecomDataCallbackHandler CreateOpenKeIdAction err", "err", err)
		}
		return response.Text("")
	}

	err = logic.ReceiveUserChatMessageSync(ctx, msgContent.ToUsername, msgContent.OpenKfId, msgContent.Token)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleWecomDataCallbackHandler ReceiveUserChatMessage err", "err", err)
	}
	return response.Text("")
}

// TicketContent 企业微信回调消息体
type TicketContent struct {
	ToUserName  string `xml:"ToUserName"`
	AuthCode    string `xml:"AuthCode"`
	TimeStamp   string `xml:"TimeStamp"`
	SuiteTicket string `xml:"SuiteTicket"`
}

// HandleWecomTicketCallbackHandler 处理企业微信回调check-系统将会把此应用的授权变更事件以及ticket参数推送给此URL
func HandleWecomTicketCallbackHandler(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()

	body, err := io.ReadAll(req.Request.Body)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleWecomTicketCallbackHandler ReadAll err", "err", err)
		return response.Text("")
	}

	verifyMsgSign := req.Query("msg_signature")
	verifyTimestamp := req.Query("timestamp")
	verifyNonce := req.Query("nonce")

	msg, cryptErr := initWx(config.GetString("weixin.open.token"), config.GetString("weixin.open.encodingAeskey"), config.GetString("weixin.open.receiverId")).
		DecryptMsg(verifyMsgSign.String(), verifyTimestamp.String(), verifyNonce.String(), body)
	if cryptErr != nil {
		log.WithContext(ctx).Errorw("HandleWecomTicketCallbackHandler DecryptMsg err", "err", err)
		return response.Text("")
	}

	log.WithContext(ctx).Infow("HandleWecomTicketCallbackHandler Successfully decrypted message", "msg", string(msg))

	var ticketContent TicketContent
	if err = xml.Unmarshal(msg, &ticketContent); err != nil {
		log.WithContext(ctx).Errorw("HandleWecomTicketCallbackHandler Unmarshal err", "err", err)
		return response.Text("")
	}

	// 正常企微响应，此部分协程处理
	ctx = context.Background()
	xsync.SafeGo(ctx, func(ctx context.Context) error {
		err = aware.FetchEnterpriseAuthCode(ctx, ticketContent.AuthCode, ticketContent.SuiteTicket)
		if err != nil {
			log.WithContext(ctx).Errorw("HandleWecomTicketCallbackHandler FetchEnterpriseAuthCode err", "err", err)
		}
		return nil
	}, boot.TraceGo(ctx))

	return response.Text("success")
}
