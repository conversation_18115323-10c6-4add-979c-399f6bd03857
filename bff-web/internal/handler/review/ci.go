package review

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	reviewpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	"google.golang.org/protobuf/encoding/protojson"
)

var unmarshaler = protojson.UnmarshalOptions{
	DiscardUnknown: true,
}

// HandleTcloudCosCiCallback 处理数据万象审核回调
func HandleTcloudCosCiCallback(req *xhttp.Request) xhttp.Response {
	ctx := req.Context()
	body, err := req.ReadBody()
	if err != nil {
		log.WithContext(ctx).Errorw("HandleTcloudCosCiCallback: read http body", "err", err)
		return xhttp.NewResponse()
	}

	rpcReq := &reviewpb.ReqHandleCiReviewCallback{}
	if err = unmarshaler.Unmarshal(body, rpcReq); err != nil {
		log.WithContext(ctx).Errorw("HandleTcloudCosCiCallback: unmarshal http body", "err", err)
		return xhttp.NewResponse()
	}

	_, err = client.ReviewClient.HandleCiReviewCallback(ctx, rpcReq)
	if err != nil {
		log.WithContext(ctx).Errorw("HandleTcloudCosCiCallback: call HandleCiReviewCallback", "err", err)
		return xhttp.NewResponse()
	}

	return xhttp.NewResponse()
}
