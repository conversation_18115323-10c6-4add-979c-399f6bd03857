package handler

import (
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/middleware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot/bffmiddleware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot/httpmiddleware"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
)

var (
	// 加密cookie
	encryptCookies = &httpmiddleware.EncryptCookies{}

	// 启动会话
	startSession = &httpmiddleware.StartSession{
		CookieName: "tes-jwttoken",
		GetSessionOptions: func(req *xhttp.Request) []xsession.Option {
			userAgent := req.Header.Get("Tanlive-Agent")
			userAgent = strings.ToLower(userAgent)

			if strings.Contains(userAgent, "miniprogram") {
				ttl := config.GetDurationOr("miniprogram.ttl", time.Hour*24*30)
				return []xsession.Option{
					xsession.WithTTL(ttl),
				}
			}

			return nil
		},
	}

	// 校验CSRF令牌
	verifyCsrfToken = &bffmiddleware.VerifyCsrfToken{
		Rule: func(req *xhttp.Request) bool {
			return false
		},
		AddToCookie: true,
	}

	// 验证码限流
	captchaRateLimit = &middleware.RateLimit{
		MaxAttempts: 20,
	}

	// 错误响应本地化
	translateErrorResponse = &bffmiddleware.TranslateErrorResponse{
		MessageMap:        errorspb.GetErrorNamesMap(),
		LanguageHeaderKey: "Lang",
	}

	// 解码请求的hashids
	decodeRequestHashIds = &middleware.DecodeRequestHashIds{}

	// 编码响应的hashids
	encodeResponseHashIds = &middleware.EncodeResponseHashIds{}

	// 认证
	authenticate = &middleware.Authenticate{}

	// 游客
	guest = &middleware.Authenticate{AllowGuest: true}

	// 鉴权
	authorize = &middleware.Authorize{}

	// 维护模式检测
	maintenance = &httpmiddleware.Maintenance{}
)

// 定义中间件顺序
var middlewareSort = []xhttp.Middleware{
	(*httpmiddleware.TraceHttp)(nil),
	(*httpmiddleware.LogRequest)(nil),
	(*middleware.EncodeResponseHashIds)(nil),
	(*bffmiddleware.TranslateErrorResponse)(nil),
	(*httpmiddleware.Maintenance)(nil),
	(*middleware.RateLimit)(nil),
	(*httpmiddleware.EncryptCookies)(nil),
	(*httpmiddleware.StartSession)(nil),
	(*bffmiddleware.VerifyCsrfToken)(nil),
	(*middleware.DecodeRequestHashIds)(nil),
	(*middleware.Authenticate)(nil),
	(*middleware.Authorize)(nil),
}
