package cmd

import (
	"context"
	"fmt"
	"os"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai/aware"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/whatsapp"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	mclient "github.com/asim/go-micro/v3/client"
)

// 把历史的会话消息导入发送记录表
func importChatMessageToSendRecord() {
	if !config.GetBool("tanlive.cmd.IMPORTCHATMESSAGETOSENDRECORD") {
		return
	}
	abroad := config.GetBool("tanlive.cmd.IMPORTCHATMESSAGETOSENDRECORDABROAD")
	s := setClient(abroad, context.Background())
	s.startImport()

	defer os.Exit(0)
}

type sendRecord struct {
	aiClient     aipb.AiService
	ctx          context.Context
	abroad       bool
	maybeWantAsk string
	wechat       *ailogic.WechatAIMessage
	whatsapp     *ailogic.WhatsappAIMessage
}

func setClient(abroad bool, ctx context.Context) *sendRecord {
	s := &sendRecord{
		ctx:    ctx,
		abroad: abroad,
	}
	if abroad { // 国外的没有建议问题
		s.aiClient = client.AiInternational
	} else {
		s.aiClient = client.AiNational
		s.maybeWantAsk = config.GetStringOr("workweixin.ai.suggestAnswerText", "你可能还想问：")
	}
	return s
}

func (s *sendRecord) startImport() {
	assistantRsp, err := client.AiNational.ListAssistant(s.ctx, &aipb.ReqListAssistant{Order: &base.OrderBy{
		Column: "id",
		Desc:   false,
	}}, mclient.WithRequestTimeout(time.Minute))
	if err != nil {
		fmt.Printf("fail to get assistants: %s\n", err.Error())
		return
	}
	if !s.abroad {
		s.wechat = &ailogic.WechatAIMessage{
			MessageAIConfig: aware.MessageAIConfig[workweixin.Message]{
				HelpCenterUrl:           config.GetString("workweixin.ai.helpCenterUrl"),
				CosPublicCdnUrl:         config.GetString("oss.publicCdn"),
				UgcJumpUrl:              config.GetString("workweixin.ai.ugcJumpUrl"),
				UgcAnswerTemplate:       config.GetString("workweixin.ai.ugcAnswerTemplate"),
				ReadMore:                config.GetStringOr("workweixin.ai.readMore", "查看更多"),
				AnswerFrom:              config.GetStringOr("workweixin.ai.answerFrom", "\n\n答案整理自 "),
				ReferenceAnswerTemplate: config.GetString("workweixin.ai.referenceAnswerTemplate"),
				ReferenceDataFrom:       config.GetStringOr("workweixin.ai.referenceDataFrom", "\n参考资料\n"),
			},
		}
	} else {
		s.whatsapp = &ailogic.WhatsappAIMessage{
			MessageAIConfig: aware.MessageAIConfig[whatsapp.WhatsappInboundMessage]{
				HelpCenterUrl:     config.GetString("whatsapp.ai.helpCenterUrl"),
				CosPublicCdnUrl:   config.GetString("oss.publicCdn"),
				UgcJumpUrl:        config.GetString("whatsapp.ai.ugcJumpUrl"),
				UgcAnswerTemplate: config.GetString("whatsapp.ai.ugcAnswerTemplate"),
				ReadMore:          config.GetStringOr("whatsapp.ai.readMore", "View more"),
				AnswerFrom:        config.GetStringOr("whatsapp.ai.answerFrom", "\n\nAnswers compiled by "),

				ReferenceAnswerTemplate: config.GetString("whatsapp.ai.referenceAnswerTemplate"),
				ReferenceDataFrom:       config.GetStringOr("whatsapp.ai.referenceDataFrom", "\nReference\n"),
			},
		}
	}
	for i, v := range assistantRsp.Assistants {
		fmt.Printf("开始迁移第%d个助手:%s\n", i+1, v.Name)
		s.start(v.Id)
		fmt.Printf("结束迁移第%d个助手:%s\n\n\n", i+1, v.Name)
	}
}

func (s *sendRecord) start(aId uint64) {
	assistantRsp, err := client.AiNational.GetAssistant(s.ctx, &aipb.ReqGetAssistant{Id: aId}, mclient.WithRequestTimeout(time.Minute))
	if err != nil {
		fmt.Printf("fail to get assistant detail: %d,err:%s\n", aId, err.Error())
		return
	} else if assistantRsp == nil || assistantRsp.AssistantDetail == nil || assistantRsp.AssistantDetail.Id == 0 {
		fmt.Printf("fail to get assistant detail: %d,err:%s\n", aId, err.Error())
		return
	}

	var (
		offset uint32 = 0
		limit  uint32 = 100
	)
	var chatType aipb.ChatType
	if s.abroad {
		chatType = aipb.ChatType_CHAT_TYPE_WHATSAPP
	} else {
		chatType = aipb.ChatType_CHAT_TYPE_WECHAT
	}

	for i := 0; i < 200; i++ {
		fmt.Printf("start assistant_id : %d,index:%d\n", aId, i)
		messageRsp, err := s.aiClient.DescribeAssistantMessage(s.ctx, &aipb.ReqDescribeAssistantMessage{
			AssistantId: aId,
			Page: &base.Paginator{
				Offset: offset,
				Limit:  limit,
			},
			ChatType: chatType,
		}, mclient.WithRequestTimeout(time.Minute))
		if err != nil {
			fmt.Printf("fail to get message : %d,err:%s\n", aId, err.Error())
			return
		}
		if len(messageRsp.Message) == 0 {
			fmt.Printf("assistant:%d is finished\n\n", aId)
			return
		}

		docs, err := ailogic.DescribeMessagesDocs(s.ctx, messageRsp.Message, false) // 获取docs
		if err != nil {
			fmt.Printf("fail to get docs : %d,err:%s\n", aId, err.Error())
			return
		}

		var records []*aipb.AiChatSendRecord
		if !s.abroad {
			for _, v := range messageRsp.Message {
				s.wechat.SetAssistant(assistantRsp.AssistantDetail)

				records = append(records, prepareMessage(s.wechat.PrepareSearchMessageByWechat,
					s.wechat.PrepareUgcMessageByWechat, v, docs, s.maybeWantAsk)...)
			}
		} else {
			for _, v := range messageRsp.Message {
				s.whatsapp.SetAssistant(assistantRsp.AssistantDetail)

				records = append(records, prepareMessage(s.whatsapp.PrepareSearchMessage,
					s.whatsapp.PrepareUgcMessage, v, docs, s.maybeWantAsk)...)
			}
		}

		_, err = s.aiClient.InsertAssistantMessageRecord(s.ctx, &aipb.ReqInsertAssistantMessageRecord{
			Records: records,
		}, mclient.WithRequestTimeout(time.Minute))
		if err != nil {
			fmt.Printf("fail to insert record : %d,err:%s\n", aId, err.Error())
		}

		if len(messageRsp.Message) < int(limit) {
			return
		}
		offset += limit
	}
}

func prepareMessage(search, ugc func(*aipb.EventChatHashMessage) string, msg *aipb.ChatMessage, docs []*aipb.ChatMessageDoc,
	maybeWantAsk string) []*aipb.AiChatSendRecord {
	eventMessage := ailogic.TransformAIMessageToEventMessage(msg, docs) // 获取docs的贡献者
	message := ailogic.EventMessageTransformWithHash(eventMessage)      // 所有id转为hashID

	result := &aipb.AiChatSendRecord{
		//Uuid: // todo uuid 用sql更新
		ChatId:      msg.ChatId,
		MessageId:   msg.Id,
		State:       2, // 直接标记为已发送
		CreateDate:  msg.CreateDate,
		SendDate:    msg.CreateDate,
		SendType:    0, // 不追加继续回答
		Content:     msg.Text,
		Type:        aipb.AiRecordType_AI_RECORD_TYPE_USER,
		MessageType: aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL,
	}
	if len(msg.ImageUrl) > 0 {
		result.Content = msg.ImageUrl[0]
	}
	if msg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_USER ||
		msg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_TIMEOUT_ERROR ||
		msg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SYSTEM_ERROR ||
		msg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_AUDIT_ERROR ||
		msg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_LIVE_AGENT ||
		msg.Type == aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SET_CHART_ANSWER {
		return []*aipb.AiChatSendRecord{result} // 不需要去拼装回答
	}

	switch message.Type {
	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION, aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SEARCH,
		aipb.ChatMessageType_CHAT_MESSAGE_TYPE_VISION: // 文本和doc link
		result.Content = search(message)
		result.Type = aipb.AiRecordType_AI_RECORD_TYPE_AI_MENU
	case aipb.ChatMessageType_CHAT_MESSAGE_TYPE_SQL_QUERY: // 返回UGC
		result.Content = ugc(message)
		result.Type = aipb.AiRecordType_AI_RECORD_TYPE_AI_TEXT
	default:
		result.Content = message.Text
		result.Type = aipb.AiRecordType_AI_RECORD_TYPE_AI_TEXT
	}

	if msg.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_DEFAULT_HIDE_SET {
		result.MessageType = aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER
	}

	if len(msg.SuggestQuestion) == 0 || len(maybeWantAsk) == 0 {
		return []*aipb.AiChatSendRecord{result}
	}

	return []*aipb.AiChatSendRecord{result, {
		//Uuid: // todo uuid 用sql更新
		ChatId:      msg.ChatId,
		MessageId:   msg.Id,
		State:       2, // 直接标记为已发送
		CreateDate:  msg.CreateDate,
		SendDate:    msg.CreateDate,
		SendType:    0, // 不追加继续回答
		Content:     maybeWantAsk,
		Type:        aipb.AiRecordType_AI_RECORD_TYPE_SUGGEST_AI_MENU,
		MessageType: aipb.AiRecordMessageType_AI_RECORD_MESSAGE_TYPE_NORMAL,
	}}
}
