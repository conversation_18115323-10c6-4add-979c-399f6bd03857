package bootstrap

import (
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/metric"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/miniprogram"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/ratelimiter"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/asr"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/captcha"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/whatsapp"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	supportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support"
)

// Boot 启动
func Boot(configPath string, bootstraps ...boot.BootstrapFunc) *boot.Bootstrapper {
	b := &boot.Bootstrapper{
		ConfigFile: configPath,
		AppBootstraps: []boot.BootstrapFunc{
			captcha.BootCaptcha,
			ratelimiter.BootRateLimiter,
			workweixin.BootWorkWeiXin,
			whatsapp.BootBusinessWhatsapp,
			miniprogram.BootMiniProgram,
			hashids.BootHashids,
			xcos.BootCos,
			asr.BootAsr,
		},
		AfterStart: []boot.Hook{
			boot.SimpleHook(metric.RegisterService(func() supportpb.SupportService {
				return client.SupportClient
			})),
		},
	}

	if err := b.Boot(bootstraps...); err != nil {
		panic(err)
	}

	return b
}
