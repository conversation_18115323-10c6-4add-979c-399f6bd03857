package middleware

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

// Authorize 鉴权
type Authorize struct {
}

// Handle 包装器
func (a *Authorize) Handle(req *xhttp.Request, next xhttp.Handler) xhttp.Response {
	ctx := req.Context()
	user := xsession.UserFromContext[iampb.UserInfo](ctx)

	result, err := client.IamClient.CheckApiPermission(ctx, &iampb.ReqCheckApiPermission{
		UserId: user.Id,
		Path:   req.URL.Path,
		AsTeam: req.Header.Get("As-Team") == "true",
	})
	if err != nil {
		return bff.Response(err)
	}
	if !result.Allowed {
		return bff.Response(xerrors.ForbiddenError("operation unauthorized"))
	}

	return next.Handle(req)
}
