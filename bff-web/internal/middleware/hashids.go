package middleware

import (
	"bytes"
	"io"

	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"github.com/tidwall/gjson"
)

// DecodeRequestHashIds 解码请求的hashids
type DecodeRequestHashIds struct {
}

// Handle ...
func (m *DecodeRequestHashIds) Handle(req *xhttp.Request, next xhttp.Handler) xhttp.Response {
	reqBody, err := req.ReadBody()
	if err == nil {
		decoded := gjson.GetBytes(reqBody, "@decodeHashids").String()
		req.Body = io.NopCloser(bytes.NewReader(xstrings.ToBytes(decoded)))
	}

	return next.Handle(req)
}

// EncodeResponseHashIds 编码响应的hashids
type EncodeResponseHashIds struct {
}

// Handle ...
func (m *EncodeResponseHashIds) Handle(req *xhttp.Request, next xhttp.Handler) xhttp.Response {
	rsp := next.Handle(req)

	rspBody, err := rsp.MarshalBody(rsp.GetBody())
	if err == nil {
		encoded := gjson.GetBytes(rspBody, "@encodeHashids").String()
		rsp.SetBody(xhttp.RawBody(xstrings.ToBytes(encoded)))
	}

	return rsp
}
