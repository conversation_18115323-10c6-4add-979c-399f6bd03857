package middleware

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

var UnauthorizedError = xerrors.UnauthorizedError("unauthenticated")

// Authenticate 认证
type Authenticate struct {
	// AllowGuest 是否允许游客访问。为true时，如果session未设置LoginUser就不做认证
	AllowGuest bool
}

// Handle ...
func (a *Authenticate) Handle(req *xhttp.Request, next xhttp.Handler) xhttp.Response {
	ctx := req.Context()

	session := xsession.SessionFromContext(ctx)
	userID := session.GetLoginUser().Uint64()

	if !a.AllowGuest || userID > 0 {
		if userID == 0 {
			return bff.Response(UnauthorizedError)
		}

		users, err := client.IamClient.GetUsersByKey(req.Context(), &iampb.ReqGetUsersByKey{
			Id: []uint64{userID},
		})
		if err != nil {
			return bff.Response(err)
		}
		if len(users.UserSet) == 0 {
			return bff.Response(UnauthorizedError)
		}

		user := users.UserSet[0]
		ctx = xsession.UserToContext(ctx, user)
		req.Request = req.Request.WithContext(ctx)
	}

	return next.Handle(req)
}
