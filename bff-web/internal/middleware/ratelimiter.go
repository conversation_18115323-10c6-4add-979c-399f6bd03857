package middleware

import (
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/ratelimiter"
)

// RateLimit 限流
type RateLimit struct {
	MaxAttempts int
	FormatKey   func(*xhttp.Request) string
}

// Handle ...
func (r *RateLimit) Handle(req *xhttp.Request, next xhttp.Handler) xhttp.Response {
	if r.MaxAttempts <= 0 {
		return next.Handle(req)
	}

	key := r.getKey(req)
	result, err := ratelimiter.AttemptPerMinute(req.Context(), key, r.MaxAttempts)
	if err != nil {
		return bff.Response(err)
	}
	if result.Allowed == 0 {
		rsp := bff.Response(xerrors.TooManyRequestsError("too many requests"))
		result.ApplyToHttpHeader(rsp.GetHeader())
		return rsp
	}

	return next.Handle(req)
}

func (r *RateLimit) getKey(req *xhttp.Request) string {
	if r.FormatKey != nil {
		return r.FormatKey(req)
	}
	return "ratelimit:" + req.Header.Get("X-Real-Ip")
}
