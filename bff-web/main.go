package main

import (
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/bootstrap"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/cmd"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler"
	_ "e.coding.net/tencent-ssv/tanlive/services/pkg/validation"
)

func main() {
	b := bootstrap.Boot("./etc/bff-web.toml")
	svc := b.NewBFFService()

	client.RegisterClient(svc)
	handler.RegisterBFFHandler(svc)
	cmd.RegisterCmd(svc)
	if err := svc.Run(); err != nil {
		panic(err)
	}
}
