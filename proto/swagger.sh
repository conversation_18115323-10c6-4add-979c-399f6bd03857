#!/bin/bash

SWAGGER_MIXIN="swagger-mixin"

do_merge() {
  local directory="$1"
  local out="$2"
  local files=""

  # 检查目录是否存在
  if [ ! -d "$directory" ]; then
    echo "Error: Directory '$directory' does not exist."
    return 1
  fi

  # 使用find命令递归搜索.json文件，并将文件路径拼接起来
  files=$(find "$directory" -type f -name "*.swagger.json" -print0 | xargs -0 -I {} echo "{} ")

  # 去除路径字符串开头的空格
  files="${files#"${files%%[![:space:]]*}"}"

  # 如果找到了文件，则执行swagger-mixin命令
  if [[ -n "$files" ]]; then
    "$SWAGGER_MIXIN" -c 100 $files > $out 2> /dev/null || true
  else
    echo "No .json files found in $directory or its subdirectories."
  fi
}

do_merge "docs/tanlive/bff-mgmt" "docs/bff-mgmt.swagger.json"
do_merge "docs/tanlive/bff-openapi" "docs/bff-openapi.swagger.json"
do_merge "docs/tanlive/bff-web" "docs/bff-web.swagger.json"
