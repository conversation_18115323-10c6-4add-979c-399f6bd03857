scopes:
    - ./tanlive/ai
    - ./tanlive/cms
    - ./tanlive/graph
    - ./tanlive/iam
    - ./tanlive/mgmt
    - ./tanlive/notify
    - ./tanlive/product
    - ./tanlive/resource
    - ./tanlive/review
    - ./tanlive/search
    - ./tanlive/support
    - ./tanlive/tag
    - ./tanlive/team
protoc: v25.0
protocWorkDir: ""
plugins:
    protoc-gen-go: google.golang.org/protobuf/cmd/protoc-gen-go@v1.31.0
    protoc-gen-micro: github.com/asim/go-micro/cmd/protoc-gen-micro/v3@v3.7.0
    protoc-gen-tanlive: e.coding.net/tencent-ssv/tanlive/gokits/cmd/protoc-gen-tanlive@v0.2.2
repositories:
    TANLIVE_GOKITS: https://e.coding.net/tencent-ssv/tanlive/gokits@cad705040efd5e104c1d37be4d3d9f9c4bf0bae1
options:
    - --go_out=paths=source_relative:./
    - --micro_out=paths=source_relative:./
    - --tanlive_out=paths=source_relative:./
importPaths:
    - .
    - $POWERPROTO_INCLUDE
    - $TANLIVE_GOKITS/e.coding.net/tencent-ssv/tanlive/gokits/proto
postActions:
    - name: remove
      args:
          - ./tanlive/ai/ai.pb.micro.go
          - ./tanlive/cms/cms.pb.micro.go
          - ./tanlive/graph/graph.pb.micro.go
          - ./tanlive/iam/iam.pb.micro.go
          - ./tanlive/mgmt/mgmt.pb.micro.go
          - ./tanlive/notify/notify.pb.micro.go
          - ./tanlive/product/product.pb.micro.go
          - ./tanlive/resource/resource.pb.micro.go
          - ./tanlive/review/review.pb.micro.go
          - ./tanlive/search/search.pb.micro.go
          - ./tanlive/support/support.pb.micro.go
          - ./tanlive/tag/tag.pb.micro.go
          - ./tanlive/team/team.pb.micro.go
postShell: ""

---
scopes:
    - ./tanlive/bff-mgmt
    - ./tanlive/bff-openapi
    - ./tanlive/bff-web
protoc: v25.0
protocWorkDir: ""
plugins:
    protoc-gen-go: google.golang.org/protobuf/cmd/protoc-gen-go@v1.31.0
    protoc-gen-openapiv2: github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2@v2.18.1
    protoc-gen-tanlive: e.coding.net/tencent-ssv/tanlive/gokits/cmd/protoc-gen-tanlive@v0.2.2
repositories:
    GOOGLE_APIS: https://github.com/googleapis/googleapis@db5d55026cd5f3d1b91665aa46afd4ba4456ac9f
    TANLIVE_GOKITS: https://e.coding.net/tencent-ssv/tanlive/gokits@cad705040efd5e104c1d37be4d3d9f9c4bf0bae1
options:
    - --go_out=paths=source_relative:./
    - --tanlive_out=paths=source_relative:./
    - --openapiv2_out=json_names_for_fields=false,enums_as_ints=true,omit_enum_default_value=true:docs
importPaths:
    - .
    - $POWERPROTO_INCLUDE
    - $GOOGLE_APIS/github.com/googleapis/googleapis
    - $TANLIVE_GOKITS/e.coding.net/tencent-ssv/tanlive/gokits/proto
postActions: []
postShell: ""

---
scopes:
    - ./tanlive/cron
    - ./tanlive/errors
protoc: v25.0
protocWorkDir: ""
plugins:
    protoc-gen-go: google.golang.org/protobuf/cmd/protoc-gen-go@v1.31.0
repositories: {}
options:
    - --go_out=paths=source_relative:./
importPaths:
    - .
    - $POWERPROTO_INCLUDE
postActions: []
postShell: ""

---
scopes:
    - ./tanlive/base
    - ./tanlive/events
protoc: v25.0
protocWorkDir: ""
plugins:
    protoc-gen-go: google.golang.org/protobuf/cmd/protoc-gen-go@v1.31.0
    protoc-gen-tanlive: e.coding.net/tencent-ssv/tanlive/gokits/cmd/protoc-gen-tanlive@v0.2.2
repositories:
    TANLIVE_GOKITS: https://e.coding.net/tencent-ssv/tanlive/gokits@cad705040efd5e104c1d37be4d3d9f9c4bf0bae1
options:
    - --go_out=paths=source_relative:./
    - --tanlive_out=paths=source_relative:./
importPaths:
    - .
    - $POWERPROTO_INCLUDE
    - $TANLIVE_GOKITS/e.coding.net/tencent-ssv/tanlive/gokits/proto
postActions: []
postShell: ""
