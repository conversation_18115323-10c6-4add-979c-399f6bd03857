#!/usr/bin/env bash
#set -x

LINK="****************:tencent-ssv/tanlive/gokits.git"
PLUGINS="cad705040efd5e104c1d37be4d3d9f9c4bf0bae1"
POWERPROTO_HOME=${HOME}/.build-tool/powerproto
GITS="$POWERPROTO_HOME/gits/"

for i in $PLUGINS;
do
  mkdir -p $GITS && cd $GITS
  if [ ! -d "$GITS/$i" ]; then
    git clone -q $LINK "$i/e.coding.net/tencent-ssv/tanlive/gokits" && \
      cd "$i/e.coding.net/tencent-ssv/tanlive/gokits" && \
      git reset --hard $i;
  fi
done
