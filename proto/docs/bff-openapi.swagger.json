{"consumes": ["application/json"], "produces": ["application/json"], "swagger": "2.0", "info": {"title": "tanlive/bff-openapi/ai/bff.proto", "version": "version not set"}, "paths": {}, "definitions": {"aiRspUploadDocs": {"type": "object", "title": "请求示例（需删除）", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}, "message": {"type": "string"}}}}, "tags": [{"name": "AiCapi"}]}