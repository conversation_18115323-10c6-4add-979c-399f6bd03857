{"swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/iam/bff.proto", "version": "version not set"}, "tags": [{"name": "IamBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/iam/disable_user": {"post": {"summary": "冻结用户", "operationId": "IamBff_DisableUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspDisableUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqDisableUser"}}], "tags": ["IamBff"]}}, "/iam/enable_user": {"post": {"summary": "解冻用户", "operationId": "IamBff_EnableUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspEnableUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqEnableUser"}}], "tags": ["IamBff"]}}}, "definitions": {"iamReqDisableUser": {"type": "object", "properties": {"user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "用户ID"}}}, "iamReqEnableUser": {"type": "object", "properties": {"user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "用户ID"}}}, "iamRspDisableUser": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamRspDisableUserResult"}}}}, "iamRspDisableUserResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}}}, "iamRspEnableUser": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamRspEnableUserResult"}}}}, "iamRspEnableUserResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}