{"swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/graph/bff.proto", "version": "version not set"}, "tags": [{"name": "GraphBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/graph/create_process_engine": {"post": {"summary": "流程引擎添加", "operationId": "GraphBff_CreateProcessEngine", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqCreateProcessEngine"}}], "tags": ["GraphBff"]}}, "/graph/delete_process_engine": {"post": {"summary": "流程引擎删除", "operationId": "GraphBff_DeleteProcessEngine", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqDeleteProcessEngine"}}], "tags": ["GraphBff"]}}, "/graph/describe_process_list": {"post": {"summary": "流程配置列表", "operationId": "GraphBff_DescribeListProcess", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/graphRspDescribeListProcess"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqDescribeListProcess"}}], "tags": ["GraphBff"]}}, "/graph/modify_enable_version": {"post": {"summary": "版本启用", "operationId": "GraphBff_ModifyEnableVersion", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqModifyEnableVersion"}}], "tags": ["GraphBff"]}}, "/graph/modify_process_engine": {"post": {"summary": "流程引擎编辑", "operationId": "GraphBff_ModifyProcessEngine", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqModifyProcessEngine"}}], "tags": ["GraphBff"]}}}, "definitions": {"graphProcessInfo": {"type": "object", "properties": {"process_name": {"type": "string", "title": "流程名称"}, "category": {"type": "integer", "format": "int64", "title": "流程类别 1图谱 2资源 3团队 4产品"}, "remark": {"type": "string", "title": "备注"}, "creator": {"type": "string", "title": "创建人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "update_date": {"type": "string", "format": "date-time", "title": "修改时间"}, "id": {"type": "string", "format": "uint64"}, "update_user": {"type": "string"}, "yaml_config": {"type": "string"}, "is_online_version": {"type": "integer", "format": "int64"}, "lang": {"type": "integer", "format": "int64"}}}, "graphReqCreateProcessEngine": {"type": "object", "properties": {"process_name": {"type": "string", "title": "流程名称"}, "category": {"type": "integer", "format": "int64", "title": "流程类别 1图谱 2资源 3团队 4产品"}, "yaml_config": {"type": "string", "title": "YAML配置"}, "remark": {"type": "string", "title": "备注"}, "lang": {"type": "integer", "format": "int64"}}, "title": "流程引擎添加请求"}, "graphReqDeleteProcessEngine": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "graphReqDescribeListProcess": {"type": "object", "properties": {"search_name": {"type": "string", "title": "搜索名称"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string", "format": "uint64"}}, "title": "流程配置列表请求"}, "graphReqModifyEnableVersion": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_online_version": {"type": "boolean", "title": "1启用 2禁用"}}}, "graphReqModifyProcessEngine": {"type": "object", "properties": {"process_name": {"type": "string", "title": "流程名称"}, "category": {"type": "integer", "format": "int64", "title": "流程类别"}, "yaml_config": {"type": "string", "title": "YAML配置"}, "remark": {"type": "string", "title": "备注"}, "id": {"type": "string", "format": "uint64"}, "lang": {"type": "integer", "format": "int64"}}, "title": "流程引擎编辑请求"}, "graphRspDescribeListProcess": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "processes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/graphProcessInfo"}}}, "title": "流程配置列表响应"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}