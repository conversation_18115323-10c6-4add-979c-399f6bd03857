{"swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/tag/bff.proto", "version": "version not set"}, "tags": [{"name": "TagBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/tag/batch_import_system_tag": {"post": {"summary": "批量导入标签", "operationId": "TagBff_BatchImportSystemTag", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqBatchImportSystemTag"}}], "tags": ["TagBff"]}}, "/tag/create_personal_tag_binding": {"post": {"summary": "创建个人用户标签绑定", "operationId": "TagBff_CreatePersonalTagBinding", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqCreatePersonalTagBinding"}}], "tags": ["TagBff"]}}, "/tag/create_team_user_tag_binding": {"post": {"summary": "创建团队个人用户标签绑定", "operationId": "TagBff_CreateTeamUserTagBinding", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqCreateTeamUserTagBinding"}}], "tags": ["TagBff"]}}, "/tag/delete_system_tag": {"post": {"summary": "删除系统标签", "operationId": "TagBff_DeleteSystemTag", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDeleteSystemTag"}}], "tags": ["TagBff"]}}, "/tag/describe_assistant_tag_binding_infos": {"post": {"summary": "查询助手-用户自动打标标签关联详情", "operationId": "TagBff_DescribeAssistantTagBindingInfos", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeAssistantTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeAssistantTagBindingInfos"}}], "tags": ["TagBff"]}}, "/tag/describe_atlas_tag_binding_infos": {"post": {"summary": "查询图谱标签关联详情", "operationId": "TagBff_DescribeAtlasTagBindingInfos", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeAtlasTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeAtlasTagBindingInfos"}}], "tags": ["TagBff"]}}, "/tag/describe_product_tag_binding_infos": {"post": {"summary": "查询产品标签关联详情", "operationId": "TagBff_DescribeProductTagBindingInfos", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeProductTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeProductTagBindingInfos"}}], "tags": ["TagBff"]}}, "/tag/describe_resource_tag_binding_infos": {"post": {"summary": "查询资源标签关联详情", "operationId": "TagBff_DescribeResourceTagBindingInfos", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeResourceTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeResourceTagBindingInfos"}}], "tags": ["TagBff"]}}, "/tag/describe_system_tags_by_index": {"post": {"summary": "获取系统索引标签列表", "operationId": "TagBff_DescribeSystemTagsByIndex", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeSystemTagsByIndex"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeSystemTagsByIndex"}}], "tags": ["TagBff"]}}, "/tag/describe_team_tag_binding_infos": {"post": {"summary": "查询团队标签关联详情", "operationId": "TagBff_DescribeTeamTagBindingInfos", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeTeamTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeTeamTagBindingInfos"}}], "tags": ["TagBff"]}}, "/tag/edit_system_tag": {"post": {"summary": "编辑系统标签", "operationId": "TagBff_EditSystemTag", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqEditSystemTag"}}], "tags": ["TagBff"]}}, "/tag/get_notify_data_by_user_label": {"post": {"summary": "获取被用户标签绑定的数据信息", "operationId": "TagBff_GetNotifyDataByUserLabel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspGetNotifyDataByUserLabel"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqGetNotifyDataByUserLabel"}}], "tags": ["TagBff"]}}, "/tag/get_system_tags": {"post": {"summary": "获取全量系统标签", "operationId": "TagBff_GetSystemTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspGetSystemTags"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqGetSystemTags"}}], "tags": ["TagBff"]}}, "/tag/merge_system_tags": {"post": {"summary": "合并系统标签", "operationId": "TagBff_MergeSystemTags", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqMergeSystemTags"}}], "tags": ["TagBff"]}}, "/tag/update_system_tag": {"post": {"summary": "更新系统标签", "operationId": "TagBff_UpdateSystemTag", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqUpdateSystemTag"}}], "tags": ["TagBff"]}}}, "definitions": {"RspDescribeAssistantTagBindingInfosInfo": {"type": "object", "properties": {"name": {"type": "string"}, "name_en": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "create_by": {"$ref": "#/definitions/baseIdentity", "title": "创建人"}, "channel": {"$ref": "#/definitions/aiAssistantChannel", "title": "渠道"}, "enabled": {"type": "boolean", "title": "是否启用"}, "is_draft": {"type": "boolean", "title": "是否草稿"}}}, "aiAssistantChannel": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6], "description": "- 1: 碳LIVE-微信\n - 2: 碳LIVE-Web\n - 3: 碳LIVE-应用\n - 4: 碳LIVE-WhatsApp\n - 5: 第三方机构-微信\n - 6: 碳LIVE-小程序", "title": "助手渠道"}, "baseIdentity": {"type": "object", "properties": {"identity_type": {"$ref": "#/definitions/baseIdentityType", "title": "身份类型"}, "identity_id": {"type": "string", "format": "uint64", "title": "身份ID"}, "name": {"type": "string", "title": "名字"}, "extra_id": {"type": "string", "format": "uint64", "title": "额外ID（团队类型表示用户ID）"}}, "title": "身份"}, "baseIdentityType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 门户端用户\n - 2: 团队\n - 3: 运营端用户\n - 4: 自定义", "title": "身份类型"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "tagReqBatchImportSystemTag": {"type": "object", "properties": {"tag_index": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagReqBatchImportSystemTagTagIndex"}, "title": "导入标签索引数组"}, "type": {"$ref": "#/definitions/tagTagCreateType", "title": "标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1"}, "notify_type": {"$ref": "#/definitions/tagTagCreateType", "title": "定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1"}, "taggable_type": {"$ref": "#/definitions/tagTaggableType", "title": "标签类型"}, "remarks": {"type": "string", "title": "备注"}}}, "tagReqBatchImportSystemTagTagIndex": {"type": "object", "properties": {"index_name": {"type": "string", "title": "索引名称"}, "tag_name": {"type": "string", "title": "标签名称"}}}, "tagReqCreatePersonalTagBinding": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uint64"}, "tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "绑定现有的标签"}, "tag_names": {"type": "array", "items": {"type": "string"}, "title": "新增的自创标签"}}}, "tagReqCreateTeamUserTagBinding": {"type": "object", "properties": {"team_id": {"type": "string", "format": "uint64"}, "tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "绑定现有的标签"}, "tag_names": {"type": "array", "items": {"type": "string"}, "title": "新增的自创标签"}}}, "tagReqDeleteSystemTag": {"type": "object", "properties": {"tag_index_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "标签索引数组，会删除索引下的所有标签"}, "taggable_type": {"$ref": "#/definitions/tagTaggableType", "title": "标签索引类型"}}}, "tagReqDescribeAssistantTagBindingInfos": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}}}, "tagReqDescribeAtlasTagBindingInfos": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "filter": {"$ref": "#/definitions/tagReqDescribeAtlasTagBindingInfosFilter"}}}, "tagReqDescribeAtlasTagBindingInfosFilter": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}}}, "tagReqDescribeProductTagBindingInfos": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "filter": {"$ref": "#/definitions/tagReqDescribeProductTagBindingInfosFilter"}}, "title": "ReqDescribeProductTagBindingInfos 查询产品技术-面向用户标签关联详情"}, "tagReqDescribeProductTagBindingInfosFilter": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}}}, "tagReqDescribeResourceTagBindingInfos": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "filter": {"$ref": "#/definitions/tagReqDescribeResourceTagBindingInfosFilter"}}, "title": "ReqDescribeResourceTagBindingInfos 查询资源相关标签关联详情"}, "tagReqDescribeResourceTagBindingInfosFilter": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}}}, "tagReqDescribeSystemTagsByIndex": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "filter": {"$ref": "#/definitions/tagReqDescribeSystemTagsByIndexFilter"}, "order_by": {"type": "string"}}}, "tagReqDescribeSystemTagsByIndexFilter": {"type": "object", "properties": {"tag_name": {"type": "string", "title": "标签名称"}, "taggable_type": {"$ref": "#/definitions/tagTaggableType", "title": "标签索引类型"}, "index_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "标签索引id"}, "language": {"type": "string", "title": "语言类型"}, "type": {"$ref": "#/definitions/tagTagCreateType", "title": "标签类型（多语言选项或注册用） 1 系统 ；2 自创"}, "notify_type": {"$ref": "#/definitions/tagTagCreateType", "title": "定向推送标签类型（我来自标签时） 1 系统 ；2 自创"}}}, "tagReqDescribeTeamTagBindingInfos": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "filter": {"$ref": "#/definitions/tagReqDescribeTeamTagBindingInfosFilter"}}, "title": "ReqDescribeTeamTagBindingInfos 查询团队类型标签关联详情"}, "tagReqDescribeTeamTagBindingInfosFilter": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}}}, "tagReqEditSystemTag": {"type": "object", "properties": {"index_id": {"type": "string", "format": "uint64", "title": "标签索引id 为0 则创建，否则更新对应数据"}, "index_name": {"type": "string", "title": "标签索引名称"}, "zh_tag": {"$ref": "#/definitions/tanlivebff_mgmttagTag", "title": "中文标签信息"}, "en_tag": {"$ref": "#/definitions/tanlivebff_mgmttagTag", "title": "英文标签信息"}, "taggable_type": {"$ref": "#/definitions/tagTaggableType", "title": "标签索引类型"}}}, "tagReqGetNotifyDataByUserLabel": {"type": "object", "properties": {"taggable_type": {"$ref": "#/definitions/tagTaggableType"}, "tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tagReqGetSystemTags": {"type": "object", "properties": {"taggable_type": {"$ref": "#/definitions/tagTaggableType", "title": "标签索引类型"}, "language": {"type": "string", "title": "语言类型zh、en"}, "order_by": {"type": "array", "items": {"type": "string"}, "title": "排序"}, "notify_type": {"$ref": "#/definitions/tagTagCreateType", "title": "定向推送用的筛选条件 1 系统标签"}, "tag_name": {"type": "string", "title": "标签名称模糊搜索"}, "with_assistant_user_tag": {"type": "boolean", "title": "是否包含 助手用户标签"}}}, "tagReqMergeSystemTags": {"type": "object", "properties": {"index_id": {"type": "string", "format": "uint64", "title": "合并后的标签索引id"}, "merge_index_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "需要合并标签索引数组"}, "zh_tag": {"$ref": "#/definitions/tanlivebff_mgmttagTag", "title": "合并后的中文标签信息"}, "en_tag": {"$ref": "#/definitions/tanlivebff_mgmttagTag", "title": "合并后英文标签信息"}, "taggable_type": {"$ref": "#/definitions/tagTaggableType", "title": "合并的标签类型"}}}, "tagReqUpdateSystemTag": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64", "title": "标签id"}, "weight": {"type": "integer", "format": "int64", "title": "标签权重"}, "type": {"$ref": "#/definitions/tagTagCreateType", "title": "标签类型（多语言选项或注册用） 1 系统 ；2 自创"}, "taggable_type": {"$ref": "#/definitions/tagTaggableType", "title": "标签索引类型"}, "notify_type": {"$ref": "#/definitions/tagTagCreateType", "title": "定向推送标签类型（我来自标签时） 1 系统 ；2 自创"}}}, "tagRspDescribeAssistantTagBindingInfos": {"type": "object", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspDescribeAssistantTagBindingInfosInfo"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeAtlasTagBindingInfos": {"type": "object", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspDescribeAtlasTagBindingInfosBindingObject"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeAtlasTagBindingInfosBindingObject": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "atlas_name": {"type": "string", "title": "标题"}, "atlas_introduction": {"type": "string", "title": "简述"}, "atlas_types": {"type": "array", "items": {"type": "string"}, "title": "图谱类型"}, "create_date": {"type": "string", "title": "创建时间"}, "pub_name": {"type": "array", "items": {"type": "string"}, "title": "发布方"}, "atlas_id": {"type": "string", "format": "uint64", "title": "图谱id"}}}, "tagRspDescribeProductTagBindingInfos": {"type": "object", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspDescribeProductTagBindingInfosBindingObject"}}, "total_count": {"type": "integer", "format": "int64"}}, "title": "RspDescribeProductTagBindingInfos 查询产品技术-面向用户类型标签关联详情回复"}, "tagRspDescribeProductTagBindingInfosBindingObject": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string", "title": "产品名称"}, "brief_intro": {"type": "string", "title": "一句话简介"}, "product_types": {"type": "array", "items": {"type": "string"}, "title": "产品类型"}, "team_short_name": {"type": "string", "title": "团队简称"}, "create_date": {"type": "string", "title": "创建时间"}, "product_id": {"type": "string", "format": "uint64", "title": "产品id"}}}, "tagRspDescribeResourceTagBindingInfos": {"type": "object", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspDescribeResourceTagBindingInfosBindingObject"}}, "total_count": {"type": "integer", "format": "int64"}}, "title": "RspDescribeResourceTagBindingInfos 查询资源相关标签关联详情回复"}, "tagRspDescribeResourceTagBindingInfosBindingObject": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "res_name": {"type": "string", "title": "资源标题"}, "res_introduction": {"type": "string", "title": "资源简述"}, "res_types": {"type": "array", "items": {"type": "string"}, "title": "资源类型"}, "create_date": {"type": "string", "title": "创建时间"}, "originator_name": {"type": "array", "items": {"type": "string"}, "title": "主办方"}, "res_id": {"type": "string", "format": "uint64", "title": "资源id"}}}, "tagRspDescribeSystemTagsByIndex": {"type": "object", "properties": {"tag_set": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivetagTagIndex"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeTeamTagBindingInfos": {"type": "object", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspDescribeTeamTagBindingInfosBindingObject"}}, "total_count": {"type": "integer", "format": "int64"}}, "title": "RspDescribeTeamTagBindingInfos 查询团队类型标签关联详情回复"}, "tagRspDescribeTeamTagBindingInfosBindingObject": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "team_id": {"type": "string", "format": "uint64", "title": "团队id"}, "team_name": {"type": "string", "title": "团队名称"}, "team_short_name": {"type": "string", "title": "团队简称"}, "create_date": {"type": "string", "title": "创建时间"}, "team_nature": {"type": "string", "title": "团队属性"}, "verify_status": {"type": "string", "title": "认证状态"}, "holder_id": {"type": "string", "format": "uint64", "title": "团队持有人id"}, "holder_name": {"type": "string", "title": "团队持有人"}, "team_types": {"type": "array", "items": {"type": "string"}, "title": "团队类型"}, "contributors": {"type": "array", "items": {"type": "string"}, "title": "贡献者"}}}, "tagRspGetNotifyDataByUserLabel": {"type": "object", "properties": {"data_infos": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspGetNotifyDataByUserLabelDataInfo"}}}}, "tagRspGetNotifyDataByUserLabelDataInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "is_international": {"type": "boolean"}}}, "tagRspGetSystemTags": {"type": "object", "properties": {"tag_set": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspGetSystemTagsTag"}}}}, "tagRspGetSystemTagsTag": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "name": {"type": "string", "title": "标签名称"}}}, "tagTagCreateType": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 系统标签\n - 2: 自创标签", "title": "TagCreateType 标签创建类型枚举"}, "tagTagInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "name": {"type": "string", "title": "标签名称"}, "language": {"type": "string", "title": "语言类型"}, "is_tmt": {"type": "boolean", "title": "是否tmt"}, "citation_num": {"type": "integer", "format": "int64", "title": "引用数量"}, "weight": {"type": "integer", "format": "int64", "title": "标签权重"}, "type": {"$ref": "#/definitions/tagTagCreateType", "title": "标签类型（多语言选项或注册用） 1 系统 ；2 自创"}, "notify_type": {"$ref": "#/definitions/tagTagCreateType", "title": "定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "last_update_by": {"type": "string", "format": "uint64"}, "last_update_date": {"type": "string", "format": "date-time"}, "allow_delete": {"type": "boolean", "title": "是否允许删除"}}}, "tagTaggableType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 23, 101, 102, 103, 200, 201, 202, 203], "description": "- 1: 1 个人信息-我来自\n - 2: 2 团队类型\n - 3: 3 团队属性\n - 4: 4 团队行业\n - 5: 5 资源受众行业\n - 6: 6 产品技术-面向用户（场景）\n - 7: 7 资源类型\n - 8: 8 图谱-适用行业\n - 9: 9 图谱-适用场景\n - 10: 10 图谱类型\n - 11: 11 产品行业认可 --2.2迭代将 21资源-产品行业认可合并入11\n - 12: 12 团队行业认可 --2.2迭代将 22资源-团队行业认可合并入12\n - 14: 14 产品适用行业\n - 23: 23 资源系列\n - 101: 101 团队发展阶段\n - 102: 102 团队产品类型\n - 103: 103 受众团队融资阶段\n - 200: 200 后端用于业务及联的特殊类型，前端忽略\n - 201: 201 个人用户-用户标签\n - 202: 202 团队用户-用户标签\n - 203: 203 AI助手-给用户打标", "title": "标签类型"}, "tanlivebff_mgmttagTag": {"type": "object", "properties": {"weight": {"type": "integer", "format": "int64", "title": "标签id\n uint64 id = 1 ;\n标签权重"}, "type": {"$ref": "#/definitions/tagTagCreateType", "title": "// 语言\n string language = 3 ;\n标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1"}, "notify_type": {"$ref": "#/definitions/tagTagCreateType", "title": "定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1"}, "name": {"type": "string", "title": "标签名称"}, "is_tmt": {"type": "boolean", "title": "是否tmt"}}}, "tanlivetagTagIndex": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签索引id"}, "name": {"type": "string", "title": "标签索引名称"}, "zh_tag": {"$ref": "#/definitions/tagTagInfo", "title": "中文标签信息"}, "en_tag": {"$ref": "#/definitions/tagTagInfo", "title": "英文标签信息"}}}}}