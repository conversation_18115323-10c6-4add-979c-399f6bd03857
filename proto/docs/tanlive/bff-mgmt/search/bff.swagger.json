{"swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/search/bff.proto", "version": "version not set"}, "tags": [{"name": "SearchBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/search/describe_product_atlas_search_option": {"post": {"summary": "查询产品-图谱搜索筛选项", "operationId": "SearchBff_DescribeProductAtlasSearchOptions", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeTeamAtlasSearchOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeTeamAtlasSearchOptions"}}], "tags": ["SearchBff"]}}, "/search/describe_search_prompt": {"post": {"summary": "查询修改搜索提示语", "operationId": "SearchBff_DescribeSearchPrompts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeSearchPrompts"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeSearchPrompts"}}], "tags": ["SearchBff"]}}, "/search/describe_team_atlas_search_option": {"post": {"summary": "查询团队-图谱搜索筛选项", "operationId": "SearchBff_DescribeTeamAtlasSearchOptions", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeTeamAtlasSearchOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeTeamAtlasSearchOptions"}}], "tags": ["SearchBff"]}}, "/search/modify_product_atlas_search_option": {"post": {"summary": "修改产品-图谱搜索筛选项", "operationId": "SearchBff_ModifyProductAtlasSearchOption", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqModifyTeamAtlasSearchOption"}}], "tags": ["SearchBff"]}}, "/search/modify_search_prompt": {"post": {"summary": "修改搜索提示语", "operationId": "SearchBff_ModifySearchPrompt", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqModifySearchPrompt"}}], "tags": ["SearchBff"]}}, "/search/modify_team_atlas_search_option": {"post": {"summary": "修改团队-图谱搜索筛选项", "operationId": "SearchBff_ModifyTeamAtlasSearchOption", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqModifyTeamAtlasSearchOption"}}], "tags": ["SearchBff"]}}}, "definitions": {"ReqDescribeTeamAtlasSearchOptionsFilter": {"type": "object", "properties": {"language": {"type": "string", "title": "语言类型zh、en"}, "status": {"$ref": "#/definitions/baseDisableState"}}}, "RspDescribeTeamAtlasSearchOptionsTeamSearchOption": {"type": "object", "properties": {"option": {"$ref": "#/definitions/searchSearchOption"}, "atlas_bind_num": {"type": "integer", "format": "int64"}, "atlas_name": {"type": "string"}, "atlas_deleted": {"type": "boolean"}, "atlas_state": {"$ref": "#/definitions/baseUgcState"}, "atlas_draft_id": {"type": "string", "format": "uint64"}}}, "baseCrudType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 创建\n - 2: 读取\n - 3: 更新\n - 4: 删除", "title": "数据操作类型"}, "baseDisableState": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 启用\n - 2: 禁用", "title": "禁用状态"}, "baseUgcState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8], "description": "- 1: 草稿。用户前台：草稿；运营后台：草稿\n - 2: 自动审核中。用户前台：处理中；运营后台：处理中\n - 3: 人工审核中（用户前台：处理中；运营后台：待处理）\n - 4: 人工审核中-可疑（用户前台：处理中；运营后台：可疑，待处理）\n - 5: 审核已通过（用户前台：下架存档；运营后台：下架存档）\n - 6: 已发布（用户前台：已发布；运营后台：已发布）\n - 7: 审核已驳回（用户前台：已驳回；运营后台：已驳回）\n - 8: 申诉中（用户前台：申诉中；运营后台：申诉待处理）", "title": "UGC状态"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "searchReqDescribeSearchPrompts": {"type": "object", "properties": {"language": {"type": "string", "title": "语言类型zh、en"}}}, "searchReqDescribeTeamAtlasSearchOptions": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "filter": {"$ref": "#/definitions/ReqDescribeTeamAtlasSearchOptionsFilter"}, "order_by": {"type": "array", "items": {"type": "string"}}}}, "searchReqModifySearchPrompt": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "content": {"type": "string", "title": "可以为空，代表将内容置空"}}}, "searchReqModifyTeamAtlasSearchOption": {"type": "object", "properties": {"option": {"$ref": "#/definitions/searchSearchOption"}, "operation": {"$ref": "#/definitions/baseCrudType"}}}, "searchRspDescribeSearchPrompts": {"type": "object", "properties": {"prompts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/searchSearchPrompt"}}}}, "searchRspDescribeTeamAtlasSearchOptions": {"type": "object", "properties": {"options": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspDescribeTeamAtlasSearchOptionsTeamSearchOption"}}, "total_count": {"type": "integer", "format": "int64"}}}, "searchSearchOption": {"type": "object", "properties": {"language": {"type": "string"}, "name": {"type": "string"}, "weight": {"type": "integer", "format": "int32"}, "refer_id": {"type": "string", "format": "int64"}, "status": {"$ref": "#/definitions/baseDisableState"}, "id": {"type": "string", "format": "int64"}}, "title": "搜索筛选项"}, "searchSearchPrompt": {"type": "object", "properties": {"target_type": {"type": "string", "title": "搜索对应的模块类型"}, "refer_type": {"type": "string", "title": "搜索对应的筛选项类型"}, "language": {"type": "string"}, "id": {"type": "string", "format": "int64"}, "content": {"type": "string"}}, "title": "搜索提示语"}}}