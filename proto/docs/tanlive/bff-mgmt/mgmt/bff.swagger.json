{"swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/mgmt/bff.proto", "version": "version not set"}, "tags": [{"name": "Au<PERSON><PERSON><PERSON>"}, {"name": "MgmtBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/mgmt/search_users": {"post": {"summary": "搜索运营端用户列表", "operationId": "MgmtBff_SearchUsers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgmtRspSearchUsers"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgmtReqSearchUsers"}}], "tags": ["MgmtBff"]}}, "/op/login": {"post": {"summary": "登录", "operationId": "AuthBff_Login", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgmtRspLogin"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgmtReqLogin"}}], "tags": ["Au<PERSON><PERSON><PERSON>"]}}, "/op/logout": {"post": {"summary": "登出", "operationId": "AuthBff_Logout", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["Au<PERSON><PERSON><PERSON>"]}}, "/op/role_get": {"post": {"summary": "查询角色列表", "operationId": "MgmtBff_GetRoles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgmtRspGetRoles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgmtReqGetRoles"}}], "tags": ["MgmtBff"]}}, "/op/text_translate": {"post": {"summary": "翻译", "operationId": "MgmtBff_TextTranslate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgmtRspTextTranslate"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgmtReqTextTranslate"}}], "tags": ["MgmtBff"]}}}, "definitions": {"RspLoginUserInfo": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uint64", "title": "用户ID"}, "user_name": {"type": "string", "title": "用户名"}, "remark_name": {"type": "string", "title": "备注名"}}}, "baseTcloudCaptcha": {"type": "object", "properties": {"ticket": {"type": "string", "title": "前端回调函数返回的用户验证票据"}, "randstr": {"type": "string", "title": "前端回调函数返回的随机字符串"}}, "title": "腾讯云验证码参数"}, "mgmtOpUser": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "username": {"type": "string"}, "remark_name": {"type": "string"}, "phone_num": {"type": "string"}, "email": {"type": "string"}, "state": {"type": "integer", "format": "int32"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "last_update_by": {"type": "string", "format": "uint64"}, "last_update_date": {"type": "string", "format": "date-time"}}, "title": "运营端用户"}, "mgmtReqGetRoles": {"type": "object", "properties": {"name": {"type": "string"}, "state": {"type": "string", "format": "uint64"}, "type": {"type": "string", "format": "uint64"}}}, "mgmtReqLogin": {"type": "object", "properties": {"username": {"type": "string", "title": "用户名"}, "password": {"type": "string", "title": "密码"}, "tcloud_captcha": {"$ref": "#/definitions/baseTcloudCaptcha", "title": "腾讯云验证码参数"}}}, "mgmtReqSearchUsers": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "keyword": {"type": "string", "title": "搜索关键词"}}}, "mgmtReqTextTranslate": {"type": "object", "properties": {"text": {"type": "string"}, "source_language": {"type": "string"}, "target_language": {"type": "string"}}}, "mgmtRspGetRoles": {"type": "object", "properties": {"roles": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtmgmtOpRole"}}}}, "mgmtRspLogin": {"type": "object", "properties": {"user_info": {"$ref": "#/definitions/RspLoginUserInfo"}}}, "mgmtRspSearchUsers": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/mgmtOpUser"}, "title": "用户列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}}}, "mgmtRspTextTranslate": {"type": "object", "properties": {"text": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "tanlivebff_mgmtmgmtOpRole": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "role_name": {"type": "string"}, "state": {"type": "string", "format": "uint64"}, "type": {"type": "string", "format": "uint64"}, "account_count": {"type": "string", "format": "uint64"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string"}, "last_update_by": {"type": "string", "format": "uint64"}, "last_update_date": {"type": "string"}, "could_del": {"type": "boolean"}}, "title": "角色"}}}