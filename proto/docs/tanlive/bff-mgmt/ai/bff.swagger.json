{"swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/ai/bff.proto", "version": "version not set"}, "tags": [{"name": "AiBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/ai/accept_feedback": {"post": {"summary": "采用用户反馈", "operationId": "AiBff_AcceptFeedback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspAcceptFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqAcceptFeedback"}}], "tags": ["AiBff"]}}, "/ai/auto_chunk_doc": {"post": {"summary": "自动文档分段", "operationId": "AiBff_AutoChunkDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspAutoChunkDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqAutoChunkDoc"}}], "tags": ["AiBff"]}}, "/ai/batch_create_assistant": {"post": {"summary": "批量创建助手", "operationId": "AiBff_BatchCreateAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBatchCreateAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchCreateAssistant"}}], "tags": ["AiBff"]}}, "/ai/batch_update_assistant": {"post": {"summary": "批量更新助手", "operationId": "AiBff_BatchUpdateAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchUpdateAssistant"}}], "tags": ["AiBff"]}}, "/ai/collection/batch_update_docs": {"post": {"summary": "批量更新doc的特定字段值", "operationId": "AiBff_BatchUpdateDocAttr", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBatchUpdateDocAttr"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchUpdateDocAttr"}}], "tags": ["AiBff"]}}, "/ai/collection/clone_doc": {"post": {"summary": "克隆QA/文本/文件", "operationId": "AiBff_CloneDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCloneDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCloneDoc"}}], "tags": ["AiBff"]}}, "/ai/collection/create_doc_query": {"post": {"summary": "创建doc查询", "operationId": "AiBff_CreateDocQuery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocQuery"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateDocQuery"}}], "tags": ["AiBff"]}}, "/ai/collection/create_qas": {"post": {"summary": "创建QA", "operationId": "AiBff_CreateQAs", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateQAs"}}], "tags": ["AiBff"]}}, "/ai/collection/create_system_doc_copy": {"post": {"summary": "创建系统文档副本（人工修改）", "operationId": "AiBff_CreateSystemDocCopy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateSystemDocCopy"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateSystemDocCopy"}}], "tags": ["AiBff"]}}, "/ai/collection/create_text_files": {"post": {"summary": "创建文本或文件", "operationId": "AiBff_CreateTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/delete_docs": {"post": {"summary": "删除Doc，包括QA，文本或文件", "operationId": "AiBff_DeleteQAs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDeleteDocs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteDocs"}}], "tags": ["AiBff"]}}, "/ai/collection/delete_system_doc": {"post": {"summary": "删除系统文档", "operationId": "AiBff_DeleteSystemDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDeleteSystemDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteSystemDoc"}}], "tags": ["AiBff"]}}, "/ai/collection/disable_system_doc": {"post": {"summary": "停用系统文档", "operationId": "AiBff_DisableSystemDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDisableSystemDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDisableSystemDoc"}}], "tags": ["AiBff"]}}, "/ai/collection/enable_system_doc": {"post": {"summary": "启用系统文档", "operationId": "AiBff_EnableSystemDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspEnableSystemDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqEnableSystemDoc"}}], "tags": ["AiBff"]}}, "/ai/collection/get_qa_tip": {"post": {"summary": "查询QA的知识提示（问题超长，内容重复）等信息", "operationId": "AiBff_GetQaTip", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetQaTip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetQaTip"}}], "tags": ["AiBff"]}}, "/ai/collection/get_text_file": {"post": {"summary": "id查询文本/文件详情", "operationId": "AiBff_GetTextFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetTextFile"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetTextFile"}}], "tags": ["AiBff"]}}, "/ai/collection/get_text_file_tip": {"post": {"summary": "查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息", "operationId": "AiBff_GetTextFileTip", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetTextFileTip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetTextFileTip"}}], "tags": ["AiBff"]}}, "/ai/collection/list_assistant": {"post": {"summary": "查询ai助手列表", "operationId": "AiBff_ListAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListAssistant"}}], "tags": ["AiBff"]}}, "/ai/collection/list_collection": {"post": {"summary": "查询collection用户端列表", "operationId": "AiBff_ListCollection", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListCollection"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["AiBff"]}}, "/ai/collection/list_contributor": {"post": {"summary": "查询贡献者列表", "operationId": "AiBff_ListContributor", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListContributor"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListContributor"}}], "tags": ["AiBff"]}}, "/ai/collection/list_doc_by_ref": {"post": {"summary": "根据文档ref_id查询文档", "operationId": "AiBff_ListDocByRef", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListDocByRef"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListDocByRef"}}], "tags": ["AiBff"]}}, "/ai/collection/list_filename": {"post": {"summary": "查询文件列表", "operationId": "AiBff_ListCollectionFileName", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListCollectionFileName"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListCollectionFileName"}}], "tags": ["AiBff"]}}, "/ai/collection/list_operator": {"post": {"summary": "查询更新人列表", "operationId": "AiBff_ListOperator", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListOperator"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListOperator"}}], "tags": ["AiBff"]}}, "/ai/collection/list_qa": {"post": {"summary": "查询QA列表", "operationId": "AiBff_ListQA", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListQA"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListQA"}}], "tags": ["AiBff"]}}, "/ai/collection/list_text_files": {"post": {"summary": "查询文本或文件列表", "operationId": "AiBff_ListTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/onoff_docs": {"post": {"summary": "启用/禁用doc", "operationId": "AiBff_OnOffDocs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspOnOffDocs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqOnOffDocs"}}], "tags": ["AiBff"]}}, "/ai/collection/reparse_text_files": {"post": {"summary": "重新解析文件", "operationId": "AiBff_ReparseTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspReparseTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqReparseTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/search_collection": {"post": {"summary": "collection向量查询", "operationId": "AiBff_SearchCollection", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSearchCollection"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSearchCollection"}}], "tags": ["AiBff"]}}, "/ai/collection/update_qas": {"post": {"summary": "更新QA", "operationId": "AiBff_UpdateQAs", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateQAs"}}], "tags": ["AiBff"]}}, "/ai/collection/update_text_files": {"post": {"summary": "更新文本或文件", "operationId": "AiBff_UpdateTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/validate_qas": {"post": {"summary": "校验待创建QA", "operationId": "AiBff_ValidateQAs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspValidateQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqValidateQAs"}}], "tags": ["AiBff"]}}, "/ai/create_assistant": {"post": {"summary": "创建助手", "operationId": "AiBff_CreateAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateAssistant"}}], "tags": ["AiBff"]}}, "/ai/create_assistant_sender": {"post": {"summary": "创建助手发送方设置", "operationId": "AiBff_CreateDocShareConfigSender", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateDocShareConfigSender"}}], "tags": ["AiBff"]}}, "/ai/create_assistant_share": {"post": {"summary": "创建文档分享（支持助手、个人、团队）", "operationId": "AiBff_CreateAssistantShare", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateAssistantShare"}}], "tags": ["AiBff"]}}, "/ai/delete_assistant": {"post": {"summary": "删除助手", "operationId": "AiBff_DeleteAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteAssistant"}}], "tags": ["AiBff"]}}, "/ai/delete_custom_labels": {"post": {"summary": "删除自定义标签", "operationId": "AiBff_DeleteCustomLabels", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteCustomLabels"}}], "tags": ["AiBff"]}}, "/ai/describe_chat_region_code": {"post": {"summary": "获取所有会话的地区编码", "operationId": "AiBff_DescribeChatRegionCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeChatRegionCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeChatRegionCode"}}], "tags": ["AiBff"]}}, "/ai/describe_chat_suggest_log": {"post": {"summary": "获取建议问题日志", "operationId": "AiBff_DescribeChatSuggestLog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeChatSuggestLog"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeChatSuggestLog"}}], "tags": ["AiBff"]}}, "/ai/describe_feedback_region_code": {"post": {"summary": "获取所有教学反馈的地区编码", "operationId": "AiBff_DescribeFeedbackRegionCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeFeedbackRegionCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeFeedbackRegionCode"}}], "tags": ["AiBff"]}}, "/ai/find_feedback": {"post": {"summary": "查询用户反馈详情", "operationId": "AiBff_FindFeedback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspFindFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqFindFeedback"}}], "tags": ["AiBff"]}}, "/ai/get_assistant_logs_page": {"post": {"summary": "查询助手日志分页列表", "operationId": "AiBff_GetAssistantLogsPage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantLogsPage"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantLogsPage"}}], "tags": ["AiBff"]}}, "/ai/get_assistant_options": {"post": {"summary": "获取助手下拉选项", "operationId": "AiBff_GetAssistantOptions", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["AiBff"]}}, "/ai/get_assistants_page": {"post": {"summary": "查询助手分页列表", "operationId": "AiBff_GetAssistantsPage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantsPage"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantsPage"}}], "tags": ["AiBff"]}}, "/ai/get_chat_detail": {"post": {"summary": "AI对话详情", "operationId": "AiBff_GetChatDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetChatDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetChatDetail"}}], "tags": ["AiBff"]}}, "/ai/get_chat_message_detail": {"post": {"summary": "获取消息详情", "operationId": "AiBff_GetChatMessageDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetChatMessageDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetChatMessageDetail"}}], "tags": ["AiBff"]}}, "/ai/get_chunk_doc_tasks": {"post": {"summary": "查询文档分段任务列表", "operationId": "AiBff_GetChunkDocTasks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetChunkDocTasks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetChunkDocTasks"}}], "tags": ["AiBff"]}}, "/ai/get_custom_labels": {"post": {"summary": "获取自定义标签列表", "operationId": "AiBff_ListCustomLabel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListCustomLabel"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListCustomLabel"}}], "tags": ["AiBff"]}}, "/ai/get_doc_chunks": {"post": {"summary": "查询文档分段信息", "operationId": "AiBff_GetDocChunks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetDocChunks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetDocChunks"}}], "tags": ["AiBff"]}}, "/ai/get_doc_embedding_models": {"post": {"summary": "查询文档的向量化模型", "operationId": "AiBff_GetDocEmbeddingModels", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetDocEmbeddingModels"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetDocEmbeddingModels"}}], "tags": ["AiBff"]}}, "/ai/get_feedback_logs": {"post": {"summary": "查询用户反馈日志列表", "operationId": "AiBff_GetFeedbackLogs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetFeedbackLogs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetFeedbackLogs"}}], "tags": ["AiBff"]}}, "/ai/get_feedbacks": {"post": {"summary": "查询用户反馈列表", "operationId": "AiBff_GetFeedbacks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetFeedbacks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetFeedbacks"}}], "tags": ["AiBff"]}}, "/ai/list_assistant_sender": {"post": {"summary": "查询助手发送方设置", "operationId": "AiBff_ListeDocShareConfigSender", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListeDocShareConfigSender"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListeDocShareConfigSender"}}], "tags": ["AiBff"]}}, "/ai/list_chat": {"post": {"summary": "AI对话管理列表", "operationId": "AiBff_ListChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListChat"}}], "tags": ["AiBff"]}}, "/ai/list_chat_live_agent": {"post": {"summary": "获取人工坐席列表", "operationId": "AiBff_ListChatLiveAgent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListChatLiveAgent"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListChatLiveAgent"}}], "tags": ["AiBff"]}}, "/ai/list_shared_assistant": {"post": {"summary": "查询可分享的助手列表", "operationId": "AiBff_ListAssistantCanShareDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListAssistantCanShareDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListAssistantCanShareDoc"}}], "tags": ["AiBff"]}}, "/ai/manual_chunk_doc": {"post": {"summary": "手动文档分段", "operationId": "AiBff_ManualChunkDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspManualChunkDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqManualChunkDoc"}}], "tags": ["AiBff"]}}, "/ai/modify_custom_labels": {"post": {"summary": "插入或更新自定义标签", "operationId": "AiBff_ModifyCustomLabels", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspModifyCustomLabels"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqModifyCustomLabels"}}], "tags": ["AiBff"]}}, "/ai/proxy_chat_url": {"post": {"summary": "获取助手url网页title", "operationId": "AiBff_ProxyChatHtmlUrl", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspProxyChatHtmlUrl"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqProxyChatHtmlUrl"}}], "tags": ["AiBff"]}}, "/ai/search_chat": {"post": {"summary": "搜索chat", "operationId": "AiBff_SearchChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSearchChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSearchChat"}}], "tags": ["AiBff"]}}, "/ai/switch_chat_live_agent": {"post": {"summary": "切换人工坐席", "operationId": "AiBff_SwitchChatLiveAgent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSwitchChatLiveAgent"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSwitchChatLiveAgent"}}], "tags": ["AiBff"]}}, "/ai/sync_fix_message_collection": {"post": {"summary": "修复message collection", "operationId": "AiBff_SyncFixChatMessageCollection", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSyncFixChatMessageCollection"}}], "tags": ["AiBff"]}}, "/ai/update_assistant": {"post": {"summary": "更新助手", "operationId": "AiBff_UpdateAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateAssistant"}}], "tags": ["AiBff"]}}, "/ai/update_assistant_notice_conf": {"post": {"summary": "更新助手的横幅信息", "operationId": "AiBff_UpdateAssistantNoticeConf", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateChatNoticeConf"}}], "tags": ["AiBff"]}}, "/ai/update_object_custom_labels": {"post": {"summary": "更新对象的自定义标签", "operationId": "AiBff_UpdateObjectCustomLabels", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateObjectCustomLabels"}}], "tags": ["AiBff"]}}, "/ai/upsert_mgmt_feedback": {"post": {"summary": "创建/更新碳LIVE反馈", "operationId": "AiBff_UpsertMgmtFeedback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpsertMgmtFeedback"}}], "tags": ["AiBff"]}}}, "definitions": {"AiAssistantNoticeConfNotice": {"type": "object", "properties": {"zh": {"type": "string"}, "en": {"type": "string"}}}, "AssistantKefuReplyCustom": {"type": "object", "properties": {"url": {"type": "string", "title": "url地址"}, "text": {"type": "string", "title": "显示文本"}}}, "AssistantKefuReplyReply": {"type": "object", "properties": {"reply_message": {"type": "string", "title": "回复消息"}, "img_url": {"type": "string", "title": "图片地址"}}}, "ChatLiveAgentInfoLiveAgentInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "nickname": {"type": "string"}}}, "FeedbackCommentFile": {"type": "object", "properties": {"file_path": {"type": "string", "title": "路径"}, "file_name": {"type": "string", "title": "文件名称"}}, "title": "文件"}, "ReqListChatFilter": {"type": "object", "properties": {"user_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "用户id"}, "chat_titles": {"type": "array", "items": {"type": "string"}, "title": "对话内容"}, "region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "nicknames": {"type": "array", "items": {"type": "string"}}, "reject_job_result": {"type": "integer", "format": "int64", "title": "筛选审核 1 违规 2 敏感 3 正常"}, "region_codes": {"type": "array", "items": {"type": "string"}, "title": "国家或地区编码"}, "is_manual": {"type": "integer", "format": "int32", "title": "是否转过人工服务 1 否 2 是"}}}, "ReqListTextFilesSearch": {"type": "object", "properties": {"text": {"type": "string", "title": "文本内容搜索"}, "file_name": {"type": "string", "title": "文件名搜索"}, "ugc_title": {"type": "string"}}}, "RspListDocByRefDoc": {"type": "object", "properties": {"text": {"type": "string"}, "question": {"type": "string"}, "file_name": {"type": "string"}, "url": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "ref": {"type": "string"}}}, "RspOnOffDocsQaContainsMatchCount": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "cnt": {"type": "string", "format": "uint64"}}}, "RspOnOffDocsRepeatCollection": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "file_name": {"type": "object", "additionalProperties": {"type": "string"}}}}, "RspProxyChatHtmlUrlContent": {"type": "object", "properties": {"url": {"type": "string"}, "title": {"type": "string"}}}, "RspValidateQAsErr": {"type": "object", "properties": {"code": {"$ref": "#/definitions/errorsAiError"}, "message": {"type": "string"}, "id": {"type": "string", "format": "uint64"}}}, "aiAiAssistantNoticeConf": {"type": "object", "properties": {"notice": {"$ref": "#/definitions/AiAssistantNoticeConfNotice"}, "enable": {"type": "boolean", "title": "是否启用"}, "range_time": {"$ref": "#/definitions/baseTimeRange"}, "channel": {"type": "array", "items": {"$ref": "#/definitions/aiAssistantChannel"}}}, "title": "AiAssistantNoticeConf ai助手横幅提示配置"}, "aiAiRecordType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "title": "- 1: 用户咨询\n - 2: 助手回答文本消息\n - 3: 助手回答菜单消息\n - 4: 助手回答建议问题菜单消息\n - 5: 助手回答贡献知识的小程序\n - 6: 发送转人工菜单信息\n - 7: 助手回答图片\n - 8: 助手回答音频\n - 9: 助手回答视频\n - 10: 助手回答文件"}, "aiAskSuggestionMode": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 模式1：根据历史问答生成问题建议\n - 2: 模式2：根据历史问答生成，并仅显示知识库中有相关知识的问题建议\n - 3: 模式3：根据问题在知识库中已命中的知识，生成问题建议\n - 4: 模式4：根据问题在知识库中尚未命中但排名靠前的知识，生成问题建议", "title": "问题建议模式"}, "aiAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollection"}}, "website_route": {"type": "string"}, "search_debug": {"type": "boolean"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int32"}, "text_weight": {"type": "number", "format": "float"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "clean_chunks": {"type": "boolean"}, "text_recall_query": {"$ref": "#/definitions/aiTextRecallQuery", "title": "关键词召回匹配目标"}, "text_recall_pattern": {"$ref": "#/definitions/aiTextRecallPattern", "title": "关键词召回模式"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}}}, "aiAssistantAction": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 创建\n - 2: 保存草稿\n - 3: 发布\n - 4: 删除", "title": "助手操作类型"}, "aiAssistantAllowlistConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "type": {"$ref": "#/definitions/aiAssistantAllowlistType", "title": "白名单类型"}, "phones": {"type": "array", "items": {"type": "string"}, "title": "手机列表"}}, "title": "助手白名单配置"}, "aiAssistantAllowlistType": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 手机号码\n - 2: 微信昵称", "title": "助手白名单类型"}, "aiAssistantAskSuggestionConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "prompt": {"type": "string", "title": "提示词"}, "count": {"type": "integer", "format": "int32", "title": "问题建议数量"}, "mode": {"$ref": "#/definitions/aiAskSuggestionMode", "title": "问题建议模式"}, "times": {"type": "integer", "format": "int64", "title": "问题建议倍数"}, "model": {"type": "string", "title": "模型"}}, "title": "问题建议配置"}, "aiAssistantChanges": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "string"}, "title": "变动字段"}, "old": {"$ref": "#/definitions/aiAssistantConfig", "title": "变化前的配置"}, "new": {"$ref": "#/definitions/aiAssistantConfig", "title": "变化后的配置"}}, "title": "助手字段变化"}, "aiAssistantChannel": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6], "description": "- 1: 碳LIVE-微信\n - 2: 碳LIVE-Web\n - 3: 碳LIVE-应用\n - 4: 碳LIVE-WhatsApp\n - 5: 第三方机构-微信\n - 6: 碳LIVE-小程序", "title": "助手渠道"}, "aiAssistantChatOrSqlConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "model": {"type": "string", "title": "模型"}}, "title": "ChatOrSql配置"}, "aiAssistantChunkConfig": {"type": "object", "properties": {"min_char_count": {"type": "integer", "format": "int32", "title": "最小字符数"}, "min_char_lang": {"type": "string", "title": "最小字符语言"}, "max_char_count": {"type": "integer", "format": "int32", "title": "最大字符数"}, "max_char_lang": {"type": "string", "title": "最大字符语言"}, "overlap_count": {"type": "integer", "format": "int32", "title": "重合字符数"}, "overlap_lang": {"type": "string", "title": "重合字符语言"}}, "title": "分段配置"}, "aiAssistantChunks": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}, "chunks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChunkItem"}, "title": "分段列表"}}, "title": "助手的分段信息"}, "aiAssistantConfig": {"type": "object", "properties": {"search_debug": {"type": "boolean", "title": "搜索测试"}, "visible_chain_config": {"$ref": "#/definitions/aiAssistantVisibleChainConfig", "title": "链路查询"}, "field_manage_config": {"$ref": "#/definitions/aiAssistantFieldManageConfig", "title": "参数管理权限"}, "name": {"type": "string", "title": "AI助手名"}, "name_en": {"type": "string", "title": "AI助手名（英文）"}, "channel": {"$ref": "#/definitions/aiAssistantChannel", "title": "渠道"}, "enabled": {"type": "boolean", "title": "是否启用"}, "admins": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseIdentity"}, "title": "助手管理员"}, "collection_lang": {"type": "string", "title": "知识库语言"}, "collection_name": {"type": "string", "title": "知识库名称（自动生成）"}, "prompt_prefix": {"type": "string", "title": "提示词"}, "model": {"type": "string", "title": "对话模型"}, "threshold": {"type": "number", "format": "float", "title": "对话阈值"}, "history_rounds": {"type": "integer", "format": "int32", "title": "对话轮数"}, "search_engine": {"type": "string", "title": "搜索引擎"}, "doc_top_n": {"type": "integer", "format": "int32", "title": "知识库Top_N"}, "search_top_n": {"type": "integer", "format": "int32", "title": "互联网Top_N"}, "chat_or_sql_config": {"$ref": "#/definitions/aiAssistantChatOrSqlConfig", "title": "ChatOrSql配置"}, "ask_suggestion_config": {"$ref": "#/definitions/aiAssistantAskSuggestionConfig", "title": "问题建议配置"}, "weixin_channel_config": {"$ref": "#/definitions/aiAssistantWeixinChannelConfig", "title": "微信渠道配置"}, "tanlive_web_channel_config": {"$ref": "#/definitions/aiAssistantTanliveWebChannelConfig", "title": "碳LIVE Web渠道配置"}, "tanlive_app_channel_config": {"$ref": "#/definitions/aiAssistantTanliveAppChannelConfig", "title": "Web渠道配置"}, "whatsapp_channel_config": {"$ref": "#/definitions/aiAssistantWhatsappChannelConfig", "title": "WhatsApp渠道配置"}, "miniprogram_channel_config": {"$ref": "#/definitions/aiAssistantMiniprogramChannelConfig", "title": "小程序渠道配置"}, "use_region_code": {"type": "string", "title": "使用地区（空不限制）"}, "close_search": {"type": "boolean", "title": "是否关闭搜索增强"}, "text_weight": {"type": "number", "format": "float", "title": "关键词搜索权重"}, "miss_reply": {"type": "string", "title": "未命中知识库自动回复"}, "brief_intro": {"type": "string", "title": "一句话介绍"}, "detail_intro": {"type": "string", "title": "助手介绍"}, "chunk_config": {"$ref": "#/definitions/aiAssistantChunkConfig", "title": "分段配置"}, "show_think": {"type": "boolean", "title": "是否展示思考过程"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "question_type_config": {"$ref": "#/definitions/aiAssistantQuestionTypeConfig", "title": "问题分类配置"}, "show_in_list": {"type": "boolean", "title": "是否在助手列表展示"}, "allowlist_config": {"$ref": "#/definitions/aiAssistantAllowlistConfig", "title": "白名单配置"}, "clean_chunks": {"type": "boolean", "title": "自动过滤"}, "text_recall_query": {"$ref": "#/definitions/aiTextRecallQuery", "title": "QA关键词召回目标"}, "text_recall_pattern": {"$ref": "#/definitions/aiTextRecallPattern", "title": "关键词召回模式"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "允许平移距离"}, "user_label_config": {"$ref": "#/definitions/aiAssistantUserLabelConfig", "title": "助手用户打标配置"}}, "title": "助手配置"}, "aiAssistantFieldManageConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用（该字段暂不使用）"}, "readable": {"type": "array", "items": {"type": "string"}, "title": "可读字段（为空全部可读）"}, "writable": {"type": "array", "items": {"type": "string"}, "title": "可写字段（为空全部不可写）"}}, "title": "字段管理配置"}, "aiAssistantGraphParseConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "model": {"type": "string", "title": "模型"}}, "title": "图谱解析配置"}, "aiAssistantInteractiveCode": {"type": "object", "properties": {"lang": {"type": "string", "title": "语言"}, "interactive_code": {"$ref": "#/definitions/aiInteractiveCode", "title": "互动暗号"}, "content": {"type": "string", "title": "内容"}, "show_in_welcome": {"type": "boolean", "title": "是否在欢迎语里显示"}}, "title": "互动暗号详情"}, "aiAssistantInteractiveCodeConfig": {"type": "object", "properties": {"codes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantInteractiveCode"}, "title": "暗号配置"}, "send_interactive_code": {"type": "boolean", "title": "开启回复发送暗号"}}, "title": "互动暗号配置"}, "aiAssistantKefuConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "staffs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantKefuStaff"}, "title": "员工信息"}, "after_remind_message": {"type": "string", "title": "转人工后的提示语"}, "before_remind_enabled": {"type": "boolean", "title": "是否启用转人工前的提示语"}, "before_remind_message": {"type": "string", "title": "转人工前的提示语"}, "reply": {"$ref": "#/definitions/aiAssistantKefuReply", "title": "客服自动回复配置"}}, "title": "人工客服配置"}, "aiAssistantKefuReply": {"type": "object", "properties": {"mainland_zh": {"$ref": "#/definitions/AssistantKefuReplyReply", "title": "大陆中文"}, "mainland_en": {"$ref": "#/definitions/AssistantKefuReplyReply", "title": "大陆英文"}, "non_mainland_zh": {"$ref": "#/definitions/AssistantKefuReplyReply", "title": "非中国大陆中文"}, "non_mainland_en": {"$ref": "#/definitions/AssistantKefuReplyReply", "title": "非中国大陆英文"}, "enable_custom": {"type": "boolean", "title": "是否启用自定义配置"}, "custom": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/AssistantKefuReplyCustom"}, "title": "自定义配置列表"}}, "title": "助手客服自动回复"}, "aiAssistantKefuStaff": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "username": {"type": "string", "title": "账号"}, "nickname": {"type": "string", "title": "昵称"}}, "title": "人工客服信息"}, "aiAssistantLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "日志ID"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "action": {"$ref": "#/definitions/aiAssistantAction", "title": "操作类型"}, "changes": {"$ref": "#/definitions/aiAssistantChanges", "title": "配置变化"}, "create_by": {"$ref": "#/definitions/baseIdentity", "title": "操作人"}, "create_date": {"type": "string", "format": "date-time", "title": "操作时间"}}, "title": "助手日志"}, "aiAssistantMiniprogramChannelConfig": {"type": "object", "properties": {"nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "miniprogram_config": {"$ref": "#/definitions/aiAssistantMiniprogramConfig", "title": "小程序配置"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "interactive_code_config": {"$ref": "#/definitions/aiAssistantInteractiveCodeConfig", "title": "互动暗号配置"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"$ref": "#/definitions/aiAssistantGraphParseConfig", "title": "图谱解析配置"}, "assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "kefu_config": {"$ref": "#/definitions/aiAssistantKefuConfig", "title": "人工客服"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}}, "title": "助手小程序渠道配置"}, "aiAssistantMiniprogramConfig": {"type": "object", "properties": {"share_title": {"type": "string", "title": "分享标题"}, "share_image": {"type": "string", "title": "分享图片"}, "url": {"type": "string", "title": "二维码URL"}, "schema": {"type": "string", "title": "小程序schema"}}, "title": "小程序配置"}, "aiAssistantPresetQuestion": {"type": "object", "properties": {"lang": {"type": "string", "title": "语言"}, "content": {"type": "string", "title": "内容"}}, "title": "预设问题详情"}, "aiAssistantPresetQuestionConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "questions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantPresetQuestion"}, "title": "配置列表"}}, "title": "预设问题配置"}, "aiAssistantQuestionTypeConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "chat_model": {"type": "string", "title": "聊天问题的模型"}, "simple_model": {"type": "string", "title": "简单问题的模型"}, "complex_model": {"type": "string", "title": "复杂问题的模型"}, "prompt": {"type": "string", "title": "简单、复杂问题提示词"}}, "title": "问题类型配置"}, "aiAssistantRatingScaleReply": {"type": "object", "properties": {"lang": {"type": "string", "title": "语言"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale", "title": "评价等级"}, "content": {"type": "string", "title": "内容"}}, "title": "满意度回复详情"}, "aiAssistantRatingScaleReplyConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "replies": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantRatingScaleReply"}, "title": "满意度回复配置"}}, "title": "满意度回复配置"}, "aiAssistantTanliveAppChannelConfig": {"type": "object", "properties": {"nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "interactive_code_config": {"$ref": "#/definitions/aiAssistantInteractiveCodeConfig", "title": "互动暗号配置"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"$ref": "#/definitions/aiAssistantGraphParseConfig", "title": "图谱解析配置"}, "assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "kefu_config": {"$ref": "#/definitions/aiAssistantKefuConfig", "title": "人工客服"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}, "app_id": {"type": "string", "title": "应用ID"}}, "title": "碳LIVE应用助手渠道配置"}, "aiAssistantTanliveWebChannelConfig": {"type": "object", "properties": {"nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "website_config": {"$ref": "#/definitions/aiAssistantWebsiteConfig", "title": "网站配置"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "interactive_code_config": {"$ref": "#/definitions/aiAssistantInteractiveCodeConfig", "title": "互动暗号配置"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"$ref": "#/definitions/aiAssistantGraphParseConfig", "title": "图谱解析配置"}, "assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "switch_assistant_id": {"type": "string", "format": "uint64", "title": "切换助手ID"}, "kefu_config": {"$ref": "#/definitions/aiAssistantKefuConfig", "title": "人工客服"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}}, "title": "碳LIVE Web助手渠道配置"}, "aiAssistantUserLabelConfig": {"type": "object", "properties": {"tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "绑定标签id"}, "tag_names": {"type": "array", "items": {"type": "string"}, "title": "绑定标签的名称"}}, "title": "助手用户打标配置"}, "aiAssistantV2": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "create_by": {"$ref": "#/definitions/baseIdentity", "title": "创建人"}, "update_by": {"$ref": "#/definitions/baseIdentity", "title": "更新人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}, "config": {"$ref": "#/definitions/aiAssistantConfig", "title": "配置详情"}, "is_draft": {"type": "boolean", "title": "是否为草稿"}, "batch_no": {"type": "string", "title": "批次号"}}, "title": "助手信息"}, "aiAssistantVisibleChainConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "visible": {"type": "array", "items": {"type": "string"}, "title": "可见字段"}}, "title": "链路查询"}, "aiAssistantWebsiteConfig": {"type": "object", "properties": {"route_path": {"type": "string", "title": "路由路径"}, "title": {"type": "string", "title": "标题"}}, "title": "网站配置"}, "aiAssistantWeixinChannelConfig": {"type": "object", "properties": {"nickname": {"type": "string", "title": "助手昵称"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "system_lang": {"type": "string", "title": "系统语言（已废弃，请使用system_languages）"}, "weixin_develop_config": {"$ref": "#/definitions/aiAssistantWeixinDevelopConfig", "title": "微信开发配置"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "kefu_config": {"$ref": "#/definitions/aiAssistantKefuConfig", "title": "人工客服配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "interactive_code_config": {"$ref": "#/definitions/aiAssistantInteractiveCodeConfig", "title": "互动暗号配置"}, "chat_idle_duration": {"type": "integer", "format": "int32", "title": "会话闲置超时时间（分钟）"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}}, "title": "微信渠道配置"}, "aiAssistantWeixinDevelopConfig": {"type": "object", "properties": {"corp_id": {"type": "string", "title": "企业ID"}, "open_kfid": {"type": "string", "title": "客服账号ID"}, "kf_url": {"type": "string", "title": "客服URL"}}, "title": "微信开发配置"}, "aiAssistantWelcomeMessage": {"type": "object", "properties": {"lang": {"type": "string", "title": "语言"}, "content": {"type": "string", "title": "内容"}}, "title": "欢迎语详情"}, "aiAssistantWelcomeMessageConfig": {"type": "object", "properties": {"messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantWelcomeMessage"}, "title": "配置列表"}}, "title": "欢迎语配置"}, "aiAssistantWhatsappChannelConfig": {"type": "object", "properties": {"system_lang": {"type": "string", "title": "系统语言（已废弃，请使用system_languages）"}, "whatsapp_develop_config": {"$ref": "#/definitions/aiAssistantWhatsappDevelopConfig", "title": "WhatsApp开发配置"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "chat_idle_duration": {"type": "integer", "format": "int32", "title": "会话闲置超时时间（分钟）"}, "nickname": {"type": "string", "title": "昵称"}, "avatar_url": {"type": "string", "title": "头像"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}}, "title": "Whatsapp助手渠道配置"}, "aiAssistantWhatsappDevelopConfig": {"type": "object", "properties": {"business_number": {"type": "string", "title": "Business number"}}, "title": "WhatsApp开发配置"}, "aiAutoChunkPara": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}, "chunk_config": {"$ref": "#/definitions/aiAssistantChunkConfig", "title": "分段配置"}, "dry_run": {"type": "boolean", "title": "仅预览"}}, "title": "自动分段参数"}, "aiChatCurrentState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: 当前会话未结束\n - 2: 当前会话已经被其他会话替代-已结束\n - 3: 当前会话已经超过可聊天规定时限-已结束"}, "aiChatLiveAgentInfo": {"type": "object", "properties": {"live_agents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/ChatLiveAgentInfoLiveAgentInfo"}, "title": "人工客服列表"}, "current_live_agent": {"$ref": "#/definitions/ChatLiveAgentInfoLiveAgentInfo", "title": "当前正在会话中的客服"}, "chat_id": {"type": "string", "format": "uint64", "title": "会话ID"}}}, "aiChatMessageContentFilterItem": {"type": "object", "properties": {"field": {"type": "string"}, "value": {"type": "string"}, "tags": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageTag"}}}}, "aiChatMessageFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "文件ID"}, "url": {"type": "string", "title": "文件URL"}, "state": {"$ref": "#/definitions/aiChatMessageFileState"}, "parsed_url": {"type": "string", "title": "解析后的URL"}}, "title": "文件信息"}, "aiChatMessageFileState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: 文件解析中\n - 2: 文件解析成功\n - 3: 文件解析失败"}, "aiChatMessageLog": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64"}, "sql_query": {"type": "string"}, "enhancement": {"type": "string"}, "gpt": {"type": "string"}, "ref": {"type": "string"}, "code": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "config_snapshot": {"type": "string"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "request_text": {"type": "string"}, "fetch_resp_time": {"type": "string", "format": "date-time"}}}, "aiChatMessageOperator": {"type": "object", "properties": {"operation_type": {"$ref": "#/definitions/aiChatOperationType"}, "stop_text": {"type": "string"}, "stop_think": {"type": "string"}, "stop_chunk_state": {"type": "integer", "format": "int32"}, "operation_params": {"type": "string"}, "hash_id": {"type": "string"}}}, "aiChatMessageState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13], "title": "- 1: 消息未发送标识\n - 2: 消息已经发送标识\n - 3: 默认消息已经发送标识\n - 4: 努力思考\n - 5: 整理答案\n - 6: 停止回答\n - 7: 回答推流中\n - 8: 推流全部完成\n - 9: 思考过程推流\n - 10: 切片推流完成\n - 12: 参考资料消息\n - 13: 建议问题"}, "aiChatMessageTask": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64"}, "pipeline_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/aiPipelineTaskState"}, "order": {"type": "integer", "format": "int32"}}}, "aiChatMessageType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "title": "- 1: 用户消息\n - 2: 数据库查询\n - 3: collection查询\n - 4: 搜索引擎查询\n - 5: 系统错误\n - 6: 敏感信息错误\n - 7: 超时错误\n - 8: 取消回复\n - 9: 预定会话聊天回复\n - 10: 人工客服消息\n - 11: 多模态消息\n - 12: 清空上下文\n - 13: 建议问题\n - 14: 转人工二维码\n - 15: 回答草稿"}, "aiChatModelOption": {"type": "object", "properties": {"model": {"type": "string", "title": "模型"}, "disable_in_console": {"type": "boolean", "title": "是否在用户后台禁用"}, "support_think": {"type": "boolean", "title": "是否支持思考"}, "only_non_stream": {"type": "boolean", "title": "仅支持非流式"}}, "title": "聊天模型选项"}, "aiChatOperationType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 正常回答\n - 2: 停止回答\n - 3: 停止思考\n - 4: 重新回答", "title": "会话操作类型"}, "aiChatSendRecordInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "记录id"}, "message_id": {"type": "string", "format": "uint64", "title": "消息id"}, "message_type": {"$ref": "#/definitions/aiChatMessageType", "title": "消息类型"}, "content": {"type": "string", "title": "记录内容"}, "send_date": {"type": "string", "format": "date-time", "title": "发送时间"}, "record_type": {"$ref": "#/definitions/aiAiRecordType", "title": "类型 1 用户询问 2 助手回答-text 3 助手回答-menu 4 助手回答建议问题菜单消息"}, "message_rating_scale": {"$ref": "#/definitions/aiRatingScale", "title": "记录对应的消息的评价信息"}, "show_type": {"type": "integer", "format": "int32", "title": "显示状态"}, "reject_reason": {"type": "string"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "image_url": {"type": "array", "items": {"type": "string"}}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}}}, "aiChatSuggestLog": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64"}, "collections": {"type": "string"}, "gpt": {"type": "string"}, "config_snapshot": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "request_type": {"type": "string"}}}, "aiChatSupportType": {"type": "integer", "format": "int32", "enum": [1, 2], "title": "- 1: ai聊天\n - 2: 客服聊天"}, "aiChatType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: web\n - 2: 微信公众号\n - 3: whatsapp\n - 4: 小程序", "title": "后续会改为使用助手表的channel，如果是新功能就不要使用这个枚举了"}, "aiChunkItem": {"type": "object", "properties": {"start": {"type": "integer", "format": "int64", "title": "起始索引位置"}, "len": {"type": "integer", "format": "int64", "title": "内容长度"}, "content": {"type": "string", "title": "分段内容"}}, "title": "文档分段信息"}, "aiCollection": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiCollectionQA": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "question": {"type": "string", "title": "问题"}, "answer": {"type": "string", "title": "答案"}, "assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistant"}, "title": "用户端"}, "state": {"$ref": "#/definitions/aiDocState", "title": "状态(不包含助手对应的状态)"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}, "title": "参考资料"}, "hit_count": {"type": "integer", "format": "int64", "title": "命中次数"}, "create_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "update_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "update_date": {"type": "string", "format": "date-time"}, "create_date": {"type": "string", "format": "date-time"}, "version_lag": {"type": "string", "format": "uint64", "title": "同步至ai侧的版本滞后数，为0代表已同步"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}, "question_oversize": {"type": "boolean", "title": "问题是否超长"}, "has_repeated": {"type": "boolean", "title": "是否有重复"}}}, "aiCollectionTextFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "file_name": {"type": "string", "title": "文件/文本名称"}, "text": {"type": "string", "title": "文件/文本内容"}, "assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistant"}, "title": "用户端"}, "state": {"$ref": "#/definitions/aiDocState", "title": "状态(不包含助手对应的状态)"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "url": {"type": "string", "title": "文件url"}, "hit_count": {"type": "integer", "format": "int64", "title": "命中次数"}, "ugc_type": {"$ref": "#/definitions/baseDataType", "title": "ugc类型"}, "ugc_id": {"type": "string", "format": "uint64", "title": "ugc的id"}, "create_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "update_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "update_date": {"type": "string", "format": "date-time"}, "create_date": {"type": "string", "format": "date-time"}, "version_lag": {"type": "string", "format": "uint64", "title": "同步至ai侧的版本滞后数，为0代表已同步"}, "parse_progress": {"type": "number", "format": "float", "title": "解析进度，0.5 = 50%"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "is_copy": {"type": "boolean", "title": "是否为副本"}, "ugc_title": {"type": "string", "title": "UGC标题"}, "is_system": {"type": "boolean", "title": "是否为系统数据"}, "content_state": {"$ref": "#/definitions/aiDocContentState", "title": "内容状态"}, "copies": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionTextFile"}, "title": "副本列表"}, "ugc_hashid": {"type": "string", "title": "ugc的hashid"}, "shared_states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "has_over_sized_tables": {"type": "boolean", "title": "知识提示\n是否有超长标题表格"}, "has_repeated": {"type": "boolean", "title": "是否内容重复（租户内）"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "data_source_state": {"type": "integer", "format": "int64", "title": "外部数据源信息"}}}, "aiContributor": {"type": "object", "properties": {"type": {"$ref": "#/definitions/baseIdentityType"}, "text": {"type": "string", "title": "自定义纯文本 或者 个人/团队/运营端账户名称"}, "id": {"type": "string", "format": "uint64", "title": "账户id\n个人账户: 个人账户id\n团队账户: 团队账户id\n运营端: 运营端账户id"}, "level": {"$ref": "#/definitions/teamTeamLevel", "title": "共创等级"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "full_name": {"type": "string", "title": "team full name"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "贡献者所在的助手"}}}, "aiCustomLabel": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "type": {"$ref": "#/definitions/aiCustomLabelType", "title": "标签类型"}, "key": {"type": "string", "title": "标签key"}, "value": {"$ref": "#/definitions/aiLabelValue"}, "create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "update_by": {"type": "string", "format": "uint64", "title": "更新人"}, "next_label_name": {"type": "string"}, "next_label_id": {"type": "string", "format": "uint64"}}, "title": "AI对话的标签"}, "aiCustomLabelObjectType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "description": "- 1: AI 对话\n - 2: 知识库文档文本与文件\n - 3: 知识库文档QA\n - 4: 腾讯云文档导入的文本文件\n - 5: SQL数据导入的文本文件", "title": "标签对象类型"}, "aiCustomLabelType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "description": "- 1: 任意纯文本\n - 2: 字符串枚举(单选)\n - 3: 字符串枚举(多选)\n - 4: int\n - 5: uint\n - 6: float\n - 7: 年\n - 8: 年月\n - 9: 年月日\n - 10: 日期时间(年月日和时间)\n - 11: 时间", "title": "标签类型"}, "aiDocAssistantState": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/aiDocState"}, "is_shared": {"type": "integer", "format": "int64", "title": "1否 2是"}}}, "aiDocChunkTask": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "任务ID"}, "state": {"$ref": "#/definitions/aiDocChunkTaskState", "title": "任务状态"}}, "title": "文段分段任务"}, "aiDocChunkTaskState": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 执行中\n - 2: 已完成", "title": "文档分段任务状态"}, "aiDocContentState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "description": "- 1: 同步中\n - 2: 有更新\n - 3: 已下架\n - 4: 已删除\n - 5: 人工", "title": "文档内容状态"}, "aiDocDataSource": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: tanlive ugc数据\n - 2: tanlive 知识库数据\n - 3: 腾讯云文档\n - 4: SQL 数据", "title": "知识的数据来源"}, "aiDocFileDownloadAsRef": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 可下载\n - 2: 仅显示文件名\n - 3: 直接发送\n - 4: 隐藏", "title": "文本文件是否能作为参考资料下载"}, "aiDocMatchPattern": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "description": "- 1: 大模型召回\n - 2: 完全匹配\n - 3: 忽略标点匹配\n - 4: 未命中\n - 5: 包含关键字", "title": "匹配模式"}, "aiDocParseMode": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 智能解析\n - 2: 文件解析\n - 3: 图像解析\n - 4: 表格解析", "title": "文档解析模式"}, "aiDocReference": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "引用doc的参考文献"}, "name": {"type": "string"}, "url": {"type": "string"}, "text": {"type": "string", "title": "纯文本参考文献"}, "show_type": {"$ref": "#/definitions/aiDocFileDownloadAsRef", "title": "仅用于控制对话展示"}}, "title": "参考文献"}, "aiDocState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9], "description": "- 1: 启用\n - 2: 禁用\n - 3: 解析中\n - 4: 解析失败\n - 5: 文件上传中\n - 6: 文件上传成功\n - 7: 删除中\n - 8: 解除了助手绑定（在助手中已删除）\n - 9: 重新解析中", "title": "知识库文档状态"}, "aiDocType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: QA\n - 2: 文本\n - 3: 文件", "title": "文档类型"}, "aiEmbeddingModelCount": {"type": "object", "properties": {"collection_lang": {"type": "string", "title": "向量化模型"}, "count": {"type": "integer", "format": "int64", "title": "数量"}, "embedding_model_name": {"type": "string", "title": "向量化模型名称"}}, "title": "向量化模型计数"}, "aiEmbeddingModelOption": {"type": "object", "properties": {"model": {"type": "string", "title": "模型名称"}, "tech_seg_min_tokens": {"type": "integer", "format": "int32", "title": "技术最小分段长度（token）"}, "tech_seg_max_tokens": {"type": "integer", "format": "int32", "title": "技术最大分段长度（token）"}, "tech_overlap_min_tokens": {"type": "integer", "format": "int32", "title": "技术overlap最小长度（token）"}, "tech_overlap_max_tokens": {"type": "integer", "format": "int32", "title": "技术overlap最大长度（token）"}, "user_seg_min_tokens": {"type": "integer", "format": "int32", "title": "用户最小分段长度默认值（token）"}, "user_seg_max_tokens": {"type": "integer", "format": "int32", "title": "用户最大分段长度默认值（token）"}, "user_overlap_tokens": {"type": "integer", "format": "int32", "title": "用户overlap长度默认值（token）"}, "zh_min_tokens_per_char": {"type": "number", "format": "float", "title": "1个中文字符约对应多少个token（下限）"}, "zh_max_tokens_per_char": {"type": "number", "format": "float", "title": "1个中文字符约对应多少个token（上限）"}, "en_min_tokens_per_char": {"type": "number", "format": "float", "title": "1个英文字符约对应多少个token（下限）"}, "en_max_tokens_per_char": {"type": "number", "format": "float", "title": "1个英文字符约对应多少个token（上限）"}, "embedding_vector_length": {"type": "integer", "format": "int32", "title": "embedding向量长度"}, "recommended": {"type": "boolean", "title": "是否推荐"}, "name": {"type": "string", "title": "名称"}, "zh_default": {"$ref": "#/definitions/aiAssistantChunkConfig", "title": "中文默认配置"}, "en_default": {"$ref": "#/definitions/aiAssistantChunkConfig", "title": "英文默认配置"}}, "title": "向量化模型选项"}, "aiEventChatMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64"}, "text": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}}, "link": {"type": "string"}, "question_id": {"type": "string", "format": "uint64"}, "sql_query": {"type": "string"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgc"}}, "create_by": {"type": "string", "format": "uint64"}, "reject_reason": {"type": "string"}, "state": {"$ref": "#/definitions/aiChatMessageState"}, "process_time": {"$ref": "#/definitions/baseTimeRange"}, "lang": {"type": "string"}, "assistant_id": {"type": "string", "format": "uint64"}, "doc_final_query": {"type": "string"}, "ask_type": {"$ref": "#/definitions/aiQuestionAskType"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "suggest_count": {"type": "integer", "format": "int32"}, "start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "image_url": {"type": "array", "items": {"type": "string"}}, "show_type": {"type": "integer", "format": "int32"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "think": {"type": "string"}, "wait_answer": {"type": "boolean"}, "think_duration": {"type": "integer", "format": "int32"}, "answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "prompt_type": {"type": "string", "title": "问题类型"}, "answers": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}, "title": "关联的回答"}, "last_operation_type": {"$ref": "#/definitions/aiChatOperationType", "title": "最后一次操作"}, "feedback_id": {"type": "string", "format": "uint64", "title": "教学反馈ID"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "is_file_ready": {"type": "boolean", "title": "文件解析成功"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}}, "is_agent_command": {"type": "boolean", "title": "是否是agent回答"}}}, "aiFeedback": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "chat_id": {"type": "string", "format": "uint64", "title": "对话ID"}, "message_id": {"type": "string", "format": "uint64", "title": "消息ID"}, "question": {"type": "string", "title": "问题"}, "answer": {"type": "string", "title": "答案"}, "state": {"$ref": "#/definitions/aiFeedbackState", "title": "状态"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "assistant_name": {"type": "string", "title": "助手名称"}, "question_id": {"type": "string", "format": "uint64", "title": "原始问题ID"}, "answer_rating": {"$ref": "#/definitions/aiFeedbackAnswerRating", "title": "回答评价"}, "hit_expected_doc": {"type": "boolean", "title": "是否命中预期知识"}, "op_comment": {"$ref": "#/definitions/aiFeedbackComment", "title": "分析备注"}, "mgmt_feedback": {"$ref": "#/definitions/aiFeedbackComment", "title": "碳LIVE反馈"}, "mgmt_comment": {"$ref": "#/definitions/aiFeedbackComment", "title": "碳LIVE备注"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "has_op_feedback": {"type": "boolean", "title": "是否有运营反馈"}, "has_mgmt_feedback": {"type": "boolean", "title": "是否有碳LIVE反馈"}, "user_feedback_by": {"$ref": "#/definitions/baseIdentity", "title": "用户反馈人"}, "op_feedback_by": {"$ref": "#/definitions/baseIdentity", "title": "运营反馈人"}, "mgmt_feedback_by": {"$ref": "#/definitions/baseIdentity", "title": "碳LIVE反馈人"}, "user_feedback_at": {"type": "string", "format": "date-time", "title": "用户反馈时间"}, "op_feedback_at": {"type": "string", "format": "date-time", "title": "运营反馈时间"}, "mgmt_feedback_at": {"type": "string", "format": "date-time", "title": "碳LIVE反馈时间"}, "create_identity": {"$ref": "#/definitions/baseIdentity", "title": "创建人身份"}, "update_identity": {"$ref": "#/definitions/baseIdentity", "title": "更新人身份"}, "answer_id": {"type": "string", "format": "uint64", "title": "原始答案ID"}}, "title": "用户反馈"}, "aiFeedbackAction": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8], "description": "- 1: 已读（已废弃）\n - 2: 采用\n - 3: 创建用户反馈\n - 4: 创建运营反馈\n - 5: 创建碳LIVE反馈\n - 6: 更新用户反馈\n - 7: 更新运营反馈\n - 8: 更新碳LIVE反馈", "title": "反馈操作"}, "aiFeedbackAnswerRating": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 好\n - 2: 坏", "title": "回答评价"}, "aiFeedbackComment": {"type": "object", "properties": {"content": {"type": "string", "title": "内容"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/FeedbackCommentFile"}, "title": "文件列表"}}, "title": "反馈备注"}, "aiFeedbackLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "action": {"$ref": "#/definitions/aiFeedbackAction", "title": "操作"}, "create_identity": {"$ref": "#/definitions/baseIdentity", "title": "操作人"}, "create_date": {"type": "string", "format": "date-time", "title": "操作时间"}}, "title": "反馈操作日志"}, "aiFeedbackReference": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "参考文献ID"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "type": {"$ref": "#/definitions/aiReferenceType", "title": "类型"}, "url": {"type": "string", "title": "URL链接"}, "text": {"type": "string", "title": "文本内容"}, "file_path": {"type": "string", "title": "文件路径"}, "file_name": {"type": "string", "title": "文件名称"}, "create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "update_by": {"type": "string", "format": "uint64", "title": "更新人"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}}, "title": "反馈参考文献"}, "aiFeedbackState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 未读\n - 2: 已读\n - 3: 已采用", "title": "反馈状态"}, "aiFullAssistant": {"type": "object", "properties": {"assistant": {"$ref": "#/definitions/aiAssistantV2", "title": "助手详情"}, "terms_confirmed": {"type": "boolean", "title": "是否确认协议"}}, "title": "完整助手信息"}, "aiFullFeedbackLog": {"type": "object", "properties": {"log": {"$ref": "#/definitions/aiFeedbackLog", "title": "日志"}, "feedback": {"$ref": "#/definitions/aiFeedback", "title": "反馈"}, "original_question": {"$ref": "#/definitions/tanliveaiChatMessage", "title": "原始问题"}}, "title": "完整反馈"}, "aiInteractiveCode": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6], "description": "- 1: 人工\n - 2: 重新回答\n - 3: 清空上下文\n - 4: 读配料表\n - 5: 读检测报告\n - 6: 贡献知识库", "title": "互动暗号"}, "aiInteractiveCodeOption": {"type": "object", "properties": {"code": {"$ref": "#/definitions/aiInteractiveCode", "title": "编号"}, "text": {"type": "string", "title": "文本"}, "default_zh": {"type": "string", "title": "默认中文值"}, "default_en": {"type": "string", "title": "默认英文值"}, "deletable": {"type": "boolean", "title": "是否可删除"}, "default_pre_zh": {"type": "string", "title": "默认中文前缀"}, "default_pre_en": {"type": "string", "title": "默认英文前缀"}}, "title": "互动暗号选项"}, "aiLabelFilter": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "eq": {"$ref": "#/definitions/aiLabelValue"}, "gte": {"$ref": "#/definitions/aiLabelValue"}, "lte": {"$ref": "#/definitions/aiLabelValue"}, "in": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelValue"}}, "like": {"$ref": "#/definitions/aiLabelValue"}, "op": {"$ref": "#/definitions/aiLabelFilterOp"}}}, "aiLabelFilterOp": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "title": "- 1: 等于\n - 2: IN\n - 3: 大于等于\n - 4: 小于等于\n - 5: LIKE模糊搜索"}, "aiLabelValue": {"type": "object", "properties": {"int_value": {"type": "string", "format": "int64", "title": "整型值"}, "uint_value": {"type": "string", "format": "uint64", "title": "无符号整形"}, "float_value": {"type": "number", "format": "double", "title": "浮点型"}, "text_value": {"type": "string", "title": "任意纯文本"}, "enum_value": {"type": "string", "title": "字符串枚举(单选)"}, "enum_m_value": {"type": "string", "title": "字符串枚举(多选)"}, "y_value": {"type": "string", "format": "int64", "title": "年"}, "ym_value": {"type": "string", "format": "int64", "title": "年月"}, "ymd_value": {"type": "string", "format": "int64", "title": "年月日"}, "datetime_value": {"type": "string", "format": "int64", "title": "日期时间(年月日和时间)"}, "time_value": {"type": "string", "format": "int64", "title": "时间"}}, "title": "标签取值"}, "aiListDocFilterType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: qa\n - 2: 文本/文件\n - 3: 系统数据"}, "aiManualChunkPara": {"type": "object", "properties": {"assistant_chunks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantChunks"}, "title": "新分段列表"}}, "title": "文档手动分段参数"}, "aiMessageCollectionSnapshot": {"type": "object", "properties": {"start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiSearchCollectionItem"}}, "clean_chunks": {"type": "boolean"}, "message_id": {"type": "string", "format": "uint64"}}}, "aiMessageTag": {"type": "object", "properties": {"taggable_type": {"type": "integer", "format": "int32"}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "taggable_id": {"type": "string", "format": "uint64"}, "type": {"type": "integer", "format": "int32"}, "data_type": {"type": "integer", "format": "int32"}}}, "aiMessageUgc": {"type": "object", "properties": {"ugc_type": {"$ref": "#/definitions/baseDataType"}, "filter": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageContentFilterItem"}}, "cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgcCard"}}, "is_ugc_link": {"type": "boolean"}, "ugc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiMessageUgcCard": {"type": "object", "properties": {"name": {"type": "string"}, "logo_url": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "tags": {"type": "string"}}}, "aiOrderByLabel": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "desc": {"type": "boolean"}}}, "aiPipelineTaskState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: 进行中\n - 2: 已完成\n - 3: 失败"}, "aiQuestionAskType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "title": "- 1: 正常问答\n - 2: 重新回答(包括了用户输入的\"重新回答\"或者，用户输入了同样的问题)\n - 3: 继续回答(包括被错误识别为了转人工之后确认为继续回答，或者 发送条数到达上限后的继续回答)\n - 4: 预设问答\n - 5: 预设隐藏回答\n - 6: 文件问答\n - 7: 语音问答\n - 8: 图片问答\n - 9: 撤回问答（用户在微信端撤回消息）\n - 10: 匹配到QA的问答"}, "aiRatingScale": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 满意\n - 2: 一般\n - 3: 不满意", "title": "评价等级"}, "aiReferenceType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: URL\n - 2: 文本\n - 3: 文件", "title": "参考文献类型"}, "aiReqAcceptFeedback": {"type": "object", "properties": {"feedback_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "反馈ID"}}}, "aiReqAutoChunkDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "new_text": {"type": "string", "title": "新文本（如果文本未改动不需要传值）"}, "auto_para": {"$ref": "#/definitions/aiAutoChunkPara", "title": "自动分段参数"}}}, "aiReqBatchCreateAssistant": {"type": "object", "properties": {"configs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantConfig"}, "title": "配置列表"}, "is_draft": {"type": "boolean", "title": "是否保存为草稿"}}}, "aiReqBatchUpdateAssistant": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqBatchUpdateAssistantItem"}, "title": "助手列表"}, "is_draft": {"type": "boolean", "title": "是否保存为草稿（已发布的助手忽略该参数）"}, "batch_no": {"type": "string", "title": "批次号\n如果指定了批次号，items里的助手必须都属于该批次号，且批次号内的助手必须为草稿，允许新增、删除；未指定批次号时items里的助手必须为非草稿，仅允许更新"}}}, "aiReqBatchUpdateAssistantItem": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "config": {"$ref": "#/definitions/aiAssistantConfig", "title": "配置详情"}, "mask": {"type": "string", "title": "Field mask"}}}, "aiReqBatchUpdateDocAttr": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "mask": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef", "title": "参考资料下载方式"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "修改关联的助手，增加的助手启用/禁用状态和已有的助手状态一致"}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}, "title": "参考资料"}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqCloneDoc": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqCreateAssistant": {"type": "object", "properties": {"config": {"$ref": "#/definitions/aiAssistantConfig", "title": "配置列表"}, "is_draft": {"type": "boolean", "title": "是否保存为草稿"}}}, "aiReqCreateAssistantShare": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "doc_id": {"type": "string", "format": "uint64"}, "query_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享给个人的ID列表"}, "team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享给团队的ID列表"}}}, "aiReqCreateDocQuery": {"type": "object", "properties": {"doc": {"$ref": "#/definitions/aiReqListTextFiles"}, "qa": {"$ref": "#/definitions/aiReqListQA"}}}, "aiReqCreateDocShareConfigSender": {"type": "object", "properties": {"share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "share_user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "share_team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqCreateQA": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "state": {"$ref": "#/definitions/aiDocState"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}}}, "aiReqCreateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateQA"}}}}, "aiReqCreateSystemDocCopy": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateSystemDocCopyItem"}, "title": "创建列表"}}}, "aiReqCreateSystemDocCopyItem": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "description": "// 文档内容\nstring text = 2 [(validator) = \"required\"];\n// 助手\nrepeated uint64 assistant_id = 3 [(validator) = \"omitempty,dive,required\"];\n// 贡献者\nrepeated tanlive.ai.Contributor contributor = 4 [(validator) = \"omitempty,dive,required\"];\n// 是否显示贡献者\nuint32 show_contributor = 5 [(validator) = \"required,oneof=1 2\"];", "title": "文档ID"}}}, "aiReqCreateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateTextFilesItem"}}}}, "aiReqCreateTextFilesItem": {"type": "object", "properties": {"file_name": {"type": "string", "title": "文件/文本名称"}, "text": {"type": "string", "title": "文件/文本内容"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "url": {"type": "string", "title": "文件url"}, "ugc_type": {"$ref": "#/definitions/baseDataType", "title": "ugc类型"}, "ugc_id": {"type": "string", "format": "uint64", "title": "ugc的id"}, "parsed_url": {"type": "string", "title": "文件解析后地址"}, "state": {"$ref": "#/definitions/aiDocState", "title": "状态 1: 启用 2: 停用 状态设置只对文本有效"}, "type": {"type": "integer", "format": "int64", "title": "类型 2:文本 3:文件"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode", "title": "解析模式"}, "data_source": {"$ref": "#/definitions/aiDocDataSource", "title": "数据源"}}}, "aiReqDeleteAssistant": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}, "batch_no": {"type": "string", "title": "批次号"}}}, "aiReqDeleteCustomLabels": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqDeleteDocs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqDeleteSystemDoc": {"type": "object", "properties": {"doc_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "文档ID"}}}, "aiReqDescribeChatRegionCode": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqDescribeChatSuggestLog": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64"}}}, "aiReqDescribeFeedbackRegionCode": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地区"}}}, "aiReqDisableSystemDoc": {"type": "object", "properties": {"doc_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "文档ID"}}}, "aiReqEnableSystemDoc": {"type": "object", "properties": {"doc_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "文档ID"}}}, "aiReqFindFeedback": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "用户反馈ID"}}}, "aiReqGetAssistantLogsPage": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "action": {"type": "array", "items": {"$ref": "#/definitions/aiAssistantAction"}, "title": "操作类型"}, "create_date": {"$ref": "#/definitions/baseTimeRange", "title": "操作时间范围"}}}, "aiReqGetAssistantsPage": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "所属用户ID"}, "team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "所属团队ID"}, "create_date": {"$ref": "#/definitions/baseTimeRange", "title": "创建时间范围"}, "channel": {"type": "array", "items": {"$ref": "#/definitions/aiAssistantChannel"}, "title": "渠道"}, "enabled": {"$ref": "#/definitions/baseBoolEnum", "title": "是否启用"}, "collection_lang": {"type": "array", "items": {"type": "string"}, "title": "知识库语言"}, "terms_confirmed": {"$ref": "#/definitions/baseBoolEnum", "title": "是否确认协议"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}, "title": "排序：create_date、update_date"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}, "route_path": {"type": "string", "title": "路由"}, "assistant_name": {"type": "string", "title": "助手名称"}, "model": {"type": "array", "items": {"type": "string"}, "title": "模型"}, "search_engine": {"type": "array", "items": {"type": "string"}, "title": "搜索引擎"}, "is_draft": {"$ref": "#/definitions/baseBoolEnum", "title": "是否为草稿"}, "batch_no": {"type": "string", "title": "批次号"}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "aiReqGetChatDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "chat_id"}, "region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "keyword": {"type": "string", "title": "消息内容搜索关键词"}, "send_range": {"$ref": "#/definitions/baseTimeRange", "title": "创建时间区间"}, "question_id": {"type": "string", "format": "uint64", "title": "问题ID"}}}, "aiReqGetChatMessageDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "region": {"$ref": "#/definitions/baseRegion"}}}, "aiReqGetChunkDocTasks": {"type": "object", "properties": {"doc_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "文档ID"}}}, "aiReqGetDocChunks": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}}}, "aiReqGetDocEmbeddingModels": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "aiReqGetFeedbackLogs": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "create_identity": {"$ref": "#/definitions/baseIdentity", "title": "操作人"}, "action": {"type": "array", "items": {"$ref": "#/definitions/aiFeedbackAction"}, "title": "操作类型"}, "create_date_range": {"$ref": "#/definitions/baseTimeRange", "title": "操作时间区间"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}, "title": "排序"}}}, "aiReqGetFeedbacks": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "feedback_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "反馈ID"}, "create_by": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "上传用户ID"}, "create_date_range": {"$ref": "#/definitions/baseTimeRange", "title": "创建时间区间"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}, "title": "排序"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手"}, "region_codes": {"type": "array", "items": {"type": "string"}}}}, "aiReqGetQaTip": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "文档ID"}}, "title": "获取QA知识提示请求"}, "aiReqGetTextFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "aiReqGetTextFileTip": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "文档ID"}}, "title": "获取文件文本知识提示请求"}, "aiReqListAssistant": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "type": {"$ref": "#/definitions/aiChatType"}, "name": {"type": "string", "title": "搜索名称关键词"}, "language": {"type": "string", "title": "语言，为空默认zh"}}}, "aiReqListAssistantCanShareDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64"}, "name": {"type": "string", "title": "搜索名称关键词"}, "language": {"type": "string", "title": "语言，为空默认zh"}}}, "aiReqListChat": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "string"}}, "filter": {"$ref": "#/definitions/ReqListChatFilter"}, "create_date_range": {"$ref": "#/definitions/baseTimeRange", "title": "创建时间区间"}, "update_date_range": {"$ref": "#/definitions/baseTimeRange", "title": "处理时间区间"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}, "title": "自定义标签kv对"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手id"}, "order_by_label": {"$ref": "#/definitions/aiOrderByLabel", "title": "自定义标签排序，只能当个标签排序"}}}, "aiReqListChatLiveAgent": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}, "region": {"$ref": "#/definitions/baseRegion", "title": "地区"}}}, "aiReqListCollectionFileName": {"type": "object", "properties": {"search": {"type": "string", "title": "文件名模糊搜索匹配"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "full_search": {"type": "array", "items": {"type": "string"}, "title": "文件名精确匹配搜索"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "aiReqListContributor": {"type": "object", "properties": {"search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "aiReqListCustomLabel": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType"}, "id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqListDocByRef": {"type": "object", "properties": {"ref": {"type": "array", "items": {"type": "string"}}}}, "aiReqListOperator": {"type": "object", "properties": {"search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}, "creator": {"type": "boolean", "title": "是否为创建人，false代表更新人，true代表创建人"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "aiReqListQA": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "在助手中"}, "state": {"$ref": "#/definitions/aiDocState"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiOperator"}}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "search": {"type": "string"}, "excluded_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "不在助手中"}, "group_repeated": {"type": "boolean", "title": "重复 doc 紧凑排序"}, "show_contributor": {"type": "integer", "format": "int64"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "order_by_label": {"$ref": "#/definitions/aiOrderByLabel", "title": "自定义标签排序，只能当个标签排序"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "create_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiOperator"}}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}, "tip_filter": {"$ref": "#/definitions/aiReqListQATipFilter"}, "share_team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享的团队id"}, "share_user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享的用户id"}}}, "aiReqListQATipFilter": {"type": "object", "properties": {"warning": {"type": "boolean"}}, "title": "知识提示过滤条件，用来筛选问题超长等问题的记录"}, "aiReqListTextFiles": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "state": {"$ref": "#/definitions/aiDocState"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiOperator"}}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "search": {"$ref": "#/definitions/ReqListTextFilesSearch"}, "excluded_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "不在助手中"}, "group_repeated": {"type": "boolean", "title": "重复 doc 紧凑排序"}, "show_contributor": {"type": "integer", "format": "int64"}, "is_system": {"type": "boolean", "title": "是否为系统文档"}, "with_copies": {"type": "boolean", "title": "是否查询副本"}, "ugc_type": {"type": "array", "items": {"$ref": "#/definitions/baseDataType"}, "title": "UGC模块"}, "content_state": {"type": "array", "items": {"$ref": "#/definitions/aiDocContentState"}, "title": "内容状态"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "order_by_label": {"$ref": "#/definitions/aiOrderByLabel", "title": "自定义标签排序，只能当个标签排序"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "create_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiOperator"}}, "parse_mode": {"type": "array", "items": {"$ref": "#/definitions/aiDocParseMode"}}, "parse_state": {"$ref": "#/definitions/aiDocState", "title": "查询解析失败的数据"}, "tip_filter": {"$ref": "#/definitions/aiReqListTextFilesTipFilter"}, "data_source": {"$ref": "#/definitions/aiDocDataSource", "title": "数据源"}, "data_source_state": {"type": "integer", "format": "int64", "title": "数据源同步状态"}}}, "aiReqListTextFilesTipFilter": {"type": "object", "properties": {"warning": {"type": "boolean", "title": "警告条件组"}}, "title": "知识提示过滤条件"}, "aiReqListeDocShareConfigSender": {"type": "object", "properties": {"name": {"type": "string", "title": "搜索名称关键词"}, "language": {"type": "string", "title": "语言，为空默认zh"}}}, "aiReqManualChunkDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "new_text": {"type": "string", "title": "新文本（如果文本未改动不需要传值）"}, "manual_para": {"$ref": "#/definitions/aiManualChunkPara", "title": "手动分段参数"}}}, "aiReqModifyCustomLabels": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType"}}}, "aiReqOnOffDocs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "state": {"$ref": "#/definitions/aiDocState"}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqProxyChatHtmlUrl": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}}}, "aiReqReparseTextFiles": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqSearchChat": {"type": "object", "properties": {"text": {"type": "string"}, "question_id": {"type": "string", "format": "uint64"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "top_n": {"type": "integer", "format": "int64", "title": "top n"}, "threshold": {"type": "number", "format": "float", "title": "阈值"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "clean_chunks": {"type": "boolean"}, "text_recall_query": {"$ref": "#/definitions/aiTextRecallQuery", "title": "关键词召回匹配目标"}, "text_recall_pattern": {"$ref": "#/definitions/aiTextRecallPattern", "title": "关键词召回模式"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}}}, "aiReqSearchCollection": {"type": "object", "properties": {"search": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "doc_type": {"$ref": "#/definitions/aiDocType"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "text_weight": {"type": "number", "format": "float"}, "text_recall_top_n": {"type": "integer", "format": "int64"}, "text_recall_query": {"$ref": "#/definitions/aiTextRecallQuery", "title": "关键词召回匹配目标"}, "text_recall_pattern": {"$ref": "#/definitions/aiTextRecallPattern", "title": "关键词召回模式"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}, "clean_chunks": {"type": "boolean"}, "temperature": {"type": "number", "format": "float"}}}, "aiReqSwitchChatLiveAgent": {"type": "object", "properties": {"live_agent_id": {"type": "string", "format": "uint64", "title": "人工客服id"}, "chat_id": {"type": "string", "format": "uint64", "title": "会话id"}, "region": {"$ref": "#/definitions/baseRegion", "title": "地区"}}}, "aiReqSyncFixChatMessageCollection": {"type": "object", "properties": {"chat_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqUpdateAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "config": {"$ref": "#/definitions/aiAssistantConfig", "title": "配置详情"}, "mask": {"type": "string", "title": "Field mask"}, "is_draft": {"type": "boolean", "title": "是否保存为草稿（已发布的助手忽略该参数）"}}}, "aiReqUpdateChatNoticeConf": {"type": "object", "properties": {"conf": {"$ref": "#/definitions/aiAiAssistantNoticeConf"}}}, "aiReqUpdateObjectCustomLabels": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "打标签的对象 id"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}, "title": "自定义标签"}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType", "title": "打标签的对象类型"}}}, "aiReqUpdateQA": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "id": {"type": "string", "format": "uint64"}, "mask": {"type": "string"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}}}, "aiReqUpdateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqUpdateQA"}}}}, "aiReqUpdateTextFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "file_name": {"type": "string"}, "text": {"type": "string"}, "url": {"type": "string"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "ugc_id": {"type": "string", "format": "uint64"}, "parsed_url": {"type": "string", "title": "解析后的文件url"}, "mask": {"type": "string"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}}}, "aiReqUpdateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqUpdateTextFile"}}}}, "aiReqUpsertMgmtFeedback": {"type": "object", "properties": {"answer_id": {"type": "string", "format": "uint64", "title": "通过答案ID更新或创建反馈"}, "answer_rating": {"$ref": "#/definitions/aiFeedbackAnswerRating", "title": "AI回答评价"}, "mgmt_feedback": {"$ref": "#/definitions/aiFeedbackComment", "title": "碳LIVE反馈"}, "mgmt_comment": {"$ref": "#/definitions/aiFeedbackComment", "title": "碳LIVE内部备注"}, "feedback_id": {"type": "string", "format": "uint64", "title": "通过反馈ID更新反馈"}, "mgmt_doc_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "预期命中的知识"}}}, "aiReqValidateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqValidateQAsItem"}}}}, "aiReqValidateQAsItem": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "state": {"$ref": "#/definitions/aiDocState"}}}, "aiRspAcceptFeedback": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspAcceptFeedbackResult"}, "title": "结果"}}}, "aiRspAcceptFeedbackResult": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspAutoChunkDoc": {"type": "object", "properties": {"chunks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChunkItem"}, "title": "分段列表"}}}, "aiRspBatchCreateAssistant": {"type": "object", "properties": {"batch_no": {"type": "string", "title": "批次号"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID列表"}}}, "aiRspBatchUpdateDocAttr": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspCloneDoc": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspCreateAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}}}, "aiRspCreateDocQuery": {"type": "object", "properties": {"query_id": {"type": "string", "format": "uint64"}, "is_empty": {"type": "boolean"}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspCreateDocShare": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspCreateFeedback": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}}}, "aiRspCreateSystemDocCopy": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspCreateSystemDocCopyResult"}, "title": "结果列表"}}}, "aiRspCreateSystemDocCopyResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspCreateTextFiles": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspDeleteDocs": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspDeleteSystemDoc": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspDeleteSystemDocResult"}, "title": "结果列表"}}}, "aiRspDeleteSystemDocResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspDescribeChatRegionCode": {"type": "object", "properties": {"region_codes": {"type": "array", "items": {"type": "string"}}}}, "aiRspDescribeChatSuggestLog": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatSuggestLog"}}}}, "aiRspDescribeFeedbackRegionCode": {"type": "object", "properties": {"region_codes": {"type": "array", "items": {"type": "string"}}}}, "aiRspDisableSystemDoc": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspDisableSystemDocResult"}, "title": "结果列表"}}}, "aiRspDisableSystemDocResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspEnableSystemDoc": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspEnableSystemDocResult"}, "title": "结果列表"}}}, "aiRspEnableSystemDocResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspFindFeedback": {"type": "object", "properties": {"feedback": {"$ref": "#/definitions/aiFeedback", "title": "反馈详情"}, "references": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFeedbackReference"}, "title": "参考文献"}, "original_question": {"$ref": "#/definitions/tanliveaiChatMessage", "title": "原始问题"}, "original_answer": {"$ref": "#/definitions/tanliveaiChatMessage", "title": "原始回答"}, "expected_docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}, "title": "预期命中的知识"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}, "mgmt_user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}, "title": "运营端用户卡片列表"}, "expected_mgmt_docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}, "title": "预期命中的知识（碳LIVE运营）"}}}, "aiRspGetAssistantLogsPage": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantLog"}, "title": "日志列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}, "mgmt_user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}, "title": "运营端用户卡片列表"}}}, "aiRspGetAssistantOptions": {"type": "object", "properties": {"chat_model": {"type": "array", "items": {"type": "string"}, "title": "对话模型"}, "chat_or_sql_model": {"type": "array", "items": {"type": "string"}, "title": "ChatOrSql模型"}, "graph_parse_mode": {"type": "array", "items": {"type": "string"}, "title": "解析图谱模型"}, "search_engine": {"type": "array", "items": {"type": "string"}, "title": "搜索引擎"}, "interactive_code": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiInteractiveCodeOption"}, "title": "互动暗号"}, "visible_chain": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiVisibleChainOption"}, "title": "链路查询"}, "ask_suggestion_model": {"type": "array", "items": {"type": "string"}, "title": "问题建议模型"}, "embedding_model": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEmbeddingModelOption"}, "title": "向量化模型"}, "chat_model_v2": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatModelOption"}, "title": "对话模型"}, "search_engine_v2": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiSearchEngineOption"}, "title": "对话模型"}, "mini_white_url": {"type": "array", "items": {"type": "string"}, "title": "小程序URL白名单"}}}, "aiRspGetAssistantsPage": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}, "title": "助手列表"}, "total_account": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}, "mgmt_user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}, "title": "运营端用户卡片列表"}}}, "aiRspGetChatDetail": {"type": "object", "properties": {"chat_detail": {"$ref": "#/definitions/tanlivebff_mgmtaiChatDetail"}, "totalCount": {"type": "integer", "format": "int64"}}}, "aiRspGetChatMessageDetail": {"type": "object", "properties": {"message": {"$ref": "#/definitions/aiEventChatMessage"}, "collection_items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtaiSearchCollectionItem"}}, "logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageLog"}}, "collection_time": {"$ref": "#/definitions/baseTimeRange"}, "clean_chunks": {"type": "boolean"}}}, "aiRspGetChunkDocTasks": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocChunkTask"}, "title": "任务列表"}}}, "aiRspGetDocChunks": {"type": "object", "properties": {"assistant_chunks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantChunks"}, "title": "助手的分段列表"}}}, "aiRspGetDocEmbeddingModels": {"type": "object", "properties": {"embedding_models": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEmbeddingModelCount"}, "title": "向量化模型列表"}}}, "aiRspGetFeedbackLogs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFullFeedbackLog"}, "title": "日志列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}, "mgmt_user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}, "title": "运营端用户卡片列表"}}}, "aiRspGetFeedbacks": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspGetFeedbacksItem"}, "title": "反馈列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}, "mgmt_user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}, "title": "运营端用户卡片列表"}}}, "aiRspGetFeedbacksItem": {"type": "object", "properties": {"feedback": {"$ref": "#/definitions/aiFeedback", "title": "反馈详情"}, "references": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFeedbackReference"}, "title": "参考文献"}, "original_question": {"$ref": "#/definitions/tanliveaiChatMessage", "title": "原始问题"}, "original_answer": {"$ref": "#/definitions/tanliveaiChatMessage", "title": "原始回答"}, "expected_docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}, "title": "预期命中的知识"}, "expected_mgmt_docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}, "title": "预期命中的知识（碳LIVE运营）"}}}, "aiRspGetQaTip": {"type": "object", "properties": {"question_over_size": {"type": "boolean", "title": "问题超长提示"}, "repeated": {"type": "array", "items": {"type": "string"}, "title": "内容重复信息"}}, "title": "获取QA知识提示响应"}, "aiRspGetTextFile": {"type": "object", "properties": {"item": {"$ref": "#/definitions/aiCollectionTextFile"}}}, "aiRspGetTextFileTip": {"type": "object", "properties": {"table_over_size": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiTextFileTipTableOverSize"}, "title": "表头过长的表格"}, "state": {"$ref": "#/definitions/aiDocState", "title": "解析状态"}, "repeated": {"type": "array", "items": {"type": "string"}, "title": "内容重复信息"}}, "title": "获取文件文本知识提示响应"}, "aiRspListAssistant": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistant"}}}}, "aiRspListAssistantCanShareDoc": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListAssistantCanShareDocSharedAssistant"}}}}, "aiRspListAssistantCanShareDocSharedAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "is_selected": {"type": "boolean"}}}, "aiRspListChat": {"type": "object", "properties": {"chats": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtaiChat"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListChatLiveAgent": {"type": "object", "properties": {"chat_live_agent": {"$ref": "#/definitions/aiChatLiveAgentInfo"}}}, "aiRspListCollection": {"type": "object", "properties": {"collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollection"}}}}, "aiRspListCollectionFileName": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListCollectionFileNameItem"}}}}, "aiRspListCollectionFileNameItem": {"type": "object", "properties": {"url": {"type": "string", "title": "文件绑定的url/path"}, "name": {"type": "string", "title": "文件名称"}, "id": {"type": "string", "format": "uint64", "title": "文件id"}}}, "aiRspListContributor": {"type": "object", "properties": {"contributors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}}}, "aiRspListCustomLabel": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListDocByRef": {"type": "object", "properties": {"docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListDocByRefDoc"}}}}, "aiRspListOperator": {"type": "object", "properties": {"operators": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtaiOperator"}}}}, "aiRspListQA": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionQA"}}}}, "aiRspListTextFiles": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionTextFile"}}, "fail_parse_count": {"type": "integer", "format": "int64"}}}, "aiRspListeDocShareConfigSender": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListeDocShareConfigSenderSharedAssistant"}}}}, "aiRspListeDocShareConfigSenderSharedAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "is_selected": {"type": "boolean"}}}, "aiRspManualChunkDoc": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64", "title": "任务ID"}}}, "aiRspModifyCustomLabels": {"type": "object"}, "aiRspOnOffDocs": {"type": "object", "properties": {"pre_repeat_collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspOnOffDocsRepeatCollection"}}, "repeat_collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspOnOffDocsRepeatCollection"}}, "qa_num_exceed": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspOnOffDocsQaContainsMatchCount"}}, "async": {"type": "boolean"}}}, "aiRspProxyChatHtmlUrl": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspProxyChatHtmlUrlContent"}}}}, "aiRspReparseTextFiles": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspSearchChat": {"type": "object", "properties": {"message": {"$ref": "#/definitions/aiEventChatMessage"}, "user_id": {"type": "string", "format": "uint64"}, "is_op": {"type": "boolean", "title": "是否是推送运营端"}, "is_only_search": {"type": "boolean", "title": "是否仅搜索"}}}, "aiRspSearchCollection": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}, "total_count": {"type": "integer", "format": "int64"}, "match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "item": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtaiSearchCollectionItem"}}}}, "aiRspSwitchChatLiveAgent": {"type": "object", "properties": {"state": {"$ref": "#/definitions/aiSwitchChatState", "title": "切换结果"}}}, "aiRspValidateQAs": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspValidateQAsErr"}}}}, "aiSearchCollectionType": {"type": "integer", "format": "int32", "enum": [1, 2], "title": "- 1: 向量搜索\n - 2: 文本搜索"}, "aiSearchEngineOption": {"type": "object", "properties": {"value": {"type": "string", "title": "值"}, "name": {"type": "string", "title": "名称"}, "name_en": {"type": "string", "title": "英文名称"}}, "title": "搜索引擎选项"}, "aiSwitchChatState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "title": "- 1: 切换成功\n - 2: 会话已结束\n - 3: 人工坐席离线\n - 4: 会话信息错误（需要重新开启一个会话或者再问一个问题）\n - 5: 人工坐席不存在"}, "aiTextFileTipTableOverSize": {"type": "object", "properties": {"header": {"type": "string", "title": "表头"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手 id"}, "assistant_name": {"type": "string", "title": "助手中文名称"}, "assistant_name_en": {"type": "string", "title": "助手英文名称"}, "table_title": {"type": "string", "title": "表格标题"}}}, "aiTextRecallPattern": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 短语匹配\n - 2: 字匹配\n - 3: 英文模糊匹配", "title": "关键词召回模式"}, "aiTextRecallQuery": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 在知识库的\"QA\"中召回\n - 2: 仅在知识库的\"Q\"中召回", "title": "QA关键词召回目标"}, "aiVisibleChainOption": {"type": "object", "properties": {"field": {"type": "string", "title": "字段"}, "name": {"type": "string", "title": "名称"}, "name_en": {"type": "string", "title": "英文名称"}, "uncheck": {"type": "boolean", "title": "是否默认不选中"}}, "title": "链路查询选项"}, "baseBoolEnum": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: false\n - 2: true", "title": "bool值的枚举"}, "baseDataType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9], "description": "- 1: 团队\n - 2: 产品\n - 3: 资源\n - 4: 图谱\n - 5: 定向推送\n - 6: 用户个人\n - 7: 图谱AI\n - 8: 帮助中心文档\n - 9: AI助手", "title": "数据类型"}, "baseIdentity": {"type": "object", "properties": {"identity_type": {"$ref": "#/definitions/baseIdentityType", "title": "身份类型"}, "identity_id": {"type": "string", "format": "uint64", "title": "身份ID"}, "name": {"type": "string", "title": "名字"}, "extra_id": {"type": "string", "format": "uint64", "title": "额外ID（团队类型表示用户ID）"}}, "title": "身份"}, "baseIdentityType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 门户端用户\n - 2: 团队\n - 3: 运营端用户\n - 4: 自定义", "title": "身份类型"}, "baseOrderBy": {"type": "object", "properties": {"column": {"type": "string", "title": "列名"}, "desc": {"type": "boolean", "title": "是否倒序"}}, "title": "排序"}, "baseRegion": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 国内\n - 2: 海外", "title": "地域"}, "baseTimeRange": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time", "title": "开始时间"}, "end": {"type": "string", "format": "date-time", "title": "结束时间"}}, "title": "时间范围"}, "errorsAiError": {"type": "integer", "format": "int32", "enum": [16001, 16002, 16003, 16004, 16005, 16006, 16007, 16008, 16009, 16010, 16011, 16012, 16013, 16014, 16015, 16016, 16017, 16018, 16019, 16020, 16021, 16022, 16023, 16024], "description": "- 16001: QA中的问题已经存在\n - 16002: 用户反馈已被采用\n - 16003: 用户反馈已标记已读\n - 16004: 用户反馈状态不允许被采用\n - 16005: 用户chat不存在\n - 16006: 非法的文档状态转换\n - 16007: 问题审核失败\n - 16008: 非法的AI租户\n - 16009: 非法的文档内容状态\n - 16010: doc中的文本/文件已经存在\n - 16011: 非法的自定义列转换\n - 16012: 助手不存在\n - 16013: 助手名称已存在\n - 16014: 助手英文名称已存在\n - 16015: 助手路由已存在\n - 16016: 助手已禁用\n - 16017: 助手当前功能已禁用\n - 16018: 助手应用ID已存在\n - 16019: 小程序code无效\n - 16020: 不是知识的贡献者\n - 16021: 助手客服用户名重复\n - 16022: 文档有运行中的分段任务\n - 16023: doc外部源Token已过期\n - 16024: 文件剪存文件夹不存在", "title": "AI服务错误\n范围：[16000, 17000)"}, "iamUserInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户ID"}, "username": {"type": "string", "title": "用户名"}, "image": {"type": "string", "title": "头像"}, "firm_id": {"type": "string", "format": "uint64", "title": "团队ID"}, "identity_set": {"$ref": "#/definitions/baseIdentityType", "title": "身份"}, "level": {"type": "string", "title": "等级"}, "country": {"type": "string", "title": "国家"}, "province": {"type": "string", "title": "省"}, "city": {"type": "string", "title": "市"}, "region_code": {"type": "string", "title": "地区编码"}, "timezone": {"type": "string", "title": "时区"}, "union_id": {"type": "string", "title": "微信unionID"}, "phone": {"type": "string", "title": "手机号"}, "phone_hash": {"type": "string", "title": "手机号哈希"}, "nick_name": {"type": "string", "title": "用户昵称"}}, "title": "用户信息"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "tanliveaiChatMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64"}, "text": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "link": {"type": "string"}, "question_id": {"type": "string", "format": "uint64"}, "sql_query": {"type": "string"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgc"}}, "create_by": {"type": "string", "format": "uint64"}, "reject_reason": {"type": "string"}, "state": {"$ref": "#/definitions/aiChatMessageState"}, "doc_names": {"type": "array", "items": {"type": "string"}}, "assistant_id": {"type": "string", "format": "uint64"}, "lang": {"type": "string"}, "collection_snapshot": {"$ref": "#/definitions/aiMessageCollectionSnapshot"}, "ask_type": {"$ref": "#/definitions/aiQuestionAskType"}, "start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "suggest_question": {"type": "array", "items": {"type": "string"}}, "final_query": {"type": "string"}, "live_agent_name": {"type": "string"}, "prompt_prefix": {"type": "string"}, "image_url": {"type": "array", "items": {"type": "string"}}, "show_type": {"type": "integer", "format": "int32"}, "final_search_query": {"type": "string"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageLog"}}, "ref_file_names": {"type": "array", "items": {"type": "string"}}, "think": {"type": "string", "title": "深度思考"}, "publish_hash_id": {"type": "string", "title": "推送消息hash id"}, "history_ignore_id": {"type": "string", "format": "uint64"}, "wait_answer": {"type": "boolean"}, "think_duration": {"type": "integer", "format": "int32"}, "answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "prompt_type": {"type": "string", "title": "问题类型"}, "feedback_id": {"type": "string", "format": "uint64", "title": "教学反馈ID"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "last_operation_type": {"$ref": "#/definitions/aiChatOperationType", "title": "最后一次操作"}, "answer_draft_id": {"type": "string", "format": "uint64", "title": "answer草稿id"}, "task": {"$ref": "#/definitions/aiChatMessageTask", "title": "当前所执行的任务"}, "is_file_ready": {"type": "boolean", "title": "文件解析成功"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}, "title": "问题附件"}, "last_operator": {"$ref": "#/definitions/aiChatMessageOperator", "title": "最后一次操作"}}}, "tanliveaiChatMessageDoc": {"type": "object", "properties": {"ugc_id": {"type": "string", "format": "uint64"}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "id": {"type": "string", "format": "uint64"}, "rag_filename": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_type": {"type": "integer", "format": "int64"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "file_name": {"type": "string"}, "index_text": {"type": "string"}, "text": {"type": "string"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "url": {"type": "string"}, "update_by": {"$ref": "#/definitions/tanliveaiOperator"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "tanliveaiOperator": {"type": "object", "properties": {"type": {"$ref": "#/definitions/baseIdentityType"}, "id": {"type": "string", "format": "uint64", "title": "用户账户: 用户id\n团队账户: 用户id\n运营端账户: 运营端用户id"}, "user_id": {"type": "string", "format": "uint64", "title": "type为团队用户时，可选的传入个人的 id"}}}, "tanliveaiSearchCollectionItem": {"type": "object", "properties": {"text": {"type": "string"}, "question": {"type": "string"}, "score": {"type": "number", "format": "float"}, "file_name": {"type": "string"}, "url": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"$ref": "#/definitions/tanliveaiOperator"}, "id": {"type": "string"}, "type": {"$ref": "#/definitions/aiSearchCollectionType", "title": "召回类型"}, "is_related": {"type": "boolean", "title": "是否相关"}, "doc_type": {"$ref": "#/definitions/aiDocType", "title": "文件类型"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_id": {"type": "string", "format": "uint64"}, "ref_name": {"type": "string"}, "ref_url": {"type": "string"}, "doc_name": {"type": "string"}}}, "tanlivebff_mgmtaiChat": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "title": {"type": "string"}, "create_by": {"$ref": "#/definitions/iamUserInfo"}, "update_date": {"type": "string", "format": "date-time"}, "create_date": {"type": "string", "format": "date-time"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "assistant_id": {"type": "string", "format": "uint64"}, "assistant_name": {"type": "string"}, "chat_state": {"$ref": "#/definitions/aiChatCurrentState"}, "support_type": {"$ref": "#/definitions/aiChatSupportType", "title": "当前服务状态"}, "question_cnt": {"type": "integer", "format": "int64", "title": "对话中问题数量"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}, "title": "自定义标签kv对"}, "reject_job_result": {"type": "integer", "format": "int64"}, "region_code": {"type": "string"}, "is_manual": {"type": "integer", "format": "int32", "title": "是否转过人工服务"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "doc_hits": {"type": "number", "format": "float"}, "avg_duration": {"type": "number", "format": "float"}}}, "tanlivebff_mgmtaiChatDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "title": {"type": "string"}, "messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}}, "region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "create_by": {"$ref": "#/definitions/iamUserInfo"}, "finish_date": {"type": "string", "format": "date-time"}, "create_date": {"type": "string", "format": "date-time"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "chat_state": {"$ref": "#/definitions/aiChatCurrentState"}, "support_type": {"$ref": "#/definitions/aiChatSupportType", "title": "当前服务状态"}, "assistant_avatar": {"type": "string", "title": "微信客服助手头像"}, "records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatSendRecordInfo"}, "title": "非web端时通过次字段返回消息详情"}, "assistant_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_mgmtaiOperator": {"type": "object", "properties": {"type": {"$ref": "#/definitions/baseIdentityType"}, "id": {"type": "string", "format": "uint64", "title": "用户id"}, "username": {"type": "string", "title": "用户名称"}, "team_name": {"type": "string", "title": "用户所属团队名称"}, "user_id": {"type": "string", "format": "uint64", "title": "用户id，只有为团队用户时，才需要"}}}, "tanlivebff_mgmtaiSearchCollectionItem": {"type": "object", "properties": {"text": {"type": "string"}, "question": {"type": "string"}, "score": {"type": "number", "format": "float"}, "file_name": {"type": "string"}, "url": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "id": {"type": "string"}, "type": {"$ref": "#/definitions/aiSearchCollectionType", "title": "召回类型"}, "is_related": {"type": "boolean", "title": "是否相关"}, "doc_type": {"$ref": "#/definitions/aiDocType", "title": "文件类型"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_id": {"type": "string", "format": "uint64"}, "doc_name": {"type": "string"}}}, "tanliveiamUserCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户ID"}, "username": {"type": "string", "title": "用户名"}, "image": {"type": "string", "title": "头像"}, "level": {"type": "string", "title": "等级"}}, "title": "用户卡片"}, "tanlivemgmtUserCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户ID"}, "username": {"type": "string", "title": "用户名"}, "remark_name": {"type": "string", "title": "备注名"}}, "title": "运营端用户卡片"}, "teamTeamCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "团队ID"}, "short_name": {"type": "string", "title": "简称"}, "full_name": {"type": "string", "title": "主体名称"}, "is_verified": {"type": "boolean", "title": "是否认证"}, "brief_intro": {"type": "string", "title": "一句话介绍"}, "level": {"$ref": "#/definitions/teamTeamLevel", "title": "共创等级"}, "logo_url": {"type": "string", "title": "团队LOGO"}, "is_published": {"type": "boolean", "title": "是否已发布"}}, "title": "团队卡片"}, "teamTeamLevel": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: CONTRIBUTOR\n - 2: COMMITTER\n - 3: MAIN<PERSON>INER", "title": "团队共创等级"}}}