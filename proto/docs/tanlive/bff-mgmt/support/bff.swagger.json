{"swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/support/bff.proto", "version": "version not set"}, "tags": [{"name": "SupportBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/support/get_hash_ids": {"post": {"operationId": "SupportBff_GetHashIds", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supportRspGetHashIds"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supportReqGetHashIds"}}], "tags": ["SupportBff"]}}, "/support/proxy": {"post": {"summary": "代理请求访问网页", "operationId": "SupportBff_Proxy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supportRspProxy"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supportReqProxy"}}], "tags": ["SupportBff"]}}}, "definitions": {"RspProxyContent": {"type": "object", "properties": {"url": {"type": "string"}, "title": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "supportReqGetHashIds": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "supportReqProxy": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}}}}, "supportRspGetHashIds": {"type": "object", "properties": {"hash_ids": {"type": "array", "items": {"type": "string"}}}}, "supportRspProxy": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspProxyContent"}}}}}}