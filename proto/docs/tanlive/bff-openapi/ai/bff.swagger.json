{"swagger": "2.0", "info": {"title": "tanlive/bff-openapi/ai/bff.proto", "version": "version not set"}, "tags": [{"name": "AiCapi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"aiRspUploadDocs": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}, "title": "请求示例（需删除）"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}