{"swagger": "2.0", "info": {"title": "tanlive/bff-web/team/bff.proto", "version": "version not set"}, "tags": [{"name": "TeamBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/team/search_teams_in_ai_share_setting": {"post": {"summary": "搜索团队（AI知识分享默认设置场景）", "operationId": "TeamBff_SearchTeamsInAiShareSetting", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/teamRspSearchTeamsInAiShareSetting"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/teamReqSearchTeamsInAiShareSetting"}}], "tags": ["TeamBff"]}}, "/team/update_ugc_custom_route": {"post": {"summary": "更新ugc自定义路由", "operationId": "TeamBff_UpdateUgcCustomRoute", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/teamReqUpdateUgcCustomRoute"}}], "tags": ["TeamBff"]}}}, "definitions": {"baseUgcState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8], "description": "- 1: 草稿。用户前台：草稿；运营后台：草稿\n - 2: 自动审核中。用户前台：处理中；运营后台：处理中\n - 3: 人工审核中（用户前台：处理中；运营后台：待处理）\n - 4: 人工审核中-可疑（用户前台：处理中；运营后台：可疑，待处理）\n - 5: 审核已通过（用户前台：下架存档；运营后台：下架存档）\n - 6: 已发布（用户前台：已发布；运营后台：已发布）\n - 7: 审核已驳回（用户前台：已驳回；运营后台：已驳回）\n - 8: 申诉中（用户前台：申诉中；运营后台：申诉待处理）", "title": "UGC状态"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "tanlivebff_webteamFullTeam": {"type": "object", "properties": {"team_info": {"$ref": "#/definitions/teamTeamInfo", "title": "团队信息"}}, "title": "完整团队信息"}, "teamReqSearchTeamsInAiShareSetting": {"type": "object", "properties": {"keyword": {"type": "string", "title": "搜索关键词"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}}}, "teamReqUpdateUgcCustomRoute": {"type": "object", "properties": {"ugc_id": {"type": "string", "format": "uint64"}, "ugc_route": {"type": "string"}}}, "teamRspSearchTeamsInAiShareSetting": {"type": "object", "properties": {"teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webteamFullTeam"}, "title": "团队列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}}}, "teamTeamInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "团队ID"}, "create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "update_by": {"type": "string", "format": "uint64", "title": "最后编辑者"}, "update_date": {"type": "string", "format": "date-time", "title": "最后编辑时间"}, "main_id": {"type": "string", "format": "uint64", "title": "主数据ID"}, "holder_id": {"type": "string", "format": "uint64", "title": "团队持有者ID"}, "full_name": {"type": "string", "title": "团队主体名称"}, "short_name": {"type": "string", "title": "团队简称"}, "level": {"$ref": "#/definitions/teamTeamLevel", "title": "共创等级"}, "ugc_state": {"$ref": "#/definitions/baseUgcState", "title": "UGC状态"}, "is_virtual": {"type": "boolean", "title": "是否为虚拟团队"}, "is_draft": {"type": "boolean", "title": "是否为草稿"}, "is_verified": {"type": "boolean", "title": "是否已认证"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "is_deregistered": {"type": "boolean", "title": "是否已注销"}, "source_lang": {"type": "string", "title": "源语言"}, "detected_lang": {"type": "string", "title": "检测语言"}, "principal_country": {"type": "string", "title": "主体国家"}, "logo_url": {"type": "string", "title": "团队LOGO"}, "brief_intro": {"type": "string", "title": "一句话介绍"}}, "title": "团队信息"}, "teamTeamLevel": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: CONTRIBUTOR\n - 2: COMMITTER\n - 3: MAIN<PERSON>INER", "title": "团队共创等级"}}}