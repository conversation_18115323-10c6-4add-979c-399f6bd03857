{"swagger": "2.0", "info": {"title": "tanlive/bff-web/notify/bff.proto", "version": "version not set"}, "tags": [{"name": "NotifyGuestBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/notify/get_email_send_status": {"post": {"operationId": "NotifyGuestBff_GetEmailSendStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/notifyRspGetEmailSendStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/notifyReqGetEmailSendStatus"}}], "tags": ["NotifyGuestBff"]}}, "/notify/get_sms_send_status": {"post": {"operationId": "NotifyGuestBff_GetSmsSendStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/notifyRspGetSmsSendStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/notifyReqGetSmsSendStatus"}}], "tags": ["NotifyGuestBff"]}}}, "definitions": {"notifyReqGetEmailSendStatus": {"type": "object", "properties": {"task_no": {"type": "string", "title": "任务ID"}}}, "notifyReqGetSmsSendStatus": {"type": "object", "properties": {"task_no": {"type": "string", "title": "任务ID"}}}, "notifyRspGetEmailSendStatus": {"type": "object", "properties": {"event": {"type": "string", "title": "事件类型：\ndelivering 投递中\ndeferred 邮件被收件人邮件服务商延迟传递，正在重试中\ndelivered 递送成功，如您未收到请检查垃圾箱\ndropped 邮件无法送达，请尝试其他邮箱地址\nbounce 收件人邮件服务商拒收此邮件，请检查邮箱地址或尝试其他邮箱地址"}, "timestamp": {"type": "integer", "format": "int32", "title": "事件产生的时间戳"}}}, "notifyRspGetSmsSendStatus": {"type": "object", "properties": {"report_status": {"type": "string", "title": "状态：SENDING（发送中）、SUCCESS（成功）、FAIL（失败）"}, "errmsg": {"type": "string", "title": "用户接收短信状态码错误信息，参考：https://cloud.tencent.com/document/api/382/59177#.E5.9B.9E.E6.89.A7.E7.8A.B6.E6.80.81.E9.94.99.E8.AF.AF.E7.A0.81"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}