{"swagger": "2.0", "info": {"title": "tanlive/bff-web/iam/bff.proto", "version": "version not set"}, "tags": [{"name": "IamBff"}, {"name": "IamGuestBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/iam/active_my_email": {"post": {"summary": "激活我的邮箱", "operationId": "IamBff_ActiveMyEmail", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqActiveMyEmail"}}], "tags": ["IamBff"]}}, "/iam/bind_my_google": {"post": {"summary": "绑定我的谷歌账号", "operationId": "IamBff_BindMyGoogle", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqBindMyGoogle"}}], "tags": ["IamBff"]}}, "/iam/bind_my_weixin_by_oauth2": {"post": {"summary": "绑定我的微信（在微信浏览器中）", "operationId": "IamBff_BindMyWeixinByOauth2", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqBindMyWeixinByOauth2"}}], "tags": ["IamBff"]}}, "/iam/create_bind_my_weixin_qrcode": {"post": {"summary": "创建绑定我的微信二维码", "operationId": "IamBff_CreateBindMyWeixinQrcode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateBindMyWeixinQrcode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreateBindMyWeixinQrcode"}}], "tags": ["IamBff"]}}, "/iam/create_email_2fa": {"post": {"summary": "创建邮箱二次验证", "operationId": "IamBff_CreateEmailTfa", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreateEmailTfa"}}], "tags": ["IamBff"]}}, "/iam/create_google_2fa": {"post": {"summary": "创建谷歌二次认证", "operationId": "IamBff_CreateGoogleTfa", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreateGoogleTfa"}}], "tags": ["IamBff"]}}, "/iam/create_password_2fa": {"post": {"summary": "创建密码二次验证", "operationId": "IamBff_CreatePasswordTfa", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreatePasswordTfa"}}], "tags": ["IamBff"]}}, "/iam/create_phone_2fa": {"post": {"summary": "创建手机二次验证", "operationId": "IamBff_CreatePhoneTfa", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreatePhoneTfa"}}], "tags": ["IamBff"]}}, "/iam/create_weixin_2fa_qrcode": {"post": {"summary": "创建微信二次验证二维码", "operationId": "IamBff_CreateWeixinTfaQrcode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateWeixinTfaQrcode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["IamBff"]}}, "/iam/create_weixin_browser_2fa": {"post": {"summary": "创建微信浏览器二次验证", "operationId": "IamBff_CreateWeixinBrowserTfa", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreateWeixinBrowserTfa"}}], "tags": ["IamBff"]}}, "/iam/get_bind_my_weixin_state": {"post": {"summary": "查询绑定我的微信状态", "operationId": "IamBff_GetBindMyWeixinState", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetBindMyWeixinState"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqGetBindMyWeixinState"}}], "tags": ["IamBff"]}}, "/iam/get_create_weixin_2fa_state": {"post": {"summary": "查询创建微信二次验证状态", "operationId": "IamBff_GetCreateWeixinTfaState", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetCreateWeixinTfaState"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqGetCreateWeixinTfaState"}}], "tags": ["IamBff"]}}, "/iam/get_google_auth_url": {"post": {"summary": "获取谷歌认证URL", "operationId": "IamGuestBff_GetGoogleAuthUrl", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetGoogleAuthUrl"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqGetGoogleAuthUrl"}}], "tags": ["IamGuestBff"]}}, "/iam/get_my_2fa": {"post": {"summary": "查询我的的二次验证", "operationId": "IamBff_GetMyTfa", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetMyTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["IamBff"]}}, "/iam/login": {"post": {"summary": "登入", "operationId": "IamGuestBff_Login", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspLogin"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqLogin"}}], "tags": ["IamGuestBff"]}}, "/iam/logout": {"post": {"summary": "登出", "operationId": "IamBff_Logout", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["IamBff"]}}, "/iam/modify_my_email": {"post": {"summary": "修改我的邮箱", "operationId": "IamBff_ModifyMyEmail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspModifyMyEmail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqModifyMyEmail"}}], "tags": ["IamBff"]}}, "/iam/modify_my_password": {"post": {"summary": "修改我的密码", "operationId": "IamBff_ModifyMyPassword", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqModifyMyPassword"}}], "tags": ["IamBff"]}}, "/iam/modify_my_phone": {"post": {"summary": "修改我的手机号", "operationId": "IamBff_ModifyMyPhone", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqModifyMyPhone"}}], "tags": ["IamBff"]}}, "/iam/modify_my_username": {"post": {"summary": "修改我的用户名", "operationId": "IamBff_ModifyMyUsername", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqModifyMyUsername"}}], "tags": ["IamBff"]}}, "/iam/send_2fa_otp": {"post": {"summary": "发送二次验证的验证码", "operationId": "IamBff_SendTfaOtp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspSendTfaOtp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqSendTfaOtp"}}], "tags": ["IamBff"]}}, "/iam/unbind_my_google": {"post": {"summary": "解绑我的谷歌账号", "operationId": "IamBff_UnbindMyGoogle", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqUnbindMyGoogle"}}], "tags": ["IamBff"]}}, "/iam/unbind_my_weixin": {"post": {"summary": "解绑我的微信", "operationId": "IamBff_UnbindMyWeixin", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqUnbindMyWeixin"}}], "tags": ["IamBff"]}}, "/notify/send_one_time_password": {"post": {"summary": "发送验证码（未登录）", "operationId": "IamGuestBff_SendAuthOtp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspSendAuthOtp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqSendAuthOtp"}}], "tags": ["IamGuestBff"]}}}, "definitions": {"ReqLoginAccountCredentials": {"type": "object", "properties": {"username": {"type": "string", "title": "用户名"}, "password": {"type": "string", "title": "密码"}}}, "ReqLoginEmailCredentials": {"type": "object", "properties": {"email": {"type": "string", "title": "邮箱"}, "auth_code": {"type": "string", "title": "验证码"}}}, "ReqLoginPhoneCredentials": {"type": "object", "properties": {"phone": {"type": "string", "title": "手机号"}, "auth_code": {"type": "string", "title": "验证码"}}}, "ReqLoginThirdCredentials": {"type": "object", "properties": {"third_ticket": {"type": "string", "title": "第三方凭证"}}}, "baseIdentityType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 门户端用户\n - 2: 团队\n - 3: 运营端用户\n - 4: 自定义", "title": "身份类型"}, "baseTcloudCaptcha": {"type": "object", "properties": {"ticket": {"type": "string", "title": "前端回调函数返回的用户验证票据"}, "randstr": {"type": "string", "title": "前端回调函数返回的随机字符串"}}, "title": "腾讯云验证码参数"}, "iamBindWeixinState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 等待扫码\n - 2: 绑定成功\n - 3: 当前微信已绑定其他账号", "title": "绑定微信状态"}, "iamCreateWeixinTfaState": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 等待扫码\n - 2: 创建成功", "title": "创建微信二次验证状态"}, "iamGoogleAuthScene": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 谷歌登录\n - 2: 绑定谷歌账号\n - 3: 创建谷歌二次验证", "title": "谷歌认证场景"}, "iamLoginType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 用户名登录\n - 2: 手机验证码登录\n - 3: 邮箱验证码登录\n - 4: 第三方登录", "title": "登录类型"}, "iamMenu": {"type": "object", "properties": {"item_code": {"type": "string"}, "item_name": {"type": "string"}, "level": {"type": "integer", "format": "int64"}, "item_desc": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}, "can_view": {"type": "boolean"}, "can_authorize": {"type": "boolean"}, "only_holder": {"type": "boolean"}, "auth_action": {"type": "integer", "format": "int64"}}, "title": "菜单信息"}, "iamOtpReceiverKind": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 手机号码\n - 2: 邮件", "title": "验证码接收者类型"}, "iamOtpScene": {"type": "integer", "format": "int32", "enum": [1, 2, 4, 6, 7, 8, 9], "description": "- 1: 注册\n - 2: 修改手机号\n - 4: 忘记密码\n - 6: 团队企业邮箱验证\n - 7: 登录场景\n - 8: 二次验证\n - 9: 修改邮箱", "title": "一次性密码使用场景（兼容老版的枚举值）"}, "iamReqActiveMyEmail": {"type": "object", "properties": {"value": {"type": "string", "title": "激活邮件参数值"}}}, "iamReqBindMyGoogle": {"type": "object", "properties": {"code": {"type": "string", "title": "谷歌回调code"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqBindMyWeixinByOauth2": {"type": "object", "properties": {"code": {"type": "string", "title": "微信静默授权返回的code"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqCreateBindMyWeixinQrcode": {"type": "object", "properties": {"tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqCreateEmailTfa": {"type": "object", "properties": {"otp": {"type": "string", "title": "一次性密码"}}}, "iamReqCreateGoogleTfa": {"type": "object", "properties": {"code": {"type": "string", "title": "谷歌静默授权返回的code"}}}, "iamReqCreatePasswordTfa": {"type": "object", "properties": {"password": {"type": "string", "title": "密码（需要RSA加密）"}}}, "iamReqCreatePhoneTfa": {"type": "object", "properties": {"otp": {"type": "string", "title": "一次性密码"}}}, "iamReqCreateWeixinBrowserTfa": {"type": "object", "properties": {"code": {"type": "string", "title": "微信静默授权返回的code"}}}, "iamReqGetBindMyWeixinState": {"type": "object", "properties": {"scene_str": {"type": "string", "title": "场景值"}}}, "iamReqGetCreateWeixinTfaState": {"type": "object", "properties": {"scene_str": {"type": "string", "title": "场景值"}}}, "iamReqGetGoogleAuthUrl": {"type": "object", "properties": {"scene": {"$ref": "#/definitions/iamGoogleAuthScene", "title": "认证场景"}, "state": {"type": "string", "title": "state参数"}}}, "iamReqLogin": {"type": "object", "properties": {"login_type": {"$ref": "#/definitions/iamLoginType", "title": "登录类型"}, "account_credentials": {"$ref": "#/definitions/ReqLoginAccountCredentials", "title": "密码登录凭证"}, "phone_credentials": {"$ref": "#/definitions/ReqLoginPhoneCredentials", "title": "手机号登录凭证"}, "email_credentials": {"$ref": "#/definitions/ReqLoginEmailCredentials", "title": "邮箱登录凭证"}, "third_credentials": {"$ref": "#/definitions/ReqLoginThirdCredentials", "title": "第三方登录凭证"}, "bind_third_ticket": {"type": "string", "title": "第三方凭证(用于绑定)"}, "tcloud_captcha": {"$ref": "#/definitions/baseTcloudCaptcha", "title": "腾讯云验证码参数"}}, "title": "登录"}, "iamReqModifyMyEmail": {"type": "object", "properties": {"email": {"type": "string", "title": "邮箱"}, "tfa_token": {"type": "string", "title": "2FA令牌"}, "tcloud_captcha": {"$ref": "#/definitions/baseTcloudCaptcha", "title": "腾讯云验证码参数"}}}, "iamReqModifyMyPassword": {"type": "object", "properties": {"password": {"type": "string", "title": "密码（需要RSA加密）"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqModifyMyPhone": {"type": "object", "properties": {"phone": {"type": "string", "title": "手机号"}, "tfa_token": {"type": "string", "title": "2FA令牌"}, "otp": {"type": "string", "title": "验证码"}}}, "iamReqModifyMyUsername": {"type": "object", "properties": {"username": {"type": "string", "title": "用户名（需要RSA加密）"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqSendAuthOtp": {"type": "object", "properties": {"scene": {"$ref": "#/definitions/iamOtpScene", "title": "场景（仅1、2、4、6、7、9有效）"}, "receiver_kind": {"$ref": "#/definitions/iamOtpReceiverKind", "title": "接收人类型"}, "receiver_phone": {"type": "string", "title": "接收人手机号"}, "receiver_email": {"type": "string", "title": "接收人邮箱"}, "tcloud_captcha": {"$ref": "#/definitions/baseTcloudCaptcha", "title": "腾讯云验证码参数"}}}, "iamReqSendTfaOtp": {"type": "object", "properties": {"receiver_kind": {"$ref": "#/definitions/iamOtpReceiverKind", "title": "接收者类型"}, "tcloud_captcha": {"$ref": "#/definitions/baseTcloudCaptcha", "title": "腾讯云验证码参数"}}}, "iamReqUnbindMyGoogle": {"type": "object", "properties": {"tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqUnbindMyWeixin": {"type": "object", "properties": {"tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamRspCreateBindMyWeixinQrcode": {"type": "object", "properties": {"qrcode": {"$ref": "#/definitions/iamWeixinQrcode", "title": "二维码信息"}}}, "iamRspCreateTfa": {"type": "object", "properties": {"tfa_token": {"$ref": "#/definitions/iamTfaToken", "title": "2FA令牌"}}}, "iamRspCreateWeixinTfaQrcode": {"type": "object", "properties": {"qrcode": {"$ref": "#/definitions/iamWeixinQrcode", "title": "二维码信息"}}}, "iamRspGetBindMyWeixinState": {"type": "object", "properties": {"state": {"$ref": "#/definitions/iamBindWeixinState", "title": "状态"}}}, "iamRspGetCreateWeixinTfaState": {"type": "object", "properties": {"state": {"$ref": "#/definitions/iamCreateWeixinTfaState", "title": "状态"}, "tfa_token": {"$ref": "#/definitions/iamTfaToken", "title": "2FA令牌"}}}, "iamRspGetGoogleAuthUrl": {"type": "object", "properties": {"url": {"type": "string", "title": "认证URL"}}}, "iamRspGetMyTfa": {"type": "object", "properties": {"tfa_token": {"$ref": "#/definitions/iamTfaToken", "title": "2FA令牌（未创建或已过期返回null）"}}}, "iamRspLogin": {"type": "object", "properties": {"user_info": {"$ref": "#/definitions/iamRspLoginUserInfo", "title": "用户信息结构体"}, "menus": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamMenu"}, "title": "该用户可显示的菜单列表"}, "new_user": {"type": "boolean", "title": "该账号是否为未登录过状态"}}}, "iamRspLoginUserInfo": {"type": "object", "properties": {"image": {"type": "string"}, "identity_set": {"$ref": "#/definitions/baseIdentityType"}, "level": {"type": "string"}, "user_name": {"type": "string"}, "firm_name": {"type": "string"}}}, "iamRspModifyMyEmail": {"type": "object", "properties": {"task_no": {"type": "string", "title": "任务ID"}, "sdk_error": {"type": "string", "title": "腾讯云SDK错误码"}}}, "iamRspSendAuthOtp": {"type": "object", "properties": {"task_no": {"type": "string", "title": "任务ID"}, "sdk_error": {"type": "string", "title": "腾讯云SDK错误码"}}}, "iamRspSendTfaOtp": {"type": "object", "properties": {"task_no": {"type": "string", "title": "任务ID"}, "sdk_error": {"type": "string", "title": "腾讯云SDK错误码"}}}, "iamTfaToken": {"type": "object", "properties": {"token": {"type": "string", "title": "认证TOKEN"}, "ttl": {"type": "string", "title": "有效期"}}, "title": "2FA(2-Factor Authentication) Token"}, "iamWeixinQrcode": {"type": "object", "properties": {"ticket": {"type": "string", "title": "获取的二维码ticket"}, "url": {"type": "string", "title": "二维码解析后的URL"}, "expires_in": {"type": "integer", "format": "int32", "title": "过期时间（单位秒）"}, "scene_str": {"type": "string", "title": "场景值"}}, "title": "微信二维码信息"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}