{"swagger": "2.0", "info": {"title": "tanlive/bff-web/search/bff.proto", "version": "version not set"}, "tags": [{"name": "SearchGuestBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/search/describe_atlas_search_option": {"post": {"operationId": "SearchGuestBff_DescribeAtlasSearchOptions", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeAtlasSearchOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeAtlasSearchOptions"}}], "tags": ["SearchGuestBff"]}}, "/search/describe_search_prompt": {"post": {"operationId": "SearchGuestBff_DescribeSearchPrompts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeSearchPrompts"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeSearchPrompts"}}], "tags": ["SearchGuestBff"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "searchReqDescribeAtlasSearchOptions": {"type": "object", "properties": {"target_type": {"$ref": "#/definitions/searchSearchOptionTargetType", "title": "搜索对应的模块类型 1:团队,2:产品"}}}, "searchReqDescribeSearchPrompts": {"type": "object", "properties": {"target_type": {"$ref": "#/definitions/searchSearchOptionTargetType", "title": "搜索对应的模块类型"}, "refer_type": {"$ref": "#/definitions/searchSearchOptionReferType", "title": "搜索对应的筛选项类型"}}}, "searchRspDescribeAtlasSearchOptions": {"type": "object", "properties": {"options": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_websearchSearchOption"}}}}, "searchRspDescribeSearchPrompts": {"type": "object", "properties": {"prompts": {"$ref": "#/definitions/tanlivebff_websearchSearchPrompt"}}}, "searchSearchOptionReferType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 图谱\n - 2: 行业认可\n - 3: 资源系列", "title": "搜索对应的筛选项"}, "searchSearchOptionTargetType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 团队\n - 2: 产品\n - 3: 资源", "title": "搜索对应的模块"}, "tanlivebff_websearchSearchOption": {"type": "object", "properties": {"name": {"type": "string", "title": "筛选项名称"}, "refer_id": {"type": "string", "format": "int64", "title": "筛选项关联的id，比如图谱id"}}, "title": "搜索筛选项"}, "tanlivebff_websearchSearchPrompt": {"type": "object", "properties": {"content": {"type": "string"}}, "title": "搜索提示语"}}}