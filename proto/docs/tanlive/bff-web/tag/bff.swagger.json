{"swagger": "2.0", "info": {"title": "tanlive/bff-web/tag/bff.proto", "version": "version not set"}, "tags": [{"name": "TagGuestBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/tag/get_system_tags": {"post": {"summary": "获取全量系统标签", "operationId": "TagGuestBff_GetSystemTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspGetSystemTags"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqGetSystemTags"}}], "tags": ["TagGuestBff"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "tagReqGetSystemTags": {"type": "object", "properties": {"taggable_type": {"$ref": "#/definitions/tagTaggableType", "title": "标签索引类型"}, "language": {"type": "string", "title": "语言类型zh、en"}, "order_by": {"type": "array", "items": {"type": "string"}, "title": "排序"}, "notify_type": {"$ref": "#/definitions/tagTagCreateType", "title": "定向推送用的筛选条件 1 系统标签"}, "tag_name": {"type": "string", "title": "标签名称模糊搜索"}, "is_direct_display_id": {"type": "boolean", "title": "是否直接显示标签id，不会被编码为hashId"}}}, "tagRspGetSystemTags": {"type": "object", "properties": {"tag_set": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspGetSystemTagsTag"}}}}, "tagRspGetSystemTagsTag": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "name": {"type": "string", "title": "标签名称"}, "direct_display_id": {"type": "string", "format": "uint64", "title": "直接显示的标签id"}}}, "tagTagCreateType": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 系统标签\n - 2: 自创标签", "title": "TagCreateType 标签创建类型枚举"}, "tagTaggableType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 23, 101, 102, 103, 200, 201, 202, 203], "description": "- 1: 1 个人信息-我来自\n - 2: 2 团队类型\n - 3: 3 团队属性\n - 4: 4 团队行业\n - 5: 5 资源受众行业\n - 6: 6 产品技术-面向用户（场景）\n - 7: 7 资源类型\n - 8: 8 图谱-适用行业\n - 9: 9 图谱-适用场景\n - 10: 10 图谱类型\n - 11: 11 产品行业认可 --2.2迭代将 21资源-产品行业认可合并入11\n - 12: 12 团队行业认可 --2.2迭代将 22资源-团队行业认可合并入12\n - 14: 14 产品适用行业\n - 23: 23 资源系列\n - 101: 101 团队发展阶段\n - 102: 102 团队产品类型\n - 103: 103 受众团队融资阶段\n - 200: 200 后端用于业务及联的特殊类型，前端忽略\n - 201: 201 个人用户-用户标签\n - 202: 202 团队用户-用户标签\n - 203: 203 AI助手-给用户打标", "title": "标签类型"}}}