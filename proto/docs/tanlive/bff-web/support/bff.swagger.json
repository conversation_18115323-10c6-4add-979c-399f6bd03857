{"swagger": "2.0", "info": {"title": "tanlive/bff-web/support/bff.proto", "version": "version not set"}, "tags": [{"name": "SupportGuestBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/support/get_place_selector": {"post": {"operationId": "SupportGuestBff_GetPlaceSelector", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspGetPlaceSelector"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqGetPlaceSelector"}}], "tags": ["SupportGuestBff"]}}, "/support/proxy": {"post": {"operationId": "SupportGuestBff_Proxy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspProxy"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqProxy"}}], "tags": ["SupportGuestBff"]}}, "/support/search_places": {"post": {"operationId": "SupportGuestBff_SearchPlaces", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspSearchPlaces"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqSearchPlaces"}}], "tags": ["SupportGuestBff"]}}, "/support/send_map_request": {"post": {"operationId": "SupportGuestBff_SendMapRequest", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspSendMapRequest"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqSendMapRequest"}}], "tags": ["SupportGuestBff"]}}}, "definitions": {"RspProxyContent": {"type": "object", "properties": {"url": {"type": "string"}, "title": {"type": "string"}}}, "baseDataType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9], "description": "- 1: 团队\n - 2: 产品\n - 3: 资源\n - 4: 图谱\n - 5: 定向推送\n - 6: 用户个人\n - 7: 图谱AI\n - 8: 帮助中心文档\n - 9: AI助手", "title": "数据类型"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "searchReqGetPlaceSelector": {"type": "object", "properties": {"by_data_type": {"$ref": "#/definitions/supportGetPlaceCondByDataType", "title": "通过数据类型查询"}, "by_place_id": {"$ref": "#/definitions/supportGetPlaceCondByPlaceId", "title": "通过地点ID查询"}}}, "searchReqProxy": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}}}}, "searchReqSearchPlaces": {"type": "object", "properties": {"name": {"type": "string", "title": "按名称模糊搜索"}, "data_type": {"$ref": "#/definitions/baseDataType", "title": "数据类型"}, "data_field": {"type": "string", "title": "数据字段\n团队：location、service_region 资源：location、target_regions"}}}, "searchReqSendMapRequest": {"type": "object", "properties": {"platform": {"$ref": "#/definitions/supportMapPlatform", "title": "地图平台"}, "request": {"$ref": "#/definitions/supportMapRequest", "title": "请求"}, "platform1_client": {"$ref": "#/definitions/supportGoogleMapClient", "title": "谷歌地图客户端"}}}, "searchRspGetPlaceSelector": {"type": "object", "properties": {"places": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/supportPlace"}, "title": "地点列表"}}}, "searchRspProxy": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspProxyContent"}}}}, "searchRspSearchPlaces": {"type": "object", "properties": {"places": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/supportPlaceWithParents"}, "title": "地点列表"}}}, "searchRspSendMapRequest": {"type": "object", "properties": {"response": {"$ref": "#/definitions/supportMapResponse", "title": "响应"}}}, "supportAdAreaType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 洲\n - 2: 国家/地区\n - 3: 一级行政区划\n - 4: 二级行政区划", "title": "行政区划类型"}, "supportGetPlaceCondByDataType": {"type": "object", "properties": {"data_type": {"$ref": "#/definitions/baseDataType", "title": "数据类型"}, "data_field": {"type": "string", "title": "数据字段\n团队：location、service_region 资源：location、target_regions"}, "parent_id": {"type": "string", "format": "uint64", "title": "父级ID，不传返回大洲数据"}}, "title": "通过数据类型查询条件"}, "supportGetPlaceCondByPlaceId": {"type": "object", "properties": {"place_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}, "title": "通过地点ID查询条件"}, "supportGoogleMapClient": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: Maps客户端\n - 2: Places客户端", "title": "谷歌地图客户端"}, "supportMapHeader": {"type": "object", "properties": {"key": {"type": "string", "title": "键名"}, "value": {"type": "string", "title": "键值"}}}, "supportMapPlatform": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 谷歌地图\n - 2: 腾讯地图", "title": "地图平台"}, "supportMapRequest": {"type": "object", "properties": {"method": {"type": "string", "title": "请求方法"}, "path": {"type": "string", "title": "请求路径"}, "header": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/supportMapHeader"}, "title": "请求头"}, "body": {"type": "string", "title": "请求体"}}, "title": "地图请求"}, "supportMapResponse": {"type": "object", "properties": {"header": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/supportMapHeader"}, "title": "响应头"}, "body": {"type": "string", "title": "响应体"}}, "title": "地图响应"}, "supportPlace": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "name": {"type": "string", "title": "名称"}, "address": {"type": "string", "title": "地址"}, "lang": {"type": "string", "title": "语言"}, "is_ad_area": {"type": "boolean", "title": "是否为行政区划地点"}, "ad_area_type": {"$ref": "#/definitions/supportAdAreaType", "title": "行政区划等级"}, "parent_id": {"type": "string", "format": "uint64", "title": "父级ID"}, "lat": {"type": "number", "format": "double", "title": "纬度"}, "lng": {"type": "number", "format": "double", "title": "经度"}, "iso_code2": {"type": "string", "title": "2位ISO编码"}, "iso_code3": {"type": "string", "title": "3位ISO编码"}, "iso_number": {"type": "string", "title": "ISO编号"}}, "title": "地点详情"}, "supportPlaceWithParents": {"type": "object", "properties": {"place": {"$ref": "#/definitions/supportPlace", "title": "地点详情"}, "parents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/supportPlace"}, "title": "父级列表"}}, "title": "地点详情"}}}