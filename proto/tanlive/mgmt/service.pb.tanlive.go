// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package mgmt

import (
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	proto "google.golang.org/protobuf/proto"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Username": "required",
		"Password": "required",
	}, &ReqCheckLoginCredentials{})
}

func (x *ReqCheckLoginCredentials) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
		"Path":   "required",
	}, &ReqCheckApiPermission{})
}

func (x *ReqCheckApiPermission) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Filter": "required",
	}, &ReqGetRoles{})
}

func (x *ReqGetRoles) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Role": "required",
	}, &ReqCreateRole{})
}

func (x *ReqCreateRole) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqCheckLoginCredentials) MaskInLog() any {
	if x == nil {
		return (*ReqCheckLoginCredentials)(nil)
	}

	y := proto.Clone(x).(*ReqCheckLoginCredentials)
	y.Username = mask.Mask(y.Username, "name")
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqCheckLoginCredentials) MaskInRpc() any {
	if x == nil {
		return (*ReqCheckLoginCredentials)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "name")
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqCheckLoginCredentials) MaskInBff() any {
	if x == nil {
		return (*ReqCheckLoginCredentials)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "name")
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *RspCheckLoginCredentials) MaskInLog() any {
	if x == nil {
		return (*RspCheckLoginCredentials)(nil)
	}

	y := proto.Clone(x).(*RspCheckLoginCredentials)
	if v, ok := any(y.User).(interface{ MaskInLog() any }); ok {
		y.User = v.MaskInLog().(*OpUser)
	}

	return y
}

func (x *RspCheckLoginCredentials) MaskInRpc() any {
	if x == nil {
		return (*RspCheckLoginCredentials)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInRpc() any }); ok {
		y.User = v.MaskInRpc().(*OpUser)
	}

	return y
}

func (x *RspCheckLoginCredentials) MaskInBff() any {
	if x == nil {
		return (*RspCheckLoginCredentials)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInBff() any }); ok {
		y.User = v.MaskInBff().(*OpUser)
	}

	return y
}

func (x *RspGetUsers) MaskInLog() any {
	if x == nil {
		return (*RspGetUsers)(nil)
	}

	y := proto.Clone(x).(*RspGetUsers)
	for k, v := range y.UserSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserSet[k] = vv.MaskInLog().(*OpUser)
		}
	}

	return y
}

func (x *RspGetUsers) MaskInRpc() any {
	if x == nil {
		return (*RspGetUsers)(nil)
	}

	y := x
	for k, v := range y.UserSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserSet[k] = vv.MaskInRpc().(*OpUser)
		}
	}

	return y
}

func (x *RspGetUsers) MaskInBff() any {
	if x == nil {
		return (*RspGetUsers)(nil)
	}

	y := x
	for k, v := range y.UserSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserSet[k] = vv.MaskInBff().(*OpUser)
		}
	}

	return y
}

func (x *ReqGetRoles) MaskInLog() any {
	if x == nil {
		return (*ReqGetRoles)(nil)
	}

	y := proto.Clone(x).(*ReqGetRoles)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetRoles_Filter)
	}

	return y
}

func (x *ReqGetRoles) MaskInRpc() any {
	if x == nil {
		return (*ReqGetRoles)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetRoles_Filter)
	}

	return y
}

func (x *ReqGetRoles) MaskInBff() any {
	if x == nil {
		return (*ReqGetRoles)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetRoles_Filter)
	}

	return y
}

func (x *RspGetRoles) MaskInLog() any {
	if x == nil {
		return (*RspGetRoles)(nil)
	}

	y := proto.Clone(x).(*RspGetRoles)
	for k, v := range y.RoleSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.RoleSet[k] = vv.MaskInLog().(*OpRole)
		}
	}

	return y
}

func (x *RspGetRoles) MaskInRpc() any {
	if x == nil {
		return (*RspGetRoles)(nil)
	}

	y := x
	for k, v := range y.RoleSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.RoleSet[k] = vv.MaskInRpc().(*OpRole)
		}
	}

	return y
}

func (x *RspGetRoles) MaskInBff() any {
	if x == nil {
		return (*RspGetRoles)(nil)
	}

	y := x
	for k, v := range y.RoleSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.RoleSet[k] = vv.MaskInBff().(*OpRole)
		}
	}

	return y
}

func (x *ReqCreateRole) MaskInLog() any {
	if x == nil {
		return (*ReqCreateRole)(nil)
	}

	y := proto.Clone(x).(*ReqCreateRole)
	if v, ok := any(y.Role).(interface{ MaskInLog() any }); ok {
		y.Role = v.MaskInLog().(*OpRole)
	}

	return y
}

func (x *ReqCreateRole) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateRole)(nil)
	}

	y := x
	if v, ok := any(y.Role).(interface{ MaskInRpc() any }); ok {
		y.Role = v.MaskInRpc().(*OpRole)
	}

	return y
}

func (x *ReqCreateRole) MaskInBff() any {
	if x == nil {
		return (*ReqCreateRole)(nil)
	}

	y := x
	if v, ok := any(y.Role).(interface{ MaskInBff() any }); ok {
		y.Role = v.MaskInBff().(*OpRole)
	}

	return y
}

func (x *RspCheckLoginCredentials) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.User).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetUsers) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserSet {
		if sanitizer, ok := any(x.UserSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetRoles) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetRoles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.RoleSet {
		if sanitizer, ok := any(x.RoleSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateRole) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Role).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
