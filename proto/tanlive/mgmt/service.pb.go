// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/mgmt/service.proto

package mgmt

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqCheckLoginCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户名
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// 密码
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *ReqCheckLoginCredentials) Reset() {
	*x = ReqCheckLoginCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCheckLoginCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCheckLoginCredentials) ProtoMessage() {}

func (x *ReqCheckLoginCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCheckLoginCredentials.ProtoReflect.Descriptor instead.
func (*ReqCheckLoginCredentials) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqCheckLoginCredentials) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ReqCheckLoginCredentials) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type RspCheckLoginCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *OpUser `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *RspCheckLoginCredentials) Reset() {
	*x = RspCheckLoginCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCheckLoginCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCheckLoginCredentials) ProtoMessage() {}

func (x *RspCheckLoginCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCheckLoginCredentials.ProtoReflect.Descriptor instead.
func (*RspCheckLoginCredentials) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{1}
}

func (x *RspCheckLoginCredentials) GetUser() *OpUser {
	if x != nil {
		return x.User
	}
	return nil
}

type ReqGetUsers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset         uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit          uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	WithTotalCount bool     `protobuf:"varint,3,opt,name=with_total_count,json=withTotalCount,proto3" json:"with_total_count,omitempty"`
	Id             []uint64 `protobuf:"varint,4,rep,packed,name=id,proto3" json:"id,omitempty"`
	Username       []string `protobuf:"bytes,5,rep,name=username,proto3" json:"username,omitempty"`
	Keyword        string   `protobuf:"bytes,6,opt,name=keyword,proto3" json:"keyword,omitempty"`
}

func (x *ReqGetUsers) Reset() {
	*x = ReqGetUsers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetUsers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetUsers) ProtoMessage() {}

func (x *ReqGetUsers) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetUsers.ProtoReflect.Descriptor instead.
func (*ReqGetUsers) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReqGetUsers) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqGetUsers) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqGetUsers) GetWithTotalCount() bool {
	if x != nil {
		return x.WithTotalCount
	}
	return false
}

func (x *ReqGetUsers) GetId() []uint64 {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *ReqGetUsers) GetUsername() []string {
	if x != nil {
		return x.Username
	}
	return nil
}

func (x *ReqGetUsers) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type RspGetUsers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserSet    []*OpUser `protobuf:"bytes,1,rep,name=user_set,json=userSet,proto3" json:"user_set,omitempty"`
	TotalCount uint32    `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspGetUsers) Reset() {
	*x = RspGetUsers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetUsers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetUsers) ProtoMessage() {}

func (x *RspGetUsers) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetUsers.ProtoReflect.Descriptor instead.
func (*RspGetUsers) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{3}
}

func (x *RspGetUsers) GetUserSet() []*OpUser {
	if x != nil {
		return x.UserSet
	}
	return nil
}

func (x *RspGetUsers) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqCheckApiPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Path   string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *ReqCheckApiPermission) Reset() {
	*x = ReqCheckApiPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCheckApiPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCheckApiPermission) ProtoMessage() {}

func (x *ReqCheckApiPermission) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCheckApiPermission.ProtoReflect.Descriptor instead.
func (*ReqCheckApiPermission) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{4}
}

func (x *ReqCheckApiPermission) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqCheckApiPermission) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type RspCheckApiPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Allowed bool `protobuf:"varint,1,opt,name=allowed,proto3" json:"allowed,omitempty"`
}

func (x *RspCheckApiPermission) Reset() {
	*x = RspCheckApiPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCheckApiPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCheckApiPermission) ProtoMessage() {}

func (x *RspCheckApiPermission) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCheckApiPermission.ProtoReflect.Descriptor instead.
func (*RspCheckApiPermission) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{5}
}

func (x *RspCheckApiPermission) GetAllowed() bool {
	if x != nil {
		return x.Allowed
	}
	return false
}

type ReqGetRoles struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ReqGetRoles_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqGetRoles) Reset() {
	*x = ReqGetRoles{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetRoles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetRoles) ProtoMessage() {}

func (x *ReqGetRoles) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetRoles.ProtoReflect.Descriptor instead.
func (*ReqGetRoles) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{6}
}

func (x *ReqGetRoles) GetFilter() *ReqGetRoles_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type RspGetRoles struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoleSet      []*OpRole         `protobuf:"bytes,1,rep,name=role_set,json=roleSet,proto3" json:"role_set,omitempty"`
	UserCountMap map[uint64]uint32 `protobuf:"bytes,2,rep,name=user_count_map,json=userCountMap,proto3" json:"user_count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *RspGetRoles) Reset() {
	*x = RspGetRoles{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetRoles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetRoles) ProtoMessage() {}

func (x *RspGetRoles) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetRoles.ProtoReflect.Descriptor instead.
func (*RspGetRoles) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{7}
}

func (x *RspGetRoles) GetRoleSet() []*OpRole {
	if x != nil {
		return x.RoleSet
	}
	return nil
}

func (x *RspGetRoles) GetUserCountMap() map[uint64]uint32 {
	if x != nil {
		return x.UserCountMap
	}
	return nil
}

type ReqCreateRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Role *OpRole `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *ReqCreateRole) Reset() {
	*x = ReqCreateRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateRole) ProtoMessage() {}

func (x *ReqCreateRole) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateRole.ProtoReflect.Descriptor instead.
func (*ReqCreateRole) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{8}
}

func (x *ReqCreateRole) GetRole() *OpRole {
	if x != nil {
		return x.Role
	}
	return nil
}

type RspCreateRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RspCreateRole) Reset() {
	*x = RspCreateRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateRole) ProtoMessage() {}

func (x *RspCreateRole) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateRole.ProtoReflect.Descriptor instead.
func (*RspCreateRole) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{9}
}

func (x *RspCreateRole) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ReqGetRoles_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchName    string              `protobuf:"bytes,1,opt,name=search_name,json=searchName,proto3" json:"search_name,omitempty"`
	State         []base.DisableState `protobuf:"varint,2,rep,packed,name=state,proto3,enum=tanlive.base.DisableState" json:"state,omitempty"`
	Type          []RoleType          `protobuf:"varint,3,rep,packed,name=type,proto3,enum=tanlive.mgmt.RoleType" json:"type,omitempty"`
	WithUserCount bool                `protobuf:"varint,4,opt,name=with_user_count,json=withUserCount,proto3" json:"with_user_count,omitempty"`
}

func (x *ReqGetRoles_Filter) Reset() {
	*x = ReqGetRoles_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetRoles_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetRoles_Filter) ProtoMessage() {}

func (x *ReqGetRoles_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetRoles_Filter.ProtoReflect.Descriptor instead.
func (*ReqGetRoles_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ReqGetRoles_Filter) GetSearchName() string {
	if x != nil {
		return x.SearchName
	}
	return ""
}

func (x *ReqGetRoles_Filter) GetState() []base.DisableState {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *ReqGetRoles_Filter) GetType() []RoleType {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *ReqGetRoles_Filter) GetWithUserCount() bool {
	if x != nil {
		return x.WithUserCount
	}
	return false
}

var File_tanlive_mgmt_service_proto protoreflect.FileDescriptor

var file_tanlive_mgmt_service_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x67, 0x6d,
	0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x84, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x71, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x12, 0x32, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x16, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x8a, 0x88, 0x27, 0x06, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x44, 0x0a, 0x18, 0x52, 0x73,
	0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x4f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x22, 0xab, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28,
	0x0a, 0x10, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x77, 0x69, 0x74, 0x68, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x5f,
	0x0a, 0x0b, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x2f, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x4f,
	0x70, 0x55, 0x73, 0x65, 0x72, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x53, 0x65, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x60, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x70, 0x69, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x22, 0x31, 0x0a, 0x15, 0x52, 0x73, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x70, 0x69,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x22, 0x87, 0x02, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x6c, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xaf, 0x01, 0x0a,
	0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x77, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd2,
	0x01, 0x0a, 0x0b, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x2f,
	0x0a, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x4f, 0x70, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x12,
	0x51, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c,
	0x65, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d,
	0x61, 0x70, 0x1a, 0x3f, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x47, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x4f, 0x70, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x1f, 0x0a, 0x0d,
	0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x32, 0xa2, 0x03,
	0x0a, 0x0b, 0x4d, 0x67, 0x6d, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x67, 0x0a,
	0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x1a, 0x26,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x73,
	0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x40, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x12, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x19, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x73, 0x70,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x5e, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x41, 0x70, 0x69, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x65,
	0x71, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x70, 0x69, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x1a, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x70, 0x69, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x6c, 0x65, 0x73, 0x12, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x1a,
	0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52,
	0x73, 0x70, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x1a, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f,
	0x6c, 0x65, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e,
	0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x67,
	0x6d, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_mgmt_service_proto_rawDescOnce sync.Once
	file_tanlive_mgmt_service_proto_rawDescData = file_tanlive_mgmt_service_proto_rawDesc
)

func file_tanlive_mgmt_service_proto_rawDescGZIP() []byte {
	file_tanlive_mgmt_service_proto_rawDescOnce.Do(func() {
		file_tanlive_mgmt_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_mgmt_service_proto_rawDescData)
	})
	return file_tanlive_mgmt_service_proto_rawDescData
}

var file_tanlive_mgmt_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_tanlive_mgmt_service_proto_goTypes = []interface{}{
	(*ReqCheckLoginCredentials)(nil), // 0: tanlive.mgmt.ReqCheckLoginCredentials
	(*RspCheckLoginCredentials)(nil), // 1: tanlive.mgmt.RspCheckLoginCredentials
	(*ReqGetUsers)(nil),              // 2: tanlive.mgmt.ReqGetUsers
	(*RspGetUsers)(nil),              // 3: tanlive.mgmt.RspGetUsers
	(*ReqCheckApiPermission)(nil),    // 4: tanlive.mgmt.ReqCheckApiPermission
	(*RspCheckApiPermission)(nil),    // 5: tanlive.mgmt.RspCheckApiPermission
	(*ReqGetRoles)(nil),              // 6: tanlive.mgmt.ReqGetRoles
	(*RspGetRoles)(nil),              // 7: tanlive.mgmt.RspGetRoles
	(*ReqCreateRole)(nil),            // 8: tanlive.mgmt.ReqCreateRole
	(*RspCreateRole)(nil),            // 9: tanlive.mgmt.RspCreateRole
	(*ReqGetRoles_Filter)(nil),       // 10: tanlive.mgmt.ReqGetRoles.Filter
	nil,                              // 11: tanlive.mgmt.RspGetRoles.UserCountMapEntry
	(*OpUser)(nil),                   // 12: tanlive.mgmt.OpUser
	(*OpRole)(nil),                   // 13: tanlive.mgmt.OpRole
	(base.DisableState)(0),           // 14: tanlive.base.DisableState
	(RoleType)(0),                    // 15: tanlive.mgmt.RoleType
}
var file_tanlive_mgmt_service_proto_depIdxs = []int32{
	12, // 0: tanlive.mgmt.RspCheckLoginCredentials.user:type_name -> tanlive.mgmt.OpUser
	12, // 1: tanlive.mgmt.RspGetUsers.user_set:type_name -> tanlive.mgmt.OpUser
	10, // 2: tanlive.mgmt.ReqGetRoles.filter:type_name -> tanlive.mgmt.ReqGetRoles.Filter
	13, // 3: tanlive.mgmt.RspGetRoles.role_set:type_name -> tanlive.mgmt.OpRole
	11, // 4: tanlive.mgmt.RspGetRoles.user_count_map:type_name -> tanlive.mgmt.RspGetRoles.UserCountMapEntry
	13, // 5: tanlive.mgmt.ReqCreateRole.role:type_name -> tanlive.mgmt.OpRole
	14, // 6: tanlive.mgmt.ReqGetRoles.Filter.state:type_name -> tanlive.base.DisableState
	15, // 7: tanlive.mgmt.ReqGetRoles.Filter.type:type_name -> tanlive.mgmt.RoleType
	0,  // 8: tanlive.mgmt.MgmtService.CheckLoginCredentials:input_type -> tanlive.mgmt.ReqCheckLoginCredentials
	2,  // 9: tanlive.mgmt.MgmtService.GetUsers:input_type -> tanlive.mgmt.ReqGetUsers
	4,  // 10: tanlive.mgmt.MgmtService.CheckApiPermission:input_type -> tanlive.mgmt.ReqCheckApiPermission
	6,  // 11: tanlive.mgmt.MgmtService.GetRoles:input_type -> tanlive.mgmt.ReqGetRoles
	8,  // 12: tanlive.mgmt.MgmtService.CreateRole:input_type -> tanlive.mgmt.ReqCreateRole
	1,  // 13: tanlive.mgmt.MgmtService.CheckLoginCredentials:output_type -> tanlive.mgmt.RspCheckLoginCredentials
	3,  // 14: tanlive.mgmt.MgmtService.GetUsers:output_type -> tanlive.mgmt.RspGetUsers
	5,  // 15: tanlive.mgmt.MgmtService.CheckApiPermission:output_type -> tanlive.mgmt.RspCheckApiPermission
	7,  // 16: tanlive.mgmt.MgmtService.GetRoles:output_type -> tanlive.mgmt.RspGetRoles
	9,  // 17: tanlive.mgmt.MgmtService.CreateRole:output_type -> tanlive.mgmt.RspCreateRole
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_tanlive_mgmt_service_proto_init() }
func file_tanlive_mgmt_service_proto_init() {
	if File_tanlive_mgmt_service_proto != nil {
		return
	}
	file_tanlive_mgmt_mgmt_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_mgmt_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCheckLoginCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCheckLoginCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetUsers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetUsers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCheckApiPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCheckApiPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetRoles); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetRoles); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetRoles_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_mgmt_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_mgmt_service_proto_goTypes,
		DependencyIndexes: file_tanlive_mgmt_service_proto_depIdxs,
		MessageInfos:      file_tanlive_mgmt_service_proto_msgTypes,
	}.Build()
	File_tanlive_mgmt_service_proto = out.File
	file_tanlive_mgmt_service_proto_rawDesc = nil
	file_tanlive_mgmt_service_proto_goTypes = nil
	file_tanlive_mgmt_service_proto_depIdxs = nil
}
