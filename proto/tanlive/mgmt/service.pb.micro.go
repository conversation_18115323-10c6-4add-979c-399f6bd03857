// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/mgmt/service.proto

package mgmt

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for MgmtService service

func NewMgmtServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for MgmtService service

type MgmtService interface {
	// 校验登录凭证
	CheckLoginCredentials(ctx context.Context, in *ReqCheckLoginCredentials, opts ...client.CallOption) (*RspCheckLoginCredentials, error)
	// 查询用户列表
	GetUsers(ctx context.Context, in *ReqGetUsers, opts ...client.CallOption) (*RspGetUsers, error)
	// 检查API权限
	CheckApiPermission(ctx context.Context, in *ReqCheckApiPermission, opts ...client.CallOption) (*RspCheckApiPermission, error)
	// 查询角色列表
	GetRoles(ctx context.Context, in *ReqGetRoles, opts ...client.CallOption) (*RspGetRoles, error)
	// 创建角色
	CreateRole(ctx context.Context, in *ReqCreateRole, opts ...client.CallOption) (*RspCreateRole, error)
}

type mgmtService struct {
	c    client.Client
	name string
}

func NewMgmtService(name string, c client.Client) MgmtService {
	return &mgmtService{
		c:    c,
		name: name,
	}
}

func (c *mgmtService) CheckLoginCredentials(ctx context.Context, in *ReqCheckLoginCredentials, opts ...client.CallOption) (*RspCheckLoginCredentials, error) {
	req := c.c.NewRequest(c.name, "MgmtService.CheckLoginCredentials", in)
	out := new(RspCheckLoginCredentials)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mgmtService) GetUsers(ctx context.Context, in *ReqGetUsers, opts ...client.CallOption) (*RspGetUsers, error) {
	req := c.c.NewRequest(c.name, "MgmtService.GetUsers", in)
	out := new(RspGetUsers)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mgmtService) CheckApiPermission(ctx context.Context, in *ReqCheckApiPermission, opts ...client.CallOption) (*RspCheckApiPermission, error) {
	req := c.c.NewRequest(c.name, "MgmtService.CheckApiPermission", in)
	out := new(RspCheckApiPermission)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mgmtService) GetRoles(ctx context.Context, in *ReqGetRoles, opts ...client.CallOption) (*RspGetRoles, error) {
	req := c.c.NewRequest(c.name, "MgmtService.GetRoles", in)
	out := new(RspGetRoles)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mgmtService) CreateRole(ctx context.Context, in *ReqCreateRole, opts ...client.CallOption) (*RspCreateRole, error) {
	req := c.c.NewRequest(c.name, "MgmtService.CreateRole", in)
	out := new(RspCreateRole)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for MgmtService service

type MgmtServiceHandler interface {
	// 校验登录凭证
	CheckLoginCredentials(context.Context, *ReqCheckLoginCredentials, *RspCheckLoginCredentials) error
	// 查询用户列表
	GetUsers(context.Context, *ReqGetUsers, *RspGetUsers) error
	// 检查API权限
	CheckApiPermission(context.Context, *ReqCheckApiPermission, *RspCheckApiPermission) error
	// 查询角色列表
	GetRoles(context.Context, *ReqGetRoles, *RspGetRoles) error
	// 创建角色
	CreateRole(context.Context, *ReqCreateRole, *RspCreateRole) error
}

func RegisterMgmtServiceHandler(s server.Server, hdlr MgmtServiceHandler, opts ...server.HandlerOption) error {
	type mgmtService interface {
		CheckLoginCredentials(ctx context.Context, in *ReqCheckLoginCredentials, out *RspCheckLoginCredentials) error
		GetUsers(ctx context.Context, in *ReqGetUsers, out *RspGetUsers) error
		CheckApiPermission(ctx context.Context, in *ReqCheckApiPermission, out *RspCheckApiPermission) error
		GetRoles(ctx context.Context, in *ReqGetRoles, out *RspGetRoles) error
		CreateRole(ctx context.Context, in *ReqCreateRole, out *RspCreateRole) error
	}
	type MgmtService struct {
		mgmtService
	}
	h := &mgmtServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&MgmtService{h}, opts...))
}

type mgmtServiceHandler struct {
	MgmtServiceHandler
}

func (h *mgmtServiceHandler) CheckLoginCredentials(ctx context.Context, in *ReqCheckLoginCredentials, out *RspCheckLoginCredentials) error {
	return h.MgmtServiceHandler.CheckLoginCredentials(ctx, in, out)
}

func (h *mgmtServiceHandler) GetUsers(ctx context.Context, in *ReqGetUsers, out *RspGetUsers) error {
	return h.MgmtServiceHandler.GetUsers(ctx, in, out)
}

func (h *mgmtServiceHandler) CheckApiPermission(ctx context.Context, in *ReqCheckApiPermission, out *RspCheckApiPermission) error {
	return h.MgmtServiceHandler.CheckApiPermission(ctx, in, out)
}

func (h *mgmtServiceHandler) GetRoles(ctx context.Context, in *ReqGetRoles, out *RspGetRoles) error {
	return h.MgmtServiceHandler.GetRoles(ctx, in, out)
}

func (h *mgmtServiceHandler) CreateRole(ctx context.Context, in *ReqCreateRole, out *RspCreateRole) error {
	return h.MgmtServiceHandler.CreateRole(ctx, in, out)
}
