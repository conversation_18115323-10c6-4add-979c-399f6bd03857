syntax = "proto3";

package tanlive.mgmt;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt";

import "google/protobuf/timestamp.proto";
import "tanlive/base/base.proto";
import "tanlive/options.proto";

// 运营端用户
message OpUser {
  uint64 id = 1;
  string username = 2 [(tanlive.mask).rule = "username"];
  string remark_name = 3;
  string phone_num = 4 [(tanlive.mask).rule = "phone"];
  string email = 5 [(tanlive.mask).rule = "email"];
  int32 state = 6;
  uint64 create_by = 7;
  google.protobuf.Timestamp create_date = 8;
  uint64 last_update_by = 9;
  google.protobuf.Timestamp last_update_date = 10;
}

// 角色类型
enum RoleType {
  ROLE_TYPE_UNSPECIFIED = 0;
  // 管理员
  ROLE_TYPE_ADMIN = 1;
  // 未分类
  ROLE_TYPE_UNCLASSIFIED = 2;
  // 自定义
  ROLE_TYPE_CUSTOM = 3;
}

// 角色
message OpRole {
  uint64 id = 1;
  string role_name = 2;
  tanlive.base.DisableState state = 3;
  RoleType type = 4;
  uint64 create_by = 6;
  google.protobuf.Timestamp create_date = 7;
  uint64 last_update_by = 8;
  google.protobuf.Timestamp last_update_date = 9;
}

// 运营端用户卡片
message UserCard {
  // 用户ID
  uint64 id = 1;
  // 用户名
  string username = 2 [(tanlive.mask).rule = "username"];
  // 备注名
  string remark_name = 3;
}
