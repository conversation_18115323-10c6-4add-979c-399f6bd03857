// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package mgmt

import (
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func (x *OpUser) MaskInLog() any {
	if x == nil {
		return (*OpUser)(nil)
	}

	y := proto.Clone(x).(*OpUser)
	y.Username = mask.Mask(y.Username, "username")
	y.PhoneNum = mask.Mask(y.PhoneNum, "phone")
	y.Email = mask.Mask(y.Email, "email")
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *OpUser) MaskInRpc() any {
	if x == nil {
		return (*OpUser)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")
	y.PhoneNum = mask.Mask(y.PhoneNum, "phone")
	y.Email = mask.Mask(y.Email, "email")
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *OpUser) MaskInBff() any {
	if x == nil {
		return (*OpUser)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")
	y.PhoneNum = mask.Mask(y.PhoneNum, "phone")
	y.Email = mask.Mask(y.Email, "email")
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *OpRole) MaskInLog() any {
	if x == nil {
		return (*OpRole)(nil)
	}

	y := proto.Clone(x).(*OpRole)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *OpRole) MaskInRpc() any {
	if x == nil {
		return (*OpRole)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *OpRole) MaskInBff() any {
	if x == nil {
		return (*OpRole)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *UserCard) MaskInLog() any {
	if x == nil {
		return (*UserCard)(nil)
	}

	y := proto.Clone(x).(*UserCard)
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *UserCard) MaskInRpc() any {
	if x == nil {
		return (*UserCard)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *UserCard) MaskInBff() any {
	if x == nil {
		return (*UserCard)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *OpUser) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *OpRole) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
