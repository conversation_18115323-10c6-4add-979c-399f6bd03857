// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/mgmt/mgmt.proto

package mgmt

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 角色类型
type RoleType int32

const (
	RoleType_ROLE_TYPE_UNSPECIFIED RoleType = 0
	// 管理员
	RoleType_ROLE_TYPE_ADMIN RoleType = 1
	// 未分类
	RoleType_ROLE_TYPE_UNCLASSIFIED RoleType = 2
	// 自定义
	RoleType_ROLE_TYPE_CUSTOM RoleType = 3
)

// Enum value maps for RoleType.
var (
	RoleType_name = map[int32]string{
		0: "ROLE_TYPE_UNSPECIFIED",
		1: "ROLE_TYPE_ADMIN",
		2: "ROLE_TYPE_UNCLASSIFIED",
		3: "ROLE_TYPE_CUSTOM",
	}
	RoleType_value = map[string]int32{
		"ROLE_TYPE_UNSPECIFIED":  0,
		"ROLE_TYPE_ADMIN":        1,
		"ROLE_TYPE_UNCLASSIFIED": 2,
		"ROLE_TYPE_CUSTOM":       3,
	}
)

func (x RoleType) Enum() *RoleType {
	p := new(RoleType)
	*p = x
	return p
}

func (x RoleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoleType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_mgmt_mgmt_proto_enumTypes[0].Descriptor()
}

func (RoleType) Type() protoreflect.EnumType {
	return &file_tanlive_mgmt_mgmt_proto_enumTypes[0]
}

func (x RoleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoleType.Descriptor instead.
func (RoleType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_mgmt_mgmt_proto_rawDescGZIP(), []int{0}
}

// 运营端用户
type OpUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username       string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	RemarkName     string                 `protobuf:"bytes,3,opt,name=remark_name,json=remarkName,proto3" json:"remark_name,omitempty"`
	PhoneNum       string                 `protobuf:"bytes,4,opt,name=phone_num,json=phoneNum,proto3" json:"phone_num,omitempty"`
	Email          string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	State          int32                  `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty"`
	CreateBy       uint64                 `protobuf:"varint,7,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	CreateDate     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	LastUpdateBy   uint64                 `protobuf:"varint,9,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
}

func (x *OpUser) Reset() {
	*x = OpUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_mgmt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpUser) ProtoMessage() {}

func (x *OpUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_mgmt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpUser.ProtoReflect.Descriptor instead.
func (*OpUser) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_mgmt_proto_rawDescGZIP(), []int{0}
}

func (x *OpUser) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OpUser) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *OpUser) GetRemarkName() string {
	if x != nil {
		return x.RemarkName
	}
	return ""
}

func (x *OpUser) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *OpUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *OpUser) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *OpUser) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *OpUser) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *OpUser) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *OpUser) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

// 角色
type OpRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RoleName       string                 `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	State          base.DisableState      `protobuf:"varint,3,opt,name=state,proto3,enum=tanlive.base.DisableState" json:"state,omitempty"`
	Type           RoleType               `protobuf:"varint,4,opt,name=type,proto3,enum=tanlive.mgmt.RoleType" json:"type,omitempty"`
	CreateBy       uint64                 `protobuf:"varint,6,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	CreateDate     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	LastUpdateBy   uint64                 `protobuf:"varint,8,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
}

func (x *OpRole) Reset() {
	*x = OpRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_mgmt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpRole) ProtoMessage() {}

func (x *OpRole) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_mgmt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpRole.ProtoReflect.Descriptor instead.
func (*OpRole) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_mgmt_proto_rawDescGZIP(), []int{1}
}

func (x *OpRole) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OpRole) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *OpRole) GetState() base.DisableState {
	if x != nil {
		return x.State
	}
	return base.DisableState(0)
}

func (x *OpRole) GetType() RoleType {
	if x != nil {
		return x.Type
	}
	return RoleType_ROLE_TYPE_UNSPECIFIED
}

func (x *OpRole) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *OpRole) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *OpRole) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *OpRole) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

// 运营端用户卡片
type UserCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户名
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// 备注名
	RemarkName string `protobuf:"bytes,3,opt,name=remark_name,json=remarkName,proto3" json:"remark_name,omitempty"`
}

func (x *UserCard) Reset() {
	*x = UserCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_mgmt_mgmt_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCard) ProtoMessage() {}

func (x *UserCard) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_mgmt_mgmt_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCard.ProtoReflect.Descriptor instead.
func (*UserCard) Descriptor() ([]byte, []int) {
	return file_tanlive_mgmt_mgmt_proto_rawDescGZIP(), []int{2}
}

func (x *UserCard) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserCard) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserCard) GetRemarkName() string {
	if x != nil {
		return x.RemarkName
	}
	return ""
}

var File_tanlive_mgmt_mgmt_proto protoreflect.FileDescriptor

var file_tanlive_mgmt_mgmt_proto_rawDesc = []byte{
	0x0a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8e, 0x03, 0x0a, 0x06, 0x4f, 0x70, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x8a, 0x88, 0x27, 0x0a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x28, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x8a, 0x88, 0x27, 0x07, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x52, 0x08, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x21, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x8a, 0x88, 0x27, 0x07, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79,
	0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x79, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0xd9, 0x02, 0x0a, 0x06, 0x4f, 0x70,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12,
	0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x67, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61, 0x72,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x2a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x8a, 0x88, 0x27, 0x0a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x2a, 0x6c,
	0x0a, 0x08, 0x52, 0x6f, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x4f,
	0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x4f,
	0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x03, 0x42, 0x3e, 0x5a, 0x3c,
	0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_mgmt_mgmt_proto_rawDescOnce sync.Once
	file_tanlive_mgmt_mgmt_proto_rawDescData = file_tanlive_mgmt_mgmt_proto_rawDesc
)

func file_tanlive_mgmt_mgmt_proto_rawDescGZIP() []byte {
	file_tanlive_mgmt_mgmt_proto_rawDescOnce.Do(func() {
		file_tanlive_mgmt_mgmt_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_mgmt_mgmt_proto_rawDescData)
	})
	return file_tanlive_mgmt_mgmt_proto_rawDescData
}

var file_tanlive_mgmt_mgmt_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_mgmt_mgmt_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_tanlive_mgmt_mgmt_proto_goTypes = []interface{}{
	(RoleType)(0),                 // 0: tanlive.mgmt.RoleType
	(*OpUser)(nil),                // 1: tanlive.mgmt.OpUser
	(*OpRole)(nil),                // 2: tanlive.mgmt.OpRole
	(*UserCard)(nil),              // 3: tanlive.mgmt.UserCard
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
	(base.DisableState)(0),        // 5: tanlive.base.DisableState
}
var file_tanlive_mgmt_mgmt_proto_depIdxs = []int32{
	4, // 0: tanlive.mgmt.OpUser.create_date:type_name -> google.protobuf.Timestamp
	4, // 1: tanlive.mgmt.OpUser.last_update_date:type_name -> google.protobuf.Timestamp
	5, // 2: tanlive.mgmt.OpRole.state:type_name -> tanlive.base.DisableState
	0, // 3: tanlive.mgmt.OpRole.type:type_name -> tanlive.mgmt.RoleType
	4, // 4: tanlive.mgmt.OpRole.create_date:type_name -> google.protobuf.Timestamp
	4, // 5: tanlive.mgmt.OpRole.last_update_date:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_tanlive_mgmt_mgmt_proto_init() }
func file_tanlive_mgmt_mgmt_proto_init() {
	if File_tanlive_mgmt_mgmt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_mgmt_mgmt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_mgmt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_mgmt_mgmt_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_mgmt_mgmt_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_mgmt_mgmt_proto_goTypes,
		DependencyIndexes: file_tanlive_mgmt_mgmt_proto_depIdxs,
		EnumInfos:         file_tanlive_mgmt_mgmt_proto_enumTypes,
		MessageInfos:      file_tanlive_mgmt_mgmt_proto_msgTypes,
	}.Build()
	File_tanlive_mgmt_mgmt_proto = out.File
	file_tanlive_mgmt_mgmt_proto_rawDesc = nil
	file_tanlive_mgmt_mgmt_proto_goTypes = nil
	file_tanlive_mgmt_mgmt_proto_depIdxs = nil
}
