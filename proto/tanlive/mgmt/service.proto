syntax = "proto3";

package tanlive.mgmt;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt";

import "tanlive/base/base.proto";
import "tanlive/mgmt/mgmt.proto";
import "tanlive/options.proto";

message ReqCheckLoginCredentials {
  // 用户名
  string username = 1 [(tanlive.validator) = "required", (tanlive.mask).rule = "name"];
  // 密码
  string password = 2 [(tanlive.validator) = "required", (tanlive.mask).rule = "secret"];
}

message RspCheckLoginCredentials {
  OpUser user = 1;
}

message ReqGetUsers {
  uint32 offset = 1;
  uint32 limit = 2;
  bool with_total_count = 3;
  repeated uint64 id = 4;
  repeated string username = 5;
  string keyword = 6;
}

message RspGetUsers {
  repeated OpUser user_set = 1;
  uint32 total_count = 2;
}

message ReqCheckApiPermission {
  uint64 user_id = 1 [(tanlive.validator) = "required"];
  string path = 2 [(tanlive.validator) = "required"];
}

message RspCheckApiPermission {
  bool allowed = 1;
}

message ReqGetRoles {
  message Filter {
    string search_name = 1;
    repeated tanlive.base.DisableState state = 2;
    repeated RoleType type = 3;
    bool with_user_count = 4;
  }
  Filter filter = 1 [(tanlive.validator) = "required"];
}

message RspGetRoles {
  repeated OpRole role_set = 1;
  map<uint64, uint32> user_count_map = 2;
}

message ReqCreateRole {
  OpRole role = 1 [(tanlive.validator) = "required"];
}

message RspCreateRole {
  uint64 id = 1;
}

// 运营管理服务
service MgmtService {
  // 校验登录凭证
  rpc CheckLoginCredentials(ReqCheckLoginCredentials) returns (RspCheckLoginCredentials);
  // 查询用户列表
  rpc GetUsers(ReqGetUsers) returns (RspGetUsers);
  // 检查API权限
  rpc CheckApiPermission(ReqCheckApiPermission) returns (RspCheckApiPermission);
  // 查询角色列表
  rpc GetRoles(ReqGetRoles) returns (RspGetRoles);
  // 创建角色
  rpc CreateRole(ReqCreateRole) returns (RspCreateRole);
}
