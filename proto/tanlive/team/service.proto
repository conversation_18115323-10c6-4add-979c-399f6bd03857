syntax = "proto3";

package tanlive.team;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team";

import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "tanlive/options.proto";
import "tanlive/team/team.proto";
import "tanlive/base/base.proto";

message  ReqUpdateUgcCustomRoute{
  CustomRoute custom_route = 1 [(tanlive.validator) = "required"];;
}

message  ReqUpdateTeamCustomRoute{
  uint64 team_id = 1;
  string route =2;
}

message  ReqCreateUgcCustomRoute{
  CustomRoute custom_route = 1 [(tanlive.validator) = "required"];;
}

message  ReqDescribeCustomRouteByUgcId{
  uint64 ugc_id =1;
}

message  ResDescribeCustomRouteByUgcId{
  CustomRoute custom_route = 1;
}

message  ReqCheckUgcCustomRouteInUsed{
  uint64 ugc_id =1;
}

message  ResCheckUgcCustomRouteInUsed{
  CustomRoute custom_route = 1;
}

message  ReqDescribeUgcIdCustomRouteRepeated{
  string route =1;
  repeated uint64 exclude_ugc_ids = 2;
}

message  ResDescribeUgcIdCustomRouteRepeated{
  CustomRoute exist_custom_route = 1;
}

message ReqDescribeCustomRouteUgcByRoute{
  string route = 1;
}

message  ResDescribeCustomRouteUgcByRoute{
  repeated CustomRoute custom_routes = 1;
}

message ReqDeleteCustomRoute{
  repeated uint64 ugc_ids = 1;
}

message ReqGetUserTeams {
  // 用户ID
  repeated uint64 user_id = 1 [(validator) = "required,dive,required"];
}

message RspGetUserTeams {
  // 团队列表<用户ID, 团队信息>
  map<uint64, FullTeam> teams = 1;
}

message ReqGetTeams {
  // 搜索场景
  enum SearchScene {
    SEARCH_SCENE_UNSPECIFIED = 0;
    // AI知识库分享默认设置中搜索
    SEARCH_SCENE_AI_SHARE_SETTING = 1;
  }
  // 过滤器
  message Filter {
    // 团队ID
    repeated uint64 team_id = 1;
    // 搜索关键词
    string search_keyword = 2;
    // 搜索场景
    SearchScene search_scene = 3;
    // 是否包含已注销的团队
    bool with_disbanded = 4;
  }
  // 分页
  base.Paginator page = 1;
  // 过滤器
  Filter filter = 2;
  // 是否返回total_count
  bool with_total_count = 3;
}

message RspGetTeams {
  // 团队列表
  repeated FullTeam teams = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqGetTeamIds {
  // 搜索团队简称和全称，or 关联查询
  string team_name = 1;
}

message RspGetTeamIds {
  repeated uint64 ids = 1;
}

message ReqGetTeamStaff {
  repeated uint64 team_ids = 1 [(tanlive.validator) = "required"];
}

message RspGetTeamStaff {
  map<uint64, Staff> staff_map = 1;
  message Staff{
    repeated uint64 user_ids = 1;
  }
}

// 团队服务
service TeamService {
  // 更新团队自定义路由
  rpc UpdateUgcCustomRouteByUgcId(CustomRoute) returns (google.protobuf.Empty);
  // 检查ugc的路由是否重名
  rpc DescribeUgcIdCustomRouteRepeated(ReqDescribeUgcIdCustomRouteRepeated) returns (ResDescribeUgcIdCustomRouteRepeated);
  // 前端通过自定义路由获取ugc信息
  rpc DescribeCustomRouteUgcByRoute(ReqDescribeCustomRouteUgcByRoute) returns (ResDescribeCustomRouteUgcByRoute);
  // 释放自定义路由
  rpc DeleteCustomRoute(ReqDeleteCustomRoute)  returns (google.protobuf.Empty);

  // 查询用户所属团队
  rpc GetUserTeams(ReqGetUserTeams) returns (RspGetUserTeams);
  // 查询团队列表
  rpc GetTeams(ReqGetTeams) returns (RspGetTeams);
  // 查询所有团队id
  rpc GetTeamIds(ReqGetTeamIds) returns (RspGetTeamIds);
  // 获取团队员工id
  rpc GetTeamStaff(ReqGetTeamStaff) returns (RspGetTeamStaff);
}
