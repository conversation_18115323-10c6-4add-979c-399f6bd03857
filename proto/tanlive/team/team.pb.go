// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/team/team.proto

package team

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 实体类型
type EntityType int32

const (
	EntityType_ENTITY_TYPE_UNSPECIFIED EntityType = 0
	// 企业
	EntityType_ENTITY_TYPE_ENTERPRISE EntityType = 1
	// 其它
	EntityType_ENTITY_TYPE_OTHER EntityType = 2
)

// Enum value maps for EntityType.
var (
	EntityType_name = map[int32]string{
		0: "ENTITY_TYPE_UNSPECIFIED",
		1: "ENTITY_TYPE_ENTERPRISE",
		2: "ENTITY_TYPE_OTHER",
	}
	EntityType_value = map[string]int32{
		"ENTITY_TYPE_UNSPECIFIED": 0,
		"ENTITY_TYPE_ENTERPRISE":  1,
		"ENTITY_TYPE_OTHER":       2,
	}
)

func (x EntityType) Enum() *EntityType {
	p := new(EntityType)
	*p = x
	return p
}

func (x EntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_team_team_proto_enumTypes[0].Descriptor()
}

func (EntityType) Type() protoreflect.EnumType {
	return &file_tanlive_team_team_proto_enumTypes[0]
}

func (x EntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EntityType.Descriptor instead.
func (EntityType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{0}
}

// 团队共创等级
type TeamLevel int32

const (
	TeamLevel_TEAM_LEVEL_UNSPECIFIED TeamLevel = 0
	// CONTRIBUTOR
	TeamLevel_TEAM_LEVEL_CONTRIBUTOR TeamLevel = 1
	// COMMITTER
	TeamLevel_TEAM_LEVEL_COMMITTER TeamLevel = 2
	// MAINTAINER
	TeamLevel_TEAM_LEVEL_MAINTAINER TeamLevel = 3
)

// Enum value maps for TeamLevel.
var (
	TeamLevel_name = map[int32]string{
		0: "TEAM_LEVEL_UNSPECIFIED",
		1: "TEAM_LEVEL_CONTRIBUTOR",
		2: "TEAM_LEVEL_COMMITTER",
		3: "TEAM_LEVEL_MAINTAINER",
	}
	TeamLevel_value = map[string]int32{
		"TEAM_LEVEL_UNSPECIFIED": 0,
		"TEAM_LEVEL_CONTRIBUTOR": 1,
		"TEAM_LEVEL_COMMITTER":   2,
		"TEAM_LEVEL_MAINTAINER":  3,
	}
)

func (x TeamLevel) Enum() *TeamLevel {
	p := new(TeamLevel)
	*p = x
	return p
}

func (x TeamLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TeamLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_team_team_proto_enumTypes[1].Descriptor()
}

func (TeamLevel) Type() protoreflect.EnumType {
	return &file_tanlive_team_team_proto_enumTypes[1]
}

func (x TeamLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TeamLevel.Descriptor instead.
func (TeamLevel) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{1}
}

// 团队规模
type TeamScale int32

const (
	TeamScale_TEAM_SCALE_UNSPECIFIED TeamScale = 0
	// 1-10人
	TeamScale_TEAM_SCALE_BETWEEN_1_10 TeamScale = 1
	// 10-50人
	TeamScale_TEAM_SCALE_BETWEEN_10_50 TeamScale = 2
	// 50-500人
	TeamScale_TEAM_SCALE_BETWEEN_50_500 TeamScale = 3
	// 500人以上
	TeamScale_TEAM_SCALE_MORE_THAN_500 TeamScale = 4
)

// Enum value maps for TeamScale.
var (
	TeamScale_name = map[int32]string{
		0: "TEAM_SCALE_UNSPECIFIED",
		1: "TEAM_SCALE_BETWEEN_1_10",
		2: "TEAM_SCALE_BETWEEN_10_50",
		3: "TEAM_SCALE_BETWEEN_50_500",
		4: "TEAM_SCALE_MORE_THAN_500",
	}
	TeamScale_value = map[string]int32{
		"TEAM_SCALE_UNSPECIFIED":    0,
		"TEAM_SCALE_BETWEEN_1_10":   1,
		"TEAM_SCALE_BETWEEN_10_50":  2,
		"TEAM_SCALE_BETWEEN_50_500": 3,
		"TEAM_SCALE_MORE_THAN_500":  4,
	}
)

func (x TeamScale) Enum() *TeamScale {
	p := new(TeamScale)
	*p = x
	return p
}

func (x TeamScale) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TeamScale) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_team_team_proto_enumTypes[2].Descriptor()
}

func (TeamScale) Type() protoreflect.EnumType {
	return &file_tanlive_team_team_proto_enumTypes[2]
}

func (x TeamScale) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TeamScale.Descriptor instead.
func (TeamScale) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{2}
}

// 员工状态
type StaffState int32

const (
	StaffState_STAFF_STATE_UNSPECIFIED StaffState = 0
	// 正常
	StaffState_STAFF_STATE_VALID StaffState = 1
	// 已删除
	StaffState_STAFF_STATE_DELETED StaffState = 2
)

// Enum value maps for StaffState.
var (
	StaffState_name = map[int32]string{
		0: "STAFF_STATE_UNSPECIFIED",
		1: "STAFF_STATE_VALID",
		2: "STAFF_STATE_DELETED",
	}
	StaffState_value = map[string]int32{
		"STAFF_STATE_UNSPECIFIED": 0,
		"STAFF_STATE_VALID":       1,
		"STAFF_STATE_DELETED":     2,
	}
)

func (x StaffState) Enum() *StaffState {
	p := new(StaffState)
	*p = x
	return p
}

func (x StaffState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StaffState) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_team_team_proto_enumTypes[3].Descriptor()
}

func (StaffState) Type() protoreflect.EnumType {
	return &file_tanlive_team_team_proto_enumTypes[3]
}

func (x StaffState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StaffState.Descriptor instead.
func (StaffState) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{3}
}

// 角色类型
type RoleType int32

const (
	RoleType_ROLE_TYPE_UNSPECIFIED RoleType = 0
	// 用户创建的
	RoleType_ROLE_TYPE_USER_CREATED RoleType = 1
	// 管理员
	RoleType_ROLE_TYPE_ADMIN RoleType = 2
	// 未分类
	RoleType_ROLE_TYPE_UNCLASSIFIED RoleType = 3
)

// Enum value maps for RoleType.
var (
	RoleType_name = map[int32]string{
		0: "ROLE_TYPE_UNSPECIFIED",
		1: "ROLE_TYPE_USER_CREATED",
		2: "ROLE_TYPE_ADMIN",
		3: "ROLE_TYPE_UNCLASSIFIED",
	}
	RoleType_value = map[string]int32{
		"ROLE_TYPE_UNSPECIFIED":  0,
		"ROLE_TYPE_USER_CREATED": 1,
		"ROLE_TYPE_ADMIN":        2,
		"ROLE_TYPE_UNCLASSIFIED": 3,
	}
)

func (x RoleType) Enum() *RoleType {
	p := new(RoleType)
	*p = x
	return p
}

func (x RoleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoleType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_team_team_proto_enumTypes[4].Descriptor()
}

func (RoleType) Type() protoreflect.EnumType {
	return &file_tanlive_team_team_proto_enumTypes[4]
}

func (x RoleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoleType.Descriptor instead.
func (RoleType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{4}
}

// 完整团队信息
type FullTeam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队信息
	TeamInfo *TeamInfo `protobuf:"bytes,1,opt,name=team_info,json=teamInfo,proto3" json:"team_info,omitempty"`
}

func (x *FullTeam) Reset() {
	*x = FullTeam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_team_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FullTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullTeam) ProtoMessage() {}

func (x *FullTeam) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_team_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullTeam.ProtoReflect.Descriptor instead.
func (*FullTeam) Descriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{0}
}

func (x *FullTeam) GetTeamInfo() *TeamInfo {
	if x != nil {
		return x.TeamInfo
	}
	return nil
}

// 团队信息
type TeamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 创建人
	CreateBy uint64 `protobuf:"varint,2,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// 创建时间
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 最后编辑者
	UpdateBy uint64 `protobuf:"varint,4,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 最后编辑时间
	UpdateDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	// 主数据ID
	MainId uint64 `protobuf:"varint,6,opt,name=main_id,json=mainId,proto3" json:"main_id,omitempty"`
	// 团队持有者ID
	HolderId uint64 `protobuf:"varint,7,opt,name=holder_id,json=holderId,proto3" json:"holder_id,omitempty"`
	// 团队主体名称
	FullName string `protobuf:"bytes,8,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	// 团队简称
	ShortName string `protobuf:"bytes,9,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	// 共创等级
	Level TeamLevel `protobuf:"varint,10,opt,name=level,proto3,enum=tanlive.team.TeamLevel" json:"level,omitempty"`
	// UGC状态
	UgcState base.UgcState `protobuf:"varint,11,opt,name=ugc_state,json=ugcState,proto3,enum=tanlive.base.UgcState" json:"ugc_state,omitempty"`
	// 是否为虚拟团队
	IsVirtual bool `protobuf:"varint,12,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual,omitempty"`
	// 是否为草稿
	IsDraft bool `protobuf:"varint,13,opt,name=is_draft,json=isDraft,proto3" json:"is_draft,omitempty"`
	// 是否已认证
	IsVerified bool `protobuf:"varint,14,opt,name=is_verified,json=isVerified,proto3" json:"is_verified,omitempty"`
	// 是否已发布
	IsPublished bool `protobuf:"varint,15,opt,name=is_published,json=isPublished,proto3" json:"is_published,omitempty"`
	// 是否已注销
	IsDeregistered bool `protobuf:"varint,16,opt,name=is_deregistered,json=isDeregistered,proto3" json:"is_deregistered,omitempty"`
	// 源语言
	SourceLang string `protobuf:"bytes,17,opt,name=source_lang,json=sourceLang,proto3" json:"source_lang,omitempty"`
	// 检测语言
	DetectedLang string `protobuf:"bytes,18,opt,name=detected_lang,json=detectedLang,proto3" json:"detected_lang,omitempty"`
	// 主体国家
	PrincipalCountry string `protobuf:"bytes,19,opt,name=principal_country,json=principalCountry,proto3" json:"principal_country,omitempty"`
	// 团队LOGO
	LogoUrl string `protobuf:"bytes,20,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 一句话介绍
	BriefIntro string `protobuf:"bytes,21,opt,name=brief_intro,json=briefIntro,proto3" json:"brief_intro,omitempty"`
}

func (x *TeamInfo) Reset() {
	*x = TeamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_team_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamInfo) ProtoMessage() {}

func (x *TeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_team_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamInfo.ProtoReflect.Descriptor instead.
func (*TeamInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{1}
}

func (x *TeamInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeamInfo) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *TeamInfo) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *TeamInfo) GetUpdateBy() uint64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *TeamInfo) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *TeamInfo) GetMainId() uint64 {
	if x != nil {
		return x.MainId
	}
	return 0
}

func (x *TeamInfo) GetHolderId() uint64 {
	if x != nil {
		return x.HolderId
	}
	return 0
}

func (x *TeamInfo) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *TeamInfo) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *TeamInfo) GetLevel() TeamLevel {
	if x != nil {
		return x.Level
	}
	return TeamLevel_TEAM_LEVEL_UNSPECIFIED
}

func (x *TeamInfo) GetUgcState() base.UgcState {
	if x != nil {
		return x.UgcState
	}
	return base.UgcState(0)
}

func (x *TeamInfo) GetIsVirtual() bool {
	if x != nil {
		return x.IsVirtual
	}
	return false
}

func (x *TeamInfo) GetIsDraft() bool {
	if x != nil {
		return x.IsDraft
	}
	return false
}

func (x *TeamInfo) GetIsVerified() bool {
	if x != nil {
		return x.IsVerified
	}
	return false
}

func (x *TeamInfo) GetIsPublished() bool {
	if x != nil {
		return x.IsPublished
	}
	return false
}

func (x *TeamInfo) GetIsDeregistered() bool {
	if x != nil {
		return x.IsDeregistered
	}
	return false
}

func (x *TeamInfo) GetSourceLang() string {
	if x != nil {
		return x.SourceLang
	}
	return ""
}

func (x *TeamInfo) GetDetectedLang() string {
	if x != nil {
		return x.DetectedLang
	}
	return ""
}

func (x *TeamInfo) GetPrincipalCountry() string {
	if x != nil {
		return x.PrincipalCountry
	}
	return ""
}

func (x *TeamInfo) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *TeamInfo) GetBriefIntro() string {
	if x != nil {
		return x.BriefIntro
	}
	return ""
}

// 团多投资者
type TeamInvestor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 投资者名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 投资者团队ID
	InvestorTeamId uint64 `protobuf:"varint,4,opt,name=investor_team_id,json=investorTeamId,proto3" json:"investor_team_id,omitempty"`
}

func (x *TeamInvestor) Reset() {
	*x = TeamInvestor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_team_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamInvestor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamInvestor) ProtoMessage() {}

func (x *TeamInvestor) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_team_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamInvestor.ProtoReflect.Descriptor instead.
func (*TeamInvestor) Descriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{2}
}

func (x *TeamInvestor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeamInvestor) GetInvestorTeamId() uint64 {
	if x != nil {
		return x.InvestorTeamId
	}
	return 0
}

// 团队成员
type TeamMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 头像
	AvatarUrl string `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	// 职位
	Position string `protobuf:"bytes,4,opt,name=position,proto3" json:"position,omitempty"`
	// 介绍
	Introduction string `protobuf:"bytes,5,opt,name=introduction,proto3" json:"introduction,omitempty"`
	// 可见规则
	VisibleRule *base.VisibleRule `protobuf:"bytes,6,opt,name=visible_rule,json=visibleRule,proto3" json:"visible_rule,omitempty"`
}

func (x *TeamMember) Reset() {
	*x = TeamMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_team_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamMember) ProtoMessage() {}

func (x *TeamMember) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_team_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamMember.ProtoReflect.Descriptor instead.
func (*TeamMember) Descriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{3}
}

func (x *TeamMember) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeamMember) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeamMember) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *TeamMember) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *TeamMember) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *TeamMember) GetVisibleRule() *base.VisibleRule {
	if x != nil {
		return x.VisibleRule
	}
	return nil
}

// 团队角色
type TeamRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 角色名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 角色类型
	Type RoleType `protobuf:"varint,3,opt,name=type,proto3,enum=tanlive.team.RoleType" json:"type,omitempty"`
	// 创建者
	CreateBy uint64 `protobuf:"varint,4,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// 创建
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 更新者
	UpdateBy uint64 `protobuf:"varint,6,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 更新时间
	UpdateDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
}

func (x *TeamRole) Reset() {
	*x = TeamRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_team_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamRole) ProtoMessage() {}

func (x *TeamRole) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_team_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamRole.ProtoReflect.Descriptor instead.
func (*TeamRole) Descriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{4}
}

func (x *TeamRole) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeamRole) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeamRole) GetType() RoleType {
	if x != nil {
		return x.Type
	}
	return RoleType_ROLE_TYPE_UNSPECIFIED
}

func (x *TeamRole) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *TeamRole) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *TeamRole) GetUpdateBy() uint64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *TeamRole) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

type CustomRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Route string `protobuf:"bytes,2,opt,name=route,proto3" json:"route,omitempty"`
	// UGC ID
	UgcId uint64 `protobuf:"varint,3,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
	// UGC类型
	UgcType base.DataType `protobuf:"varint,4,opt,name=ugc_type,json=ugcType,proto3,enum=tanlive.base.DataType" json:"ugc_type,omitempty"`
	// 更新者
	LastUpdateBy uint64 `protobuf:"varint,5,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	// 更新时间
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
}

func (x *CustomRoute) Reset() {
	*x = CustomRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_team_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomRoute) ProtoMessage() {}

func (x *CustomRoute) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_team_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomRoute.ProtoReflect.Descriptor instead.
func (*CustomRoute) Descriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{5}
}

func (x *CustomRoute) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomRoute) GetRoute() string {
	if x != nil {
		return x.Route
	}
	return ""
}

func (x *CustomRoute) GetUgcId() uint64 {
	if x != nil {
		return x.UgcId
	}
	return 0
}

func (x *CustomRoute) GetUgcType() base.DataType {
	if x != nil {
		return x.UgcType
	}
	return base.DataType(0)
}

func (x *CustomRoute) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *CustomRoute) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

// 团队卡片
type TeamCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 简称
	ShortName string `protobuf:"bytes,2,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	// 主体名称
	FullName string `protobuf:"bytes,3,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	// 是否认证
	IsVerified bool `protobuf:"varint,4,opt,name=is_verified,json=isVerified,proto3" json:"is_verified,omitempty"`
	// 一句话介绍
	BriefIntro string `protobuf:"bytes,5,opt,name=brief_intro,json=briefIntro,proto3" json:"brief_intro,omitempty"`
	// 共创等级
	Level TeamLevel `protobuf:"varint,6,opt,name=level,proto3,enum=tanlive.team.TeamLevel" json:"level,omitempty"`
	// 团队LOGO
	LogoUrl string `protobuf:"bytes,7,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 是否已发布
	IsPublished bool `protobuf:"varint,8,opt,name=is_published,json=isPublished,proto3" json:"is_published,omitempty"`
}

func (x *TeamCard) Reset() {
	*x = TeamCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_team_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamCard) ProtoMessage() {}

func (x *TeamCard) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_team_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamCard.ProtoReflect.Descriptor instead.
func (*TeamCard) Descriptor() ([]byte, []int) {
	return file_tanlive_team_team_proto_rawDescGZIP(), []int{6}
}

func (x *TeamCard) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeamCard) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *TeamCard) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *TeamCard) GetIsVerified() bool {
	if x != nil {
		return x.IsVerified
	}
	return false
}

func (x *TeamCard) GetBriefIntro() string {
	if x != nil {
		return x.BriefIntro
	}
	return ""
}

func (x *TeamCard) GetLevel() TeamLevel {
	if x != nil {
		return x.Level
	}
	return TeamLevel_TEAM_LEVEL_UNSPECIFIED
}

func (x *TeamCard) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *TeamCard) GetIsPublished() bool {
	if x != nil {
		return x.IsPublished
	}
	return false
}

var File_tanlive_team_team_proto protoreflect.FileDescriptor

var file_tanlive_team_team_proto_rawDesc = []byte{
	0x0a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x74,
	0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x08, 0x46, 0x75, 0x6c, 0x6c, 0x54,
	0x65, 0x61, 0x6d, 0x12, 0x33, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xfa, 0x05, 0x0a, 0x08, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x69,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x61, 0x69, 0x6e,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x33, 0x0a, 0x09, 0x75, 0x67,
	0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x67, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x75, 0x67, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x73, 0x5f, 0x64, 0x72, 0x61, 0x66, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x69, 0x73, 0x44, 0x72, 0x61, 0x66, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73,
	0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x44, 0x65, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x2b, 0x0a, 0x11,
	0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70,
	0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67,
	0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67,
	0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x69, 0x65, 0x66, 0x5f, 0x69, 0x6e,
	0x74, 0x72, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x72, 0x69, 0x65, 0x66,
	0x49, 0x6e, 0x74, 0x72, 0x6f, 0x22, 0x4c, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x64, 0x22, 0xcd, 0x01, 0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0c, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x56, 0x69, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0b, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x22, 0x8e, 0x02, 0x0a, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x6f, 0x6c, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61,
	0x6d, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x22, 0xe9, 0x01, 0x0a, 0x0b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x67,
	0x63, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49,
	0x64, 0x12, 0x31, 0x0a, 0x08, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x75, 0x67, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6c, 0x61,
	0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x22, 0x85, 0x02, 0x0a, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72,
	0x69, 0x65, 0x66, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x62, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x12, 0x2d, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f,
	0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f,
	0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x2a, 0x5c, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x10, 0x01, 0x12,
	0x15, 0x0a, 0x11, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x10, 0x02, 0x2a, 0x78, 0x0a, 0x09, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4c, 0x45, 0x56, 0x45,
	0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1a, 0x0a, 0x16, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x54,
	0x45, 0x41, 0x4d, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x49, 0x54,
	0x54, 0x45, 0x52, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4c, 0x45,
	0x56, 0x45, 0x4c, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x10, 0x03,
	0x2a, 0x9f, 0x01, 0x0a, 0x09, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x1a,
	0x0a, 0x16, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x43, 0x41, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x45,
	0x41, 0x4d, 0x5f, 0x53, 0x43, 0x41, 0x4c, 0x45, 0x5f, 0x42, 0x45, 0x54, 0x57, 0x45, 0x45, 0x4e,
	0x5f, 0x31, 0x5f, 0x31, 0x30, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x53, 0x43, 0x41, 0x4c, 0x45, 0x5f, 0x42, 0x45, 0x54, 0x57, 0x45, 0x45, 0x4e, 0x5f, 0x31, 0x30,
	0x5f, 0x35, 0x30, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x43,
	0x41, 0x4c, 0x45, 0x5f, 0x42, 0x45, 0x54, 0x57, 0x45, 0x45, 0x4e, 0x5f, 0x35, 0x30, 0x5f, 0x35,
	0x30, 0x30, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x43, 0x41,
	0x4c, 0x45, 0x5f, 0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x35, 0x30, 0x30,
	0x10, 0x04, 0x2a, 0x59, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1b, 0x0a, 0x17, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a,
	0x11, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x72, 0x0a,
	0x08, 0x52, 0x6f, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x4f, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x13, 0x0a, 0x0f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44,
	0x4d, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x03, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65,
	0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x65, 0x61,
	0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_team_team_proto_rawDescOnce sync.Once
	file_tanlive_team_team_proto_rawDescData = file_tanlive_team_team_proto_rawDesc
)

func file_tanlive_team_team_proto_rawDescGZIP() []byte {
	file_tanlive_team_team_proto_rawDescOnce.Do(func() {
		file_tanlive_team_team_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_team_team_proto_rawDescData)
	})
	return file_tanlive_team_team_proto_rawDescData
}

var file_tanlive_team_team_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_tanlive_team_team_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_tanlive_team_team_proto_goTypes = []interface{}{
	(EntityType)(0),               // 0: tanlive.team.EntityType
	(TeamLevel)(0),                // 1: tanlive.team.TeamLevel
	(TeamScale)(0),                // 2: tanlive.team.TeamScale
	(StaffState)(0),               // 3: tanlive.team.StaffState
	(RoleType)(0),                 // 4: tanlive.team.RoleType
	(*FullTeam)(nil),              // 5: tanlive.team.FullTeam
	(*TeamInfo)(nil),              // 6: tanlive.team.TeamInfo
	(*TeamInvestor)(nil),          // 7: tanlive.team.TeamInvestor
	(*TeamMember)(nil),            // 8: tanlive.team.TeamMember
	(*TeamRole)(nil),              // 9: tanlive.team.TeamRole
	(*CustomRoute)(nil),           // 10: tanlive.team.CustomRoute
	(*TeamCard)(nil),              // 11: tanlive.team.TeamCard
	(*timestamppb.Timestamp)(nil), // 12: google.protobuf.Timestamp
	(base.UgcState)(0),            // 13: tanlive.base.UgcState
	(*base.VisibleRule)(nil),      // 14: tanlive.base.VisibleRule
	(base.DataType)(0),            // 15: tanlive.base.DataType
}
var file_tanlive_team_team_proto_depIdxs = []int32{
	6,  // 0: tanlive.team.FullTeam.team_info:type_name -> tanlive.team.TeamInfo
	12, // 1: tanlive.team.TeamInfo.create_date:type_name -> google.protobuf.Timestamp
	12, // 2: tanlive.team.TeamInfo.update_date:type_name -> google.protobuf.Timestamp
	1,  // 3: tanlive.team.TeamInfo.level:type_name -> tanlive.team.TeamLevel
	13, // 4: tanlive.team.TeamInfo.ugc_state:type_name -> tanlive.base.UgcState
	14, // 5: tanlive.team.TeamMember.visible_rule:type_name -> tanlive.base.VisibleRule
	4,  // 6: tanlive.team.TeamRole.type:type_name -> tanlive.team.RoleType
	12, // 7: tanlive.team.TeamRole.create_date:type_name -> google.protobuf.Timestamp
	12, // 8: tanlive.team.TeamRole.update_date:type_name -> google.protobuf.Timestamp
	15, // 9: tanlive.team.CustomRoute.ugc_type:type_name -> tanlive.base.DataType
	12, // 10: tanlive.team.CustomRoute.last_update_date:type_name -> google.protobuf.Timestamp
	1,  // 11: tanlive.team.TeamCard.level:type_name -> tanlive.team.TeamLevel
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_tanlive_team_team_proto_init() }
func file_tanlive_team_team_proto_init() {
	if File_tanlive_team_team_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_team_team_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FullTeam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_team_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_team_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamInvestor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_team_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_team_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_team_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_team_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_team_team_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_team_team_proto_goTypes,
		DependencyIndexes: file_tanlive_team_team_proto_depIdxs,
		EnumInfos:         file_tanlive_team_team_proto_enumTypes,
		MessageInfos:      file_tanlive_team_team_proto_msgTypes,
	}.Build()
	File_tanlive_team_team_proto = out.File
	file_tanlive_team_team_proto_rawDesc = nil
	file_tanlive_team_team_proto_goTypes = nil
	file_tanlive_team_team_proto_depIdxs = nil
}
