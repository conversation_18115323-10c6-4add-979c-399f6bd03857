// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/team/service.proto

package team

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/fieldmaskpb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for TeamService service

func NewTeamServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for TeamService service

type TeamService interface {
	// 更新团队自定义路由
	UpdateUgcCustomRouteByUgcId(ctx context.Context, in *CustomRoute, opts ...client.CallOption) (*emptypb.Empty, error)
	// 检查ugc的路由是否重名
	DescribeUgcIdCustomRouteRepeated(ctx context.Context, in *ReqDescribeUgcIdCustomRouteRepeated, opts ...client.CallOption) (*ResDescribeUgcIdCustomRouteRepeated, error)
	// 前端通过自定义路由获取ugc信息
	DescribeCustomRouteUgcByRoute(ctx context.Context, in *ReqDescribeCustomRouteUgcByRoute, opts ...client.CallOption) (*ResDescribeCustomRouteUgcByRoute, error)
	// 释放自定义路由
	DeleteCustomRoute(ctx context.Context, in *ReqDeleteCustomRoute, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询用户所属团队
	GetUserTeams(ctx context.Context, in *ReqGetUserTeams, opts ...client.CallOption) (*RspGetUserTeams, error)
	// 查询团队列表
	GetTeams(ctx context.Context, in *ReqGetTeams, opts ...client.CallOption) (*RspGetTeams, error)
	// 查询所有团队id
	GetTeamIds(ctx context.Context, in *ReqGetTeamIds, opts ...client.CallOption) (*RspGetTeamIds, error)
	// 获取团队员工id
	GetTeamStaff(ctx context.Context, in *ReqGetTeamStaff, opts ...client.CallOption) (*RspGetTeamStaff, error)
}

type teamService struct {
	c    client.Client
	name string
}

func NewTeamService(name string, c client.Client) TeamService {
	return &teamService{
		c:    c,
		name: name,
	}
}

func (c *teamService) UpdateUgcCustomRouteByUgcId(ctx context.Context, in *CustomRoute, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TeamService.UpdateUgcCustomRouteByUgcId", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamService) DescribeUgcIdCustomRouteRepeated(ctx context.Context, in *ReqDescribeUgcIdCustomRouteRepeated, opts ...client.CallOption) (*ResDescribeUgcIdCustomRouteRepeated, error) {
	req := c.c.NewRequest(c.name, "TeamService.DescribeUgcIdCustomRouteRepeated", in)
	out := new(ResDescribeUgcIdCustomRouteRepeated)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamService) DescribeCustomRouteUgcByRoute(ctx context.Context, in *ReqDescribeCustomRouteUgcByRoute, opts ...client.CallOption) (*ResDescribeCustomRouteUgcByRoute, error) {
	req := c.c.NewRequest(c.name, "TeamService.DescribeCustomRouteUgcByRoute", in)
	out := new(ResDescribeCustomRouteUgcByRoute)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamService) DeleteCustomRoute(ctx context.Context, in *ReqDeleteCustomRoute, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TeamService.DeleteCustomRoute", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamService) GetUserTeams(ctx context.Context, in *ReqGetUserTeams, opts ...client.CallOption) (*RspGetUserTeams, error) {
	req := c.c.NewRequest(c.name, "TeamService.GetUserTeams", in)
	out := new(RspGetUserTeams)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamService) GetTeams(ctx context.Context, in *ReqGetTeams, opts ...client.CallOption) (*RspGetTeams, error) {
	req := c.c.NewRequest(c.name, "TeamService.GetTeams", in)
	out := new(RspGetTeams)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamService) GetTeamIds(ctx context.Context, in *ReqGetTeamIds, opts ...client.CallOption) (*RspGetTeamIds, error) {
	req := c.c.NewRequest(c.name, "TeamService.GetTeamIds", in)
	out := new(RspGetTeamIds)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamService) GetTeamStaff(ctx context.Context, in *ReqGetTeamStaff, opts ...client.CallOption) (*RspGetTeamStaff, error) {
	req := c.c.NewRequest(c.name, "TeamService.GetTeamStaff", in)
	out := new(RspGetTeamStaff)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for TeamService service

type TeamServiceHandler interface {
	// 更新团队自定义路由
	UpdateUgcCustomRouteByUgcId(context.Context, *CustomRoute, *emptypb.Empty) error
	// 检查ugc的路由是否重名
	DescribeUgcIdCustomRouteRepeated(context.Context, *ReqDescribeUgcIdCustomRouteRepeated, *ResDescribeUgcIdCustomRouteRepeated) error
	// 前端通过自定义路由获取ugc信息
	DescribeCustomRouteUgcByRoute(context.Context, *ReqDescribeCustomRouteUgcByRoute, *ResDescribeCustomRouteUgcByRoute) error
	// 释放自定义路由
	DeleteCustomRoute(context.Context, *ReqDeleteCustomRoute, *emptypb.Empty) error
	// 查询用户所属团队
	GetUserTeams(context.Context, *ReqGetUserTeams, *RspGetUserTeams) error
	// 查询团队列表
	GetTeams(context.Context, *ReqGetTeams, *RspGetTeams) error
	// 查询所有团队id
	GetTeamIds(context.Context, *ReqGetTeamIds, *RspGetTeamIds) error
	// 获取团队员工id
	GetTeamStaff(context.Context, *ReqGetTeamStaff, *RspGetTeamStaff) error
}

func RegisterTeamServiceHandler(s server.Server, hdlr TeamServiceHandler, opts ...server.HandlerOption) error {
	type teamService interface {
		UpdateUgcCustomRouteByUgcId(ctx context.Context, in *CustomRoute, out *emptypb.Empty) error
		DescribeUgcIdCustomRouteRepeated(ctx context.Context, in *ReqDescribeUgcIdCustomRouteRepeated, out *ResDescribeUgcIdCustomRouteRepeated) error
		DescribeCustomRouteUgcByRoute(ctx context.Context, in *ReqDescribeCustomRouteUgcByRoute, out *ResDescribeCustomRouteUgcByRoute) error
		DeleteCustomRoute(ctx context.Context, in *ReqDeleteCustomRoute, out *emptypb.Empty) error
		GetUserTeams(ctx context.Context, in *ReqGetUserTeams, out *RspGetUserTeams) error
		GetTeams(ctx context.Context, in *ReqGetTeams, out *RspGetTeams) error
		GetTeamIds(ctx context.Context, in *ReqGetTeamIds, out *RspGetTeamIds) error
		GetTeamStaff(ctx context.Context, in *ReqGetTeamStaff, out *RspGetTeamStaff) error
	}
	type TeamService struct {
		teamService
	}
	h := &teamServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&TeamService{h}, opts...))
}

type teamServiceHandler struct {
	TeamServiceHandler
}

func (h *teamServiceHandler) UpdateUgcCustomRouteByUgcId(ctx context.Context, in *CustomRoute, out *emptypb.Empty) error {
	return h.TeamServiceHandler.UpdateUgcCustomRouteByUgcId(ctx, in, out)
}

func (h *teamServiceHandler) DescribeUgcIdCustomRouteRepeated(ctx context.Context, in *ReqDescribeUgcIdCustomRouteRepeated, out *ResDescribeUgcIdCustomRouteRepeated) error {
	return h.TeamServiceHandler.DescribeUgcIdCustomRouteRepeated(ctx, in, out)
}

func (h *teamServiceHandler) DescribeCustomRouteUgcByRoute(ctx context.Context, in *ReqDescribeCustomRouteUgcByRoute, out *ResDescribeCustomRouteUgcByRoute) error {
	return h.TeamServiceHandler.DescribeCustomRouteUgcByRoute(ctx, in, out)
}

func (h *teamServiceHandler) DeleteCustomRoute(ctx context.Context, in *ReqDeleteCustomRoute, out *emptypb.Empty) error {
	return h.TeamServiceHandler.DeleteCustomRoute(ctx, in, out)
}

func (h *teamServiceHandler) GetUserTeams(ctx context.Context, in *ReqGetUserTeams, out *RspGetUserTeams) error {
	return h.TeamServiceHandler.GetUserTeams(ctx, in, out)
}

func (h *teamServiceHandler) GetTeams(ctx context.Context, in *ReqGetTeams, out *RspGetTeams) error {
	return h.TeamServiceHandler.GetTeams(ctx, in, out)
}

func (h *teamServiceHandler) GetTeamIds(ctx context.Context, in *ReqGetTeamIds, out *RspGetTeamIds) error {
	return h.TeamServiceHandler.GetTeamIds(ctx, in, out)
}

func (h *teamServiceHandler) GetTeamStaff(ctx context.Context, in *ReqGetTeamStaff, out *RspGetTeamStaff) error {
	return h.TeamServiceHandler.GetTeamStaff(ctx, in, out)
}
