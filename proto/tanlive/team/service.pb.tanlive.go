// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package team

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"CustomRoute": "required",
	}, &ReqUpdateUgcCustomRoute{})
}

func (x *ReqUpdateUgcCustomRoute) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"CustomRoute": "required",
	}, &ReqCreateUgcCustomRoute{})
}

func (x *ReqCreateUgcCustomRoute) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required,dive,required",
	}, &ReqGetUserTeams{})
}

func (x *ReqGetUserTeams) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TeamIds": "required",
	}, &ReqGetTeamStaff{})
}

func (x *ReqGetTeamStaff) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqUpdateUgcCustomRoute) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateUgcCustomRoute)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateUgcCustomRoute)
	if v, ok := any(y.CustomRoute).(interface{ MaskInLog() any }); ok {
		y.CustomRoute = v.MaskInLog().(*CustomRoute)
	}

	return y
}

func (x *ReqUpdateUgcCustomRoute) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateUgcCustomRoute)(nil)
	}

	y := x
	if v, ok := any(y.CustomRoute).(interface{ MaskInRpc() any }); ok {
		y.CustomRoute = v.MaskInRpc().(*CustomRoute)
	}

	return y
}

func (x *ReqUpdateUgcCustomRoute) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateUgcCustomRoute)(nil)
	}

	y := x
	if v, ok := any(y.CustomRoute).(interface{ MaskInBff() any }); ok {
		y.CustomRoute = v.MaskInBff().(*CustomRoute)
	}

	return y
}

func (x *ReqCreateUgcCustomRoute) MaskInLog() any {
	if x == nil {
		return (*ReqCreateUgcCustomRoute)(nil)
	}

	y := proto.Clone(x).(*ReqCreateUgcCustomRoute)
	if v, ok := any(y.CustomRoute).(interface{ MaskInLog() any }); ok {
		y.CustomRoute = v.MaskInLog().(*CustomRoute)
	}

	return y
}

func (x *ReqCreateUgcCustomRoute) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateUgcCustomRoute)(nil)
	}

	y := x
	if v, ok := any(y.CustomRoute).(interface{ MaskInRpc() any }); ok {
		y.CustomRoute = v.MaskInRpc().(*CustomRoute)
	}

	return y
}

func (x *ReqCreateUgcCustomRoute) MaskInBff() any {
	if x == nil {
		return (*ReqCreateUgcCustomRoute)(nil)
	}

	y := x
	if v, ok := any(y.CustomRoute).(interface{ MaskInBff() any }); ok {
		y.CustomRoute = v.MaskInBff().(*CustomRoute)
	}

	return y
}

func (x *ResDescribeCustomRouteByUgcId) MaskInLog() any {
	if x == nil {
		return (*ResDescribeCustomRouteByUgcId)(nil)
	}

	y := proto.Clone(x).(*ResDescribeCustomRouteByUgcId)
	if v, ok := any(y.CustomRoute).(interface{ MaskInLog() any }); ok {
		y.CustomRoute = v.MaskInLog().(*CustomRoute)
	}

	return y
}

func (x *ResDescribeCustomRouteByUgcId) MaskInRpc() any {
	if x == nil {
		return (*ResDescribeCustomRouteByUgcId)(nil)
	}

	y := x
	if v, ok := any(y.CustomRoute).(interface{ MaskInRpc() any }); ok {
		y.CustomRoute = v.MaskInRpc().(*CustomRoute)
	}

	return y
}

func (x *ResDescribeCustomRouteByUgcId) MaskInBff() any {
	if x == nil {
		return (*ResDescribeCustomRouteByUgcId)(nil)
	}

	y := x
	if v, ok := any(y.CustomRoute).(interface{ MaskInBff() any }); ok {
		y.CustomRoute = v.MaskInBff().(*CustomRoute)
	}

	return y
}

func (x *ResCheckUgcCustomRouteInUsed) MaskInLog() any {
	if x == nil {
		return (*ResCheckUgcCustomRouteInUsed)(nil)
	}

	y := proto.Clone(x).(*ResCheckUgcCustomRouteInUsed)
	if v, ok := any(y.CustomRoute).(interface{ MaskInLog() any }); ok {
		y.CustomRoute = v.MaskInLog().(*CustomRoute)
	}

	return y
}

func (x *ResCheckUgcCustomRouteInUsed) MaskInRpc() any {
	if x == nil {
		return (*ResCheckUgcCustomRouteInUsed)(nil)
	}

	y := x
	if v, ok := any(y.CustomRoute).(interface{ MaskInRpc() any }); ok {
		y.CustomRoute = v.MaskInRpc().(*CustomRoute)
	}

	return y
}

func (x *ResCheckUgcCustomRouteInUsed) MaskInBff() any {
	if x == nil {
		return (*ResCheckUgcCustomRouteInUsed)(nil)
	}

	y := x
	if v, ok := any(y.CustomRoute).(interface{ MaskInBff() any }); ok {
		y.CustomRoute = v.MaskInBff().(*CustomRoute)
	}

	return y
}

func (x *ResDescribeUgcIdCustomRouteRepeated) MaskInLog() any {
	if x == nil {
		return (*ResDescribeUgcIdCustomRouteRepeated)(nil)
	}

	y := proto.Clone(x).(*ResDescribeUgcIdCustomRouteRepeated)
	if v, ok := any(y.ExistCustomRoute).(interface{ MaskInLog() any }); ok {
		y.ExistCustomRoute = v.MaskInLog().(*CustomRoute)
	}

	return y
}

func (x *ResDescribeUgcIdCustomRouteRepeated) MaskInRpc() any {
	if x == nil {
		return (*ResDescribeUgcIdCustomRouteRepeated)(nil)
	}

	y := x
	if v, ok := any(y.ExistCustomRoute).(interface{ MaskInRpc() any }); ok {
		y.ExistCustomRoute = v.MaskInRpc().(*CustomRoute)
	}

	return y
}

func (x *ResDescribeUgcIdCustomRouteRepeated) MaskInBff() any {
	if x == nil {
		return (*ResDescribeUgcIdCustomRouteRepeated)(nil)
	}

	y := x
	if v, ok := any(y.ExistCustomRoute).(interface{ MaskInBff() any }); ok {
		y.ExistCustomRoute = v.MaskInBff().(*CustomRoute)
	}

	return y
}

func (x *ResDescribeCustomRouteUgcByRoute) MaskInLog() any {
	if x == nil {
		return (*ResDescribeCustomRouteUgcByRoute)(nil)
	}

	y := proto.Clone(x).(*ResDescribeCustomRouteUgcByRoute)
	for k, v := range y.CustomRoutes {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CustomRoutes[k] = vv.MaskInLog().(*CustomRoute)
		}
	}

	return y
}

func (x *ResDescribeCustomRouteUgcByRoute) MaskInRpc() any {
	if x == nil {
		return (*ResDescribeCustomRouteUgcByRoute)(nil)
	}

	y := x
	for k, v := range y.CustomRoutes {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CustomRoutes[k] = vv.MaskInRpc().(*CustomRoute)
		}
	}

	return y
}

func (x *ResDescribeCustomRouteUgcByRoute) MaskInBff() any {
	if x == nil {
		return (*ResDescribeCustomRouteUgcByRoute)(nil)
	}

	y := x
	for k, v := range y.CustomRoutes {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CustomRoutes[k] = vv.MaskInBff().(*CustomRoute)
		}
	}

	return y
}

func (x *RspGetUserTeams) MaskInLog() any {
	if x == nil {
		return (*RspGetUserTeams)(nil)
	}

	y := proto.Clone(x).(*RspGetUserTeams)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*FullTeam)
		}
	}

	return y
}

func (x *RspGetUserTeams) MaskInRpc() any {
	if x == nil {
		return (*RspGetUserTeams)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*FullTeam)
		}
	}

	return y
}

func (x *RspGetUserTeams) MaskInBff() any {
	if x == nil {
		return (*RspGetUserTeams)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*FullTeam)
		}
	}

	return y
}

func (x *ReqGetTeams) MaskInLog() any {
	if x == nil {
		return (*ReqGetTeams)(nil)
	}

	y := proto.Clone(x).(*ReqGetTeams)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetTeams_Filter)
	}

	return y
}

func (x *ReqGetTeams) MaskInRpc() any {
	if x == nil {
		return (*ReqGetTeams)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetTeams_Filter)
	}

	return y
}

func (x *ReqGetTeams) MaskInBff() any {
	if x == nil {
		return (*ReqGetTeams)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetTeams_Filter)
	}

	return y
}

func (x *RspGetTeams) MaskInLog() any {
	if x == nil {
		return (*RspGetTeams)(nil)
	}

	y := proto.Clone(x).(*RspGetTeams)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*FullTeam)
		}
	}

	return y
}

func (x *RspGetTeams) MaskInRpc() any {
	if x == nil {
		return (*RspGetTeams)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*FullTeam)
		}
	}

	return y
}

func (x *RspGetTeams) MaskInBff() any {
	if x == nil {
		return (*RspGetTeams)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*FullTeam)
		}
	}

	return y
}

func (x *RspGetTeamStaff) MaskInLog() any {
	if x == nil {
		return (*RspGetTeamStaff)(nil)
	}

	y := proto.Clone(x).(*RspGetTeamStaff)
	for k, v := range y.StaffMap {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.StaffMap[k] = vv.MaskInLog().(*RspGetTeamStaff_Staff)
		}
	}

	return y
}

func (x *RspGetTeamStaff) MaskInRpc() any {
	if x == nil {
		return (*RspGetTeamStaff)(nil)
	}

	y := x
	for k, v := range y.StaffMap {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.StaffMap[k] = vv.MaskInRpc().(*RspGetTeamStaff_Staff)
		}
	}

	return y
}

func (x *RspGetTeamStaff) MaskInBff() any {
	if x == nil {
		return (*RspGetTeamStaff)(nil)
	}

	y := x
	for k, v := range y.StaffMap {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.StaffMap[k] = vv.MaskInBff().(*RspGetTeamStaff_Staff)
		}
	}

	return y
}

func (x *ReqUpdateUgcCustomRoute) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CustomRoute).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqCreateUgcCustomRoute) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CustomRoute).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ResDescribeCustomRouteByUgcId) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CustomRoute).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ResCheckUgcCustomRouteInUsed) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CustomRoute).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ResDescribeUgcIdCustomRouteRepeated) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ExistCustomRoute).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ResDescribeCustomRouteUgcByRoute) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.CustomRoutes {
		if sanitizer, ok := any(x.CustomRoutes[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetUserTeams) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetTeams) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetTeams) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetTeamStaff) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.StaffMap {
		if sanitizer, ok := any(x.StaffMap[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
