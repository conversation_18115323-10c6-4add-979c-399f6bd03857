// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/team/service.proto

package team

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/fieldmaskpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 搜索场景
type ReqGetTeams_SearchScene int32

const (
	ReqGetTeams_SEARCH_SCENE_UNSPECIFIED ReqGetTeams_SearchScene = 0
	// AI知识库分享默认设置中搜索
	ReqGetTeams_SEARCH_SCENE_AI_SHARE_SETTING ReqGetTeams_SearchScene = 1
)

// Enum value maps for ReqGetTeams_SearchScene.
var (
	ReqGetTeams_SearchScene_name = map[int32]string{
		0: "SEARCH_SCENE_UNSPECIFIED",
		1: "SEARCH_SCENE_AI_SHARE_SETTING",
	}
	ReqGetTeams_SearchScene_value = map[string]int32{
		"SEARCH_SCENE_UNSPECIFIED":      0,
		"SEARCH_SCENE_AI_SHARE_SETTING": 1,
	}
)

func (x ReqGetTeams_SearchScene) Enum() *ReqGetTeams_SearchScene {
	p := new(ReqGetTeams_SearchScene)
	*p = x
	return p
}

func (x ReqGetTeams_SearchScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReqGetTeams_SearchScene) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_team_service_proto_enumTypes[0].Descriptor()
}

func (ReqGetTeams_SearchScene) Type() protoreflect.EnumType {
	return &file_tanlive_team_service_proto_enumTypes[0]
}

func (x ReqGetTeams_SearchScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReqGetTeams_SearchScene.Descriptor instead.
func (ReqGetTeams_SearchScene) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{14, 0}
}

type ReqUpdateUgcCustomRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomRoute *CustomRoute `protobuf:"bytes,1,opt,name=custom_route,json=customRoute,proto3" json:"custom_route,omitempty"`
}

func (x *ReqUpdateUgcCustomRoute) Reset() {
	*x = ReqUpdateUgcCustomRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateUgcCustomRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateUgcCustomRoute) ProtoMessage() {}

func (x *ReqUpdateUgcCustomRoute) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateUgcCustomRoute.ProtoReflect.Descriptor instead.
func (*ReqUpdateUgcCustomRoute) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqUpdateUgcCustomRoute) GetCustomRoute() *CustomRoute {
	if x != nil {
		return x.CustomRoute
	}
	return nil
}

type ReqUpdateTeamCustomRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId uint64 `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	Route  string `protobuf:"bytes,2,opt,name=route,proto3" json:"route,omitempty"`
}

func (x *ReqUpdateTeamCustomRoute) Reset() {
	*x = ReqUpdateTeamCustomRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateTeamCustomRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateTeamCustomRoute) ProtoMessage() {}

func (x *ReqUpdateTeamCustomRoute) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateTeamCustomRoute.ProtoReflect.Descriptor instead.
func (*ReqUpdateTeamCustomRoute) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{1}
}

func (x *ReqUpdateTeamCustomRoute) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *ReqUpdateTeamCustomRoute) GetRoute() string {
	if x != nil {
		return x.Route
	}
	return ""
}

type ReqCreateUgcCustomRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomRoute *CustomRoute `protobuf:"bytes,1,opt,name=custom_route,json=customRoute,proto3" json:"custom_route,omitempty"`
}

func (x *ReqCreateUgcCustomRoute) Reset() {
	*x = ReqCreateUgcCustomRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateUgcCustomRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateUgcCustomRoute) ProtoMessage() {}

func (x *ReqCreateUgcCustomRoute) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateUgcCustomRoute.ProtoReflect.Descriptor instead.
func (*ReqCreateUgcCustomRoute) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReqCreateUgcCustomRoute) GetCustomRoute() *CustomRoute {
	if x != nil {
		return x.CustomRoute
	}
	return nil
}

type ReqDescribeCustomRouteByUgcId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UgcId uint64 `protobuf:"varint,1,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
}

func (x *ReqDescribeCustomRouteByUgcId) Reset() {
	*x = ReqDescribeCustomRouteByUgcId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeCustomRouteByUgcId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeCustomRouteByUgcId) ProtoMessage() {}

func (x *ReqDescribeCustomRouteByUgcId) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeCustomRouteByUgcId.ProtoReflect.Descriptor instead.
func (*ReqDescribeCustomRouteByUgcId) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{3}
}

func (x *ReqDescribeCustomRouteByUgcId) GetUgcId() uint64 {
	if x != nil {
		return x.UgcId
	}
	return 0
}

type ResDescribeCustomRouteByUgcId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomRoute *CustomRoute `protobuf:"bytes,1,opt,name=custom_route,json=customRoute,proto3" json:"custom_route,omitempty"`
}

func (x *ResDescribeCustomRouteByUgcId) Reset() {
	*x = ResDescribeCustomRouteByUgcId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResDescribeCustomRouteByUgcId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResDescribeCustomRouteByUgcId) ProtoMessage() {}

func (x *ResDescribeCustomRouteByUgcId) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResDescribeCustomRouteByUgcId.ProtoReflect.Descriptor instead.
func (*ResDescribeCustomRouteByUgcId) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{4}
}

func (x *ResDescribeCustomRouteByUgcId) GetCustomRoute() *CustomRoute {
	if x != nil {
		return x.CustomRoute
	}
	return nil
}

type ReqCheckUgcCustomRouteInUsed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UgcId uint64 `protobuf:"varint,1,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
}

func (x *ReqCheckUgcCustomRouteInUsed) Reset() {
	*x = ReqCheckUgcCustomRouteInUsed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCheckUgcCustomRouteInUsed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCheckUgcCustomRouteInUsed) ProtoMessage() {}

func (x *ReqCheckUgcCustomRouteInUsed) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCheckUgcCustomRouteInUsed.ProtoReflect.Descriptor instead.
func (*ReqCheckUgcCustomRouteInUsed) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{5}
}

func (x *ReqCheckUgcCustomRouteInUsed) GetUgcId() uint64 {
	if x != nil {
		return x.UgcId
	}
	return 0
}

type ResCheckUgcCustomRouteInUsed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomRoute *CustomRoute `protobuf:"bytes,1,opt,name=custom_route,json=customRoute,proto3" json:"custom_route,omitempty"`
}

func (x *ResCheckUgcCustomRouteInUsed) Reset() {
	*x = ResCheckUgcCustomRouteInUsed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResCheckUgcCustomRouteInUsed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResCheckUgcCustomRouteInUsed) ProtoMessage() {}

func (x *ResCheckUgcCustomRouteInUsed) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResCheckUgcCustomRouteInUsed.ProtoReflect.Descriptor instead.
func (*ResCheckUgcCustomRouteInUsed) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{6}
}

func (x *ResCheckUgcCustomRouteInUsed) GetCustomRoute() *CustomRoute {
	if x != nil {
		return x.CustomRoute
	}
	return nil
}

type ReqDescribeUgcIdCustomRouteRepeated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Route         string   `protobuf:"bytes,1,opt,name=route,proto3" json:"route,omitempty"`
	ExcludeUgcIds []uint64 `protobuf:"varint,2,rep,packed,name=exclude_ugc_ids,json=excludeUgcIds,proto3" json:"exclude_ugc_ids,omitempty"`
}

func (x *ReqDescribeUgcIdCustomRouteRepeated) Reset() {
	*x = ReqDescribeUgcIdCustomRouteRepeated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeUgcIdCustomRouteRepeated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeUgcIdCustomRouteRepeated) ProtoMessage() {}

func (x *ReqDescribeUgcIdCustomRouteRepeated) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeUgcIdCustomRouteRepeated.ProtoReflect.Descriptor instead.
func (*ReqDescribeUgcIdCustomRouteRepeated) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{7}
}

func (x *ReqDescribeUgcIdCustomRouteRepeated) GetRoute() string {
	if x != nil {
		return x.Route
	}
	return ""
}

func (x *ReqDescribeUgcIdCustomRouteRepeated) GetExcludeUgcIds() []uint64 {
	if x != nil {
		return x.ExcludeUgcIds
	}
	return nil
}

type ResDescribeUgcIdCustomRouteRepeated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExistCustomRoute *CustomRoute `protobuf:"bytes,1,opt,name=exist_custom_route,json=existCustomRoute,proto3" json:"exist_custom_route,omitempty"`
}

func (x *ResDescribeUgcIdCustomRouteRepeated) Reset() {
	*x = ResDescribeUgcIdCustomRouteRepeated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResDescribeUgcIdCustomRouteRepeated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResDescribeUgcIdCustomRouteRepeated) ProtoMessage() {}

func (x *ResDescribeUgcIdCustomRouteRepeated) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResDescribeUgcIdCustomRouteRepeated.ProtoReflect.Descriptor instead.
func (*ResDescribeUgcIdCustomRouteRepeated) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{8}
}

func (x *ResDescribeUgcIdCustomRouteRepeated) GetExistCustomRoute() *CustomRoute {
	if x != nil {
		return x.ExistCustomRoute
	}
	return nil
}

type ReqDescribeCustomRouteUgcByRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Route string `protobuf:"bytes,1,opt,name=route,proto3" json:"route,omitempty"`
}

func (x *ReqDescribeCustomRouteUgcByRoute) Reset() {
	*x = ReqDescribeCustomRouteUgcByRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeCustomRouteUgcByRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeCustomRouteUgcByRoute) ProtoMessage() {}

func (x *ReqDescribeCustomRouteUgcByRoute) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeCustomRouteUgcByRoute.ProtoReflect.Descriptor instead.
func (*ReqDescribeCustomRouteUgcByRoute) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{9}
}

func (x *ReqDescribeCustomRouteUgcByRoute) GetRoute() string {
	if x != nil {
		return x.Route
	}
	return ""
}

type ResDescribeCustomRouteUgcByRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomRoutes []*CustomRoute `protobuf:"bytes,1,rep,name=custom_routes,json=customRoutes,proto3" json:"custom_routes,omitempty"`
}

func (x *ResDescribeCustomRouteUgcByRoute) Reset() {
	*x = ResDescribeCustomRouteUgcByRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResDescribeCustomRouteUgcByRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResDescribeCustomRouteUgcByRoute) ProtoMessage() {}

func (x *ResDescribeCustomRouteUgcByRoute) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResDescribeCustomRouteUgcByRoute.ProtoReflect.Descriptor instead.
func (*ResDescribeCustomRouteUgcByRoute) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{10}
}

func (x *ResDescribeCustomRouteUgcByRoute) GetCustomRoutes() []*CustomRoute {
	if x != nil {
		return x.CustomRoutes
	}
	return nil
}

type ReqDeleteCustomRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UgcIds []uint64 `protobuf:"varint,1,rep,packed,name=ugc_ids,json=ugcIds,proto3" json:"ugc_ids,omitempty"`
}

func (x *ReqDeleteCustomRoute) Reset() {
	*x = ReqDeleteCustomRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteCustomRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteCustomRoute) ProtoMessage() {}

func (x *ReqDeleteCustomRoute) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteCustomRoute.ProtoReflect.Descriptor instead.
func (*ReqDeleteCustomRoute) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{11}
}

func (x *ReqDeleteCustomRoute) GetUgcIds() []uint64 {
	if x != nil {
		return x.UgcIds
	}
	return nil
}

type ReqGetUserTeams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId []uint64 `protobuf:"varint,1,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqGetUserTeams) Reset() {
	*x = ReqGetUserTeams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetUserTeams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetUserTeams) ProtoMessage() {}

func (x *ReqGetUserTeams) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetUserTeams.ProtoReflect.Descriptor instead.
func (*ReqGetUserTeams) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{12}
}

func (x *ReqGetUserTeams) GetUserId() []uint64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

type RspGetUserTeams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队列表<用户ID, 团队信息>
	Teams map[uint64]*FullTeam `protobuf:"bytes,1,rep,name=teams,proto3" json:"teams,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RspGetUserTeams) Reset() {
	*x = RspGetUserTeams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetUserTeams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetUserTeams) ProtoMessage() {}

func (x *RspGetUserTeams) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetUserTeams.ProtoReflect.Descriptor instead.
func (*RspGetUserTeams) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{13}
}

func (x *RspGetUserTeams) GetTeams() map[uint64]*FullTeam {
	if x != nil {
		return x.Teams
	}
	return nil
}

type ReqGetTeams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页
	Page *base.Paginator `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	// 过滤器
	Filter *ReqGetTeams_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// 是否返回total_count
	WithTotalCount bool `protobuf:"varint,3,opt,name=with_total_count,json=withTotalCount,proto3" json:"with_total_count,omitempty"`
}

func (x *ReqGetTeams) Reset() {
	*x = ReqGetTeams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTeams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTeams) ProtoMessage() {}

func (x *ReqGetTeams) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTeams.ProtoReflect.Descriptor instead.
func (*ReqGetTeams) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{14}
}

func (x *ReqGetTeams) GetPage() *base.Paginator {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ReqGetTeams) GetFilter() *ReqGetTeams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqGetTeams) GetWithTotalCount() bool {
	if x != nil {
		return x.WithTotalCount
	}
	return false
}

type RspGetTeams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队列表
	Teams []*FullTeam `protobuf:"bytes,1,rep,name=teams,proto3" json:"teams,omitempty"`
	// 总数
	TotalCount uint32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspGetTeams) Reset() {
	*x = RspGetTeams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTeams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTeams) ProtoMessage() {}

func (x *RspGetTeams) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTeams.ProtoReflect.Descriptor instead.
func (*RspGetTeams) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{15}
}

func (x *RspGetTeams) GetTeams() []*FullTeam {
	if x != nil {
		return x.Teams
	}
	return nil
}

func (x *RspGetTeams) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqGetTeamIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索团队简称和全称，or 关联查询
	TeamName string `protobuf:"bytes,1,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
}

func (x *ReqGetTeamIds) Reset() {
	*x = ReqGetTeamIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTeamIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTeamIds) ProtoMessage() {}

func (x *ReqGetTeamIds) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTeamIds.ProtoReflect.Descriptor instead.
func (*ReqGetTeamIds) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{16}
}

func (x *ReqGetTeamIds) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

type RspGetTeamIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []uint64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *RspGetTeamIds) Reset() {
	*x = RspGetTeamIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTeamIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTeamIds) ProtoMessage() {}

func (x *RspGetTeamIds) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTeamIds.ProtoReflect.Descriptor instead.
func (*RspGetTeamIds) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{17}
}

func (x *RspGetTeamIds) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ReqGetTeamStaff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamIds []uint64 `protobuf:"varint,1,rep,packed,name=team_ids,json=teamIds,proto3" json:"team_ids,omitempty"`
}

func (x *ReqGetTeamStaff) Reset() {
	*x = ReqGetTeamStaff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTeamStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTeamStaff) ProtoMessage() {}

func (x *ReqGetTeamStaff) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTeamStaff.ProtoReflect.Descriptor instead.
func (*ReqGetTeamStaff) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{18}
}

func (x *ReqGetTeamStaff) GetTeamIds() []uint64 {
	if x != nil {
		return x.TeamIds
	}
	return nil
}

type RspGetTeamStaff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StaffMap map[uint64]*RspGetTeamStaff_Staff `protobuf:"bytes,1,rep,name=staff_map,json=staffMap,proto3" json:"staff_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RspGetTeamStaff) Reset() {
	*x = RspGetTeamStaff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTeamStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTeamStaff) ProtoMessage() {}

func (x *RspGetTeamStaff) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTeamStaff.ProtoReflect.Descriptor instead.
func (*RspGetTeamStaff) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{19}
}

func (x *RspGetTeamStaff) GetStaffMap() map[uint64]*RspGetTeamStaff_Staff {
	if x != nil {
		return x.StaffMap
	}
	return nil
}

// 过滤器
type ReqGetTeams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队ID
	TeamId []uint64 `protobuf:"varint,1,rep,packed,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// 搜索关键词
	SearchKeyword string `protobuf:"bytes,2,opt,name=search_keyword,json=searchKeyword,proto3" json:"search_keyword,omitempty"`
	// 搜索场景
	SearchScene ReqGetTeams_SearchScene `protobuf:"varint,3,opt,name=search_scene,json=searchScene,proto3,enum=tanlive.team.ReqGetTeams_SearchScene" json:"search_scene,omitempty"`
	// 是否包含已注销的团队
	WithDisbanded bool `protobuf:"varint,4,opt,name=with_disbanded,json=withDisbanded,proto3" json:"with_disbanded,omitempty"`
}

func (x *ReqGetTeams_Filter) Reset() {
	*x = ReqGetTeams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTeams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTeams_Filter) ProtoMessage() {}

func (x *ReqGetTeams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTeams_Filter.ProtoReflect.Descriptor instead.
func (*ReqGetTeams_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ReqGetTeams_Filter) GetTeamId() []uint64 {
	if x != nil {
		return x.TeamId
	}
	return nil
}

func (x *ReqGetTeams_Filter) GetSearchKeyword() string {
	if x != nil {
		return x.SearchKeyword
	}
	return ""
}

func (x *ReqGetTeams_Filter) GetSearchScene() ReqGetTeams_SearchScene {
	if x != nil {
		return x.SearchScene
	}
	return ReqGetTeams_SEARCH_SCENE_UNSPECIFIED
}

func (x *ReqGetTeams_Filter) GetWithDisbanded() bool {
	if x != nil {
		return x.WithDisbanded
	}
	return false
}

type RspGetTeamStaff_Staff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []uint64 `protobuf:"varint,1,rep,packed,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
}

func (x *RspGetTeamStaff_Staff) Reset() {
	*x = RspGetTeamStaff_Staff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_team_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTeamStaff_Staff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTeamStaff_Staff) ProtoMessage() {}

func (x *RspGetTeamStaff_Staff) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_team_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTeamStaff_Staff.ProtoReflect.Descriptor instead.
func (*RspGetTeamStaff_Staff) Descriptor() ([]byte, []int) {
	return file_tanlive_team_service_proto_rawDescGZIP(), []int{19, 1}
}

func (x *RspGetTeamStaff_Staff) GetUserIds() []uint64 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

var File_tanlive_team_service_proto protoreflect.FileDescriptor

var file_tanlive_team_service_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d,
	0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x74,
	0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x65, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x67, 0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x4a, 0x0a,
	0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65,
	0x61, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x22, 0x49, 0x0a, 0x18, 0x52, 0x65, 0x71,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x22, 0x65, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x67, 0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12,
	0x4a, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x65, 0x61, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x22, 0x36, 0x0a, 0x1d, 0x52,
	0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x42, 0x79, 0x55, 0x67, 0x63, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x75, 0x67,
	0x63, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x1d, 0x52, 0x65, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x42, 0x79, 0x55,
	0x67, 0x63, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x22, 0x35, 0x0a, 0x1c, 0x52, 0x65, 0x71, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x67,
	0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x55, 0x73,
	0x65, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x22, 0x5c, 0x0a, 0x1c, 0x52, 0x65, 0x73,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x67, 0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x49, 0x6e, 0x55, 0x73, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x0c, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x22, 0x63, 0x0a, 0x23, 0x52, 0x65, 0x71, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x67, 0x63, 0x49, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f,
	0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0d, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x67, 0x63, 0x49, 0x64, 0x73, 0x22, 0x6e, 0x0a, 0x23,
	0x52, 0x65, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x67, 0x63, 0x49, 0x64,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x12, 0x47, 0x0a, 0x12, 0x65, 0x78, 0x69, 0x73, 0x74, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x10, 0x65, 0x78, 0x69, 0x73,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x22, 0x38, 0x0a, 0x20,
	0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x55, 0x67, 0x63, 0x42, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x22, 0x62, 0x0a, 0x20, 0x52, 0x65, 0x73, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x55, 0x67, 0x63, 0x42, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x0c, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x22, 0x2f, 0x0a, 0x14, 0x52, 0x65,
	0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x06, 0x75, 0x67, 0x63, 0x49, 0x64, 0x73, 0x22, 0x46, 0x0a, 0x0f, 0x52,
	0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x33,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42,
	0x1a, 0x82, 0x88, 0x27, 0x16, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x64, 0x69,
	0x76, 0x65, 0x2c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0xa3, 0x01, 0x0a, 0x0f, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x3e, 0x0a, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x54, 0x65, 0x61, 0x6d, 0x73, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x1a, 0x50, 0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xaa, 0x03, 0x0a, 0x0b, 0x52, 0x65,
	0x71, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d,
	0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x28, 0x0a, 0x10, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x77, 0x69, 0x74, 0x68,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xb9, 0x01, 0x0a, 0x06, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x48, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65,
	0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x63, 0x65,
	0x6e, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x64, 0x69, 0x73, 0x62, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x77, 0x69, 0x74, 0x68, 0x44, 0x69, 0x73,
	0x62, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x22, 0x4e, 0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f,
	0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x53, 0x43,
	0x45, 0x4e, 0x45, 0x5f, 0x41, 0x49, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x54,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x22, 0x5c, 0x0a, 0x0b, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74,
	0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74,
	0x65, 0x61, 0x6d, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x05, 0x74, 0x65,
	0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2c, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x21, 0x0a, 0x0d, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x3a, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54,
	0x65, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x27, 0x0a, 0x08, 0x74, 0x65, 0x61, 0x6d,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64,
	0x73, 0x22, 0xe1, 0x01, 0x0a, 0x0f, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x48, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x65,
	0x61, 0x6d, 0x53, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x1a,
	0x60, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d,
	0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x22, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x32, 0xe2, 0x05, 0x0a, 0x0b, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x50, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x67, 0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x42, 0x79, 0x55,
	0x67, 0x63, 0x49, 0x64, 0x12, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74,
	0x65, 0x61, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x88, 0x01, 0x0a, 0x20, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x55, 0x67, 0x63, 0x49, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x31, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x67, 0x63, 0x49, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x1a,
	0x31, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52,
	0x65, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x55, 0x67, 0x63, 0x49, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x12, 0x7f, 0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x55, 0x67, 0x63, 0x42, 0x79, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x12, 0x2e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65,
	0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x55, 0x67, 0x63, 0x42, 0x79, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x1a, 0x2e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65,
	0x61, 0x6d, 0x2e, 0x52, 0x65, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x55, 0x67, 0x63, 0x42, 0x79, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54,
	0x65, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74,
	0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65,
	0x61, 0x6d, 0x73, 0x1a, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65,
	0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61,
	0x6d, 0x73, 0x12, 0x40, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x19,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65,
	0x71, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x1a, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54,
	0x65, 0x61, 0x6d, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49,
	0x64, 0x73, 0x12, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61,
	0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x1a,
	0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52,
	0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x4c, 0x0a, 0x0c,
	0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1d, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x66, 0x66, 0x1a, 0x1d, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65,
	0x74, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65,
	0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_tanlive_team_service_proto_rawDescOnce sync.Once
	file_tanlive_team_service_proto_rawDescData = file_tanlive_team_service_proto_rawDesc
)

func file_tanlive_team_service_proto_rawDescGZIP() []byte {
	file_tanlive_team_service_proto_rawDescOnce.Do(func() {
		file_tanlive_team_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_team_service_proto_rawDescData)
	})
	return file_tanlive_team_service_proto_rawDescData
}

var file_tanlive_team_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_team_service_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_tanlive_team_service_proto_goTypes = []interface{}{
	(ReqGetTeams_SearchScene)(0),                // 0: tanlive.team.ReqGetTeams.SearchScene
	(*ReqUpdateUgcCustomRoute)(nil),             // 1: tanlive.team.ReqUpdateUgcCustomRoute
	(*ReqUpdateTeamCustomRoute)(nil),            // 2: tanlive.team.ReqUpdateTeamCustomRoute
	(*ReqCreateUgcCustomRoute)(nil),             // 3: tanlive.team.ReqCreateUgcCustomRoute
	(*ReqDescribeCustomRouteByUgcId)(nil),       // 4: tanlive.team.ReqDescribeCustomRouteByUgcId
	(*ResDescribeCustomRouteByUgcId)(nil),       // 5: tanlive.team.ResDescribeCustomRouteByUgcId
	(*ReqCheckUgcCustomRouteInUsed)(nil),        // 6: tanlive.team.ReqCheckUgcCustomRouteInUsed
	(*ResCheckUgcCustomRouteInUsed)(nil),        // 7: tanlive.team.ResCheckUgcCustomRouteInUsed
	(*ReqDescribeUgcIdCustomRouteRepeated)(nil), // 8: tanlive.team.ReqDescribeUgcIdCustomRouteRepeated
	(*ResDescribeUgcIdCustomRouteRepeated)(nil), // 9: tanlive.team.ResDescribeUgcIdCustomRouteRepeated
	(*ReqDescribeCustomRouteUgcByRoute)(nil),    // 10: tanlive.team.ReqDescribeCustomRouteUgcByRoute
	(*ResDescribeCustomRouteUgcByRoute)(nil),    // 11: tanlive.team.ResDescribeCustomRouteUgcByRoute
	(*ReqDeleteCustomRoute)(nil),                // 12: tanlive.team.ReqDeleteCustomRoute
	(*ReqGetUserTeams)(nil),                     // 13: tanlive.team.ReqGetUserTeams
	(*RspGetUserTeams)(nil),                     // 14: tanlive.team.RspGetUserTeams
	(*ReqGetTeams)(nil),                         // 15: tanlive.team.ReqGetTeams
	(*RspGetTeams)(nil),                         // 16: tanlive.team.RspGetTeams
	(*ReqGetTeamIds)(nil),                       // 17: tanlive.team.ReqGetTeamIds
	(*RspGetTeamIds)(nil),                       // 18: tanlive.team.RspGetTeamIds
	(*ReqGetTeamStaff)(nil),                     // 19: tanlive.team.ReqGetTeamStaff
	(*RspGetTeamStaff)(nil),                     // 20: tanlive.team.RspGetTeamStaff
	nil,                                         // 21: tanlive.team.RspGetUserTeams.TeamsEntry
	(*ReqGetTeams_Filter)(nil),                  // 22: tanlive.team.ReqGetTeams.Filter
	nil,                                         // 23: tanlive.team.RspGetTeamStaff.StaffMapEntry
	(*RspGetTeamStaff_Staff)(nil),               // 24: tanlive.team.RspGetTeamStaff.Staff
	(*CustomRoute)(nil),                         // 25: tanlive.team.CustomRoute
	(*base.Paginator)(nil),                      // 26: tanlive.base.Paginator
	(*FullTeam)(nil),                            // 27: tanlive.team.FullTeam
	(*emptypb.Empty)(nil),                       // 28: google.protobuf.Empty
}
var file_tanlive_team_service_proto_depIdxs = []int32{
	25, // 0: tanlive.team.ReqUpdateUgcCustomRoute.custom_route:type_name -> tanlive.team.CustomRoute
	25, // 1: tanlive.team.ReqCreateUgcCustomRoute.custom_route:type_name -> tanlive.team.CustomRoute
	25, // 2: tanlive.team.ResDescribeCustomRouteByUgcId.custom_route:type_name -> tanlive.team.CustomRoute
	25, // 3: tanlive.team.ResCheckUgcCustomRouteInUsed.custom_route:type_name -> tanlive.team.CustomRoute
	25, // 4: tanlive.team.ResDescribeUgcIdCustomRouteRepeated.exist_custom_route:type_name -> tanlive.team.CustomRoute
	25, // 5: tanlive.team.ResDescribeCustomRouteUgcByRoute.custom_routes:type_name -> tanlive.team.CustomRoute
	21, // 6: tanlive.team.RspGetUserTeams.teams:type_name -> tanlive.team.RspGetUserTeams.TeamsEntry
	26, // 7: tanlive.team.ReqGetTeams.page:type_name -> tanlive.base.Paginator
	22, // 8: tanlive.team.ReqGetTeams.filter:type_name -> tanlive.team.ReqGetTeams.Filter
	27, // 9: tanlive.team.RspGetTeams.teams:type_name -> tanlive.team.FullTeam
	23, // 10: tanlive.team.RspGetTeamStaff.staff_map:type_name -> tanlive.team.RspGetTeamStaff.StaffMapEntry
	27, // 11: tanlive.team.RspGetUserTeams.TeamsEntry.value:type_name -> tanlive.team.FullTeam
	0,  // 12: tanlive.team.ReqGetTeams.Filter.search_scene:type_name -> tanlive.team.ReqGetTeams.SearchScene
	24, // 13: tanlive.team.RspGetTeamStaff.StaffMapEntry.value:type_name -> tanlive.team.RspGetTeamStaff.Staff
	25, // 14: tanlive.team.TeamService.UpdateUgcCustomRouteByUgcId:input_type -> tanlive.team.CustomRoute
	8,  // 15: tanlive.team.TeamService.DescribeUgcIdCustomRouteRepeated:input_type -> tanlive.team.ReqDescribeUgcIdCustomRouteRepeated
	10, // 16: tanlive.team.TeamService.DescribeCustomRouteUgcByRoute:input_type -> tanlive.team.ReqDescribeCustomRouteUgcByRoute
	12, // 17: tanlive.team.TeamService.DeleteCustomRoute:input_type -> tanlive.team.ReqDeleteCustomRoute
	13, // 18: tanlive.team.TeamService.GetUserTeams:input_type -> tanlive.team.ReqGetUserTeams
	15, // 19: tanlive.team.TeamService.GetTeams:input_type -> tanlive.team.ReqGetTeams
	17, // 20: tanlive.team.TeamService.GetTeamIds:input_type -> tanlive.team.ReqGetTeamIds
	19, // 21: tanlive.team.TeamService.GetTeamStaff:input_type -> tanlive.team.ReqGetTeamStaff
	28, // 22: tanlive.team.TeamService.UpdateUgcCustomRouteByUgcId:output_type -> google.protobuf.Empty
	9,  // 23: tanlive.team.TeamService.DescribeUgcIdCustomRouteRepeated:output_type -> tanlive.team.ResDescribeUgcIdCustomRouteRepeated
	11, // 24: tanlive.team.TeamService.DescribeCustomRouteUgcByRoute:output_type -> tanlive.team.ResDescribeCustomRouteUgcByRoute
	28, // 25: tanlive.team.TeamService.DeleteCustomRoute:output_type -> google.protobuf.Empty
	14, // 26: tanlive.team.TeamService.GetUserTeams:output_type -> tanlive.team.RspGetUserTeams
	16, // 27: tanlive.team.TeamService.GetTeams:output_type -> tanlive.team.RspGetTeams
	18, // 28: tanlive.team.TeamService.GetTeamIds:output_type -> tanlive.team.RspGetTeamIds
	20, // 29: tanlive.team.TeamService.GetTeamStaff:output_type -> tanlive.team.RspGetTeamStaff
	22, // [22:30] is the sub-list for method output_type
	14, // [14:22] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_tanlive_team_service_proto_init() }
func file_tanlive_team_service_proto_init() {
	if File_tanlive_team_service_proto != nil {
		return
	}
	file_tanlive_team_team_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_team_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateUgcCustomRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateTeamCustomRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateUgcCustomRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeCustomRouteByUgcId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResDescribeCustomRouteByUgcId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCheckUgcCustomRouteInUsed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResCheckUgcCustomRouteInUsed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeUgcIdCustomRouteRepeated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResDescribeUgcIdCustomRouteRepeated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeCustomRouteUgcByRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResDescribeCustomRouteUgcByRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteCustomRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetUserTeams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetUserTeams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTeams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTeams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTeamIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTeamIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTeamStaff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTeamStaff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTeams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_team_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTeamStaff_Staff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_team_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_team_service_proto_goTypes,
		DependencyIndexes: file_tanlive_team_service_proto_depIdxs,
		EnumInfos:         file_tanlive_team_service_proto_enumTypes,
		MessageInfos:      file_tanlive_team_service_proto_msgTypes,
	}.Build()
	File_tanlive_team_service_proto = out.File
	file_tanlive_team_service_proto_rawDesc = nil
	file_tanlive_team_service_proto_goTypes = nil
	file_tanlive_team_service_proto_depIdxs = nil
}
