syntax = "proto3";

package tanlive.team;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team";

import "google/protobuf/timestamp.proto";
import "tanlive/base/ugc.proto";
import "tanlive/options.proto";

// 实体类型
enum EntityType {
  ENTITY_TYPE_UNSPECIFIED = 0;
  // 企业
  ENTITY_TYPE_ENTERPRISE = 1;
  // 其它
  ENTITY_TYPE_OTHER = 2;
}

// 团队共创等级
enum TeamLevel {
  TEAM_LEVEL_UNSPECIFIED = 0;
  // CONTRIBUTOR
  TEAM_LEVEL_CONTRIBUTOR = 1;
  // COMMITTER
  TEAM_LEVEL_COMMITTER = 2;
  // MAINTAINER
  TEAM_LEVEL_MAINTAINER = 3;
}

// 团队规模
enum TeamScale {
  TEAM_SCALE_UNSPECIFIED = 0;
  // 1-10人
  TEAM_SCALE_BETWEEN_1_10 = 1;
  // 10-50人
  TEAM_SCALE_BETWEEN_10_50 = 2;
  // 50-500人
  TEAM_SCALE_BETWEEN_50_500 = 3;
  // 500人以上
  TEAM_SCALE_MORE_THAN_500 = 4;
}

// 员工状态
enum StaffState {
  STAFF_STATE_UNSPECIFIED = 0;
  // 正常
  STAFF_STATE_VALID = 1;
  // 已删除
  STAFF_STATE_DELETED = 2;
}

// 完整团队信息
message FullTeam {
  // 团队信息
  TeamInfo team_info = 1;
}

// 团队信息
message TeamInfo {
  // 团队ID
  uint64 id = 1;
  // 创建人
  uint64 create_by = 2;
  // 创建时间
  google.protobuf.Timestamp create_date = 3;
  // 最后编辑者
  uint64 update_by = 4;
  // 最后编辑时间
  google.protobuf.Timestamp update_date = 5;
  // 主数据ID
  uint64 main_id = 6;
  // 团队持有者ID
  uint64 holder_id = 7;
  // 团队主体名称
  string full_name = 8;
  // 团队简称
  string short_name = 9;
  // 共创等级
  TeamLevel level = 10;
  // UGC状态
  base.UgcState ugc_state = 11;
  // 是否为虚拟团队
  bool is_virtual = 12;
  // 是否为草稿
  bool is_draft = 13;
  // 是否已认证
  bool is_verified = 14;
  // 是否已发布
  bool is_published = 15;
  // 是否已注销
  bool is_deregistered = 16;
  // 源语言
  string source_lang = 17;
  // 检测语言
  string detected_lang = 18;
  // 主体国家
  string principal_country = 19;
  // 团队LOGO
  string logo_url = 20;
  // 一句话介绍
  string brief_intro = 21;
}

// 团多投资者
message TeamInvestor {
  // 投资者名称
  string name = 3;
  // 投资者团队ID
  uint64 investor_team_id = 4;
}

// 团队成员
message TeamMember {
  // ID
  uint64 id = 1;
  // 姓名
  string name = 2;
  // 头像
  string avatar_url = 3;
  // 职位
  string position = 4;
  // 介绍
  string introduction = 5;
  // 可见规则
  base.VisibleRule visible_rule = 6;
}

// 角色类型
enum RoleType {
  ROLE_TYPE_UNSPECIFIED = 0;
  // 用户创建的
  ROLE_TYPE_USER_CREATED = 1;
  // 管理员
  ROLE_TYPE_ADMIN = 2;
  // 未分类
  ROLE_TYPE_UNCLASSIFIED = 3;
}

// 团队角色
message TeamRole {
  // ID
  uint64 id = 1;
  // 角色名
  string name = 2;
  // 角色类型
  RoleType type = 3;
  // 创建者
  uint64 create_by = 4;
  // 创建
  google.protobuf.Timestamp create_date = 5;
  // 更新者
  uint64 update_by = 6;
  // 更新时间
  google.protobuf.Timestamp update_date = 7;
}

message CustomRoute {
  uint64 id = 1;
  string route = 2;
  // UGC ID
  uint64 ugc_id = 3;
  // UGC类型
  base.DataType ugc_type = 4;
  // 更新者
  uint64 last_update_by = 5;
  // 更新时间
  google.protobuf.Timestamp last_update_date = 8;
}

// 团队卡片
message TeamCard {
  // 团队ID
  uint64 id = 1;
  // 简称
  string short_name = 2;
  // 主体名称
  string full_name = 3;
  // 是否认证
  bool is_verified = 4;
  // 一句话介绍
  string brief_intro = 5;
  // 共创等级
  TeamLevel level = 6;
  // 团队LOGO
  string logo_url = 7;
  // 是否已发布
  bool is_published = 8;
}
