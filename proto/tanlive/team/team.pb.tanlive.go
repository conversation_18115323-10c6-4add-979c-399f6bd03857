// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package team

import (
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func (x *FullTeam) MaskInLog() any {
	if x == nil {
		return (*FullTeam)(nil)
	}

	y := proto.Clone(x).(*FullTeam)
	if v, ok := any(y.TeamInfo).(interface{ MaskInLog() any }); ok {
		y.TeamInfo = v.MaskInLog().(*TeamInfo)
	}

	return y
}

func (x *FullTeam) MaskInRpc() any {
	if x == nil {
		return (*FullTeam)(nil)
	}

	y := x
	if v, ok := any(y.TeamInfo).(interface{ MaskInRpc() any }); ok {
		y.TeamInfo = v.MaskInRpc().(*TeamInfo)
	}

	return y
}

func (x *FullTeam) MaskInBff() any {
	if x == nil {
		return (*FullTeam)(nil)
	}

	y := x
	if v, ok := any(y.TeamInfo).(interface{ MaskInBff() any }); ok {
		y.TeamInfo = v.MaskInBff().(*TeamInfo)
	}

	return y
}

func (x *TeamInfo) MaskInLog() any {
	if x == nil {
		return (*TeamInfo)(nil)
	}

	y := proto.Clone(x).(*TeamInfo)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TeamInfo) MaskInRpc() any {
	if x == nil {
		return (*TeamInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TeamInfo) MaskInBff() any {
	if x == nil {
		return (*TeamInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TeamMember) MaskInLog() any {
	if x == nil {
		return (*TeamMember)(nil)
	}

	y := proto.Clone(x).(*TeamMember)
	if v, ok := any(y.VisibleRule).(interface{ MaskInLog() any }); ok {
		y.VisibleRule = v.MaskInLog().(*base.VisibleRule)
	}

	return y
}

func (x *TeamMember) MaskInRpc() any {
	if x == nil {
		return (*TeamMember)(nil)
	}

	y := x
	if v, ok := any(y.VisibleRule).(interface{ MaskInRpc() any }); ok {
		y.VisibleRule = v.MaskInRpc().(*base.VisibleRule)
	}

	return y
}

func (x *TeamMember) MaskInBff() any {
	if x == nil {
		return (*TeamMember)(nil)
	}

	y := x
	if v, ok := any(y.VisibleRule).(interface{ MaskInBff() any }); ok {
		y.VisibleRule = v.MaskInBff().(*base.VisibleRule)
	}

	return y
}

func (x *TeamRole) MaskInLog() any {
	if x == nil {
		return (*TeamRole)(nil)
	}

	y := proto.Clone(x).(*TeamRole)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TeamRole) MaskInRpc() any {
	if x == nil {
		return (*TeamRole)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TeamRole) MaskInBff() any {
	if x == nil {
		return (*TeamRole)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *CustomRoute) MaskInLog() any {
	if x == nil {
		return (*CustomRoute)(nil)
	}

	y := proto.Clone(x).(*CustomRoute)
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *CustomRoute) MaskInRpc() any {
	if x == nil {
		return (*CustomRoute)(nil)
	}

	y := x
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *CustomRoute) MaskInBff() any {
	if x == nil {
		return (*CustomRoute)(nil)
	}

	y := x
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *FullTeam) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TeamInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TeamInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TeamMember) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.VisibleRule).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TeamRole) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *CustomRoute) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
