// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/cms/cms.proto

package cms

import (
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 文档可见性
type DocVisibility int32

const (
	// 全部可见
	DocVisibility_DOC_VISIBILITY_ALL DocVisibility = 0
	// 仅登录用户可见
	DocVisibility_DOC_VISIBILITY_USER DocVisibility = 1
	// 仅AI助手可见
	DocVisibility_DOC_VISIBILITY_AI_ASSISTANT DocVisibility = 2
)

// Enum value maps for DocVisibility.
var (
	DocVisibility_name = map[int32]string{
		0: "DOC_VISIBILITY_ALL",
		1: "DOC_VISIBILITY_USER",
		2: "DOC_VISIBILITY_AI_ASSISTANT",
	}
	DocVisibility_value = map[string]int32{
		"DOC_VISIBILITY_ALL":          0,
		"DOC_VISIBILITY_USER":         1,
		"DOC_VISIBILITY_AI_ASSISTANT": 2,
	}
)

func (x DocVisibility) Enum() *DocVisibility {
	p := new(DocVisibility)
	*p = x
	return p
}

func (x DocVisibility) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocVisibility) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_cms_cms_proto_enumTypes[0].Descriptor()
}

func (DocVisibility) Type() protoreflect.EnumType {
	return &file_tanlive_cms_cms_proto_enumTypes[0]
}

func (x DocVisibility) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocVisibility.Descriptor instead.
func (DocVisibility) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_cms_cms_proto_rawDescGZIP(), []int{0}
}

// 文档状态
type DocState int32

const (
	// 编辑中
	DocState_HELP_CENTER_STATE_EDITING DocState = 0
	// 已发布
	DocState_HELP_CENTER_STATE_PUBLISHED DocState = 1
	// 已禁用
	DocState_HELP_CENTER_STATE_DISABLED DocState = 2
)

// Enum value maps for DocState.
var (
	DocState_name = map[int32]string{
		0: "HELP_CENTER_STATE_EDITING",
		1: "HELP_CENTER_STATE_PUBLISHED",
		2: "HELP_CENTER_STATE_DISABLED",
	}
	DocState_value = map[string]int32{
		"HELP_CENTER_STATE_EDITING":   0,
		"HELP_CENTER_STATE_PUBLISHED": 1,
		"HELP_CENTER_STATE_DISABLED":  2,
	}
)

func (x DocState) Enum() *DocState {
	p := new(DocState)
	*p = x
	return p
}

func (x DocState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocState) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_cms_cms_proto_enumTypes[1].Descriptor()
}

func (DocState) Type() protoreflect.EnumType {
	return &file_tanlive_cms_cms_proto_enumTypes[1]
}

func (x DocState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocState.Descriptor instead.
func (DocState) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_cms_cms_proto_rawDescGZIP(), []int{1}
}

// 协议类型
type TermsType int32

const (
	TermsType_TERMS_TYPE_UNSPECIFIED TermsType = 0
	// AI服务协议（国内）
	TermsType_TERMS_TYPE_AI_SERVICE_CN TermsType = 1
	// AI服务协议（海外）
	TermsType_TERMS_TYPE_AI_SERVICE_OVERSEA TermsType = 2
)

// Enum value maps for TermsType.
var (
	TermsType_name = map[int32]string{
		0: "TERMS_TYPE_UNSPECIFIED",
		1: "TERMS_TYPE_AI_SERVICE_CN",
		2: "TERMS_TYPE_AI_SERVICE_OVERSEA",
	}
	TermsType_value = map[string]int32{
		"TERMS_TYPE_UNSPECIFIED":        0,
		"TERMS_TYPE_AI_SERVICE_CN":      1,
		"TERMS_TYPE_AI_SERVICE_OVERSEA": 2,
	}
)

func (x TermsType) Enum() *TermsType {
	p := new(TermsType)
	*p = x
	return p
}

func (x TermsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TermsType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_cms_cms_proto_enumTypes[2].Descriptor()
}

func (TermsType) Type() protoreflect.EnumType {
	return &file_tanlive_cms_cms_proto_enumTypes[2]
}

func (x TermsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TermsType.Descriptor instead.
func (TermsType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_cms_cms_proto_rawDescGZIP(), []int{2}
}

var File_tanlive_cms_cms_proto protoreflect.FileDescriptor

var file_tanlive_cms_cms_proto_rawDesc = []byte{
	0x0a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x63, 0x6d, 0x73, 0x2f, 0x63, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x63, 0x6d, 0x73, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62,
	0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0x61,
	0x0a, 0x0d, 0x44, 0x6f, 0x63, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x16, 0x0a, 0x12, 0x44, 0x4f, 0x43, 0x5f, 0x56, 0x49, 0x53, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x4f, 0x43, 0x5f, 0x56,
	0x49, 0x53, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x01,
	0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x4f, 0x43, 0x5f, 0x56, 0x49, 0x53, 0x49, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x41, 0x49, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x10,
	0x02, 0x2a, 0x6a, 0x0a, 0x08, 0x44, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a,
	0x19, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b,
	0x48, 0x45, 0x4c, 0x50, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1e, 0x0a,
	0x1a, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x68, 0x0a,
	0x09, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x45,
	0x52, 0x4d, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x45, 0x52, 0x4d, 0x53, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x49, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x43, 0x4e, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x45, 0x52, 0x4d, 0x53, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x41, 0x49, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x56,
	0x45, 0x52, 0x53, 0x45, 0x41, 0x10, 0x02, 0x42, 0x3d, 0x5a, 0x3b, 0x65, 0x2e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x63, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_cms_cms_proto_rawDescOnce sync.Once
	file_tanlive_cms_cms_proto_rawDescData = file_tanlive_cms_cms_proto_rawDesc
)

func file_tanlive_cms_cms_proto_rawDescGZIP() []byte {
	file_tanlive_cms_cms_proto_rawDescOnce.Do(func() {
		file_tanlive_cms_cms_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_cms_cms_proto_rawDescData)
	})
	return file_tanlive_cms_cms_proto_rawDescData
}

var file_tanlive_cms_cms_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_tanlive_cms_cms_proto_goTypes = []interface{}{
	(DocVisibility)(0), // 0: tanlive.cms.DocVisibility
	(DocState)(0),      // 1: tanlive.cms.DocState
	(TermsType)(0),     // 2: tanlive.cms.TermsType
}
var file_tanlive_cms_cms_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_cms_cms_proto_init() }
func file_tanlive_cms_cms_proto_init() {
	if File_tanlive_cms_cms_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_cms_cms_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_cms_cms_proto_goTypes,
		DependencyIndexes: file_tanlive_cms_cms_proto_depIdxs,
		EnumInfos:         file_tanlive_cms_cms_proto_enumTypes,
	}.Build()
	File_tanlive_cms_cms_proto = out.File
	file_tanlive_cms_cms_proto_rawDesc = nil
	file_tanlive_cms_cms_proto_goTypes = nil
	file_tanlive_cms_cms_proto_depIdxs = nil
}
