// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/cms/service.proto

package cms

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqConfirmTerms struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 身份
	Identity *base.Identity `protobuf:"bytes,1,opt,name=identity,proto3" json:"identity,omitempty"`
	// 协议类型
	TermsType TermsType `protobuf:"varint,2,opt,name=terms_type,json=termsType,proto3,enum=tanlive.cms.TermsType" json:"terms_type,omitempty"`
	// 是否同意
	IsAgreed bool `protobuf:"varint,3,opt,name=is_agreed,json=isAgreed,proto3" json:"is_agreed,omitempty"`
	// 创建人
	CreateBy uint64 `protobuf:"varint,4,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
}

func (x *ReqConfirmTerms) Reset() {
	*x = ReqConfirmTerms{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_cms_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqConfirmTerms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqConfirmTerms) ProtoMessage() {}

func (x *ReqConfirmTerms) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_cms_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqConfirmTerms.ProtoReflect.Descriptor instead.
func (*ReqConfirmTerms) Descriptor() ([]byte, []int) {
	return file_tanlive_cms_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqConfirmTerms) GetIdentity() *base.Identity {
	if x != nil {
		return x.Identity
	}
	return nil
}

func (x *ReqConfirmTerms) GetTermsType() TermsType {
	if x != nil {
		return x.TermsType
	}
	return TermsType_TERMS_TYPE_UNSPECIFIED
}

func (x *ReqConfirmTerms) GetIsAgreed() bool {
	if x != nil {
		return x.IsAgreed
	}
	return false
}

func (x *ReqConfirmTerms) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

type ReqGetTermsConfirmations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 身份
	Identity *base.Identity `protobuf:"bytes,1,opt,name=identity,proto3" json:"identity,omitempty"`
	// 协议类型
	TermsTypes []TermsType `protobuf:"varint,2,rep,packed,name=terms_types,json=termsTypes,proto3,enum=tanlive.cms.TermsType" json:"terms_types,omitempty"`
}

func (x *ReqGetTermsConfirmations) Reset() {
	*x = ReqGetTermsConfirmations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_cms_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTermsConfirmations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTermsConfirmations) ProtoMessage() {}

func (x *ReqGetTermsConfirmations) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_cms_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTermsConfirmations.ProtoReflect.Descriptor instead.
func (*ReqGetTermsConfirmations) Descriptor() ([]byte, []int) {
	return file_tanlive_cms_service_proto_rawDescGZIP(), []int{1}
}

func (x *ReqGetTermsConfirmations) GetIdentity() *base.Identity {
	if x != nil {
		return x.Identity
	}
	return nil
}

func (x *ReqGetTermsConfirmations) GetTermsTypes() []TermsType {
	if x != nil {
		return x.TermsTypes
	}
	return nil
}

type RspGetTermsConfirmations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 确认信息列表
	Confirmations []*RspGetTermsConfirmations_Confirmation `protobuf:"bytes,1,rep,name=confirmations,proto3" json:"confirmations,omitempty"`
}

func (x *RspGetTermsConfirmations) Reset() {
	*x = RspGetTermsConfirmations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_cms_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTermsConfirmations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTermsConfirmations) ProtoMessage() {}

func (x *RspGetTermsConfirmations) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_cms_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTermsConfirmations.ProtoReflect.Descriptor instead.
func (*RspGetTermsConfirmations) Descriptor() ([]byte, []int) {
	return file_tanlive_cms_service_proto_rawDescGZIP(), []int{2}
}

func (x *RspGetTermsConfirmations) GetConfirmations() []*RspGetTermsConfirmations_Confirmation {
	if x != nil {
		return x.Confirmations
	}
	return nil
}

// 确认信息
type RspGetTermsConfirmations_Confirmation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 协议类型
	TermsType TermsType `protobuf:"varint,1,opt,name=terms_type,json=termsType,proto3,enum=tanlive.cms.TermsType" json:"terms_type,omitempty"`
	// 是否已同意
	IsAgreed bool `protobuf:"varint,2,opt,name=is_agreed,json=isAgreed,proto3" json:"is_agreed,omitempty"`
	// 协议文档路径
	DocPath string `protobuf:"bytes,3,opt,name=doc_path,json=docPath,proto3" json:"doc_path,omitempty"`
}

func (x *RspGetTermsConfirmations_Confirmation) Reset() {
	*x = RspGetTermsConfirmations_Confirmation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_cms_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTermsConfirmations_Confirmation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTermsConfirmations_Confirmation) ProtoMessage() {}

func (x *RspGetTermsConfirmations_Confirmation) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_cms_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTermsConfirmations_Confirmation.ProtoReflect.Descriptor instead.
func (*RspGetTermsConfirmations_Confirmation) Descriptor() ([]byte, []int) {
	return file_tanlive_cms_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *RspGetTermsConfirmations_Confirmation) GetTermsType() TermsType {
	if x != nil {
		return x.TermsType
	}
	return TermsType_TERMS_TYPE_UNSPECIFIED
}

func (x *RspGetTermsConfirmations_Confirmation) GetIsAgreed() bool {
	if x != nil {
		return x.IsAgreed
	}
	return false
}

func (x *RspGetTermsConfirmations_Confirmation) GetDocPath() string {
	if x != nil {
		return x.DocPath
	}
	return ""
}

var File_tanlive_cms_service_proto protoreflect.FileDescriptor

var file_tanlive_cms_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x63, 0x6d, 0x73, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x63, 0x6d, 0x73, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62,
	0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x63, 0x6d, 0x73, 0x2f, 0x63, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0, 0x01, 0x0a,
	0x0f, 0x52, 0x65, 0x71, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x54, 0x65, 0x72, 0x6d, 0x73,
	0x12, 0x40, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x63, 0x6d, 0x73, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x74, 0x65,
	0x72, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x22,
	0xb1, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x40, 0x0a, 0x08,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x53,
	0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x63, 0x6d,
	0x73, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x1a, 0x82, 0x88, 0x27,
	0x16, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x22, 0xf3, 0x01, 0x0a, 0x18, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x65,
	0x72, 0x6d, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x58, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x63, 0x6d, 0x73, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x7d, 0x0a, 0x0c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x0a, 0x74, 0x65,
	0x72, 0x6d, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x63, 0x6d, 0x73, 0x2e, 0x54, 0x65, 0x72,
	0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x67, 0x72, 0x65, 0x65, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x64, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68, 0x32, 0xb9, 0x01, 0x0a, 0x0a, 0x43, 0x6d,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x63, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x65,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x63, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x25,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x63, 0x6d, 0x73, 0x2e, 0x52, 0x73, 0x70,
	0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x3d, 0x5a, 0x3b, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73,
	0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x63, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_cms_service_proto_rawDescOnce sync.Once
	file_tanlive_cms_service_proto_rawDescData = file_tanlive_cms_service_proto_rawDesc
)

func file_tanlive_cms_service_proto_rawDescGZIP() []byte {
	file_tanlive_cms_service_proto_rawDescOnce.Do(func() {
		file_tanlive_cms_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_cms_service_proto_rawDescData)
	})
	return file_tanlive_cms_service_proto_rawDescData
}

var file_tanlive_cms_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_tanlive_cms_service_proto_goTypes = []interface{}{
	(*ReqConfirmTerms)(nil),                       // 0: tanlive.cms.ReqConfirmTerms
	(*ReqGetTermsConfirmations)(nil),              // 1: tanlive.cms.ReqGetTermsConfirmations
	(*RspGetTermsConfirmations)(nil),              // 2: tanlive.cms.RspGetTermsConfirmations
	(*RspGetTermsConfirmations_Confirmation)(nil), // 3: tanlive.cms.RspGetTermsConfirmations.Confirmation
	(*base.Identity)(nil),                         // 4: tanlive.base.Identity
	(TermsType)(0),                                // 5: tanlive.cms.TermsType
	(*emptypb.Empty)(nil),                         // 6: google.protobuf.Empty
}
var file_tanlive_cms_service_proto_depIdxs = []int32{
	4, // 0: tanlive.cms.ReqConfirmTerms.identity:type_name -> tanlive.base.Identity
	5, // 1: tanlive.cms.ReqConfirmTerms.terms_type:type_name -> tanlive.cms.TermsType
	4, // 2: tanlive.cms.ReqGetTermsConfirmations.identity:type_name -> tanlive.base.Identity
	5, // 3: tanlive.cms.ReqGetTermsConfirmations.terms_types:type_name -> tanlive.cms.TermsType
	3, // 4: tanlive.cms.RspGetTermsConfirmations.confirmations:type_name -> tanlive.cms.RspGetTermsConfirmations.Confirmation
	5, // 5: tanlive.cms.RspGetTermsConfirmations.Confirmation.terms_type:type_name -> tanlive.cms.TermsType
	0, // 6: tanlive.cms.CmsService.ConfirmTerms:input_type -> tanlive.cms.ReqConfirmTerms
	1, // 7: tanlive.cms.CmsService.GetTermsConfirmations:input_type -> tanlive.cms.ReqGetTermsConfirmations
	6, // 8: tanlive.cms.CmsService.ConfirmTerms:output_type -> google.protobuf.Empty
	2, // 9: tanlive.cms.CmsService.GetTermsConfirmations:output_type -> tanlive.cms.RspGetTermsConfirmations
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_tanlive_cms_service_proto_init() }
func file_tanlive_cms_service_proto_init() {
	if File_tanlive_cms_service_proto != nil {
		return
	}
	file_tanlive_cms_cms_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_cms_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqConfirmTerms); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_cms_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTermsConfirmations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_cms_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTermsConfirmations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_cms_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTermsConfirmations_Confirmation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_cms_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_cms_service_proto_goTypes,
		DependencyIndexes: file_tanlive_cms_service_proto_depIdxs,
		MessageInfos:      file_tanlive_cms_service_proto_msgTypes,
	}.Build()
	File_tanlive_cms_service_proto = out.File
	file_tanlive_cms_service_proto_rawDesc = nil
	file_tanlive_cms_service_proto_goTypes = nil
	file_tanlive_cms_service_proto_depIdxs = nil
}
