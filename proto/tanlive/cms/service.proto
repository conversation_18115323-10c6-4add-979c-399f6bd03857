syntax = "proto3";

package tanlive.cms;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/cms";

import "google/protobuf/empty.proto";
import "tanlive/base/base.proto";
import "tanlive/cms/cms.proto";
import "tanlive/options.proto";

// cms服务
service CmsService {
  // 确认协议
  rpc ConfirmTerms(ReqConfirmTerms) returns (google.protobuf.Empty);
  // 获取协议确认情况
  rpc GetTermsConfirmations(ReqGetTermsConfirmations) returns (RspGetTermsConfirmations);
}

message ReqConfirmTerms {
  // 身份
  base.Identity identity = 1 [(validator) = "required"];
  // 协议类型
  TermsType terms_type = 2 [(validator) = "required"];
  // 是否同意
  bool is_agreed = 3;
  // 创建人
  uint64 create_by = 4 [(validator) = "required"];
}

message ReqGetTermsConfirmations {
  // 身份
  base.Identity identity = 1 [(validator) = "required"];
  // 协议类型
  repeated TermsType terms_types = 2 [(validator) = "required,dive,required"];
}

message RspGetTermsConfirmations {
  // 确认信息
  message Confirmation {
    // 协议类型
    TermsType terms_type = 1;
    // 是否已同意
    bool is_agreed = 2;
    // 协议文档路径
    string doc_path = 3;
  }
  // 确认信息列表
  repeated Confirmation confirmations = 1;
}
