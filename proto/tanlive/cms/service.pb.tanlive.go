// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package cms

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Identity":  "required",
		"TermsType": "required",
		"CreateBy":  "required",
	}, &ReqConfirmTerms{})
}

func (x *ReqConfirmTerms) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Identity":   "required",
		"TermsTypes": "required,dive,required",
	}, &ReqGetTermsConfirmations{})
}

func (x *ReqGetTermsConfirmations) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqConfirmTerms) MaskInLog() any {
	if x == nil {
		return (*ReqConfirmTerms)(nil)
	}

	y := proto.Clone(x).(*ReqConfirmTerms)
	if v, ok := any(y.Identity).(interface{ MaskInLog() any }); ok {
		y.Identity = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqConfirmTerms) MaskInRpc() any {
	if x == nil {
		return (*ReqConfirmTerms)(nil)
	}

	y := x
	if v, ok := any(y.Identity).(interface{ MaskInRpc() any }); ok {
		y.Identity = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqConfirmTerms) MaskInBff() any {
	if x == nil {
		return (*ReqConfirmTerms)(nil)
	}

	y := x
	if v, ok := any(y.Identity).(interface{ MaskInBff() any }); ok {
		y.Identity = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqGetTermsConfirmations) MaskInLog() any {
	if x == nil {
		return (*ReqGetTermsConfirmations)(nil)
	}

	y := proto.Clone(x).(*ReqGetTermsConfirmations)
	if v, ok := any(y.Identity).(interface{ MaskInLog() any }); ok {
		y.Identity = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqGetTermsConfirmations) MaskInRpc() any {
	if x == nil {
		return (*ReqGetTermsConfirmations)(nil)
	}

	y := x
	if v, ok := any(y.Identity).(interface{ MaskInRpc() any }); ok {
		y.Identity = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqGetTermsConfirmations) MaskInBff() any {
	if x == nil {
		return (*ReqGetTermsConfirmations)(nil)
	}

	y := x
	if v, ok := any(y.Identity).(interface{ MaskInBff() any }); ok {
		y.Identity = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *RspGetTermsConfirmations) MaskInLog() any {
	if x == nil {
		return (*RspGetTermsConfirmations)(nil)
	}

	y := proto.Clone(x).(*RspGetTermsConfirmations)
	for k, v := range y.Confirmations {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Confirmations[k] = vv.MaskInLog().(*RspGetTermsConfirmations_Confirmation)
		}
	}

	return y
}

func (x *RspGetTermsConfirmations) MaskInRpc() any {
	if x == nil {
		return (*RspGetTermsConfirmations)(nil)
	}

	y := x
	for k, v := range y.Confirmations {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Confirmations[k] = vv.MaskInRpc().(*RspGetTermsConfirmations_Confirmation)
		}
	}

	return y
}

func (x *RspGetTermsConfirmations) MaskInBff() any {
	if x == nil {
		return (*RspGetTermsConfirmations)(nil)
	}

	y := x
	for k, v := range y.Confirmations {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Confirmations[k] = vv.MaskInBff().(*RspGetTermsConfirmations_Confirmation)
		}
	}

	return y
}

func (x *ReqConfirmTerms) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Identity).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetTermsConfirmations) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Identity).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetTermsConfirmations) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Confirmations {
		if sanitizer, ok := any(x.Confirmations[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
