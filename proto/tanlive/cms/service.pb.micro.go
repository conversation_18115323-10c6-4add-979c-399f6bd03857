// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/cms/service.proto

package cms

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for CmsService service

func NewCmsServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for CmsService service

type CmsService interface {
	// 确认协议
	ConfirmTerms(ctx context.Context, in *ReqConfirmTerms, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取协议确认情况
	GetTermsConfirmations(ctx context.Context, in *ReqGetTermsConfirmations, opts ...client.CallOption) (*RspGetTermsConfirmations, error)
}

type cmsService struct {
	c    client.Client
	name string
}

func NewCmsService(name string, c client.Client) CmsService {
	return &cmsService{
		c:    c,
		name: name,
	}
}

func (c *cmsService) ConfirmTerms(ctx context.Context, in *ReqConfirmTerms, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "CmsService.ConfirmTerms", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cmsService) GetTermsConfirmations(ctx context.Context, in *ReqGetTermsConfirmations, opts ...client.CallOption) (*RspGetTermsConfirmations, error) {
	req := c.c.NewRequest(c.name, "CmsService.GetTermsConfirmations", in)
	out := new(RspGetTermsConfirmations)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for CmsService service

type CmsServiceHandler interface {
	// 确认协议
	ConfirmTerms(context.Context, *ReqConfirmTerms, *emptypb.Empty) error
	// 获取协议确认情况
	GetTermsConfirmations(context.Context, *ReqGetTermsConfirmations, *RspGetTermsConfirmations) error
}

func RegisterCmsServiceHandler(s server.Server, hdlr CmsServiceHandler, opts ...server.HandlerOption) error {
	type cmsService interface {
		ConfirmTerms(ctx context.Context, in *ReqConfirmTerms, out *emptypb.Empty) error
		GetTermsConfirmations(ctx context.Context, in *ReqGetTermsConfirmations, out *RspGetTermsConfirmations) error
	}
	type CmsService struct {
		cmsService
	}
	h := &cmsServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&CmsService{h}, opts...))
}

type cmsServiceHandler struct {
	CmsServiceHandler
}

func (h *cmsServiceHandler) ConfirmTerms(ctx context.Context, in *ReqConfirmTerms, out *emptypb.Empty) error {
	return h.CmsServiceHandler.ConfirmTerms(ctx, in, out)
}

func (h *cmsServiceHandler) GetTermsConfirmations(ctx context.Context, in *ReqGetTermsConfirmations, out *RspGetTermsConfirmations) error {
	return h.CmsServiceHandler.GetTermsConfirmations(ctx, in, out)
}
