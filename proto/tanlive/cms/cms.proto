syntax = "proto3";

package tanlive.cms;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/cms";

import "google/protobuf/timestamp.proto";
import "tanlive/base/base.proto";

// 文档可见性
enum DocVisibility {
  // 全部可见
  DOC_VISIBILITY_ALL = 0;
  // 仅登录用户可见
  DOC_VISIBILITY_USER = 1;
  // 仅AI助手可见
  DOC_VISIBILITY_AI_ASSISTANT = 2;
}

// 文档状态
enum DocState {
  // 编辑中
  HELP_CENTER_STATE_EDITING = 0;
  // 已发布
  HELP_CENTER_STATE_PUBLISHED = 1;
  // 已禁用
  HELP_CENTER_STATE_DISABLED = 2;
}

// 协议类型
enum TermsType {
  TERMS_TYPE_UNSPECIFIED = 0;
  // AI服务协议（国内）
  TERMS_TYPE_AI_SERVICE_CN = 1;
  // AI服务协议（海外）
  TERMS_TYPE_AI_SERVICE_OVERSEA = 2;
}
