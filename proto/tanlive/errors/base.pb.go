// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/errors/base.proto

package errors

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 通用基础错误
// 范围：[1000, 2000)
// 优先使用HTTP状态码
type BaseError int32

const (
	BaseError_BaseNoError BaseError = 0
	// 非法请求
	BaseError_BadRequest BaseError = 400
	// 请先登录
	BaseError_Unauthorized BaseError = 401
	// 没有权限
	BaseError_Forbidden BaseError = 403
	// 接口不存在
	BaseError_NotFound BaseError = 404
	// 请求方法不允许
	BaseError_MethodNotAllowed BaseError = 405
	// rpc请求超时
	BaseError_RequestTimeout BaseError = 408
	// 参数错误
	BaseError_UnprocessableEntity BaseError = 422
	// 请求过于频繁
	BaseError_TooManyRequests BaseError = 429
	// 服务暂时不可用
	BaseError_ServiceUnavailable BaseError = 503
	// 腾讯云验证码错误
	BaseError_TcloudCaptchaError BaseError = 1000
	// TQL语法错误
	BaseError_TqlGrammarError BaseError = 1001
)

// Enum value maps for BaseError.
var (
	BaseError_name = map[int32]string{
		0:    "BaseNoError",
		400:  "BadRequest",
		401:  "Unauthorized",
		403:  "Forbidden",
		404:  "NotFound",
		405:  "MethodNotAllowed",
		408:  "RequestTimeout",
		422:  "UnprocessableEntity",
		429:  "TooManyRequests",
		503:  "ServiceUnavailable",
		1000: "TcloudCaptchaError",
		1001: "TqlGrammarError",
	}
	BaseError_value = map[string]int32{
		"BaseNoError":         0,
		"BadRequest":          400,
		"Unauthorized":        401,
		"Forbidden":           403,
		"NotFound":            404,
		"MethodNotAllowed":    405,
		"RequestTimeout":      408,
		"UnprocessableEntity": 422,
		"TooManyRequests":     429,
		"ServiceUnavailable":  503,
		"TcloudCaptchaError":  1000,
		"TqlGrammarError":     1001,
	}
)

func (x BaseError) Enum() *BaseError {
	p := new(BaseError)
	*p = x
	return p
}

func (x BaseError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BaseError) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_errors_base_proto_enumTypes[0].Descriptor()
}

func (BaseError) Type() protoreflect.EnumType {
	return &file_tanlive_errors_base_proto_enumTypes[0]
}

func (x BaseError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BaseError.Descriptor instead.
func (BaseError) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_errors_base_proto_rawDescGZIP(), []int{0}
}

var File_tanlive_errors_base_proto protoreflect.FileDescriptor

var file_tanlive_errors_base_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2a, 0x83, 0x02, 0x0a, 0x09,
	0x42, 0x61, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x61, 0x73,
	0x65, 0x4e, 0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0a, 0x42, 0x61,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x10, 0x90, 0x03, 0x12, 0x11, 0x0a, 0x0c, 0x55,
	0x6e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x10, 0x91, 0x03, 0x12, 0x0e,
	0x0a, 0x09, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x10, 0x93, 0x03, 0x12, 0x0d,
	0x0a, 0x08, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x94, 0x03, 0x12, 0x15, 0x0a,
	0x10, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4e, 0x6f, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x10, 0x95, 0x03, 0x12, 0x13, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10, 0x98, 0x03, 0x12, 0x18, 0x0a, 0x13, 0x55, 0x6e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x10, 0xa6, 0x03, 0x12, 0x14, 0x0a, 0x0f, 0x54, 0x6f, 0x6f, 0x4d, 0x61, 0x6e, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x10, 0xad, 0x03, 0x12, 0x17, 0x0a, 0x12, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10,
	0xf7, 0x03, 0x12, 0x17, 0x0a, 0x12, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0xe8, 0x07, 0x12, 0x14, 0x0a, 0x0f, 0x54,
	0x71, 0x6c, 0x47, 0x72, 0x61, 0x6d, 0x6d, 0x61, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0xe9,
	0x07, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65,
	0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_errors_base_proto_rawDescOnce sync.Once
	file_tanlive_errors_base_proto_rawDescData = file_tanlive_errors_base_proto_rawDesc
)

func file_tanlive_errors_base_proto_rawDescGZIP() []byte {
	file_tanlive_errors_base_proto_rawDescOnce.Do(func() {
		file_tanlive_errors_base_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_errors_base_proto_rawDescData)
	})
	return file_tanlive_errors_base_proto_rawDescData
}

var file_tanlive_errors_base_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_errors_base_proto_goTypes = []interface{}{
	(BaseError)(0), // 0: tanlive.errors.BaseError
}
var file_tanlive_errors_base_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_errors_base_proto_init() }
func file_tanlive_errors_base_proto_init() {
	if File_tanlive_errors_base_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_errors_base_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_errors_base_proto_goTypes,
		DependencyIndexes: file_tanlive_errors_base_proto_depIdxs,
		EnumInfos:         file_tanlive_errors_base_proto_enumTypes,
	}.Build()
	File_tanlive_errors_base_proto = out.File
	file_tanlive_errors_base_proto_rawDesc = nil
	file_tanlive_errors_base_proto_goTypes = nil
	file_tanlive_errors_base_proto_depIdxs = nil
}
