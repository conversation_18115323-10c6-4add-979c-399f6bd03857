// Package errors 错误定义
package errors

var names = []map[int32]string{
	AiError_name,
	BaseError_name,
	CmsError_name,
	IamError_name,
	MgmtError_name,
	NotifyError_name,
	ReviewError_name,
	SearchError_name,
	SupportError_name,
	TagError_name,
	TeamError_name,
}

// GetErrorNamesMap 返回错误名称映射
func GetErrorNamesMap() map[int32]string {
	m := make(map[int32]string)
	for _, name := range names {
		for k, v := range name {
			m[k] = v
		}
	}
	return m
}
