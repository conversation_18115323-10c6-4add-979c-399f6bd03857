syntax = "proto3";

package tanlive.errors;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors";

// IAM服务错误
// 范围：[2000, 3000)
enum IamError {
  IamNoError = 0;
  // 账号或密码错误
  InvalidUsernameOrPassword = 2000;
  // 账户已被冻结
  AccountFrozen = 2001;
  // 第三方账号已被绑定
  ThirdAccountHasBeenBound = 2002;
  // 用户不存在
  IamUserNotFound = 2003;
  // 用户未设置邮箱
  IamUserNoEmail = 2004;
  // 用户未设置手机号
  IamUserNoPhone = 2005;
  // 一次性密码已失效（从notify迁移过来，为兼容性保留原值）
  IamOtpExpired = 10000;
  // 一次性密码不匹配（从notify迁移过来，为兼容性保留原值）
  IamOtpMismatched = 10001;
  // 不支持的一次性密码接收者
  IamUnsupportedOtpReceiver = 2006;
  // 二次验证失败
  IamTfaFailed = 2007;
  // 用户名已存在
  IamUsernameAlreadyExists = 2008;
  // 手机号已存在
  IamPhoneAlreadyExists = 2009;
  // 邮箱已存在
  IamEmailAlreadyExists = 2010;
  // 激活邮件链接已失效
  IamActiveEmailLinkExpired = 2011;
  // 微信已被绑定
  IamWeixinAlreadyBoundAccount = 2012;
  // 微信二维码已被使用
  IamWeixinQrcodeAlreadyUse = 2013;
  // 用户没有订阅微信公众号
  IamUserNotSubscribeWeixinOfficialAccount = 2014;
  // 用户未绑定微信
  IamUserNotBindWeixin = 2015;
  // 微信未绑定账号
  IamWeixinNotBoundAccount = 2016;
  // 谷歌邮箱已被绑定
  IamGmailAlreadyBoundAccount = 2017;
  // 密码错误
  InvalidPassword = 2018;
  // 谷歌邮箱未绑定账号
  IamGmailNotBoundAccount = 2019;
  // 生成唯一用户名时到达最大尝试次数
  IamUniqueUsernameReachMaxAttempts = 2020;
  // 用户已冻结
  IamUserAlreadyDisabled = 2021;
  // 用户已解冻
  IamUserAlreadyEnabled = 2022;
}
