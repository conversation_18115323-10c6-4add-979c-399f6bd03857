// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/errors/ai.proto

package errors

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AI服务错误
// 范围：[16000, 17000)
type AiError int32

const (
	AiError_AiNoError AiError = 0
	// QA中的问题已经存在
	AiError_AiCollectionQuestionExisted AiError = 16001
	// 用户反馈已被采用
	AiError_AiFeedbackAlreadyAccepted AiError = 16002
	// 用户反馈已标记已读
	AiError_AiFeedbackAlreadyRead AiError = 16003
	// 用户反馈状态不允许被采用
	AiError_AiFeedbackStateNotAllowedAccept AiError = 16004
	// 用户chat不存在
	AiError_AiChatNoPermission AiError = 16005
	// 非法的文档状态转换
	AiError_AiCollectionDocStateChangeInvalid AiError = 16006
	// 问题审核失败
	AiError_AiQuestionReviewReject AiError = 16007
	// 非法的AI租户
	AiError_AiTenantInvalid AiError = 16008
	// 非法的文档内容状态
	AiError_AiInvalidDocContentState AiError = 16009
	// doc中的文本/文件已经存在
	AiError_AiCollectionTextFileExisted AiError = 16010
	// 非法的自定义列转换
	AiError_AiInvalidCustomLabelConvert AiError = 16011
	// 助手不存在
	AiError_AiAssistantNotFound AiError = 16012
	// 助手名称已存在
	AiError_AiAssistantNameExisted AiError = 16013
	// 助手英文名称已存在
	AiError_AiAssistantNameEnExisted AiError = 16014
	// 助手路由已存在
	AiError_AiAssistantRoutePathExisted AiError = 16015
	// 助手已禁用
	AiError_AiAssistantDisabled AiError = 16016
	// 助手当前功能已禁用
	AiError_AiAssistantFeatureDisabled AiError = 16017
	// 助手应用ID已存在
	AiError_AiAssistantAppIdExisted AiError = 16018
	// 小程序code无效
	AiError_AiMiniProgramNoCodeLogin AiError = 16019
	// 不是知识的贡献者
	AiError_AiNotDocContributor AiError = 16020
	// 助手客服用户名重复
	AiError_AiAssistantKefuStaffUsernameRepeated AiError = 16021
	// 文档有运行中的分段任务
	AiError_AiDocHasRunningChunkTask AiError = 16022
	// doc外部源Token已过期
	AiError_AiDocExternalSourceTokenExpired AiError = 16023
	// 文件剪存文件夹不存在
	AiError_AiDocTencentWebClicpDirNotExisted AiError = 16024
)

// Enum value maps for AiError.
var (
	AiError_name = map[int32]string{
		0:     "AiNoError",
		16001: "AiCollectionQuestionExisted",
		16002: "AiFeedbackAlreadyAccepted",
		16003: "AiFeedbackAlreadyRead",
		16004: "AiFeedbackStateNotAllowedAccept",
		16005: "AiChatNoPermission",
		16006: "AiCollectionDocStateChangeInvalid",
		16007: "AiQuestionReviewReject",
		16008: "AiTenantInvalid",
		16009: "AiInvalidDocContentState",
		16010: "AiCollectionTextFileExisted",
		16011: "AiInvalidCustomLabelConvert",
		16012: "AiAssistantNotFound",
		16013: "AiAssistantNameExisted",
		16014: "AiAssistantNameEnExisted",
		16015: "AiAssistantRoutePathExisted",
		16016: "AiAssistantDisabled",
		16017: "AiAssistantFeatureDisabled",
		16018: "AiAssistantAppIdExisted",
		16019: "AiMiniProgramNoCodeLogin",
		16020: "AiNotDocContributor",
		16021: "AiAssistantKefuStaffUsernameRepeated",
		16022: "AiDocHasRunningChunkTask",
		16023: "AiDocExternalSourceTokenExpired",
		16024: "AiDocTencentWebClicpDirNotExisted",
	}
	AiError_value = map[string]int32{
		"AiNoError":                            0,
		"AiCollectionQuestionExisted":          16001,
		"AiFeedbackAlreadyAccepted":            16002,
		"AiFeedbackAlreadyRead":                16003,
		"AiFeedbackStateNotAllowedAccept":      16004,
		"AiChatNoPermission":                   16005,
		"AiCollectionDocStateChangeInvalid":    16006,
		"AiQuestionReviewReject":               16007,
		"AiTenantInvalid":                      16008,
		"AiInvalidDocContentState":             16009,
		"AiCollectionTextFileExisted":          16010,
		"AiInvalidCustomLabelConvert":          16011,
		"AiAssistantNotFound":                  16012,
		"AiAssistantNameExisted":               16013,
		"AiAssistantNameEnExisted":             16014,
		"AiAssistantRoutePathExisted":          16015,
		"AiAssistantDisabled":                  16016,
		"AiAssistantFeatureDisabled":           16017,
		"AiAssistantAppIdExisted":              16018,
		"AiMiniProgramNoCodeLogin":             16019,
		"AiNotDocContributor":                  16020,
		"AiAssistantKefuStaffUsernameRepeated": 16021,
		"AiDocHasRunningChunkTask":             16022,
		"AiDocExternalSourceTokenExpired":      16023,
		"AiDocTencentWebClicpDirNotExisted":    16024,
	}
)

func (x AiError) Enum() *AiError {
	p := new(AiError)
	*p = x
	return p
}

func (x AiError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AiError) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_errors_ai_proto_enumTypes[0].Descriptor()
}

func (AiError) Type() protoreflect.EnumType {
	return &file_tanlive_errors_ai_proto_enumTypes[0]
}

func (x AiError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AiError.Descriptor instead.
func (AiError) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_errors_ai_proto_rawDescGZIP(), []int{0}
}

var File_tanlive_errors_ai_proto protoreflect.FileDescriptor

var file_tanlive_errors_ai_proto_rawDesc = []byte{
	0x0a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2a, 0x95, 0x06, 0x0a, 0x07, 0x41, 0x69,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x69, 0x4e, 0x6f, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1b, 0x41, 0x69, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x69, 0x73,
	0x74, 0x65, 0x64, 0x10, 0x81, 0x7d, 0x12, 0x1e, 0x0a, 0x19, 0x41, 0x69, 0x46, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x41, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x65, 0x64, 0x10, 0x82, 0x7d, 0x12, 0x1a, 0x0a, 0x15, 0x41, 0x69, 0x46, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x61, 0x64, 0x10,
	0x83, 0x7d, 0x12, 0x24, 0x0a, 0x1f, 0x41, 0x69, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x10, 0x84, 0x7d, 0x12, 0x17, 0x0a, 0x12, 0x41, 0x69, 0x43, 0x68,
	0x61, 0x74, 0x4e, 0x6f, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x85,
	0x7d, 0x12, 0x26, 0x0a, 0x21, 0x41, 0x69, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49,
	0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x86, 0x7d, 0x12, 0x1b, 0x0a, 0x16, 0x41, 0x69, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x6a,
	0x65, 0x63, 0x74, 0x10, 0x87, 0x7d, 0x12, 0x14, 0x0a, 0x0f, 0x41, 0x69, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x88, 0x7d, 0x12, 0x1d, 0x0a, 0x18,
	0x41, 0x69, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x44, 0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0x89, 0x7d, 0x12, 0x20, 0x0a, 0x1b, 0x41,
	0x69, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x10, 0x8a, 0x7d, 0x12, 0x20, 0x0a,
	0x1b, 0x41, 0x69, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x10, 0x8b, 0x7d, 0x12,
	0x18, 0x0a, 0x13, 0x41, 0x69, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4e, 0x6f,
	0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x8c, 0x7d, 0x12, 0x1b, 0x0a, 0x16, 0x41, 0x69, 0x41,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x69, 0x73,
	0x74, 0x65, 0x64, 0x10, 0x8d, 0x7d, 0x12, 0x1d, 0x0a, 0x18, 0x41, 0x69, 0x41, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x65, 0x64, 0x10, 0x8e, 0x7d, 0x12, 0x20, 0x0a, 0x1b, 0x41, 0x69, 0x41, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x50, 0x61, 0x74, 0x68, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x65, 0x64, 0x10, 0x8f, 0x7d, 0x12, 0x18, 0x0a, 0x13, 0x41, 0x69, 0x41, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x90,
	0x7d, 0x12, 0x1f, 0x0a, 0x1a, 0x41, 0x69, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10,
	0x91, 0x7d, 0x12, 0x1c, 0x0a, 0x17, 0x41, 0x69, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x41, 0x70, 0x70, 0x49, 0x64, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x10, 0x92, 0x7d,
	0x12, 0x1d, 0x0a, 0x18, 0x41, 0x69, 0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x4e, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x10, 0x93, 0x7d, 0x12,
	0x18, 0x0a, 0x13, 0x41, 0x69, 0x4e, 0x6f, 0x74, 0x44, 0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x10, 0x94, 0x7d, 0x12, 0x29, 0x0a, 0x24, 0x41, 0x69, 0x41,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4b, 0x65, 0x66, 0x75, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x10, 0x95, 0x7d, 0x12, 0x1d, 0x0a, 0x18, 0x41, 0x69, 0x44, 0x6f, 0x63, 0x48, 0x61, 0x73,
	0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x54, 0x61, 0x73, 0x6b,
	0x10, 0x96, 0x7d, 0x12, 0x24, 0x0a, 0x1f, 0x41, 0x69, 0x44, 0x6f, 0x63, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x97, 0x7d, 0x12, 0x26, 0x0a, 0x21, 0x41, 0x69, 0x44,
	0x6f, 0x63, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x57, 0x65, 0x62, 0x43, 0x6c, 0x69, 0x63,
	0x70, 0x44, 0x69, 0x72, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x10, 0x98,
	0x7d, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65,
	0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_errors_ai_proto_rawDescOnce sync.Once
	file_tanlive_errors_ai_proto_rawDescData = file_tanlive_errors_ai_proto_rawDesc
)

func file_tanlive_errors_ai_proto_rawDescGZIP() []byte {
	file_tanlive_errors_ai_proto_rawDescOnce.Do(func() {
		file_tanlive_errors_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_errors_ai_proto_rawDescData)
	})
	return file_tanlive_errors_ai_proto_rawDescData
}

var file_tanlive_errors_ai_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_errors_ai_proto_goTypes = []interface{}{
	(AiError)(0), // 0: tanlive.errors.AiError
}
var file_tanlive_errors_ai_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_errors_ai_proto_init() }
func file_tanlive_errors_ai_proto_init() {
	if File_tanlive_errors_ai_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_errors_ai_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_errors_ai_proto_goTypes,
		DependencyIndexes: file_tanlive_errors_ai_proto_depIdxs,
		EnumInfos:         file_tanlive_errors_ai_proto_enumTypes,
	}.Build()
	File_tanlive_errors_ai_proto = out.File
	file_tanlive_errors_ai_proto_rawDesc = nil
	file_tanlive_errors_ai_proto_goTypes = nil
	file_tanlive_errors_ai_proto_depIdxs = nil
}
