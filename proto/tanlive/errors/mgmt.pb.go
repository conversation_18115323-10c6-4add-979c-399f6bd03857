// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/errors/mgmt.proto

package errors

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 运营端服务错误
// 范围：[11000, 12000)
type MgmtError int32

const (
	MgmtError_MgmtNoError MgmtError = 0
	// 用户名或密码错误
	MgmtError_MgmtInvalidUsernameOrPassword MgmtError = 11000
)

// Enum value maps for MgmtError.
var (
	MgmtError_name = map[int32]string{
		0:     "MgmtNoError",
		11000: "MgmtInvalidUsernameOrPassword",
	}
	MgmtError_value = map[string]int32{
		"MgmtNoError":                   0,
		"MgmtInvalidUsernameOrPassword": 11000,
	}
)

func (x MgmtError) Enum() *MgmtError {
	p := new(MgmtError)
	*p = x
	return p
}

func (x MgmtError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MgmtError) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_errors_mgmt_proto_enumTypes[0].Descriptor()
}

func (MgmtError) Type() protoreflect.EnumType {
	return &file_tanlive_errors_mgmt_proto_enumTypes[0]
}

func (x MgmtError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MgmtError.Descriptor instead.
func (MgmtError) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_errors_mgmt_proto_rawDescGZIP(), []int{0}
}

var File_tanlive_errors_mgmt_proto protoreflect.FileDescriptor

var file_tanlive_errors_mgmt_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2a, 0x40, 0x0a, 0x09, 0x4d,
	0x67, 0x6d, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x67, 0x6d, 0x74,
	0x4e, 0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1d, 0x4d, 0x67, 0x6d,
	0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x4f, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x10, 0xf8, 0x55, 0x42, 0x40, 0x5a,
	0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65,
	0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_errors_mgmt_proto_rawDescOnce sync.Once
	file_tanlive_errors_mgmt_proto_rawDescData = file_tanlive_errors_mgmt_proto_rawDesc
)

func file_tanlive_errors_mgmt_proto_rawDescGZIP() []byte {
	file_tanlive_errors_mgmt_proto_rawDescOnce.Do(func() {
		file_tanlive_errors_mgmt_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_errors_mgmt_proto_rawDescData)
	})
	return file_tanlive_errors_mgmt_proto_rawDescData
}

var file_tanlive_errors_mgmt_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_errors_mgmt_proto_goTypes = []interface{}{
	(MgmtError)(0), // 0: tanlive.errors.MgmtError
}
var file_tanlive_errors_mgmt_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_errors_mgmt_proto_init() }
func file_tanlive_errors_mgmt_proto_init() {
	if File_tanlive_errors_mgmt_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_errors_mgmt_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_errors_mgmt_proto_goTypes,
		DependencyIndexes: file_tanlive_errors_mgmt_proto_depIdxs,
		EnumInfos:         file_tanlive_errors_mgmt_proto_enumTypes,
	}.Build()
	File_tanlive_errors_mgmt_proto = out.File
	file_tanlive_errors_mgmt_proto_rawDesc = nil
	file_tanlive_errors_mgmt_proto_goTypes = nil
	file_tanlive_errors_mgmt_proto_depIdxs = nil
}
