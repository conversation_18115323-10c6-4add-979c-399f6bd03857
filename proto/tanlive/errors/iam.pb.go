// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/errors/iam.proto

package errors

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// IAM服务错误
// 范围：[2000, 3000)
type IamError int32

const (
	IamError_IamNoError IamError = 0
	// 账号或密码错误
	IamError_InvalidUsernameOrPassword IamError = 2000
	// 账户已被冻结
	IamError_AccountFrozen IamError = 2001
	// 第三方账号已被绑定
	IamError_ThirdAccountHasBeenBound IamError = 2002
	// 用户不存在
	IamError_IamUserNotFound IamError = 2003
	// 用户未设置邮箱
	IamError_IamUserNoEmail IamError = 2004
	// 用户未设置手机号
	IamError_IamUserNoPhone IamError = 2005
	// 一次性密码已失效（从notify迁移过来，为兼容性保留原值）
	IamError_IamOtpExpired IamError = 10000
	// 一次性密码不匹配（从notify迁移过来，为兼容性保留原值）
	IamError_IamOtpMismatched IamError = 10001
	// 不支持的一次性密码接收者
	IamError_IamUnsupportedOtpReceiver IamError = 2006
	// 二次验证失败
	IamError_IamTfaFailed IamError = 2007
	// 用户名已存在
	IamError_IamUsernameAlreadyExists IamError = 2008
	// 手机号已存在
	IamError_IamPhoneAlreadyExists IamError = 2009
	// 邮箱已存在
	IamError_IamEmailAlreadyExists IamError = 2010
	// 激活邮件链接已失效
	IamError_IamActiveEmailLinkExpired IamError = 2011
	// 微信已被绑定
	IamError_IamWeixinAlreadyBoundAccount IamError = 2012
	// 微信二维码已被使用
	IamError_IamWeixinQrcodeAlreadyUse IamError = 2013
	// 用户没有订阅微信公众号
	IamError_IamUserNotSubscribeWeixinOfficialAccount IamError = 2014
	// 用户未绑定微信
	IamError_IamUserNotBindWeixin IamError = 2015
	// 微信未绑定账号
	IamError_IamWeixinNotBoundAccount IamError = 2016
	// 谷歌邮箱已被绑定
	IamError_IamGmailAlreadyBoundAccount IamError = 2017
	// 密码错误
	IamError_InvalidPassword IamError = 2018
	// 谷歌邮箱未绑定账号
	IamError_IamGmailNotBoundAccount IamError = 2019
	// 生成唯一用户名时到达最大尝试次数
	IamError_IamUniqueUsernameReachMaxAttempts IamError = 2020
	// 用户已冻结
	IamError_IamUserAlreadyDisabled IamError = 2021
	// 用户已解冻
	IamError_IamUserAlreadyEnabled IamError = 2022
)

// Enum value maps for IamError.
var (
	IamError_name = map[int32]string{
		0:     "IamNoError",
		2000:  "InvalidUsernameOrPassword",
		2001:  "AccountFrozen",
		2002:  "ThirdAccountHasBeenBound",
		2003:  "IamUserNotFound",
		2004:  "IamUserNoEmail",
		2005:  "IamUserNoPhone",
		10000: "IamOtpExpired",
		10001: "IamOtpMismatched",
		2006:  "IamUnsupportedOtpReceiver",
		2007:  "IamTfaFailed",
		2008:  "IamUsernameAlreadyExists",
		2009:  "IamPhoneAlreadyExists",
		2010:  "IamEmailAlreadyExists",
		2011:  "IamActiveEmailLinkExpired",
		2012:  "IamWeixinAlreadyBoundAccount",
		2013:  "IamWeixinQrcodeAlreadyUse",
		2014:  "IamUserNotSubscribeWeixinOfficialAccount",
		2015:  "IamUserNotBindWeixin",
		2016:  "IamWeixinNotBoundAccount",
		2017:  "IamGmailAlreadyBoundAccount",
		2018:  "InvalidPassword",
		2019:  "IamGmailNotBoundAccount",
		2020:  "IamUniqueUsernameReachMaxAttempts",
		2021:  "IamUserAlreadyDisabled",
		2022:  "IamUserAlreadyEnabled",
	}
	IamError_value = map[string]int32{
		"IamNoError":                               0,
		"InvalidUsernameOrPassword":                2000,
		"AccountFrozen":                            2001,
		"ThirdAccountHasBeenBound":                 2002,
		"IamUserNotFound":                          2003,
		"IamUserNoEmail":                           2004,
		"IamUserNoPhone":                           2005,
		"IamOtpExpired":                            10000,
		"IamOtpMismatched":                         10001,
		"IamUnsupportedOtpReceiver":                2006,
		"IamTfaFailed":                             2007,
		"IamUsernameAlreadyExists":                 2008,
		"IamPhoneAlreadyExists":                    2009,
		"IamEmailAlreadyExists":                    2010,
		"IamActiveEmailLinkExpired":                2011,
		"IamWeixinAlreadyBoundAccount":             2012,
		"IamWeixinQrcodeAlreadyUse":                2013,
		"IamUserNotSubscribeWeixinOfficialAccount": 2014,
		"IamUserNotBindWeixin":                     2015,
		"IamWeixinNotBoundAccount":                 2016,
		"IamGmailAlreadyBoundAccount":              2017,
		"InvalidPassword":                          2018,
		"IamGmailNotBoundAccount":                  2019,
		"IamUniqueUsernameReachMaxAttempts":        2020,
		"IamUserAlreadyDisabled":                   2021,
		"IamUserAlreadyEnabled":                    2022,
	}
)

func (x IamError) Enum() *IamError {
	p := new(IamError)
	*p = x
	return p
}

func (x IamError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IamError) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_errors_iam_proto_enumTypes[0].Descriptor()
}

func (IamError) Type() protoreflect.EnumType {
	return &file_tanlive_errors_iam_proto_enumTypes[0]
}

func (x IamError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IamError.Descriptor instead.
func (IamError) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_errors_iam_proto_rawDescGZIP(), []int{0}
}

var File_tanlive_errors_iam_proto protoreflect.FileDescriptor

var file_tanlive_errors_iam_proto_rawDesc = []byte{
	0x0a, 0x18, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x69, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2a, 0xe5, 0x05, 0x0a, 0x08, 0x49,
	0x61, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x61, 0x6d, 0x4e, 0x6f,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x19, 0x49, 0x6e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x4f, 0x72, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x10, 0xd0, 0x0f, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x46, 0x72, 0x6f, 0x7a, 0x65, 0x6e, 0x10, 0xd1, 0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x54,
	0x68, 0x69, 0x72, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x42, 0x65,
	0x65, 0x6e, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0xd2, 0x0f, 0x12, 0x14, 0x0a, 0x0f, 0x49, 0x61,
	0x6d, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0xd3, 0x0f,
	0x12, 0x13, 0x0a, 0x0e, 0x49, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x10, 0xd4, 0x0f, 0x12, 0x13, 0x0a, 0x0e, 0x49, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72,
	0x4e, 0x6f, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x10, 0xd5, 0x0f, 0x12, 0x12, 0x0a, 0x0d, 0x49, 0x61,
	0x6d, 0x4f, 0x74, 0x70, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x90, 0x4e, 0x12, 0x15,
	0x0a, 0x10, 0x49, 0x61, 0x6d, 0x4f, 0x74, 0x70, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x64, 0x10, 0x91, 0x4e, 0x12, 0x1e, 0x0a, 0x19, 0x49, 0x61, 0x6d, 0x55, 0x6e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x10, 0xd6, 0x0f, 0x12, 0x11, 0x0a, 0x0c, 0x49, 0x61, 0x6d, 0x54, 0x66, 0x61, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0xd7, 0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x49, 0x61, 0x6d, 0x55,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x73, 0x10, 0xd8, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x49, 0x61, 0x6d, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73,
	0x10, 0xd9, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x49, 0x61, 0x6d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41,
	0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0xda, 0x0f, 0x12,
	0x1e, 0x0a, 0x19, 0x49, 0x61, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0xdb, 0x0f, 0x12,
	0x21, 0x0a, 0x1c, 0x49, 0x61, 0x6d, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x41, 0x6c, 0x72, 0x65,
	0x61, 0x64, 0x79, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x10,
	0xdc, 0x0f, 0x12, 0x1e, 0x0a, 0x19, 0x49, 0x61, 0x6d, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51,
	0x72, 0x63, 0x6f, 0x64, 0x65, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x55, 0x73, 0x65, 0x10,
	0xdd, 0x0f, 0x12, 0x2d, 0x0a, 0x28, 0x49, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x74,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x4f,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0xde,
	0x0f, 0x12, 0x19, 0x0a, 0x14, 0x49, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x42,
	0x69, 0x6e, 0x64, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x10, 0xdf, 0x0f, 0x12, 0x1d, 0x0a, 0x18,
	0x49, 0x61, 0x6d, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x42, 0x6f, 0x75, 0x6e,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0xe0, 0x0f, 0x12, 0x20, 0x0a, 0x1b, 0x49,
	0x61, 0x6d, 0x47, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x42, 0x6f,
	0x75, 0x6e, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0xe1, 0x0f, 0x12, 0x14, 0x0a,
	0x0f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x10, 0xe2, 0x0f, 0x12, 0x1c, 0x0a, 0x17, 0x49, 0x61, 0x6d, 0x47, 0x6d, 0x61, 0x69, 0x6c, 0x4e,
	0x6f, 0x74, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0xe3,
	0x0f, 0x12, 0x26, 0x0a, 0x21, 0x49, 0x61, 0x6d, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x61, 0x63, 0x68, 0x4d, 0x61, 0x78, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x10, 0xe4, 0x0f, 0x12, 0x1b, 0x0a, 0x16, 0x49, 0x61, 0x6d,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x10, 0xe5, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x49, 0x61, 0x6d, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10,
	0xe6, 0x0f, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e,
	0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_errors_iam_proto_rawDescOnce sync.Once
	file_tanlive_errors_iam_proto_rawDescData = file_tanlive_errors_iam_proto_rawDesc
)

func file_tanlive_errors_iam_proto_rawDescGZIP() []byte {
	file_tanlive_errors_iam_proto_rawDescOnce.Do(func() {
		file_tanlive_errors_iam_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_errors_iam_proto_rawDescData)
	})
	return file_tanlive_errors_iam_proto_rawDescData
}

var file_tanlive_errors_iam_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_errors_iam_proto_goTypes = []interface{}{
	(IamError)(0), // 0: tanlive.errors.IamError
}
var file_tanlive_errors_iam_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_errors_iam_proto_init() }
func file_tanlive_errors_iam_proto_init() {
	if File_tanlive_errors_iam_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_errors_iam_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_errors_iam_proto_goTypes,
		DependencyIndexes: file_tanlive_errors_iam_proto_depIdxs,
		EnumInfos:         file_tanlive_errors_iam_proto_enumTypes,
	}.Build()
	File_tanlive_errors_iam_proto = out.File
	file_tanlive_errors_iam_proto_rawDesc = nil
	file_tanlive_errors_iam_proto_goTypes = nil
	file_tanlive_errors_iam_proto_depIdxs = nil
}
