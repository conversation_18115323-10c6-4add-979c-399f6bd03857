syntax = "proto3";

package tanlive.errors;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors";

// 团队服务错误
// 范围：[3000, 4000)
enum TeamError {
  TeamNoError = 0;
  // 团队全称已存在
  FullNameExisted = 3000;
  // 团队简称已存在
  ShortNameExisted = 3001;
  // 团队不存在
  TeamNotFound = 3002;
  // 非法的UGC状态
  InvalidUgcState = 3003;
  // 用户已经加入团队
  UserAlreadyJoinedTeam = 3004;
  // 团队全称已存在
  TeamFullNameConflict = 3005;
  // 团队简称已存在
  TeamShortNameConflict = 3006;
  // 用户未加入团队
  UserNotJoinTeam = 3007;
  // 自定义路由已被占用
  CustomRouteIsInUsed = 3008;
  // 创建自定义路由时UGCid已被占用
  CustomRouteUgcIdExists = 3009;
  // 团队状态不允许编辑
  TeamNotEditable = 3010;
  // 团队状态不允许提交审核
  TeamNotReviewable = 3011;
}
