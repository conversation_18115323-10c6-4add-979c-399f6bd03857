// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/errors/review.proto

package errors

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 审核服务错误
// 范围：[8000, 9000)
type ReviewError int32

const (
	ReviewError_ReviewNoError ReviewError = 0
	// 审核信息未找到
	ReviewError_ReviewInfoNotFound ReviewError = 8000
	// 审核状态非法
	ReviewError_InvalidReviewState ReviewError = 8001
)

// Enum value maps for ReviewError.
var (
	ReviewError_name = map[int32]string{
		0:    "ReviewNoError",
		8000: "ReviewInfoNotFound",
		8001: "InvalidReviewState",
	}
	ReviewError_value = map[string]int32{
		"ReviewNoError":      0,
		"ReviewInfoNotFound": 8000,
		"InvalidReviewState": 8001,
	}
)

func (x ReviewError) Enum() *ReviewError {
	p := new(ReviewError)
	*p = x
	return p
}

func (x ReviewError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewError) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_errors_review_proto_enumTypes[0].Descriptor()
}

func (ReviewError) Type() protoreflect.EnumType {
	return &file_tanlive_errors_review_proto_enumTypes[0]
}

func (x ReviewError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewError.Descriptor instead.
func (ReviewError) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_errors_review_proto_rawDescGZIP(), []int{0}
}

var File_tanlive_errors_review_proto protoreflect.FileDescriptor

var file_tanlive_errors_review_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2a, 0x52, 0x0a,
	0x0b, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x11, 0x0a, 0x0d,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4e, 0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x00, 0x12,
	0x17, 0x0a, 0x12, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x4e, 0x6f, 0x74,
	0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0xc0, 0x3e, 0x12, 0x17, 0x0a, 0x12, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0xc1,
	0x3e, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65,
	0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_errors_review_proto_rawDescOnce sync.Once
	file_tanlive_errors_review_proto_rawDescData = file_tanlive_errors_review_proto_rawDesc
)

func file_tanlive_errors_review_proto_rawDescGZIP() []byte {
	file_tanlive_errors_review_proto_rawDescOnce.Do(func() {
		file_tanlive_errors_review_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_errors_review_proto_rawDescData)
	})
	return file_tanlive_errors_review_proto_rawDescData
}

var file_tanlive_errors_review_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_errors_review_proto_goTypes = []interface{}{
	(ReviewError)(0), // 0: tanlive.errors.ReviewError
}
var file_tanlive_errors_review_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_errors_review_proto_init() }
func file_tanlive_errors_review_proto_init() {
	if File_tanlive_errors_review_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_errors_review_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_errors_review_proto_goTypes,
		DependencyIndexes: file_tanlive_errors_review_proto_depIdxs,
		EnumInfos:         file_tanlive_errors_review_proto_enumTypes,
	}.Build()
	File_tanlive_errors_review_proto = out.File
	file_tanlive_errors_review_proto_rawDesc = nil
	file_tanlive_errors_review_proto_goTypes = nil
	file_tanlive_errors_review_proto_depIdxs = nil
}
