// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/errors/team.proto

package errors

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 团队服务错误
// 范围：[3000, 4000)
type TeamError int32

const (
	TeamError_TeamNoError TeamError = 0
	// 团队全称已存在
	TeamError_FullNameExisted TeamError = 3000
	// 团队简称已存在
	TeamError_ShortNameExisted TeamError = 3001
	// 团队不存在
	TeamError_TeamNotFound TeamError = 3002
	// 非法的UGC状态
	TeamError_InvalidUgcState TeamError = 3003
	// 用户已经加入团队
	TeamError_UserAlreadyJoinedTeam TeamError = 3004
	// 团队全称已存在
	TeamError_TeamFullNameConflict TeamError = 3005
	// 团队简称已存在
	TeamError_TeamShortNameConflict TeamError = 3006
	// 用户未加入团队
	TeamError_UserNotJoinTeam TeamError = 3007
	// 自定义路由已被占用
	TeamError_CustomRouteIsInUsed TeamError = 3008
	// 创建自定义路由时UGCid已被占用
	TeamError_CustomRouteUgcIdExists TeamError = 3009
	// 团队状态不允许编辑
	TeamError_TeamNotEditable TeamError = 3010
	// 团队状态不允许提交审核
	TeamError_TeamNotReviewable TeamError = 3011
)

// Enum value maps for TeamError.
var (
	TeamError_name = map[int32]string{
		0:    "TeamNoError",
		3000: "FullNameExisted",
		3001: "ShortNameExisted",
		3002: "TeamNotFound",
		3003: "InvalidUgcState",
		3004: "UserAlreadyJoinedTeam",
		3005: "TeamFullNameConflict",
		3006: "TeamShortNameConflict",
		3007: "UserNotJoinTeam",
		3008: "CustomRouteIsInUsed",
		3009: "CustomRouteUgcIdExists",
		3010: "TeamNotEditable",
		3011: "TeamNotReviewable",
	}
	TeamError_value = map[string]int32{
		"TeamNoError":            0,
		"FullNameExisted":        3000,
		"ShortNameExisted":       3001,
		"TeamNotFound":           3002,
		"InvalidUgcState":        3003,
		"UserAlreadyJoinedTeam":  3004,
		"TeamFullNameConflict":   3005,
		"TeamShortNameConflict":  3006,
		"UserNotJoinTeam":        3007,
		"CustomRouteIsInUsed":    3008,
		"CustomRouteUgcIdExists": 3009,
		"TeamNotEditable":        3010,
		"TeamNotReviewable":      3011,
	}
)

func (x TeamError) Enum() *TeamError {
	p := new(TeamError)
	*p = x
	return p
}

func (x TeamError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TeamError) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_errors_team_proto_enumTypes[0].Descriptor()
}

func (TeamError) Type() protoreflect.EnumType {
	return &file_tanlive_errors_team_proto_enumTypes[0]
}

func (x TeamError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TeamError.Descriptor instead.
func (TeamError) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_errors_team_proto_rawDescGZIP(), []int{0}
}

var File_tanlive_errors_team_proto protoreflect.FileDescriptor

var file_tanlive_errors_team_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2a, 0xc0, 0x02, 0x0a, 0x09,
	0x54, 0x65, 0x61, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x65, 0x61,
	0x6d, 0x4e, 0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x0f, 0x46, 0x75,
	0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x10, 0xb8, 0x17,
	0x12, 0x15, 0x0a, 0x10, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x65, 0x64, 0x10, 0xb9, 0x17, 0x12, 0x11, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4e,
	0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0xba, 0x17, 0x12, 0x14, 0x0a, 0x0f, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x55, 0x67, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0xbb, 0x17,
	0x12, 0x1a, 0x0a, 0x15, 0x55, 0x73, 0x65, 0x72, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x4a,
	0x6f, 0x69, 0x6e, 0x65, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x10, 0xbc, 0x17, 0x12, 0x19, 0x0a, 0x14,
	0x54, 0x65, 0x61, 0x6d, 0x46, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x6c, 0x69, 0x63, 0x74, 0x10, 0xbd, 0x17, 0x12, 0x1a, 0x0a, 0x15, 0x54, 0x65, 0x61, 0x6d, 0x53,
	0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74,
	0x10, 0xbe, 0x17, 0x12, 0x14, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x4a, 0x6f,
	0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x10, 0xbf, 0x17, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x49, 0x73, 0x49, 0x6e, 0x55, 0x73, 0x65, 0x64,
	0x10, 0xc0, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x55, 0x67, 0x63, 0x49, 0x64, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0xc1, 0x17,
	0x12, 0x14, 0x0a, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x4e, 0x6f, 0x74, 0x45, 0x64, 0x69, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x10, 0xc2, 0x17, 0x12, 0x16, 0x0a, 0x11, 0x54, 0x65, 0x61, 0x6d, 0x4e, 0x6f,
	0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x61, 0x62, 0x6c, 0x65, 0x10, 0xc3, 0x17, 0x42, 0x40,
	0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74,
	0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_errors_team_proto_rawDescOnce sync.Once
	file_tanlive_errors_team_proto_rawDescData = file_tanlive_errors_team_proto_rawDesc
)

func file_tanlive_errors_team_proto_rawDescGZIP() []byte {
	file_tanlive_errors_team_proto_rawDescOnce.Do(func() {
		file_tanlive_errors_team_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_errors_team_proto_rawDescData)
	})
	return file_tanlive_errors_team_proto_rawDescData
}

var file_tanlive_errors_team_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_errors_team_proto_goTypes = []interface{}{
	(TeamError)(0), // 0: tanlive.errors.TeamError
}
var file_tanlive_errors_team_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_errors_team_proto_init() }
func file_tanlive_errors_team_proto_init() {
	if File_tanlive_errors_team_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_errors_team_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_errors_team_proto_goTypes,
		DependencyIndexes: file_tanlive_errors_team_proto_depIdxs,
		EnumInfos:         file_tanlive_errors_team_proto_enumTypes,
	}.Build()
	File_tanlive_errors_team_proto = out.File
	file_tanlive_errors_team_proto_rawDesc = nil
	file_tanlive_errors_team_proto_goTypes = nil
	file_tanlive_errors_team_proto_depIdxs = nil
}
