syntax = "proto3";

package tanlive.errors;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors";

// 通用基础错误
// 范围：[1000, 2000)
// 优先使用HTTP状态码
enum BaseError {
  BaseNoError = 0;
  // 非法请求
  BadRequest = 400;
  // 请先登录
  Unauthorized = 401;
  // 没有权限
  Forbidden = 403;
  // 接口不存在
  NotFound = 404;
  // 请求方法不允许
  MethodNotAllowed = 405;
  // rpc请求超时
  RequestTimeout = 408;
  // 参数错误
  UnprocessableEntity = 422;
  // 请求过于频繁
  TooManyRequests = 429;
  // 服务暂时不可用
  ServiceUnavailable = 503;

  // 腾讯云验证码错误
  TcloudCaptchaError = 1000;
  // TQL语法错误
  TqlGrammarError = 1001;
}
