// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/errors/tag.proto

package errors

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 标签服务错误
// 范围：[13000, 14000)
type TagError int32

const (
	TagError_TagNoError TagError = 0
)

// Enum value maps for TagError.
var (
	TagError_name = map[int32]string{
		0: "TagNoError",
	}
	TagError_value = map[string]int32{
		"TagNoError": 0,
	}
)

func (x TagError) Enum() *TagError {
	p := new(TagError)
	*p = x
	return p
}

func (x TagError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TagError) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_errors_tag_proto_enumTypes[0].Descriptor()
}

func (TagError) Type() protoreflect.EnumType {
	return &file_tanlive_errors_tag_proto_enumTypes[0]
}

func (x TagError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TagError.Descriptor instead.
func (TagError) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_errors_tag_proto_rawDescGZIP(), []int{0}
}

var File_tanlive_errors_tag_proto protoreflect.FileDescriptor

var file_tanlive_errors_tag_proto_rawDesc = []byte{
	0x0a, 0x18, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2a, 0x1a, 0x0a, 0x08, 0x54, 0x61,
	0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x61, 0x67, 0x4e, 0x6f, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x10, 0x00, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73,
	0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_errors_tag_proto_rawDescOnce sync.Once
	file_tanlive_errors_tag_proto_rawDescData = file_tanlive_errors_tag_proto_rawDesc
)

func file_tanlive_errors_tag_proto_rawDescGZIP() []byte {
	file_tanlive_errors_tag_proto_rawDescOnce.Do(func() {
		file_tanlive_errors_tag_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_errors_tag_proto_rawDescData)
	})
	return file_tanlive_errors_tag_proto_rawDescData
}

var file_tanlive_errors_tag_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_errors_tag_proto_goTypes = []interface{}{
	(TagError)(0), // 0: tanlive.errors.TagError
}
var file_tanlive_errors_tag_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_errors_tag_proto_init() }
func file_tanlive_errors_tag_proto_init() {
	if File_tanlive_errors_tag_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_errors_tag_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_errors_tag_proto_goTypes,
		DependencyIndexes: file_tanlive_errors_tag_proto_depIdxs,
		EnumInfos:         file_tanlive_errors_tag_proto_enumTypes,
	}.Build()
	File_tanlive_errors_tag_proto = out.File
	file_tanlive_errors_tag_proto_rawDesc = nil
	file_tanlive_errors_tag_proto_goTypes = nil
	file_tanlive_errors_tag_proto_depIdxs = nil
}
