syntax = "proto3";

package tanlive.errors;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors";

// AI服务错误
// 范围：[16000, 17000)
enum AiError {
  AiNoError = 0;


  // QA中的问题已经存在
  AiCollectionQuestionExisted = 16001;
  // 用户反馈已被采用
  AiFeedbackAlreadyAccepted = 16002;
  // 用户反馈已标记已读
  AiFeedbackAlreadyRead = 16003;
  // 用户反馈状态不允许被采用
  AiFeedbackStateNotAllowedAccept = 16004;
  // 用户chat不存在
  AiChatNoPermission = 16005;
  // 非法的文档状态转换
  AiCollectionDocStateChangeInvalid = 16006;
  // 问题审核失败
  AiQuestionReviewReject = 16007;
  // 非法的AI租户
  AiTenantInvalid = 16008;
  // 非法的文档内容状态
  AiInvalidDocContentState = 16009;
  // doc中的文本/文件已经存在
  AiCollectionTextFileExisted = 16010;
  // 非法的自定义列转换
  AiInvalidCustomLabelConvert = 16011;
  // 助手不存在
  AiAssistantNotFound = 16012;
  // 助手名称已存在
  AiAssistantNameExisted = 16013;
  // 助手英文名称已存在
  AiAssistantNameEnExisted = 16014;
  // 助手路由已存在
  AiAssistantRoutePathExisted = 16015;
  // 助手已禁用
  AiAssistantDisabled = 16016;
  // 助手当前功能已禁用
  AiAssistantFeatureDisabled = 16017;
  // 助手应用ID已存在
  AiAssistantAppIdExisted = 16018;
  // 小程序code无效
  AiMiniProgramNoCodeLogin = 16019;
  // 不是知识的贡献者
  AiNotDocContributor = 16020;
  // 助手客服用户名重复
  AiAssistantKefuStaffUsernameRepeated = 16021;
  // 文档有运行中的分段任务
  AiDocHasRunningChunkTask = 16022;
  // doc外部源Token已过期
  AiDocExternalSourceTokenExpired = 16023;
  // 文件剪存文件夹不存在
  AiDocTencentWebClicpDirNotExisted = 16024;
}
