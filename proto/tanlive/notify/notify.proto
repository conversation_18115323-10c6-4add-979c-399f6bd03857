syntax = "proto3";

package tanlive.notify;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify";

import "tanlive/options.proto";

// 递送类型
enum DeliveryKind {
  DELIVERY_KIND_UNSPECIFIED = 0;
  // 短信
  DELIVERY_KIND_SMS = 1;
  // 邮箱
  DELIVERY_KIND_EMAIL = 2;
}

// 递送结果
message DeliveryResult {
  // 发送验证码的任务ID
  string task_id = 1;
  // SDK错误码
  string sdk_error = 2;
}

// 腾讯云邮件推送事件
message TcloudSesEvent {
  string event = 1;
  string email = 2 [(mask).rule = "email"];
  string link = 3;
  string bulk_id = 4;
  int32 timestamp = 5;
  string reason = 6;
  string bounce_type = 7;
  string username = 8 [(mask).rule = "secret"];
  string from = 9;
  string from_domain = 10;
  int32 template_id = 11;
  string useragent = 12;
}

// 腾讯云短信状态
message TcloudSmsStatus {
  string user_receive_time = 1;
  string nationcode = 2;
  string mobile = 3 [(mask).rule = "phone"];
  string report_status = 4;
  string errmsg = 5;
  string description = 6;
  string sid = 7;
  string ext = 8;
}
