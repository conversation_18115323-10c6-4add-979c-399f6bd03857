// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/notify/service.proto

package notify

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 邮件模板
type ReqSendOneTimePassword_EmailTemplate int32

const (
	// 默认
	ReqSendOneTimePassword_EMAIL_TEMPLATE_DEFAULT ReqSendOneTimePassword_EmailTemplate = 0
	// 团队认证
	ReqSendOneTimePassword_EMAIL_TEMPLATE_TEAM_VERIFY ReqSendOneTimePassword_EmailTemplate = 1
)

// Enum value maps for ReqSendOneTimePassword_EmailTemplate.
var (
	ReqSendOneTimePassword_EmailTemplate_name = map[int32]string{
		0: "EMAIL_TEMPLATE_DEFAULT",
		1: "EMAIL_TEMPLATE_TEAM_VERIFY",
	}
	ReqSendOneTimePassword_EmailTemplate_value = map[string]int32{
		"EMAIL_TEMPLATE_DEFAULT":     0,
		"EMAIL_TEMPLATE_TEAM_VERIFY": 1,
	}
)

func (x ReqSendOneTimePassword_EmailTemplate) Enum() *ReqSendOneTimePassword_EmailTemplate {
	p := new(ReqSendOneTimePassword_EmailTemplate)
	*p = x
	return p
}

func (x ReqSendOneTimePassword_EmailTemplate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReqSendOneTimePassword_EmailTemplate) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_notify_service_proto_enumTypes[0].Descriptor()
}

func (ReqSendOneTimePassword_EmailTemplate) Type() protoreflect.EnumType {
	return &file_tanlive_notify_service_proto_enumTypes[0]
}

func (x ReqSendOneTimePassword_EmailTemplate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReqSendOneTimePassword_EmailTemplate.Descriptor instead.
func (ReqSendOneTimePassword_EmailTemplate) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{0, 0}
}

type ReqSendOneTimePassword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 一次性密码
	Otp *ReqSendOneTimePassword_Otp `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
	// 递送器
	//
	// Types that are assignable to Deliverer:
	//
	//	*ReqSendOneTimePassword_SmsDeliverer
	//	*ReqSendOneTimePassword_EmailDeliverer
	Deliverer isReqSendOneTimePassword_Deliverer `protobuf_oneof:"deliverer"`
}

func (x *ReqSendOneTimePassword) Reset() {
	*x = ReqSendOneTimePassword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSendOneTimePassword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSendOneTimePassword) ProtoMessage() {}

func (x *ReqSendOneTimePassword) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSendOneTimePassword.ProtoReflect.Descriptor instead.
func (*ReqSendOneTimePassword) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqSendOneTimePassword) GetOtp() *ReqSendOneTimePassword_Otp {
	if x != nil {
		return x.Otp
	}
	return nil
}

func (m *ReqSendOneTimePassword) GetDeliverer() isReqSendOneTimePassword_Deliverer {
	if m != nil {
		return m.Deliverer
	}
	return nil
}

func (x *ReqSendOneTimePassword) GetSmsDeliverer() *ReqSendOneTimePassword_SmsPara {
	if x, ok := x.GetDeliverer().(*ReqSendOneTimePassword_SmsDeliverer); ok {
		return x.SmsDeliverer
	}
	return nil
}

func (x *ReqSendOneTimePassword) GetEmailDeliverer() *ReqSendOneTimePassword_EmailPara {
	if x, ok := x.GetDeliverer().(*ReqSendOneTimePassword_EmailDeliverer); ok {
		return x.EmailDeliverer
	}
	return nil
}

type isReqSendOneTimePassword_Deliverer interface {
	isReqSendOneTimePassword_Deliverer()
}

type ReqSendOneTimePassword_SmsDeliverer struct {
	// 短信递送
	SmsDeliverer *ReqSendOneTimePassword_SmsPara `protobuf:"bytes,2,opt,name=sms_deliverer,json=smsDeliverer,proto3,oneof"`
}

type ReqSendOneTimePassword_EmailDeliverer struct {
	// 邮件递送
	EmailDeliverer *ReqSendOneTimePassword_EmailPara `protobuf:"bytes,3,opt,name=email_deliverer,json=emailDeliverer,proto3,oneof"`
}

func (*ReqSendOneTimePassword_SmsDeliverer) isReqSendOneTimePassword_Deliverer() {}

func (*ReqSendOneTimePassword_EmailDeliverer) isReqSendOneTimePassword_Deliverer() {}

type RspSendOneTimePassword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发送结果
	Result *DeliveryResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *RspSendOneTimePassword) Reset() {
	*x = RspSendOneTimePassword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSendOneTimePassword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSendOneTimePassword) ProtoMessage() {}

func (x *RspSendOneTimePassword) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSendOneTimePassword.ProtoReflect.Descriptor instead.
func (*RspSendOneTimePassword) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{1}
}

func (x *RspSendOneTimePassword) GetResult() *DeliveryResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type ReqSendActiveEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 邮件
	EmailAddress string `protobuf:"bytes,1,opt,name=email_address,json=emailAddress,proto3" json:"email_address,omitempty"`
	// 激活地址
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// 模板语言
	TemplateLang string `protobuf:"bytes,3,opt,name=template_lang,json=templateLang,proto3" json:"template_lang,omitempty"`
}

func (x *ReqSendActiveEmail) Reset() {
	*x = ReqSendActiveEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSendActiveEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSendActiveEmail) ProtoMessage() {}

func (x *ReqSendActiveEmail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSendActiveEmail.ProtoReflect.Descriptor instead.
func (*ReqSendActiveEmail) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReqSendActiveEmail) GetEmailAddress() string {
	if x != nil {
		return x.EmailAddress
	}
	return ""
}

func (x *ReqSendActiveEmail) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ReqSendActiveEmail) GetTemplateLang() string {
	if x != nil {
		return x.TemplateLang
	}
	return ""
}

type RspSendActiveEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发送结果
	Result *DeliveryResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *RspSendActiveEmail) Reset() {
	*x = RspSendActiveEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSendActiveEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSendActiveEmail) ProtoMessage() {}

func (x *RspSendActiveEmail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSendActiveEmail.ProtoReflect.Descriptor instead.
func (*RspSendActiveEmail) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{3}
}

func (x *RspSendActiveEmail) GetResult() *DeliveryResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type ReqGetTcloudSesEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// SendEmail 接口返回的 MessageId
	BulkId string `protobuf:"bytes,1,opt,name=bulk_id,json=bulkId,proto3" json:"bulk_id,omitempty"`
}

func (x *ReqGetTcloudSesEvent) Reset() {
	*x = ReqGetTcloudSesEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTcloudSesEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTcloudSesEvent) ProtoMessage() {}

func (x *ReqGetTcloudSesEvent) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTcloudSesEvent.ProtoReflect.Descriptor instead.
func (*ReqGetTcloudSesEvent) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{4}
}

func (x *ReqGetTcloudSesEvent) GetBulkId() string {
	if x != nil {
		return x.BulkId
	}
	return ""
}

type RspGetTcloudSesEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event *TcloudSesEvent `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
}

func (x *RspGetTcloudSesEvent) Reset() {
	*x = RspGetTcloudSesEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTcloudSesEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTcloudSesEvent) ProtoMessage() {}

func (x *RspGetTcloudSesEvent) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTcloudSesEvent.ProtoReflect.Descriptor instead.
func (*RspGetTcloudSesEvent) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{5}
}

func (x *RspGetTcloudSesEvent) GetEvent() *TcloudSesEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

type ReqUpdateTcloudSesEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event *TcloudSesEvent `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
}

func (x *ReqUpdateTcloudSesEvent) Reset() {
	*x = ReqUpdateTcloudSesEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateTcloudSesEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateTcloudSesEvent) ProtoMessage() {}

func (x *ReqUpdateTcloudSesEvent) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateTcloudSesEvent.ProtoReflect.Descriptor instead.
func (*ReqUpdateTcloudSesEvent) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{6}
}

func (x *ReqUpdateTcloudSesEvent) GetEvent() *TcloudSesEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

type ReqGetTcloudSmsStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
}

func (x *ReqGetTcloudSmsStatus) Reset() {
	*x = ReqGetTcloudSmsStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTcloudSmsStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTcloudSmsStatus) ProtoMessage() {}

func (x *ReqGetTcloudSmsStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTcloudSmsStatus.ProtoReflect.Descriptor instead.
func (*ReqGetTcloudSmsStatus) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{7}
}

func (x *ReqGetTcloudSmsStatus) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

type RspGetTcloudSmsStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *TcloudSmsStatus `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RspGetTcloudSmsStatus) Reset() {
	*x = RspGetTcloudSmsStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTcloudSmsStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTcloudSmsStatus) ProtoMessage() {}

func (x *RspGetTcloudSmsStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTcloudSmsStatus.ProtoReflect.Descriptor instead.
func (*RspGetTcloudSmsStatus) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{8}
}

func (x *RspGetTcloudSmsStatus) GetStatus() *TcloudSmsStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

type ReqUpdateTcloudSmsStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Statuses []*TcloudSmsStatus `protobuf:"bytes,1,rep,name=statuses,proto3" json:"statuses,omitempty"`
}

func (x *ReqUpdateTcloudSmsStatus) Reset() {
	*x = ReqUpdateTcloudSmsStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateTcloudSmsStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateTcloudSmsStatus) ProtoMessage() {}

func (x *ReqUpdateTcloudSmsStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateTcloudSmsStatus.ProtoReflect.Descriptor instead.
func (*ReqUpdateTcloudSmsStatus) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{9}
}

func (x *ReqUpdateTcloudSmsStatus) GetStatuses() []*TcloudSmsStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// 短信参数
type ReqSendOneTimePassword_SmsPara struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 手机号码
	PhoneNumber string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// 模板语言
	TemplateLang string `protobuf:"bytes,2,opt,name=template_lang,json=templateLang,proto3" json:"template_lang,omitempty"`
}

func (x *ReqSendOneTimePassword_SmsPara) Reset() {
	*x = ReqSendOneTimePassword_SmsPara{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSendOneTimePassword_SmsPara) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSendOneTimePassword_SmsPara) ProtoMessage() {}

func (x *ReqSendOneTimePassword_SmsPara) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSendOneTimePassword_SmsPara.ProtoReflect.Descriptor instead.
func (*ReqSendOneTimePassword_SmsPara) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ReqSendOneTimePassword_SmsPara) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ReqSendOneTimePassword_SmsPara) GetTemplateLang() string {
	if x != nil {
		return x.TemplateLang
	}
	return ""
}

// 邮件参数
type ReqSendOneTimePassword_EmailPara struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 邮箱地址
	EmailAddress string `protobuf:"bytes,1,opt,name=email_address,json=emailAddress,proto3" json:"email_address,omitempty"`
	// 模板语言
	TemplateLang string `protobuf:"bytes,2,opt,name=template_lang,json=templateLang,proto3" json:"template_lang,omitempty"`
	// 模板
	Template ReqSendOneTimePassword_EmailTemplate `protobuf:"varint,3,opt,name=template,proto3,enum=tanlive.notify.ReqSendOneTimePassword_EmailTemplate" json:"template,omitempty"`
}

func (x *ReqSendOneTimePassword_EmailPara) Reset() {
	*x = ReqSendOneTimePassword_EmailPara{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSendOneTimePassword_EmailPara) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSendOneTimePassword_EmailPara) ProtoMessage() {}

func (x *ReqSendOneTimePassword_EmailPara) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSendOneTimePassword_EmailPara.ProtoReflect.Descriptor instead.
func (*ReqSendOneTimePassword_EmailPara) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ReqSendOneTimePassword_EmailPara) GetEmailAddress() string {
	if x != nil {
		return x.EmailAddress
	}
	return ""
}

func (x *ReqSendOneTimePassword_EmailPara) GetTemplateLang() string {
	if x != nil {
		return x.TemplateLang
	}
	return ""
}

func (x *ReqSendOneTimePassword_EmailPara) GetTemplate() ReqSendOneTimePassword_EmailTemplate {
	if x != nil {
		return x.Template
	}
	return ReqSendOneTimePassword_EMAIL_TEMPLATE_DEFAULT
}

// 一次性密码
type ReqSendOneTimePassword_Otp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 密码
	Password string `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
	// 有效期
	Ttl *durationpb.Duration `protobuf:"bytes,4,opt,name=ttl,proto3" json:"ttl,omitempty"`
}

func (x *ReqSendOneTimePassword_Otp) Reset() {
	*x = ReqSendOneTimePassword_Otp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSendOneTimePassword_Otp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSendOneTimePassword_Otp) ProtoMessage() {}

func (x *ReqSendOneTimePassword_Otp) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSendOneTimePassword_Otp.ProtoReflect.Descriptor instead.
func (*ReqSendOneTimePassword_Otp) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_service_proto_rawDescGZIP(), []int{0, 2}
}

func (x *ReqSendOneTimePassword_Otp) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ReqSendOneTimePassword_Otp) GetTtl() *durationpb.Duration {
	if x != nil {
		return x.Ttl
	}
	return nil
}

var File_tanlive_notify_service_proto protoreflect.FileDescriptor

var file_tanlive_notify_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x1a, 0x1b,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xf5, 0x06, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x4a, 0x0a, 0x03, 0x6f, 0x74,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x6e, 0x64,
	0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x2e,
	0x4f, 0x74, 0x70, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x63, 0x0a, 0x0d, 0x73, 0x6d, 0x73, 0x5f, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52,
	0x65, 0x71, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x2e, 0x53, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x0c, 0x73,
	0x6d, 0x73, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x72, 0x12, 0x69, 0x0a, 0x0f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x6e, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x2e, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x65, 0x72, 0x1a, 0x89, 0x01, 0x0a, 0x07, 0x53, 0x6d, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x12, 0x3f, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0x82, 0x88, 0x27, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x65, 0x31, 0x36, 0x34, 0x8a, 0x88, 0x27, 0x07, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x14,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x3d, 0x7a,
	0x68, 0x20, 0x65, 0x6e, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x61,
	0x6e, 0x67, 0x1a, 0xe0, 0x01, 0x0a, 0x09, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x12, 0x42, 0x0a, 0x0d, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x8a, 0x88, 0x27, 0x07, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27,
	0x14, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x3d,
	0x7a, 0x68, 0x20, 0x65, 0x6e, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c,
	0x61, 0x6e, 0x67, 0x12, 0x50, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x6e,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x2e, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x1a, 0x76, 0x0a, 0x03, 0x4f, 0x74, 0x70, 0x12, 0x34, 0x0a, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08,
	0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x39, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x22, 0x4b, 0x0a,
	0x0d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x16, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45,
	0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x45, 0x41,
	0x4d, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x10, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x72, 0x22, 0x50, 0x0a, 0x16, 0x52, 0x73, 0x70, 0x53, 0x65,
	0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x12, 0x36, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xb7, 0x01, 0x0a, 0x12, 0x52, 0x65,
	0x71, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x42, 0x0a, 0x0d, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x8a, 0x88, 0x27, 0x07, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x3d, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27,
	0x14, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x3d,
	0x7a, 0x68, 0x20, 0x65, 0x6e, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c,
	0x61, 0x6e, 0x67, 0x22, 0x4c, 0x0a, 0x12, 0x52, 0x73, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x36, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x3d, 0x0a, 0x14, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x53, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x07, 0x62, 0x75, 0x6c,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x62, 0x75, 0x6c, 0x6b, 0x49, 0x64,
	0x22, 0x4c, 0x0a, 0x14, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x53, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53,
	0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x5d,
	0x0a, 0x17, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x53, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x05, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x53, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x37, 0x0a,
	0x15, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x03, 0x73, 0x69, 0x64, 0x22, 0x50, 0x0a, 0x15, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74,
	0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2e, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x65, 0x0a, 0x18, 0x52, 0x65, 0x71, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x32,
	0xca, 0x04, 0x0a, 0x0d, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x65, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x6e,
	0x64, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x1a, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x59, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x22, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x53, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x1a,
	0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x5f, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x53, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74,
	0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x24,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e,
	0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x65, 0x73, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x57, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x53, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x65, 0x73,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x62, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x53, 0x6d, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x25, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x73, 0x70, 0x47,
	0x65, 0x74, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x59, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x53, 0x6d, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x40, 0x5a, 0x3e,
	0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_notify_service_proto_rawDescOnce sync.Once
	file_tanlive_notify_service_proto_rawDescData = file_tanlive_notify_service_proto_rawDesc
)

func file_tanlive_notify_service_proto_rawDescGZIP() []byte {
	file_tanlive_notify_service_proto_rawDescOnce.Do(func() {
		file_tanlive_notify_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_notify_service_proto_rawDescData)
	})
	return file_tanlive_notify_service_proto_rawDescData
}

var file_tanlive_notify_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_notify_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_tanlive_notify_service_proto_goTypes = []interface{}{
	(ReqSendOneTimePassword_EmailTemplate)(0), // 0: tanlive.notify.ReqSendOneTimePassword.EmailTemplate
	(*ReqSendOneTimePassword)(nil),            // 1: tanlive.notify.ReqSendOneTimePassword
	(*RspSendOneTimePassword)(nil),            // 2: tanlive.notify.RspSendOneTimePassword
	(*ReqSendActiveEmail)(nil),                // 3: tanlive.notify.ReqSendActiveEmail
	(*RspSendActiveEmail)(nil),                // 4: tanlive.notify.RspSendActiveEmail
	(*ReqGetTcloudSesEvent)(nil),              // 5: tanlive.notify.ReqGetTcloudSesEvent
	(*RspGetTcloudSesEvent)(nil),              // 6: tanlive.notify.RspGetTcloudSesEvent
	(*ReqUpdateTcloudSesEvent)(nil),           // 7: tanlive.notify.ReqUpdateTcloudSesEvent
	(*ReqGetTcloudSmsStatus)(nil),             // 8: tanlive.notify.ReqGetTcloudSmsStatus
	(*RspGetTcloudSmsStatus)(nil),             // 9: tanlive.notify.RspGetTcloudSmsStatus
	(*ReqUpdateTcloudSmsStatus)(nil),          // 10: tanlive.notify.ReqUpdateTcloudSmsStatus
	(*ReqSendOneTimePassword_SmsPara)(nil),    // 11: tanlive.notify.ReqSendOneTimePassword.SmsPara
	(*ReqSendOneTimePassword_EmailPara)(nil),  // 12: tanlive.notify.ReqSendOneTimePassword.EmailPara
	(*ReqSendOneTimePassword_Otp)(nil),        // 13: tanlive.notify.ReqSendOneTimePassword.Otp
	(*DeliveryResult)(nil),                    // 14: tanlive.notify.DeliveryResult
	(*TcloudSesEvent)(nil),                    // 15: tanlive.notify.TcloudSesEvent
	(*TcloudSmsStatus)(nil),                   // 16: tanlive.notify.TcloudSmsStatus
	(*durationpb.Duration)(nil),               // 17: google.protobuf.Duration
	(*emptypb.Empty)(nil),                     // 18: google.protobuf.Empty
}
var file_tanlive_notify_service_proto_depIdxs = []int32{
	13, // 0: tanlive.notify.ReqSendOneTimePassword.otp:type_name -> tanlive.notify.ReqSendOneTimePassword.Otp
	11, // 1: tanlive.notify.ReqSendOneTimePassword.sms_deliverer:type_name -> tanlive.notify.ReqSendOneTimePassword.SmsPara
	12, // 2: tanlive.notify.ReqSendOneTimePassword.email_deliverer:type_name -> tanlive.notify.ReqSendOneTimePassword.EmailPara
	14, // 3: tanlive.notify.RspSendOneTimePassword.result:type_name -> tanlive.notify.DeliveryResult
	14, // 4: tanlive.notify.RspSendActiveEmail.result:type_name -> tanlive.notify.DeliveryResult
	15, // 5: tanlive.notify.RspGetTcloudSesEvent.event:type_name -> tanlive.notify.TcloudSesEvent
	15, // 6: tanlive.notify.ReqUpdateTcloudSesEvent.event:type_name -> tanlive.notify.TcloudSesEvent
	16, // 7: tanlive.notify.RspGetTcloudSmsStatus.status:type_name -> tanlive.notify.TcloudSmsStatus
	16, // 8: tanlive.notify.ReqUpdateTcloudSmsStatus.statuses:type_name -> tanlive.notify.TcloudSmsStatus
	0,  // 9: tanlive.notify.ReqSendOneTimePassword.EmailPara.template:type_name -> tanlive.notify.ReqSendOneTimePassword.EmailTemplate
	17, // 10: tanlive.notify.ReqSendOneTimePassword.Otp.ttl:type_name -> google.protobuf.Duration
	1,  // 11: tanlive.notify.NotifyService.SendOneTimePassword:input_type -> tanlive.notify.ReqSendOneTimePassword
	3,  // 12: tanlive.notify.NotifyService.SendActiveEmail:input_type -> tanlive.notify.ReqSendActiveEmail
	5,  // 13: tanlive.notify.NotifyService.GetTcloudSesEvent:input_type -> tanlive.notify.ReqGetTcloudSesEvent
	7,  // 14: tanlive.notify.NotifyService.UpdateTcloudSesEvent:input_type -> tanlive.notify.ReqUpdateTcloudSesEvent
	8,  // 15: tanlive.notify.NotifyService.GetTcloudSmsStatus:input_type -> tanlive.notify.ReqGetTcloudSmsStatus
	10, // 16: tanlive.notify.NotifyService.UpdateTcloudSmsStatus:input_type -> tanlive.notify.ReqUpdateTcloudSmsStatus
	2,  // 17: tanlive.notify.NotifyService.SendOneTimePassword:output_type -> tanlive.notify.RspSendOneTimePassword
	4,  // 18: tanlive.notify.NotifyService.SendActiveEmail:output_type -> tanlive.notify.RspSendActiveEmail
	6,  // 19: tanlive.notify.NotifyService.GetTcloudSesEvent:output_type -> tanlive.notify.RspGetTcloudSesEvent
	18, // 20: tanlive.notify.NotifyService.UpdateTcloudSesEvent:output_type -> google.protobuf.Empty
	9,  // 21: tanlive.notify.NotifyService.GetTcloudSmsStatus:output_type -> tanlive.notify.RspGetTcloudSmsStatus
	18, // 22: tanlive.notify.NotifyService.UpdateTcloudSmsStatus:output_type -> google.protobuf.Empty
	17, // [17:23] is the sub-list for method output_type
	11, // [11:17] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_tanlive_notify_service_proto_init() }
func file_tanlive_notify_service_proto_init() {
	if File_tanlive_notify_service_proto != nil {
		return
	}
	file_tanlive_notify_notify_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_notify_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSendOneTimePassword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSendOneTimePassword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSendActiveEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSendActiveEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTcloudSesEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTcloudSesEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateTcloudSesEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTcloudSmsStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTcloudSmsStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateTcloudSmsStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSendOneTimePassword_SmsPara); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSendOneTimePassword_EmailPara); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSendOneTimePassword_Otp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tanlive_notify_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ReqSendOneTimePassword_SmsDeliverer)(nil),
		(*ReqSendOneTimePassword_EmailDeliverer)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_notify_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_notify_service_proto_goTypes,
		DependencyIndexes: file_tanlive_notify_service_proto_depIdxs,
		EnumInfos:         file_tanlive_notify_service_proto_enumTypes,
		MessageInfos:      file_tanlive_notify_service_proto_msgTypes,
	}.Build()
	File_tanlive_notify_service_proto = out.File
	file_tanlive_notify_service_proto_rawDesc = nil
	file_tanlive_notify_service_proto_goTypes = nil
	file_tanlive_notify_service_proto_depIdxs = nil
}
