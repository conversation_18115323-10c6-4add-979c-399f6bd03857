// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package notify

import (
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	proto "google.golang.org/protobuf/proto"
)

func (x *TcloudSesEvent) MaskInLog() any {
	if x == nil {
		return (*TcloudSesEvent)(nil)
	}

	y := proto.Clone(x).(*TcloudSesEvent)
	y.Email = mask.Mask(y.Email, "email")
	y.Username = mask.Mask(y.Username, "secret")

	return y
}

func (x *TcloudSesEvent) MaskInRpc() any {
	if x == nil {
		return (*TcloudSesEvent)(nil)
	}

	y := x
	y.Email = mask.Mask(y.Email, "email")
	y.Username = mask.Mask(y.Username, "secret")

	return y
}

func (x *TcloudSesEvent) MaskInBff() any {
	if x == nil {
		return (*TcloudSesEvent)(nil)
	}

	y := x
	y.Email = mask.Mask(y.Email, "email")
	y.Username = mask.Mask(y.Username, "secret")

	return y
}

func (x *TcloudSmsStatus) MaskInLog() any {
	if x == nil {
		return (*TcloudSmsStatus)(nil)
	}

	y := proto.Clone(x).(*TcloudSmsStatus)
	y.Mobile = mask.Mask(y.Mobile, "phone")

	return y
}

func (x *TcloudSmsStatus) MaskInRpc() any {
	if x == nil {
		return (*TcloudSmsStatus)(nil)
	}

	y := x
	y.Mobile = mask.Mask(y.Mobile, "phone")

	return y
}

func (x *TcloudSmsStatus) MaskInBff() any {
	if x == nil {
		return (*TcloudSmsStatus)(nil)
	}

	y := x
	y.Mobile = mask.Mask(y.Mobile, "phone")

	return y
}
