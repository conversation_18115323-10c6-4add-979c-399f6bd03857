syntax = "proto3";

package tanlive.notify;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify";

import "tanlive/notify/notify.proto";
import "tanlive/options.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/duration.proto";

message ReqSendOneTimePassword {
  // 邮件模板
  enum EmailTemplate {
    // 默认
    EMAIL_TEMPLATE_DEFAULT = 0;
    // 团队认证
    EMAIL_TEMPLATE_TEAM_VERIFY = 1;
  }
  // 短信参数
  message SmsPara {
    // 手机号码
    string phone_number = 1 [(validator) = "required,e164", (mask).rule = "phone"];
    // 模板语言
    string template_lang = 2 [(validator) = "required,oneof=zh en"];
  }
  // 邮件参数
  message EmailPara {
    // 邮箱地址
    string email_address = 1 [(validator) = "required,email", (mask).rule = "email"];
    // 模板语言
    string template_lang = 2 [(validator) = "required,oneof=zh en"];
    // 模板
    EmailTemplate template = 3;
  }
  // 一次性密码
  message Otp {
    // 密码
    string password = 1 [(validator) = "required", (mask).rule = "secret"];
    // 有效期
    google.protobuf.Duration ttl = 4 [(validator) = "required"];
  }

  // 一次性密码
  Otp otp = 1 [(validator) = "required"];
  // 递送器
  oneof deliverer {
    // 短信递送
    SmsPara sms_deliverer = 2 [(validator) = "required"];
    // 邮件递送
    EmailPara email_deliverer = 3 [(validator) = "required"];
  }
}

message RspSendOneTimePassword {
  // 发送结果
  DeliveryResult result = 1;
}

message ReqSendActiveEmail {
  // 邮件
  string email_address = 1 [(validator) = "required,email", (mask).rule = "email"];
  // 激活地址
  string url = 2 [(validator) = "required"];
  // 模板语言
  string template_lang = 3 [(validator) = "required,oneof=zh en"];
}

message RspSendActiveEmail {
  // 发送结果
  DeliveryResult result = 1;
}

message ReqGetTcloudSesEvent {
  // SendEmail 接口返回的 MessageId
  string bulk_id = 1 [(validator) = "required"];
}

message RspGetTcloudSesEvent {
  TcloudSesEvent event = 1;
}

message ReqUpdateTcloudSesEvent {
  TcloudSesEvent event = 1 [(validator) = "required"];
}

message ReqGetTcloudSmsStatus {
  string sid = 1 [(validator) = "required"];
}

message RspGetTcloudSmsStatus {
  TcloudSmsStatus status = 1;
}

message ReqUpdateTcloudSmsStatus {
  repeated TcloudSmsStatus statuses = 1 [(validator) = "required"];
}

// Notify服务
service NotifyService {
  // 发送一次性密码
  rpc SendOneTimePassword(ReqSendOneTimePassword) returns (RspSendOneTimePassword);
  // 发送激活邮箱邮件
  rpc SendActiveEmail(ReqSendActiveEmail) returns (RspSendActiveEmail);
  // 查询腾讯云邮件事件
  rpc GetTcloudSesEvent(ReqGetTcloudSesEvent) returns (RspGetTcloudSesEvent);
  // 更新腾讯云邮件事件
  rpc UpdateTcloudSesEvent(ReqUpdateTcloudSesEvent) returns (google.protobuf.Empty);
  // 查询腾讯云短信状态
  rpc GetTcloudSmsStatus(ReqGetTcloudSmsStatus) returns (RspGetTcloudSmsStatus);
  // 更新腾讯云短信状态
  rpc UpdateTcloudSmsStatus(ReqUpdateTcloudSmsStatus) returns (google.protobuf.Empty);
}
