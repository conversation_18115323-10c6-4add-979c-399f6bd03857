// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package notify

import (
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	proto "google.golang.org/protobuf/proto"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Otp":            "required",
		"SmsDeliverer":   "required",
		"EmailDeliverer": "required",
	}, &ReqSendOneTimePassword{})
}

func (x *ReqSendOneTimePassword) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"PhoneNumber":  "required,e164",
		"TemplateLang": "required,oneof=zh en",
	}, &ReqSendOneTimePassword_SmsPara{})
}

func (x *ReqSendOneTimePassword_SmsPara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"EmailAddress": "required,email",
		"TemplateLang": "required,oneof=zh en",
	}, &ReqSendOneTimePassword_EmailPara{})
}

func (x *ReqSendOneTimePassword_EmailPara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Password": "required",
		"Ttl":      "required",
	}, &ReqSendOneTimePassword_Otp{})
}

func (x *ReqSendOneTimePassword_Otp) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"EmailAddress": "required,email",
		"Url":          "required",
		"TemplateLang": "required,oneof=zh en",
	}, &ReqSendActiveEmail{})
}

func (x *ReqSendActiveEmail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"BulkId": "required",
	}, &ReqGetTcloudSesEvent{})
}

func (x *ReqGetTcloudSesEvent) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Event": "required",
	}, &ReqUpdateTcloudSesEvent{})
}

func (x *ReqUpdateTcloudSesEvent) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Sid": "required",
	}, &ReqGetTcloudSmsStatus{})
}

func (x *ReqGetTcloudSmsStatus) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Statuses": "required",
	}, &ReqUpdateTcloudSmsStatus{})
}

func (x *ReqUpdateTcloudSmsStatus) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqSendOneTimePassword) MaskInLog() any {
	if x == nil {
		return (*ReqSendOneTimePassword)(nil)
	}

	y := proto.Clone(x).(*ReqSendOneTimePassword)
	if v, ok := any(y.Otp).(interface{ MaskInLog() any }); ok {
		y.Otp = v.MaskInLog().(*ReqSendOneTimePassword_Otp)
	}

	switch v := y.Deliverer.(type) {
	case *ReqSendOneTimePassword_SmsDeliverer:
		if vv, ok := any(v.SmsDeliverer).(interface{ MaskInLog() any }); ok {
			v.SmsDeliverer = vv.MaskInLog().(*ReqSendOneTimePassword_SmsPara)
		}
	case *ReqSendOneTimePassword_EmailDeliverer:
		if vv, ok := any(v.EmailDeliverer).(interface{ MaskInLog() any }); ok {
			v.EmailDeliverer = vv.MaskInLog().(*ReqSendOneTimePassword_EmailPara)
		}
	}

	return y
}

func (x *ReqSendOneTimePassword) MaskInRpc() any {
	if x == nil {
		return (*ReqSendOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.Otp).(interface{ MaskInRpc() any }); ok {
		y.Otp = v.MaskInRpc().(*ReqSendOneTimePassword_Otp)
	}

	switch v := y.Deliverer.(type) {
	case *ReqSendOneTimePassword_SmsDeliverer:
		if vv, ok := any(v.SmsDeliverer).(interface{ MaskInRpc() any }); ok {
			v.SmsDeliverer = vv.MaskInRpc().(*ReqSendOneTimePassword_SmsPara)
		}
	case *ReqSendOneTimePassword_EmailDeliverer:
		if vv, ok := any(v.EmailDeliverer).(interface{ MaskInRpc() any }); ok {
			v.EmailDeliverer = vv.MaskInRpc().(*ReqSendOneTimePassword_EmailPara)
		}
	}

	return y
}

func (x *ReqSendOneTimePassword) MaskInBff() any {
	if x == nil {
		return (*ReqSendOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.Otp).(interface{ MaskInBff() any }); ok {
		y.Otp = v.MaskInBff().(*ReqSendOneTimePassword_Otp)
	}

	switch v := y.Deliverer.(type) {
	case *ReqSendOneTimePassword_SmsDeliverer:
		if vv, ok := any(v.SmsDeliverer).(interface{ MaskInBff() any }); ok {
			v.SmsDeliverer = vv.MaskInBff().(*ReqSendOneTimePassword_SmsPara)
		}
	case *ReqSendOneTimePassword_EmailDeliverer:
		if vv, ok := any(v.EmailDeliverer).(interface{ MaskInBff() any }); ok {
			v.EmailDeliverer = vv.MaskInBff().(*ReqSendOneTimePassword_EmailPara)
		}
	}

	return y
}

func (x *ReqSendOneTimePassword_SmsPara) MaskInLog() any {
	if x == nil {
		return (*ReqSendOneTimePassword_SmsPara)(nil)
	}

	y := proto.Clone(x).(*ReqSendOneTimePassword_SmsPara)
	y.PhoneNumber = mask.Mask(y.PhoneNumber, "phone")

	return y
}

func (x *ReqSendOneTimePassword_SmsPara) MaskInRpc() any {
	if x == nil {
		return (*ReqSendOneTimePassword_SmsPara)(nil)
	}

	y := x
	y.PhoneNumber = mask.Mask(y.PhoneNumber, "phone")

	return y
}

func (x *ReqSendOneTimePassword_SmsPara) MaskInBff() any {
	if x == nil {
		return (*ReqSendOneTimePassword_SmsPara)(nil)
	}

	y := x
	y.PhoneNumber = mask.Mask(y.PhoneNumber, "phone")

	return y
}

func (x *ReqSendOneTimePassword_EmailPara) MaskInLog() any {
	if x == nil {
		return (*ReqSendOneTimePassword_EmailPara)(nil)
	}

	y := proto.Clone(x).(*ReqSendOneTimePassword_EmailPara)
	y.EmailAddress = mask.Mask(y.EmailAddress, "email")

	return y
}

func (x *ReqSendOneTimePassword_EmailPara) MaskInRpc() any {
	if x == nil {
		return (*ReqSendOneTimePassword_EmailPara)(nil)
	}

	y := x
	y.EmailAddress = mask.Mask(y.EmailAddress, "email")

	return y
}

func (x *ReqSendOneTimePassword_EmailPara) MaskInBff() any {
	if x == nil {
		return (*ReqSendOneTimePassword_EmailPara)(nil)
	}

	y := x
	y.EmailAddress = mask.Mask(y.EmailAddress, "email")

	return y
}

func (x *ReqSendOneTimePassword_Otp) MaskInLog() any {
	if x == nil {
		return (*ReqSendOneTimePassword_Otp)(nil)
	}

	y := proto.Clone(x).(*ReqSendOneTimePassword_Otp)
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.Ttl).(interface{ MaskInLog() any }); ok {
		y.Ttl = v.MaskInLog().(*durationpb.Duration)
	}

	return y
}

func (x *ReqSendOneTimePassword_Otp) MaskInRpc() any {
	if x == nil {
		return (*ReqSendOneTimePassword_Otp)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.Ttl).(interface{ MaskInRpc() any }); ok {
		y.Ttl = v.MaskInRpc().(*durationpb.Duration)
	}

	return y
}

func (x *ReqSendOneTimePassword_Otp) MaskInBff() any {
	if x == nil {
		return (*ReqSendOneTimePassword_Otp)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.Ttl).(interface{ MaskInBff() any }); ok {
		y.Ttl = v.MaskInBff().(*durationpb.Duration)
	}

	return y
}

func (x *RspSendOneTimePassword) MaskInLog() any {
	if x == nil {
		return (*RspSendOneTimePassword)(nil)
	}

	y := proto.Clone(x).(*RspSendOneTimePassword)
	if v, ok := any(y.Result).(interface{ MaskInLog() any }); ok {
		y.Result = v.MaskInLog().(*DeliveryResult)
	}

	return y
}

func (x *RspSendOneTimePassword) MaskInRpc() any {
	if x == nil {
		return (*RspSendOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.Result).(interface{ MaskInRpc() any }); ok {
		y.Result = v.MaskInRpc().(*DeliveryResult)
	}

	return y
}

func (x *RspSendOneTimePassword) MaskInBff() any {
	if x == nil {
		return (*RspSendOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.Result).(interface{ MaskInBff() any }); ok {
		y.Result = v.MaskInBff().(*DeliveryResult)
	}

	return y
}

func (x *ReqSendActiveEmail) MaskInLog() any {
	if x == nil {
		return (*ReqSendActiveEmail)(nil)
	}

	y := proto.Clone(x).(*ReqSendActiveEmail)
	y.EmailAddress = mask.Mask(y.EmailAddress, "email")

	return y
}

func (x *ReqSendActiveEmail) MaskInRpc() any {
	if x == nil {
		return (*ReqSendActiveEmail)(nil)
	}

	y := x
	y.EmailAddress = mask.Mask(y.EmailAddress, "email")

	return y
}

func (x *ReqSendActiveEmail) MaskInBff() any {
	if x == nil {
		return (*ReqSendActiveEmail)(nil)
	}

	y := x
	y.EmailAddress = mask.Mask(y.EmailAddress, "email")

	return y
}

func (x *RspSendActiveEmail) MaskInLog() any {
	if x == nil {
		return (*RspSendActiveEmail)(nil)
	}

	y := proto.Clone(x).(*RspSendActiveEmail)
	if v, ok := any(y.Result).(interface{ MaskInLog() any }); ok {
		y.Result = v.MaskInLog().(*DeliveryResult)
	}

	return y
}

func (x *RspSendActiveEmail) MaskInRpc() any {
	if x == nil {
		return (*RspSendActiveEmail)(nil)
	}

	y := x
	if v, ok := any(y.Result).(interface{ MaskInRpc() any }); ok {
		y.Result = v.MaskInRpc().(*DeliveryResult)
	}

	return y
}

func (x *RspSendActiveEmail) MaskInBff() any {
	if x == nil {
		return (*RspSendActiveEmail)(nil)
	}

	y := x
	if v, ok := any(y.Result).(interface{ MaskInBff() any }); ok {
		y.Result = v.MaskInBff().(*DeliveryResult)
	}

	return y
}

func (x *RspGetTcloudSesEvent) MaskInLog() any {
	if x == nil {
		return (*RspGetTcloudSesEvent)(nil)
	}

	y := proto.Clone(x).(*RspGetTcloudSesEvent)
	if v, ok := any(y.Event).(interface{ MaskInLog() any }); ok {
		y.Event = v.MaskInLog().(*TcloudSesEvent)
	}

	return y
}

func (x *RspGetTcloudSesEvent) MaskInRpc() any {
	if x == nil {
		return (*RspGetTcloudSesEvent)(nil)
	}

	y := x
	if v, ok := any(y.Event).(interface{ MaskInRpc() any }); ok {
		y.Event = v.MaskInRpc().(*TcloudSesEvent)
	}

	return y
}

func (x *RspGetTcloudSesEvent) MaskInBff() any {
	if x == nil {
		return (*RspGetTcloudSesEvent)(nil)
	}

	y := x
	if v, ok := any(y.Event).(interface{ MaskInBff() any }); ok {
		y.Event = v.MaskInBff().(*TcloudSesEvent)
	}

	return y
}

func (x *ReqUpdateTcloudSesEvent) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateTcloudSesEvent)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateTcloudSesEvent)
	if v, ok := any(y.Event).(interface{ MaskInLog() any }); ok {
		y.Event = v.MaskInLog().(*TcloudSesEvent)
	}

	return y
}

func (x *ReqUpdateTcloudSesEvent) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateTcloudSesEvent)(nil)
	}

	y := x
	if v, ok := any(y.Event).(interface{ MaskInRpc() any }); ok {
		y.Event = v.MaskInRpc().(*TcloudSesEvent)
	}

	return y
}

func (x *ReqUpdateTcloudSesEvent) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateTcloudSesEvent)(nil)
	}

	y := x
	if v, ok := any(y.Event).(interface{ MaskInBff() any }); ok {
		y.Event = v.MaskInBff().(*TcloudSesEvent)
	}

	return y
}

func (x *RspGetTcloudSmsStatus) MaskInLog() any {
	if x == nil {
		return (*RspGetTcloudSmsStatus)(nil)
	}

	y := proto.Clone(x).(*RspGetTcloudSmsStatus)
	if v, ok := any(y.Status).(interface{ MaskInLog() any }); ok {
		y.Status = v.MaskInLog().(*TcloudSmsStatus)
	}

	return y
}

func (x *RspGetTcloudSmsStatus) MaskInRpc() any {
	if x == nil {
		return (*RspGetTcloudSmsStatus)(nil)
	}

	y := x
	if v, ok := any(y.Status).(interface{ MaskInRpc() any }); ok {
		y.Status = v.MaskInRpc().(*TcloudSmsStatus)
	}

	return y
}

func (x *RspGetTcloudSmsStatus) MaskInBff() any {
	if x == nil {
		return (*RspGetTcloudSmsStatus)(nil)
	}

	y := x
	if v, ok := any(y.Status).(interface{ MaskInBff() any }); ok {
		y.Status = v.MaskInBff().(*TcloudSmsStatus)
	}

	return y
}

func (x *ReqUpdateTcloudSmsStatus) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateTcloudSmsStatus)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateTcloudSmsStatus)
	for k, v := range y.Statuses {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Statuses[k] = vv.MaskInLog().(*TcloudSmsStatus)
		}
	}

	return y
}

func (x *ReqUpdateTcloudSmsStatus) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateTcloudSmsStatus)(nil)
	}

	y := x
	for k, v := range y.Statuses {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Statuses[k] = vv.MaskInRpc().(*TcloudSmsStatus)
		}
	}

	return y
}

func (x *ReqUpdateTcloudSmsStatus) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateTcloudSmsStatus)(nil)
	}

	y := x
	for k, v := range y.Statuses {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Statuses[k] = vv.MaskInBff().(*TcloudSmsStatus)
		}
	}

	return y
}

func (x *ReqSendOneTimePassword) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Otp).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	switch oneof := x.Deliverer.(type) {
	case *ReqSendOneTimePassword_SmsDeliverer:
		if sanitizer, ok := any(oneof.SmsDeliverer).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Deliverer = oneof
	case *ReqSendOneTimePassword_EmailDeliverer:
		if sanitizer, ok := any(oneof.EmailDeliverer).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Deliverer = oneof
	}
}

func (x *ReqSendOneTimePassword_Otp) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Ttl).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSendOneTimePassword) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Result).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSendActiveEmail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Result).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetTcloudSesEvent) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Event).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpdateTcloudSesEvent) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Event).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetTcloudSmsStatus) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Status).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpdateTcloudSmsStatus) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Statuses {
		if sanitizer, ok := any(x.Statuses[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
