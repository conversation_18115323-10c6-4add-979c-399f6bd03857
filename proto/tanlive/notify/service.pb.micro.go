// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/notify/service.proto

package notify

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for NotifyService service

func NewNotifyServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for NotifyService service

type NotifyService interface {
	// 发送一次性密码
	SendOneTimePassword(ctx context.Context, in *ReqSendOneTimePassword, opts ...client.CallOption) (*RspSendOneTimePassword, error)
	// 发送激活邮箱邮件
	SendActiveEmail(ctx context.Context, in *ReqSendActiveEmail, opts ...client.CallOption) (*RspSendActiveEmail, error)
	// 查询腾讯云邮件事件
	GetTcloudSesEvent(ctx context.Context, in *ReqGetTcloudSesEvent, opts ...client.CallOption) (*RspGetTcloudSesEvent, error)
	// 更新腾讯云邮件事件
	UpdateTcloudSesEvent(ctx context.Context, in *ReqUpdateTcloudSesEvent, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询腾讯云短信状态
	GetTcloudSmsStatus(ctx context.Context, in *ReqGetTcloudSmsStatus, opts ...client.CallOption) (*RspGetTcloudSmsStatus, error)
	// 更新腾讯云短信状态
	UpdateTcloudSmsStatus(ctx context.Context, in *ReqUpdateTcloudSmsStatus, opts ...client.CallOption) (*emptypb.Empty, error)
}

type notifyService struct {
	c    client.Client
	name string
}

func NewNotifyService(name string, c client.Client) NotifyService {
	return &notifyService{
		c:    c,
		name: name,
	}
}

func (c *notifyService) SendOneTimePassword(ctx context.Context, in *ReqSendOneTimePassword, opts ...client.CallOption) (*RspSendOneTimePassword, error) {
	req := c.c.NewRequest(c.name, "NotifyService.SendOneTimePassword", in)
	out := new(RspSendOneTimePassword)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyService) SendActiveEmail(ctx context.Context, in *ReqSendActiveEmail, opts ...client.CallOption) (*RspSendActiveEmail, error) {
	req := c.c.NewRequest(c.name, "NotifyService.SendActiveEmail", in)
	out := new(RspSendActiveEmail)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyService) GetTcloudSesEvent(ctx context.Context, in *ReqGetTcloudSesEvent, opts ...client.CallOption) (*RspGetTcloudSesEvent, error) {
	req := c.c.NewRequest(c.name, "NotifyService.GetTcloudSesEvent", in)
	out := new(RspGetTcloudSesEvent)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyService) UpdateTcloudSesEvent(ctx context.Context, in *ReqUpdateTcloudSesEvent, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "NotifyService.UpdateTcloudSesEvent", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyService) GetTcloudSmsStatus(ctx context.Context, in *ReqGetTcloudSmsStatus, opts ...client.CallOption) (*RspGetTcloudSmsStatus, error) {
	req := c.c.NewRequest(c.name, "NotifyService.GetTcloudSmsStatus", in)
	out := new(RspGetTcloudSmsStatus)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyService) UpdateTcloudSmsStatus(ctx context.Context, in *ReqUpdateTcloudSmsStatus, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "NotifyService.UpdateTcloudSmsStatus", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for NotifyService service

type NotifyServiceHandler interface {
	// 发送一次性密码
	SendOneTimePassword(context.Context, *ReqSendOneTimePassword, *RspSendOneTimePassword) error
	// 发送激活邮箱邮件
	SendActiveEmail(context.Context, *ReqSendActiveEmail, *RspSendActiveEmail) error
	// 查询腾讯云邮件事件
	GetTcloudSesEvent(context.Context, *ReqGetTcloudSesEvent, *RspGetTcloudSesEvent) error
	// 更新腾讯云邮件事件
	UpdateTcloudSesEvent(context.Context, *ReqUpdateTcloudSesEvent, *emptypb.Empty) error
	// 查询腾讯云短信状态
	GetTcloudSmsStatus(context.Context, *ReqGetTcloudSmsStatus, *RspGetTcloudSmsStatus) error
	// 更新腾讯云短信状态
	UpdateTcloudSmsStatus(context.Context, *ReqUpdateTcloudSmsStatus, *emptypb.Empty) error
}

func RegisterNotifyServiceHandler(s server.Server, hdlr NotifyServiceHandler, opts ...server.HandlerOption) error {
	type notifyService interface {
		SendOneTimePassword(ctx context.Context, in *ReqSendOneTimePassword, out *RspSendOneTimePassword) error
		SendActiveEmail(ctx context.Context, in *ReqSendActiveEmail, out *RspSendActiveEmail) error
		GetTcloudSesEvent(ctx context.Context, in *ReqGetTcloudSesEvent, out *RspGetTcloudSesEvent) error
		UpdateTcloudSesEvent(ctx context.Context, in *ReqUpdateTcloudSesEvent, out *emptypb.Empty) error
		GetTcloudSmsStatus(ctx context.Context, in *ReqGetTcloudSmsStatus, out *RspGetTcloudSmsStatus) error
		UpdateTcloudSmsStatus(ctx context.Context, in *ReqUpdateTcloudSmsStatus, out *emptypb.Empty) error
	}
	type NotifyService struct {
		notifyService
	}
	h := &notifyServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&NotifyService{h}, opts...))
}

type notifyServiceHandler struct {
	NotifyServiceHandler
}

func (h *notifyServiceHandler) SendOneTimePassword(ctx context.Context, in *ReqSendOneTimePassword, out *RspSendOneTimePassword) error {
	return h.NotifyServiceHandler.SendOneTimePassword(ctx, in, out)
}

func (h *notifyServiceHandler) SendActiveEmail(ctx context.Context, in *ReqSendActiveEmail, out *RspSendActiveEmail) error {
	return h.NotifyServiceHandler.SendActiveEmail(ctx, in, out)
}

func (h *notifyServiceHandler) GetTcloudSesEvent(ctx context.Context, in *ReqGetTcloudSesEvent, out *RspGetTcloudSesEvent) error {
	return h.NotifyServiceHandler.GetTcloudSesEvent(ctx, in, out)
}

func (h *notifyServiceHandler) UpdateTcloudSesEvent(ctx context.Context, in *ReqUpdateTcloudSesEvent, out *emptypb.Empty) error {
	return h.NotifyServiceHandler.UpdateTcloudSesEvent(ctx, in, out)
}

func (h *notifyServiceHandler) GetTcloudSmsStatus(ctx context.Context, in *ReqGetTcloudSmsStatus, out *RspGetTcloudSmsStatus) error {
	return h.NotifyServiceHandler.GetTcloudSmsStatus(ctx, in, out)
}

func (h *notifyServiceHandler) UpdateTcloudSmsStatus(ctx context.Context, in *ReqUpdateTcloudSmsStatus, out *emptypb.Empty) error {
	return h.NotifyServiceHandler.UpdateTcloudSmsStatus(ctx, in, out)
}
