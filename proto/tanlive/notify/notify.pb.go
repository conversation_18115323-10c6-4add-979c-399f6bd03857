// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/notify/notify.proto

package notify

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 递送类型
type DeliveryKind int32

const (
	DeliveryKind_DELIVERY_KIND_UNSPECIFIED DeliveryKind = 0
	// 短信
	DeliveryKind_DELIVERY_KIND_SMS DeliveryKind = 1
	// 邮箱
	DeliveryKind_DELIVERY_KIND_EMAIL DeliveryKind = 2
)

// Enum value maps for DeliveryKind.
var (
	DeliveryKind_name = map[int32]string{
		0: "DELIVERY_KIND_UNSPECIFIED",
		1: "DELIVERY_KIND_SMS",
		2: "DELIVERY_KIND_EMAIL",
	}
	DeliveryKind_value = map[string]int32{
		"DELIVERY_KIND_UNSPECIFIED": 0,
		"DELIVERY_KIND_SMS":         1,
		"DELIVERY_KIND_EMAIL":       2,
	}
)

func (x DeliveryKind) Enum() *DeliveryKind {
	p := new(DeliveryKind)
	*p = x
	return p
}

func (x DeliveryKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeliveryKind) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_notify_notify_proto_enumTypes[0].Descriptor()
}

func (DeliveryKind) Type() protoreflect.EnumType {
	return &file_tanlive_notify_notify_proto_enumTypes[0]
}

func (x DeliveryKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeliveryKind.Descriptor instead.
func (DeliveryKind) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_notify_notify_proto_rawDescGZIP(), []int{0}
}

// 递送结果
type DeliveryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发送验证码的任务ID
	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// SDK错误码
	SdkError string `protobuf:"bytes,2,opt,name=sdk_error,json=sdkError,proto3" json:"sdk_error,omitempty"`
}

func (x *DeliveryResult) Reset() {
	*x = DeliveryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_notify_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryResult) ProtoMessage() {}

func (x *DeliveryResult) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_notify_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryResult.ProtoReflect.Descriptor instead.
func (*DeliveryResult) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_notify_proto_rawDescGZIP(), []int{0}
}

func (x *DeliveryResult) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *DeliveryResult) GetSdkError() string {
	if x != nil {
		return x.SdkError
	}
	return ""
}

// 腾讯云邮件推送事件
type TcloudSesEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event      string `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
	Email      string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Link       string `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	BulkId     string `protobuf:"bytes,4,opt,name=bulk_id,json=bulkId,proto3" json:"bulk_id,omitempty"`
	Timestamp  int32  `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Reason     string `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	BounceType string `protobuf:"bytes,7,opt,name=bounce_type,json=bounceType,proto3" json:"bounce_type,omitempty"`
	Username   string `protobuf:"bytes,8,opt,name=username,proto3" json:"username,omitempty"`
	From       string `protobuf:"bytes,9,opt,name=from,proto3" json:"from,omitempty"`
	FromDomain string `protobuf:"bytes,10,opt,name=from_domain,json=fromDomain,proto3" json:"from_domain,omitempty"`
	TemplateId int32  `protobuf:"varint,11,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	Useragent  string `protobuf:"bytes,12,opt,name=useragent,proto3" json:"useragent,omitempty"`
}

func (x *TcloudSesEvent) Reset() {
	*x = TcloudSesEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_notify_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TcloudSesEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TcloudSesEvent) ProtoMessage() {}

func (x *TcloudSesEvent) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_notify_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TcloudSesEvent.ProtoReflect.Descriptor instead.
func (*TcloudSesEvent) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_notify_proto_rawDescGZIP(), []int{1}
}

func (x *TcloudSesEvent) GetEvent() string {
	if x != nil {
		return x.Event
	}
	return ""
}

func (x *TcloudSesEvent) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *TcloudSesEvent) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *TcloudSesEvent) GetBulkId() string {
	if x != nil {
		return x.BulkId
	}
	return ""
}

func (x *TcloudSesEvent) GetTimestamp() int32 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *TcloudSesEvent) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TcloudSesEvent) GetBounceType() string {
	if x != nil {
		return x.BounceType
	}
	return ""
}

func (x *TcloudSesEvent) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *TcloudSesEvent) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *TcloudSesEvent) GetFromDomain() string {
	if x != nil {
		return x.FromDomain
	}
	return ""
}

func (x *TcloudSesEvent) GetTemplateId() int32 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *TcloudSesEvent) GetUseragent() string {
	if x != nil {
		return x.Useragent
	}
	return ""
}

// 腾讯云短信状态
type TcloudSmsStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserReceiveTime string `protobuf:"bytes,1,opt,name=user_receive_time,json=userReceiveTime,proto3" json:"user_receive_time,omitempty"`
	Nationcode      string `protobuf:"bytes,2,opt,name=nationcode,proto3" json:"nationcode,omitempty"`
	Mobile          string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"`
	ReportStatus    string `protobuf:"bytes,4,opt,name=report_status,json=reportStatus,proto3" json:"report_status,omitempty"`
	Errmsg          string `protobuf:"bytes,5,opt,name=errmsg,proto3" json:"errmsg,omitempty"`
	Description     string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	Sid             string `protobuf:"bytes,7,opt,name=sid,proto3" json:"sid,omitempty"`
	Ext             string `protobuf:"bytes,8,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *TcloudSmsStatus) Reset() {
	*x = TcloudSmsStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_notify_notify_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TcloudSmsStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TcloudSmsStatus) ProtoMessage() {}

func (x *TcloudSmsStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_notify_notify_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TcloudSmsStatus.ProtoReflect.Descriptor instead.
func (*TcloudSmsStatus) Descriptor() ([]byte, []int) {
	return file_tanlive_notify_notify_proto_rawDescGZIP(), []int{2}
}

func (x *TcloudSmsStatus) GetUserReceiveTime() string {
	if x != nil {
		return x.UserReceiveTime
	}
	return ""
}

func (x *TcloudSmsStatus) GetNationcode() string {
	if x != nil {
		return x.Nationcode
	}
	return ""
}

func (x *TcloudSmsStatus) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *TcloudSmsStatus) GetReportStatus() string {
	if x != nil {
		return x.ReportStatus
	}
	return ""
}

func (x *TcloudSmsStatus) GetErrmsg() string {
	if x != nil {
		return x.Errmsg
	}
	return ""
}

func (x *TcloudSmsStatus) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TcloudSmsStatus) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *TcloudSmsStatus) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

var File_tanlive_notify_notify_proto protoreflect.FileDescriptor

var file_tanlive_notify_notify_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x1a, 0x15, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x46, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0xeb, 0x02, 0x0a,
	0x0e, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x8a, 0x88, 0x27, 0x07, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x17, 0x0a, 0x07,
	0x62, 0x75, 0x6c, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x75, 0x6c, 0x6b, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x6f, 0x75, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c,
	0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x22, 0x85, 0x02, 0x0a, 0x0f, 0x54,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x6d, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a,
	0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x8a, 0x88, 0x27, 0x07,
	0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6d, 0x73, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6d, 0x73, 0x67, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65,
	0x78, 0x74, 0x2a, 0x5d, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x4b, 0x69,
	0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x4b,
	0x49, 0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x4c, 0x49,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10,
	0x02, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65,
	0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_notify_notify_proto_rawDescOnce sync.Once
	file_tanlive_notify_notify_proto_rawDescData = file_tanlive_notify_notify_proto_rawDesc
)

func file_tanlive_notify_notify_proto_rawDescGZIP() []byte {
	file_tanlive_notify_notify_proto_rawDescOnce.Do(func() {
		file_tanlive_notify_notify_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_notify_notify_proto_rawDescData)
	})
	return file_tanlive_notify_notify_proto_rawDescData
}

var file_tanlive_notify_notify_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_notify_notify_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_tanlive_notify_notify_proto_goTypes = []interface{}{
	(DeliveryKind)(0),       // 0: tanlive.notify.DeliveryKind
	(*DeliveryResult)(nil),  // 1: tanlive.notify.DeliveryResult
	(*TcloudSesEvent)(nil),  // 2: tanlive.notify.TcloudSesEvent
	(*TcloudSmsStatus)(nil), // 3: tanlive.notify.TcloudSmsStatus
}
var file_tanlive_notify_notify_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_notify_notify_proto_init() }
func file_tanlive_notify_notify_proto_init() {
	if File_tanlive_notify_notify_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_notify_notify_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_notify_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TcloudSesEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_notify_notify_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TcloudSmsStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_notify_notify_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_notify_notify_proto_goTypes,
		DependencyIndexes: file_tanlive_notify_notify_proto_depIdxs,
		EnumInfos:         file_tanlive_notify_notify_proto_enumTypes,
		MessageInfos:      file_tanlive_notify_notify_proto_msgTypes,
	}.Build()
	File_tanlive_notify_notify_proto = out.File
	file_tanlive_notify_notify_proto_rawDesc = nil
	file_tanlive_notify_notify_proto_goTypes = nil
	file_tanlive_notify_notify_proto_depIdxs = nil
}
