syntax = "proto3";

package tanlive.bff_mgmt.search;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/search";

import "google/api/annotations.proto";
import "tanlive/bff-mgmt/search/search.proto";
import "tanlive/options.proto";
import "google/protobuf/empty.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";


service SearchBff {
  option (tanlive.bff) = true;
  // 修改团队-图谱搜索筛选项
  rpc ModifyTeamAtlasSearchOption(ReqModifyTeamAtlasSearchOption) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/search/modify_team_atlas_search_option"
      body: "*"
    };
  };

  // 查询团队-图谱搜索筛选项
  rpc DescribeTeamAtlasSearchOptions(ReqDescribeTeamAtlasSearchOptions) returns (RspDescribeTeamAtlasSearchOptions){
    option (google.api.http) = {
      post: "/search/describe_team_atlas_search_option"
      body: "*"
    };
  };

  // 修改产品-图谱搜索筛选项
  rpc ModifyProductAtlasSearchOption(ReqModifyTeamAtlasSearchOption) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/search/modify_product_atlas_search_option"
      body: "*"
    };
  };

  // 查询产品-图谱搜索筛选项
  rpc DescribeProductAtlasSearchOptions(ReqDescribeTeamAtlasSearchOptions) returns (RspDescribeTeamAtlasSearchOptions){
    option (google.api.http) = {
      post: "/search/describe_product_atlas_search_option"
      body: "*"
    };
  };

  // 修改搜索提示语
  rpc ModifySearchPrompt(ReqModifySearchPrompt) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/search/modify_search_prompt"
      body: "*"
    };
  };

  // 查询修改搜索提示语
  rpc DescribeSearchPrompts(ReqDescribeSearchPrompts) returns (RspDescribeSearchPrompts){
    option (google.api.http) = {
      post: "/search/describe_search_prompt"
      body: "*"
    };
  };
}

message ReqModifyTeamAtlasSearchOption{
  SearchOption option = 1 [(tanlive.validator) = "required"];
  base.CrudType operation = 2 [(tanlive.validator) = "required"];
}

message ReqDescribeTeamAtlasSearchOptions{
  uint32 offset = 1;
  uint32 limit = 2;
  message Filter{
    // 语言类型zh、en
    string language = 1 [(tanlive.validator) = "oneof=en zh"];
    base.DisableState status = 2;
  }
  Filter filter = 3;
  repeated string order_by = 4;
}

message RspDescribeTeamAtlasSearchOptions{
  message TeamSearchOption{
    SearchOption option = 1;
    uint32 atlas_bind_num = 2;
    string atlas_name = 3;
    bool atlas_deleted = 4;
    base.UgcState atlas_state = 5;
    uint64 atlas_draft_id = 6;
  }
  repeated TeamSearchOption options = 1;
  uint32 total_count = 2;
}

message ReqModifySearchPrompt{
  int64 id = 4 [(tanlive.validator) = "required"];
  string content = 5; //可以为空，代表将内容置空
}

message ReqDescribeSearchPrompts{
  // 语言类型zh、en
  string language = 1;
}

message RspDescribeSearchPrompts{
  repeated SearchPrompt prompts = 1;
}