syntax = "proto3";

package tanlive.bff_mgmt.search;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/search";

import "tanlive/base/base.proto";

// 搜索筛选项
message SearchOption{
  string language = 3;
  string name = 4;
  int32 weight = 5;
  int64 refer_id = 6;
  base.DisableState status = 7;
  int64 id = 8;
}

// 搜索提示语
message SearchPrompt{
  // 搜索对应的模块类型
  string target_type = 1;
  // 搜索对应的筛选项类型
  string refer_type = 2;
  string language = 3;
  int64 id = 4;
  string content = 5;
}