// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/search/search.proto

package search

import (
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 搜索筛选项
type SearchOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language string            `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	Name     string            `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Weight   int32             `protobuf:"varint,5,opt,name=weight,proto3" json:"weight,omitempty"`
	ReferId  int64             `protobuf:"varint,6,opt,name=refer_id,json=referId,proto3" json:"refer_id,omitempty"`
	Status   base.DisableState `protobuf:"varint,7,opt,name=status,proto3,enum=tanlive.base.DisableState" json:"status,omitempty"`
	Id       int64             `protobuf:"varint,8,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SearchOption) Reset() {
	*x = SearchOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_search_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchOption) ProtoMessage() {}

func (x *SearchOption) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_search_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchOption.ProtoReflect.Descriptor instead.
func (*SearchOption) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_search_proto_rawDescGZIP(), []int{0}
}

func (x *SearchOption) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *SearchOption) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchOption) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *SearchOption) GetReferId() int64 {
	if x != nil {
		return x.ReferId
	}
	return 0
}

func (x *SearchOption) GetStatus() base.DisableState {
	if x != nil {
		return x.Status
	}
	return base.DisableState(0)
}

func (x *SearchOption) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 搜索提示语
type SearchPrompt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索对应的模块类型
	TargetType string `protobuf:"bytes,1,opt,name=target_type,json=targetType,proto3" json:"target_type,omitempty"`
	// 搜索对应的筛选项类型
	ReferType string `protobuf:"bytes,2,opt,name=refer_type,json=referType,proto3" json:"refer_type,omitempty"`
	Language  string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	Id        int64  `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	Content   string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *SearchPrompt) Reset() {
	*x = SearchPrompt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_search_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPrompt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPrompt) ProtoMessage() {}

func (x *SearchPrompt) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_search_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPrompt.ProtoReflect.Descriptor instead.
func (*SearchPrompt) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_search_proto_rawDescGZIP(), []int{1}
}

func (x *SearchPrompt) GetTargetType() string {
	if x != nil {
		return x.TargetType
	}
	return ""
}

func (x *SearchPrompt) GetReferType() string {
	if x != nil {
		return x.ReferType
	}
	return ""
}

func (x *SearchPrompt) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *SearchPrompt) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchPrompt) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

var File_tanlive_bff_mgmt_search_search_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_search_search_proto_rawDesc = []byte{
	0x0a, 0x24, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a,
	0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x01, 0x0a, 0x0c, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x65, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x94, 0x01, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x41, 0x5a, 0x3f, 0x65, 0x2e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d,
	0x67, 0x6d, 0x74, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_search_search_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_search_search_proto_rawDescData = file_tanlive_bff_mgmt_search_search_proto_rawDesc
)

func file_tanlive_bff_mgmt_search_search_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_search_search_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_search_search_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_search_search_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_search_search_proto_rawDescData
}

var file_tanlive_bff_mgmt_search_search_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tanlive_bff_mgmt_search_search_proto_goTypes = []interface{}{
	(*SearchOption)(nil),   // 0: tanlive.bff_mgmt.search.SearchOption
	(*SearchPrompt)(nil),   // 1: tanlive.bff_mgmt.search.SearchPrompt
	(base.DisableState)(0), // 2: tanlive.base.DisableState
}
var file_tanlive_bff_mgmt_search_search_proto_depIdxs = []int32{
	2, // 0: tanlive.bff_mgmt.search.SearchOption.status:type_name -> tanlive.base.DisableState
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_search_search_proto_init() }
func file_tanlive_bff_mgmt_search_search_proto_init() {
	if File_tanlive_bff_mgmt_search_search_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_search_search_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_search_search_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPrompt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_search_search_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_bff_mgmt_search_search_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_search_search_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_search_search_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_search_search_proto = out.File
	file_tanlive_bff_mgmt_search_search_proto_rawDesc = nil
	file_tanlive_bff_mgmt_search_search_proto_goTypes = nil
	file_tanlive_bff_mgmt_search_search_proto_depIdxs = nil
}
