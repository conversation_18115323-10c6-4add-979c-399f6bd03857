// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package search

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Option":    "required",
		"Operation": "required",
	}, &ReqModifyTeamAtlasSearchOption{})
}

func (x *ReqModifyTeamAtlasSearchOption) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Language": "oneof=en zh",
	}, &ReqDescribeTeamAtlasSearchOptions_Filter{})
}

func (x *ReqDescribeTeamAtlasSearchOptions_Filter) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqModifySearchPrompt{})
}

func (x *ReqModifySearchPrompt) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqModifyTeamAtlasSearchOption) MaskInLog() any {
	if x == nil {
		return (*ReqModifyTeamAtlasSearchOption)(nil)
	}

	y := proto.Clone(x).(*ReqModifyTeamAtlasSearchOption)
	if v, ok := any(y.Option).(interface{ MaskInLog() any }); ok {
		y.Option = v.MaskInLog().(*SearchOption)
	}

	return y
}

func (x *ReqModifyTeamAtlasSearchOption) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyTeamAtlasSearchOption)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInRpc() any }); ok {
		y.Option = v.MaskInRpc().(*SearchOption)
	}

	return y
}

func (x *ReqModifyTeamAtlasSearchOption) MaskInBff() any {
	if x == nil {
		return (*ReqModifyTeamAtlasSearchOption)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInBff() any }); ok {
		y.Option = v.MaskInBff().(*SearchOption)
	}

	return y
}

func (x *ReqDescribeTeamAtlasSearchOptions) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeTeamAtlasSearchOptions)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeTeamAtlasSearchOptions)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeTeamAtlasSearchOptions_Filter)
	}

	return y
}

func (x *ReqDescribeTeamAtlasSearchOptions) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeTeamAtlasSearchOptions)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeTeamAtlasSearchOptions_Filter)
	}

	return y
}

func (x *ReqDescribeTeamAtlasSearchOptions) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeTeamAtlasSearchOptions)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeTeamAtlasSearchOptions_Filter)
	}

	return y
}

func (x *RspDescribeTeamAtlasSearchOptions) MaskInLog() any {
	if x == nil {
		return (*RspDescribeTeamAtlasSearchOptions)(nil)
	}

	y := proto.Clone(x).(*RspDescribeTeamAtlasSearchOptions)
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Options[k] = vv.MaskInLog().(*RspDescribeTeamAtlasSearchOptions_TeamSearchOption)
		}
	}

	return y
}

func (x *RspDescribeTeamAtlasSearchOptions) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeTeamAtlasSearchOptions)(nil)
	}

	y := x
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Options[k] = vv.MaskInRpc().(*RspDescribeTeamAtlasSearchOptions_TeamSearchOption)
		}
	}

	return y
}

func (x *RspDescribeTeamAtlasSearchOptions) MaskInBff() any {
	if x == nil {
		return (*RspDescribeTeamAtlasSearchOptions)(nil)
	}

	y := x
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Options[k] = vv.MaskInBff().(*RspDescribeTeamAtlasSearchOptions_TeamSearchOption)
		}
	}

	return y
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) MaskInLog() any {
	if x == nil {
		return (*RspDescribeTeamAtlasSearchOptions_TeamSearchOption)(nil)
	}

	y := proto.Clone(x).(*RspDescribeTeamAtlasSearchOptions_TeamSearchOption)
	if v, ok := any(y.Option).(interface{ MaskInLog() any }); ok {
		y.Option = v.MaskInLog().(*SearchOption)
	}

	return y
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeTeamAtlasSearchOptions_TeamSearchOption)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInRpc() any }); ok {
		y.Option = v.MaskInRpc().(*SearchOption)
	}

	return y
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) MaskInBff() any {
	if x == nil {
		return (*RspDescribeTeamAtlasSearchOptions_TeamSearchOption)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInBff() any }); ok {
		y.Option = v.MaskInBff().(*SearchOption)
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSearchPrompts)
	for k, v := range y.Prompts {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Prompts[k] = vv.MaskInLog().(*SearchPrompt)
		}
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := x
	for k, v := range y.Prompts {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Prompts[k] = vv.MaskInRpc().(*SearchPrompt)
		}
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := x
	for k, v := range y.Prompts {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Prompts[k] = vv.MaskInBff().(*SearchPrompt)
		}
	}

	return y
}

func (x *ReqModifyTeamAtlasSearchOption) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Option).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqDescribeTeamAtlasSearchOptions) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeTeamAtlasSearchOptions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Options {
		if sanitizer, ok := any(x.Options[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Option).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeSearchPrompts) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Prompts {
		if sanitizer, ok := any(x.Prompts[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type SearchBffHandler interface {
	// 修改团队-图谱搜索筛选项
	ModifyTeamAtlasSearchOption(context.Context, *ReqModifyTeamAtlasSearchOption, *emptypb.Empty) error
	// 查询团队-图谱搜索筛选项
	DescribeTeamAtlasSearchOptions(context.Context, *ReqDescribeTeamAtlasSearchOptions, *RspDescribeTeamAtlasSearchOptions) error
	// 修改产品-图谱搜索筛选项
	ModifyProductAtlasSearchOption(context.Context, *ReqModifyTeamAtlasSearchOption, *emptypb.Empty) error
	// 查询产品-图谱搜索筛选项
	DescribeProductAtlasSearchOptions(context.Context, *ReqDescribeTeamAtlasSearchOptions, *RspDescribeTeamAtlasSearchOptions) error
	// 修改搜索提示语
	ModifySearchPrompt(context.Context, *ReqModifySearchPrompt, *emptypb.Empty) error
	// 查询修改搜索提示语
	DescribeSearchPrompts(context.Context, *ReqDescribeSearchPrompts, *RspDescribeSearchPrompts) error
}

func RegisterSearchBff(s bff.Server, h SearchBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Search"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/search/modify_team_atlas_search_option", h.ModifyTeamAtlasSearchOption).Name("ModifyTeamAtlasSearchOption")
	bff.AddRoute(group, http.MethodPost, "/search/describe_team_atlas_search_option", h.DescribeTeamAtlasSearchOptions).Name("DescribeTeamAtlasSearchOptions")
	bff.AddRoute(group, http.MethodPost, "/search/modify_product_atlas_search_option", h.ModifyProductAtlasSearchOption).Name("ModifyProductAtlasSearchOption")
	bff.AddRoute(group, http.MethodPost, "/search/describe_product_atlas_search_option", h.DescribeProductAtlasSearchOptions).Name("DescribeProductAtlasSearchOptions")
	bff.AddRoute(group, http.MethodPost, "/search/modify_search_prompt", h.ModifySearchPrompt).Name("ModifySearchPrompt")
	bff.AddRoute(group, http.MethodPost, "/search/describe_search_prompt", h.DescribeSearchPrompts).Name("DescribeSearchPrompts")
	return group
}
