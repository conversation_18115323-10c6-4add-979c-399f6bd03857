// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/search/bff.proto

package search

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqModifyTeamAtlasSearchOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Option    *SearchOption `protobuf:"bytes,1,opt,name=option,proto3" json:"option,omitempty"`
	Operation base.CrudType `protobuf:"varint,2,opt,name=operation,proto3,enum=tanlive.base.CrudType" json:"operation,omitempty"`
}

func (x *ReqModifyTeamAtlasSearchOption) Reset() {
	*x = ReqModifyTeamAtlasSearchOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyTeamAtlasSearchOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyTeamAtlasSearchOption) ProtoMessage() {}

func (x *ReqModifyTeamAtlasSearchOption) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyTeamAtlasSearchOption.ProtoReflect.Descriptor instead.
func (*ReqModifyTeamAtlasSearchOption) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqModifyTeamAtlasSearchOption) GetOption() *SearchOption {
	if x != nil {
		return x.Option
	}
	return nil
}

func (x *ReqModifyTeamAtlasSearchOption) GetOperation() base.CrudType {
	if x != nil {
		return x.Operation
	}
	return base.CrudType(0)
}

type ReqDescribeTeamAtlasSearchOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset  uint32                                    `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit   uint32                                    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter  *ReqDescribeTeamAtlasSearchOptions_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy []string                                  `protobuf:"bytes,4,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
}

func (x *ReqDescribeTeamAtlasSearchOptions) Reset() {
	*x = ReqDescribeTeamAtlasSearchOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeTeamAtlasSearchOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeTeamAtlasSearchOptions) ProtoMessage() {}

func (x *ReqDescribeTeamAtlasSearchOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeTeamAtlasSearchOptions.ProtoReflect.Descriptor instead.
func (*ReqDescribeTeamAtlasSearchOptions) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP(), []int{1}
}

func (x *ReqDescribeTeamAtlasSearchOptions) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeTeamAtlasSearchOptions) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeTeamAtlasSearchOptions) GetFilter() *ReqDescribeTeamAtlasSearchOptions_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqDescribeTeamAtlasSearchOptions) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

type RspDescribeTeamAtlasSearchOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Options    []*RspDescribeTeamAtlasSearchOptions_TeamSearchOption `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
	TotalCount uint32                                                `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeTeamAtlasSearchOptions) Reset() {
	*x = RspDescribeTeamAtlasSearchOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeTeamAtlasSearchOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeTeamAtlasSearchOptions) ProtoMessage() {}

func (x *RspDescribeTeamAtlasSearchOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeTeamAtlasSearchOptions.ProtoReflect.Descriptor instead.
func (*RspDescribeTeamAtlasSearchOptions) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP(), []int{2}
}

func (x *RspDescribeTeamAtlasSearchOptions) GetOptions() []*RspDescribeTeamAtlasSearchOptions_TeamSearchOption {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *RspDescribeTeamAtlasSearchOptions) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqModifySearchPrompt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64  `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"` //可以为空，代表将内容置空
}

func (x *ReqModifySearchPrompt) Reset() {
	*x = ReqModifySearchPrompt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifySearchPrompt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifySearchPrompt) ProtoMessage() {}

func (x *ReqModifySearchPrompt) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifySearchPrompt.ProtoReflect.Descriptor instead.
func (*ReqModifySearchPrompt) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP(), []int{3}
}

func (x *ReqModifySearchPrompt) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReqModifySearchPrompt) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ReqDescribeSearchPrompts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 语言类型zh、en
	Language string `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *ReqDescribeSearchPrompts) Reset() {
	*x = ReqDescribeSearchPrompts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSearchPrompts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSearchPrompts) ProtoMessage() {}

func (x *ReqDescribeSearchPrompts) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSearchPrompts.ProtoReflect.Descriptor instead.
func (*ReqDescribeSearchPrompts) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP(), []int{4}
}

func (x *ReqDescribeSearchPrompts) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type RspDescribeSearchPrompts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prompts []*SearchPrompt `protobuf:"bytes,1,rep,name=prompts,proto3" json:"prompts,omitempty"`
}

func (x *RspDescribeSearchPrompts) Reset() {
	*x = RspDescribeSearchPrompts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSearchPrompts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSearchPrompts) ProtoMessage() {}

func (x *RspDescribeSearchPrompts) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSearchPrompts.ProtoReflect.Descriptor instead.
func (*RspDescribeSearchPrompts) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP(), []int{5}
}

func (x *RspDescribeSearchPrompts) GetPrompts() []*SearchPrompt {
	if x != nil {
		return x.Prompts
	}
	return nil
}

type ReqDescribeTeamAtlasSearchOptions_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 语言类型zh、en
	Language string            `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
	Status   base.DisableState `protobuf:"varint,2,opt,name=status,proto3,enum=tanlive.base.DisableState" json:"status,omitempty"`
}

func (x *ReqDescribeTeamAtlasSearchOptions_Filter) Reset() {
	*x = ReqDescribeTeamAtlasSearchOptions_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeTeamAtlasSearchOptions_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeTeamAtlasSearchOptions_Filter) ProtoMessage() {}

func (x *ReqDescribeTeamAtlasSearchOptions_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeTeamAtlasSearchOptions_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeTeamAtlasSearchOptions_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ReqDescribeTeamAtlasSearchOptions_Filter) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqDescribeTeamAtlasSearchOptions_Filter) GetStatus() base.DisableState {
	if x != nil {
		return x.Status
	}
	return base.DisableState(0)
}

type RspDescribeTeamAtlasSearchOptions_TeamSearchOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Option       *SearchOption `protobuf:"bytes,1,opt,name=option,proto3" json:"option,omitempty"`
	AtlasBindNum uint32        `protobuf:"varint,2,opt,name=atlas_bind_num,json=atlasBindNum,proto3" json:"atlas_bind_num,omitempty"`
	AtlasName    string        `protobuf:"bytes,3,opt,name=atlas_name,json=atlasName,proto3" json:"atlas_name,omitempty"`
	AtlasDeleted bool          `protobuf:"varint,4,opt,name=atlas_deleted,json=atlasDeleted,proto3" json:"atlas_deleted,omitempty"`
	AtlasState   base.UgcState `protobuf:"varint,5,opt,name=atlas_state,json=atlasState,proto3,enum=tanlive.base.UgcState" json:"atlas_state,omitempty"`
	AtlasDraftId uint64        `protobuf:"varint,6,opt,name=atlas_draft_id,json=atlasDraftId,proto3" json:"atlas_draft_id,omitempty"`
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) Reset() {
	*x = RspDescribeTeamAtlasSearchOptions_TeamSearchOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeTeamAtlasSearchOptions_TeamSearchOption) ProtoMessage() {}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_search_bff_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeTeamAtlasSearchOptions_TeamSearchOption.ProtoReflect.Descriptor instead.
func (*RspDescribeTeamAtlasSearchOptions_TeamSearchOption) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP(), []int{2, 0}
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) GetOption() *SearchOption {
	if x != nil {
		return x.Option
	}
	return nil
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) GetAtlasBindNum() uint32 {
	if x != nil {
		return x.AtlasBindNum
	}
	return 0
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) GetAtlasName() string {
	if x != nil {
		return x.AtlasName
	}
	return ""
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) GetAtlasDeleted() bool {
	if x != nil {
		return x.AtlasDeleted
	}
	return false
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) GetAtlasState() base.UgcState {
	if x != nil {
		return x.AtlasState
	}
	return base.UgcState(0)
}

func (x *RspDescribeTeamAtlasSearchOptions_TeamSearchOption) GetAtlasDraftId() uint64 {
	if x != nil {
		return x.AtlasDraftId
	}
	return 0
}

var File_tanlive_bff_mgmt_search_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_search_bff_proto_rawDesc = []byte{
	0x0a, 0x21, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x01, 0x0a, 0x1e, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x75, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb2, 0x02, 0x0a, 0x21, 0x52, 0x65,
	0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74, 0x6c,
	0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x59, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x1a, 0x69, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2b, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0f, 0x82, 0x88, 0x27, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x3d, 0x65, 0x6e, 0x20, 0x7a, 0x68,
	0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc8,
	0x03, 0x0a, 0x21, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65,
	0x61, 0x6d, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x65, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x41,
	0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x9a, 0x02, 0x0a,
	0x10, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x3d, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x24, 0x0a, 0x0e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x42,
	0x69, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x6c, 0x61,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x61, 0x74,
	0x6c, 0x61, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x0b, 0x61, 0x74,
	0x6c, 0x61, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55,
	0x67, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x64, 0x72, 0x61,
	0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x74, 0x6c,
	0x61, 0x73, 0x44, 0x72, 0x61, 0x66, 0x74, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x15, 0x52, 0x65, 0x71,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x36, 0x0a, 0x18, 0x52, 0x65,
	0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x22, 0x5b, 0x0a, 0x18, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x3f,
	0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x32,
	0xbc, 0x08, 0x0a, 0x09, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x66, 0x66, 0x12, 0xa2, 0x01,
	0x0a, 0x1b, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74, 0x6c, 0x61,
	0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x32,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0xce, 0x01, 0x0a, 0x1e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54,
	0x65, 0x61, 0x6d, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x41,
	0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x1a, 0x3a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74, 0x6c, 0x61, 0x73,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x34, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22, 0x29, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x5f,
	0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0xa8, 0x01, 0x0a, 0x1e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2e, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74,
	0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x3a,
	0x01, 0x2a, 0x22, 0x2a, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xd4,
	0x01, 0x0a, 0x21, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52,
	0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74,
	0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x1a, 0x3a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x37, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x31, 0x3a, 0x01, 0x2a, 0x22, 0x2c, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x85, 0x01, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x2e, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22,
	0x1c, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0xa8, 0x01,
	0x0a, 0x15, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x31, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x1a, 0x31, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x22, 0x29, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x42, 0x41,
	0x5a, 0x3f, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74,
	0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_search_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_search_bff_proto_rawDescData = file_tanlive_bff_mgmt_search_bff_proto_rawDesc
)

func file_tanlive_bff_mgmt_search_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_search_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_search_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_search_bff_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_search_bff_proto_rawDescData
}

var file_tanlive_bff_mgmt_search_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_tanlive_bff_mgmt_search_bff_proto_goTypes = []interface{}{
	(*ReqModifyTeamAtlasSearchOption)(nil),                     // 0: tanlive.bff_mgmt.search.ReqModifyTeamAtlasSearchOption
	(*ReqDescribeTeamAtlasSearchOptions)(nil),                  // 1: tanlive.bff_mgmt.search.ReqDescribeTeamAtlasSearchOptions
	(*RspDescribeTeamAtlasSearchOptions)(nil),                  // 2: tanlive.bff_mgmt.search.RspDescribeTeamAtlasSearchOptions
	(*ReqModifySearchPrompt)(nil),                              // 3: tanlive.bff_mgmt.search.ReqModifySearchPrompt
	(*ReqDescribeSearchPrompts)(nil),                           // 4: tanlive.bff_mgmt.search.ReqDescribeSearchPrompts
	(*RspDescribeSearchPrompts)(nil),                           // 5: tanlive.bff_mgmt.search.RspDescribeSearchPrompts
	(*ReqDescribeTeamAtlasSearchOptions_Filter)(nil),           // 6: tanlive.bff_mgmt.search.ReqDescribeTeamAtlasSearchOptions.Filter
	(*RspDescribeTeamAtlasSearchOptions_TeamSearchOption)(nil), // 7: tanlive.bff_mgmt.search.RspDescribeTeamAtlasSearchOptions.TeamSearchOption
	(*SearchOption)(nil),                                       // 8: tanlive.bff_mgmt.search.SearchOption
	(base.CrudType)(0),                                         // 9: tanlive.base.CrudType
	(*SearchPrompt)(nil),                                       // 10: tanlive.bff_mgmt.search.SearchPrompt
	(base.DisableState)(0),                                     // 11: tanlive.base.DisableState
	(base.UgcState)(0),                                         // 12: tanlive.base.UgcState
	(*emptypb.Empty)(nil),                                      // 13: google.protobuf.Empty
}
var file_tanlive_bff_mgmt_search_bff_proto_depIdxs = []int32{
	8,  // 0: tanlive.bff_mgmt.search.ReqModifyTeamAtlasSearchOption.option:type_name -> tanlive.bff_mgmt.search.SearchOption
	9,  // 1: tanlive.bff_mgmt.search.ReqModifyTeamAtlasSearchOption.operation:type_name -> tanlive.base.CrudType
	6,  // 2: tanlive.bff_mgmt.search.ReqDescribeTeamAtlasSearchOptions.filter:type_name -> tanlive.bff_mgmt.search.ReqDescribeTeamAtlasSearchOptions.Filter
	7,  // 3: tanlive.bff_mgmt.search.RspDescribeTeamAtlasSearchOptions.options:type_name -> tanlive.bff_mgmt.search.RspDescribeTeamAtlasSearchOptions.TeamSearchOption
	10, // 4: tanlive.bff_mgmt.search.RspDescribeSearchPrompts.prompts:type_name -> tanlive.bff_mgmt.search.SearchPrompt
	11, // 5: tanlive.bff_mgmt.search.ReqDescribeTeamAtlasSearchOptions.Filter.status:type_name -> tanlive.base.DisableState
	8,  // 6: tanlive.bff_mgmt.search.RspDescribeTeamAtlasSearchOptions.TeamSearchOption.option:type_name -> tanlive.bff_mgmt.search.SearchOption
	12, // 7: tanlive.bff_mgmt.search.RspDescribeTeamAtlasSearchOptions.TeamSearchOption.atlas_state:type_name -> tanlive.base.UgcState
	0,  // 8: tanlive.bff_mgmt.search.SearchBff.ModifyTeamAtlasSearchOption:input_type -> tanlive.bff_mgmt.search.ReqModifyTeamAtlasSearchOption
	1,  // 9: tanlive.bff_mgmt.search.SearchBff.DescribeTeamAtlasSearchOptions:input_type -> tanlive.bff_mgmt.search.ReqDescribeTeamAtlasSearchOptions
	0,  // 10: tanlive.bff_mgmt.search.SearchBff.ModifyProductAtlasSearchOption:input_type -> tanlive.bff_mgmt.search.ReqModifyTeamAtlasSearchOption
	1,  // 11: tanlive.bff_mgmt.search.SearchBff.DescribeProductAtlasSearchOptions:input_type -> tanlive.bff_mgmt.search.ReqDescribeTeamAtlasSearchOptions
	3,  // 12: tanlive.bff_mgmt.search.SearchBff.ModifySearchPrompt:input_type -> tanlive.bff_mgmt.search.ReqModifySearchPrompt
	4,  // 13: tanlive.bff_mgmt.search.SearchBff.DescribeSearchPrompts:input_type -> tanlive.bff_mgmt.search.ReqDescribeSearchPrompts
	13, // 14: tanlive.bff_mgmt.search.SearchBff.ModifyTeamAtlasSearchOption:output_type -> google.protobuf.Empty
	2,  // 15: tanlive.bff_mgmt.search.SearchBff.DescribeTeamAtlasSearchOptions:output_type -> tanlive.bff_mgmt.search.RspDescribeTeamAtlasSearchOptions
	13, // 16: tanlive.bff_mgmt.search.SearchBff.ModifyProductAtlasSearchOption:output_type -> google.protobuf.Empty
	2,  // 17: tanlive.bff_mgmt.search.SearchBff.DescribeProductAtlasSearchOptions:output_type -> tanlive.bff_mgmt.search.RspDescribeTeamAtlasSearchOptions
	13, // 18: tanlive.bff_mgmt.search.SearchBff.ModifySearchPrompt:output_type -> google.protobuf.Empty
	5,  // 19: tanlive.bff_mgmt.search.SearchBff.DescribeSearchPrompts:output_type -> tanlive.bff_mgmt.search.RspDescribeSearchPrompts
	14, // [14:20] is the sub-list for method output_type
	8,  // [8:14] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_search_bff_proto_init() }
func file_tanlive_bff_mgmt_search_bff_proto_init() {
	if File_tanlive_bff_mgmt_search_bff_proto != nil {
		return
	}
	file_tanlive_bff_mgmt_search_search_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_search_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyTeamAtlasSearchOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_search_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeTeamAtlasSearchOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_search_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeTeamAtlasSearchOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_search_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifySearchPrompt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_search_bff_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSearchPrompts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_search_bff_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSearchPrompts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_search_bff_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeTeamAtlasSearchOptions_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_search_bff_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeTeamAtlasSearchOptions_TeamSearchOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_search_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_bff_mgmt_search_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_search_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_search_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_search_bff_proto = out.File
	file_tanlive_bff_mgmt_search_bff_proto_rawDesc = nil
	file_tanlive_bff_mgmt_search_bff_proto_goTypes = nil
	file_tanlive_bff_mgmt_search_bff_proto_depIdxs = nil
}
