// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/support/bff.proto

package support

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqProxy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Urls []string `protobuf:"bytes,1,rep,name=urls,proto3" json:"urls,omitempty"`
}

func (x *ReqProxy) Reset() {
	*x = ReqProxy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqProxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqProxy) ProtoMessage() {}

func (x *ReqProxy) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqProxy.ProtoReflect.Descriptor instead.
func (*ReqProxy) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_support_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqProxy) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

type RspProxy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Contents []*RspProxy_Content `protobuf:"bytes,1,rep,name=contents,proto3" json:"contents,omitempty"`
}

func (x *RspProxy) Reset() {
	*x = RspProxy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspProxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspProxy) ProtoMessage() {}

func (x *RspProxy) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspProxy.ProtoReflect.Descriptor instead.
func (*RspProxy) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_support_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspProxy) GetContents() []*RspProxy_Content {
	if x != nil {
		return x.Contents
	}
	return nil
}

type ReqGetHashIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []uint64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ReqGetHashIds) Reset() {
	*x = ReqGetHashIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetHashIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetHashIds) ProtoMessage() {}

func (x *ReqGetHashIds) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetHashIds.ProtoReflect.Descriptor instead.
func (*ReqGetHashIds) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_support_bff_proto_rawDescGZIP(), []int{2}
}

func (x *ReqGetHashIds) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type RspGetHashIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HashIds []string `protobuf:"bytes,2,rep,name=hash_ids,json=hashIds,proto3" json:"hash_ids,omitempty"`
}

func (x *RspGetHashIds) Reset() {
	*x = RspGetHashIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetHashIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetHashIds) ProtoMessage() {}

func (x *RspGetHashIds) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetHashIds.ProtoReflect.Descriptor instead.
func (*RspGetHashIds) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_support_bff_proto_rawDescGZIP(), []int{3}
}

func (x *RspGetHashIds) GetHashIds() []string {
	if x != nil {
		return x.HashIds
	}
	return nil
}

type RspProxy_Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *RspProxy_Content) Reset() {
	*x = RspProxy_Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspProxy_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspProxy_Content) ProtoMessage() {}

func (x *RspProxy_Content) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_support_bff_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspProxy_Content.ProtoReflect.Descriptor instead.
func (*RspProxy_Content) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_support_bff_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RspProxy_Content) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RspProxy_Content) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

var File_tanlive_bff_mgmt_support_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_support_bff_proto_rawDesc = []byte{
	0x0a, 0x22, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x15,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x2c, 0x0a, 0x08, 0x52, 0x65, 0x71, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x12,
	0x20, 0x0a, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x75, 0x72, 0x6c,
	0x73, 0x22, 0x85, 0x01, 0x0a, 0x08, 0x52, 0x73, 0x70, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x46,
	0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x50,
	0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x31, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x2f, 0x0a, 0x0d, 0x52, 0x65, 0x71,
	0x47, 0x65, 0x74, 0x48, 0x61, 0x73, 0x68, 0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x2a, 0x0a, 0x0d, 0x52, 0x73,
	0x70, 0x47, 0x65, 0x74, 0x48, 0x61, 0x73, 0x68, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x68,
	0x61, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x68,
	0x61, 0x73, 0x68, 0x49, 0x64, 0x73, 0x32, 0x81, 0x02, 0x0a, 0x0a, 0x53, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x42, 0x66, 0x66, 0x12, 0x6a, 0x0a, 0x05, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x22,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x50, 0x72, 0x6f,
	0x78, 0x79, 0x1a, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73,
	0x70, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01,
	0x2a, 0x22, 0x0e, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x78,
	0x79, 0x12, 0x80, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x48, 0x61, 0x73, 0x68, 0x49, 0x64, 0x73,
	0x12, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x74, 0x48, 0x61, 0x73, 0x68, 0x49, 0x64, 0x73, 0x1a, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x48, 0x61, 0x73, 0x68, 0x49,
	0x64, 0x73, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x68, 0x61, 0x73, 0x68,
	0x5f, 0x69, 0x64, 0x73, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x42, 0x42, 0x5a, 0x40, 0x65, 0x2e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65,
	0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66,
	0x66, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_support_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_support_bff_proto_rawDescData = file_tanlive_bff_mgmt_support_bff_proto_rawDesc
)

func file_tanlive_bff_mgmt_support_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_support_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_support_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_support_bff_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_support_bff_proto_rawDescData
}

var file_tanlive_bff_mgmt_support_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_tanlive_bff_mgmt_support_bff_proto_goTypes = []interface{}{
	(*ReqProxy)(nil),         // 0: tanlive.bff_mgmt.support.ReqProxy
	(*RspProxy)(nil),         // 1: tanlive.bff_mgmt.support.RspProxy
	(*ReqGetHashIds)(nil),    // 2: tanlive.bff_mgmt.support.ReqGetHashIds
	(*RspGetHashIds)(nil),    // 3: tanlive.bff_mgmt.support.RspGetHashIds
	(*RspProxy_Content)(nil), // 4: tanlive.bff_mgmt.support.RspProxy.Content
}
var file_tanlive_bff_mgmt_support_bff_proto_depIdxs = []int32{
	4, // 0: tanlive.bff_mgmt.support.RspProxy.contents:type_name -> tanlive.bff_mgmt.support.RspProxy.Content
	0, // 1: tanlive.bff_mgmt.support.SupportBff.Proxy:input_type -> tanlive.bff_mgmt.support.ReqProxy
	2, // 2: tanlive.bff_mgmt.support.SupportBff.GetHashIds:input_type -> tanlive.bff_mgmt.support.ReqGetHashIds
	1, // 3: tanlive.bff_mgmt.support.SupportBff.Proxy:output_type -> tanlive.bff_mgmt.support.RspProxy
	3, // 4: tanlive.bff_mgmt.support.SupportBff.GetHashIds:output_type -> tanlive.bff_mgmt.support.RspGetHashIds
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_support_bff_proto_init() }
func file_tanlive_bff_mgmt_support_bff_proto_init() {
	if File_tanlive_bff_mgmt_support_bff_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_support_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqProxy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_support_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspProxy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_support_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetHashIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_support_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetHashIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_support_bff_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspProxy_Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_support_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_bff_mgmt_support_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_support_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_support_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_support_bff_proto = out.File
	file_tanlive_bff_mgmt_support_bff_proto_rawDesc = nil
	file_tanlive_bff_mgmt_support_bff_proto_goTypes = nil
	file_tanlive_bff_mgmt_support_bff_proto_depIdxs = nil
}
