syntax = "proto3";

package tanlive.bff_mgmt.support;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/support";

import "tanlive/options.proto";
import "google/api/annotations.proto";

service SupportBff {
  option (tanlive.bff) = true;

  // 代理请求访问网页
  rpc Proxy(ReqProxy) returns (RspProxy){
    option (google.api.http) = {
      post: "/support/proxy"
      body: "*"
    };
  };


  rpc GetHashIds(ReqGetHashIds) returns (RspGetHashIds) {
    option (google.api.http) = {
      post: "/support/get_hash_ids"
      body: "*"
    };
  }
}


message ReqProxy{
  repeated string urls = 1 [(validator) = "required"];
}

message RspProxy{
  message Content {
    string url = 1;
    string title =2;
  }
  repeated Content contents = 1;
}

message ReqGetHashIds {
  repeated uint64 ids = 1 [(validator) = "required"];
}

message RspGetHashIds {
  repeated string hash_ids = 2;
}