// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package support

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	proto "google.golang.org/protobuf/proto"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Urls": "required",
	}, &ReqProxy{})
}

func (x *ReqProxy) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Ids": "required",
	}, &ReqGetHashIds{})
}

func (x *ReqGetHashIds) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspProxy) MaskInLog() any {
	if x == nil {
		return (*RspProxy)(nil)
	}

	y := proto.Clone(x).(*RspProxy)
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contents[k] = vv.MaskInLog().(*RspProxy_Content)
		}
	}

	return y
}

func (x *RspProxy) MaskInRpc() any {
	if x == nil {
		return (*RspProxy)(nil)
	}

	y := x
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contents[k] = vv.MaskInRpc().(*RspProxy_Content)
		}
	}

	return y
}

func (x *RspProxy) MaskInBff() any {
	if x == nil {
		return (*RspProxy)(nil)
	}

	y := x
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contents[k] = vv.MaskInBff().(*RspProxy_Content)
		}
	}

	return y
}

func (x *RspProxy) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contents {
		if sanitizer, ok := any(x.Contents[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type SupportBffHandler interface {
	// 代理请求访问网页
	Proxy(context.Context, *ReqProxy, *RspProxy) error
	GetHashIds(context.Context, *ReqGetHashIds, *RspGetHashIds) error
}

func RegisterSupportBff(s bff.Server, h SupportBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Support"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/support/proxy", h.Proxy).Name("Proxy")
	bff.AddRoute(group, http.MethodPost, "/support/get_hash_ids", h.GetHashIds).Name("GetHashIds")
	return group
}
