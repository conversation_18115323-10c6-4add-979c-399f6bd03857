// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package tag

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Weight": "min=0,max=1000",
		"Name":   "ugc_between=2-100",
		"IsTmt":  "boolean",
	}, &Tag{})
}

func (x *Tag) Validate() error {
	return validator.Validator().Struct(x)
}
