// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/tag/bff.proto

package tag

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	ai "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	tag "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqUpdateSystemTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	// 标签权重
	Weight uint32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创
	Type tag.TagCreateType `protobuf:"varint,3,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 标签索引类型
	TaggableType tag.TaggableType `protobuf:"varint,4,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 定向推送标签类型（我来自标签时） 1 系统 ；2 自创
	NotifyType tag.TagCreateType `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
}

func (x *ReqUpdateSystemTag) Reset() {
	*x = ReqUpdateSystemTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateSystemTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateSystemTag) ProtoMessage() {}

func (x *ReqUpdateSystemTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateSystemTag.ProtoReflect.Descriptor instead.
func (*ReqUpdateSystemTag) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqUpdateSystemTag) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

func (x *ReqUpdateSystemTag) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *ReqUpdateSystemTag) GetType() tag.TagCreateType {
	if x != nil {
		return x.Type
	}
	return tag.TagCreateType(0)
}

func (x *ReqUpdateSystemTag) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

func (x *ReqUpdateSystemTag) GetNotifyType() tag.TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return tag.TagCreateType(0)
}

type ReqEditSystemTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签索引id 为0 则创建，否则更新对应数据
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 标签索引名称
	IndexName string `protobuf:"bytes,2,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	// 中文标签信息
	ZhTag *Tag `protobuf:"bytes,3,opt,name=zh_tag,json=zhTag,proto3" json:"zh_tag,omitempty"`
	// 英文标签信息
	EnTag *Tag `protobuf:"bytes,4,opt,name=en_tag,json=enTag,proto3" json:"en_tag,omitempty"`
	// 标签索引类型
	TaggableType tag.TaggableType `protobuf:"varint,5,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
}

func (x *ReqEditSystemTag) Reset() {
	*x = ReqEditSystemTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqEditSystemTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqEditSystemTag) ProtoMessage() {}

func (x *ReqEditSystemTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqEditSystemTag.ProtoReflect.Descriptor instead.
func (*ReqEditSystemTag) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{1}
}

func (x *ReqEditSystemTag) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *ReqEditSystemTag) GetIndexName() string {
	if x != nil {
		return x.IndexName
	}
	return ""
}

func (x *ReqEditSystemTag) GetZhTag() *Tag {
	if x != nil {
		return x.ZhTag
	}
	return nil
}

func (x *ReqEditSystemTag) GetEnTag() *Tag {
	if x != nil {
		return x.EnTag
	}
	return nil
}

func (x *ReqEditSystemTag) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

type ReqDeleteSystemTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签索引数组，会删除索引下的所有标签
	TagIndexIds []uint64 `protobuf:"varint,1,rep,packed,name=tag_index_ids,json=tagIndexIds,proto3" json:"tag_index_ids,omitempty"`
	// 标签索引类型
	TaggableType tag.TaggableType `protobuf:"varint,2,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
}

func (x *ReqDeleteSystemTag) Reset() {
	*x = ReqDeleteSystemTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteSystemTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteSystemTag) ProtoMessage() {}

func (x *ReqDeleteSystemTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteSystemTag.ProtoReflect.Descriptor instead.
func (*ReqDeleteSystemTag) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{2}
}

func (x *ReqDeleteSystemTag) GetTagIndexIds() []uint64 {
	if x != nil {
		return x.TagIndexIds
	}
	return nil
}

func (x *ReqDeleteSystemTag) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

type ReqMergeSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 合并后的标签索引id
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 需要合并标签索引数组
	MergeIndexIds []uint64 `protobuf:"varint,2,rep,packed,name=merge_index_ids,json=mergeIndexIds,proto3" json:"merge_index_ids,omitempty"`
	// 合并后的中文标签信息
	ZhTag *Tag `protobuf:"bytes,3,opt,name=zh_tag,json=zhTag,proto3" json:"zh_tag,omitempty"`
	// 合并后英文标签信息
	EnTag *Tag `protobuf:"bytes,4,opt,name=en_tag,json=enTag,proto3" json:"en_tag,omitempty"`
	// 合并的标签类型
	TaggableType tag.TaggableType `protobuf:"varint,5,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
}

func (x *ReqMergeSystemTags) Reset() {
	*x = ReqMergeSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqMergeSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqMergeSystemTags) ProtoMessage() {}

func (x *ReqMergeSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqMergeSystemTags.ProtoReflect.Descriptor instead.
func (*ReqMergeSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{3}
}

func (x *ReqMergeSystemTags) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *ReqMergeSystemTags) GetMergeIndexIds() []uint64 {
	if x != nil {
		return x.MergeIndexIds
	}
	return nil
}

func (x *ReqMergeSystemTags) GetZhTag() *Tag {
	if x != nil {
		return x.ZhTag
	}
	return nil
}

func (x *ReqMergeSystemTags) GetEnTag() *Tag {
	if x != nil {
		return x.EnTag
	}
	return nil
}

func (x *ReqMergeSystemTags) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

type ReqDescribeSystemTagsByIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset  uint32                               `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit   uint32                               `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter  *ReqDescribeSystemTagsByIndex_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy string                               `protobuf:"bytes,4,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
}

func (x *ReqDescribeSystemTagsByIndex) Reset() {
	*x = ReqDescribeSystemTagsByIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSystemTagsByIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSystemTagsByIndex) ProtoMessage() {}

func (x *ReqDescribeSystemTagsByIndex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSystemTagsByIndex.ProtoReflect.Descriptor instead.
func (*ReqDescribeSystemTagsByIndex) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{4}
}

func (x *ReqDescribeSystemTagsByIndex) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeSystemTagsByIndex) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeSystemTagsByIndex) GetFilter() *ReqDescribeSystemTagsByIndex_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqDescribeSystemTagsByIndex) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type RspDescribeSystemTagsByIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagSet     []*tag.TagIndex `protobuf:"bytes,1,rep,name=tag_set,json=tagSet,proto3" json:"tag_set,omitempty"`
	TotalCount uint32          `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeSystemTagsByIndex) Reset() {
	*x = RspDescribeSystemTagsByIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSystemTagsByIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSystemTagsByIndex) ProtoMessage() {}

func (x *RspDescribeSystemTagsByIndex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSystemTagsByIndex.ProtoReflect.Descriptor instead.
func (*RspDescribeSystemTagsByIndex) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{5}
}

func (x *RspDescribeSystemTagsByIndex) GetTagSet() []*tag.TagIndex {
	if x != nil {
		return x.TagSet
	}
	return nil
}

func (x *RspDescribeSystemTagsByIndex) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// ReqDescribeTeamTagBindingInfos 查询团队类型标签关联详情
type ReqDescribeTeamTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint32                                 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32                                 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter *ReqDescribeTeamTagBindingInfos_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeTeamTagBindingInfos) Reset() {
	*x = ReqDescribeTeamTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeTeamTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeTeamTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeTeamTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeTeamTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeTeamTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{6}
}

func (x *ReqDescribeTeamTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeTeamTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeTeamTagBindingInfos) GetFilter() *ReqDescribeTeamTagBindingInfos_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// RspDescribeTeamTagBindingInfos 查询团队类型标签关联详情回复
type RspDescribeTeamTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeTeamTagBindingInfos_BindingObject `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                          `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeTeamTagBindingInfos) Reset() {
	*x = RspDescribeTeamTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeTeamTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeTeamTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeTeamTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeTeamTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeTeamTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{7}
}

func (x *RspDescribeTeamTagBindingInfos) GetBindingObjects() []*RspDescribeTeamTagBindingInfos_BindingObject {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeTeamTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// ReqDescribeResourceTagBindingInfos 查询资源相关标签关联详情
type ReqDescribeResourceTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint32                                     `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32                                     `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter *ReqDescribeResourceTagBindingInfos_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeResourceTagBindingInfos) Reset() {
	*x = ReqDescribeResourceTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeResourceTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeResourceTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeResourceTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeResourceTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeResourceTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{8}
}

func (x *ReqDescribeResourceTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeResourceTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeResourceTagBindingInfos) GetFilter() *ReqDescribeResourceTagBindingInfos_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// RspDescribeResourceTagBindingInfos 查询资源相关标签关联详情回复
type RspDescribeResourceTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeResourceTagBindingInfos_BindingObject `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                              `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeResourceTagBindingInfos) Reset() {
	*x = RspDescribeResourceTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeResourceTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeResourceTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeResourceTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeResourceTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeResourceTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{9}
}

func (x *RspDescribeResourceTagBindingInfos) GetBindingObjects() []*RspDescribeResourceTagBindingInfos_BindingObject {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeResourceTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqDescribeAtlasTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint32                                  `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32                                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter *ReqDescribeAtlasTagBindingInfos_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeAtlasTagBindingInfos) Reset() {
	*x = ReqDescribeAtlasTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeAtlasTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeAtlasTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeAtlasTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeAtlasTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeAtlasTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{10}
}

func (x *ReqDescribeAtlasTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeAtlasTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeAtlasTagBindingInfos) GetFilter() *ReqDescribeAtlasTagBindingInfos_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type RspDescribeAtlasTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeAtlasTagBindingInfos_BindingObject `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                           `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeAtlasTagBindingInfos) Reset() {
	*x = RspDescribeAtlasTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeAtlasTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeAtlasTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeAtlasTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeAtlasTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeAtlasTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{11}
}

func (x *RspDescribeAtlasTagBindingInfos) GetBindingObjects() []*RspDescribeAtlasTagBindingInfos_BindingObject {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeAtlasTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// ReqDescribeProductTagBindingInfos 查询产品技术-面向用户标签关联详情
type ReqDescribeProductTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint32                                    `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32                                    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter *ReqDescribeProductTagBindingInfos_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeProductTagBindingInfos) Reset() {
	*x = ReqDescribeProductTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeProductTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeProductTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeProductTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeProductTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeProductTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{12}
}

func (x *ReqDescribeProductTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeProductTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeProductTagBindingInfos) GetFilter() *ReqDescribeProductTagBindingInfos_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// RspDescribeProductTagBindingInfos 查询产品技术-面向用户类型标签关联详情回复
type RspDescribeProductTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeProductTagBindingInfos_BindingObject `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                             `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeProductTagBindingInfos) Reset() {
	*x = RspDescribeProductTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeProductTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeProductTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeProductTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeProductTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeProductTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{13}
}

func (x *RspDescribeProductTagBindingInfos) GetBindingObjects() []*RspDescribeProductTagBindingInfos_BindingObject {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeProductTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqDescribeAssistantTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId  uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Offset uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *ReqDescribeAssistantTagBindingInfos) Reset() {
	*x = ReqDescribeAssistantTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeAssistantTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeAssistantTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeAssistantTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeAssistantTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeAssistantTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{14}
}

func (x *ReqDescribeAssistantTagBindingInfos) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

func (x *ReqDescribeAssistantTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeAssistantTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type RspDescribeAssistantTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeAssistantTagBindingInfos_Info `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                      `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeAssistantTagBindingInfos) Reset() {
	*x = RspDescribeAssistantTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeAssistantTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeAssistantTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeAssistantTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeAssistantTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeAssistantTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{15}
}

func (x *RspDescribeAssistantTagBindingInfos) GetBindingObjects() []*RspDescribeAssistantTagBindingInfos_Info {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeAssistantTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqBatchImportSystemTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 导入标签索引数组
	TagIndex []*ReqBatchImportSystemTag_TagIndex `protobuf:"bytes,1,rep,name=tag_index,json=tagIndex,proto3" json:"tag_index,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
	Type tag.TagCreateType `protobuf:"varint,2,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
	NotifyType tag.TagCreateType `protobuf:"varint,3,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 标签类型
	TaggableType tag.TaggableType `protobuf:"varint,4,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 备注
	Remarks string `protobuf:"bytes,5,opt,name=remarks,proto3" json:"remarks,omitempty"`
}

func (x *ReqBatchImportSystemTag) Reset() {
	*x = ReqBatchImportSystemTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBatchImportSystemTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBatchImportSystemTag) ProtoMessage() {}

func (x *ReqBatchImportSystemTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBatchImportSystemTag.ProtoReflect.Descriptor instead.
func (*ReqBatchImportSystemTag) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{16}
}

func (x *ReqBatchImportSystemTag) GetTagIndex() []*ReqBatchImportSystemTag_TagIndex {
	if x != nil {
		return x.TagIndex
	}
	return nil
}

func (x *ReqBatchImportSystemTag) GetType() tag.TagCreateType {
	if x != nil {
		return x.Type
	}
	return tag.TagCreateType(0)
}

func (x *ReqBatchImportSystemTag) GetNotifyType() tag.TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return tag.TagCreateType(0)
}

func (x *ReqBatchImportSystemTag) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

func (x *ReqBatchImportSystemTag) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

type ReqGetSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签索引类型
	TaggableType tag.TaggableType `protobuf:"varint,1,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 语言类型zh、en
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	// 排序
	OrderBy []string `protobuf:"bytes,3,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// 定向推送用的筛选条件 1 系统标签
	NotifyType tag.TagCreateType `protobuf:"varint,4,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 标签名称模糊搜索
	TagName string `protobuf:"bytes,5,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 是否包含 助手用户标签
	WithAssistantUserTag bool `protobuf:"varint,6,opt,name=with_assistant_user_tag,json=withAssistantUserTag,proto3" json:"with_assistant_user_tag,omitempty"`
}

func (x *ReqGetSystemTags) Reset() {
	*x = ReqGetSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetSystemTags) ProtoMessage() {}

func (x *ReqGetSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetSystemTags.ProtoReflect.Descriptor instead.
func (*ReqGetSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{17}
}

func (x *ReqGetSystemTags) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

func (x *ReqGetSystemTags) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqGetSystemTags) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ReqGetSystemTags) GetNotifyType() tag.TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return tag.TagCreateType(0)
}

func (x *ReqGetSystemTags) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *ReqGetSystemTags) GetWithAssistantUserTag() bool {
	if x != nil {
		return x.WithAssistantUserTag
	}
	return false
}

type RspGetSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagSet []*RspGetSystemTags_Tag `protobuf:"bytes,1,rep,name=tag_set,json=tagSet,proto3" json:"tag_set,omitempty"`
}

func (x *RspGetSystemTags) Reset() {
	*x = RspGetSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetSystemTags) ProtoMessage() {}

func (x *RspGetSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetSystemTags.ProtoReflect.Descriptor instead.
func (*RspGetSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{18}
}

func (x *RspGetSystemTags) GetTagSet() []*RspGetSystemTags_Tag {
	if x != nil {
		return x.TagSet
	}
	return nil
}

type ReqCreatePersonalTagBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 绑定现有的标签
	TagIds []uint64 `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// 新增的自创标签
	TagNames []string `protobuf:"bytes,3,rep,name=tag_names,json=tagNames,proto3" json:"tag_names,omitempty"`
}

func (x *ReqCreatePersonalTagBinding) Reset() {
	*x = ReqCreatePersonalTagBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreatePersonalTagBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreatePersonalTagBinding) ProtoMessage() {}

func (x *ReqCreatePersonalTagBinding) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreatePersonalTagBinding.ProtoReflect.Descriptor instead.
func (*ReqCreatePersonalTagBinding) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{19}
}

func (x *ReqCreatePersonalTagBinding) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqCreatePersonalTagBinding) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqCreatePersonalTagBinding) GetTagNames() []string {
	if x != nil {
		return x.TagNames
	}
	return nil
}

type ReqCreateTeamUserTagBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId uint64 `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// 绑定现有的标签
	TagIds []uint64 `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// 新增的自创标签
	TagNames []string `protobuf:"bytes,3,rep,name=tag_names,json=tagNames,proto3" json:"tag_names,omitempty"`
}

func (x *ReqCreateTeamUserTagBinding) Reset() {
	*x = ReqCreateTeamUserTagBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateTeamUserTagBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateTeamUserTagBinding) ProtoMessage() {}

func (x *ReqCreateTeamUserTagBinding) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateTeamUserTagBinding.ProtoReflect.Descriptor instead.
func (*ReqCreateTeamUserTagBinding) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{20}
}

func (x *ReqCreateTeamUserTagBinding) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *ReqCreateTeamUserTagBinding) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqCreateTeamUserTagBinding) GetTagNames() []string {
	if x != nil {
		return x.TagNames
	}
	return nil
}

type ReqGetNotifyDataByUserLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaggableType tag.TaggableType `protobuf:"varint,1,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	TagIds       []uint64         `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
}

func (x *ReqGetNotifyDataByUserLabel) Reset() {
	*x = ReqGetNotifyDataByUserLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetNotifyDataByUserLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetNotifyDataByUserLabel) ProtoMessage() {}

func (x *ReqGetNotifyDataByUserLabel) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetNotifyDataByUserLabel.ProtoReflect.Descriptor instead.
func (*ReqGetNotifyDataByUserLabel) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{21}
}

func (x *ReqGetNotifyDataByUserLabel) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

func (x *ReqGetNotifyDataByUserLabel) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

type RspGetNotifyDataByUserLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataInfos []*RspGetNotifyDataByUserLabel_DataInfo `protobuf:"bytes,1,rep,name=data_infos,json=dataInfos,proto3" json:"data_infos,omitempty"`
}

func (x *RspGetNotifyDataByUserLabel) Reset() {
	*x = RspGetNotifyDataByUserLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetNotifyDataByUserLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetNotifyDataByUserLabel) ProtoMessage() {}

func (x *RspGetNotifyDataByUserLabel) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetNotifyDataByUserLabel.ProtoReflect.Descriptor instead.
func (*RspGetNotifyDataByUserLabel) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{22}
}

func (x *RspGetNotifyDataByUserLabel) GetDataInfos() []*RspGetNotifyDataByUserLabel_DataInfo {
	if x != nil {
		return x.DataInfos
	}
	return nil
}

type ReqDescribeSystemTagsByIndex_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签名称
	TagName string `protobuf:"bytes,1,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 标签索引类型
	TaggableType tag.TaggableType `protobuf:"varint,2,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 标签索引id
	IndexIds []uint64 `protobuf:"varint,3,rep,packed,name=index_ids,json=indexIds,proto3" json:"index_ids,omitempty"`
	// 语言类型
	Language string `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创
	Type tag.TagCreateType `protobuf:"varint,5,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（我来自标签时） 1 系统 ；2 自创
	NotifyType tag.TagCreateType `protobuf:"varint,6,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
}

func (x *ReqDescribeSystemTagsByIndex_Filter) Reset() {
	*x = ReqDescribeSystemTagsByIndex_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSystemTagsByIndex_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSystemTagsByIndex_Filter) ProtoMessage() {}

func (x *ReqDescribeSystemTagsByIndex_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSystemTagsByIndex_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeSystemTagsByIndex_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetIndexIds() []uint64 {
	if x != nil {
		return x.IndexIds
	}
	return nil
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetType() tag.TagCreateType {
	if x != nil {
		return x.Type
	}
	return tag.TagCreateType(0)
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetNotifyType() tag.TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return tag.TagCreateType(0)
}

type ReqDescribeTeamTagBindingInfos_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (x *ReqDescribeTeamTagBindingInfos_Filter) Reset() {
	*x = ReqDescribeTeamTagBindingInfos_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeTeamTagBindingInfos_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeTeamTagBindingInfos_Filter) ProtoMessage() {}

func (x *ReqDescribeTeamTagBindingInfos_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeTeamTagBindingInfos_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeTeamTagBindingInfos_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ReqDescribeTeamTagBindingInfos_Filter) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

type RspDescribeTeamTagBindingInfos_BindingObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 团队id
	TeamId uint64 `protobuf:"varint,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// 团队名称
	TeamName string `protobuf:"bytes,3,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
	// 团队简称
	TeamShortName string `protobuf:"bytes,4,opt,name=team_short_name,json=teamShortName,proto3" json:"team_short_name,omitempty"`
	// 创建时间
	CreateDate string `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 团队属性
	TeamNature string `protobuf:"bytes,6,opt,name=team_nature,json=teamNature,proto3" json:"team_nature,omitempty"`
	// 认证状态
	VerifyStatus string `protobuf:"bytes,7,opt,name=verify_status,json=verifyStatus,proto3" json:"verify_status,omitempty"`
	// 团队持有人id
	HolderId uint64 `protobuf:"varint,8,opt,name=holder_id,json=holderId,proto3" json:"holder_id,omitempty"`
	// 团队持有人
	HolderName string `protobuf:"bytes,9,opt,name=holder_name,json=holderName,proto3" json:"holder_name,omitempty"`
	// 团队类型
	TeamTypes []string `protobuf:"bytes,10,rep,name=team_types,json=teamTypes,proto3" json:"team_types,omitempty"`
	// 贡献者
	Contributors []string `protobuf:"bytes,11,rep,name=contributors,proto3" json:"contributors,omitempty"`
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) Reset() {
	*x = RspDescribeTeamTagBindingInfos_BindingObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeTeamTagBindingInfos_BindingObject) ProtoMessage() {}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeTeamTagBindingInfos_BindingObject.ProtoReflect.Descriptor instead.
func (*RspDescribeTeamTagBindingInfos_BindingObject) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{7, 0}
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamShortName() string {
	if x != nil {
		return x.TeamShortName
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamNature() string {
	if x != nil {
		return x.TeamNature
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetVerifyStatus() string {
	if x != nil {
		return x.VerifyStatus
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetHolderId() uint64 {
	if x != nil {
		return x.HolderId
	}
	return 0
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetHolderName() string {
	if x != nil {
		return x.HolderName
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamTypes() []string {
	if x != nil {
		return x.TeamTypes
	}
	return nil
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetContributors() []string {
	if x != nil {
		return x.Contributors
	}
	return nil
}

type ReqDescribeResourceTagBindingInfos_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (x *ReqDescribeResourceTagBindingInfos_Filter) Reset() {
	*x = ReqDescribeResourceTagBindingInfos_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeResourceTagBindingInfos_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeResourceTagBindingInfos_Filter) ProtoMessage() {}

func (x *ReqDescribeResourceTagBindingInfos_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeResourceTagBindingInfos_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeResourceTagBindingInfos_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ReqDescribeResourceTagBindingInfos_Filter) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

type RspDescribeResourceTagBindingInfos_BindingObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 资源标题
	ResName string `protobuf:"bytes,2,opt,name=res_name,json=resName,proto3" json:"res_name,omitempty"`
	// 资源简述
	ResIntroduction string `protobuf:"bytes,3,opt,name=res_introduction,json=resIntroduction,proto3" json:"res_introduction,omitempty"`
	// 资源类型
	ResTypes []string `protobuf:"bytes,4,rep,name=res_types,json=resTypes,proto3" json:"res_types,omitempty"`
	// 创建时间
	CreateDate string `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 主办方
	OriginatorName []string `protobuf:"bytes,6,rep,name=originator_name,json=originatorName,proto3" json:"originator_name,omitempty"`
	// 资源id
	ResId uint64 `protobuf:"varint,7,opt,name=res_id,json=resId,proto3" json:"res_id,omitempty"`
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) Reset() {
	*x = RspDescribeResourceTagBindingInfos_BindingObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeResourceTagBindingInfos_BindingObject) ProtoMessage() {}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeResourceTagBindingInfos_BindingObject.ProtoReflect.Descriptor instead.
func (*RspDescribeResourceTagBindingInfos_BindingObject) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{9, 0}
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetResName() string {
	if x != nil {
		return x.ResName
	}
	return ""
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetResIntroduction() string {
	if x != nil {
		return x.ResIntroduction
	}
	return ""
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetResTypes() []string {
	if x != nil {
		return x.ResTypes
	}
	return nil
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetOriginatorName() []string {
	if x != nil {
		return x.OriginatorName
	}
	return nil
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetResId() uint64 {
	if x != nil {
		return x.ResId
	}
	return 0
}

type ReqDescribeAtlasTagBindingInfos_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (x *ReqDescribeAtlasTagBindingInfos_Filter) Reset() {
	*x = ReqDescribeAtlasTagBindingInfos_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeAtlasTagBindingInfos_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeAtlasTagBindingInfos_Filter) ProtoMessage() {}

func (x *ReqDescribeAtlasTagBindingInfos_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeAtlasTagBindingInfos_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeAtlasTagBindingInfos_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ReqDescribeAtlasTagBindingInfos_Filter) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

type RspDescribeAtlasTagBindingInfos_BindingObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标题
	AtlasName string `protobuf:"bytes,2,opt,name=atlas_name,json=atlasName,proto3" json:"atlas_name,omitempty"`
	// 简述
	AtlasIntroduction string `protobuf:"bytes,3,opt,name=atlas_introduction,json=atlasIntroduction,proto3" json:"atlas_introduction,omitempty"`
	// 图谱类型
	AtlasTypes []string `protobuf:"bytes,4,rep,name=atlas_types,json=atlasTypes,proto3" json:"atlas_types,omitempty"`
	// 创建时间
	CreateDate string `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 发布方
	PubName []string `protobuf:"bytes,6,rep,name=pub_name,json=pubName,proto3" json:"pub_name,omitempty"`
	// 图谱id
	AtlasId uint64 `protobuf:"varint,7,opt,name=atlas_id,json=atlasId,proto3" json:"atlas_id,omitempty"`
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) Reset() {
	*x = RspDescribeAtlasTagBindingInfos_BindingObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeAtlasTagBindingInfos_BindingObject) ProtoMessage() {}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeAtlasTagBindingInfos_BindingObject.ProtoReflect.Descriptor instead.
func (*RspDescribeAtlasTagBindingInfos_BindingObject) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{11, 0}
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetAtlasName() string {
	if x != nil {
		return x.AtlasName
	}
	return ""
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetAtlasIntroduction() string {
	if x != nil {
		return x.AtlasIntroduction
	}
	return ""
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetAtlasTypes() []string {
	if x != nil {
		return x.AtlasTypes
	}
	return nil
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetPubName() []string {
	if x != nil {
		return x.PubName
	}
	return nil
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetAtlasId() uint64 {
	if x != nil {
		return x.AtlasId
	}
	return 0
}

type ReqDescribeProductTagBindingInfos_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (x *ReqDescribeProductTagBindingInfos_Filter) Reset() {
	*x = ReqDescribeProductTagBindingInfos_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeProductTagBindingInfos_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeProductTagBindingInfos_Filter) ProtoMessage() {}

func (x *ReqDescribeProductTagBindingInfos_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeProductTagBindingInfos_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeProductTagBindingInfos_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ReqDescribeProductTagBindingInfos_Filter) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

type RspDescribeProductTagBindingInfos_BindingObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 产品名称
	ProductName string `protobuf:"bytes,2,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"`
	// 一句话简介
	BriefIntro string `protobuf:"bytes,3,opt,name=brief_intro,json=briefIntro,proto3" json:"brief_intro,omitempty"`
	// 产品类型
	ProductTypes []string `protobuf:"bytes,4,rep,name=product_types,json=productTypes,proto3" json:"product_types,omitempty"`
	// 团队简称
	TeamShortName string `protobuf:"bytes,5,opt,name=team_short_name,json=teamShortName,proto3" json:"team_short_name,omitempty"`
	// 创建时间
	CreateDate string `protobuf:"bytes,6,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 产品id
	ProductId uint64 `protobuf:"varint,7,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) Reset() {
	*x = RspDescribeProductTagBindingInfos_BindingObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeProductTagBindingInfos_BindingObject) ProtoMessage() {}

func (x *RspDescribeProductTagBindingInfos_BindingObject) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeProductTagBindingInfos_BindingObject.ProtoReflect.Descriptor instead.
func (*RspDescribeProductTagBindingInfos_BindingObject) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{13, 0}
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetBriefIntro() string {
	if x != nil {
		return x.BriefIntro
	}
	return ""
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetProductTypes() []string {
	if x != nil {
		return x.ProductTypes
	}
	return nil
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetTeamShortName() string {
	if x != nil {
		return x.TeamShortName
	}
	return ""
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetProductId() uint64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

type RspDescribeAssistantTagBindingInfos_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	NameEn     string                 `protobuf:"bytes,2,opt,name=name_en,json=nameEn,proto3" json:"name_en,omitempty"`
	Id         uint64                 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 创建人
	CreateBy *base.Identity `protobuf:"bytes,5,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// 渠道
	Channel ai.AssistantChannel `protobuf:"varint,6,opt,name=channel,proto3,enum=tanlive.ai.AssistantChannel" json:"channel,omitempty"`
	// 是否启用
	Enabled bool `protobuf:"varint,7,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// 是否草稿
	IsDraft bool `protobuf:"varint,8,opt,name=is_draft,json=isDraft,proto3" json:"is_draft,omitempty"`
}

func (x *RspDescribeAssistantTagBindingInfos_Info) Reset() {
	*x = RspDescribeAssistantTagBindingInfos_Info{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeAssistantTagBindingInfos_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeAssistantTagBindingInfos_Info) ProtoMessage() {}

func (x *RspDescribeAssistantTagBindingInfos_Info) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeAssistantTagBindingInfos_Info.ProtoReflect.Descriptor instead.
func (*RspDescribeAssistantTagBindingInfos_Info) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{15, 0}
}

func (x *RspDescribeAssistantTagBindingInfos_Info) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RspDescribeAssistantTagBindingInfos_Info) GetNameEn() string {
	if x != nil {
		return x.NameEn
	}
	return ""
}

func (x *RspDescribeAssistantTagBindingInfos_Info) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeAssistantTagBindingInfos_Info) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *RspDescribeAssistantTagBindingInfos_Info) GetCreateBy() *base.Identity {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *RspDescribeAssistantTagBindingInfos_Info) GetChannel() ai.AssistantChannel {
	if x != nil {
		return x.Channel
	}
	return ai.AssistantChannel(0)
}

func (x *RspDescribeAssistantTagBindingInfos_Info) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *RspDescribeAssistantTagBindingInfos_Info) GetIsDraft() bool {
	if x != nil {
		return x.IsDraft
	}
	return false
}

type ReqBatchImportSystemTag_TagIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引名称
	IndexName string `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	// 标签名称
	TagName string `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
}

func (x *ReqBatchImportSystemTag_TagIndex) Reset() {
	*x = ReqBatchImportSystemTag_TagIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBatchImportSystemTag_TagIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBatchImportSystemTag_TagIndex) ProtoMessage() {}

func (x *ReqBatchImportSystemTag_TagIndex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBatchImportSystemTag_TagIndex.ProtoReflect.Descriptor instead.
func (*ReqBatchImportSystemTag_TagIndex) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ReqBatchImportSystemTag_TagIndex) GetIndexName() string {
	if x != nil {
		return x.IndexName
	}
	return ""
}

func (x *ReqBatchImportSystemTag_TagIndex) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

type RspGetSystemTags_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *RspGetSystemTags_Tag) Reset() {
	*x = RspGetSystemTags_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetSystemTags_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetSystemTags_Tag) ProtoMessage() {}

func (x *RspGetSystemTags_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetSystemTags_Tag.ProtoReflect.Descriptor instead.
func (*RspGetSystemTags_Tag) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{18, 0}
}

func (x *RspGetSystemTags_Tag) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspGetSystemTags_Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RspGetNotifyDataByUserLabel_DataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsInternational bool   `protobuf:"varint,3,opt,name=is_international,json=isInternational,proto3" json:"is_international,omitempty"`
}

func (x *RspGetNotifyDataByUserLabel_DataInfo) Reset() {
	*x = RspGetNotifyDataByUserLabel_DataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetNotifyDataByUserLabel_DataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetNotifyDataByUserLabel_DataInfo) ProtoMessage() {}

func (x *RspGetNotifyDataByUserLabel_DataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetNotifyDataByUserLabel_DataInfo.ProtoReflect.Descriptor instead.
func (*RspGetNotifyDataByUserLabel_DataInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP(), []int{22, 0}
}

func (x *RspGetNotifyDataByUserLabel_DataInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspGetNotifyDataByUserLabel_DataInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RspGetNotifyDataByUserLabel_DataInfo) GetIsInternational() bool {
	if x != nil {
		return x.IsInternational
	}
	return false
}

var File_tanlive_bff_mgmt_tag_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_tag_bff_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x14, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x2f,
	0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x74, 0x61, 0x67, 0x2f,
	0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x61, 0x69, 0x2f, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe2,
	0x02, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x29, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x6d, 0x69, 0x6e, 0x3d, 0x30, 0x2c, 0x6d, 0x61, 0x78, 0x3d,
	0x31, 0x30, 0x30, 0x30, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x49, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x19, 0x82, 0x88, 0x27, 0x15, 0x6f, 0x6d, 0x69, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x30, 0x2c, 0x6d, 0x61, 0x78, 0x3d,
	0x32, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67,
	0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0c, 0x74,
	0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x19, 0x82, 0x88,
	0x27, 0x15, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x6d, 0x69, 0x6e, 0x3d,
	0x30, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x32, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x9a, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x45, 0x64, 0x69, 0x74, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0a, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x82, 0x88, 0x27, 0x10, 0x75, 0x67, 0x63,
	0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x3d, 0x32, 0x2d, 0x33, 0x30, 0x52, 0x09, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x7a, 0x68, 0x5f, 0x74,
	0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x54, 0x61, 0x67, 0x52, 0x05, 0x7a, 0x68, 0x54, 0x61, 0x67, 0x12, 0x30, 0x0a, 0x06, 0x65, 0x6e,
	0x5f, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x05, 0x65, 0x6e, 0x54, 0x61, 0x67, 0x12, 0x52, 0x0a, 0x0d,
	0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x12,
	0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e,
	0x3d, 0x31, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x9a, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x30, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0b, 0x74, 0x61,
	0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x74, 0x61, 0x67,
	0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x12, 0x82, 0x88, 0x27,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52,
	0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb7, 0x02,
	0x0a, 0x12, 0x52, 0x65, 0x71, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x54, 0x61, 0x67, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x42, 0x12, 0x82, 0x88,
	0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x32,
	0x52, 0x0d, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x73, 0x12,
	0x30, 0x0a, 0x06, 0x7a, 0x68, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x05, 0x7a, 0x68, 0x54, 0x61,
	0x67, 0x12, 0x30, 0x0a, 0x06, 0x65, 0x6e, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x05, 0x65, 0x6e,
	0x54, 0x61, 0x67, 0x12, 0x52, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb9, 0x04, 0x0a, 0x1c, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67,
	0x73, 0x42, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x5f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65,
	0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54,
	0x61, 0x67, 0x73, 0x42, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x42, 0x79, 0x1a, 0xee, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a,
	0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61,
	0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0c,
	0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0x82, 0x88, 0x27,
	0x15, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x3d, 0x65, 0x6e, 0x20, 0x7a, 0x68, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x49, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x19, 0x82, 0x88, 0x27, 0x15,
	0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x30, 0x2c,
	0x6d, 0x61, 0x78, 0x3d, 0x32, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x19, 0x82, 0x88,
	0x27, 0x15, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x6d, 0x69, 0x6e, 0x3d,
	0x30, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x32, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x6f, 0x0a, 0x1c, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x42, 0x79, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x2e, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74,
	0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x06, 0x74, 0x61, 0x67,
	0x53, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc4, 0x01, 0x0a, 0x1e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x53, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x1f, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x22, 0x96, 0x04, 0x0a, 0x1e,
	0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54,
	0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x6b,
	0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52,
	0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61,
	0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x62, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe5, 0x02, 0x0a,
	0x0d, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x73, 0x68, 0x6f,
	0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74,
	0x65, 0x61, 0x6d, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x61, 0x6d, 0x4e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x6f, 0x72, 0x73, 0x22, 0xcc, 0x01, 0x0a, 0x22, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x57, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x1a, 0x1f, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06,
	0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61,
	0x67, 0x49, 0x64, 0x22, 0x9c, 0x03, 0x0a, 0x22, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x6f, 0x0a, 0x0f, 0x62, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61,
	0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x62, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe3, 0x01, 0x0a,
	0x0d, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x73,
	0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x72, 0x65, 0x73,
	0x49, 0x64, 0x22, 0xc6, 0x01, 0x0a, 0x1f, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x54, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x1f, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x22, 0x98, 0x03, 0x0a, 0x1f,
	0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73,
	0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x6c, 0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73,
	0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x62,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe5,
	0x01, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2d, 0x0a, 0x12, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x74, 0x6c,
	0x61, 0x73, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x75, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x49, 0x64, 0x22, 0xca, 0x01, 0x0a, 0x21, 0x52, 0x65, 0x71, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x56, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x1a, 0x1f, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06,
	0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61,
	0x67, 0x49, 0x64, 0x22, 0xa7, 0x03, 0x0a, 0x21, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x6e, 0x0a, 0x0f, 0x62, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xf0, 0x01, 0x0a, 0x0d, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x69, 0x65, 0x66, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x74, 0x72, 0x6f,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x73, 0x68,
	0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x65, 0x61, 0x6d, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x22, 0x78, 0x0a,
	0x23, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xd4, 0x03, 0x0a, 0x23, 0x52, 0x73, 0x70, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x67, 0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xa2, 0x02, 0x0a, 0x04, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x79, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e,
	0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x64, 0x72, 0x61, 0x66, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x44, 0x72, 0x61, 0x66, 0x74, 0x22, 0xf1,
	0x03, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x66, 0x0a, 0x09, 0x74, 0x61,
	0x67, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x42, 0x11, 0x82, 0x88, 0x27, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x52, 0x08, 0x74, 0x61, 0x67, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x52, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c,
	0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0x82, 0x88, 0x27, 0x11, 0x75, 0x67, 0x63, 0x5f, 0x62, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x3d, 0x30, 0x2d, 0x31, 0x30, 0x30, 0x52, 0x07, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x1a, 0x7c, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x34, 0x0a, 0x0a, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0x82, 0x88, 0x27, 0x11, 0x75, 0x67, 0x63, 0x5f, 0x62, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x3d, 0x32, 0x2d, 0x31, 0x30, 0x30, 0x52, 0x09, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1f, 0x82, 0x88, 0x27, 0x1b, 0x6f, 0x6d,
	0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x75, 0x67, 0x63, 0x5f, 0x62, 0x65, 0x74, 0x77,
	0x65, 0x65, 0x6e, 0x3d, 0x32, 0x2d, 0x31, 0x30, 0x30, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xb4, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67,
	0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x09, 0x82, 0x88, 0x27, 0x05, 0x6d,
	0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x4c, 0x0a, 0x0b, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x82, 0x88, 0x27, 0x0b,
	0x6d, 0x69, 0x6e, 0x3d, 0x30, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x31, 0x52, 0x0a, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x14, 0x77, 0x69, 0x74, 0x68, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x22, 0x82, 0x01, 0x0a, 0x10, 0x52, 0x73,
	0x70, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x43,
	0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x06, 0x74, 0x61, 0x67,
	0x53, 0x65, 0x74, 0x1a, 0x29, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x7a,
	0x0a, 0x1b, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x6c, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x7a, 0x0a, 0x1b, 0x52, 0x65,
	0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x54,
	0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x07, 0x74, 0x65, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61,
	0x67, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x92, 0x01, 0x0a, 0x1b, 0x52, 0x65, 0x71, 0x47, 0x65,
	0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x4c, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67,
	0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x22, 0xd3, 0x01, 0x0a, 0x1b,
	0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x59, 0x0a, 0x0a, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x59, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x69, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x32, 0xeb, 0x12, 0x0a, 0x06, 0x54, 0x61, 0x67, 0x42, 0x66, 0x66, 0x12, 0x76, 0x0a, 0x0f,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12,
	0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x74,
	0x61, 0x67, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x61, 0x67, 0x12, 0x70, 0x0a, 0x0d, 0x45, 0x64, 0x69, 0x74, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71,
	0x45, 0x64, 0x69, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a,
	0x22, 0x14, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x12, 0x76, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x54, 0x61, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x12, 0x76,
	0x0a, 0x0f, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67,
	0x73, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16,
	0x2f, 0x74, 0x61, 0x67, 0x2f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x12, 0xb2, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x42, 0x79, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x32, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67,
	0x73, 0x42, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x1a, 0x32, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x54, 0x61, 0x67, 0x73, 0x42, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x2d, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x61,
	0x67, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0xba, 0x01, 0x0a, 0x1b,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x34, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74,
	0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65,
	0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x1a, 0x34, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a,
	0x01, 0x2a, 0x22, 0x24, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0xca, 0x01, 0x0a, 0x1f, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x38, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x38, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73,
	0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x74, 0x61,
	0x67, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0xbe, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x35, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65,
	0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61,
	0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x35, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22,
	0x25, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0xc6, 0x01, 0x0a, 0x1e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x37, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x1a, 0x37, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x32, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x61,
	0x67, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0xce, 0x01, 0x0a, 0x20, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x12, 0x39, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a,
	0x39, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22, 0x29, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x74,
	0x61, 0x67, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73,
	0x12, 0x86, 0x01, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x2d, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x52, 0x65, 0x71, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x74, 0x61,
	0x67, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x12, 0x80, 0x01, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x26, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74,
	0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54,
	0x61, 0x67, 0x73, 0x1a, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65,
	0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x22, 0x1f, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x67, 0x65, 0x74,
	0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x12, 0x92, 0x01, 0x0a,
	0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x54,
	0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x61, 0x6c, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22,
	0x20, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x93, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x31,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x65, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0xaf, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x12, 0x31, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x1a, 0x31, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52,
	0x73, 0x70, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x44, 0x61, 0x74, 0x61, 0x42,
	0x79, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x67, 0x65, 0x74, 0x5f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x62, 0x79, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x42,
	0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f,
	0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x74, 0x61, 0x67, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_tag_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_tag_bff_proto_rawDescData = file_tanlive_bff_mgmt_tag_bff_proto_rawDesc
)

func file_tanlive_bff_mgmt_tag_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_tag_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_tag_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_tag_bff_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_tag_bff_proto_rawDescData
}

var file_tanlive_bff_mgmt_tag_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_tanlive_bff_mgmt_tag_bff_proto_goTypes = []interface{}{
	(*ReqUpdateSystemTag)(nil),                               // 0: tanlive.bff_mgmt.tag.ReqUpdateSystemTag
	(*ReqEditSystemTag)(nil),                                 // 1: tanlive.bff_mgmt.tag.ReqEditSystemTag
	(*ReqDeleteSystemTag)(nil),                               // 2: tanlive.bff_mgmt.tag.ReqDeleteSystemTag
	(*ReqMergeSystemTags)(nil),                               // 3: tanlive.bff_mgmt.tag.ReqMergeSystemTags
	(*ReqDescribeSystemTagsByIndex)(nil),                     // 4: tanlive.bff_mgmt.tag.ReqDescribeSystemTagsByIndex
	(*RspDescribeSystemTagsByIndex)(nil),                     // 5: tanlive.bff_mgmt.tag.RspDescribeSystemTagsByIndex
	(*ReqDescribeTeamTagBindingInfos)(nil),                   // 6: tanlive.bff_mgmt.tag.ReqDescribeTeamTagBindingInfos
	(*RspDescribeTeamTagBindingInfos)(nil),                   // 7: tanlive.bff_mgmt.tag.RspDescribeTeamTagBindingInfos
	(*ReqDescribeResourceTagBindingInfos)(nil),               // 8: tanlive.bff_mgmt.tag.ReqDescribeResourceTagBindingInfos
	(*RspDescribeResourceTagBindingInfos)(nil),               // 9: tanlive.bff_mgmt.tag.RspDescribeResourceTagBindingInfos
	(*ReqDescribeAtlasTagBindingInfos)(nil),                  // 10: tanlive.bff_mgmt.tag.ReqDescribeAtlasTagBindingInfos
	(*RspDescribeAtlasTagBindingInfos)(nil),                  // 11: tanlive.bff_mgmt.tag.RspDescribeAtlasTagBindingInfos
	(*ReqDescribeProductTagBindingInfos)(nil),                // 12: tanlive.bff_mgmt.tag.ReqDescribeProductTagBindingInfos
	(*RspDescribeProductTagBindingInfos)(nil),                // 13: tanlive.bff_mgmt.tag.RspDescribeProductTagBindingInfos
	(*ReqDescribeAssistantTagBindingInfos)(nil),              // 14: tanlive.bff_mgmt.tag.ReqDescribeAssistantTagBindingInfos
	(*RspDescribeAssistantTagBindingInfos)(nil),              // 15: tanlive.bff_mgmt.tag.RspDescribeAssistantTagBindingInfos
	(*ReqBatchImportSystemTag)(nil),                          // 16: tanlive.bff_mgmt.tag.ReqBatchImportSystemTag
	(*ReqGetSystemTags)(nil),                                 // 17: tanlive.bff_mgmt.tag.ReqGetSystemTags
	(*RspGetSystemTags)(nil),                                 // 18: tanlive.bff_mgmt.tag.RspGetSystemTags
	(*ReqCreatePersonalTagBinding)(nil),                      // 19: tanlive.bff_mgmt.tag.ReqCreatePersonalTagBinding
	(*ReqCreateTeamUserTagBinding)(nil),                      // 20: tanlive.bff_mgmt.tag.ReqCreateTeamUserTagBinding
	(*ReqGetNotifyDataByUserLabel)(nil),                      // 21: tanlive.bff_mgmt.tag.ReqGetNotifyDataByUserLabel
	(*RspGetNotifyDataByUserLabel)(nil),                      // 22: tanlive.bff_mgmt.tag.RspGetNotifyDataByUserLabel
	(*ReqDescribeSystemTagsByIndex_Filter)(nil),              // 23: tanlive.bff_mgmt.tag.ReqDescribeSystemTagsByIndex.Filter
	(*ReqDescribeTeamTagBindingInfos_Filter)(nil),            // 24: tanlive.bff_mgmt.tag.ReqDescribeTeamTagBindingInfos.Filter
	(*RspDescribeTeamTagBindingInfos_BindingObject)(nil),     // 25: tanlive.bff_mgmt.tag.RspDescribeTeamTagBindingInfos.BindingObject
	(*ReqDescribeResourceTagBindingInfos_Filter)(nil),        // 26: tanlive.bff_mgmt.tag.ReqDescribeResourceTagBindingInfos.Filter
	(*RspDescribeResourceTagBindingInfos_BindingObject)(nil), // 27: tanlive.bff_mgmt.tag.RspDescribeResourceTagBindingInfos.BindingObject
	(*ReqDescribeAtlasTagBindingInfos_Filter)(nil),           // 28: tanlive.bff_mgmt.tag.ReqDescribeAtlasTagBindingInfos.Filter
	(*RspDescribeAtlasTagBindingInfos_BindingObject)(nil),    // 29: tanlive.bff_mgmt.tag.RspDescribeAtlasTagBindingInfos.BindingObject
	(*ReqDescribeProductTagBindingInfos_Filter)(nil),         // 30: tanlive.bff_mgmt.tag.ReqDescribeProductTagBindingInfos.Filter
	(*RspDescribeProductTagBindingInfos_BindingObject)(nil),  // 31: tanlive.bff_mgmt.tag.RspDescribeProductTagBindingInfos.BindingObject
	(*RspDescribeAssistantTagBindingInfos_Info)(nil),         // 32: tanlive.bff_mgmt.tag.RspDescribeAssistantTagBindingInfos.Info
	(*ReqBatchImportSystemTag_TagIndex)(nil),                 // 33: tanlive.bff_mgmt.tag.ReqBatchImportSystemTag.TagIndex
	(*RspGetSystemTags_Tag)(nil),                             // 34: tanlive.bff_mgmt.tag.RspGetSystemTags.Tag
	(*RspGetNotifyDataByUserLabel_DataInfo)(nil),             // 35: tanlive.bff_mgmt.tag.RspGetNotifyDataByUserLabel.DataInfo
	(tag.TagCreateType)(0),                                   // 36: tanlive.tag.TagCreateType
	(tag.TaggableType)(0),                                    // 37: tanlive.tag.TaggableType
	(*Tag)(nil),                                              // 38: tanlive.bff_mgmt.tag.Tag
	(*tag.TagIndex)(nil),                                     // 39: tanlive.tag.TagIndex
	(*timestamppb.Timestamp)(nil),                            // 40: google.protobuf.Timestamp
	(*base.Identity)(nil),                                    // 41: tanlive.base.Identity
	(ai.AssistantChannel)(0),                                 // 42: tanlive.ai.AssistantChannel
	(*emptypb.Empty)(nil),                                    // 43: google.protobuf.Empty
}
var file_tanlive_bff_mgmt_tag_bff_proto_depIdxs = []int32{
	36, // 0: tanlive.bff_mgmt.tag.ReqUpdateSystemTag.type:type_name -> tanlive.tag.TagCreateType
	37, // 1: tanlive.bff_mgmt.tag.ReqUpdateSystemTag.taggable_type:type_name -> tanlive.tag.TaggableType
	36, // 2: tanlive.bff_mgmt.tag.ReqUpdateSystemTag.notify_type:type_name -> tanlive.tag.TagCreateType
	38, // 3: tanlive.bff_mgmt.tag.ReqEditSystemTag.zh_tag:type_name -> tanlive.bff_mgmt.tag.Tag
	38, // 4: tanlive.bff_mgmt.tag.ReqEditSystemTag.en_tag:type_name -> tanlive.bff_mgmt.tag.Tag
	37, // 5: tanlive.bff_mgmt.tag.ReqEditSystemTag.taggable_type:type_name -> tanlive.tag.TaggableType
	37, // 6: tanlive.bff_mgmt.tag.ReqDeleteSystemTag.taggable_type:type_name -> tanlive.tag.TaggableType
	38, // 7: tanlive.bff_mgmt.tag.ReqMergeSystemTags.zh_tag:type_name -> tanlive.bff_mgmt.tag.Tag
	38, // 8: tanlive.bff_mgmt.tag.ReqMergeSystemTags.en_tag:type_name -> tanlive.bff_mgmt.tag.Tag
	37, // 9: tanlive.bff_mgmt.tag.ReqMergeSystemTags.taggable_type:type_name -> tanlive.tag.TaggableType
	23, // 10: tanlive.bff_mgmt.tag.ReqDescribeSystemTagsByIndex.filter:type_name -> tanlive.bff_mgmt.tag.ReqDescribeSystemTagsByIndex.Filter
	39, // 11: tanlive.bff_mgmt.tag.RspDescribeSystemTagsByIndex.tag_set:type_name -> tanlive.tag.TagIndex
	24, // 12: tanlive.bff_mgmt.tag.ReqDescribeTeamTagBindingInfos.filter:type_name -> tanlive.bff_mgmt.tag.ReqDescribeTeamTagBindingInfos.Filter
	25, // 13: tanlive.bff_mgmt.tag.RspDescribeTeamTagBindingInfos.binding_objects:type_name -> tanlive.bff_mgmt.tag.RspDescribeTeamTagBindingInfos.BindingObject
	26, // 14: tanlive.bff_mgmt.tag.ReqDescribeResourceTagBindingInfos.filter:type_name -> tanlive.bff_mgmt.tag.ReqDescribeResourceTagBindingInfos.Filter
	27, // 15: tanlive.bff_mgmt.tag.RspDescribeResourceTagBindingInfos.binding_objects:type_name -> tanlive.bff_mgmt.tag.RspDescribeResourceTagBindingInfos.BindingObject
	28, // 16: tanlive.bff_mgmt.tag.ReqDescribeAtlasTagBindingInfos.filter:type_name -> tanlive.bff_mgmt.tag.ReqDescribeAtlasTagBindingInfos.Filter
	29, // 17: tanlive.bff_mgmt.tag.RspDescribeAtlasTagBindingInfos.binding_objects:type_name -> tanlive.bff_mgmt.tag.RspDescribeAtlasTagBindingInfos.BindingObject
	30, // 18: tanlive.bff_mgmt.tag.ReqDescribeProductTagBindingInfos.filter:type_name -> tanlive.bff_mgmt.tag.ReqDescribeProductTagBindingInfos.Filter
	31, // 19: tanlive.bff_mgmt.tag.RspDescribeProductTagBindingInfos.binding_objects:type_name -> tanlive.bff_mgmt.tag.RspDescribeProductTagBindingInfos.BindingObject
	32, // 20: tanlive.bff_mgmt.tag.RspDescribeAssistantTagBindingInfos.binding_objects:type_name -> tanlive.bff_mgmt.tag.RspDescribeAssistantTagBindingInfos.Info
	33, // 21: tanlive.bff_mgmt.tag.ReqBatchImportSystemTag.tag_index:type_name -> tanlive.bff_mgmt.tag.ReqBatchImportSystemTag.TagIndex
	36, // 22: tanlive.bff_mgmt.tag.ReqBatchImportSystemTag.type:type_name -> tanlive.tag.TagCreateType
	36, // 23: tanlive.bff_mgmt.tag.ReqBatchImportSystemTag.notify_type:type_name -> tanlive.tag.TagCreateType
	37, // 24: tanlive.bff_mgmt.tag.ReqBatchImportSystemTag.taggable_type:type_name -> tanlive.tag.TaggableType
	37, // 25: tanlive.bff_mgmt.tag.ReqGetSystemTags.taggable_type:type_name -> tanlive.tag.TaggableType
	36, // 26: tanlive.bff_mgmt.tag.ReqGetSystemTags.notify_type:type_name -> tanlive.tag.TagCreateType
	34, // 27: tanlive.bff_mgmt.tag.RspGetSystemTags.tag_set:type_name -> tanlive.bff_mgmt.tag.RspGetSystemTags.Tag
	37, // 28: tanlive.bff_mgmt.tag.ReqGetNotifyDataByUserLabel.taggable_type:type_name -> tanlive.tag.TaggableType
	35, // 29: tanlive.bff_mgmt.tag.RspGetNotifyDataByUserLabel.data_infos:type_name -> tanlive.bff_mgmt.tag.RspGetNotifyDataByUserLabel.DataInfo
	37, // 30: tanlive.bff_mgmt.tag.ReqDescribeSystemTagsByIndex.Filter.taggable_type:type_name -> tanlive.tag.TaggableType
	36, // 31: tanlive.bff_mgmt.tag.ReqDescribeSystemTagsByIndex.Filter.type:type_name -> tanlive.tag.TagCreateType
	36, // 32: tanlive.bff_mgmt.tag.ReqDescribeSystemTagsByIndex.Filter.notify_type:type_name -> tanlive.tag.TagCreateType
	40, // 33: tanlive.bff_mgmt.tag.RspDescribeAssistantTagBindingInfos.Info.create_date:type_name -> google.protobuf.Timestamp
	41, // 34: tanlive.bff_mgmt.tag.RspDescribeAssistantTagBindingInfos.Info.create_by:type_name -> tanlive.base.Identity
	42, // 35: tanlive.bff_mgmt.tag.RspDescribeAssistantTagBindingInfos.Info.channel:type_name -> tanlive.ai.AssistantChannel
	0,  // 36: tanlive.bff_mgmt.tag.TagBff.UpdateSystemTag:input_type -> tanlive.bff_mgmt.tag.ReqUpdateSystemTag
	1,  // 37: tanlive.bff_mgmt.tag.TagBff.EditSystemTag:input_type -> tanlive.bff_mgmt.tag.ReqEditSystemTag
	2,  // 38: tanlive.bff_mgmt.tag.TagBff.DeleteSystemTag:input_type -> tanlive.bff_mgmt.tag.ReqDeleteSystemTag
	3,  // 39: tanlive.bff_mgmt.tag.TagBff.MergeSystemTags:input_type -> tanlive.bff_mgmt.tag.ReqMergeSystemTags
	4,  // 40: tanlive.bff_mgmt.tag.TagBff.DescribeSystemTagsByIndex:input_type -> tanlive.bff_mgmt.tag.ReqDescribeSystemTagsByIndex
	6,  // 41: tanlive.bff_mgmt.tag.TagBff.DescribeTeamTagBindingInfos:input_type -> tanlive.bff_mgmt.tag.ReqDescribeTeamTagBindingInfos
	8,  // 42: tanlive.bff_mgmt.tag.TagBff.DescribeResourceTagBindingInfos:input_type -> tanlive.bff_mgmt.tag.ReqDescribeResourceTagBindingInfos
	10, // 43: tanlive.bff_mgmt.tag.TagBff.DescribeAtlasTagBindingInfos:input_type -> tanlive.bff_mgmt.tag.ReqDescribeAtlasTagBindingInfos
	12, // 44: tanlive.bff_mgmt.tag.TagBff.DescribeProductTagBindingInfos:input_type -> tanlive.bff_mgmt.tag.ReqDescribeProductTagBindingInfos
	14, // 45: tanlive.bff_mgmt.tag.TagBff.DescribeAssistantTagBindingInfos:input_type -> tanlive.bff_mgmt.tag.ReqDescribeAssistantTagBindingInfos
	16, // 46: tanlive.bff_mgmt.tag.TagBff.BatchImportSystemTag:input_type -> tanlive.bff_mgmt.tag.ReqBatchImportSystemTag
	17, // 47: tanlive.bff_mgmt.tag.TagBff.GetSystemTags:input_type -> tanlive.bff_mgmt.tag.ReqGetSystemTags
	19, // 48: tanlive.bff_mgmt.tag.TagBff.CreatePersonalTagBinding:input_type -> tanlive.bff_mgmt.tag.ReqCreatePersonalTagBinding
	20, // 49: tanlive.bff_mgmt.tag.TagBff.CreateTeamUserTagBinding:input_type -> tanlive.bff_mgmt.tag.ReqCreateTeamUserTagBinding
	21, // 50: tanlive.bff_mgmt.tag.TagBff.GetNotifyDataByUserLabel:input_type -> tanlive.bff_mgmt.tag.ReqGetNotifyDataByUserLabel
	43, // 51: tanlive.bff_mgmt.tag.TagBff.UpdateSystemTag:output_type -> google.protobuf.Empty
	43, // 52: tanlive.bff_mgmt.tag.TagBff.EditSystemTag:output_type -> google.protobuf.Empty
	43, // 53: tanlive.bff_mgmt.tag.TagBff.DeleteSystemTag:output_type -> google.protobuf.Empty
	43, // 54: tanlive.bff_mgmt.tag.TagBff.MergeSystemTags:output_type -> google.protobuf.Empty
	5,  // 55: tanlive.bff_mgmt.tag.TagBff.DescribeSystemTagsByIndex:output_type -> tanlive.bff_mgmt.tag.RspDescribeSystemTagsByIndex
	7,  // 56: tanlive.bff_mgmt.tag.TagBff.DescribeTeamTagBindingInfos:output_type -> tanlive.bff_mgmt.tag.RspDescribeTeamTagBindingInfos
	9,  // 57: tanlive.bff_mgmt.tag.TagBff.DescribeResourceTagBindingInfos:output_type -> tanlive.bff_mgmt.tag.RspDescribeResourceTagBindingInfos
	11, // 58: tanlive.bff_mgmt.tag.TagBff.DescribeAtlasTagBindingInfos:output_type -> tanlive.bff_mgmt.tag.RspDescribeAtlasTagBindingInfos
	13, // 59: tanlive.bff_mgmt.tag.TagBff.DescribeProductTagBindingInfos:output_type -> tanlive.bff_mgmt.tag.RspDescribeProductTagBindingInfos
	15, // 60: tanlive.bff_mgmt.tag.TagBff.DescribeAssistantTagBindingInfos:output_type -> tanlive.bff_mgmt.tag.RspDescribeAssistantTagBindingInfos
	43, // 61: tanlive.bff_mgmt.tag.TagBff.BatchImportSystemTag:output_type -> google.protobuf.Empty
	18, // 62: tanlive.bff_mgmt.tag.TagBff.GetSystemTags:output_type -> tanlive.bff_mgmt.tag.RspGetSystemTags
	43, // 63: tanlive.bff_mgmt.tag.TagBff.CreatePersonalTagBinding:output_type -> google.protobuf.Empty
	43, // 64: tanlive.bff_mgmt.tag.TagBff.CreateTeamUserTagBinding:output_type -> google.protobuf.Empty
	22, // 65: tanlive.bff_mgmt.tag.TagBff.GetNotifyDataByUserLabel:output_type -> tanlive.bff_mgmt.tag.RspGetNotifyDataByUserLabel
	51, // [51:66] is the sub-list for method output_type
	36, // [36:51] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_tag_bff_proto_init() }
func file_tanlive_bff_mgmt_tag_bff_proto_init() {
	if File_tanlive_bff_mgmt_tag_bff_proto != nil {
		return
	}
	file_tanlive_bff_mgmt_tag_tag_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateSystemTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqEditSystemTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteSystemTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqMergeSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSystemTagsByIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSystemTagsByIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeTeamTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeTeamTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeResourceTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeResourceTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeAtlasTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeAtlasTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeProductTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeProductTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeAssistantTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeAssistantTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBatchImportSystemTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreatePersonalTagBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateTeamUserTagBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetNotifyDataByUserLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetNotifyDataByUserLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSystemTagsByIndex_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeTeamTagBindingInfos_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeTeamTagBindingInfos_BindingObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeResourceTagBindingInfos_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeResourceTagBindingInfos_BindingObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeAtlasTagBindingInfos_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeAtlasTagBindingInfos_BindingObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeProductTagBindingInfos_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeProductTagBindingInfos_BindingObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeAssistantTagBindingInfos_Info); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBatchImportSystemTag_TagIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetSystemTags_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_tag_bff_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetNotifyDataByUserLabel_DataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_tag_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_bff_mgmt_tag_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_tag_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_tag_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_tag_bff_proto = out.File
	file_tanlive_bff_mgmt_tag_bff_proto_rawDesc = nil
	file_tanlive_bff_mgmt_tag_bff_proto_goTypes = nil
	file_tanlive_bff_mgmt_tag_bff_proto_depIdxs = nil
}
