// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/tag/tag.proto

package tag

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	tag "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	//
	//	uint64 id = 1 ;
	//
	// 标签权重
	Weight uint32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	//	// 语言
	//	string language = 3 ;
	//
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
	Type tag.TagCreateType `protobuf:"varint,4,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
	NotifyType tag.TagCreateType `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// 是否tmt
	IsTmt bool `protobuf:"varint,7,opt,name=is_tmt,json=isTmt,proto3" json:"is_tmt,omitempty"`
}

func (x *Tag) Reset() {
	*x = Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_tag_tag_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tag) ProtoMessage() {}

func (x *Tag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_tag_tag_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tag.ProtoReflect.Descriptor instead.
func (*Tag) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_tag_tag_proto_rawDescGZIP(), []int{0}
}

func (x *Tag) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Tag) GetType() tag.TagCreateType {
	if x != nil {
		return x.Type
	}
	return tag.TagCreateType(0)
}

func (x *Tag) GetNotifyType() tag.TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return tag.TagCreateType(0)
}

func (x *Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tag) GetIsTmt() bool {
	if x != nil {
		return x.IsTmt
	}
	return false
}

var File_tanlive_bff_mgmt_tag_tag_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_tag_tag_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x14, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x74, 0x61, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x74, 0x61, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed, 0x01, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x2a, 0x0a,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x12, 0x82,
	0x88, 0x27, 0x0e, 0x6d, 0x69, 0x6e, 0x3d, 0x30, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x31, 0x30, 0x30,
	0x30, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0x82, 0x88, 0x27, 0x11, 0x75, 0x67, 0x63, 0x5f, 0x62, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x3d, 0x32, 0x2d, 0x31, 0x30, 0x30, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x22, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x74, 0x6d, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x0b, 0x82, 0x88, 0x27, 0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x52, 0x05,
	0x69, 0x73, 0x54, 0x6d, 0x74, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73,
	0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67, 0x6d,
	0x74, 0x2f, 0x74, 0x61, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_tag_tag_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_tag_tag_proto_rawDescData = file_tanlive_bff_mgmt_tag_tag_proto_rawDesc
)

func file_tanlive_bff_mgmt_tag_tag_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_tag_tag_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_tag_tag_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_tag_tag_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_tag_tag_proto_rawDescData
}

var file_tanlive_bff_mgmt_tag_tag_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tanlive_bff_mgmt_tag_tag_proto_goTypes = []interface{}{
	(*Tag)(nil),            // 0: tanlive.bff_mgmt.tag.Tag
	(tag.TagCreateType)(0), // 1: tanlive.tag.TagCreateType
}
var file_tanlive_bff_mgmt_tag_tag_proto_depIdxs = []int32{
	1, // 0: tanlive.bff_mgmt.tag.Tag.type:type_name -> tanlive.tag.TagCreateType
	1, // 1: tanlive.bff_mgmt.tag.Tag.notify_type:type_name -> tanlive.tag.TagCreateType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_tag_tag_proto_init() }
func file_tanlive_bff_mgmt_tag_tag_proto_init() {
	if File_tanlive_bff_mgmt_tag_tag_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_tag_tag_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_tag_tag_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_bff_mgmt_tag_tag_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_tag_tag_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_tag_tag_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_tag_tag_proto = out.File
	file_tanlive_bff_mgmt_tag_tag_proto_rawDesc = nil
	file_tanlive_bff_mgmt_tag_tag_proto_goTypes = nil
	file_tanlive_bff_mgmt_tag_tag_proto_depIdxs = nil
}
