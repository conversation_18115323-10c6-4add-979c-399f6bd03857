syntax = "proto3";

package tanlive.bff_mgmt.tag;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/tag";

import "google/protobuf/timestamp.proto";
import "tanlive/options.proto";
import "tanlive/tag/tag.proto";

message Tag {
  // 标签id
//  uint64 id = 1 ;
  // 标签权重
  uint32 weight = 2 [(tanlive.validator) = "min=0,max=1000"];
//  // 语言
//  string language = 3 ;
  // 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
  tanlive.tag.TagCreateType type = 4;
  // 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
  tanlive.tag.TagCreateType notify_type = 5;
  // 标签名称
  string name = 6 [(tanlive.validator) = "ugc_between=2-100"];
  // 是否tmt
  bool is_tmt = 7 [(tanlive.validator) = "boolean"];
}