syntax = "proto3";

package tanlive.bff_mgmt.tag;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/tag";

import "google/protobuf/timestamp.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "tanlive/tag/tag.proto";
import "tanlive/bff-mgmt/tag/tag.proto";
import "tanlive/ai/ai.proto";
import "tanlive/base/base.proto";
import "tanlive/options.proto";

message ReqUpdateSystemTag {
  // 标签id
  uint64 tag_id = 1 [(tanlive.validator) = "required,min=1"];
  // 标签权重
  uint32 weight = 2 [(tanlive.validator) = "min=0,max=1000"];
  // 标签类型（多语言选项或注册用） 1 系统 ；2 自创
  tanlive.tag.TagCreateType type = 3 [(tanlive.validator) = "omitempty,min=0,max=2"];
  // 标签索引类型
  tanlive.tag.TaggableType taggable_type = 4 [(tanlive.validator) = "required,min=1"];
  // 定向推送标签类型（我来自标签时） 1 系统 ；2 自创
  tanlive.tag.TagCreateType notify_type = 5 [(tanlive.validator) = "omitempty,min=0,max=2"];
}

message ReqEditSystemTag {
  // 标签索引id 为0 则创建，否则更新对应数据
  uint64 index_id = 1 ;
  // 标签索引名称
  string index_name = 2 [(tanlive.validator) = "ugc_between=2-30"];
  // 中文标签信息
  Tag zh_tag = 3 ;
  // 英文标签信息
  Tag en_tag = 4 ;
  // 标签索引类型
  tanlive.tag.TaggableType taggable_type = 5 [(tanlive.validator) = "required,min=1"];
}

message ReqDeleteSystemTag {
  // 标签索引数组，会删除索引下的所有标签
  repeated uint64 tag_index_ids = 1 [(tanlive.validator) = "required"];
  // 标签索引类型
  tanlive.tag.TaggableType taggable_type = 2 [(tanlive.validator) = "required,min=1"];
}

message ReqMergeSystemTags {
  // 合并后的标签索引id
  uint64 index_id = 1 [(tanlive.validator) = "required,min=1"];
  // 需要合并标签索引数组
  repeated uint64 merge_index_ids = 2 [(tanlive.validator) = "required,min=2"];
  // 合并后的中文标签信息
  Tag zh_tag = 3 ;
  // 合并后英文标签信息
  Tag en_tag = 4 ;
  // 合并的标签类型
  tanlive.tag.TaggableType taggable_type = 5 [(tanlive.validator) = "required,min=1"];
}

message ReqDescribeSystemTagsByIndex {
  message Filter {
    // 标签名称
    string tag_name = 1;
    // 标签索引类型
    tanlive.tag.TaggableType taggable_type = 2 [(tanlive.validator) = "required,min=1"];
    // 标签索引id
    repeated uint64 index_ids = 3;
    // 语言类型
    string language = 4 [(tanlive.validator) = "omitempty,oneof=en zh"];
    // 标签类型（多语言选项或注册用） 1 系统 ；2 自创
    tanlive.tag.TagCreateType type = 5 [(tanlive.validator) = "omitempty,min=0,max=2"];
    // 定向推送标签类型（我来自标签时） 1 系统 ；2 自创
    tanlive.tag.TagCreateType notify_type = 6 [(tanlive.validator) = "omitempty,min=0,max=2"];
  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3  [(tanlive.validator) = "required"];
  string order_by = 4;
}

message RspDescribeSystemTagsByIndex {
  repeated tanlive.tag.TagIndex tag_set = 1;
  uint32 total_count = 2;
}

// ReqDescribeTeamTagBindingInfos 查询团队类型标签关联详情
message ReqDescribeTeamTagBindingInfos {
  message Filter {
    uint64 tag_id = 1;

  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
}

// RspDescribeTeamTagBindingInfos 查询团队类型标签关联详情回复
message RspDescribeTeamTagBindingInfos {
  message BindingObject {
    uint64 id = 1;
    // 团队id
    uint64 team_id = 2;
    // 团队名称
    string team_name = 3;
    // 团队简称
    string team_short_name = 4;
    // 创建时间
    string create_date = 5;
    // 团队属性
    string team_nature = 6;
    // 认证状态
    string verify_status = 7;
    // 团队持有人id
    uint64 holder_id = 8;
    // 团队持有人
    string holder_name = 9;
    // 团队类型
    repeated string team_types = 10;
    // 贡献者
    repeated string contributors = 11;
  }
  repeated BindingObject binding_objects = 1;
  uint32 total_count = 2;

}

// ReqDescribeResourceTagBindingInfos 查询资源相关标签关联详情
message ReqDescribeResourceTagBindingInfos {
  message Filter {
    uint64 tag_id = 1;
  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
}

// RspDescribeResourceTagBindingInfos 查询资源相关标签关联详情回复
message RspDescribeResourceTagBindingInfos {
  message BindingObject {
    uint64 id = 1;
    // 资源标题
    string res_name = 2;
    // 资源简述
    string res_introduction = 3;
    // 资源类型
    repeated string res_types = 4;
    // 创建时间
    string create_date = 5;
    // 主办方
    repeated string originator_name = 6;
    // 资源id
    uint64 res_id = 7;
  }
  repeated BindingObject binding_objects = 1;
  uint32 total_count = 2;
}

message ReqDescribeAtlasTagBindingInfos {
  message Filter {
    uint64 tag_id = 1;

  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
}

message RspDescribeAtlasTagBindingInfos {
  message BindingObject {
    uint64 id = 1;
    // 标题
    string atlas_name = 2;
    // 简述
    string atlas_introduction = 3;
    // 图谱类型
    repeated string atlas_types = 4;
    // 创建时间
    string create_date = 5;
    // 发布方
    repeated string pub_name = 6;
    // 图谱id
    uint64 atlas_id = 7;
  }
  repeated BindingObject binding_objects = 1;
  uint32 total_count = 2;
}


// ReqDescribeProductTagBindingInfos 查询产品技术-面向用户标签关联详情
message ReqDescribeProductTagBindingInfos {
  message Filter {
    uint64 tag_id = 1;

  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
}

// RspDescribeProductTagBindingInfos 查询产品技术-面向用户类型标签关联详情回复
message RspDescribeProductTagBindingInfos {
  message BindingObject {
    uint64 id = 1;
    // 产品名称
    string product_name = 2;
    // 一句话简介
    string brief_intro = 3;
    // 产品类型
    repeated string product_types = 4;
    // 团队简称
    string team_short_name = 5;
    // 创建时间
    string create_date = 6;
    // 产品id
    uint64 product_id = 7;
  }
  repeated BindingObject binding_objects = 1;
  uint32 total_count = 2;
}

message ReqDescribeAssistantTagBindingInfos {
  uint64 tag_id = 1 [(tanlive.validator) = "required"];
  uint32 offset = 2;
  uint32 limit = 3;
}

message RspDescribeAssistantTagBindingInfos {
  message Info {
    string name = 1;
    string name_en = 2;
    uint64 id = 3;
    google.protobuf.Timestamp create_date = 4;
    // 创建人
    base.Identity create_by = 5;
    // 渠道
    tanlive.ai.AssistantChannel channel = 6;
    // 是否启用
    bool enabled = 7;
    // 是否草稿
    bool is_draft = 8;
  }
  repeated Info binding_objects = 1;
  uint32 total_count = 2;
}

message ReqBatchImportSystemTag {
  // 导入标签索引数组
  repeated TagIndex tag_index = 1 [(tanlive.validator) = "required,dive"];
  // 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
  tanlive.tag.TagCreateType type = 2;
  // 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
  tanlive.tag.TagCreateType notify_type = 3;
  // 标签类型
  tanlive.tag.TaggableType taggable_type = 4 [(tanlive.validator) = "required,min=1"];
  // 备注
  string remarks = 5 [(tanlive.validator) = "ugc_between=0-100"];
  message TagIndex {
    //索引名称
    string index_name = 1 [(tanlive.validator) = "ugc_between=2-100"];
    //标签名称
    string tag_name = 2 [(tanlive.validator) = "omitempty,ugc_between=2-100"];
  }
}

message ReqGetSystemTags{
  // 标签索引类型
  tanlive.tag.TaggableType taggable_type = 1 [(tanlive.validator) = "min=1"];
  // 语言类型zh、en
  string language = 2;
  // 排序
  repeated string order_by = 3;
  // 定向推送用的筛选条件 1 系统标签
  tanlive.tag.TagCreateType notify_type = 4 [(tanlive.validator) = "min=0,max=1"];
  // 标签名称模糊搜索
  string tag_name = 5;
  // 是否包含 助手用户标签
  bool with_assistant_user_tag = 6;
}

message RspGetSystemTags{
  repeated Tag tag_set = 1;
  message Tag {
    // 标签id
    uint64 id = 1;
    // 标签名称
    string name = 2;
  }
}

message ReqCreatePersonalTagBinding {
  uint64 user_id = 1 [(tanlive.validator) = "required"];
  // 绑定现有的标签
  repeated uint64 tag_ids = 2;
  // 新增的自创标签
  repeated string tag_names = 3;
}

message ReqCreateTeamUserTagBinding {
  uint64 team_id = 1 [(tanlive.validator) = "required"];
  // 绑定现有的标签
  repeated uint64 tag_ids = 2;
  // 新增的自创标签
  repeated string tag_names = 3;
}

message ReqGetNotifyDataByUserLabel {
  tanlive.tag.TaggableType taggable_type = 1 [(tanlive.validator) = "required"];
  repeated uint64 tag_ids = 2 [(tanlive.validator) = "required"];
}

message RspGetNotifyDataByUserLabel {
  message DataInfo {
    uint64 id = 1;
    string name = 2;
    bool is_international = 3;
  }
  repeated DataInfo data_infos = 1;
}

// 标签BFF
service TagBff {
  option (tanlive.bff) = true;

  // 更新系统标签
  rpc UpdateSystemTag(ReqUpdateSystemTag) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/tag/update_system_tag",
      body: "*",
    };
  };

  // 编辑系统标签
  rpc EditSystemTag(ReqEditSystemTag) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/tag/edit_system_tag",
      body: "*",
    };
  };

  // 删除系统标签
  rpc DeleteSystemTag(ReqDeleteSystemTag) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/tag/delete_system_tag",
      body: "*",
    };
  };

  // 合并系统标签
  rpc MergeSystemTags(ReqMergeSystemTags) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/tag/merge_system_tags",
      body: "*",
    };
  };

  // 获取系统索引标签列表
  rpc DescribeSystemTagsByIndex(ReqDescribeSystemTagsByIndex) returns (RspDescribeSystemTagsByIndex){
    option (google.api.http) = {
      post: "/tag/describe_system_tags_by_index",
      body: "*",
    };
  };

  // 查询团队标签关联详情
  rpc DescribeTeamTagBindingInfos(ReqDescribeTeamTagBindingInfos) returns (RspDescribeTeamTagBindingInfos) {
    option (google.api.http) = {
      post: "/tag/describe_team_tag_binding_infos"
      body: "*"
    };
  };

  // 查询资源标签关联详情
  rpc DescribeResourceTagBindingInfos(ReqDescribeResourceTagBindingInfos) returns (RspDescribeResourceTagBindingInfos) {
    option (google.api.http) = {
      post: "/tag/describe_resource_tag_binding_infos"
      body: "*"
    };
  };

  // 查询图谱标签关联详情
  rpc DescribeAtlasTagBindingInfos(ReqDescribeAtlasTagBindingInfos) returns (RspDescribeAtlasTagBindingInfos) {
    option (google.api.http) = {
      post: "/tag/describe_atlas_tag_binding_infos"
      body: "*"
    };
  };

  // 查询产品标签关联详情
  rpc DescribeProductTagBindingInfos(ReqDescribeProductTagBindingInfos) returns (RspDescribeProductTagBindingInfos) {
    option (google.api.http) = {
      post: "/tag/describe_product_tag_binding_infos"
      body: "*"
    };
  };

  // 查询助手-用户自动打标标签关联详情
  rpc DescribeAssistantTagBindingInfos(ReqDescribeAssistantTagBindingInfos) returns (RspDescribeAssistantTagBindingInfos) {
    option (google.api.http) = {
      post: "/tag/describe_assistant_tag_binding_infos"
      body: "*"
    };
  };

  // 批量导入标签
  rpc BatchImportSystemTag(ReqBatchImportSystemTag) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/tag/batch_import_system_tag"
      body: "*"
    };
  };

  // 获取全量系统标签
  rpc GetSystemTags(ReqGetSystemTags) returns (RspGetSystemTags) {
    option (google.api.http) = {
      post: "/tag/get_system_tags"
      body: "*"
    };
  };

  // 创建个人用户标签绑定
  rpc CreatePersonalTagBinding(ReqCreatePersonalTagBinding) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/tag/create_personal_tag_binding"
      body: "*"
    };
  };

  // 创建团队个人用户标签绑定
  rpc CreateTeamUserTagBinding(ReqCreateTeamUserTagBinding) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/tag/create_team_user_tag_binding"
      body: "*"
    };
  };

  // 获取被用户标签绑定的数据信息
  rpc GetNotifyDataByUserLabel(ReqGetNotifyDataByUserLabel) returns (RspGetNotifyDataByUserLabel){
    option (google.api.http) = {
      post: "/tag/get_notify_data_by_user_label"
      body: "*"
    };
  }

}
