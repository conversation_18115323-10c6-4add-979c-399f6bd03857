// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package tag

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	tag "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TagId":        "required,min=1",
		"Weight":       "min=0,max=1000",
		"Type":         "omitempty,min=0,max=2",
		"TaggableType": "required,min=1",
		"NotifyType":   "omitempty,min=0,max=2",
	}, &ReqUpdateSystemTag{})
}

func (x *ReqUpdateSystemTag) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"IndexName":    "ugc_between=2-30",
		"TaggableType": "required,min=1",
	}, &ReqEditSystemTag{})
}

func (x *ReqEditSystemTag) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TagIndexIds":  "required",
		"TaggableType": "required,min=1",
	}, &ReqDeleteSystemTag{})
}

func (x *ReqDeleteSystemTag) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"IndexId":       "required,min=1",
		"MergeIndexIds": "required,min=2",
		"TaggableType":  "required,min=1",
	}, &ReqMergeSystemTags{})
}

func (x *ReqMergeSystemTags) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Filter": "required",
	}, &ReqDescribeSystemTagsByIndex{})
}

func (x *ReqDescribeSystemTagsByIndex) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TaggableType": "required,min=1",
		"Language":     "omitempty,oneof=en zh",
		"Type":         "omitempty,min=0,max=2",
		"NotifyType":   "omitempty,min=0,max=2",
	}, &ReqDescribeSystemTagsByIndex_Filter{})
}

func (x *ReqDescribeSystemTagsByIndex_Filter) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TagId": "required",
	}, &ReqDescribeAssistantTagBindingInfos{})
}

func (x *ReqDescribeAssistantTagBindingInfos) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TagIndex":     "required,dive",
		"TaggableType": "required,min=1",
		"Remarks":      "ugc_between=0-100",
	}, &ReqBatchImportSystemTag{})
}

func (x *ReqBatchImportSystemTag) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"IndexName": "ugc_between=2-100",
		"TagName":   "omitempty,ugc_between=2-100",
	}, &ReqBatchImportSystemTag_TagIndex{})
}

func (x *ReqBatchImportSystemTag_TagIndex) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TaggableType": "min=1",
		"NotifyType":   "min=0,max=1",
	}, &ReqGetSystemTags{})
}

func (x *ReqGetSystemTags) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
	}, &ReqCreatePersonalTagBinding{})
}

func (x *ReqCreatePersonalTagBinding) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TeamId": "required",
	}, &ReqCreateTeamUserTagBinding{})
}

func (x *ReqCreateTeamUserTagBinding) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TaggableType": "required",
		"TagIds":       "required",
	}, &ReqGetNotifyDataByUserLabel{})
}

func (x *ReqGetNotifyDataByUserLabel) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqEditSystemTag) MaskInLog() any {
	if x == nil {
		return (*ReqEditSystemTag)(nil)
	}

	y := proto.Clone(x).(*ReqEditSystemTag)
	if v, ok := any(y.ZhTag).(interface{ MaskInLog() any }); ok {
		y.ZhTag = v.MaskInLog().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInLog() any }); ok {
		y.EnTag = v.MaskInLog().(*Tag)
	}

	return y
}

func (x *ReqEditSystemTag) MaskInRpc() any {
	if x == nil {
		return (*ReqEditSystemTag)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInRpc() any }); ok {
		y.ZhTag = v.MaskInRpc().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInRpc() any }); ok {
		y.EnTag = v.MaskInRpc().(*Tag)
	}

	return y
}

func (x *ReqEditSystemTag) MaskInBff() any {
	if x == nil {
		return (*ReqEditSystemTag)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInBff() any }); ok {
		y.ZhTag = v.MaskInBff().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInBff() any }); ok {
		y.EnTag = v.MaskInBff().(*Tag)
	}

	return y
}

func (x *ReqMergeSystemTags) MaskInLog() any {
	if x == nil {
		return (*ReqMergeSystemTags)(nil)
	}

	y := proto.Clone(x).(*ReqMergeSystemTags)
	if v, ok := any(y.ZhTag).(interface{ MaskInLog() any }); ok {
		y.ZhTag = v.MaskInLog().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInLog() any }); ok {
		y.EnTag = v.MaskInLog().(*Tag)
	}

	return y
}

func (x *ReqMergeSystemTags) MaskInRpc() any {
	if x == nil {
		return (*ReqMergeSystemTags)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInRpc() any }); ok {
		y.ZhTag = v.MaskInRpc().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInRpc() any }); ok {
		y.EnTag = v.MaskInRpc().(*Tag)
	}

	return y
}

func (x *ReqMergeSystemTags) MaskInBff() any {
	if x == nil {
		return (*ReqMergeSystemTags)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInBff() any }); ok {
		y.ZhTag = v.MaskInBff().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInBff() any }); ok {
		y.EnTag = v.MaskInBff().(*Tag)
	}

	return y
}

func (x *ReqDescribeSystemTagsByIndex) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeSystemTagsByIndex)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeSystemTagsByIndex)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeSystemTagsByIndex_Filter)
	}

	return y
}

func (x *ReqDescribeSystemTagsByIndex) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeSystemTagsByIndex)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeSystemTagsByIndex_Filter)
	}

	return y
}

func (x *ReqDescribeSystemTagsByIndex) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeSystemTagsByIndex)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeSystemTagsByIndex_Filter)
	}

	return y
}

func (x *RspDescribeSystemTagsByIndex) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSystemTagsByIndex)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSystemTagsByIndex)
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagSet[k] = vv.MaskInLog().(*tag.TagIndex)
		}
	}

	return y
}

func (x *RspDescribeSystemTagsByIndex) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSystemTagsByIndex)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagSet[k] = vv.MaskInRpc().(*tag.TagIndex)
		}
	}

	return y
}

func (x *RspDescribeSystemTagsByIndex) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSystemTagsByIndex)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagSet[k] = vv.MaskInBff().(*tag.TagIndex)
		}
	}

	return y
}

func (x *ReqDescribeTeamTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeTeamTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeTeamTagBindingInfos)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeTeamTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeTeamTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeTeamTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeTeamTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeTeamTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeTeamTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeTeamTagBindingInfos_Filter)
	}

	return y
}

func (x *RspDescribeTeamTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeTeamTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeTeamTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeTeamTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeTeamTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeTeamTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeTeamTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeTeamTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeTeamTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeTeamTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *ReqDescribeResourceTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeResourceTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeResourceTagBindingInfos)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeResourceTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeResourceTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeResourceTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeResourceTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeResourceTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeResourceTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeResourceTagBindingInfos_Filter)
	}

	return y
}

func (x *RspDescribeResourceTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeResourceTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeResourceTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeResourceTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeResourceTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeResourceTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeResourceTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeResourceTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeResourceTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeResourceTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *ReqDescribeAtlasTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeAtlasTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeAtlasTagBindingInfos)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeAtlasTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeAtlasTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeAtlasTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeAtlasTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeAtlasTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeAtlasTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeAtlasTagBindingInfos_Filter)
	}

	return y
}

func (x *RspDescribeAtlasTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeAtlasTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeAtlasTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeAtlasTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeAtlasTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeAtlasTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeAtlasTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeAtlasTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeAtlasTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeAtlasTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *ReqDescribeProductTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeProductTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeProductTagBindingInfos)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeProductTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeProductTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeProductTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeProductTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeProductTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeProductTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeProductTagBindingInfos_Filter)
	}

	return y
}

func (x *RspDescribeProductTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeProductTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeProductTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeProductTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeProductTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeProductTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeProductTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeProductTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeProductTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeProductTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeAssistantTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeAssistantTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeAssistantTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeAssistantTagBindingInfos_Info)
		}
	}

	return y
}

func (x *RspDescribeAssistantTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeAssistantTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeAssistantTagBindingInfos_Info)
		}
	}

	return y
}

func (x *RspDescribeAssistantTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeAssistantTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeAssistantTagBindingInfos_Info)
		}
	}

	return y
}

func (x *RspDescribeAssistantTagBindingInfos_Info) MaskInLog() any {
	if x == nil {
		return (*RspDescribeAssistantTagBindingInfos_Info)(nil)
	}

	y := proto.Clone(x).(*RspDescribeAssistantTagBindingInfos_Info)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *RspDescribeAssistantTagBindingInfos_Info) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeAssistantTagBindingInfos_Info)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *RspDescribeAssistantTagBindingInfos_Info) MaskInBff() any {
	if x == nil {
		return (*RspDescribeAssistantTagBindingInfos_Info)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqBatchImportSystemTag) MaskInLog() any {
	if x == nil {
		return (*ReqBatchImportSystemTag)(nil)
	}

	y := proto.Clone(x).(*ReqBatchImportSystemTag)
	for k, v := range y.TagIndex {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagIndex[k] = vv.MaskInLog().(*ReqBatchImportSystemTag_TagIndex)
		}
	}

	return y
}

func (x *ReqBatchImportSystemTag) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchImportSystemTag)(nil)
	}

	y := x
	for k, v := range y.TagIndex {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagIndex[k] = vv.MaskInRpc().(*ReqBatchImportSystemTag_TagIndex)
		}
	}

	return y
}

func (x *ReqBatchImportSystemTag) MaskInBff() any {
	if x == nil {
		return (*ReqBatchImportSystemTag)(nil)
	}

	y := x
	for k, v := range y.TagIndex {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagIndex[k] = vv.MaskInBff().(*ReqBatchImportSystemTag_TagIndex)
		}
	}

	return y
}

func (x *RspGetSystemTags) MaskInLog() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := proto.Clone(x).(*RspGetSystemTags)
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagSet[k] = vv.MaskInLog().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *RspGetSystemTags) MaskInRpc() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagSet[k] = vv.MaskInRpc().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *RspGetSystemTags) MaskInBff() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagSet[k] = vv.MaskInBff().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *RspGetNotifyDataByUserLabel) MaskInLog() any {
	if x == nil {
		return (*RspGetNotifyDataByUserLabel)(nil)
	}

	y := proto.Clone(x).(*RspGetNotifyDataByUserLabel)
	for k, v := range y.DataInfos {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.DataInfos[k] = vv.MaskInLog().(*RspGetNotifyDataByUserLabel_DataInfo)
		}
	}

	return y
}

func (x *RspGetNotifyDataByUserLabel) MaskInRpc() any {
	if x == nil {
		return (*RspGetNotifyDataByUserLabel)(nil)
	}

	y := x
	for k, v := range y.DataInfos {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.DataInfos[k] = vv.MaskInRpc().(*RspGetNotifyDataByUserLabel_DataInfo)
		}
	}

	return y
}

func (x *RspGetNotifyDataByUserLabel) MaskInBff() any {
	if x == nil {
		return (*RspGetNotifyDataByUserLabel)(nil)
	}

	y := x
	for k, v := range y.DataInfos {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.DataInfos[k] = vv.MaskInBff().(*RspGetNotifyDataByUserLabel_DataInfo)
		}
	}

	return y
}

func (x *ReqEditSystemTag) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ZhTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EnTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqMergeSystemTags) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ZhTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EnTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqDescribeSystemTagsByIndex) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeSystemTagsByIndex) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagSet {
		if sanitizer, ok := any(x.TagSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeTeamTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeTeamTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeResourceTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeResourceTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeAtlasTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeAtlasTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeProductTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeProductTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeAssistantTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeAssistantTagBindingInfos_Info) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqBatchImportSystemTag) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagIndex {
		if sanitizer, ok := any(x.TagIndex[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetSystemTags) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagSet {
		if sanitizer, ok := any(x.TagSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetNotifyDataByUserLabel) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.DataInfos {
		if sanitizer, ok := any(x.DataInfos[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type TagBffHandler interface {
	// 更新系统标签
	UpdateSystemTag(context.Context, *ReqUpdateSystemTag, *emptypb.Empty) error
	// 编辑系统标签
	EditSystemTag(context.Context, *ReqEditSystemTag, *emptypb.Empty) error
	// 删除系统标签
	DeleteSystemTag(context.Context, *ReqDeleteSystemTag, *emptypb.Empty) error
	// 合并系统标签
	MergeSystemTags(context.Context, *ReqMergeSystemTags, *emptypb.Empty) error
	// 获取系统索引标签列表
	DescribeSystemTagsByIndex(context.Context, *ReqDescribeSystemTagsByIndex, *RspDescribeSystemTagsByIndex) error
	// 查询团队标签关联详情
	DescribeTeamTagBindingInfos(context.Context, *ReqDescribeTeamTagBindingInfos, *RspDescribeTeamTagBindingInfos) error
	// 查询资源标签关联详情
	DescribeResourceTagBindingInfos(context.Context, *ReqDescribeResourceTagBindingInfos, *RspDescribeResourceTagBindingInfos) error
	// 查询图谱标签关联详情
	DescribeAtlasTagBindingInfos(context.Context, *ReqDescribeAtlasTagBindingInfos, *RspDescribeAtlasTagBindingInfos) error
	// 查询产品标签关联详情
	DescribeProductTagBindingInfos(context.Context, *ReqDescribeProductTagBindingInfos, *RspDescribeProductTagBindingInfos) error
	// 查询助手-用户自动打标标签关联详情
	DescribeAssistantTagBindingInfos(context.Context, *ReqDescribeAssistantTagBindingInfos, *RspDescribeAssistantTagBindingInfos) error
	// 批量导入标签
	BatchImportSystemTag(context.Context, *ReqBatchImportSystemTag, *emptypb.Empty) error
	// 获取全量系统标签
	GetSystemTags(context.Context, *ReqGetSystemTags, *RspGetSystemTags) error
	// 创建个人用户标签绑定
	CreatePersonalTagBinding(context.Context, *ReqCreatePersonalTagBinding, *emptypb.Empty) error
	// 创建团队个人用户标签绑定
	CreateTeamUserTagBinding(context.Context, *ReqCreateTeamUserTagBinding, *emptypb.Empty) error
	// 获取被用户标签绑定的数据信息
	GetNotifyDataByUserLabel(context.Context, *ReqGetNotifyDataByUserLabel, *RspGetNotifyDataByUserLabel) error
}

func RegisterTagBff(s bff.Server, h TagBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Tag"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/tag/update_system_tag", h.UpdateSystemTag).Name("UpdateSystemTag")
	bff.AddRoute(group, http.MethodPost, "/tag/edit_system_tag", h.EditSystemTag).Name("EditSystemTag")
	bff.AddRoute(group, http.MethodPost, "/tag/delete_system_tag", h.DeleteSystemTag).Name("DeleteSystemTag")
	bff.AddRoute(group, http.MethodPost, "/tag/merge_system_tags", h.MergeSystemTags).Name("MergeSystemTags")
	bff.AddRoute(group, http.MethodPost, "/tag/describe_system_tags_by_index", h.DescribeSystemTagsByIndex).Name("DescribeSystemTagsByIndex")
	bff.AddRoute(group, http.MethodPost, "/tag/describe_team_tag_binding_infos", h.DescribeTeamTagBindingInfos).Name("DescribeTeamTagBindingInfos")
	bff.AddRoute(group, http.MethodPost, "/tag/describe_resource_tag_binding_infos", h.DescribeResourceTagBindingInfos).Name("DescribeResourceTagBindingInfos")
	bff.AddRoute(group, http.MethodPost, "/tag/describe_atlas_tag_binding_infos", h.DescribeAtlasTagBindingInfos).Name("DescribeAtlasTagBindingInfos")
	bff.AddRoute(group, http.MethodPost, "/tag/describe_product_tag_binding_infos", h.DescribeProductTagBindingInfos).Name("DescribeProductTagBindingInfos")
	bff.AddRoute(group, http.MethodPost, "/tag/describe_assistant_tag_binding_infos", h.DescribeAssistantTagBindingInfos).Name("DescribeAssistantTagBindingInfos")
	bff.AddRoute(group, http.MethodPost, "/tag/batch_import_system_tag", h.BatchImportSystemTag).Name("BatchImportSystemTag")
	bff.AddRoute(group, http.MethodPost, "/tag/get_system_tags", h.GetSystemTags).Name("GetSystemTags")
	bff.AddRoute(group, http.MethodPost, "/tag/create_personal_tag_binding", h.CreatePersonalTagBinding).Name("CreatePersonalTagBinding")
	bff.AddRoute(group, http.MethodPost, "/tag/create_team_user_tag_binding", h.CreateTeamUserTagBinding).Name("CreateTeamUserTagBinding")
	bff.AddRoute(group, http.MethodPost, "/tag/get_notify_data_by_user_label", h.GetNotifyDataByUserLabel).Name("GetNotifyDataByUserLabel")
	return group
}
