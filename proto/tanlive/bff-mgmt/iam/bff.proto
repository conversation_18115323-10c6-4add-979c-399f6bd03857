syntax = "proto3";

package tanlive.bff_mgmt.iam;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/iam";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "tanlive/options.proto";

// IAM BFF
service IamBff {
  option (tanlive.bff) = true;

  // 冻结用户
  rpc DisableUser(ReqDisableUser) returns (RspDisableUser){
    option (google.api.http) = {
      post: "/iam/disable_user"
      body: "*"
    };
  }

  // 解冻用户
  rpc EnableUser(ReqEnableUser) returns (RspEnableUser){
    option (google.api.http) = {
      post: "/iam/enable_user"
      body: "*"
    };
  }
}

message ReqDisableUser {
  // 用户ID
  repeated uint64 user_id = 1 [(validator) = "required,dive,required"];
}

message RspDisableUser {
  message Result {
    int32 code = 1;
  }
  repeated Result results = 1;
}

message ReqEnableUser {
  // 用户ID
  repeated uint64 user_id = 1 [(validator) = "required,dive,required"];
}

message RspEnableUser {
  message Result {
    int32 code = 1;
  }
  repeated Result results = 1;
}
