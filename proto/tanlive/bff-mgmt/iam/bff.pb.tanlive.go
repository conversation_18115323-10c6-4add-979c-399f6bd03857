// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package iam

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	proto "google.golang.org/protobuf/proto"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required,dive,required",
	}, &ReqDisableUser{})
}

func (x *ReqDisableUser) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required,dive,required",
	}, &ReqEnableUser{})
}

func (x *ReqEnableUser) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspDisableUser) MaskInLog() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := proto.Clone(x).(*RspDisableUser)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspDisableUser) MaskInRpc() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspDisableUser) MaskInBff() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInLog() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := proto.Clone(x).(*RspEnableUser)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInRpc() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInBff() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *RspDisableUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspEnableUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type IamBffHandler interface {
	// 冻结用户
	DisableUser(context.Context, *ReqDisableUser, *RspDisableUser) error
	// 解冻用户
	EnableUser(context.Context, *ReqEnableUser, *RspEnableUser) error
}

func RegisterIamBff(s bff.Server, h IamBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Iam"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/iam/disable_user", h.DisableUser).Name("DisableUser")
	bff.AddRoute(group, http.MethodPost, "/iam/enable_user", h.EnableUser).Name("EnableUser")
	return group
}
