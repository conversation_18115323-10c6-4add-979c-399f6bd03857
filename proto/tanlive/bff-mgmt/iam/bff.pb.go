// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/iam/bff.proto

package iam

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqDisableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId []uint64 `protobuf:"varint,1,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqDisableUser) Reset() {
	*x = ReqDisableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDisableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDisableUser) ProtoMessage() {}

func (x *ReqDisableUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDisableUser.ProtoReflect.Descriptor instead.
func (*ReqDisableUser) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_iam_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqDisableUser) GetUserId() []uint64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

type RspDisableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*RspDisableUser_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *RspDisableUser) Reset() {
	*x = RspDisableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDisableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDisableUser) ProtoMessage() {}

func (x *RspDisableUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDisableUser.ProtoReflect.Descriptor instead.
func (*RspDisableUser) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_iam_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspDisableUser) GetResults() []*RspDisableUser_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type ReqEnableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId []uint64 `protobuf:"varint,1,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqEnableUser) Reset() {
	*x = ReqEnableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqEnableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqEnableUser) ProtoMessage() {}

func (x *ReqEnableUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqEnableUser.ProtoReflect.Descriptor instead.
func (*ReqEnableUser) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_iam_bff_proto_rawDescGZIP(), []int{2}
}

func (x *ReqEnableUser) GetUserId() []uint64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

type RspEnableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*RspEnableUser_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *RspEnableUser) Reset() {
	*x = RspEnableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspEnableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspEnableUser) ProtoMessage() {}

func (x *RspEnableUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspEnableUser.ProtoReflect.Descriptor instead.
func (*RspEnableUser) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_iam_bff_proto_rawDescGZIP(), []int{3}
}

func (x *RspEnableUser) GetResults() []*RspEnableUser_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type RspDisableUser_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *RspDisableUser_Result) Reset() {
	*x = RspDisableUser_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDisableUser_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDisableUser_Result) ProtoMessage() {}

func (x *RspDisableUser_Result) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDisableUser_Result.ProtoReflect.Descriptor instead.
func (*RspDisableUser_Result) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_iam_bff_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RspDisableUser_Result) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type RspEnableUser_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *RspEnableUser_Result) Reset() {
	*x = RspEnableUser_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspEnableUser_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspEnableUser_Result) ProtoMessage() {}

func (x *RspEnableUser_Result) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspEnableUser_Result.ProtoReflect.Descriptor instead.
func (*RspEnableUser_Result) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_iam_bff_proto_rawDescGZIP(), []int{3, 0}
}

func (x *RspEnableUser_Result) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_tanlive_bff_mgmt_iam_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_iam_bff_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x14, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x69, 0x61, 0x6d, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x45, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x1a, 0x82, 0x88, 0x27,
	0x16, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x75, 0x0a, 0x0e, 0x52, 0x73, 0x70, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x45, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x1c, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x44, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x1a, 0x82, 0x88, 0x27, 0x16, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x73, 0x0a, 0x0d,
	0x52, 0x73, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x44, 0x0a,
	0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x73, 0x1a, 0x1c, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x32, 0xfc, 0x01, 0x0a, 0x06, 0x49, 0x61, 0x6d, 0x42, 0x66, 0x66, 0x12, 0x77, 0x0a, 0x0b,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x24, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x1a, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a,
	0x01, 0x2a, 0x22, 0x11, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x73, 0x0a, 0x0a, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x1a, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x73, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x22, 0x1b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x15, 0x3a, 0x01, 0x2a, 0x22, 0x10, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01,
	0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74,
	0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x69, 0x61, 0x6d,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_iam_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_iam_bff_proto_rawDescData = file_tanlive_bff_mgmt_iam_bff_proto_rawDesc
)

func file_tanlive_bff_mgmt_iam_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_iam_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_iam_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_iam_bff_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_iam_bff_proto_rawDescData
}

var file_tanlive_bff_mgmt_iam_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_tanlive_bff_mgmt_iam_bff_proto_goTypes = []interface{}{
	(*ReqDisableUser)(nil),        // 0: tanlive.bff_mgmt.iam.ReqDisableUser
	(*RspDisableUser)(nil),        // 1: tanlive.bff_mgmt.iam.RspDisableUser
	(*ReqEnableUser)(nil),         // 2: tanlive.bff_mgmt.iam.ReqEnableUser
	(*RspEnableUser)(nil),         // 3: tanlive.bff_mgmt.iam.RspEnableUser
	(*RspDisableUser_Result)(nil), // 4: tanlive.bff_mgmt.iam.RspDisableUser.Result
	(*RspEnableUser_Result)(nil),  // 5: tanlive.bff_mgmt.iam.RspEnableUser.Result
}
var file_tanlive_bff_mgmt_iam_bff_proto_depIdxs = []int32{
	4, // 0: tanlive.bff_mgmt.iam.RspDisableUser.results:type_name -> tanlive.bff_mgmt.iam.RspDisableUser.Result
	5, // 1: tanlive.bff_mgmt.iam.RspEnableUser.results:type_name -> tanlive.bff_mgmt.iam.RspEnableUser.Result
	0, // 2: tanlive.bff_mgmt.iam.IamBff.DisableUser:input_type -> tanlive.bff_mgmt.iam.ReqDisableUser
	2, // 3: tanlive.bff_mgmt.iam.IamBff.EnableUser:input_type -> tanlive.bff_mgmt.iam.ReqEnableUser
	1, // 4: tanlive.bff_mgmt.iam.IamBff.DisableUser:output_type -> tanlive.bff_mgmt.iam.RspDisableUser
	3, // 5: tanlive.bff_mgmt.iam.IamBff.EnableUser:output_type -> tanlive.bff_mgmt.iam.RspEnableUser
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_iam_bff_proto_init() }
func file_tanlive_bff_mgmt_iam_bff_proto_init() {
	if File_tanlive_bff_mgmt_iam_bff_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDisableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDisableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqEnableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspEnableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDisableUser_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_iam_bff_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspEnableUser_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_iam_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_bff_mgmt_iam_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_iam_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_iam_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_iam_bff_proto = out.File
	file_tanlive_bff_mgmt_iam_bff_proto_rawDesc = nil
	file_tanlive_bff_mgmt_iam_bff_proto_goTypes = nil
	file_tanlive_bff_mgmt_iam_bff_proto_depIdxs = nil
}
