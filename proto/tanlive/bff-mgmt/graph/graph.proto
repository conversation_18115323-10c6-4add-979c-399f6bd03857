syntax = "proto3";

package tanlive.bff_mgmt.graph;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/graph";

import "google/protobuf/timestamp.proto";


message ProcessInfo {
  // 流程名称
  string process_name = 1;
  // 流程类别 1图谱 2资源 3团队 4产品
  uint32 category = 2;
  // 备注
  string remark = 3;
  // 创建人
  string creator = 4;
  // 创建时间
  google.protobuf.Timestamp create_date = 5;
  // 修改时间
  google.protobuf.Timestamp update_date = 6;
  uint64 id = 7;
  string update_user = 8;
  string yaml_config = 9;
  uint32 is_online_version = 10;
  uint32 lang = 11;
}

message VersionInfo {
  // 流程名称
  string process_name = 1;
  // 流程类别
  uint32 category = 2;
  // 备注
  string remark = 3;
  // 版本号
  string version_number = 4;
  // 是否线上版本
  bool is_online_version = 5;
  // 创建人
  string creator = 6;
  // 创建时间
  google.protobuf.Timestamp create_time = 7;
  // 修改时间
  google.protobuf.Timestamp update_time = 8;
  uint64 id = 9;
}