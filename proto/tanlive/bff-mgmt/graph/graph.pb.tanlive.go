// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package graph

import (
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func (x *ProcessInfo) MaskInLog() any {
	if x == nil {
		return (*ProcessInfo)(nil)
	}

	y := proto.Clone(x).(*ProcessInfo)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ProcessInfo) MaskInRpc() any {
	if x == nil {
		return (*ProcessInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ProcessInfo) MaskInBff() any {
	if x == nil {
		return (*ProcessInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *VersionInfo) MaskInLog() any {
	if x == nil {
		return (*VersionInfo)(nil)
	}

	y := proto.Clone(x).(*VersionInfo)
	if v, ok := any(y.CreateTime).(interface{ MaskInLog() any }); ok {
		y.CreateTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateTime).(interface{ MaskInLog() any }); ok {
		y.UpdateTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *VersionInfo) MaskInRpc() any {
	if x == nil {
		return (*VersionInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateTime).(interface{ MaskInRpc() any }); ok {
		y.CreateTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateTime).(interface{ MaskInRpc() any }); ok {
		y.UpdateTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *VersionInfo) MaskInBff() any {
	if x == nil {
		return (*VersionInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateTime).(interface{ MaskInBff() any }); ok {
		y.CreateTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateTime).(interface{ MaskInBff() any }); ok {
		y.UpdateTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ProcessInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *VersionInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
