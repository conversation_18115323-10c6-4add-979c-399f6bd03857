syntax = "proto3";

package tanlive.bff_mgmt.graph;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/graph";

import "google/api/annotations.proto";
import "tanlive/bff-mgmt/graph/graph.proto";
import "tanlive/options.proto";
import "google/protobuf/empty.proto";


// 流程配置列表请求
message ReqDescribeListProcess {
  // 搜索名称
  string search_name = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated string order_by = 4;
  uint64 id = 5;
}

// 流程配置列表响应
message RspDescribeListProcess {
  uint32 total_count = 1;
  repeated ProcessInfo processes = 2;
}

// 流程引擎添加请求
message ReqCreateProcessEngine {
  // 流程名称
  string process_name = 1  [(tanlive.validator) = "required"];
  // 流程类别 1图谱 2资源 3团队 4产品
  uint32 category = 2;
  // YAML配置
  string yaml_config = 3;
  // 备注
  string remark = 4;
  uint32 lang = 5;
}

// 版本列表请求
message ReqDescribeListVersions {
  // 搜索名称
  string search_name = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated string order_by = 4;
  uint64 id = 5;
}

// 版本列表响应
message RspDescribeListVersions {
  uint32 total_count = 1;
  repeated VersionInfo versions = 2;
}

// 流程引擎编辑请求
message ReqModifyProcessEngine {
  // 流程名称
  string process_name = 1  [(tanlive.validator) = "required"];
  // 流程类别
  uint32 category = 2;
  // YAML配置
  string yaml_config = 3;
  // 备注
  string remark = 4;
  uint64 id = 5  [(tanlive.validator) = "required"];
  uint32 lang = 6;
}

message ReqModifyEnableVersion {
  uint64 id = 1  [(tanlive.validator) = "required"];
  // 1启用 2禁用
  bool is_online_version = 2;
}

message ReqDeleteProcessEngine {
  uint64 id = 1  [(tanlive.validator) = "required"];
}

service GraphBff {
  option (tanlive.bff) = true;

  // 流程配置列表
  rpc DescribeListProcess(ReqDescribeListProcess) returns (RspDescribeListProcess) {
    option (google.api.http) = {
      post: "/graph/describe_process_list"
      body: "*"
    };
  }

  // 流程引擎添加
  rpc CreateProcessEngine(ReqCreateProcessEngine) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/graph/create_process_engine"
      body: "*"
    };
  }

  // 流程引擎编辑
  rpc ModifyProcessEngine(ReqModifyProcessEngine) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/graph/modify_process_engine"
      body: "*"
    };
  }

  // 版本启用
  rpc ModifyEnableVersion(ReqModifyEnableVersion) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/graph/modify_enable_version"
      body: "*"
    };
  }

  // 流程引擎删除
  rpc DeleteProcessEngine(ReqDeleteProcessEngine) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/graph/delete_process_engine"
      body: "*"
    };
  }

}
