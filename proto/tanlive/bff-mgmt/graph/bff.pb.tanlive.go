// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package graph

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ProcessName": "required",
	}, &ReqCreateProcessEngine{})
}

func (x *ReqCreateProcessEngine) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ProcessName": "required",
		"Id":          "required",
	}, &ReqModifyProcessEngine{})
}

func (x *ReqModifyProcessEngine) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqModifyEnableVersion{})
}

func (x *ReqModifyEnableVersion) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqDeleteProcessEngine{})
}

func (x *ReqDeleteProcessEngine) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspDescribeListProcess) MaskInLog() any {
	if x == nil {
		return (*RspDescribeListProcess)(nil)
	}

	y := proto.Clone(x).(*RspDescribeListProcess)
	for k, v := range y.Processes {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Processes[k] = vv.MaskInLog().(*ProcessInfo)
		}
	}

	return y
}

func (x *RspDescribeListProcess) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeListProcess)(nil)
	}

	y := x
	for k, v := range y.Processes {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Processes[k] = vv.MaskInRpc().(*ProcessInfo)
		}
	}

	return y
}

func (x *RspDescribeListProcess) MaskInBff() any {
	if x == nil {
		return (*RspDescribeListProcess)(nil)
	}

	y := x
	for k, v := range y.Processes {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Processes[k] = vv.MaskInBff().(*ProcessInfo)
		}
	}

	return y
}

func (x *RspDescribeListVersions) MaskInLog() any {
	if x == nil {
		return (*RspDescribeListVersions)(nil)
	}

	y := proto.Clone(x).(*RspDescribeListVersions)
	for k, v := range y.Versions {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Versions[k] = vv.MaskInLog().(*VersionInfo)
		}
	}

	return y
}

func (x *RspDescribeListVersions) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeListVersions)(nil)
	}

	y := x
	for k, v := range y.Versions {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Versions[k] = vv.MaskInRpc().(*VersionInfo)
		}
	}

	return y
}

func (x *RspDescribeListVersions) MaskInBff() any {
	if x == nil {
		return (*RspDescribeListVersions)(nil)
	}

	y := x
	for k, v := range y.Versions {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Versions[k] = vv.MaskInBff().(*VersionInfo)
		}
	}

	return y
}

func (x *RspDescribeListProcess) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Processes {
		if sanitizer, ok := any(x.Processes[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeListVersions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Versions {
		if sanitizer, ok := any(x.Versions[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type GraphBffHandler interface {
	// 流程配置列表
	DescribeListProcess(context.Context, *ReqDescribeListProcess, *RspDescribeListProcess) error
	// 流程引擎添加
	CreateProcessEngine(context.Context, *ReqCreateProcessEngine, *emptypb.Empty) error
	// 流程引擎编辑
	ModifyProcessEngine(context.Context, *ReqModifyProcessEngine, *emptypb.Empty) error
	// 版本启用
	ModifyEnableVersion(context.Context, *ReqModifyEnableVersion, *emptypb.Empty) error
	// 流程引擎删除
	DeleteProcessEngine(context.Context, *ReqDeleteProcessEngine, *emptypb.Empty) error
}

func RegisterGraphBff(s bff.Server, h GraphBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Graph"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/graph/describe_process_list", h.DescribeListProcess).Name("DescribeListProcess")
	bff.AddRoute(group, http.MethodPost, "/graph/create_process_engine", h.CreateProcessEngine).Name("CreateProcessEngine")
	bff.AddRoute(group, http.MethodPost, "/graph/modify_process_engine", h.ModifyProcessEngine).Name("ModifyProcessEngine")
	bff.AddRoute(group, http.MethodPost, "/graph/modify_enable_version", h.ModifyEnableVersion).Name("ModifyEnableVersion")
	bff.AddRoute(group, http.MethodPost, "/graph/delete_process_engine", h.DeleteProcessEngine).Name("DeleteProcessEngine")
	return group
}
