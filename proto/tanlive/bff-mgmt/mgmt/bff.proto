syntax = "proto3";

package tanlive.bff_mgmt.mgmt;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/mgmt";

import "google/api/annotations.proto";
import "tanlive/base/tcloud.proto";
import "tanlive/bff-mgmt/mgmt/mgmt.proto";
import "tanlive/mgmt/mgmt.proto";
import "tanlive/options.proto";
import "google/protobuf/empty.proto";

message ReqLogin {
  // 用户名
  string username = 1 [(tanlive.validator) = "required", (tanlive.mask).rule = "name"];
  // 密码
  string password = 2 [(tanlive.validator) = "required", (tanlive.mask).rule = "secret"];
  // 腾讯云验证码参数
  tanlive.base.TcloudCaptcha tcloud_captcha = 3 [(tanlive.validator) = "required"];
}

message RspLogin {
  message UserInfo {
    // 用户ID
    uint64 user_id = 1;
    // 用户名
    string user_name = 2 [(tanlive.mask) = {rule: "name", disable_bff: true}];
    // 备注名
    string remark_name = 3 [(tanlive.mask) = {rule: "name", disable_bff: true}];
  }
  UserInfo user_info = 1;
}

service AuthBff {
  option (tanlive.bff) = true;

  // 登录
  rpc Login(ReqLogin) returns (RspLogin) {
    option (google.api.http) = {
      post: "/op/login",
      body: "*",
    };
  };

  // 登出
  rpc Logout(google.protobuf.Empty) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/op/logout",
      body: "*",
    };
  };
}

message ReqGetRoles {
  string name = 1;
  uint64 state = 2;
  uint64 type = 3;
}

message RspGetRoles {
  repeated OpRole roles = 1;
}

message ReqTextTranslate {
  string text = 1 [(tanlive.validator) = "required"];
  string source_language = 2;
  string target_language = 3 [(tanlive.validator) = "required"];
}

message RspTextTranslate {
  string text = 1;
}

message ReqSearchUsers {
  // 分页偏移量
  uint32 offset = 1;
  // 分页大小
  uint32 limit = 2;
  // 搜索关键词
  string keyword = 3;
}

message RspSearchUsers {
  // 用户列表
  repeated tanlive.mgmt.OpUser users = 1;
  // 总数
  uint32 total_count = 2;
}

service MgmtBff {
  option (tanlive.bff) = true;

  // 查询角色列表
  rpc GetRoles(ReqGetRoles) returns (RspGetRoles){
    option (google.api.http) = {
      post: "/op/role_get"
      body: "*"
    };
  };

  // 翻译
  rpc TextTranslate(ReqTextTranslate) returns (RspTextTranslate){
    option (google.api.http) = {
      post: "/op/text_translate"
      body: "*"
    };
  };

  // 搜索运营端用户列表
  rpc SearchUsers(ReqSearchUsers) returns (RspSearchUsers){
    option (google.api.http) = {
      post: "/mgmt/search_users"
      body: "*"
    };
  };
}
