// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/mgmt/mgmt.proto

package mgmt

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 角色
type OpRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RoleName       string `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	State          uint64 `protobuf:"varint,3,opt,name=state,proto3" json:"state,omitempty"`
	Type           uint64 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	AccountCount   uint64 `protobuf:"varint,5,opt,name=account_count,json=accountCount,proto3" json:"account_count,omitempty"`
	CreateBy       uint64 `protobuf:"varint,6,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	CreateDate     string `protobuf:"bytes,7,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	LastUpdateBy   uint64 `protobuf:"varint,8,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	LastUpdateDate string `protobuf:"bytes,9,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
	CouldDel       bool   `protobuf:"varint,10,opt,name=could_del,json=couldDel,proto3" json:"could_del,omitempty"`
}

func (x *OpRole) Reset() {
	*x = OpRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_mgmt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpRole) ProtoMessage() {}

func (x *OpRole) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_mgmt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpRole.ProtoReflect.Descriptor instead.
func (*OpRole) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDescGZIP(), []int{0}
}

func (x *OpRole) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OpRole) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *OpRole) GetState() uint64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *OpRole) GetType() uint64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *OpRole) GetAccountCount() uint64 {
	if x != nil {
		return x.AccountCount
	}
	return 0
}

func (x *OpRole) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *OpRole) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *OpRole) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *OpRole) GetLastUpdateDate() string {
	if x != nil {
		return x.LastUpdateDate
	}
	return ""
}

func (x *OpRole) GetCouldDel() bool {
	if x != nil {
		return x.CouldDel
	}
	return false
}

var File_tanlive_bff_mgmt_mgmt_mgmt_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDesc = []byte{
	0x0a, 0x20, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x22, 0xaf, 0x02, 0x0a, 0x06, 0x4f, 0x70,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24,
	0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x63, 0x6f, 0x75, 0x6c, 0x64, 0x44, 0x65, 0x6c, 0x42, 0x3f, 0x5a, 0x3d, 0x65,
	0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62,
	0x66, 0x66, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDescData = file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDesc
)

func file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDescData
}

var file_tanlive_bff_mgmt_mgmt_mgmt_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tanlive_bff_mgmt_mgmt_mgmt_proto_goTypes = []interface{}{
	(*OpRole)(nil), // 0: tanlive.bff_mgmt.mgmt.OpRole
}
var file_tanlive_bff_mgmt_mgmt_mgmt_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_mgmt_mgmt_proto_init() }
func file_tanlive_bff_mgmt_mgmt_mgmt_proto_init() {
	if File_tanlive_bff_mgmt_mgmt_mgmt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_mgmt_mgmt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_bff_mgmt_mgmt_mgmt_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_mgmt_mgmt_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_mgmt_mgmt_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_mgmt_mgmt_proto = out.File
	file_tanlive_bff_mgmt_mgmt_mgmt_proto_rawDesc = nil
	file_tanlive_bff_mgmt_mgmt_mgmt_proto_goTypes = nil
	file_tanlive_bff_mgmt_mgmt_mgmt_proto_depIdxs = nil
}
