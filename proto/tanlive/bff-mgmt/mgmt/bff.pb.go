// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/mgmt/bff.proto

package mgmt

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	mgmt "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqLogin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户名
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// 密码
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// 腾讯云验证码参数
	TcloudCaptcha *base.TcloudCaptcha `protobuf:"bytes,3,opt,name=tcloud_captcha,json=tcloudCaptcha,proto3" json:"tcloud_captcha,omitempty"`
}

func (x *ReqLogin) Reset() {
	*x = ReqLogin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqLogin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqLogin) ProtoMessage() {}

func (x *ReqLogin) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqLogin.ProtoReflect.Descriptor instead.
func (*ReqLogin) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqLogin) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ReqLogin) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ReqLogin) GetTcloudCaptcha() *base.TcloudCaptcha {
	if x != nil {
		return x.TcloudCaptcha
	}
	return nil
}

type RspLogin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo *RspLogin_UserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
}

func (x *RspLogin) Reset() {
	*x = RspLogin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspLogin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspLogin) ProtoMessage() {}

func (x *RspLogin) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspLogin.ProtoReflect.Descriptor instead.
func (*RspLogin) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspLogin) GetUserInfo() *RspLogin_UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type ReqGetRoles struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	State uint64 `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"`
	Type  uint64 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *ReqGetRoles) Reset() {
	*x = ReqGetRoles{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetRoles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetRoles) ProtoMessage() {}

func (x *ReqGetRoles) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetRoles.ProtoReflect.Descriptor instead.
func (*ReqGetRoles) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{2}
}

func (x *ReqGetRoles) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReqGetRoles) GetState() uint64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *ReqGetRoles) GetType() uint64 {
	if x != nil {
		return x.Type
	}
	return 0
}

type RspGetRoles struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roles []*OpRole `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
}

func (x *RspGetRoles) Reset() {
	*x = RspGetRoles{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetRoles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetRoles) ProtoMessage() {}

func (x *RspGetRoles) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetRoles.ProtoReflect.Descriptor instead.
func (*RspGetRoles) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{3}
}

func (x *RspGetRoles) GetRoles() []*OpRole {
	if x != nil {
		return x.Roles
	}
	return nil
}

type ReqTextTranslate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text           string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	SourceLanguage string `protobuf:"bytes,2,opt,name=source_language,json=sourceLanguage,proto3" json:"source_language,omitempty"`
	TargetLanguage string `protobuf:"bytes,3,opt,name=target_language,json=targetLanguage,proto3" json:"target_language,omitempty"`
}

func (x *ReqTextTranslate) Reset() {
	*x = ReqTextTranslate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqTextTranslate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqTextTranslate) ProtoMessage() {}

func (x *ReqTextTranslate) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqTextTranslate.ProtoReflect.Descriptor instead.
func (*ReqTextTranslate) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{4}
}

func (x *ReqTextTranslate) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ReqTextTranslate) GetSourceLanguage() string {
	if x != nil {
		return x.SourceLanguage
	}
	return ""
}

func (x *ReqTextTranslate) GetTargetLanguage() string {
	if x != nil {
		return x.TargetLanguage
	}
	return ""
}

type RspTextTranslate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *RspTextTranslate) Reset() {
	*x = RspTextTranslate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspTextTranslate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspTextTranslate) ProtoMessage() {}

func (x *RspTextTranslate) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspTextTranslate.ProtoReflect.Descriptor instead.
func (*RspTextTranslate) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{5}
}

func (x *RspTextTranslate) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type ReqSearchUsers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页偏移量
	Offset uint32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// 分页大小
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 搜索关键词
	Keyword string `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty"`
}

func (x *ReqSearchUsers) Reset() {
	*x = ReqSearchUsers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchUsers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchUsers) ProtoMessage() {}

func (x *ReqSearchUsers) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchUsers.ProtoReflect.Descriptor instead.
func (*ReqSearchUsers) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{6}
}

func (x *ReqSearchUsers) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqSearchUsers) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqSearchUsers) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type RspSearchUsers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户列表
	Users []*mgmt.OpUser `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	// 总数
	TotalCount uint32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspSearchUsers) Reset() {
	*x = RspSearchUsers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSearchUsers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSearchUsers) ProtoMessage() {}

func (x *RspSearchUsers) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSearchUsers.ProtoReflect.Descriptor instead.
func (*RspSearchUsers) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{7}
}

func (x *RspSearchUsers) GetUsers() []*mgmt.OpUser {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *RspSearchUsers) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type RspLogin_UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户名
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	// 备注名
	RemarkName string `protobuf:"bytes,3,opt,name=remark_name,json=remarkName,proto3" json:"remark_name,omitempty"`
}

func (x *RspLogin_UserInfo) Reset() {
	*x = RspLogin_UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspLogin_UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspLogin_UserInfo) ProtoMessage() {}

func (x *RspLogin_UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspLogin_UserInfo.ProtoReflect.Descriptor instead.
func (*RspLogin_UserInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RspLogin_UserInfo) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RspLogin_UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *RspLogin_UserInfo) GetRemarkName() string {
	if x != nil {
		return x.RemarkName
	}
	return ""
}

var File_tanlive_bff_mgmt_mgmt_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_mgmt_bff_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x62, 0x61, 0x73, 0x65, 0x2f, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x20, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d,
	0x67, 0x6d, 0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x67, 0x6d,
	0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xc6, 0x01, 0x0a, 0x08, 0x52, 0x65, 0x71, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x32, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x16, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27,
	0x06, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x34, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x50, 0x0a, 0x0e, 0x74, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0d, 0x74, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x22, 0xd0, 0x01, 0x0a, 0x08, 0x52, 0x73,
	0x70, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x45, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x52, 0x73, 0x70, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x7d, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x28, 0x01, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a,
	0x0b, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0c, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x28, 0x01,
	0x52, 0x0a, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4b, 0x0a, 0x0b,
	0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x42, 0x0a, 0x0b, 0x52, 0x73, 0x70,
	0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x4f, 0x70, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x94, 0x01,
	0x0a, 0x10, 0x52, 0x65, 0x71, 0x54, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x20, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x35, 0x0a,
	0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x22, 0x26, 0x0a, 0x10, 0x52, 0x73, 0x70, 0x54, 0x65, 0x78, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0x58, 0x0a, 0x0e,
	0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x5d, 0x0a, 0x0e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x4f, 0x70, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0xc1, 0x01, 0x0a, 0x07, 0x41, 0x75, 0x74, 0x68, 0x42, 0x66,
	0x66, 0x12, 0x5f, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x1a, 0x1f, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x22, 0x14, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0e, 0x3a, 0x01, 0x2a, 0x22, 0x09, 0x2f, 0x6f, 0x70, 0x2f, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x12, 0x4f, 0x0a, 0x06, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x15, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x6f, 0x70, 0x2f, 0x6c, 0x6f, 0x67,
	0x6f, 0x75, 0x74, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x32, 0xfb, 0x02, 0x0a, 0x07, 0x4d, 0x67,
	0x6d, 0x74, 0x42, 0x66, 0x66, 0x12, 0x6b, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65,
	0x73, 0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x6c, 0x65, 0x73, 0x1a, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x73,
	0x70, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x11, 0x3a, 0x01, 0x2a, 0x22, 0x0c, 0x2f, 0x6f, 0x70, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x67,
	0x65, 0x74, 0x12, 0x80, 0x01, 0x0a, 0x0d, 0x54, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x65, 0x71,
	0x54, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x1a, 0x27, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x54, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01,
	0x2a, 0x22, 0x12, 0x2f, 0x6f, 0x70, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x7a, 0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x52, 0x65, 0x71,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x25, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22, 0x12, 0x2f,
	0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x42, 0x3f, 0x5a, 0x3d, 0x65, 0x2e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d,
	0x67, 0x6d, 0x74, 0x2f, 0x6d, 0x67, 0x6d, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescData = file_tanlive_bff_mgmt_mgmt_bff_proto_rawDesc
)

func file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_mgmt_bff_proto_rawDescData
}

var file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_tanlive_bff_mgmt_mgmt_bff_proto_goTypes = []interface{}{
	(*ReqLogin)(nil),           // 0: tanlive.bff_mgmt.mgmt.ReqLogin
	(*RspLogin)(nil),           // 1: tanlive.bff_mgmt.mgmt.RspLogin
	(*ReqGetRoles)(nil),        // 2: tanlive.bff_mgmt.mgmt.ReqGetRoles
	(*RspGetRoles)(nil),        // 3: tanlive.bff_mgmt.mgmt.RspGetRoles
	(*ReqTextTranslate)(nil),   // 4: tanlive.bff_mgmt.mgmt.ReqTextTranslate
	(*RspTextTranslate)(nil),   // 5: tanlive.bff_mgmt.mgmt.RspTextTranslate
	(*ReqSearchUsers)(nil),     // 6: tanlive.bff_mgmt.mgmt.ReqSearchUsers
	(*RspSearchUsers)(nil),     // 7: tanlive.bff_mgmt.mgmt.RspSearchUsers
	(*RspLogin_UserInfo)(nil),  // 8: tanlive.bff_mgmt.mgmt.RspLogin.UserInfo
	(*base.TcloudCaptcha)(nil), // 9: tanlive.base.TcloudCaptcha
	(*OpRole)(nil),             // 10: tanlive.bff_mgmt.mgmt.OpRole
	(*mgmt.OpUser)(nil),        // 11: tanlive.mgmt.OpUser
	(*emptypb.Empty)(nil),      // 12: google.protobuf.Empty
}
var file_tanlive_bff_mgmt_mgmt_bff_proto_depIdxs = []int32{
	9,  // 0: tanlive.bff_mgmt.mgmt.ReqLogin.tcloud_captcha:type_name -> tanlive.base.TcloudCaptcha
	8,  // 1: tanlive.bff_mgmt.mgmt.RspLogin.user_info:type_name -> tanlive.bff_mgmt.mgmt.RspLogin.UserInfo
	10, // 2: tanlive.bff_mgmt.mgmt.RspGetRoles.roles:type_name -> tanlive.bff_mgmt.mgmt.OpRole
	11, // 3: tanlive.bff_mgmt.mgmt.RspSearchUsers.users:type_name -> tanlive.mgmt.OpUser
	0,  // 4: tanlive.bff_mgmt.mgmt.AuthBff.Login:input_type -> tanlive.bff_mgmt.mgmt.ReqLogin
	12, // 5: tanlive.bff_mgmt.mgmt.AuthBff.Logout:input_type -> google.protobuf.Empty
	2,  // 6: tanlive.bff_mgmt.mgmt.MgmtBff.GetRoles:input_type -> tanlive.bff_mgmt.mgmt.ReqGetRoles
	4,  // 7: tanlive.bff_mgmt.mgmt.MgmtBff.TextTranslate:input_type -> tanlive.bff_mgmt.mgmt.ReqTextTranslate
	6,  // 8: tanlive.bff_mgmt.mgmt.MgmtBff.SearchUsers:input_type -> tanlive.bff_mgmt.mgmt.ReqSearchUsers
	1,  // 9: tanlive.bff_mgmt.mgmt.AuthBff.Login:output_type -> tanlive.bff_mgmt.mgmt.RspLogin
	12, // 10: tanlive.bff_mgmt.mgmt.AuthBff.Logout:output_type -> google.protobuf.Empty
	3,  // 11: tanlive.bff_mgmt.mgmt.MgmtBff.GetRoles:output_type -> tanlive.bff_mgmt.mgmt.RspGetRoles
	5,  // 12: tanlive.bff_mgmt.mgmt.MgmtBff.TextTranslate:output_type -> tanlive.bff_mgmt.mgmt.RspTextTranslate
	7,  // 13: tanlive.bff_mgmt.mgmt.MgmtBff.SearchUsers:output_type -> tanlive.bff_mgmt.mgmt.RspSearchUsers
	9,  // [9:14] is the sub-list for method output_type
	4,  // [4:9] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_mgmt_bff_proto_init() }
func file_tanlive_bff_mgmt_mgmt_bff_proto_init() {
	if File_tanlive_bff_mgmt_mgmt_bff_proto != nil {
		return
	}
	file_tanlive_bff_mgmt_mgmt_mgmt_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqLogin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspLogin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetRoles); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetRoles); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqTextTranslate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspTextTranslate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchUsers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSearchUsers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspLogin_UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_mgmt_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_tanlive_bff_mgmt_mgmt_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_mgmt_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_mgmt_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_mgmt_bff_proto = out.File
	file_tanlive_bff_mgmt_mgmt_bff_proto_rawDesc = nil
	file_tanlive_bff_mgmt_mgmt_bff_proto_goTypes = nil
	file_tanlive_bff_mgmt_mgmt_bff_proto_depIdxs = nil
}
