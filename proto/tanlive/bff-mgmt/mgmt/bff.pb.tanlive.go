// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package mgmt

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	mgmt "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Username":      "required",
		"Password":      "required",
		"TcloudCaptcha": "required",
	}, &ReqLogin{})
}

func (x *ReqLogin) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text":           "required",
		"TargetLanguage": "required",
	}, &ReqTextTranslate{})
}

func (x *ReqTextTranslate) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqLogin) MaskInLog() any {
	if x == nil {
		return (*ReqLogin)(nil)
	}

	y := proto.Clone(x).(*ReqLogin)
	y.Username = mask.Mask(y.Username, "name")
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInLog() any }); ok {
		y.TcloudCaptcha = v.MaskInLog().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqLogin) MaskInRpc() any {
	if x == nil {
		return (*ReqLogin)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "name")
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInRpc() any }); ok {
		y.TcloudCaptcha = v.MaskInRpc().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqLogin) MaskInBff() any {
	if x == nil {
		return (*ReqLogin)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "name")
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInBff() any }); ok {
		y.TcloudCaptcha = v.MaskInBff().(*base.TcloudCaptcha)
	}

	return y
}

func (x *RspLogin) MaskInLog() any {
	if x == nil {
		return (*RspLogin)(nil)
	}

	y := proto.Clone(x).(*RspLogin)
	if v, ok := any(y.UserInfo).(interface{ MaskInLog() any }); ok {
		y.UserInfo = v.MaskInLog().(*RspLogin_UserInfo)
	}

	return y
}

func (x *RspLogin) MaskInRpc() any {
	if x == nil {
		return (*RspLogin)(nil)
	}

	y := x
	if v, ok := any(y.UserInfo).(interface{ MaskInRpc() any }); ok {
		y.UserInfo = v.MaskInRpc().(*RspLogin_UserInfo)
	}

	return y
}

func (x *RspLogin) MaskInBff() any {
	if x == nil {
		return (*RspLogin)(nil)
	}

	y := x
	if v, ok := any(y.UserInfo).(interface{ MaskInBff() any }); ok {
		y.UserInfo = v.MaskInBff().(*RspLogin_UserInfo)
	}

	return y
}

func (x *RspLogin_UserInfo) MaskInLog() any {
	if x == nil {
		return (*RspLogin_UserInfo)(nil)
	}

	y := proto.Clone(x).(*RspLogin_UserInfo)
	y.UserName = mask.Mask(y.UserName, "name")
	y.RemarkName = mask.Mask(y.RemarkName, "name")

	return y
}

func (x *RspLogin_UserInfo) MaskInRpc() any {
	if x == nil {
		return (*RspLogin_UserInfo)(nil)
	}

	y := x
	y.UserName = mask.Mask(y.UserName, "name")
	y.RemarkName = mask.Mask(y.RemarkName, "name")

	return y
}

func (x *RspGetRoles) MaskInLog() any {
	if x == nil {
		return (*RspGetRoles)(nil)
	}

	y := proto.Clone(x).(*RspGetRoles)
	for k, v := range y.Roles {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Roles[k] = vv.MaskInLog().(*OpRole)
		}
	}

	return y
}

func (x *RspGetRoles) MaskInRpc() any {
	if x == nil {
		return (*RspGetRoles)(nil)
	}

	y := x
	for k, v := range y.Roles {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Roles[k] = vv.MaskInRpc().(*OpRole)
		}
	}

	return y
}

func (x *RspGetRoles) MaskInBff() any {
	if x == nil {
		return (*RspGetRoles)(nil)
	}

	y := x
	for k, v := range y.Roles {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Roles[k] = vv.MaskInBff().(*OpRole)
		}
	}

	return y
}

func (x *RspSearchUsers) MaskInLog() any {
	if x == nil {
		return (*RspSearchUsers)(nil)
	}

	y := proto.Clone(x).(*RspSearchUsers)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*mgmt.OpUser)
		}
	}

	return y
}

func (x *RspSearchUsers) MaskInRpc() any {
	if x == nil {
		return (*RspSearchUsers)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*mgmt.OpUser)
		}
	}

	return y
}

func (x *RspSearchUsers) MaskInBff() any {
	if x == nil {
		return (*RspSearchUsers)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*mgmt.OpUser)
		}
	}

	return y
}

func (x *ReqLogin) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TcloudCaptcha).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspLogin) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.UserInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetRoles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Roles {
		if sanitizer, ok := any(x.Roles[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspSearchUsers) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type AuthBffHandler interface {
	// 登录
	Login(context.Context, *ReqLogin, *RspLogin) error
	// 登出
	Logout(context.Context, *emptypb.Empty, *emptypb.Empty) error
}

func RegisterAuthBff(s bff.Server, h AuthBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Auth"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/op/login", h.Login).Name("Login")
	bff.AddRoute(group, http.MethodPost, "/op/logout", h.Logout).Name("Logout")
	return group
}

type MgmtBffHandler interface {
	// 查询角色列表
	GetRoles(context.Context, *ReqGetRoles, *RspGetRoles) error
	// 翻译
	TextTranslate(context.Context, *ReqTextTranslate, *RspTextTranslate) error
	// 搜索运营端用户列表
	SearchUsers(context.Context, *ReqSearchUsers, *RspSearchUsers) error
}

func RegisterMgmtBff(s bff.Server, h MgmtBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Mgmt"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/op/role_get", h.GetRoles).Name("GetRoles")
	bff.AddRoute(group, http.MethodPost, "/op/text_translate", h.TextTranslate).Name("TextTranslate")
	bff.AddRoute(group, http.MethodPost, "/mgmt/search_users", h.SearchUsers).Name("SearchUsers")
	return group
}
