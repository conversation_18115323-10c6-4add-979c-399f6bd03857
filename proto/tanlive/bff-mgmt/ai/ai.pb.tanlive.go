// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package ai

import (
	ai "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iam "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func (x *CollectionTextFile) MaskInLog() any {
	if x == nil {
		return (*CollectionTextFile)(nil)
	}

	y := proto.Clone(x).(*CollectionTextFile)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*Operator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Copies {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Copies[k] = vv.MaskInLog().(*CollectionTextFile)
		}
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedStates[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *CollectionTextFile) MaskInRpc() any {
	if x == nil {
		return (*CollectionTextFile)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*Operator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Copies {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Copies[k] = vv.MaskInRpc().(*CollectionTextFile)
		}
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedStates[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *CollectionTextFile) MaskInBff() any {
	if x == nil {
		return (*CollectionTextFile)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*Operator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Copies {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Copies[k] = vv.MaskInBff().(*CollectionTextFile)
		}
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedStates[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *CollectionQA) MaskInLog() any {
	if x == nil {
		return (*CollectionQA)(nil)
	}

	y := proto.Clone(x).(*CollectionQA)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*Operator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *CollectionQA) MaskInRpc() any {
	if x == nil {
		return (*CollectionQA)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*Operator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *CollectionQA) MaskInBff() any {
	if x == nil {
		return (*CollectionQA)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*Operator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *Chat) MaskInLog() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := proto.Clone(x).(*Chat)
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*iam.UserInfo)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *Chat) MaskInRpc() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*iam.UserInfo)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *Chat) MaskInBff() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*iam.UserInfo)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ChatDetail) MaskInLog() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := proto.Clone(x).(*ChatDetail)
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Messages[k] = vv.MaskInLog().(*ai.EventChatMessage)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*iam.UserInfo)
	}
	if v, ok := any(y.FinishDate).(interface{ MaskInLog() any }); ok {
		y.FinishDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Records[k] = vv.MaskInLog().(*ai.ChatSendRecordInfo)
		}
	}

	return y
}

func (x *ChatDetail) MaskInRpc() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Messages[k] = vv.MaskInRpc().(*ai.EventChatMessage)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*iam.UserInfo)
	}
	if v, ok := any(y.FinishDate).(interface{ MaskInRpc() any }); ok {
		y.FinishDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Records[k] = vv.MaskInRpc().(*ai.ChatSendRecordInfo)
		}
	}

	return y
}

func (x *ChatDetail) MaskInBff() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Messages[k] = vv.MaskInBff().(*ai.EventChatMessage)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*iam.UserInfo)
	}
	if v, ok := any(y.FinishDate).(interface{ MaskInBff() any }); ok {
		y.FinishDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Records[k] = vv.MaskInBff().(*ai.ChatSendRecordInfo)
		}
	}

	return y
}

func (x *ChatMessage) MaskInLog() any {
	if x == nil {
		return (*ChatMessage)(nil)
	}

	y := proto.Clone(x).(*ChatMessage)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessage_ChatMessageDoc)
		}
	}
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Filter[k] = vv.MaskInLog().(*ai.ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Ugcs[k] = vv.MaskInLog().(*ChatMessage_ChatMessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInLog() any }); ok {
		y.ProcessTime = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ChatMessage) MaskInRpc() any {
	if x == nil {
		return (*ChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessage_ChatMessageDoc)
		}
	}
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Filter[k] = vv.MaskInRpc().(*ai.ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Ugcs[k] = vv.MaskInRpc().(*ChatMessage_ChatMessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInRpc() any }); ok {
		y.ProcessTime = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ChatMessage) MaskInBff() any {
	if x == nil {
		return (*ChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessage_ChatMessageDoc)
		}
	}
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Filter[k] = vv.MaskInBff().(*ai.ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Ugcs[k] = vv.MaskInBff().(*ChatMessage_ChatMessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInBff() any }); ok {
		y.ProcessTime = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *ChatMessage_ChatMessageDoc) MaskInLog() any {
	if x == nil {
		return (*ChatMessage_ChatMessageDoc)(nil)
	}

	y := proto.Clone(x).(*ChatMessage_ChatMessageDoc)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *ChatMessage_ChatMessageDoc) MaskInRpc() any {
	if x == nil {
		return (*ChatMessage_ChatMessageDoc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *ChatMessage_ChatMessageDoc) MaskInBff() any {
	if x == nil {
		return (*ChatMessage_ChatMessageDoc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *ChatMessage_ChatMessageUgc) MaskInLog() any {
	if x == nil {
		return (*ChatMessage_ChatMessageUgc)(nil)
	}

	y := proto.Clone(x).(*ChatMessage_ChatMessageUgc)
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Filter[k] = vv.MaskInLog().(*ai.ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Cards[k] = vv.MaskInLog().(*ChatMessage_ChatMessageUgcCard)
		}
	}

	return y
}

func (x *ChatMessage_ChatMessageUgc) MaskInRpc() any {
	if x == nil {
		return (*ChatMessage_ChatMessageUgc)(nil)
	}

	y := x
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Filter[k] = vv.MaskInRpc().(*ai.ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Cards[k] = vv.MaskInRpc().(*ChatMessage_ChatMessageUgcCard)
		}
	}

	return y
}

func (x *ChatMessage_ChatMessageUgc) MaskInBff() any {
	if x == nil {
		return (*ChatMessage_ChatMessageUgc)(nil)
	}

	y := x
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Filter[k] = vv.MaskInBff().(*ai.ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Cards[k] = vv.MaskInBff().(*ChatMessage_ChatMessageUgcCard)
		}
	}

	return y
}

func (x *CollectionTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Copies {
		if sanitizer, ok := any(x.Copies[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SharedStates {
		if sanitizer, ok := any(x.SharedStates[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *CollectionQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *Chat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Messages {
		if sanitizer, ok := any(x.Messages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.FinishDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Records {
		if sanitizer, ok := any(x.Records[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Filter {
		if sanitizer, ok := any(x.Filter[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Ugcs {
		if sanitizer, ok := any(x.Ugcs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.ProcessTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatMessage_ChatMessageDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatMessage_ChatMessageUgc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Filter {
		if sanitizer, ok := any(x.Filter[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Cards {
		if sanitizer, ok := any(x.Cards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
