syntax = "proto3";

package tanlive.bff_mgmt.ai;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/ai";

import "google/protobuf/timestamp.proto";
import "tanlive/ai/ai.proto";
import "tanlive/iam/iam.proto";
import "tanlive/base/ugc.proto";
import "tanlive/base/base.proto";

message Operator{
  base.IdentityType type = 1;
  // 用户id
  uint64 id = 2;
  // 用户名称
  string username = 3;
  // 用户所属团队名称
  string team_name = 4;
  // 用户id，只有为团队用户时，才需要
  uint64 user_id = 5;
}

message CollectionTextFile{
  uint64  id = 1;
  // 文件/文本名称
  string file_name = 2;
  // 文件/文本内容
  string text = 3;
  // 用户端
  repeated tanlive.ai.Assistant assistants = 4;
  // 状态(不包含助手对应的状态)
  tanlive.ai.DocState state = 5;
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 6;
  // 文件url
  string url = 7;
  // 命中次数
  uint32 hit_count = 8;
  // ugc类型
  base.DataType ugc_type = 9;
  // ugc的id
  uint64 ugc_id = 10;

  Operator create_by = 11;
  Operator  update_by = 12;
  google.protobuf.Timestamp update_date = 13;
  google.protobuf.Timestamp create_date = 14;
  // 同步至ai侧的版本滞后数，为0代表已同步
  uint64 version_lag = 15;
  // 解析进度，0.5 = 50%
  float parse_progress = 16;
  // 是否显示贡献者
  uint32 show_contributor = 17;
  repeated tanlive.ai.DocAssistantState states = 18;
  // 是否为副本
  bool is_copy = 19;
  // UGC标题
  string ugc_title = 20;
  // 是否为系统数据
  bool is_system = 21;
  // 内容状态
  tanlive.ai.DocContentState content_state = 22;
  // 副本列表
  repeated CollectionTextFile copies = 23;
  // ugc的hashid
  string ugc_hashid = 24;
  repeated tanlive.ai.DocAssistantState shared_states = 25;
  repeated tanlive.ai.CustomLabel labels = 26;
  repeated tanlive.ai.DocReference reference = 27;
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 28;
  tanlive.ai.DocParseMode parse_mode = 29;
  // 知识提示
  // 是否有超长标题表格
  bool has_over_sized_tables = 30;
  // 是否内容重复（租户内）
  bool has_repeated = 31;
  tanlive.ai.DocDataSource data_source = 32;
  // 外部数据源信息
  uint32 data_source_state = 33;
}

message CollectionQA{
  uint64 id = 1;
  // 问题
  string question = 2;
  // 答案
  string answer = 3;
  // 用户端
  repeated tanlive.ai.Assistant assistants = 4;
  // 状态(不包含助手对应的状态)
  tanlive.ai.DocState state = 5;
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 6;
  // 参考资料
  repeated tanlive.ai.DocReference reference = 7;
  // 命中次数
  uint32 hit_count = 8;

  Operator create_by = 9;
  Operator  update_by = 10;
  google.protobuf.Timestamp update_date = 11;
  google.protobuf.Timestamp create_date = 12;
  // 同步至ai侧的版本滞后数，为0代表已同步
  uint64 version_lag = 13;
  // 是否显示贡献者
  uint32 show_contributor = 14;
  repeated tanlive.ai.DocAssistantState states = 15;
  repeated tanlive.ai.CustomLabel labels = 16;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 17;
  // 问题是否超长
  bool question_oversize = 18;
  // 是否有重复
  bool has_repeated = 19;
}

message Chat {
  uint64 id = 1;
  string title = 2;

  tanlive.iam.UserInfo create_by = 3;
  google.protobuf.Timestamp update_date = 4;
  google.protobuf.Timestamp create_date = 5;
  tanlive.ai.ChatType chat_type = 6;
  uint64 assistant_id = 7;
  string assistant_name = 8;
  tanlive.ai.ChatCurrentState chat_state = 9;
  // 当前服务状态
  tanlive.ai.ChatSupportType support_type = 10;
  // 对话中问题数量
  uint32 question_cnt = 11;
  // 自定义标签kv对
  repeated tanlive.ai.CustomLabel labels = 12;
  uint32 reject_job_result = 13;
  string region_code = 14;
  // 是否转过人工服务
  int32 is_manual = 15;
  tanlive.ai.RatingScale  rating_scale = 16;
  float doc_hits = 17;
  float avg_duration = 18;
}

message ChatDetail {
  uint64 id = 1;
  string title = 2;
  repeated tanlive.ai.EventChatMessage messages = 3;
  // 地区
  tanlive.base.Region region = 4;

  tanlive.iam.UserInfo create_by = 5;
  google.protobuf.Timestamp finish_date = 6;
  google.protobuf.Timestamp create_date = 7;
  tanlive.ai.ChatType chat_type = 8;
  tanlive.ai.ChatCurrentState chat_state = 9;
  // 当前服务状态
  tanlive.ai.ChatSupportType support_type = 10;
  // 微信客服助手头像
  string assistant_avatar = 11;
  // 非web端时通过次字段返回消息详情
  repeated tanlive.ai.ChatSendRecordInfo records = 12;
  uint64 assistant_id = 13;
}

message ChatMessage {
  message ChatMessageDoc {
    uint64 ugc_id = 1;
    uint32 ugc_type = 2;
    repeated tanlive.ai.Contributor contributor = 5;
    uint32 data_type = 6;
    repeated tanlive.ai.DocReference reference = 7;
  }
  message ChatMessageUgcCard {
    string name = 1;
    string logo_url = 2;
    uint64 id = 3;
    string tags = 4;
    string hash_id = 5;
  }
  message ChatMessageUgc{
    tanlive.base.DataType ugc_type = 2;
    repeated  tanlive.ai.ChatMessageContentFilterItem filter = 3;
    repeated ChatMessageUgcCard cards = 4;
    bool is_ugc_link = 5;
    repeated uint64 ugc_ids = 6;
  }
  uint64 id = 1;
  uint64 chat_id = 2;
  string text = 4;
  google.protobuf.Timestamp create_date = 5;
  int32 type = 6;
  tanlive.ai.RatingScale rating_scale = 7;
  repeated ChatMessageDoc docs = 8;
  string link = 9;
  bool is_ugc_link = 10;
  tanlive.base.DataType ugc_type = 11;
  uint64 question_id = 12;
  string sql_query = 13;
  repeated  tanlive.ai.ChatMessageContentFilterItem filter = 14;
  repeated ChatMessageUgc ugcs = 15;
  base.TimeRange process_time = 20;
}
