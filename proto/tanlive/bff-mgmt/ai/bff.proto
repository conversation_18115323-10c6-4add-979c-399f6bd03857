syntax = "proto3";

package tanlive.bff_mgmt.ai;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/ai";

import "google/api/annotations.proto";
import "tanlive/bff-mgmt/ai/ai.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/options.proto";
import "google/protobuf/empty.proto";
import "tanlive/ai/ai.proto";
import "tanlive/iam/iam.proto";
import "tanlive/mgmt/mgmt.proto";
import "tanlive/errors/ai.proto";
import "tanlive/team/team.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

service AiBff {
  option (tanlive.bff) = true;

  // 创建文本或文件
  rpc CreateTextFiles(ReqCreateTextFiles) returns (RspCreateTextFiles){
    option (google.api.http) = {
      post: "/ai/collection/create_text_files"
      body: "*"
    };
  }
  // 查询文本或文件列表
  rpc ListTextFiles(ReqListTextFiles) returns (RspListTextFiles){
    option (google.api.http) = {
      post: "/ai/collection/list_text_files"
      body: "*"
    };
  }
  // 更新文本或文件
  rpc UpdateTextFiles(ReqUpdateTextFiles) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/collection/update_text_files"
      body: "*"
    };
  }
  // 创建系统文档副本（人工修改）
  rpc CreateSystemDocCopy(ReqCreateSystemDocCopy) returns (RspCreateSystemDocCopy) {
    option (google.api.http) = {
      post: "/ai/collection/create_system_doc_copy"
      body: "*"
    };
  }
  // 启用系统文档
  rpc EnableSystemDoc(ReqEnableSystemDoc) returns (RspEnableSystemDoc) {
    option (google.api.http) = {
      post: "/ai/collection/enable_system_doc"
      body: "*"
    };
  }
  // 停用系统文档
  rpc DisableSystemDoc(ReqDisableSystemDoc) returns (RspDisableSystemDoc) {
    option (google.api.http) = {
      post: "/ai/collection/disable_system_doc"
      body: "*"
    };
  }
  // 删除系统文档
  rpc DeleteSystemDoc(ReqDeleteSystemDoc) returns (RspDeleteSystemDoc) {
    option (google.api.http) = {
      post: "/ai/collection/delete_system_doc"
      body: "*"
    };
  }
  // 查询QA列表
  rpc ListQA(ReqListQA) returns (RspListQA){
    option (google.api.http) = {
      post: "/ai/collection/list_qa"
      body: "*"
    };
  }
  // 创建QA
  rpc CreateQAs(ReqCreateQAs) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/collection/create_qas"
      body: "*"
    };
  }
  // 更新QA
  rpc UpdateQAs(ReqUpdateQAs) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/collection/update_qas"
      body: "*"
    };
  }
  // 删除Doc，包括QA，文本或文件
  rpc DeleteQAs(ReqDeleteDocs) returns (RspDeleteDocs){
    option (google.api.http) = {
      post: "/ai/collection/delete_docs"
      body: "*"
    };
  }
  // 启用/禁用doc
  rpc OnOffDocs(ReqOnOffDocs) returns (RspOnOffDocs){
    option (google.api.http) = {
      post: "/ai/collection/onoff_docs"
      body: "*"
    };
  }
  // id查询文本/文件详情
  rpc GetTextFile(ReqGetTextFile) returns (RspGetTextFile){
    option (google.api.http) = {
      post: "/ai/collection/get_text_file"
      body: "*"
    };
  }

  // 批量更新doc的特定字段值
  rpc BatchUpdateDocAttr(ReqBatchUpdateDocAttr) returns (RspBatchUpdateDocAttr){
    option (google.api.http) = {
      post: "/ai/collection/batch_update_docs"
      body: "*"
    };
  }

  // 重新解析文件
  rpc ReparseTextFiles(ReqReparseTextFiles) returns (RspReparseTextFiles){
    option (google.api.http) = {
      post: "/ai/collection/reparse_text_files"
      body: "*"
    };
  }

  // 创建doc查询
  rpc CreateDocQuery(ReqCreateDocQuery) returns (RspCreateDocQuery){
    option (google.api.http) = {
      post: "/ai/collection/create_doc_query"
      body: "*"
    };
  }


  // 查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
  rpc GetTextFileTip(ReqGetTextFileTip) returns (RspGetTextFileTip){
    option (google.api.http) = {
      post: "/ai/collection/get_text_file_tip"
      body: "*"
    };
  }

  // 查询QA的知识提示（问题超长，内容重复）等信息
  rpc GetQaTip(ReqGetQaTip) returns (RspGetQaTip){
    option (google.api.http) = {
      post: "/ai/collection/get_qa_tip"
      body: "*"
    };
  }

  // collection向量查询
  rpc SearchCollection(ReqSearchCollection) returns (RspSearchCollection){
    option (google.api.http) = {
      post: "/ai/collection/search_collection"
      body: "*"
    };
  }
  // 校验待创建QA
  rpc ValidateQAs(ReqValidateQAs) returns (RspValidateQAs){
    option (google.api.http) = {
      post: "/ai/collection/validate_qas"
      body: "*"
    };
  }
  // 查询collection用户端列表
  rpc ListCollection(google.protobuf.Empty) returns (RspListCollection){
    option (google.api.http) = {
      post: "/ai/collection/list_collection"
      body: "*"
    };
  }
  // 查询ai助手列表
  rpc ListAssistant(ReqListAssistant) returns (RspListAssistant){
    option (google.api.http) = {
      post: "/ai/collection/list_assistant"
      body: "*"
    };
  }

  // 查询贡献者列表
  rpc ListContributor(ReqListContributor) returns (RspListContributor){
    option (google.api.http) = {
      post: "/ai/collection/list_contributor"
      body: "*"
    };
  }
  // 查询更新人列表
  rpc ListOperator(ReqListOperator) returns (RspListOperator){
    option (google.api.http) = {
      post: "/ai/collection/list_operator"
      body: "*"
    };
  }

  // 查询文件列表
  rpc ListCollectionFileName(ReqListCollectionFileName) returns (RspListCollectionFileName){
    option (google.api.http) = {
      post: "/ai/collection/list_filename"
      body: "*"
    };
  }
  // 克隆QA/文本/文件
  rpc CloneDoc(ReqCloneDoc) returns (RspCloneDoc){
    option (google.api.http) = {
      post: "/ai/collection/clone_doc"
      body: "*"
    };
  }
  // 根据文档ref_id查询文档
  rpc ListDocByRef(ReqListDocByRef) returns (RspListDocByRef){
    option (google.api.http) = {
      post: "/ai/collection/list_doc_by_ref"
      body: "*"
    };
  }

  // 创建/更新碳LIVE反馈
  rpc UpsertMgmtFeedback(ReqUpsertMgmtFeedback) returns (RspCreateFeedback){
    option (google.api.http) = {
      post: "/ai/upsert_mgmt_feedback"
      body: "*"
    };
  }

  // 查询用户反馈列表
  rpc GetFeedbacks(ReqGetFeedbacks) returns (RspGetFeedbacks){
    option (google.api.http) = {
      post: "/ai/get_feedbacks"
      body: "*"
    };
  }

  // 查询用户反馈详情
  rpc FindFeedback(ReqFindFeedback) returns (RspFindFeedback){
    option (google.api.http) = {
      post: "/ai/find_feedback"
      body: "*"
    };
  }

  // 采用用户反馈
  rpc AcceptFeedback(ReqAcceptFeedback) returns (RspAcceptFeedback){
    option (google.api.http) = {
      post: "/ai/accept_feedback"
      body: "*"
    };
  }

  // 查询用户反馈日志列表
  rpc GetFeedbackLogs(ReqGetFeedbackLogs) returns (RspGetFeedbackLogs){
    option (google.api.http) = {
      post: "/ai/get_feedback_logs"
      body: "*"
    };
  }

  // 更新助手的横幅信息
  rpc UpdateAssistantNoticeConf(ReqUpdateChatNoticeConf) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/update_assistant_notice_conf"
      body: "*"
    };
  }

  // 获取所有会话的地区编码
  rpc DescribeChatRegionCode(ReqDescribeChatRegionCode) returns (RspDescribeChatRegionCode){
    option (google.api.http) = {
      post: "/ai/describe_chat_region_code"
      body: "*"
    };
  }

  // 获取所有教学反馈的地区编码
  rpc DescribeFeedbackRegionCode(ReqDescribeFeedbackRegionCode) returns (RspDescribeFeedbackRegionCode){
    option (google.api.http) = {
      post: "/ai/describe_feedback_region_code"
      body: "*"
    };
  }

  // AI对话管理列表
  rpc ListChat(ReqListChat) returns (RspListChat){
    option (google.api.http) = {
      post: "/ai/list_chat"
      body: "*"
    };
  }
  // AI对话详情
  rpc GetChatDetail(ReqGetChatDetail) returns (RspGetChatDetail){
    option (google.api.http) = {
      post: "/ai/get_chat_detail"
      body: "*"
    };
  }
  // 搜索chat
  rpc SearchChat(ReqSearchChat) returns (RspSearchChat){
    option (google.api.http) = {
      post: "/ai/search_chat"
      body: "*"
    };
  }
  // 获取消息详情
  rpc GetChatMessageDetail(ReqGetChatMessageDetail) returns (RspGetChatMessageDetail){
    option (google.api.http) = {
      post: "/ai/get_chat_message_detail"
      body: "*"
    };
  }
  // 获取自定义标签列表
  rpc ListCustomLabel(ReqListCustomLabel) returns (RspListCustomLabel){
    option (google.api.http) = {
      post: "/ai/get_custom_labels"
      body: "*"
    };
  }
  // 插入或更新自定义标签
  rpc ModifyCustomLabels(ReqModifyCustomLabels) returns (RspModifyCustomLabels){
    option (google.api.http) = {
      post: "/ai/modify_custom_labels"
      body: "*"
    };
  }
  // 删除自定义标签
  rpc DeleteCustomLabels(ReqDeleteCustomLabels) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/delete_custom_labels"
      body: "*"
    };
  }
  // 更新对象的自定义标签
  rpc UpdateObjectCustomLabels(ReqUpdateObjectCustomLabels) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/update_object_custom_labels"
      body: "*"
    };
  }

  // 获取人工坐席列表
  rpc ListChatLiveAgent(ReqListChatLiveAgent) returns (RspListChatLiveAgent){
    option (google.api.http) = {
      post: "/ai/list_chat_live_agent"
      body: "*"
    };
  }

  // 切换人工坐席
  rpc SwitchChatLiveAgent(ReqSwitchChatLiveAgent) returns (RspSwitchChatLiveAgent){
    option (google.api.http) = {
      post: "/ai/switch_chat_live_agent"
      body: "*"
    };
  }

  // 修复message collection
  rpc SyncFixChatMessageCollection(ReqSyncFixChatMessageCollection) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/sync_fix_message_collection"
      body: "*"
    };
  }

  // 查询可分享的助手列表
  rpc ListAssistantCanShareDoc(ReqListAssistantCanShareDoc) returns (RspListAssistantCanShareDoc){
    option (google.api.http) = {
      post: "/ai/list_shared_assistant"
      body: "*"
    };
  }

  // 创建文档分享（支持助手、个人、团队）
  rpc CreateAssistantShare(ReqCreateAssistantShare) returns (RspCreateDocShare){
    option (google.api.http) = {
      post: "/ai/create_assistant_share"
      body: "*"
    };
  }

  // 创建助手发送方设置
  rpc CreateDocShareConfigSender(ReqCreateDocShareConfigSender) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/create_assistant_sender"
      body: "*"
    };
  }
  // 查询助手发送方设置
  rpc ListeDocShareConfigSender(ReqListeDocShareConfigSender) returns (RspListeDocShareConfigSender){
    option (google.api.http) = {
      post: "/ai/list_assistant_sender"
      body: "*"
    };
  }

  // 获取助手url网页title
  rpc ProxyChatHtmlUrl(ReqProxyChatHtmlUrl) returns (RspProxyChatHtmlUrl){
    option (google.api.http) = {
      post: "/ai/proxy_chat_url"
      body: "*"
    };
  }

  // 查询助手分页列表
  rpc GetAssistantsPage(ReqGetAssistantsPage) returns (RspGetAssistantsPage) {
    option (google.api.http) = {
      post: "/ai/get_assistants_page"
      body: "*"
    };
  }

  // 创建助手
  rpc CreateAssistant(ReqCreateAssistant) returns (RspCreateAssistant) {
    option (google.api.http) = {
      post: "/ai/create_assistant"
      body: "*"
    };
  }

  // 批量创建助手
  rpc BatchCreateAssistant(ReqBatchCreateAssistant) returns (RspBatchCreateAssistant) {
    option (google.api.http) = {
      post: "/ai/batch_create_assistant"
      body: "*"
    };
  }

  // 更新助手
  rpc UpdateAssistant(ReqUpdateAssistant) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/update_assistant"
      body: "*"
    };
  }

  // 批量更新助手
  rpc BatchUpdateAssistant(ReqBatchUpdateAssistant) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/batch_update_assistant"
      body: "*"
    };
  }

  // 删除助手
  rpc DeleteAssistant(ReqDeleteAssistant) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/delete_assistant"
      body: "*"
    };
  }

  // 查询助手日志分页列表
  rpc GetAssistantLogsPage(ReqGetAssistantLogsPage) returns (RspGetAssistantLogsPage) {
    option (google.api.http) = {
      post: "/ai/get_assistant_logs_page"
      body: "*"
    };
  }

  // 获取助手下拉选项
  rpc GetAssistantOptions(google.protobuf.Empty) returns (RspGetAssistantOptions) {
    option (google.api.http) = {
      post: "/ai/get_assistant_options"
      body: "*"
    };
  }

  // 自动文档分段
  rpc AutoChunkDoc(ReqAutoChunkDoc) returns (RspAutoChunkDoc) {
    option (google.api.http) = {
      post: "/ai/auto_chunk_doc"
      body: "*"
    };
  }

  // 手动文档分段
  rpc ManualChunkDoc(ReqManualChunkDoc) returns (RspManualChunkDoc) {
    option (google.api.http) = {
      post: "/ai/manual_chunk_doc"
      body: "*"
    };
  }

  // 查询文档分段信息
  rpc GetDocChunks(ReqGetDocChunks) returns (RspGetDocChunks) {
    option (google.api.http) = {
      post: "/ai/get_doc_chunks"
      body: "*"
    };
  }

  // 查询文档分段任务列表
  rpc GetChunkDocTasks(ReqGetChunkDocTasks) returns (RspGetChunkDocTasks) {
    option (google.api.http) = {
      post: "/ai/get_chunk_doc_tasks"
      body: "*"
    };
  }

  // 查询文档的向量化模型
  rpc GetDocEmbeddingModels(ReqGetDocEmbeddingModels) returns (RspGetDocEmbeddingModels) {
    option (google.api.http) = {
      post: "/ai/get_doc_embedding_models"
      body: "*"
    };
  }

  // 获取建议问题日志
  rpc DescribeChatSuggestLog(ReqDescribeChatSuggestLog) returns (RspDescribeChatSuggestLog) {
    option (google.api.http) = {
      post: "/ai/describe_chat_suggest_log"
      body: "*"
    };
  }
}

message ReqSearchChatStream {
  string text = 1 [(validator) = "required"];
  string question_id = 2 [(validator) = "required"];
  // 助手id
  string assistant_id = 3 [(validator) = "required"];
  // topN
  uint32 top_n = 4;
  // 阈值
  float threshold = 5[(validator) = "min=0,max=1"];
  // 关键词召回条数
  int32 text_recall_top_n = 7;
  // 温度
  float temperature = 8[(validator) = "min=0,max=2"];
  bool clean_chunks = 9;
  // 关键词召回匹配目标
  tanlive.ai.TextRecallQuery text_recall_query = 50;
  // 关键词召回模式
  tanlive.ai.TextRecallPattern text_recall_pattern = 51;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 52;
}


message ReqDescribeChatSuggestLog {
  uint64 message_id = 1;
}

message RspDescribeChatSuggestLog {
  repeated tanlive.ai.ChatSuggestLog logs = 1;
}

message ReqProxyChatHtmlUrl{
  repeated string urls = 1 [(validator) = "required"];
  // 助手id
  uint64 assistant_id = 2;
}

message RspProxyChatHtmlUrl{
  message Content {
    string url = 1;
    string title = 2;
  }
  repeated Content contents = 1;
}

message ReqGetTextFile{
  uint64 id = 1;
}

message RspGetTextFile{
  CollectionTextFile item = 1;
}

message ReqReparseTextFiles {
  repeated uint64 ids = 1;
  tanlive.ai.DocParseMode parse_mode = 2;
  uint64 query_id = 3;
}

message RspReparseTextFiles {
  bool async = 1;
}

message ReqCreateDocQuery {
  ReqListTextFiles doc = 1;
  ReqListQA qa = 2;
}

message RspCreateDocQuery {
  uint64 query_id = 1;
  bool is_empty = 2;
  uint32 total_count = 3;
}

message ReqSyncFixChatMessageCollection{
  repeated uint64 chat_ids = 1;
}

message ReqUpdateObjectCustomLabels{
  // 打标签的对象 id
  repeated uint64 id = 1 [(validator) = "required"];
  // 自定义标签
  repeated tanlive.ai.CustomLabel labels = 2;
  // 打标签的对象类型
  tanlive.ai.CustomLabelObjectType object_type = 3;
}

message RspListAssistant{
  uint32 total_count = 1;
  repeated tanlive.ai.Assistant assistants = 2;
}

message ReqListAssistant{
  uint32 offset = 1;
  uint32 limit = 2;
  tanlive.ai.ChatType type = 3;
  // 搜索名称关键词
  string name = 4;
  // 语言，为空默认zh
  string language = 5 [(validator) = "omitempty,oneof=zh en"];
}

message ReqDeleteCustomLabels{
  repeated uint64 ids = 1;
}

message ReqModifyCustomLabels{
  repeated tanlive.ai.CustomLabel labels = 1;
  tanlive.ai.CustomLabelObjectType object_type = 3 [(validator) = "oneof=1 2 3"];
}

message RspModifyCustomLabels{
}

message ReqListCustomLabel{
  uint32 offset = 1;
  uint32 limit = 2;
  tanlive.ai.CustomLabelObjectType object_type = 3[(validator) = "oneof=1 2 3"];
  repeated uint64 id = 4;
}

message RspListCustomLabel{
  repeated tanlive.ai.CustomLabel labels = 1;
  uint32  total_count = 2;
}

message ReqListDocByRef{
  repeated string ref = 1;
}

message RspListDocByRef{
  message Doc {
    string text = 1;
    string question = 2;
    string file_name = 4;
    string url = 5;
    repeated tanlive.ai.Contributor contributor = 6;
    Operator  update_by = 7;
    string ref = 8;
  }
  repeated Doc docs = 1;
}

message ReqGetChatMessageDetail{
  uint64 id = 1 [(validator) = "required"];
  base.Region region = 2 [(validator) = "required"];
}

message RspGetChatMessageDetail{
  tanlive.ai.EventChatMessage message = 1;
  repeated SearchCollectionItem collection_items = 2;
  repeated tanlive.ai.ChatMessageLog logs = 3;
  base.TimeRange collection_time = 4;
  bool clean_chunks = 5;
}

message ReqValidateQAs{
  message Item{
    string question = 1;
    string answer = 2;
    repeated tanlive.ai.DocReference reference = 3;
    repeated uint64 assistant_id = 4;
    // 贡献者
    repeated tanlive.ai.Contributor contributor = 5;
    tanlive.ai.DocState state = 6;
  }
  repeated Item items = 1;
}

message ReqListCollectionFileName{
  // 文件名模糊搜索匹配
  string search = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  // 文件名精确匹配搜索
  repeated string full_search = 4;
  tanlive.ai.DocDataSource data_source = 5;
}

message RspListCollectionFileName{
  uint32 total_count = 1;
  message Item{
    // 文件绑定的url/path
    string url = 1;
    // 文件名称
    string name = 2;
    // 文件id
    uint64 id = 3;
  }
  repeated Item items = 2;
}

message RspListCollection{
  repeated tanlive.ai.Collection collections = 1;
}
message ReqListContributor{
  string search = 1;
  tanlive.ai.ListDocFilterType type = 2;
  tanlive.ai.DocDataSource data_source = 3;
}

message RspListContributor{
  repeated tanlive.ai.Contributor contributors = 1;
}

message ReqListOperator{
  string search = 1;
  tanlive.ai.ListDocFilterType type = 2;
  // 是否为创建人，false代表更新人，true代表创建人
  bool creator = 3;
  tanlive.ai.DocDataSource data_source = 4;
}

message RspListOperator{
  repeated Operator operators = 1;
}

message RspValidateQAs{
  message Err{
    errors.AiError code = 1;
    string message = 2;
    uint64 id = 3;
  }
  repeated Err errors = 1;
}

message ReqSearchCollection{
  string search = 1 [(validator) = "required"];
  repeated uint64 assistant_id = 2 [(validator) = "required"];
  tanlive.ai.DocType doc_type = 3;
  float threshold = 4;
  uint32 top_n = 5;
  uint32 offset = 6;
  uint32 limit = 7;
  float text_weight = 8;
  uint32 text_recall_top_n = 9;
  // 关键词召回匹配目标
  tanlive.ai.TextRecallQuery text_recall_query = 10;
  // 关键词召回模式
  tanlive.ai.TextRecallPattern text_recall_pattern = 11;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 12;
  bool clean_chunks = 13;
  float temperature = 14;
}

message SearchCollectionItem{
  string text = 1;
  string question = 2;
  float score = 3;
  string file_name = 4;
  string url = 5;
  repeated tanlive.ai.Contributor contributor = 6;
  Operator update_by = 10;
  string id = 11;
  // 召回类型
  tanlive.ai.SearchCollectionType type = 12;
  // 是否相关
  bool is_related = 13;
  // 文件类型
  tanlive.ai.DocType doc_type = 14;
  tanlive.ai.DocDataSource data_source = 15;
  uint64 doc_id = 16;
  string doc_name = 17;
}

message RspSearchCollection{
  google.protobuf.Timestamp start = 2;
  google.protobuf.Timestamp end = 3;
  uint32  total_count = 4;
  tanlive.ai.DocMatchPattern match_pattern = 5;
  repeated SearchCollectionItem item = 6;
}

message ReqDeleteDocs{
  repeated uint64 id = 1 ;
  uint64 query_id = 2;
}

message RspDeleteDocs{
  bool async = 1;
}

message ReqUpdateQA{
  string question = 1;
  string answer = 2;
  repeated tanlive.ai.DocReference reference = 3;
  repeated tanlive.ai.DocAssistantState states = 4;
  repeated tanlive.ai.Contributor contributor = 7 [(validator) = "omitempty,dive"];
  uint64 id = 8 [(validator) = "required"];
  google.protobuf.FieldMask mask = 9;
  // 是否显示贡献者
  uint32 show_contributor = 10;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 11;
}

message ReqUpdateQAs{
  repeated ReqUpdateQA items = 1 [(validator) = "required,max=200,dive"];
}

message ReqCreateQA{
  string question = 1;
  string answer = 2;
  repeated tanlive.ai.DocReference reference = 3;
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 5 [(validator) = "omitempty,dive"];
  tanlive.ai.DocState state = 6 [(validator) = "omitempty,oneof=1 2"];
  repeated uint64 assistant_id = 9;
  // 是否显示贡献者
  uint32 show_contributor = 10;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 11;
}

message ReqCreateQAs{
  repeated ReqCreateQA items = 6 [(validator) = "required,max=200,dive"];
}

message ReqListQA{
  // 在助手中
  repeated uint64 assistant_id = 1;
  tanlive.ai.DocState state = 2;
  repeated tanlive.ai.Contributor contributor = 3;
  repeated tanlive.ai.Operator update_by = 4;
  repeated base.OrderBy order_by = 5;
  uint32 offset = 6;
  uint32 limit = 7;
  string search = 8;
  // 不在助手中
  repeated uint64 excluded_assistant_id = 9;
  // 重复 doc 紧凑排序
  bool group_repeated = 10;
  uint32 show_contributor = 11;
  repeated tanlive.ai.LabelFilter labels = 12;
  // 自定义标签排序，只能当个标签排序
  tanlive.ai.OrderByLabel order_by_label = 13;
  repeated uint64 ids = 14;
  repeated tanlive.ai.Operator create_by = 15;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 16;

  // 知识提示过滤条件，用来筛选问题超长等问题的记录
  message TipFilter {
    bool warning = 1;
  }
  TipFilter tip_filter = 17;
  // 分享的团队id
  repeated uint64 share_team_id = 18;
  // 分享的用户id
  repeated uint64 share_user_id = 19;
}

message RspListQA{
  uint32 total_count = 1;
  repeated CollectionQA items = 2;
}

message ReqUpdateTextFile{
  uint64 id = 1 [(validator) = "required"];
  string file_name = 2 [(validator) = "omitempty,max=4096"];
  string text = 3;
  string url = 4;
  repeated tanlive.ai.DocAssistantState states = 6;
  repeated tanlive.ai.Contributor contributor = 7;
  base.DataType ugc_type = 8;
  uint64 ugc_id = 9;
  // 解析后的文件url
  string parsed_url = 10;
  google.protobuf.FieldMask mask = 11;
  // 是否显示贡献者
  uint32 show_contributor = 12;
  repeated tanlive.ai.DocReference reference = 13;
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 14;
}

message ReqUpdateTextFiles{
  repeated ReqUpdateTextFile items = 1 [(validator) = "required,max=200,dive"];
}

message ReqCreateTextFiles{
  message Item{
    // 文件/文本名称
    string file_name = 1 [(validator) = "required"];
    // 文件/文本内容
    string text = 2;
    // 助手
    repeated uint64 assistant_id = 3;
    // 贡献者
    repeated tanlive.ai.Contributor contributor = 4 [(validator) = "omitempty,dive"];
    // 文件url
    string url = 5;
    // ugc类型
    base.DataType ugc_type = 6;
    // ugc的id
    uint64 ugc_id = 7;
    // 文件解析后地址
    string parsed_url = 8;
    // 状态 1: 启用 2: 停用 状态设置只对文本有效
    tanlive.ai.DocState state = 9;
    // 类型 2:文本 3:文件
    uint32 type = 10 [(validator) = "omitempty,oneof=2 3"];
    // 是否显示贡献者
    uint32 show_contributor = 11;
    repeated tanlive.ai.DocReference reference = 12;
    // 解析模式
    tanlive.ai.DocParseMode parse_mode = 15;
    // 数据源
    tanlive.ai.DocDataSource data_source = 16;
  }
  repeated Item items = 1 [(validator) = "required,max=1000,dive"];
}

message RspCreateTextFiles{
  repeated uint64 id = 9;
}

message ReqListTextFiles{
  repeated uint64 assistant_id = 1;
  tanlive.ai.DocState state = 2;
  repeated tanlive.ai.Contributor contributor = 3;
  repeated tanlive.ai.Operator update_by = 4;
  repeated base.OrderBy order_by = 5;
  uint32 offset = 6;
  uint32 limit = 7;
  message Search{
    string text = 1; // 文本内容搜索
    string file_name = 2; // 文件名搜索
    string ugc_title = 3;
  }
  Search search = 8;
  // 不在助手中
  repeated uint64 excluded_assistant_id = 10;
  // 重复 doc 紧凑排序
  bool group_repeated = 15;
  uint32 show_contributor = 11;
  // 是否为系统文档
  bool is_system = 12;
  // 是否查询副本
  bool with_copies = 13;
  // UGC模块
  repeated base.DataType ugc_type = 14;
  // 内容状态
  repeated tanlive.ai.DocContentState content_state = 16;
  repeated tanlive.ai.LabelFilter labels = 17;
  // 自定义标签排序，只能当个标签排序
  tanlive.ai.OrderByLabel order_by_label = 18;
  repeated uint64 ids = 19;
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 20;
  repeated tanlive.ai.Operator create_by = 21;
  repeated tanlive.ai.DocParseMode parse_mode = 22;
  // 查询解析失败的数据
  tanlive.ai.DocState parse_state = 23;
  // 知识提示过滤条件
  message TipFilter {
    // 警告条件组
    bool warning = 1;
  }
  TipFilter tip_filter = 24;
  // 数据源
  tanlive.ai.DocDataSource data_source = 25;
  // 数据源同步状态
  uint32 data_source_state = 26;
}

message RspListTextFiles{
  uint32 total_count = 1;
  repeated CollectionTextFile items = 2;
  uint32 fail_parse_count = 3;
}

message ReqCreateSystemDocCopy {
  message Item {
    // 文档ID
    uint64 doc_id = 1 [(validator) = "required"];
    /*
    // 文档内容
    string text = 2 [(validator) = "required"];
    // 助手
    repeated uint64 assistant_id = 3 [(validator) = "omitempty,dive,required"];
    // 贡献者
    repeated tanlive.ai.Contributor contributor = 4 [(validator) = "omitempty,dive,required"];
    // 是否显示贡献者
    uint32 show_contributor = 5 [(validator) = "required,oneof=1 2"];
    */
  }
  // 创建列表
  repeated Item items = 1 [(validator) = "required,dive"];
}

message RspCreateSystemDocCopy {
  message Result {
    // 错误码
    int32 code = 1;
  }
  // 结果列表
  repeated Result results = 1;
}

message ReqEnableSystemDoc {
  // 文档ID
  repeated uint64 doc_id = 1 [(validator) = "required,dive,required"];
}

message RspEnableSystemDoc {
  message Result {
    // 错误码
    int32 code = 1;
  }
  // 结果列表
  repeated Result results = 1;
}

message ReqDisableSystemDoc {
  // 文档ID
  repeated uint64 doc_id = 1 [(validator) = "required,dive,required"];
}

message RspDisableSystemDoc {
  message Result {
    // 错误码
    int32 code = 1;
  }
  // 结果列表
  repeated Result results = 1;
}

message ReqDeleteSystemDoc {
  // 文档ID
  repeated uint64 doc_id = 1 [(validator) = "required,dive,required"];
}

message RspDeleteSystemDoc {
  message Result {
    // 错误码
    int32 code = 1;
  }
  // 结果列表
  repeated Result results = 1;
}

message ReqUpsertMgmtFeedback {
  // 通过答案ID更新或创建反馈
  uint64 answer_id = 1 [(validator) = "required_without=FeedbackId"];
  // AI回答评价
  tanlive.ai.FeedbackAnswerRating answer_rating = 2 [(validator) = "required"];
  // 碳LIVE反馈
  tanlive.ai.FeedbackComment mgmt_feedback = 3 [(validator) = "required"];
  // 碳LIVE内部备注
  tanlive.ai.FeedbackComment mgmt_comment = 4;
  // 通过反馈ID更新反馈
  uint64 feedback_id = 5 [(validator) = "required_without=AnswerId"];
  // 预期命中的知识
  repeated uint64 mgmt_doc_id = 6 [(validator) = "omitempty,dive,required"];
}

message RspCreateFeedback {
  // 反馈ID
  uint64 feedback_id = 1;
}

message ReqGetFeedbacks {
  // 地区
  base.Region region = 1 [(validator) = "required"];
  // 反馈ID
  repeated uint64 feedback_id = 2;
  // 上传用户ID
  repeated uint64 create_by = 3;
  // 创建时间区间
  tanlive.base.TimeRange create_date_range = 4;
  // 分页偏移量
  uint32 offset = 5;
  // 分页大小
  uint32 limit = 6;
  // 排序
  repeated base.OrderBy order_by = 7;
  // 助手
  repeated uint64 assistant_ids = 8;
  repeated string region_codes = 9;
}

message RspGetFeedbacks {
  message Item {
    // 反馈详情
    tanlive.ai.Feedback feedback = 1;
    // 参考文献
    repeated tanlive.ai.FeedbackReference references = 2;
    // 原始问题
    tanlive.ai.ChatMessage original_question = 3;
    // 原始回答
    tanlive.ai.ChatMessage original_answer = 4;
    // 预期命中的知识
    repeated tanlive.ai.ChatMessageDoc expected_docs = 5;
    // 预期命中的知识（碳LIVE运营）
    repeated tanlive.ai.ChatMessageDoc expected_mgmt_docs = 6;
  }
  // 反馈列表
  repeated Item items = 1;
  // 总数
  uint32 total_count = 2;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 3;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 4;
  // 运营端用户卡片列表
  repeated tanlive.mgmt.UserCard mgmt_user_cards = 5;
}

message ReqFindFeedback {
  // 用户反馈ID
  uint64 feedback_id = 1 [(validator) = "required"];
}

message RspFindFeedback {
  // 反馈详情
  tanlive.ai.Feedback feedback = 1;
  // 参考文献
  repeated tanlive.ai.FeedbackReference references = 2;
  // 原始问题
  tanlive.ai.ChatMessage original_question = 3;
  // 原始回答
  tanlive.ai.ChatMessage original_answer = 4;
  // 预期命中的知识
  repeated tanlive.ai.ChatMessageDoc expected_docs = 5;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 6;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 7;
  // 运营端用户卡片列表
  repeated tanlive.mgmt.UserCard mgmt_user_cards = 8;
  // 预期命中的知识（碳LIVE运营）
  repeated tanlive.ai.ChatMessageDoc expected_mgmt_docs = 9;
}

message ReqAcceptFeedback {
  // 反馈ID
  repeated uint64 feedback_ids = 1;
}

message RspAcceptFeedback {
  message Result {
    // 反馈ID
    uint64 feedback_id = 1;
    // 错误码
    int32 code = 2;
  }
  // 结果
  repeated Result results = 1;
}

message ReqGetFeedbackLogs {
  // 地区
  base.Region region = 1 [(validator) = "required"];
  // 反馈ID
  uint64 feedback_id = 2;
  // 操作人
  tanlive.base.Identity create_identity = 3;
  // 操作类型
  repeated tanlive.ai.FeedbackAction action = 4;
  // 操作时间区间
  tanlive.base.TimeRange create_date_range = 5;
  // 分页偏移量
  uint32 offset = 6;
  // 分页大小
  uint32 limit = 7;
  // 排序
  repeated base.OrderBy order_by = 8;
}

message RspGetFeedbackLogs {
  // 日志列表
  repeated tanlive.ai.FullFeedbackLog items = 1;
  // 总数
  uint32 total_count = 2;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 3;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 4;
  // 运营端用户卡片列表
  repeated tanlive.mgmt.UserCard mgmt_user_cards = 5;
}

message ReqUpdateChatNoticeConf {
  tanlive.ai.AiAssistantNoticeConf conf = 1 [(validator) = "required"];
}

message ReqDescribeChatRegionCode {
  // 地区
  base.Region region = 1 [(validator) = "required"];
  repeated uint64 assistant_ids = 2 ;
}

message RspDescribeChatRegionCode {
  repeated string region_codes = 1;
}

message ReqDescribeFeedbackRegionCode {
  // 地区
  base.Region region = 1 [(validator) = "required"];
}

message RspDescribeFeedbackRegionCode {
  repeated string region_codes = 1;
}

message ReqListChat {
  message Filter {
    // 用户id
    repeated uint64 user_ids = 1;
    // 对话内容
    repeated string chat_titles = 2;
    // 地区
    base.Region region = 3 [(validator) = "required"];
    tanlive.ai.ChatType chat_type = 4;
    repeated string nicknames = 5;
    // 筛选审核 1 违规 2 敏感 3 正常
    uint32 reject_job_result = 6;
    // 国家或地区编码
    repeated string region_codes = 7;
    // 是否转过人工服务 1 否 2 是
    int32 is_manual = 9;
  }
  uint32 offset = 1;
  uint32 limit = 2;
  repeated string order_by = 3;
  Filter filter = 4 [(validator) = "required"];
  // 创建时间区间
  tanlive.base.TimeRange create_date_range = 5;
  // 处理时间区间
  tanlive.base.TimeRange update_date_range = 6;
  // 自定义标签kv对
  repeated tanlive.ai.LabelFilter labels = 7;
  // 助手id
  repeated uint64 assistant_ids = 8 ;
  // 自定义标签排序，只能当个标签排序
  tanlive.ai.OrderByLabel order_by_label = 15;
}

message RspListChat {
  repeated Chat chats = 1;
  uint32 total_count = 2;
}

message ReqGetChatDetail {
  // chat_id
  uint64 id = 1 [(validator) = "min=1"];
  // 地区
  base.Region region = 2 [(validator) = "required"];
  uint32 offset = 3;
  uint32 limit = 4;
  // 消息内容搜索关键词
  string keyword = 5;
  // 创建时间区间
  tanlive.base.TimeRange send_range = 6;
  // 问题ID
  uint64 question_id = 7;
}

message RspGetChatDetail {
  ChatDetail chat_detail = 1;
  uint32 totalCount = 2;
}

message ReqSearchChat {
  string text = 1 [(validator) = "required"];
  uint64 question_id = 2 [(validator) = "required"];
  // 助手id
  uint64 assistant_id = 3 [(validator) = "required"];
  // top n
  uint32 top_n = 4;
  // 阈值
  float threshold = 5 [(validator) = "min=0,max=1"];
  // 关键词召回条数
  int32 text_recall_top_n = 7;
  // 温度
  float temperature = 8 [(validator) = "min=0,max=2"];
  bool clean_chunks = 9;
  // 关键词召回匹配目标
  tanlive.ai.TextRecallQuery text_recall_query = 50;
  // 关键词召回模式
  tanlive.ai.TextRecallPattern text_recall_pattern = 51;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 52;
}

message RspSearchChat {
  tanlive.ai.EventChatMessage message = 1 [(tanlive.validator) = "required"];
  uint64 user_id = 2 [(tanlive.validator) = "required"];
  // 是否是推送运营端
  bool is_op = 3;
  // 是否仅搜索
  bool is_only_search = 4;
}

message ReqCloneDoc{
  repeated uint64 id = 1 [(validator) = "required"];
  uint64 query_id = 2;
}

message RspCloneDoc{
  repeated uint64 id = 1;
}

message ReqOnOffDocs{
  repeated uint64 id = 1 ;
  tanlive.ai.DocState state = 2 [(validator) = "required,oneof=1 2"];
  uint64 query_id = 3;
}

message ReqListChatLiveAgent {
  uint64 chat_id = 1 [(validator) = "required"];
  // 地区
  base.Region region = 2 [(validator) = "required"];
}

message RspListChatLiveAgent {
  tanlive.ai.ChatLiveAgentInfo chat_live_agent = 1;
}

message ReqSwitchChatLiveAgent {
  // 人工客服id
  uint64 live_agent_id = 1 [(validator) = "required"];
  // 会话id
  uint64 chat_id = 2 [(validator) = "required"];
  // 地区
  base.Region region = 3 [(validator) = "required"];
}

message RspSwitchChatLiveAgent {
  // 切换结果
  tanlive.ai.SwitchChatState state = 1;
}

message RspOnOffDocs{
  message RepeatCollection{
    uint64 id = 1;
    map<uint64, string> file_name = 2;
  }
  repeated RepeatCollection pre_repeat_collections = 1;
  repeated RepeatCollection repeat_collections = 2;
  message QaContainsMatchCount{
    uint64 assistant_id = 1;
    uint64 cnt = 2;
  }
  repeated QaContainsMatchCount qa_num_exceed = 3;
  bool async = 4;
}


message ReqListAssistantCanShareDoc{
  uint64 doc_id = 1;
  // 搜索名称关键词
  string name = 2;
  // 语言，为空默认zh
  string language = 3 [(validator) = "omitempty,oneof=zh en"];
}

message RspListAssistantCanShareDoc{
  message SharedAssistant{
    uint64 id = 1;
    string name = 2;
    string name_en = 3;
    bool is_selected = 4;
  }

  repeated SharedAssistant assistants = 1;
}

message ReqCreateAssistantShare{
  repeated uint64 assistant_id = 1;
  uint64 doc_id = 2;
  uint64 query_id = 3;
  // 分享给个人的ID列表
  repeated uint64 user_id = 4;
  // 分享给团队的ID列表
  repeated uint64 team_id = 5;
}

message RspCreateDocShare {
  bool async = 1;
}

// 保持向后兼容性
message RspCreateDocShareAssistant {
  bool async = 1;
}

message ReqCreateDocShareConfigSender{
  repeated uint64 share_assistant_id = 2;
  repeated uint64 share_user_id = 3;
  repeated uint64 share_team_id = 4;
}

message ReqListeDocShareConfigSender{
  // 搜索名称关键词
  string name = 1;
  // 语言，为空默认zh
  string language = 2 [(validator) = "omitempty,oneof=zh en"];
}

message RspListeDocShareConfigSender{
  message SharedAssistant{
    uint64 id = 1;
    string name = 2;
    string name_en = 3;
    bool is_selected = 4;
  }

  repeated SharedAssistant assistants = 1;
}

message ReqGetAssistantsPage {
  // 分页偏移量
  uint32 offset = 1;
  // 分页大小
  uint32 limit = 2;
  // 所属用户ID
  repeated uint64 user_id = 3;
  // 所属团队ID
  repeated uint64 team_id = 4;
  // 创建时间范围
  tanlive.base.TimeRange create_date = 5;
  // 渠道
  repeated tanlive.ai.AssistantChannel channel = 6;
  // 是否启用
  tanlive.base.BoolEnum enabled = 7;
  // 知识库语言
  repeated string collection_lang = 8;
  // 是否确认协议
  tanlive.base.BoolEnum terms_confirmed = 9;
  // 排序：create_date、update_date
  repeated tanlive.base.OrderBy order_by = 10;
  // 助手ID
  repeated uint64 assistant_id = 11;
  // 路由
  string route_path = 12;
  // 助手名称
  string assistant_name = 13;
  // 模型
  repeated string model = 14;
  // 搜索引擎
  repeated string search_engine = 15;
  // 是否为草稿
  tanlive.base.BoolEnum is_draft = 16;
  // 批次号
  string batch_no = 17;
  // 文档ID
  uint64 doc_id = 18;
}

message RspGetAssistantsPage {
  // 助手列表
  repeated tanlive.ai.FullAssistant assistants = 1;
  // 总数
  uint32 total_account = 2;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 3;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 4;
  // 运营端用户卡片列表
  repeated tanlive.mgmt.UserCard mgmt_user_cards = 5;
}

message ReqCreateAssistant {
  // 配置列表
  tanlive.ai.AssistantConfig config = 1 [(validator) = "required"];
  // 是否保存为草稿
  bool is_draft = 2;
}

message RspCreateAssistant {
  // 助手ID
  uint64 assistant_id = 1;
}

message ReqBatchCreateAssistant {
  // 配置列表
  repeated tanlive.ai.AssistantConfig configs = 1 [(validator) = "required,dive,required"];
  // 是否保存为草稿
  bool is_draft = 2;
}

message RspBatchCreateAssistant {
  // 批次号
  string batch_no = 1;
  // 助手ID列表
  repeated uint64 assistant_id = 2;
}

message ReqUpdateAssistant {
  // 助手ID
  uint64 assistant_id = 1 [(validator) = "required"];
  // 配置详情
  tanlive.ai.AssistantConfig config = 2 [(validator) = "required"];
  // Field mask
  google.protobuf.FieldMask mask = 3 [(validator) = "required"];
  // 是否保存为草稿（已发布的助手忽略该参数）
  bool is_draft = 4;
}

message ReqBatchUpdateAssistant {
  message Item {
    // 助手ID
    uint64 assistant_id = 1;
    // 配置详情
    tanlive.ai.AssistantConfig config = 2 [(validator) = "required"];
    // Field mask
    google.protobuf.FieldMask mask = 3 [(validator) = "required"];
  }
  // 助手列表
  repeated Item items = 1 [(validator) = "required,dive,required"];
  // 是否保存为草稿（已发布的助手忽略该参数）
  bool is_draft = 2;
  // 批次号
  // 如果指定了批次号，items里的助手必须都属于该批次号，且批次号内的助手必须为草稿，允许新增、删除；未指定批次号时items里的助手必须为非草稿，仅允许更新
  string batch_no = 3;
}

message ReqDeleteAssistant {
  // 助手ID
  repeated uint64 assistant_id = 1;
  // 批次号
  string batch_no = 2;
}

message ReqGetAssistantLogsPage {
  // 分页偏移量
  uint32 offset = 1;
  // 分页大小
  uint32 limit = 2;
  // 助手ID
  uint64 assistant_id = 3 [(validator) = "required"];
  // 操作类型
  repeated tanlive.ai.AssistantAction action = 4 [(validator) = "omitempty,dive,required"];
  // 操作时间范围
  tanlive.base.TimeRange create_date = 5;
}

message RspGetAssistantLogsPage {
  // 日志列表
  repeated tanlive.ai.AssistantLog logs = 1;
  // 总数
  uint32 total_count = 2;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 3;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 4;
  // 运营端用户卡片列表
  repeated tanlive.mgmt.UserCard mgmt_user_cards = 5;
}

message RspGetAssistantOptions {
  // 对话模型
  repeated string chat_model = 1;
  // ChatOrSql模型
  repeated string chat_or_sql_model = 2;
  // 解析图谱模型
  repeated string graph_parse_mode = 3;
  // 搜索引擎
  repeated string search_engine = 4;
  // 互动暗号
  repeated tanlive.ai.InteractiveCodeOption interactive_code = 5;
  // 链路查询
  repeated tanlive.ai.VisibleChainOption visible_chain = 6;
  // 问题建议模型
  repeated string ask_suggestion_model = 7;
  // 向量化模型
  repeated tanlive.ai.EmbeddingModelOption embedding_model = 8;
  // 对话模型
  repeated tanlive.ai.ChatModelOption chat_model_v2 = 9;
  // 对话模型
  repeated tanlive.ai.SearchEngineOption search_engine_v2 = 10;
  // 小程序URL白名单
  repeated string mini_white_url = 12;
}

message ReqAutoChunkDoc {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 新文本（如果文本未改动不需要传值）
  string new_text = 2;
  // 自动分段参数
  tanlive.ai.AutoChunkPara auto_para = 3 [(validator) = "required"];
}

message RspAutoChunkDoc {
  // 分段列表
  repeated tanlive.ai.ChunkItem chunks = 1;
}

message ReqManualChunkDoc {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 新文本（如果文本未改动不需要传值）
  string new_text = 2;
  // 手动分段参数
  tanlive.ai.ManualChunkPara manual_para = 3 [(validator) = "required"];
}

message RspManualChunkDoc {
  // 任务ID
  uint64 task_id = 1;
}

message ReqGetDocChunks {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 助手ID
  repeated uint64 assistant_id = 2;
}

message RspGetDocChunks {
  // 助手的分段列表
  repeated tanlive.ai.AssistantChunks assistant_chunks = 1;
}

message ReqGetChunkDocTasks {
  // 文档ID
  repeated uint64 doc_id = 1 [(validator) = "required,dive,required"];
}

message RspGetChunkDocTasks {
  // 任务列表
  repeated tanlive.ai.DocChunkTask tasks = 1;
}

message ReqGetDocEmbeddingModels {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
}

message RspGetDocEmbeddingModels {
  // 向量化模型列表
  repeated tanlive.ai.EmbeddingModelCount embedding_models = 1;
}

message ReqBatchUpdateDocAttr{
  repeated uint64 id = 1 ;
  google.protobuf.FieldMask mask = 2 [(validator) = "required"];
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 4;
  // 是否显示贡献者
  uint32 show_contributor = 5;
  // 参考资料下载方式
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 6;
  // 修改关联的助手，增加的助手启用/禁用状态和已有的助手状态一致
  repeated uint64 assistant_id = 7;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 8;
  // 参考资料
  repeated tanlive.ai.DocReference reference = 9;
  uint64 query_id = 10;
}

message RspBatchUpdateDocAttr {
  bool async = 1;
}

message RspUpdateDocAttrInBulk{

}

// 获取文件文本知识提示请求
message ReqGetTextFileTip {
  // 文档ID
  uint64 id = 1;
}

// 获取QA知识提示请求
message ReqGetQaTip {
  // 文档ID
  uint64 id = 1;
}

// 获取文件文本知识提示响应
message RspGetTextFileTip {
  // 表头过长的表格
  repeated tanlive.ai.TextFileTipTableOverSize table_over_size = 1;
  // 解析状态
  tanlive.ai.DocState state = 2;
  // 内容重复信息
  repeated string repeated = 3;
}

// 获取QA知识提示响应
message RspGetQaTip {
  // 问题超长提示
  bool question_over_size = 1;
  // 内容重复信息
  repeated string repeated = 2;
}
