// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package events

import (
	context "context"
	event "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/event"
	tanlive "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	v3 "github.com/asim/go-micro/v3"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

type IamV1TopicListener interface {
	OnTagInfoChanged(context.Context, *TagInfoChanged) error
	OnReviewStateChanged(context.Context, *ReviewStateChanged) error
}

func SubscribeIamV1Topic(topic string, s server.Server, l IamV1TopicListener, queue string) error {
	return v3.RegisterSubscriber(topic, s, func(ctx context.Context, evt *tanlive.Event) error {
		if evt == nil || evt.Metadata == nil {
			return nil
		}
		ctx = event.MetadataToContext(ctx, evt.Metadata)
		switch evt.Metadata.Name {
		case "tanlive.events.TagInfoChanged":
			data := &TagInfoChanged{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnTagInfoChanged(ctx, data)
		case "tanlive.events.ReviewStateChanged":
			data := &ReviewStateChanged{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnReviewStateChanged(ctx, data)
		}
		return nil
	}, server.SubscriberQueue(queue))
}

type IamV1TopicPublisher interface {
	PublishTagInfoChangedEvent(context.Context, *TagInfoChanged) error
	PublishReviewStateChangedEvent(context.Context, *ReviewStateChanged) error
}

func NewIamV1TopicPublisher(topic string, c client.Client) IamV1TopicPublisher {
	return &iamV1TopicPublisher{
		event: event.NewEvent(topic, c),
	}
}

type iamV1TopicPublisher struct {
	event event.Event
}

func (p *iamV1TopicPublisher) PublishTagInfoChangedEvent(ctx context.Context, data *TagInfoChanged) error {
	return p.event.PublishEvent(ctx, "tanlive.events.TagInfoChanged", data)
}

func (p *iamV1TopicPublisher) PublishReviewStateChangedEvent(ctx context.Context, data *ReviewStateChanged) error {
	return p.event.PublishEvent(ctx, "tanlive.events.ReviewStateChanged", data)
}

type AppV1TopicListener interface {
	OnTagInfoChanged(context.Context, *TagInfoChanged) error
	OnReviewStateChanged(context.Context, *ReviewStateChanged) error
}

func SubscribeAppV1Topic(topic string, s server.Server, l AppV1TopicListener, queue string) error {
	return v3.RegisterSubscriber(topic, s, func(ctx context.Context, evt *tanlive.Event) error {
		if evt == nil || evt.Metadata == nil {
			return nil
		}
		ctx = event.MetadataToContext(ctx, evt.Metadata)
		switch evt.Metadata.Name {
		case "tanlive.events.TagInfoChanged":
			data := &TagInfoChanged{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnTagInfoChanged(ctx, data)
		case "tanlive.events.ReviewStateChanged":
			data := &ReviewStateChanged{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnReviewStateChanged(ctx, data)
		}
		return nil
	}, server.SubscriberQueue(queue))
}

type AppV1TopicPublisher interface {
	PublishTagInfoChangedEvent(context.Context, *TagInfoChanged) error
	PublishReviewStateChangedEvent(context.Context, *ReviewStateChanged) error
}

func NewAppV1TopicPublisher(topic string, c client.Client) AppV1TopicPublisher {
	return &appV1TopicPublisher{
		event: event.NewEvent(topic, c),
	}
}

type appV1TopicPublisher struct {
	event event.Event
}

func (p *appV1TopicPublisher) PublishTagInfoChangedEvent(ctx context.Context, data *TagInfoChanged) error {
	return p.event.PublishEvent(ctx, "tanlive.events.TagInfoChanged", data)
}

func (p *appV1TopicPublisher) PublishReviewStateChangedEvent(ctx context.Context, data *ReviewStateChanged) error {
	return p.event.PublishEvent(ctx, "tanlive.events.ReviewStateChanged", data)
}
