// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package events

import (
	context "context"
	event "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/event"
	tanlive "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	v3 "github.com/asim/go-micro/v3"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

type SearchTopicListener interface {
}

func SubscribeSearchTopic(topic string, s server.Server, l SearchTopicListener, queue string) error {
	return v3.RegisterSubscriber(topic, s, func(ctx context.Context, evt *tanlive.Event) error {
		if evt == nil || evt.Metadata == nil {
			return nil
		}
		ctx = event.MetadataToContext(ctx, evt.Metadata)
		switch evt.Metadata.Name {
		}
		return nil
	}, server.SubscriberQueue(queue))
}

type SearchTopicPublisher interface {
}

func NewSearchTopicPublisher(topic string, c client.Client) SearchTopicPublisher {
	return &searchTopicPublisher{
		event: event.NewEvent(topic, c),
	}
}

type searchTopicPublisher struct {
	event event.Event
}
