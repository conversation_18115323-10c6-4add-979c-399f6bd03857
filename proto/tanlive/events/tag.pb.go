// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/events/tag.proto

package events

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	tag "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TagInfoChanged struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 替换
	Replace []*TagInfoChanged_Replace `protobuf:"bytes,1,rep,name=replace,proto3" json:"replace,omitempty"`
	// 删除
	DeleteTagIds []uint64 `protobuf:"varint,2,rep,packed,name=delete_tag_ids,json=deleteTagIds,proto3" json:"delete_tag_ids,omitempty"`
	// 受影响的正式ugcID
	DataIds []uint64 `protobuf:"varint,3,rep,packed,name=data_ids,json=dataIds,proto3" json:"data_ids,omitempty"`
	// 受影响的正式ugc类型
	DataType base.DataType           `protobuf:"varint,4,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	Cover    *TagInfoChanged_TagItem `protobuf:"bytes,5,opt,name=cover,proto3" json:"cover,omitempty"`
	// 绑定(与data_ids 字段配置和使用)
	BindingTag []*TagInfoChanged_TagItem `protobuf:"bytes,6,rep,name=binding_tag,json=bindingTag,proto3" json:"binding_tag,omitempty"`
	// 解绑(与data_ids 字段配置和使用)
	UnbindingTag []*TagInfoChanged_TagItem `protobuf:"bytes,7,rep,name=unbinding_tag,json=unbindingTag,proto3" json:"unbinding_tag,omitempty"`
	// 迁移data数据，与（data_type 配合使用）
	Migrate *TagInfoChanged_Migrate `protobuf:"bytes,8,opt,name=migrate,proto3" json:"migrate,omitempty"`
}

func (x *TagInfoChanged) Reset() {
	*x = TagInfoChanged{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_tag_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagInfoChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagInfoChanged) ProtoMessage() {}

func (x *TagInfoChanged) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_tag_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagInfoChanged.ProtoReflect.Descriptor instead.
func (*TagInfoChanged) Descriptor() ([]byte, []int) {
	return file_tanlive_events_tag_proto_rawDescGZIP(), []int{0}
}

func (x *TagInfoChanged) GetReplace() []*TagInfoChanged_Replace {
	if x != nil {
		return x.Replace
	}
	return nil
}

func (x *TagInfoChanged) GetDeleteTagIds() []uint64 {
	if x != nil {
		return x.DeleteTagIds
	}
	return nil
}

func (x *TagInfoChanged) GetDataIds() []uint64 {
	if x != nil {
		return x.DataIds
	}
	return nil
}

func (x *TagInfoChanged) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *TagInfoChanged) GetCover() *TagInfoChanged_TagItem {
	if x != nil {
		return x.Cover
	}
	return nil
}

func (x *TagInfoChanged) GetBindingTag() []*TagInfoChanged_TagItem {
	if x != nil {
		return x.BindingTag
	}
	return nil
}

func (x *TagInfoChanged) GetUnbindingTag() []*TagInfoChanged_TagItem {
	if x != nil {
		return x.UnbindingTag
	}
	return nil
}

func (x *TagInfoChanged) GetMigrate() *TagInfoChanged_Migrate {
	if x != nil {
		return x.Migrate
	}
	return nil
}

type WeixinOfficialAccountTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 需要打标的用户id
	UserIds []uint64 `protobuf:"varint,1,rep,packed,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	// 绑定的标签id
	BindingTagId uint64 `protobuf:"varint,2,opt,name=binding_tag_id,json=bindingTagId,proto3" json:"binding_tag_id,omitempty"`
	// 解绑的标签id
	UnbindingTagId uint64 `protobuf:"varint,3,opt,name=unbinding_tag_id,json=unbindingTagId,proto3" json:"unbinding_tag_id,omitempty"`
}

func (x *WeixinOfficialAccountTag) Reset() {
	*x = WeixinOfficialAccountTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_tag_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeixinOfficialAccountTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeixinOfficialAccountTag) ProtoMessage() {}

func (x *WeixinOfficialAccountTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_tag_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeixinOfficialAccountTag.ProtoReflect.Descriptor instead.
func (*WeixinOfficialAccountTag) Descriptor() ([]byte, []int) {
	return file_tanlive_events_tag_proto_rawDescGZIP(), []int{1}
}

func (x *WeixinOfficialAccountTag) GetUserIds() []uint64 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *WeixinOfficialAccountTag) GetBindingTagId() uint64 {
	if x != nil {
		return x.BindingTagId
	}
	return 0
}

func (x *WeixinOfficialAccountTag) GetUnbindingTagId() uint64 {
	if x != nil {
		return x.UnbindingTagId
	}
	return 0
}

type TagInfoChanged_Replace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromTagIds []uint64 `protobuf:"varint,1,rep,packed,name=from_tag_ids,json=fromTagIds,proto3" json:"from_tag_ids,omitempty"`
	ToTagId    uint64   `protobuf:"varint,2,opt,name=to_tag_id,json=toTagId,proto3" json:"to_tag_id,omitempty"`
}

func (x *TagInfoChanged_Replace) Reset() {
	*x = TagInfoChanged_Replace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_tag_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagInfoChanged_Replace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagInfoChanged_Replace) ProtoMessage() {}

func (x *TagInfoChanged_Replace) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_tag_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagInfoChanged_Replace.ProtoReflect.Descriptor instead.
func (*TagInfoChanged_Replace) Descriptor() ([]byte, []int) {
	return file_tanlive_events_tag_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TagInfoChanged_Replace) GetFromTagIds() []uint64 {
	if x != nil {
		return x.FromTagIds
	}
	return nil
}

func (x *TagInfoChanged_Replace) GetToTagId() uint64 {
	if x != nil {
		return x.ToTagId
	}
	return 0
}

type TagInfoChanged_TagItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagIds       []uint64         `protobuf:"varint,1,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	TaggableType tag.TaggableType `protobuf:"varint,2,opt,name=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggableType,omitempty"`
}

func (x *TagInfoChanged_TagItem) Reset() {
	*x = TagInfoChanged_TagItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_tag_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagInfoChanged_TagItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagInfoChanged_TagItem) ProtoMessage() {}

func (x *TagInfoChanged_TagItem) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_tag_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagInfoChanged_TagItem.ProtoReflect.Descriptor instead.
func (*TagInfoChanged_TagItem) Descriptor() ([]byte, []int) {
	return file_tanlive_events_tag_proto_rawDescGZIP(), []int{0, 1}
}

func (x *TagInfoChanged_TagItem) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *TagInfoChanged_TagItem) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

type TagInfoChanged_Migrate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From uint64 `protobuf:"varint,1,opt,name=from,proto3" json:"from,omitempty"`
	To   uint64 `protobuf:"varint,2,opt,name=to,proto3" json:"to,omitempty"`
}

func (x *TagInfoChanged_Migrate) Reset() {
	*x = TagInfoChanged_Migrate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_tag_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagInfoChanged_Migrate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagInfoChanged_Migrate) ProtoMessage() {}

func (x *TagInfoChanged_Migrate) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_tag_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagInfoChanged_Migrate.ProtoReflect.Descriptor instead.
func (*TagInfoChanged_Migrate) Descriptor() ([]byte, []int) {
	return file_tanlive_events_tag_proto_rawDescGZIP(), []int{0, 2}
}

func (x *TagInfoChanged_Migrate) GetFrom() uint64 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *TagInfoChanged_Migrate) GetTo() uint64 {
	if x != nil {
		return x.To
	}
	return 0
}

var File_tanlive_events_tag_proto protoreflect.FileDescriptor

var file_tanlive_events_tag_proto_rawDesc = []byte{
	0x0a, 0x18, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x74, 0x61, 0x67, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb9, 0x05,
	0x0a, 0x0e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64,
	0x12, 0x40, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x64, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x61, 0x67,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x54, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61,
	0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x0b, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x61, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x2e, 0x54, 0x61, 0x67, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x12,
	0x4b, 0x0a, 0x0d, 0x75, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x61, 0x67,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c,
	0x75, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x12, 0x40, 0x0a, 0x07,
	0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54,
	0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x2e, 0x4d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x07, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x1a, 0x47,
	0x0a, 0x07, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x09, 0x74,
	0x6f, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x74, 0x6f, 0x54, 0x61, 0x67, 0x49, 0x64, 0x1a, 0x61, 0x0a, 0x07, 0x54, 0x61, 0x67, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x74,
	0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x61,
	0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x2d, 0x0a, 0x07, 0x4d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x74, 0x6f, 0x22, 0x85, 0x01, 0x0a, 0x18, 0x57, 0x65,
	0x69, 0x78, 0x69, 0x6e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x61, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x6e, 0x62, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0e, 0x75, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x49,
	0x64, 0x32, 0x10, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x1a, 0x04, 0xc8,
	0xc6, 0x27, 0x01, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_events_tag_proto_rawDescOnce sync.Once
	file_tanlive_events_tag_proto_rawDescData = file_tanlive_events_tag_proto_rawDesc
)

func file_tanlive_events_tag_proto_rawDescGZIP() []byte {
	file_tanlive_events_tag_proto_rawDescOnce.Do(func() {
		file_tanlive_events_tag_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_events_tag_proto_rawDescData)
	})
	return file_tanlive_events_tag_proto_rawDescData
}

var file_tanlive_events_tag_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_tanlive_events_tag_proto_goTypes = []interface{}{
	(*TagInfoChanged)(nil),           // 0: tanlive.events.TagInfoChanged
	(*WeixinOfficialAccountTag)(nil), // 1: tanlive.events.WeixinOfficialAccountTag
	(*TagInfoChanged_Replace)(nil),   // 2: tanlive.events.TagInfoChanged.Replace
	(*TagInfoChanged_TagItem)(nil),   // 3: tanlive.events.TagInfoChanged.TagItem
	(*TagInfoChanged_Migrate)(nil),   // 4: tanlive.events.TagInfoChanged.Migrate
	(base.DataType)(0),               // 5: tanlive.base.DataType
	(tag.TaggableType)(0),            // 6: tanlive.tag.TaggableType
}
var file_tanlive_events_tag_proto_depIdxs = []int32{
	2, // 0: tanlive.events.TagInfoChanged.replace:type_name -> tanlive.events.TagInfoChanged.Replace
	5, // 1: tanlive.events.TagInfoChanged.data_type:type_name -> tanlive.base.DataType
	3, // 2: tanlive.events.TagInfoChanged.cover:type_name -> tanlive.events.TagInfoChanged.TagItem
	3, // 3: tanlive.events.TagInfoChanged.binding_tag:type_name -> tanlive.events.TagInfoChanged.TagItem
	3, // 4: tanlive.events.TagInfoChanged.unbinding_tag:type_name -> tanlive.events.TagInfoChanged.TagItem
	4, // 5: tanlive.events.TagInfoChanged.migrate:type_name -> tanlive.events.TagInfoChanged.Migrate
	6, // 6: tanlive.events.TagInfoChanged.TagItem.taggableType:type_name -> tanlive.tag.TaggableType
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_tanlive_events_tag_proto_init() }
func file_tanlive_events_tag_proto_init() {
	if File_tanlive_events_tag_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_events_tag_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagInfoChanged); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_events_tag_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeixinOfficialAccountTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_events_tag_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagInfoChanged_Replace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_events_tag_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagInfoChanged_TagItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_events_tag_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagInfoChanged_Migrate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_events_tag_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_events_tag_proto_goTypes,
		DependencyIndexes: file_tanlive_events_tag_proto_depIdxs,
		MessageInfos:      file_tanlive_events_tag_proto_msgTypes,
	}.Build()
	File_tanlive_events_tag_proto = out.File
	file_tanlive_events_tag_proto_rawDesc = nil
	file_tanlive_events_tag_proto_goTypes = nil
	file_tanlive_events_tag_proto_depIdxs = nil
}
