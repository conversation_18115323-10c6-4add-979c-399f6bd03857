syntax = "proto3";

package tanlive.events;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events";

import "tanlive/options.proto";
import "google/protobuf/empty.proto";
import "tanlive/base/ugc.proto";
import "tanlive/tag/tag.proto";

message TagInfoChanged {
  message Replace {
    repeated uint64 from_tag_ids = 1;
    uint64 to_tag_id = 2;
  }
  message TagItem {
    repeated uint64 tag_ids = 1;
    tanlive.tag.TaggableType taggableType = 2;
  }
  message Migrate {
    uint64 from = 1;
    uint64 to = 2;
  }
  // 替换
  repeated Replace replace = 1;
  // 删除
  repeated uint64 delete_tag_ids = 2;
  // 受影响的正式ugcID
  repeated uint64 data_ids = 3;
  // 受影响的正式ugc类型
  tanlive.base.DataType data_type = 4;
  TagItem cover = 5;
  // 绑定(与data_ids 字段配置和使用)
  repeated TagItem binding_tag = 6;
  // 解绑(与data_ids 字段配置和使用)
  repeated TagItem unbinding_tag = 7;
  // 迁移data数据，与（data_type 配合使用）
  Migrate migrate = 8;
}

message WeixinOfficialAccountTag {
  // 需要打标的用户id
  repeated uint64 user_ids = 1;
  // 绑定的标签id
  uint64 binding_tag_id = 2;
  // 解绑的标签id
  uint64 unbinding_tag_id = 3;
}

// 搜索主题
service TagTopic {
  option (tanlive.event) = true;
}
