// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/events/review.proto

package events

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	review "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 审核状态发生变化
type ReviewStateChanged struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReviewInfo *review.ReviewInfo `protobuf:"bytes,1,opt,name=review_info,json=reviewInfo,proto3" json:"review_info,omitempty"`
}

func (x *ReviewStateChanged) Reset() {
	*x = ReviewStateChanged{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_review_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewStateChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewStateChanged) ProtoMessage() {}

func (x *ReviewStateChanged) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_review_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewStateChanged.ProtoReflect.Descriptor instead.
func (*ReviewStateChanged) Descriptor() ([]byte, []int) {
	return file_tanlive_events_review_proto_rawDescGZIP(), []int{0}
}

func (x *ReviewStateChanged) GetReviewInfo() *review.ReviewInfo {
	if x != nil {
		return x.ReviewInfo
	}
	return nil
}

var File_tanlive_events_review_proto protoreflect.FileDescriptor

var file_tanlive_events_review_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x63, 0x72, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x51, 0x0a, 0x12, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x12,
	0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x32, 0x51, 0x0a, 0x0b,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x3c, 0x0a, 0x09, 0x4f,
	0x6e, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x12, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f,
	0x62, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x04, 0xc8, 0xc6, 0x27, 0x01, 0x42,
	0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f,
	0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_events_review_proto_rawDescOnce sync.Once
	file_tanlive_events_review_proto_rawDescData = file_tanlive_events_review_proto_rawDesc
)

func file_tanlive_events_review_proto_rawDescGZIP() []byte {
	file_tanlive_events_review_proto_rawDescOnce.Do(func() {
		file_tanlive_events_review_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_events_review_proto_rawDescData)
	})
	return file_tanlive_events_review_proto_rawDescData
}

var file_tanlive_events_review_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tanlive_events_review_proto_goTypes = []interface{}{
	(*ReviewStateChanged)(nil), // 0: tanlive.events.ReviewStateChanged
	(*review.ReviewInfo)(nil),  // 1: tanlive.review.ReviewInfo
	(*CronJob)(nil),            // 2: tanlive.events.CronJob
	(*emptypb.Empty)(nil),      // 3: google.protobuf.Empty
}
var file_tanlive_events_review_proto_depIdxs = []int32{
	1, // 0: tanlive.events.ReviewStateChanged.review_info:type_name -> tanlive.review.ReviewInfo
	2, // 1: tanlive.events.ReviewTopic.OnCronJob:input_type -> tanlive.events.CronJob
	3, // 2: tanlive.events.ReviewTopic.OnCronJob:output_type -> google.protobuf.Empty
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tanlive_events_review_proto_init() }
func file_tanlive_events_review_proto_init() {
	if File_tanlive_events_review_proto != nil {
		return
	}
	file_tanlive_events_cron_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_events_review_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewStateChanged); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_events_review_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_events_review_proto_goTypes,
		DependencyIndexes: file_tanlive_events_review_proto_depIdxs,
		MessageInfos:      file_tanlive_events_review_proto_msgTypes,
	}.Build()
	File_tanlive_events_review_proto = out.File
	file_tanlive_events_review_proto_rawDesc = nil
	file_tanlive_events_review_proto_goTypes = nil
	file_tanlive_events_review_proto_depIdxs = nil
}
