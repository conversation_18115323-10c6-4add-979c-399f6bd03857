syntax = "proto3";

package tanlive.events;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events";

import "google/protobuf/empty.proto";
import "tanlive/events/cron.proto";
import "tanlive/options.proto";
import "tanlive/review/review.proto";

// 审核状态发生变化
message ReviewStateChanged {
  tanlive.review.ReviewInfo review_info = 1;
}

// 审核主题
service ReviewTopic {
  option (tanlive.event) = true;

  // 处理定时任务
  rpc OnCronJob(CronJob) returns (google.protobuf.Empty);
}
