// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package events

import (
	context "context"
	event "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/event"
	tanlive "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	v3 "github.com/asim/go-micro/v3"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
	proto "google.golang.org/protobuf/proto"
)

func (x *TagInfoChanged) MaskInLog() any {
	if x == nil {
		return (*TagInfoChanged)(nil)
	}

	y := proto.Clone(x).(*TagInfoChanged)
	for k, v := range y.Replace {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Replace[k] = vv.MaskInLog().(*TagInfoChanged_Replace)
		}
	}
	if v, ok := any(y.Cover).(interface{ MaskInLog() any }); ok {
		y.Cover = v.MaskInLog().(*TagInfoChanged_TagItem)
	}
	for k, v := range y.BindingTag {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingTag[k] = vv.MaskInLog().(*TagInfoChanged_TagItem)
		}
	}
	for k, v := range y.UnbindingTag {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UnbindingTag[k] = vv.MaskInLog().(*TagInfoChanged_TagItem)
		}
	}
	if v, ok := any(y.Migrate).(interface{ MaskInLog() any }); ok {
		y.Migrate = v.MaskInLog().(*TagInfoChanged_Migrate)
	}

	return y
}

func (x *TagInfoChanged) MaskInRpc() any {
	if x == nil {
		return (*TagInfoChanged)(nil)
	}

	y := x
	for k, v := range y.Replace {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Replace[k] = vv.MaskInRpc().(*TagInfoChanged_Replace)
		}
	}
	if v, ok := any(y.Cover).(interface{ MaskInRpc() any }); ok {
		y.Cover = v.MaskInRpc().(*TagInfoChanged_TagItem)
	}
	for k, v := range y.BindingTag {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingTag[k] = vv.MaskInRpc().(*TagInfoChanged_TagItem)
		}
	}
	for k, v := range y.UnbindingTag {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UnbindingTag[k] = vv.MaskInRpc().(*TagInfoChanged_TagItem)
		}
	}
	if v, ok := any(y.Migrate).(interface{ MaskInRpc() any }); ok {
		y.Migrate = v.MaskInRpc().(*TagInfoChanged_Migrate)
	}

	return y
}

func (x *TagInfoChanged) MaskInBff() any {
	if x == nil {
		return (*TagInfoChanged)(nil)
	}

	y := x
	for k, v := range y.Replace {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Replace[k] = vv.MaskInBff().(*TagInfoChanged_Replace)
		}
	}
	if v, ok := any(y.Cover).(interface{ MaskInBff() any }); ok {
		y.Cover = v.MaskInBff().(*TagInfoChanged_TagItem)
	}
	for k, v := range y.BindingTag {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingTag[k] = vv.MaskInBff().(*TagInfoChanged_TagItem)
		}
	}
	for k, v := range y.UnbindingTag {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UnbindingTag[k] = vv.MaskInBff().(*TagInfoChanged_TagItem)
		}
	}
	if v, ok := any(y.Migrate).(interface{ MaskInBff() any }); ok {
		y.Migrate = v.MaskInBff().(*TagInfoChanged_Migrate)
	}

	return y
}

func (x *TagInfoChanged) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Replace {
		if sanitizer, ok := any(x.Replace[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Cover).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.BindingTag {
		if sanitizer, ok := any(x.BindingTag[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UnbindingTag {
		if sanitizer, ok := any(x.UnbindingTag[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Migrate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

type TagTopicListener interface {
}

func SubscribeTagTopic(topic string, s server.Server, l TagTopicListener, queue string) error {
	return v3.RegisterSubscriber(topic, s, func(ctx context.Context, evt *tanlive.Event) error {
		if evt == nil || evt.Metadata == nil {
			return nil
		}
		ctx = event.MetadataToContext(ctx, evt.Metadata)
		switch evt.Metadata.Name {
		}
		return nil
	}, server.SubscriberQueue(queue))
}

type TagTopicPublisher interface {
}

func NewTagTopicPublisher(topic string, c client.Client) TagTopicPublisher {
	return &tagTopicPublisher{
		event: event.NewEvent(topic, c),
	}
}

type tagTopicPublisher struct {
	event event.Event
}
