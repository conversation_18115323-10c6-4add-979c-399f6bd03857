syntax = "proto3";

package tanlive.events;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events";

import "google/protobuf/empty.proto";
import "tanlive/events/review.proto";
import "tanlive/events/tag.proto";
import "tanlive/options.proto";

// iam主题（v1）
service IamV1Topic {
  option (tanlive.event) = true;

  // 标签信息变更
  rpc OnTagInfoChanged(TagInfoChanged) returns (google.protobuf.Empty);
  // 审核状态发生变化
  rpc OnReviewStateChanged(ReviewStateChanged) returns (google.protobuf.Empty);
}

// iam主题（v1）
service AppV1Topic {
  option (tanlive.event) = true;

  // 标签信息变更
  rpc OnTagInfoChanged(TagInfoChanged) returns (google.protobuf.Empty);
  // 审核状态发生变化
  rpc OnReviewStateChanged(ReviewStateChanged) returns (google.protobuf.Empty);
}
