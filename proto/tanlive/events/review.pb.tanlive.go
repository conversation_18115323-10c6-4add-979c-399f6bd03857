// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package events

import (
	context "context"
	event "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/event"
	tanlive "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	review "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	v3 "github.com/asim/go-micro/v3"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
	proto "google.golang.org/protobuf/proto"
)

func (x *ReviewStateChanged) MaskInLog() any {
	if x == nil {
		return (*ReviewStateChanged)(nil)
	}

	y := proto.Clone(x).(*ReviewStateChanged)
	if v, ok := any(y.ReviewInfo).(interface{ MaskInLog() any }); ok {
		y.ReviewInfo = v.MaskInLog().(*review.ReviewInfo)
	}

	return y
}

func (x *ReviewStateChanged) MaskInRpc() any {
	if x == nil {
		return (*ReviewStateChanged)(nil)
	}

	y := x
	if v, ok := any(y.ReviewInfo).(interface{ MaskInRpc() any }); ok {
		y.ReviewInfo = v.MaskInRpc().(*review.ReviewInfo)
	}

	return y
}

func (x *ReviewStateChanged) MaskInBff() any {
	if x == nil {
		return (*ReviewStateChanged)(nil)
	}

	y := x
	if v, ok := any(y.ReviewInfo).(interface{ MaskInBff() any }); ok {
		y.ReviewInfo = v.MaskInBff().(*review.ReviewInfo)
	}

	return y
}

func (x *ReviewStateChanged) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ReviewInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

type ReviewTopicListener interface {
	OnCronJob(context.Context, *CronJob) error
}

func SubscribeReviewTopic(topic string, s server.Server, l ReviewTopicListener, queue string) error {
	return v3.RegisterSubscriber(topic, s, func(ctx context.Context, evt *tanlive.Event) error {
		if evt == nil || evt.Metadata == nil {
			return nil
		}
		ctx = event.MetadataToContext(ctx, evt.Metadata)
		switch evt.Metadata.Name {
		case "tanlive.events.CronJob":
			data := &CronJob{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnCronJob(ctx, data)
		}
		return nil
	}, server.SubscriberQueue(queue))
}

type ReviewTopicPublisher interface {
	PublishCronJobEvent(context.Context, *CronJob) error
}

func NewReviewTopicPublisher(topic string, c client.Client) ReviewTopicPublisher {
	return &reviewTopicPublisher{
		event: event.NewEvent(topic, c),
	}
}

type reviewTopicPublisher struct {
	event event.Event
}

func (p *reviewTopicPublisher) PublishCronJobEvent(ctx context.Context, data *CronJob) error {
	return p.event.PublishEvent(ctx, "tanlive.events.CronJob", data)
}
