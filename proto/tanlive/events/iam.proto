syntax = "proto3";

package tanlive.events;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events";

import "tanlive/options.proto";
import "tanlive/events/tag.proto";
import "google/protobuf/empty.proto";

// iam主题（v2）
service IamV2Topic {
  option (tanlive.event) = true;

  // 微信公众号用户打标
  rpc LabelWeixinOfficialAccount(WeixinOfficialAccountTag) returns (google.protobuf.Empty);
}