// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package events

import (
	context "context"
	event "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/event"
	tanlive "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	v3 "github.com/asim/go-micro/v3"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

type IamV2TopicListener interface {
	LabelWeixinOfficialAccount(context.Context, *WeixinOfficialAccountTag) error
}

func SubscribeIamV2Topic(topic string, s server.Server, l IamV2TopicListener, queue string) error {
	return v3.RegisterSubscriber(topic, s, func(ctx context.Context, evt *tanlive.Event) error {
		if evt == nil || evt.Metadata == nil {
			return nil
		}
		ctx = event.MetadataToContext(ctx, evt.Metadata)
		switch evt.Metadata.Name {
		case "tanlive.events.WeixinOfficialAccountTag":
			data := &WeixinOfficialAccountTag{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.LabelWeixinOfficialAccount(ctx, data)
		}
		return nil
	}, server.SubscriberQueue(queue))
}

type IamV2TopicPublisher interface {
	PublishWeixinOfficialAccountTagEvent(context.Context, *WeixinOfficialAccountTag) error
}

func NewIamV2TopicPublisher(topic string, c client.Client) IamV2TopicPublisher {
	return &iamV2TopicPublisher{
		event: event.NewEvent(topic, c),
	}
}

type iamV2TopicPublisher struct {
	event event.Event
}

func (p *iamV2TopicPublisher) PublishWeixinOfficialAccountTagEvent(ctx context.Context, data *WeixinOfficialAccountTag) error {
	return p.event.PublishEvent(ctx, "tanlive.events.WeixinOfficialAccountTag", data)
}
