// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/review/review.proto

package review

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 审核类型
type ReviewType int32

const (
	ReviewType_REVIEW_TYPE_UNSPECIFIED ReviewType = 0
	// 人工审核
	ReviewType_REVIEW_TYPE_MANUAL ReviewType = 1
	// 自动审核
	ReviewType_REVIEW_TYPE_AUTO ReviewType = 2
	// 申诉
	ReviewType_REVIEW_TYPE_APPEAL ReviewType = 3
	// 抽查
	ReviewType_REVIEW_TYPE_RECHECK ReviewType = 4
)

// Enum value maps for ReviewType.
var (
	ReviewType_name = map[int32]string{
		0: "REVIEW_TYPE_UNSPECIFIED",
		1: "REVIEW_TYPE_MANUAL",
		2: "REVIEW_TYPE_AUTO",
		3: "REVIEW_TYPE_APPEAL",
		4: "REVIEW_TYPE_RECHECK",
	}
	ReviewType_value = map[string]int32{
		"REVIEW_TYPE_UNSPECIFIED": 0,
		"REVIEW_TYPE_MANUAL":      1,
		"REVIEW_TYPE_AUTO":        2,
		"REVIEW_TYPE_APPEAL":      3,
		"REVIEW_TYPE_RECHECK":     4,
	}
)

func (x ReviewType) Enum() *ReviewType {
	p := new(ReviewType)
	*p = x
	return p
}

func (x ReviewType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_review_review_proto_enumTypes[0].Descriptor()
}

func (ReviewType) Type() protoreflect.EnumType {
	return &file_tanlive_review_review_proto_enumTypes[0]
}

func (x ReviewType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewType.Descriptor instead.
func (ReviewType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{0}
}

// 审核状态
type ReviewState int32

const (
	ReviewState_REVIEW_STATE_UNSPECIFIED ReviewState = 0
	// 审核中
	ReviewState_REVIEW_STATE_REVIEWING ReviewState = 1
	// 已驳回
	ReviewState_REVIEW_STATE_REJECTED ReviewState = 2
	// 已通过
	ReviewState_REVIEW_STATE_PASSED ReviewState = 3
	// 审核失败
	ReviewState_REVIEW_STATE_FAILED ReviewState = 4
	// 已撤回
	ReviewState_REVIEW_STATE_REVOKED ReviewState = 5
	// 审核结果可疑
	ReviewState_REVIEW_STATE_SUSPICIOUS ReviewState = 6
)

// Enum value maps for ReviewState.
var (
	ReviewState_name = map[int32]string{
		0: "REVIEW_STATE_UNSPECIFIED",
		1: "REVIEW_STATE_REVIEWING",
		2: "REVIEW_STATE_REJECTED",
		3: "REVIEW_STATE_PASSED",
		4: "REVIEW_STATE_FAILED",
		5: "REVIEW_STATE_REVOKED",
		6: "REVIEW_STATE_SUSPICIOUS",
	}
	ReviewState_value = map[string]int32{
		"REVIEW_STATE_UNSPECIFIED": 0,
		"REVIEW_STATE_REVIEWING":   1,
		"REVIEW_STATE_REJECTED":    2,
		"REVIEW_STATE_PASSED":      3,
		"REVIEW_STATE_FAILED":      4,
		"REVIEW_STATE_REVOKED":     5,
		"REVIEW_STATE_SUSPICIOUS":  6,
	}
)

func (x ReviewState) Enum() *ReviewState {
	p := new(ReviewState)
	*p = x
	return p
}

func (x ReviewState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewState) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_review_review_proto_enumTypes[1].Descriptor()
}

func (ReviewState) Type() protoreflect.EnumType {
	return &file_tanlive_review_review_proto_enumTypes[1]
}

func (x ReviewState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewState.Descriptor instead.
func (ReviewState) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{1}
}

// 自动任务类型
type ReviewJobType int32

const (
	ReviewJobType_REVIEW_JOB_TYPE_UNSPECIFIED ReviewJobType = 0
	// 文本
	ReviewJobType_REVIEW_JOB_TYPE_TEXT ReviewJobType = 1
	// 图片
	ReviewJobType_REVIEW_JOB_TYPE_IMAGE ReviewJobType = 2
	// 视频
	ReviewJobType_REVIEW_JOB_TYPE_VIDEO ReviewJobType = 3
	// 文档
	ReviewJobType_REVIEW_JOB_TYPE_DOC ReviewJobType = 4
	// 音频
	ReviewJobType_REVIEW_JOB_TYPE_AUDIO ReviewJobType = 5
)

// Enum value maps for ReviewJobType.
var (
	ReviewJobType_name = map[int32]string{
		0: "REVIEW_JOB_TYPE_UNSPECIFIED",
		1: "REVIEW_JOB_TYPE_TEXT",
		2: "REVIEW_JOB_TYPE_IMAGE",
		3: "REVIEW_JOB_TYPE_VIDEO",
		4: "REVIEW_JOB_TYPE_DOC",
		5: "REVIEW_JOB_TYPE_AUDIO",
	}
	ReviewJobType_value = map[string]int32{
		"REVIEW_JOB_TYPE_UNSPECIFIED": 0,
		"REVIEW_JOB_TYPE_TEXT":        1,
		"REVIEW_JOB_TYPE_IMAGE":       2,
		"REVIEW_JOB_TYPE_VIDEO":       3,
		"REVIEW_JOB_TYPE_DOC":         4,
		"REVIEW_JOB_TYPE_AUDIO":       5,
	}
)

func (x ReviewJobType) Enum() *ReviewJobType {
	p := new(ReviewJobType)
	*p = x
	return p
}

func (x ReviewJobType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewJobType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_review_review_proto_enumTypes[2].Descriptor()
}

func (ReviewJobType) Type() protoreflect.EnumType {
	return &file_tanlive_review_review_proto_enumTypes[2]
}

func (x ReviewJobType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewJobType.Descriptor instead.
func (ReviewJobType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{2}
}

// 审核策略
type ReviewStrategy int32

const (
	ReviewStrategy_REVIEW_STRATEGY_UNSPECIFIED ReviewStrategy = 0
	// 文本忽略广告
	ReviewStrategy_REVIEW_STRATEGY_TEXT_IGNORE_ADS ReviewStrategy = 1
	// 图片忽略广告
	ReviewStrategy_REVIEW_STRATEGY_IMAGE_IGNORE_ADS ReviewStrategy = 2
	// AI对话
	ReviewStrategy_REVIEW_STRATEGY_TEXT_AI_CHAT ReviewStrategy = 3
)

// Enum value maps for ReviewStrategy.
var (
	ReviewStrategy_name = map[int32]string{
		0: "REVIEW_STRATEGY_UNSPECIFIED",
		1: "REVIEW_STRATEGY_TEXT_IGNORE_ADS",
		2: "REVIEW_STRATEGY_IMAGE_IGNORE_ADS",
		3: "REVIEW_STRATEGY_TEXT_AI_CHAT",
	}
	ReviewStrategy_value = map[string]int32{
		"REVIEW_STRATEGY_UNSPECIFIED":      0,
		"REVIEW_STRATEGY_TEXT_IGNORE_ADS":  1,
		"REVIEW_STRATEGY_IMAGE_IGNORE_ADS": 2,
		"REVIEW_STRATEGY_TEXT_AI_CHAT":     3,
	}
)

func (x ReviewStrategy) Enum() *ReviewStrategy {
	p := new(ReviewStrategy)
	*p = x
	return p
}

func (x ReviewStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_review_review_proto_enumTypes[3].Descriptor()
}

func (ReviewStrategy) Type() protoreflect.EnumType {
	return &file_tanlive_review_review_proto_enumTypes[3]
}

func (x ReviewStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewStrategy.Descriptor instead.
func (ReviewStrategy) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{3}
}

// COS桶类型
type CosBucketType int32

const (
	CosBucketType_COS_BUCKET_TYPE_UNSPECIFIED CosBucketType = 0
	// 公有桶
	CosBucketType_COS_BUCKET_TYPE_PUBLIC CosBucketType = 1
	// 私有桶
	CosBucketType_COS_BUCKET_TYPE_PRIVATE CosBucketType = 2
)

// Enum value maps for CosBucketType.
var (
	CosBucketType_name = map[int32]string{
		0: "COS_BUCKET_TYPE_UNSPECIFIED",
		1: "COS_BUCKET_TYPE_PUBLIC",
		2: "COS_BUCKET_TYPE_PRIVATE",
	}
	CosBucketType_value = map[string]int32{
		"COS_BUCKET_TYPE_UNSPECIFIED": 0,
		"COS_BUCKET_TYPE_PUBLIC":      1,
		"COS_BUCKET_TYPE_PRIVATE":     2,
	}
)

func (x CosBucketType) Enum() *CosBucketType {
	p := new(CosBucketType)
	*p = x
	return p
}

func (x CosBucketType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CosBucketType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_review_review_proto_enumTypes[4].Descriptor()
}

func (CosBucketType) Type() protoreflect.EnumType {
	return &file_tanlive_review_review_proto_enumTypes[4]
}

func (x CosBucketType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CosBucketType.Descriptor instead.
func (CosBucketType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{4}
}

// 驳回原因类型
type RejectReasonType int32

const (
	// 其它
	RejectReasonType_REJECT_REASON_TYPE_OTHER RejectReasonType = 0
	// 内容违反使用条款
	RejectReasonType_REJECT_REASON_TYPE_VIOLATE_TERMS RejectReasonType = 1
	// 内容含不支持的语言
	RejectReasonType_REJECT_REASON_TYPE_UNSUPPORTED_LANG RejectReasonType = 2
	// 消息信息缺失
	RejectReasonType_REJECT_REASON_TYPE_NOTIFY_INFO_MISSING RejectReasonType = 3
	// 消息中有错别字或标点使用不当
	RejectReasonType_REJECT_REASON_TYPE_NOTIFY_HAS_TYPO RejectReasonType = 4
	// 消息模版选择错误
	RejectReasonType_REJECT_REASON_TYPE_NOTIFY_WRONG_TMPL RejectReasonType = 5
	// 团队简称已存在
	RejectReasonType_REJECT_REASON_TYPE_TEAM_SHORT_NAME_ALREADY_EXISTS RejectReasonType = 6
)

// Enum value maps for RejectReasonType.
var (
	RejectReasonType_name = map[int32]string{
		0: "REJECT_REASON_TYPE_OTHER",
		1: "REJECT_REASON_TYPE_VIOLATE_TERMS",
		2: "REJECT_REASON_TYPE_UNSUPPORTED_LANG",
		3: "REJECT_REASON_TYPE_NOTIFY_INFO_MISSING",
		4: "REJECT_REASON_TYPE_NOTIFY_HAS_TYPO",
		5: "REJECT_REASON_TYPE_NOTIFY_WRONG_TMPL",
		6: "REJECT_REASON_TYPE_TEAM_SHORT_NAME_ALREADY_EXISTS",
	}
	RejectReasonType_value = map[string]int32{
		"REJECT_REASON_TYPE_OTHER":                          0,
		"REJECT_REASON_TYPE_VIOLATE_TERMS":                  1,
		"REJECT_REASON_TYPE_UNSUPPORTED_LANG":               2,
		"REJECT_REASON_TYPE_NOTIFY_INFO_MISSING":            3,
		"REJECT_REASON_TYPE_NOTIFY_HAS_TYPO":                4,
		"REJECT_REASON_TYPE_NOTIFY_WRONG_TMPL":              5,
		"REJECT_REASON_TYPE_TEAM_SHORT_NAME_ALREADY_EXISTS": 6,
	}
)

func (x RejectReasonType) Enum() *RejectReasonType {
	p := new(RejectReasonType)
	*p = x
	return p
}

func (x RejectReasonType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RejectReasonType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_review_review_proto_enumTypes[5].Descriptor()
}

func (RejectReasonType) Type() protoreflect.EnumType {
	return &file_tanlive_review_review_proto_enumTypes[5]
}

func (x RejectReasonType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RejectReasonType.Descriptor instead.
func (RejectReasonType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{5}
}

// 命中的关键词
type HitKeyword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 关键词
	Keyword []string `protobuf:"bytes,1,rep,name=keyword,proto3" json:"keyword,omitempty"`
	// 标签
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *HitKeyword) Reset() {
	*x = HitKeyword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_review_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HitKeyword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HitKeyword) ProtoMessage() {}

func (x *HitKeyword) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_review_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HitKeyword.ProtoReflect.Descriptor instead.
func (*HitKeyword) Descriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{0}
}

func (x *HitKeyword) GetKeyword() []string {
	if x != nil {
		return x.Keyword
	}
	return nil
}

func (x *HitKeyword) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

// 审核任务结果
type ReviewJobResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据字段
	DataField string `protobuf:"bytes,1,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
	// 任务结果
	JobResult int32 `protobuf:"varint,2,opt,name=job_result,json=jobResult,proto3" json:"job_result,omitempty"`
	// 任务标签
	JobLabel string `protobuf:"bytes,3,opt,name=job_label,json=jobLabel,proto3" json:"job_label,omitempty"`
	// 命中的敏感词
	Keywords []*HitKeyword `protobuf:"bytes,4,rep,name=keywords,proto3" json:"keywords,omitempty"`
	// 任务审核的内容。文本类型为text_content，其他类型为file_path
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *ReviewJobResult) Reset() {
	*x = ReviewJobResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_review_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewJobResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewJobResult) ProtoMessage() {}

func (x *ReviewJobResult) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_review_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewJobResult.ProtoReflect.Descriptor instead.
func (*ReviewJobResult) Descriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{1}
}

func (x *ReviewJobResult) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

func (x *ReviewJobResult) GetJobResult() int32 {
	if x != nil {
		return x.JobResult
	}
	return 0
}

func (x *ReviewJobResult) GetJobLabel() string {
	if x != nil {
		return x.JobLabel
	}
	return ""
}

func (x *ReviewJobResult) GetKeywords() []*HitKeyword {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *ReviewJobResult) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// 驳回原因
type RejectReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 驳回原因类型
	ReasonType RejectReasonType `protobuf:"varint,1,opt,name=reason_type,json=reasonType,proto3,enum=tanlive.review.RejectReasonType" json:"reason_type,omitempty"`
	// 其它原因
	OtherReason string `protobuf:"bytes,2,opt,name=other_reason,json=otherReason,proto3" json:"other_reason,omitempty"`
	// 自动任务结果
	AutoJobResults []*ReviewJobResult `protobuf:"bytes,3,rep,name=auto_job_results,json=autoJobResults,proto3" json:"auto_job_results,omitempty"`
}

func (x *RejectReason) Reset() {
	*x = RejectReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_review_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RejectReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectReason) ProtoMessage() {}

func (x *RejectReason) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_review_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectReason.ProtoReflect.Descriptor instead.
func (*RejectReason) Descriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{2}
}

func (x *RejectReason) GetReasonType() RejectReasonType {
	if x != nil {
		return x.ReasonType
	}
	return RejectReasonType_REJECT_REASON_TYPE_OTHER
}

func (x *RejectReason) GetOtherReason() string {
	if x != nil {
		return x.OtherReason
	}
	return ""
}

func (x *RejectReason) GetAutoJobResults() []*ReviewJobResult {
	if x != nil {
		return x.AutoJobResults
	}
	return nil
}

// 审核备注
type ReviewRemark struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 备注内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 图片列表
	Images []string `protobuf:"bytes,2,rep,name=images,proto3" json:"images,omitempty"`
}

func (x *ReviewRemark) Reset() {
	*x = ReviewRemark{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_review_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewRemark) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRemark) ProtoMessage() {}

func (x *ReviewRemark) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_review_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRemark.ProtoReflect.Descriptor instead.
func (*ReviewRemark) Descriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{3}
}

func (x *ReviewRemark) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ReviewRemark) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

// 审核信息
type ReviewInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 审核ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 创建时间
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 创建人
	CreateBy uint64 `protobuf:"varint,3,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// 更新时间
	UpdateDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	// 更新人
	UpdateBy uint64 `protobuf:"varint,5,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 数据ID
	DataId uint64 `protobuf:"varint,6,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	// 数据主ID
	DataMainId uint64 `protobuf:"varint,7,opt,name=data_main_id,json=dataMainId,proto3" json:"data_main_id,omitempty"`
	// 数据类型
	DataType base.DataType `protobuf:"varint,8,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 审核类型
	ReviewType ReviewType `protobuf:"varint,9,opt,name=review_type,json=reviewType,proto3,enum=tanlive.review.ReviewType" json:"review_type,omitempty"`
	// 审核状态
	ReviewState ReviewState `protobuf:"varint,10,opt,name=review_state,json=reviewState,proto3,enum=tanlive.review.ReviewState" json:"review_state,omitempty"`
	// 审核时间
	ReviewedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=reviewed_at,json=reviewedAt,proto3" json:"reviewed_at,omitempty"`
	// 审核人（自动审核为0）
	ReviewedBy uint64 `protobuf:"varint,12,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
	// 驳回时间
	RevokedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=revoked_at,json=revokedAt,proto3" json:"revoked_at,omitempty"`
	// 驳回人
	RevokedBy uint64 `protobuf:"varint,14,opt,name=revoked_by,json=revokedBy,proto3" json:"revoked_by,omitempty"`
	// 申诉对象ID
	AppealTarget uint64 `protobuf:"varint,15,opt,name=appeal_target,json=appealTarget,proto3" json:"appeal_target,omitempty"`
	// 驳回原因
	RejectReason *RejectReason `protobuf:"bytes,16,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
	// 申诉原因
	AppealReason *RejectReason `protobuf:"bytes,17,opt,name=appeal_reason,json=appealReason,proto3" json:"appeal_reason,omitempty"`
	// 申诉文件
	AppealFiles []string `protobuf:"bytes,18,rep,name=appeal_files,json=appealFiles,proto3" json:"appeal_files,omitempty"`
	// 上一次驳回原因
	LastRejectReason *RejectReason `protobuf:"bytes,19,opt,name=last_reject_reason,json=lastRejectReason,proto3" json:"last_reject_reason,omitempty"`
	// 备注
	Remark *ReviewRemark `protobuf:"bytes,20,opt,name=remark,proto3" json:"remark,omitempty"`
}

func (x *ReviewInfo) Reset() {
	*x = ReviewInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_review_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewInfo) ProtoMessage() {}

func (x *ReviewInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_review_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewInfo.ProtoReflect.Descriptor instead.
func (*ReviewInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{4}
}

func (x *ReviewInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewInfo) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *ReviewInfo) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *ReviewInfo) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *ReviewInfo) GetUpdateBy() uint64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *ReviewInfo) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *ReviewInfo) GetDataMainId() uint64 {
	if x != nil {
		return x.DataMainId
	}
	return 0
}

func (x *ReviewInfo) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReviewInfo) GetReviewType() ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return ReviewType_REVIEW_TYPE_UNSPECIFIED
}

func (x *ReviewInfo) GetReviewState() ReviewState {
	if x != nil {
		return x.ReviewState
	}
	return ReviewState_REVIEW_STATE_UNSPECIFIED
}

func (x *ReviewInfo) GetReviewedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedAt
	}
	return nil
}

func (x *ReviewInfo) GetReviewedBy() uint64 {
	if x != nil {
		return x.ReviewedBy
	}
	return 0
}

func (x *ReviewInfo) GetRevokedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RevokedAt
	}
	return nil
}

func (x *ReviewInfo) GetRevokedBy() uint64 {
	if x != nil {
		return x.RevokedBy
	}
	return 0
}

func (x *ReviewInfo) GetAppealTarget() uint64 {
	if x != nil {
		return x.AppealTarget
	}
	return 0
}

func (x *ReviewInfo) GetRejectReason() *RejectReason {
	if x != nil {
		return x.RejectReason
	}
	return nil
}

func (x *ReviewInfo) GetAppealReason() *RejectReason {
	if x != nil {
		return x.AppealReason
	}
	return nil
}

func (x *ReviewInfo) GetAppealFiles() []string {
	if x != nil {
		return x.AppealFiles
	}
	return nil
}

func (x *ReviewInfo) GetLastRejectReason() *RejectReason {
	if x != nil {
		return x.LastRejectReason
	}
	return nil
}

func (x *ReviewInfo) GetRemark() *ReviewRemark {
	if x != nil {
		return x.Remark
	}
	return nil
}

// 审核任务
type ReviewJob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 创建时间
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 创建人
	CreateBy uint64 `protobuf:"varint,3,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// 更新时间
	UpdateDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	// 更新人
	UpdateBy uint64 `protobuf:"varint,5,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 审核ID
	ReviewId uint64 `protobuf:"varint,6,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	// 数据字段
	DataField string `protobuf:"bytes,7,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
	// 文本内容
	TextContent string `protobuf:"bytes,8,opt,name=text_content,json=textContent,proto3" json:"text_content,omitempty"`
	// 文件路径
	FilePath string `protobuf:"bytes,9,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	// cos桶类型
	BucketType CosBucketType `protobuf:"varint,10,opt,name=bucket_type,json=bucketType,proto3,enum=tanlive.review.CosBucketType" json:"bucket_type,omitempty"`
	// 审核策略
	Strategy ReviewStrategy `protobuf:"varint,11,opt,name=strategy,proto3,enum=tanlive.review.ReviewStrategy" json:"strategy,omitempty"`
	// 任务已创建
	JobCreated bool `protobuf:"varint,12,opt,name=job_created,json=jobCreated,proto3" json:"job_created,omitempty"`
	// 外部任务ID
	JobId string `protobuf:"bytes,13,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	// 审核任务类型
	JobType ReviewJobType `protobuf:"varint,14,opt,name=job_type,json=jobType,proto3,enum=tanlive.review.ReviewJobType" json:"job_type,omitempty"`
	// 任务状态
	JobState string `protobuf:"bytes,15,opt,name=job_state,json=jobState,proto3" json:"job_state,omitempty"`
	// 任务创建时间
	JobCreationTime string `protobuf:"bytes,16,opt,name=job_creation_time,json=jobCreationTime,proto3" json:"job_creation_time,omitempty"`
	// 任务结果
	JobResult int32 `protobuf:"varint,17,opt,name=job_result,json=jobResult,proto3" json:"job_result,omitempty"`
	// 任务标签
	JobLabel string `protobuf:"bytes,18,opt,name=job_label,json=jobLabel,proto3" json:"job_label,omitempty"`
	// 命中的关键词
	HitKeywords []*HitKeyword `protobuf:"bytes,19,rep,name=hit_keywords,json=hitKeywords,proto3" json:"hit_keywords,omitempty"`
}

func (x *ReviewJob) Reset() {
	*x = ReviewJob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_review_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewJob) ProtoMessage() {}

func (x *ReviewJob) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_review_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewJob.ProtoReflect.Descriptor instead.
func (*ReviewJob) Descriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{5}
}

func (x *ReviewJob) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewJob) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *ReviewJob) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *ReviewJob) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *ReviewJob) GetUpdateBy() uint64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *ReviewJob) GetReviewId() uint64 {
	if x != nil {
		return x.ReviewId
	}
	return 0
}

func (x *ReviewJob) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

func (x *ReviewJob) GetTextContent() string {
	if x != nil {
		return x.TextContent
	}
	return ""
}

func (x *ReviewJob) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *ReviewJob) GetBucketType() CosBucketType {
	if x != nil {
		return x.BucketType
	}
	return CosBucketType_COS_BUCKET_TYPE_UNSPECIFIED
}

func (x *ReviewJob) GetStrategy() ReviewStrategy {
	if x != nil {
		return x.Strategy
	}
	return ReviewStrategy_REVIEW_STRATEGY_UNSPECIFIED
}

func (x *ReviewJob) GetJobCreated() bool {
	if x != nil {
		return x.JobCreated
	}
	return false
}

func (x *ReviewJob) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *ReviewJob) GetJobType() ReviewJobType {
	if x != nil {
		return x.JobType
	}
	return ReviewJobType_REVIEW_JOB_TYPE_UNSPECIFIED
}

func (x *ReviewJob) GetJobState() string {
	if x != nil {
		return x.JobState
	}
	return ""
}

func (x *ReviewJob) GetJobCreationTime() string {
	if x != nil {
		return x.JobCreationTime
	}
	return ""
}

func (x *ReviewJob) GetJobResult() int32 {
	if x != nil {
		return x.JobResult
	}
	return 0
}

func (x *ReviewJob) GetJobLabel() string {
	if x != nil {
		return x.JobLabel
	}
	return ""
}

func (x *ReviewJob) GetHitKeywords() []*HitKeyword {
	if x != nil {
		return x.HitKeywords
	}
	return nil
}

// 完整的审核信息
type FullReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 审核详情
	ReviewInfo *ReviewInfo `protobuf:"bytes,1,opt,name=review_info,json=reviewInfo,proto3" json:"review_info,omitempty"`
	// 任务列表
	Jobs []*ReviewJob `protobuf:"bytes,2,rep,name=jobs,proto3" json:"jobs,omitempty"`
}

func (x *FullReview) Reset() {
	*x = FullReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_review_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FullReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullReview) ProtoMessage() {}

func (x *FullReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_review_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullReview.ProtoReflect.Descriptor instead.
func (*FullReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_review_proto_rawDescGZIP(), []int{6}
}

func (x *FullReview) GetReviewInfo() *ReviewInfo {
	if x != nil {
		return x.ReviewInfo
	}
	return nil
}

func (x *FullReview) GetJobs() []*ReviewJob {
	if x != nil {
		return x.Jobs
	}
	return nil
}

var File_tanlive_review_review_proto protoreflect.FileDescriptor

var file_tanlive_review_review_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3c, 0x0a,
	0x0a, 0x48, 0x69, 0x74, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0xbe, 0x01, 0x0a, 0x0f,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x6a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6a, 0x6f, 0x62, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x36, 0x0a, 0x08, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x48, 0x69,
	0x74, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xbf, 0x01, 0x0a,
	0x0c, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x41, 0x0a,
	0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6a, 0x6f, 0x62, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0e,
	0x61, 0x75, 0x74, 0x6f, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x3a,
	0x0a, 0x0c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x22, 0xc5, 0x07, 0x0a, 0x0a, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x17, 0x0a,
	0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d,
	0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x64, 0x61,
	0x74, 0x61, 0x4d, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a,
	0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x42, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x72, 0x65, 0x76, 0x6f,
	0x6b, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x65, 0x61,
	0x6c, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x41, 0x0a, 0x0d, 0x72, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0c, 0x72, 0x65,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0d, 0x61, 0x70,
	0x70, 0x65, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x12, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x4a, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74,
	0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x22, 0xfd, 0x05, 0x0a, 0x09, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x3e, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x3a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x6a, 0x6f, 0x62, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x6a, 0x6f, 0x62, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a,
	0x6f, 0x62, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x08, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6a,
	0x6f, 0x62, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6a, 0x6f, 0x62,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x3d, 0x0a, 0x0c, 0x68, 0x69, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x48, 0x69, 0x74, 0x4b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x0b, 0x68, 0x69, 0x74, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x73, 0x22, 0x78, 0x0a, 0x0a, 0x46, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x0a,
	0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x2a, 0x88, 0x01, 0x0a,
	0x0a, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x55, 0x54, 0x4f, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x45, 0x41, 0x4c, 0x10, 0x03, 0x12, 0x17,
	0x0a, 0x13, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45,
	0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x04, 0x2a, 0xcb, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x53,
	0x53, 0x45, 0x44, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x18,
	0x0a, 0x14, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x49, 0x43, 0x49,
	0x4f, 0x55, 0x53, 0x10, 0x06, 0x2a, 0xb4, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x4a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x4a, 0x4f, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x4a, 0x4f, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x58, 0x54,
	0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x4a, 0x4f, 0x42,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x19, 0x0a,
	0x15, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x4a, 0x4f, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x4a, 0x4f, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x10,
	0x04, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x4a, 0x4f, 0x42, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x10, 0x05, 0x2a, 0x9e, 0x01, 0x0a,
	0x0e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12,
	0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45,
	0x47, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54,
	0x45, 0x47, 0x59, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f,
	0x41, 0x44, 0x53, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x49,
	0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x44, 0x53, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x54,
	0x45, 0x58, 0x54, 0x5f, 0x41, 0x49, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x10, 0x03, 0x2a, 0x69, 0x0a,
	0x0d, 0x43, 0x6f, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x1b, 0x43, 0x4f, 0x53, 0x5f, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1a, 0x0a, 0x16, 0x43, 0x4f, 0x53, 0x5f, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x43,
	0x4f, 0x53, 0x5f, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50,
	0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x10, 0x02, 0x2a, 0xb4, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x18, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x52,
	0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x56, 0x49, 0x4f, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x53, 0x10,
	0x01, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52,
	0x54, 0x45, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x52, 0x45,
	0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x4d, 0x49, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x59, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x4f, 0x10, 0x04, 0x12, 0x28,
	0x0a, 0x24, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x57, 0x52, 0x4f, 0x4e,
	0x47, 0x5f, 0x54, 0x4d, 0x50, 0x4c, 0x10, 0x05, 0x12, 0x35, 0x0a, 0x31, 0x52, 0x45, 0x4a, 0x45,
	0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54,
	0x45, 0x41, 0x4d, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41,
	0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x06, 0x42,
	0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f,
	0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_review_review_proto_rawDescOnce sync.Once
	file_tanlive_review_review_proto_rawDescData = file_tanlive_review_review_proto_rawDesc
)

func file_tanlive_review_review_proto_rawDescGZIP() []byte {
	file_tanlive_review_review_proto_rawDescOnce.Do(func() {
		file_tanlive_review_review_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_review_review_proto_rawDescData)
	})
	return file_tanlive_review_review_proto_rawDescData
}

var file_tanlive_review_review_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_tanlive_review_review_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_tanlive_review_review_proto_goTypes = []interface{}{
	(ReviewType)(0),               // 0: tanlive.review.ReviewType
	(ReviewState)(0),              // 1: tanlive.review.ReviewState
	(ReviewJobType)(0),            // 2: tanlive.review.ReviewJobType
	(ReviewStrategy)(0),           // 3: tanlive.review.ReviewStrategy
	(CosBucketType)(0),            // 4: tanlive.review.CosBucketType
	(RejectReasonType)(0),         // 5: tanlive.review.RejectReasonType
	(*HitKeyword)(nil),            // 6: tanlive.review.HitKeyword
	(*ReviewJobResult)(nil),       // 7: tanlive.review.ReviewJobResult
	(*RejectReason)(nil),          // 8: tanlive.review.RejectReason
	(*ReviewRemark)(nil),          // 9: tanlive.review.ReviewRemark
	(*ReviewInfo)(nil),            // 10: tanlive.review.ReviewInfo
	(*ReviewJob)(nil),             // 11: tanlive.review.ReviewJob
	(*FullReview)(nil),            // 12: tanlive.review.FullReview
	(*timestamppb.Timestamp)(nil), // 13: google.protobuf.Timestamp
	(base.DataType)(0),            // 14: tanlive.base.DataType
}
var file_tanlive_review_review_proto_depIdxs = []int32{
	6,  // 0: tanlive.review.ReviewJobResult.keywords:type_name -> tanlive.review.HitKeyword
	5,  // 1: tanlive.review.RejectReason.reason_type:type_name -> tanlive.review.RejectReasonType
	7,  // 2: tanlive.review.RejectReason.auto_job_results:type_name -> tanlive.review.ReviewJobResult
	13, // 3: tanlive.review.ReviewInfo.create_date:type_name -> google.protobuf.Timestamp
	13, // 4: tanlive.review.ReviewInfo.update_date:type_name -> google.protobuf.Timestamp
	14, // 5: tanlive.review.ReviewInfo.data_type:type_name -> tanlive.base.DataType
	0,  // 6: tanlive.review.ReviewInfo.review_type:type_name -> tanlive.review.ReviewType
	1,  // 7: tanlive.review.ReviewInfo.review_state:type_name -> tanlive.review.ReviewState
	13, // 8: tanlive.review.ReviewInfo.reviewed_at:type_name -> google.protobuf.Timestamp
	13, // 9: tanlive.review.ReviewInfo.revoked_at:type_name -> google.protobuf.Timestamp
	8,  // 10: tanlive.review.ReviewInfo.reject_reason:type_name -> tanlive.review.RejectReason
	8,  // 11: tanlive.review.ReviewInfo.appeal_reason:type_name -> tanlive.review.RejectReason
	8,  // 12: tanlive.review.ReviewInfo.last_reject_reason:type_name -> tanlive.review.RejectReason
	9,  // 13: tanlive.review.ReviewInfo.remark:type_name -> tanlive.review.ReviewRemark
	13, // 14: tanlive.review.ReviewJob.create_date:type_name -> google.protobuf.Timestamp
	13, // 15: tanlive.review.ReviewJob.update_date:type_name -> google.protobuf.Timestamp
	4,  // 16: tanlive.review.ReviewJob.bucket_type:type_name -> tanlive.review.CosBucketType
	3,  // 17: tanlive.review.ReviewJob.strategy:type_name -> tanlive.review.ReviewStrategy
	2,  // 18: tanlive.review.ReviewJob.job_type:type_name -> tanlive.review.ReviewJobType
	6,  // 19: tanlive.review.ReviewJob.hit_keywords:type_name -> tanlive.review.HitKeyword
	10, // 20: tanlive.review.FullReview.review_info:type_name -> tanlive.review.ReviewInfo
	11, // 21: tanlive.review.FullReview.jobs:type_name -> tanlive.review.ReviewJob
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_tanlive_review_review_proto_init() }
func file_tanlive_review_review_proto_init() {
	if File_tanlive_review_review_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_review_review_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HitKeyword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_review_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewJobResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_review_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RejectReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_review_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewRemark); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_review_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_review_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewJob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_review_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FullReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_review_review_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_review_review_proto_goTypes,
		DependencyIndexes: file_tanlive_review_review_proto_depIdxs,
		EnumInfos:         file_tanlive_review_review_proto_enumTypes,
		MessageInfos:      file_tanlive_review_review_proto_msgTypes,
	}.Build()
	File_tanlive_review_review_proto = out.File
	file_tanlive_review_review_proto_rawDesc = nil
	file_tanlive_review_review_proto_goTypes = nil
	file_tanlive_review_review_proto_depIdxs = nil
}
