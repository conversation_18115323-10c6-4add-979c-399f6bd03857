syntax = "proto3";

package tanlive.review;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review";

import "google/protobuf/timestamp.proto";
import "tanlive/base/ugc.proto";
import "tanlive/options.proto";

// 审核类型
enum ReviewType {
  REVIEW_TYPE_UNSPECIFIED = 0;
  // 人工审核
  REVIEW_TYPE_MANUAL = 1;
  // 自动审核
  REVIEW_TYPE_AUTO = 2;
  // 申诉
  REVIEW_TYPE_APPEAL = 3;
  // 抽查
  REVIEW_TYPE_RECHECK = 4;
}

// 审核状态
enum ReviewState {
  REVIEW_STATE_UNSPECIFIED = 0;
  // 审核中
  REVIEW_STATE_REVIEWING = 1;
  // 已驳回
  REVIEW_STATE_REJECTED = 2;
  // 已通过
  REVIEW_STATE_PASSED = 3;
  // 审核失败
  REVIEW_STATE_FAILED = 4;
  // 已撤回
  REVIEW_STATE_REVOKED = 5;
  // 审核结果可疑
  REVIEW_STATE_SUSPICIOUS = 6;
}

// 自动任务类型
enum ReviewJobType {
  REVIEW_JOB_TYPE_UNSPECIFIED = 0;
  // 文本
  REVIEW_JOB_TYPE_TEXT = 1;
  // 图片
  REVIEW_JOB_TYPE_IMAGE = 2;
  // 视频
  REVIEW_JOB_TYPE_VIDEO = 3;
  // 文档
  REVIEW_JOB_TYPE_DOC = 4;
  // 音频
  REVIEW_JOB_TYPE_AUDIO = 5;
}

// 审核策略
enum ReviewStrategy {
  REVIEW_STRATEGY_UNSPECIFIED = 0;
  // 文本忽略广告
  REVIEW_STRATEGY_TEXT_IGNORE_ADS = 1;
  // 图片忽略广告
  REVIEW_STRATEGY_IMAGE_IGNORE_ADS = 2;
  // AI对话
  REVIEW_STRATEGY_TEXT_AI_CHAT = 3;
}

// COS桶类型
enum CosBucketType {
  COS_BUCKET_TYPE_UNSPECIFIED = 0;
  // 公有桶
  COS_BUCKET_TYPE_PUBLIC = 1;
  // 私有桶
  COS_BUCKET_TYPE_PRIVATE = 2;
}

// 驳回原因类型
enum RejectReasonType {
  // 其它
  REJECT_REASON_TYPE_OTHER = 0;
  // 内容违反使用条款
  REJECT_REASON_TYPE_VIOLATE_TERMS = 1;
  // 内容含不支持的语言
  REJECT_REASON_TYPE_UNSUPPORTED_LANG = 2;
  // 消息信息缺失
  REJECT_REASON_TYPE_NOTIFY_INFO_MISSING = 3;
  // 消息中有错别字或标点使用不当
  REJECT_REASON_TYPE_NOTIFY_HAS_TYPO = 4;
  // 消息模版选择错误
  REJECT_REASON_TYPE_NOTIFY_WRONG_TMPL = 5;
  // 团队简称已存在
  REJECT_REASON_TYPE_TEAM_SHORT_NAME_ALREADY_EXISTS = 6;
}

// 命中的关键词
message HitKeyword {
  // 关键词
  repeated string keyword = 1;
  // 标签
  string label = 2;
}

// 审核任务结果
message ReviewJobResult {
  // 数据字段
  string data_field = 1;
  // 任务结果
  int32 job_result = 2;
  // 任务标签
  string job_label = 3;
  // 命中的敏感词
  repeated HitKeyword keywords = 4;
  // 任务审核的内容。文本类型为text_content，其他类型为file_path
  string content = 5;
}

// 驳回原因
message RejectReason {
  // 驳回原因类型
  RejectReasonType reason_type = 1;
  // 其它原因
  string other_reason = 2;
  // 自动任务结果
  repeated ReviewJobResult auto_job_results = 3;
}

// 审核备注
message ReviewRemark{
  // 备注内容
  string text = 1;
  // 图片列表
  repeated string images = 2;
}


// 审核信息
message ReviewInfo {
  // 审核ID
  uint64 id = 1;
  // 创建时间
  google.protobuf.Timestamp create_date = 2;
  // 创建人
  uint64 create_by = 3;
  // 更新时间
  google.protobuf.Timestamp update_date = 4;
  // 更新人
  uint64 update_by = 5;
  // 数据ID
  uint64 data_id = 6;
  // 数据主ID
  uint64 data_main_id = 7;
  // 数据类型
  base.DataType data_type = 8;
  // 审核类型
  ReviewType review_type = 9;
  // 审核状态
  ReviewState review_state = 10;
  // 审核时间
  google.protobuf.Timestamp reviewed_at = 11;
  // 审核人（自动审核为0）
  uint64 reviewed_by = 12;
  // 驳回时间
  google.protobuf.Timestamp revoked_at = 13;
  // 驳回人
  uint64 revoked_by = 14;
  // 申诉对象ID
  uint64 appeal_target = 15;
  // 驳回原因
  RejectReason reject_reason = 16;
  // 申诉原因
  RejectReason appeal_reason = 17;
  // 申诉文件
  repeated string appeal_files = 18;
  // 上一次驳回原因
  RejectReason last_reject_reason = 19;
  // 备注
  ReviewRemark remark = 20;
}

// 审核任务
message ReviewJob {
  // 任务ID
  uint64 id = 1;
  // 创建时间
  google.protobuf.Timestamp create_date = 2;
  // 创建人
  uint64 create_by = 3;
  // 更新时间
  google.protobuf.Timestamp update_date = 4;
  // 更新人
  uint64 update_by = 5;
  // 审核ID
  uint64 review_id = 6;
  // 数据字段
  string data_field = 7;
  // 文本内容
  string text_content = 8;
  // 文件路径
  string file_path = 9;
  // cos桶类型
  CosBucketType bucket_type = 10;
  // 审核策略
  ReviewStrategy strategy = 11;
  // 任务已创建
  bool job_created = 12;
  // 外部任务ID
  string job_id = 13;
  // 审核任务类型
  ReviewJobType job_type = 14;
  // 任务状态
  string job_state = 15;
  // 任务创建时间
  string job_creation_time = 16;
  // 任务结果
  int32 job_result = 17;
  // 任务标签
  string job_label = 18;
  // 命中的关键词
  repeated HitKeyword hit_keywords = 19;
}

// 完整的审核信息
message FullReview {
  // 审核详情
  ReviewInfo review_info = 1;
  // 任务列表
  repeated ReviewJob jobs = 2;
}
