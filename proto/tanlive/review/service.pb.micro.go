// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/review/service.proto

package review

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for ReviewService service

func NewReviewServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for ReviewService service

type ReviewService interface {
	// 创建审核
	CreateReview(ctx context.Context, in *ReqCreateReview, opts ...client.CallOption) (*RspCreateReview, error)
	// 通过审核
	PassReview(ctx context.Context, in *ReqPassReview, opts ...client.CallOption) (*emptypb.Empty, error)
	// 驳回审核
	RejectReview(ctx context.Context, in *ReqRejectReview, opts ...client.CallOption) (*emptypb.Empty, error)
	// 撤销审核
	RevokeReview(ctx context.Context, in *ReqRevokeReview, opts ...client.CallOption) (*RspRevokeReview, error)
	// 申诉审核
	AppealReview(ctx context.Context, in *ReqAppealReview, opts ...client.CallOption) (*RspAppealReview, error)
	// 查询审核列表
	GetReviews(ctx context.Context, in *ReqGetReviews, opts ...client.CallOption) (*RspGetReviews, error)
	// 处理数据万象审核回调
	HandleCiReviewCallback(ctx context.Context, in *ReqHandleCiReviewCallback, opts ...client.CallOption) (*emptypb.Empty, error)
	// 创建文本审核
	CreateTextReview(ctx context.Context, in *ReqCreateTextReview, opts ...client.CallOption) (*RspCreateTextReview, error)
}

type reviewService struct {
	c    client.Client
	name string
}

func NewReviewService(name string, c client.Client) ReviewService {
	return &reviewService{
		c:    c,
		name: name,
	}
}

func (c *reviewService) CreateReview(ctx context.Context, in *ReqCreateReview, opts ...client.CallOption) (*RspCreateReview, error) {
	req := c.c.NewRequest(c.name, "ReviewService.CreateReview", in)
	out := new(RspCreateReview)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewService) PassReview(ctx context.Context, in *ReqPassReview, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "ReviewService.PassReview", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewService) RejectReview(ctx context.Context, in *ReqRejectReview, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "ReviewService.RejectReview", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewService) RevokeReview(ctx context.Context, in *ReqRevokeReview, opts ...client.CallOption) (*RspRevokeReview, error) {
	req := c.c.NewRequest(c.name, "ReviewService.RevokeReview", in)
	out := new(RspRevokeReview)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewService) AppealReview(ctx context.Context, in *ReqAppealReview, opts ...client.CallOption) (*RspAppealReview, error) {
	req := c.c.NewRequest(c.name, "ReviewService.AppealReview", in)
	out := new(RspAppealReview)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewService) GetReviews(ctx context.Context, in *ReqGetReviews, opts ...client.CallOption) (*RspGetReviews, error) {
	req := c.c.NewRequest(c.name, "ReviewService.GetReviews", in)
	out := new(RspGetReviews)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewService) HandleCiReviewCallback(ctx context.Context, in *ReqHandleCiReviewCallback, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "ReviewService.HandleCiReviewCallback", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewService) CreateTextReview(ctx context.Context, in *ReqCreateTextReview, opts ...client.CallOption) (*RspCreateTextReview, error) {
	req := c.c.NewRequest(c.name, "ReviewService.CreateTextReview", in)
	out := new(RspCreateTextReview)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ReviewService service

type ReviewServiceHandler interface {
	// 创建审核
	CreateReview(context.Context, *ReqCreateReview, *RspCreateReview) error
	// 通过审核
	PassReview(context.Context, *ReqPassReview, *emptypb.Empty) error
	// 驳回审核
	RejectReview(context.Context, *ReqRejectReview, *emptypb.Empty) error
	// 撤销审核
	RevokeReview(context.Context, *ReqRevokeReview, *RspRevokeReview) error
	// 申诉审核
	AppealReview(context.Context, *ReqAppealReview, *RspAppealReview) error
	// 查询审核列表
	GetReviews(context.Context, *ReqGetReviews, *RspGetReviews) error
	// 处理数据万象审核回调
	HandleCiReviewCallback(context.Context, *ReqHandleCiReviewCallback, *emptypb.Empty) error
	// 创建文本审核
	CreateTextReview(context.Context, *ReqCreateTextReview, *RspCreateTextReview) error
}

func RegisterReviewServiceHandler(s server.Server, hdlr ReviewServiceHandler, opts ...server.HandlerOption) error {
	type reviewService interface {
		CreateReview(ctx context.Context, in *ReqCreateReview, out *RspCreateReview) error
		PassReview(ctx context.Context, in *ReqPassReview, out *emptypb.Empty) error
		RejectReview(ctx context.Context, in *ReqRejectReview, out *emptypb.Empty) error
		RevokeReview(ctx context.Context, in *ReqRevokeReview, out *RspRevokeReview) error
		AppealReview(ctx context.Context, in *ReqAppealReview, out *RspAppealReview) error
		GetReviews(ctx context.Context, in *ReqGetReviews, out *RspGetReviews) error
		HandleCiReviewCallback(ctx context.Context, in *ReqHandleCiReviewCallback, out *emptypb.Empty) error
		CreateTextReview(ctx context.Context, in *ReqCreateTextReview, out *RspCreateTextReview) error
	}
	type ReviewService struct {
		reviewService
	}
	h := &reviewServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&ReviewService{h}, opts...))
}

type reviewServiceHandler struct {
	ReviewServiceHandler
}

func (h *reviewServiceHandler) CreateReview(ctx context.Context, in *ReqCreateReview, out *RspCreateReview) error {
	return h.ReviewServiceHandler.CreateReview(ctx, in, out)
}

func (h *reviewServiceHandler) PassReview(ctx context.Context, in *ReqPassReview, out *emptypb.Empty) error {
	return h.ReviewServiceHandler.PassReview(ctx, in, out)
}

func (h *reviewServiceHandler) RejectReview(ctx context.Context, in *ReqRejectReview, out *emptypb.Empty) error {
	return h.ReviewServiceHandler.RejectReview(ctx, in, out)
}

func (h *reviewServiceHandler) RevokeReview(ctx context.Context, in *ReqRevokeReview, out *RspRevokeReview) error {
	return h.ReviewServiceHandler.RevokeReview(ctx, in, out)
}

func (h *reviewServiceHandler) AppealReview(ctx context.Context, in *ReqAppealReview, out *RspAppealReview) error {
	return h.ReviewServiceHandler.AppealReview(ctx, in, out)
}

func (h *reviewServiceHandler) GetReviews(ctx context.Context, in *ReqGetReviews, out *RspGetReviews) error {
	return h.ReviewServiceHandler.GetReviews(ctx, in, out)
}

func (h *reviewServiceHandler) HandleCiReviewCallback(ctx context.Context, in *ReqHandleCiReviewCallback, out *emptypb.Empty) error {
	return h.ReviewServiceHandler.HandleCiReviewCallback(ctx, in, out)
}

func (h *reviewServiceHandler) CreateTextReview(ctx context.Context, in *ReqCreateTextReview, out *RspCreateTextReview) error {
	return h.ReviewServiceHandler.CreateTextReview(ctx, in, out)
}
