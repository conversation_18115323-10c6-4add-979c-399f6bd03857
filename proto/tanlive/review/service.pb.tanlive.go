// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package review

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ReviewInfo": "required",
		"ReviewJobs": "omitempty,dive,required",
	}, &ReqCreateReview{})
}

func (x *ReqCreateReview) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DataId":           "required",
		"DataMainId":       "omitempty",
		"DataType":         "required",
		"ReviewType":       "required",
		"CreateBy":         "required",
		"LastRejectReason": "omitempty",
		"Remark":           "omitempty",
	}, &ReqCreateReview_ReviewInfo{})
}

func (x *ReqCreateReview_ReviewInfo) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DataField":  "required",
		"BucketType": "required",
	}, &ReqCreateReview_ReviewJob{})
}

func (x *ReqCreateReview_ReviewJob) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ReviewId": "required",
		"Para":     "required",
	}, &ReqPassReview{})
}

func (x *ReqPassReview) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Remark":   "omitempty",
		"PassedBy": "required",
	}, &ReqPassReview_Para{})
}

func (x *ReqPassReview_Para) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ReviewId": "required",
		"Para":     "required",
	}, &ReqRejectReview{})
}

func (x *ReqRejectReview) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"RejectReason": "required",
		"Remark":       "omitempty",
		"RejectedBy":   "required",
	}, &ReqRejectReview_Para{})
}

func (x *ReqRejectReview_Para) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ReviewId": "required",
		"Para":     "required",
	}, &ReqRevokeReview{})
}

func (x *ReqRevokeReview) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"RevokedBy": "required",
	}, &ReqRevokeReview_Para{})
}

func (x *ReqRevokeReview_Para) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ReviewId": "required",
		"Para":     "required",
	}, &ReqAppealReview{})
}

func (x *ReqAppealReview) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AppealReason": "required",
		"AppealedBy":   "required",
	}, &ReqAppealReview_Para{})
}

func (x *ReqAppealReview_Para) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text":       "required",
		"BucketType": "required",
	}, &ReqCreateTextReview{})
}

func (x *ReqCreateTextReview) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqCreateReview) MaskInLog() any {
	if x == nil {
		return (*ReqCreateReview)(nil)
	}

	y := proto.Clone(x).(*ReqCreateReview)
	if v, ok := any(y.ReviewInfo).(interface{ MaskInLog() any }); ok {
		y.ReviewInfo = v.MaskInLog().(*ReqCreateReview_ReviewInfo)
	}
	for k, v := range y.ReviewJobs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ReviewJobs[k] = vv.MaskInLog().(*ReqCreateReview_ReviewJob)
		}
	}

	return y
}

func (x *ReqCreateReview) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateReview)(nil)
	}

	y := x
	if v, ok := any(y.ReviewInfo).(interface{ MaskInRpc() any }); ok {
		y.ReviewInfo = v.MaskInRpc().(*ReqCreateReview_ReviewInfo)
	}
	for k, v := range y.ReviewJobs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ReviewJobs[k] = vv.MaskInRpc().(*ReqCreateReview_ReviewJob)
		}
	}

	return y
}

func (x *ReqCreateReview) MaskInBff() any {
	if x == nil {
		return (*ReqCreateReview)(nil)
	}

	y := x
	if v, ok := any(y.ReviewInfo).(interface{ MaskInBff() any }); ok {
		y.ReviewInfo = v.MaskInBff().(*ReqCreateReview_ReviewInfo)
	}
	for k, v := range y.ReviewJobs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ReviewJobs[k] = vv.MaskInBff().(*ReqCreateReview_ReviewJob)
		}
	}

	return y
}

func (x *ReqCreateReview_ReviewInfo) MaskInLog() any {
	if x == nil {
		return (*ReqCreateReview_ReviewInfo)(nil)
	}

	y := proto.Clone(x).(*ReqCreateReview_ReviewInfo)
	if v, ok := any(y.LastRejectReason).(interface{ MaskInLog() any }); ok {
		y.LastRejectReason = v.MaskInLog().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInLog() any }); ok {
		y.Remark = v.MaskInLog().(*ReviewRemark)
	}

	return y
}

func (x *ReqCreateReview_ReviewInfo) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateReview_ReviewInfo)(nil)
	}

	y := x
	if v, ok := any(y.LastRejectReason).(interface{ MaskInRpc() any }); ok {
		y.LastRejectReason = v.MaskInRpc().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInRpc() any }); ok {
		y.Remark = v.MaskInRpc().(*ReviewRemark)
	}

	return y
}

func (x *ReqCreateReview_ReviewInfo) MaskInBff() any {
	if x == nil {
		return (*ReqCreateReview_ReviewInfo)(nil)
	}

	y := x
	if v, ok := any(y.LastRejectReason).(interface{ MaskInBff() any }); ok {
		y.LastRejectReason = v.MaskInBff().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInBff() any }); ok {
		y.Remark = v.MaskInBff().(*ReviewRemark)
	}

	return y
}

func (x *ReqPassReview) MaskInLog() any {
	if x == nil {
		return (*ReqPassReview)(nil)
	}

	y := proto.Clone(x).(*ReqPassReview)
	if v, ok := any(y.Para).(interface{ MaskInLog() any }); ok {
		y.Para = v.MaskInLog().(*ReqPassReview_Para)
	}

	return y
}

func (x *ReqPassReview) MaskInRpc() any {
	if x == nil {
		return (*ReqPassReview)(nil)
	}

	y := x
	if v, ok := any(y.Para).(interface{ MaskInRpc() any }); ok {
		y.Para = v.MaskInRpc().(*ReqPassReview_Para)
	}

	return y
}

func (x *ReqPassReview) MaskInBff() any {
	if x == nil {
		return (*ReqPassReview)(nil)
	}

	y := x
	if v, ok := any(y.Para).(interface{ MaskInBff() any }); ok {
		y.Para = v.MaskInBff().(*ReqPassReview_Para)
	}

	return y
}

func (x *ReqPassReview_Para) MaskInLog() any {
	if x == nil {
		return (*ReqPassReview_Para)(nil)
	}

	y := proto.Clone(x).(*ReqPassReview_Para)
	if v, ok := any(y.Remark).(interface{ MaskInLog() any }); ok {
		y.Remark = v.MaskInLog().(*ReviewRemark)
	}

	return y
}

func (x *ReqPassReview_Para) MaskInRpc() any {
	if x == nil {
		return (*ReqPassReview_Para)(nil)
	}

	y := x
	if v, ok := any(y.Remark).(interface{ MaskInRpc() any }); ok {
		y.Remark = v.MaskInRpc().(*ReviewRemark)
	}

	return y
}

func (x *ReqPassReview_Para) MaskInBff() any {
	if x == nil {
		return (*ReqPassReview_Para)(nil)
	}

	y := x
	if v, ok := any(y.Remark).(interface{ MaskInBff() any }); ok {
		y.Remark = v.MaskInBff().(*ReviewRemark)
	}

	return y
}

func (x *ReqRejectReview) MaskInLog() any {
	if x == nil {
		return (*ReqRejectReview)(nil)
	}

	y := proto.Clone(x).(*ReqRejectReview)
	if v, ok := any(y.Para).(interface{ MaskInLog() any }); ok {
		y.Para = v.MaskInLog().(*ReqRejectReview_Para)
	}

	return y
}

func (x *ReqRejectReview) MaskInRpc() any {
	if x == nil {
		return (*ReqRejectReview)(nil)
	}

	y := x
	if v, ok := any(y.Para).(interface{ MaskInRpc() any }); ok {
		y.Para = v.MaskInRpc().(*ReqRejectReview_Para)
	}

	return y
}

func (x *ReqRejectReview) MaskInBff() any {
	if x == nil {
		return (*ReqRejectReview)(nil)
	}

	y := x
	if v, ok := any(y.Para).(interface{ MaskInBff() any }); ok {
		y.Para = v.MaskInBff().(*ReqRejectReview_Para)
	}

	return y
}

func (x *ReqRejectReview_Para) MaskInLog() any {
	if x == nil {
		return (*ReqRejectReview_Para)(nil)
	}

	y := proto.Clone(x).(*ReqRejectReview_Para)
	if v, ok := any(y.RejectReason).(interface{ MaskInLog() any }); ok {
		y.RejectReason = v.MaskInLog().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInLog() any }); ok {
		y.Remark = v.MaskInLog().(*ReviewRemark)
	}

	return y
}

func (x *ReqRejectReview_Para) MaskInRpc() any {
	if x == nil {
		return (*ReqRejectReview_Para)(nil)
	}

	y := x
	if v, ok := any(y.RejectReason).(interface{ MaskInRpc() any }); ok {
		y.RejectReason = v.MaskInRpc().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInRpc() any }); ok {
		y.Remark = v.MaskInRpc().(*ReviewRemark)
	}

	return y
}

func (x *ReqRejectReview_Para) MaskInBff() any {
	if x == nil {
		return (*ReqRejectReview_Para)(nil)
	}

	y := x
	if v, ok := any(y.RejectReason).(interface{ MaskInBff() any }); ok {
		y.RejectReason = v.MaskInBff().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInBff() any }); ok {
		y.Remark = v.MaskInBff().(*ReviewRemark)
	}

	return y
}

func (x *ReqRevokeReview) MaskInLog() any {
	if x == nil {
		return (*ReqRevokeReview)(nil)
	}

	y := proto.Clone(x).(*ReqRevokeReview)
	if v, ok := any(y.Para).(interface{ MaskInLog() any }); ok {
		y.Para = v.MaskInLog().(*ReqRevokeReview_Para)
	}

	return y
}

func (x *ReqRevokeReview) MaskInRpc() any {
	if x == nil {
		return (*ReqRevokeReview)(nil)
	}

	y := x
	if v, ok := any(y.Para).(interface{ MaskInRpc() any }); ok {
		y.Para = v.MaskInRpc().(*ReqRevokeReview_Para)
	}

	return y
}

func (x *ReqRevokeReview) MaskInBff() any {
	if x == nil {
		return (*ReqRevokeReview)(nil)
	}

	y := x
	if v, ok := any(y.Para).(interface{ MaskInBff() any }); ok {
		y.Para = v.MaskInBff().(*ReqRevokeReview_Para)
	}

	return y
}

func (x *ReqAppealReview) MaskInLog() any {
	if x == nil {
		return (*ReqAppealReview)(nil)
	}

	y := proto.Clone(x).(*ReqAppealReview)
	if v, ok := any(y.Para).(interface{ MaskInLog() any }); ok {
		y.Para = v.MaskInLog().(*ReqAppealReview_Para)
	}

	return y
}

func (x *ReqAppealReview) MaskInRpc() any {
	if x == nil {
		return (*ReqAppealReview)(nil)
	}

	y := x
	if v, ok := any(y.Para).(interface{ MaskInRpc() any }); ok {
		y.Para = v.MaskInRpc().(*ReqAppealReview_Para)
	}

	return y
}

func (x *ReqAppealReview) MaskInBff() any {
	if x == nil {
		return (*ReqAppealReview)(nil)
	}

	y := x
	if v, ok := any(y.Para).(interface{ MaskInBff() any }); ok {
		y.Para = v.MaskInBff().(*ReqAppealReview_Para)
	}

	return y
}

func (x *ReqAppealReview_Para) MaskInLog() any {
	if x == nil {
		return (*ReqAppealReview_Para)(nil)
	}

	y := proto.Clone(x).(*ReqAppealReview_Para)
	if v, ok := any(y.AppealReason).(interface{ MaskInLog() any }); ok {
		y.AppealReason = v.MaskInLog().(*RejectReason)
	}

	return y
}

func (x *ReqAppealReview_Para) MaskInRpc() any {
	if x == nil {
		return (*ReqAppealReview_Para)(nil)
	}

	y := x
	if v, ok := any(y.AppealReason).(interface{ MaskInRpc() any }); ok {
		y.AppealReason = v.MaskInRpc().(*RejectReason)
	}

	return y
}

func (x *ReqAppealReview_Para) MaskInBff() any {
	if x == nil {
		return (*ReqAppealReview_Para)(nil)
	}

	y := x
	if v, ok := any(y.AppealReason).(interface{ MaskInBff() any }); ok {
		y.AppealReason = v.MaskInBff().(*RejectReason)
	}

	return y
}

func (x *ReqGetReviews) MaskInLog() any {
	if x == nil {
		return (*ReqGetReviews)(nil)
	}

	y := proto.Clone(x).(*ReqGetReviews)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetReviews_Filter)
	}
	if v, ok := any(y.Relation).(interface{ MaskInLog() any }); ok {
		y.Relation = v.MaskInLog().(*ReqGetReviews_Relation)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetReviews) MaskInRpc() any {
	if x == nil {
		return (*ReqGetReviews)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetReviews_Filter)
	}
	if v, ok := any(y.Relation).(interface{ MaskInRpc() any }); ok {
		y.Relation = v.MaskInRpc().(*ReqGetReviews_Relation)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetReviews) MaskInBff() any {
	if x == nil {
		return (*ReqGetReviews)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetReviews_Filter)
	}
	if v, ok := any(y.Relation).(interface{ MaskInBff() any }); ok {
		y.Relation = v.MaskInBff().(*ReqGetReviews_Relation)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}

	return y
}

func (x *RspGetReviews) MaskInLog() any {
	if x == nil {
		return (*RspGetReviews)(nil)
	}

	y := proto.Clone(x).(*RspGetReviews)
	for k, v := range y.Reviews {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reviews[k] = vv.MaskInLog().(*FullReview)
		}
	}

	return y
}

func (x *RspGetReviews) MaskInRpc() any {
	if x == nil {
		return (*RspGetReviews)(nil)
	}

	y := x
	for k, v := range y.Reviews {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reviews[k] = vv.MaskInRpc().(*FullReview)
		}
	}

	return y
}

func (x *RspGetReviews) MaskInBff() any {
	if x == nil {
		return (*RspGetReviews)(nil)
	}

	y := x
	for k, v := range y.Reviews {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reviews[k] = vv.MaskInBff().(*FullReview)
		}
	}

	return y
}

func (x *ReqHandleCiReviewCallback) MaskInLog() any {
	if x == nil {
		return (*ReqHandleCiReviewCallback)(nil)
	}

	y := proto.Clone(x).(*ReqHandleCiReviewCallback)
	if v, ok := any(y.JobsDetail).(interface{ MaskInLog() any }); ok {
		y.JobsDetail = v.MaskInLog().(*ReqHandleCiReviewCallback_JobDetail)
	}

	return y
}

func (x *ReqHandleCiReviewCallback) MaskInRpc() any {
	if x == nil {
		return (*ReqHandleCiReviewCallback)(nil)
	}

	y := x
	if v, ok := any(y.JobsDetail).(interface{ MaskInRpc() any }); ok {
		y.JobsDetail = v.MaskInRpc().(*ReqHandleCiReviewCallback_JobDetail)
	}

	return y
}

func (x *ReqHandleCiReviewCallback) MaskInBff() any {
	if x == nil {
		return (*ReqHandleCiReviewCallback)(nil)
	}

	y := x
	if v, ok := any(y.JobsDetail).(interface{ MaskInBff() any }); ok {
		y.JobsDetail = v.MaskInBff().(*ReqHandleCiReviewCallback_JobDetail)
	}

	return y
}

func (x *RspCreateTextReview) MaskInLog() any {
	if x == nil {
		return (*RspCreateTextReview)(nil)
	}

	y := proto.Clone(x).(*RspCreateTextReview)
	if v, ok := any(y.RejectReason).(interface{ MaskInLog() any }); ok {
		y.RejectReason = v.MaskInLog().(*RejectReason)
	}

	return y
}

func (x *RspCreateTextReview) MaskInRpc() any {
	if x == nil {
		return (*RspCreateTextReview)(nil)
	}

	y := x
	if v, ok := any(y.RejectReason).(interface{ MaskInRpc() any }); ok {
		y.RejectReason = v.MaskInRpc().(*RejectReason)
	}

	return y
}

func (x *RspCreateTextReview) MaskInBff() any {
	if x == nil {
		return (*RspCreateTextReview)(nil)
	}

	y := x
	if v, ok := any(y.RejectReason).(interface{ MaskInBff() any }); ok {
		y.RejectReason = v.MaskInBff().(*RejectReason)
	}

	return y
}

func (x *ReqCreateReview) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ReviewInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.ReviewJobs {
		if sanitizer, ok := any(x.ReviewJobs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateReview_ReviewInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.LastRejectReason).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Remark).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqPassReview) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Para).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqPassReview_Para) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Remark).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqRejectReview) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Para).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqRejectReview_Para) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.RejectReason).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Remark).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqRevokeReview) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Para).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqAppealReview) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Para).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqAppealReview_Para) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AppealReason).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetReviews) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Relation).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetReviews) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reviews {
		if sanitizer, ok := any(x.Reviews[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqHandleCiReviewCallback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.JobsDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateTextReview) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.RejectReason).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
