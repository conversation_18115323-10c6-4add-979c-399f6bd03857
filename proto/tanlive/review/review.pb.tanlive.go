// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package review

import (
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func (x *ReviewJobResult) MaskInLog() any {
	if x == nil {
		return (*ReviewJobResult)(nil)
	}

	y := proto.Clone(x).(*ReviewJobResult)
	for k, v := range y.Keywords {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Keywords[k] = vv.MaskInLog().(*HitKeyword)
		}
	}

	return y
}

func (x *ReviewJobResult) MaskInRpc() any {
	if x == nil {
		return (*ReviewJobResult)(nil)
	}

	y := x
	for k, v := range y.Keywords {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Keywords[k] = vv.MaskInRpc().(*HitKeyword)
		}
	}

	return y
}

func (x *ReviewJobResult) MaskInBff() any {
	if x == nil {
		return (*ReviewJobResult)(nil)
	}

	y := x
	for k, v := range y.Keywords {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Keywords[k] = vv.MaskInBff().(*HitKeyword)
		}
	}

	return y
}

func (x *RejectReason) MaskInLog() any {
	if x == nil {
		return (*RejectReason)(nil)
	}

	y := proto.Clone(x).(*RejectReason)
	for k, v := range y.AutoJobResults {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.AutoJobResults[k] = vv.MaskInLog().(*ReviewJobResult)
		}
	}

	return y
}

func (x *RejectReason) MaskInRpc() any {
	if x == nil {
		return (*RejectReason)(nil)
	}

	y := x
	for k, v := range y.AutoJobResults {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.AutoJobResults[k] = vv.MaskInRpc().(*ReviewJobResult)
		}
	}

	return y
}

func (x *RejectReason) MaskInBff() any {
	if x == nil {
		return (*RejectReason)(nil)
	}

	y := x
	for k, v := range y.AutoJobResults {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.AutoJobResults[k] = vv.MaskInBff().(*ReviewJobResult)
		}
	}

	return y
}

func (x *ReviewInfo) MaskInLog() any {
	if x == nil {
		return (*ReviewInfo)(nil)
	}

	y := proto.Clone(x).(*ReviewInfo)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ReviewedAt).(interface{ MaskInLog() any }); ok {
		y.ReviewedAt = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.RevokedAt).(interface{ MaskInLog() any }); ok {
		y.RevokedAt = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.RejectReason).(interface{ MaskInLog() any }); ok {
		y.RejectReason = v.MaskInLog().(*RejectReason)
	}
	if v, ok := any(y.AppealReason).(interface{ MaskInLog() any }); ok {
		y.AppealReason = v.MaskInLog().(*RejectReason)
	}
	if v, ok := any(y.LastRejectReason).(interface{ MaskInLog() any }); ok {
		y.LastRejectReason = v.MaskInLog().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInLog() any }); ok {
		y.Remark = v.MaskInLog().(*ReviewRemark)
	}

	return y
}

func (x *ReviewInfo) MaskInRpc() any {
	if x == nil {
		return (*ReviewInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ReviewedAt).(interface{ MaskInRpc() any }); ok {
		y.ReviewedAt = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.RevokedAt).(interface{ MaskInRpc() any }); ok {
		y.RevokedAt = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.RejectReason).(interface{ MaskInRpc() any }); ok {
		y.RejectReason = v.MaskInRpc().(*RejectReason)
	}
	if v, ok := any(y.AppealReason).(interface{ MaskInRpc() any }); ok {
		y.AppealReason = v.MaskInRpc().(*RejectReason)
	}
	if v, ok := any(y.LastRejectReason).(interface{ MaskInRpc() any }); ok {
		y.LastRejectReason = v.MaskInRpc().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInRpc() any }); ok {
		y.Remark = v.MaskInRpc().(*ReviewRemark)
	}

	return y
}

func (x *ReviewInfo) MaskInBff() any {
	if x == nil {
		return (*ReviewInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ReviewedAt).(interface{ MaskInBff() any }); ok {
		y.ReviewedAt = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.RevokedAt).(interface{ MaskInBff() any }); ok {
		y.RevokedAt = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.RejectReason).(interface{ MaskInBff() any }); ok {
		y.RejectReason = v.MaskInBff().(*RejectReason)
	}
	if v, ok := any(y.AppealReason).(interface{ MaskInBff() any }); ok {
		y.AppealReason = v.MaskInBff().(*RejectReason)
	}
	if v, ok := any(y.LastRejectReason).(interface{ MaskInBff() any }); ok {
		y.LastRejectReason = v.MaskInBff().(*RejectReason)
	}
	if v, ok := any(y.Remark).(interface{ MaskInBff() any }); ok {
		y.Remark = v.MaskInBff().(*ReviewRemark)
	}

	return y
}

func (x *ReviewJob) MaskInLog() any {
	if x == nil {
		return (*ReviewJob)(nil)
	}

	y := proto.Clone(x).(*ReviewJob)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.HitKeywords {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.HitKeywords[k] = vv.MaskInLog().(*HitKeyword)
		}
	}

	return y
}

func (x *ReviewJob) MaskInRpc() any {
	if x == nil {
		return (*ReviewJob)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.HitKeywords {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.HitKeywords[k] = vv.MaskInRpc().(*HitKeyword)
		}
	}

	return y
}

func (x *ReviewJob) MaskInBff() any {
	if x == nil {
		return (*ReviewJob)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.HitKeywords {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.HitKeywords[k] = vv.MaskInBff().(*HitKeyword)
		}
	}

	return y
}

func (x *FullReview) MaskInLog() any {
	if x == nil {
		return (*FullReview)(nil)
	}

	y := proto.Clone(x).(*FullReview)
	if v, ok := any(y.ReviewInfo).(interface{ MaskInLog() any }); ok {
		y.ReviewInfo = v.MaskInLog().(*ReviewInfo)
	}
	for k, v := range y.Jobs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Jobs[k] = vv.MaskInLog().(*ReviewJob)
		}
	}

	return y
}

func (x *FullReview) MaskInRpc() any {
	if x == nil {
		return (*FullReview)(nil)
	}

	y := x
	if v, ok := any(y.ReviewInfo).(interface{ MaskInRpc() any }); ok {
		y.ReviewInfo = v.MaskInRpc().(*ReviewInfo)
	}
	for k, v := range y.Jobs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Jobs[k] = vv.MaskInRpc().(*ReviewJob)
		}
	}

	return y
}

func (x *FullReview) MaskInBff() any {
	if x == nil {
		return (*FullReview)(nil)
	}

	y := x
	if v, ok := any(y.ReviewInfo).(interface{ MaskInBff() any }); ok {
		y.ReviewInfo = v.MaskInBff().(*ReviewInfo)
	}
	for k, v := range y.Jobs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Jobs[k] = vv.MaskInBff().(*ReviewJob)
		}
	}

	return y
}

func (x *ReviewJobResult) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Keywords {
		if sanitizer, ok := any(x.Keywords[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RejectReason) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.AutoJobResults {
		if sanitizer, ok := any(x.AutoJobResults[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReviewInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.ReviewedAt).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RevokedAt).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RejectReason).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.AppealReason).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastRejectReason).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Remark).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReviewJob) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.HitKeywords {
		if sanitizer, ok := any(x.HitKeywords[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *FullReview) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ReviewInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Jobs {
		if sanitizer, ok := any(x.Jobs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
