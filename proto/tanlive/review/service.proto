syntax = "proto3";

package tanlive.review;

option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review";

import "google/protobuf/empty.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/options.proto";
import "tanlive/review/review.proto";

message ReqCreateReview {
  // 审核信息
  message ReviewInfo {
    // 数据ID
    uint64 data_id = 1 [(validator) = "required"];
    // 数据main_id
    uint64 data_main_id = 2 [(validator) = "omitempty"];
    // 数据类型
    base.DataType data_type = 3 [(validator) = "required"];
    // 审核类型
    ReviewType review_type = 4 [(validator) = "required"];
    // 创建人
    uint64 create_by = 5 [(validator) = "required"];
    // 上次驳回原因
    RejectReason last_reject_reason = 6 [(validator) = "omitempty"];
    // 备注
    ReviewRemark remark = 7 [(validator) = "omitempty"];
  }
  // 审核任务
  message ReviewJob {
    // 数据字段
    string data_field = 1 [(validator) = "required"];
    // 审核内容
    oneof content {
      // 文本内容
      string text_content = 2;
      // 文件路径
      string file_path = 3;
    }
    // cos桶类型
    CosBucketType bucket_type = 4 [(validator) = "required"];
    // 审核策略
    ReviewStrategy strategy = 5;
  }
  // 审核信息
  ReviewInfo review_info = 1 [(validator) = "required"];
  // 任务列表
  repeated ReviewJob review_jobs = 9 [(validator) = "omitempty,dive,required"];
}

message RspCreateReview {
  // 审核ID
  uint64 review_id = 1;
}

message ReqPassReview {
  // 通过参数
  message Para {
    // 备注
    ReviewRemark remark = 1 [(validator) = "omitempty"];
    // 通过人
    uint64 passed_by = 2 [(validator) = "required"];
  }
  // 通过对象（审核ID）
  uint64 review_id = 1 [(validator) = "required"];
  // 通过参数
  Para para = 2 [(validator) = "required"];
}

message ReqRejectReview {
  // 驳回参数
  message Para {
    // 驳回原因
    RejectReason reject_reason = 1 [(validator) = "required"];
    // 备注
    ReviewRemark remark  = 2 [(validator) = "omitempty"];
    // 审核人
    uint64 rejected_by = 3 [(validator) = "required"];
  }
  // 驳回对象（审核ID）
  uint64 review_id = 1 [(validator) = "required"];
  // 驳回参数
  Para para = 2 [(validator) = "required"];
}

message ReqRevokeReview {
  // 撤销参数
  message Para {
    // 撤销人
    uint64 revoked_by = 1 [(validator) = "required"];
  }
  // 撤销对象（审核ID）
  uint64 review_id = 1 [(validator) = "required"];
  // 撤销参数
  Para para = 2 [(validator) = "required"];
}

message RspRevokeReview {
  // 申诉的审核ID
  uint64 appeal_target = 1;
}

message ReqAppealReview {
  // 申诉参数
  message Para {
    // 申诉原因
    RejectReason appeal_reason = 1 [(validator) = "required"];
    // 申诉文件
    repeated string appeal_files = 2;
    // 申诉人
    uint64 appealed_by = 3 [(validator) = "required"];
  }
  // 申诉对象（审核ID）
  uint64 review_id = 1 [(validator) = "required"];
  // 申诉参数
  Para para = 2 [(validator) = "required"];
}

message RspAppealReview {
  // 申诉会创建一个新的审核记录
  uint64 review_id = 1;
}

message ReqGetReviews {
  // 过滤器
  message Filter {
    // 审核ID
    repeated uint64 review_id = 1;
    // 审核状态
    repeated ReviewState review_state = 2;
    // 数据类型
    base.DataType data_type = 3;
    // 数据ID
    repeated uint64 data_id = 4;
    // 数据main_id
    repeated uint64 data_main_id = 5;
  }
  // 关系
  message Relation {
    // 任务列表
    bool with_jobs = 1;
  }
  // 分页
  base.Paginator page = 1;
  // 过滤器
  Filter filter = 2;
  // 关系
  Relation relation = 3;
  // 排序
  repeated base.OrderBy order_by = 4;
  // 是否返回total_count
  bool with_total_count = 5;
}

message RspGetReviews {
  // 审核列表
  repeated FullReview reviews = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqHandleCiReviewCallback {
  message JobDetail {
    string job_id = 1 [json_name = "JobId"];
    string state = 2 [json_name = "State"];
    string creation_time = 3 [json_name = "CreationTime"];
    string object = 4 [json_name = "Object"];
    string label = 6 [json_name = "Label"];
    int32 result = 7 [json_name = "Result"];
    int32 suggestion = 8 [json_name = "Suggestion"];
  }
  string event_name = 1 [json_name = "EventName"];
  JobDetail jobs_detail = 2 [json_name = "JobsDetail"];
}

message ReqCreateTextReview {
  // 文本
  string text = 1 [(validator) = "required"];
  // 策略
  ReviewStrategy strategy = 2;
  // 桶类型
  CosBucketType bucket_type = 3 [(validator) = "required"];
}

message RspCreateTextReview {
  // 驳回原因
  RejectReason reject_reason = 1;
}

// 审核服务
service ReviewService {
  // 创建审核
  rpc CreateReview(ReqCreateReview) returns (RspCreateReview);
  // 通过审核
  rpc PassReview(ReqPassReview) returns (google.protobuf.Empty);
  // 驳回审核
  rpc RejectReview(ReqRejectReview) returns (google.protobuf.Empty);
  // 撤销审核
  rpc RevokeReview(ReqRevokeReview) returns (RspRevokeReview);
  // 申诉审核
  rpc AppealReview(ReqAppealReview) returns (RspAppealReview);
  // 查询审核列表
  rpc GetReviews(ReqGetReviews) returns (RspGetReviews);
  // 处理数据万象审核回调
  rpc HandleCiReviewCallback(ReqHandleCiReviewCallback) returns (google.protobuf.Empty);
  // 创建文本审核
  rpc CreateTextReview(ReqCreateTextReview) returns (RspCreateTextReview);
}
