// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/review/service.proto

package review

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqCreateReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 审核信息
	ReviewInfo *ReqCreateReview_ReviewInfo `protobuf:"bytes,1,opt,name=review_info,json=reviewInfo,proto3" json:"review_info,omitempty"`
	// 任务列表
	ReviewJobs []*ReqCreateReview_ReviewJob `protobuf:"bytes,9,rep,name=review_jobs,json=reviewJobs,proto3" json:"review_jobs,omitempty"`
}

func (x *ReqCreateReview) Reset() {
	*x = ReqCreateReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateReview) ProtoMessage() {}

func (x *ReqCreateReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateReview.ProtoReflect.Descriptor instead.
func (*ReqCreateReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqCreateReview) GetReviewInfo() *ReqCreateReview_ReviewInfo {
	if x != nil {
		return x.ReviewInfo
	}
	return nil
}

func (x *ReqCreateReview) GetReviewJobs() []*ReqCreateReview_ReviewJob {
	if x != nil {
		return x.ReviewJobs
	}
	return nil
}

type RspCreateReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 审核ID
	ReviewId uint64 `protobuf:"varint,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
}

func (x *RspCreateReview) Reset() {
	*x = RspCreateReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateReview) ProtoMessage() {}

func (x *RspCreateReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateReview.ProtoReflect.Descriptor instead.
func (*RspCreateReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{1}
}

func (x *RspCreateReview) GetReviewId() uint64 {
	if x != nil {
		return x.ReviewId
	}
	return 0
}

type ReqPassReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 通过对象（审核ID）
	ReviewId uint64 `protobuf:"varint,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	// 通过参数
	Para *ReqPassReview_Para `protobuf:"bytes,2,opt,name=para,proto3" json:"para,omitempty"`
}

func (x *ReqPassReview) Reset() {
	*x = ReqPassReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqPassReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqPassReview) ProtoMessage() {}

func (x *ReqPassReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqPassReview.ProtoReflect.Descriptor instead.
func (*ReqPassReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReqPassReview) GetReviewId() uint64 {
	if x != nil {
		return x.ReviewId
	}
	return 0
}

func (x *ReqPassReview) GetPara() *ReqPassReview_Para {
	if x != nil {
		return x.Para
	}
	return nil
}

type ReqRejectReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 驳回对象（审核ID）
	ReviewId uint64 `protobuf:"varint,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	// 驳回参数
	Para *ReqRejectReview_Para `protobuf:"bytes,2,opt,name=para,proto3" json:"para,omitempty"`
}

func (x *ReqRejectReview) Reset() {
	*x = ReqRejectReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqRejectReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRejectReview) ProtoMessage() {}

func (x *ReqRejectReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRejectReview.ProtoReflect.Descriptor instead.
func (*ReqRejectReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{3}
}

func (x *ReqRejectReview) GetReviewId() uint64 {
	if x != nil {
		return x.ReviewId
	}
	return 0
}

func (x *ReqRejectReview) GetPara() *ReqRejectReview_Para {
	if x != nil {
		return x.Para
	}
	return nil
}

type ReqRevokeReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 撤销对象（审核ID）
	ReviewId uint64 `protobuf:"varint,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	// 撤销参数
	Para *ReqRevokeReview_Para `protobuf:"bytes,2,opt,name=para,proto3" json:"para,omitempty"`
}

func (x *ReqRevokeReview) Reset() {
	*x = ReqRevokeReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqRevokeReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRevokeReview) ProtoMessage() {}

func (x *ReqRevokeReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRevokeReview.ProtoReflect.Descriptor instead.
func (*ReqRevokeReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{4}
}

func (x *ReqRevokeReview) GetReviewId() uint64 {
	if x != nil {
		return x.ReviewId
	}
	return 0
}

func (x *ReqRevokeReview) GetPara() *ReqRevokeReview_Para {
	if x != nil {
		return x.Para
	}
	return nil
}

type RspRevokeReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 申诉的审核ID
	AppealTarget uint64 `protobuf:"varint,1,opt,name=appeal_target,json=appealTarget,proto3" json:"appeal_target,omitempty"`
}

func (x *RspRevokeReview) Reset() {
	*x = RspRevokeReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspRevokeReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspRevokeReview) ProtoMessage() {}

func (x *RspRevokeReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspRevokeReview.ProtoReflect.Descriptor instead.
func (*RspRevokeReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{5}
}

func (x *RspRevokeReview) GetAppealTarget() uint64 {
	if x != nil {
		return x.AppealTarget
	}
	return 0
}

type ReqAppealReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 申诉对象（审核ID）
	ReviewId uint64 `protobuf:"varint,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	// 申诉参数
	Para *ReqAppealReview_Para `protobuf:"bytes,2,opt,name=para,proto3" json:"para,omitempty"`
}

func (x *ReqAppealReview) Reset() {
	*x = ReqAppealReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqAppealReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqAppealReview) ProtoMessage() {}

func (x *ReqAppealReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqAppealReview.ProtoReflect.Descriptor instead.
func (*ReqAppealReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{6}
}

func (x *ReqAppealReview) GetReviewId() uint64 {
	if x != nil {
		return x.ReviewId
	}
	return 0
}

func (x *ReqAppealReview) GetPara() *ReqAppealReview_Para {
	if x != nil {
		return x.Para
	}
	return nil
}

type RspAppealReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 申诉会创建一个新的审核记录
	ReviewId uint64 `protobuf:"varint,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
}

func (x *RspAppealReview) Reset() {
	*x = RspAppealReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspAppealReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspAppealReview) ProtoMessage() {}

func (x *RspAppealReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspAppealReview.ProtoReflect.Descriptor instead.
func (*RspAppealReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{7}
}

func (x *RspAppealReview) GetReviewId() uint64 {
	if x != nil {
		return x.ReviewId
	}
	return 0
}

type ReqGetReviews struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页
	Page *base.Paginator `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	// 过滤器
	Filter *ReqGetReviews_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// 关系
	Relation *ReqGetReviews_Relation `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
	// 排序
	OrderBy []*base.OrderBy `protobuf:"bytes,4,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// 是否返回total_count
	WithTotalCount bool `protobuf:"varint,5,opt,name=with_total_count,json=withTotalCount,proto3" json:"with_total_count,omitempty"`
}

func (x *ReqGetReviews) Reset() {
	*x = ReqGetReviews{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetReviews) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetReviews) ProtoMessage() {}

func (x *ReqGetReviews) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetReviews.ProtoReflect.Descriptor instead.
func (*ReqGetReviews) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{8}
}

func (x *ReqGetReviews) GetPage() *base.Paginator {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ReqGetReviews) GetFilter() *ReqGetReviews_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqGetReviews) GetRelation() *ReqGetReviews_Relation {
	if x != nil {
		return x.Relation
	}
	return nil
}

func (x *ReqGetReviews) GetOrderBy() []*base.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ReqGetReviews) GetWithTotalCount() bool {
	if x != nil {
		return x.WithTotalCount
	}
	return false
}

type RspGetReviews struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 审核列表
	Reviews []*FullReview `protobuf:"bytes,1,rep,name=reviews,proto3" json:"reviews,omitempty"`
	// 总数
	TotalCount uint32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspGetReviews) Reset() {
	*x = RspGetReviews{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetReviews) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetReviews) ProtoMessage() {}

func (x *RspGetReviews) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetReviews.ProtoReflect.Descriptor instead.
func (*RspGetReviews) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{9}
}

func (x *RspGetReviews) GetReviews() []*FullReview {
	if x != nil {
		return x.Reviews
	}
	return nil
}

func (x *RspGetReviews) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqHandleCiReviewCallback struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventName  string                               `protobuf:"bytes,1,opt,name=event_name,json=EventName,proto3" json:"event_name,omitempty"`
	JobsDetail *ReqHandleCiReviewCallback_JobDetail `protobuf:"bytes,2,opt,name=jobs_detail,json=JobsDetail,proto3" json:"jobs_detail,omitempty"`
}

func (x *ReqHandleCiReviewCallback) Reset() {
	*x = ReqHandleCiReviewCallback{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqHandleCiReviewCallback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqHandleCiReviewCallback) ProtoMessage() {}

func (x *ReqHandleCiReviewCallback) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqHandleCiReviewCallback.ProtoReflect.Descriptor instead.
func (*ReqHandleCiReviewCallback) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{10}
}

func (x *ReqHandleCiReviewCallback) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *ReqHandleCiReviewCallback) GetJobsDetail() *ReqHandleCiReviewCallback_JobDetail {
	if x != nil {
		return x.JobsDetail
	}
	return nil
}

type ReqCreateTextReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文本
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 策略
	Strategy ReviewStrategy `protobuf:"varint,2,opt,name=strategy,proto3,enum=tanlive.review.ReviewStrategy" json:"strategy,omitempty"`
	// 桶类型
	BucketType CosBucketType `protobuf:"varint,3,opt,name=bucket_type,json=bucketType,proto3,enum=tanlive.review.CosBucketType" json:"bucket_type,omitempty"`
}

func (x *ReqCreateTextReview) Reset() {
	*x = ReqCreateTextReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateTextReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateTextReview) ProtoMessage() {}

func (x *ReqCreateTextReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateTextReview.ProtoReflect.Descriptor instead.
func (*ReqCreateTextReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{11}
}

func (x *ReqCreateTextReview) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ReqCreateTextReview) GetStrategy() ReviewStrategy {
	if x != nil {
		return x.Strategy
	}
	return ReviewStrategy_REVIEW_STRATEGY_UNSPECIFIED
}

func (x *ReqCreateTextReview) GetBucketType() CosBucketType {
	if x != nil {
		return x.BucketType
	}
	return CosBucketType_COS_BUCKET_TYPE_UNSPECIFIED
}

type RspCreateTextReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 驳回原因
	RejectReason *RejectReason `protobuf:"bytes,1,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
}

func (x *RspCreateTextReview) Reset() {
	*x = RspCreateTextReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateTextReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateTextReview) ProtoMessage() {}

func (x *RspCreateTextReview) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateTextReview.ProtoReflect.Descriptor instead.
func (*RspCreateTextReview) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{12}
}

func (x *RspCreateTextReview) GetRejectReason() *RejectReason {
	if x != nil {
		return x.RejectReason
	}
	return nil
}

// 审核信息
type ReqCreateReview_ReviewInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据ID
	DataId uint64 `protobuf:"varint,1,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	// 数据main_id
	DataMainId uint64 `protobuf:"varint,2,opt,name=data_main_id,json=dataMainId,proto3" json:"data_main_id,omitempty"`
	// 数据类型
	DataType base.DataType `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 审核类型
	ReviewType ReviewType `protobuf:"varint,4,opt,name=review_type,json=reviewType,proto3,enum=tanlive.review.ReviewType" json:"review_type,omitempty"`
	// 创建人
	CreateBy uint64 `protobuf:"varint,5,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// 上次驳回原因
	LastRejectReason *RejectReason `protobuf:"bytes,6,opt,name=last_reject_reason,json=lastRejectReason,proto3" json:"last_reject_reason,omitempty"`
	// 备注
	Remark *ReviewRemark `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`
}

func (x *ReqCreateReview_ReviewInfo) Reset() {
	*x = ReqCreateReview_ReviewInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateReview_ReviewInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateReview_ReviewInfo) ProtoMessage() {}

func (x *ReqCreateReview_ReviewInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateReview_ReviewInfo.ProtoReflect.Descriptor instead.
func (*ReqCreateReview_ReviewInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ReqCreateReview_ReviewInfo) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *ReqCreateReview_ReviewInfo) GetDataMainId() uint64 {
	if x != nil {
		return x.DataMainId
	}
	return 0
}

func (x *ReqCreateReview_ReviewInfo) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqCreateReview_ReviewInfo) GetReviewType() ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return ReviewType_REVIEW_TYPE_UNSPECIFIED
}

func (x *ReqCreateReview_ReviewInfo) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *ReqCreateReview_ReviewInfo) GetLastRejectReason() *RejectReason {
	if x != nil {
		return x.LastRejectReason
	}
	return nil
}

func (x *ReqCreateReview_ReviewInfo) GetRemark() *ReviewRemark {
	if x != nil {
		return x.Remark
	}
	return nil
}

// 审核任务
type ReqCreateReview_ReviewJob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据字段
	DataField string `protobuf:"bytes,1,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
	// 审核内容
	//
	// Types that are assignable to Content:
	//
	//	*ReqCreateReview_ReviewJob_TextContent
	//	*ReqCreateReview_ReviewJob_FilePath
	Content isReqCreateReview_ReviewJob_Content `protobuf_oneof:"content"`
	// cos桶类型
	BucketType CosBucketType `protobuf:"varint,4,opt,name=bucket_type,json=bucketType,proto3,enum=tanlive.review.CosBucketType" json:"bucket_type,omitempty"`
	// 审核策略
	Strategy ReviewStrategy `protobuf:"varint,5,opt,name=strategy,proto3,enum=tanlive.review.ReviewStrategy" json:"strategy,omitempty"`
}

func (x *ReqCreateReview_ReviewJob) Reset() {
	*x = ReqCreateReview_ReviewJob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateReview_ReviewJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateReview_ReviewJob) ProtoMessage() {}

func (x *ReqCreateReview_ReviewJob) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateReview_ReviewJob.ProtoReflect.Descriptor instead.
func (*ReqCreateReview_ReviewJob) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ReqCreateReview_ReviewJob) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

func (m *ReqCreateReview_ReviewJob) GetContent() isReqCreateReview_ReviewJob_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (x *ReqCreateReview_ReviewJob) GetTextContent() string {
	if x, ok := x.GetContent().(*ReqCreateReview_ReviewJob_TextContent); ok {
		return x.TextContent
	}
	return ""
}

func (x *ReqCreateReview_ReviewJob) GetFilePath() string {
	if x, ok := x.GetContent().(*ReqCreateReview_ReviewJob_FilePath); ok {
		return x.FilePath
	}
	return ""
}

func (x *ReqCreateReview_ReviewJob) GetBucketType() CosBucketType {
	if x != nil {
		return x.BucketType
	}
	return CosBucketType_COS_BUCKET_TYPE_UNSPECIFIED
}

func (x *ReqCreateReview_ReviewJob) GetStrategy() ReviewStrategy {
	if x != nil {
		return x.Strategy
	}
	return ReviewStrategy_REVIEW_STRATEGY_UNSPECIFIED
}

type isReqCreateReview_ReviewJob_Content interface {
	isReqCreateReview_ReviewJob_Content()
}

type ReqCreateReview_ReviewJob_TextContent struct {
	// 文本内容
	TextContent string `protobuf:"bytes,2,opt,name=text_content,json=textContent,proto3,oneof"`
}

type ReqCreateReview_ReviewJob_FilePath struct {
	// 文件路径
	FilePath string `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3,oneof"`
}

func (*ReqCreateReview_ReviewJob_TextContent) isReqCreateReview_ReviewJob_Content() {}

func (*ReqCreateReview_ReviewJob_FilePath) isReqCreateReview_ReviewJob_Content() {}

// 通过参数
type ReqPassReview_Para struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 备注
	Remark *ReviewRemark `protobuf:"bytes,1,opt,name=remark,proto3" json:"remark,omitempty"`
	// 通过人
	PassedBy uint64 `protobuf:"varint,2,opt,name=passed_by,json=passedBy,proto3" json:"passed_by,omitempty"`
}

func (x *ReqPassReview_Para) Reset() {
	*x = ReqPassReview_Para{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqPassReview_Para) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqPassReview_Para) ProtoMessage() {}

func (x *ReqPassReview_Para) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqPassReview_Para.ProtoReflect.Descriptor instead.
func (*ReqPassReview_Para) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ReqPassReview_Para) GetRemark() *ReviewRemark {
	if x != nil {
		return x.Remark
	}
	return nil
}

func (x *ReqPassReview_Para) GetPassedBy() uint64 {
	if x != nil {
		return x.PassedBy
	}
	return 0
}

// 驳回参数
type ReqRejectReview_Para struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 驳回原因
	RejectReason *RejectReason `protobuf:"bytes,1,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
	// 备注
	Remark *ReviewRemark `protobuf:"bytes,2,opt,name=remark,proto3" json:"remark,omitempty"`
	// 审核人
	RejectedBy uint64 `protobuf:"varint,3,opt,name=rejected_by,json=rejectedBy,proto3" json:"rejected_by,omitempty"`
}

func (x *ReqRejectReview_Para) Reset() {
	*x = ReqRejectReview_Para{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqRejectReview_Para) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRejectReview_Para) ProtoMessage() {}

func (x *ReqRejectReview_Para) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRejectReview_Para.ProtoReflect.Descriptor instead.
func (*ReqRejectReview_Para) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ReqRejectReview_Para) GetRejectReason() *RejectReason {
	if x != nil {
		return x.RejectReason
	}
	return nil
}

func (x *ReqRejectReview_Para) GetRemark() *ReviewRemark {
	if x != nil {
		return x.Remark
	}
	return nil
}

func (x *ReqRejectReview_Para) GetRejectedBy() uint64 {
	if x != nil {
		return x.RejectedBy
	}
	return 0
}

// 撤销参数
type ReqRevokeReview_Para struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 撤销人
	RevokedBy uint64 `protobuf:"varint,1,opt,name=revoked_by,json=revokedBy,proto3" json:"revoked_by,omitempty"`
}

func (x *ReqRevokeReview_Para) Reset() {
	*x = ReqRevokeReview_Para{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqRevokeReview_Para) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRevokeReview_Para) ProtoMessage() {}

func (x *ReqRevokeReview_Para) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRevokeReview_Para.ProtoReflect.Descriptor instead.
func (*ReqRevokeReview_Para) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ReqRevokeReview_Para) GetRevokedBy() uint64 {
	if x != nil {
		return x.RevokedBy
	}
	return 0
}

// 申诉参数
type ReqAppealReview_Para struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 申诉原因
	AppealReason *RejectReason `protobuf:"bytes,1,opt,name=appeal_reason,json=appealReason,proto3" json:"appeal_reason,omitempty"`
	// 申诉文件
	AppealFiles []string `protobuf:"bytes,2,rep,name=appeal_files,json=appealFiles,proto3" json:"appeal_files,omitempty"`
	// 申诉人
	AppealedBy uint64 `protobuf:"varint,3,opt,name=appealed_by,json=appealedBy,proto3" json:"appealed_by,omitempty"`
}

func (x *ReqAppealReview_Para) Reset() {
	*x = ReqAppealReview_Para{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqAppealReview_Para) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqAppealReview_Para) ProtoMessage() {}

func (x *ReqAppealReview_Para) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqAppealReview_Para.ProtoReflect.Descriptor instead.
func (*ReqAppealReview_Para) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ReqAppealReview_Para) GetAppealReason() *RejectReason {
	if x != nil {
		return x.AppealReason
	}
	return nil
}

func (x *ReqAppealReview_Para) GetAppealFiles() []string {
	if x != nil {
		return x.AppealFiles
	}
	return nil
}

func (x *ReqAppealReview_Para) GetAppealedBy() uint64 {
	if x != nil {
		return x.AppealedBy
	}
	return 0
}

// 过滤器
type ReqGetReviews_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 审核ID
	ReviewId []uint64 `protobuf:"varint,1,rep,packed,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	// 审核状态
	ReviewState []ReviewState `protobuf:"varint,2,rep,packed,name=review_state,json=reviewState,proto3,enum=tanlive.review.ReviewState" json:"review_state,omitempty"`
	// 数据类型
	DataType base.DataType `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 数据ID
	DataId []uint64 `protobuf:"varint,4,rep,packed,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	// 数据main_id
	DataMainId []uint64 `protobuf:"varint,5,rep,packed,name=data_main_id,json=dataMainId,proto3" json:"data_main_id,omitempty"`
}

func (x *ReqGetReviews_Filter) Reset() {
	*x = ReqGetReviews_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetReviews_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetReviews_Filter) ProtoMessage() {}

func (x *ReqGetReviews_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetReviews_Filter.ProtoReflect.Descriptor instead.
func (*ReqGetReviews_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ReqGetReviews_Filter) GetReviewId() []uint64 {
	if x != nil {
		return x.ReviewId
	}
	return nil
}

func (x *ReqGetReviews_Filter) GetReviewState() []ReviewState {
	if x != nil {
		return x.ReviewState
	}
	return nil
}

func (x *ReqGetReviews_Filter) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqGetReviews_Filter) GetDataId() []uint64 {
	if x != nil {
		return x.DataId
	}
	return nil
}

func (x *ReqGetReviews_Filter) GetDataMainId() []uint64 {
	if x != nil {
		return x.DataMainId
	}
	return nil
}

// 关系
type ReqGetReviews_Relation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务列表
	WithJobs bool `protobuf:"varint,1,opt,name=with_jobs,json=withJobs,proto3" json:"with_jobs,omitempty"`
}

func (x *ReqGetReviews_Relation) Reset() {
	*x = ReqGetReviews_Relation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetReviews_Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetReviews_Relation) ProtoMessage() {}

func (x *ReqGetReviews_Relation) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetReviews_Relation.ProtoReflect.Descriptor instead.
func (*ReqGetReviews_Relation) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{8, 1}
}

func (x *ReqGetReviews_Relation) GetWithJobs() bool {
	if x != nil {
		return x.WithJobs
	}
	return false
}

type ReqHandleCiReviewCallback_JobDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId        string `protobuf:"bytes,1,opt,name=job_id,json=JobId,proto3" json:"job_id,omitempty"`
	State        string `protobuf:"bytes,2,opt,name=state,json=State,proto3" json:"state,omitempty"`
	CreationTime string `protobuf:"bytes,3,opt,name=creation_time,json=CreationTime,proto3" json:"creation_time,omitempty"`
	Object       string `protobuf:"bytes,4,opt,name=object,json=Object,proto3" json:"object,omitempty"`
	Label        string `protobuf:"bytes,6,opt,name=label,json=Label,proto3" json:"label,omitempty"`
	Result       int32  `protobuf:"varint,7,opt,name=result,json=Result,proto3" json:"result,omitempty"`
	Suggestion   int32  `protobuf:"varint,8,opt,name=suggestion,json=Suggestion,proto3" json:"suggestion,omitempty"`
}

func (x *ReqHandleCiReviewCallback_JobDetail) Reset() {
	*x = ReqHandleCiReviewCallback_JobDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_review_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqHandleCiReviewCallback_JobDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqHandleCiReviewCallback_JobDetail) ProtoMessage() {}

func (x *ReqHandleCiReviewCallback_JobDetail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_review_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqHandleCiReviewCallback_JobDetail.ProtoReflect.Descriptor instead.
func (*ReqHandleCiReviewCallback_JobDetail) Descriptor() ([]byte, []int) {
	return file_tanlive_review_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ReqHandleCiReviewCallback_JobDetail) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *ReqHandleCiReviewCallback_JobDetail) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ReqHandleCiReviewCallback_JobDetail) GetCreationTime() string {
	if x != nil {
		return x.CreationTime
	}
	return ""
}

func (x *ReqHandleCiReviewCallback_JobDetail) GetObject() string {
	if x != nil {
		return x.Object
	}
	return ""
}

func (x *ReqHandleCiReviewCallback_JobDetail) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *ReqHandleCiReviewCallback_JobDetail) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *ReqHandleCiReviewCallback_JobDetail) GetSuggestion() int32 {
	if x != nil {
		return x.Suggestion
	}
	return 0
}

var File_tanlive_review_service_proto protoreflect.FileDescriptor

var file_tanlive_review_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xa9, 0x07, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x59, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x67, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x42,
	0x1b, 0x82, 0x88, 0x27, 0x17, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x64,
	0x69, 0x76, 0x65, 0x2c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0a, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x73, 0x1a, 0xbd, 0x03, 0x0a, 0x0a, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x2f,
	0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0x82, 0x88, 0x27, 0x09, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12,
	0x41, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x49, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x59, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x42, 0x0d, 0x82, 0x88, 0x27, 0x09, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x42, 0x0d, 0x82, 0x88, 0x27, 0x09, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x1a, 0x91, 0x02, 0x0a, 0x09, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x12, 0x2b, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x23, 0x0a, 0x0c, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x74, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x4c, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f,
	0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x42, 0x09, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x2e, 0x0a, 0x0f,
	0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x22, 0xf8, 0x01, 0x0a,
	0x0d, 0x52, 0x65, 0x71, 0x50, 0x61, 0x73, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x29,
	0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x04, 0x70, 0x61, 0x72,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x50, 0x61, 0x73, 0x73,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x70, 0x61, 0x72, 0x61, 0x1a,
	0x76, 0x0a, 0x04, 0x50, 0x61, 0x72, 0x61, 0x12, 0x43, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x42, 0x0d, 0x82, 0x88, 0x27, 0x09, 0x6f, 0x6d, 0x69, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x29, 0x0a, 0x09,
	0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x65, 0x64, 0x42, 0x79, 0x22, 0xd2, 0x02, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x52,
	0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x29, 0x0a, 0x09, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x04, 0x70, 0x61, 0x72, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x70, 0x61, 0x72, 0x61, 0x1a, 0xcb,
	0x01, 0x0a, 0x04, 0x50, 0x61, 0x72, 0x61, 0x12, 0x4f, 0x0a, 0x0d, 0x72, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0c, 0x72, 0x65, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x42, 0x0d, 0x82, 0x88, 0x27, 0x09, 0x6f, 0x6d, 0x69, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x2d, 0x0a,
	0x0b, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x0a, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0xb9, 0x01, 0x0a,
	0x0f, 0x52, 0x65, 0x71, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x29, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x04, 0x70,
	0x61, 0x72, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x52, 0x65,
	0x76, 0x6f, 0x6b, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x70,
	0x61, 0x72, 0x61, 0x1a, 0x33, 0x0a, 0x04, 0x50, 0x61, 0x72, 0x61, 0x12, 0x2b, 0x0a, 0x0a, 0x72,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x72,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x64, 0x42, 0x79, 0x22, 0x36, 0x0a, 0x0f, 0x52, 0x73, 0x70, 0x52,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x23, 0x0a, 0x0d, 0x61,
	0x70, 0x70, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x22, 0xb0, 0x02, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x41, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x29, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12,
	0x46, 0x0a, 0x04, 0x70, 0x61, 0x72, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52,
	0x65, 0x71, 0x41, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x50,
	0x61, 0x72, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x04, 0x70, 0x61, 0x72, 0x61, 0x1a, 0xa9, 0x01, 0x0a, 0x04, 0x50, 0x61, 0x72, 0x61,
	0x12, 0x4f, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x65,
	0x64, 0x42, 0x79, 0x22, 0x2e, 0x0a, 0x0f, 0x52, 0x73, 0x70, 0x41, 0x70, 0x70, 0x65, 0x61, 0x6c,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x49, 0x64, 0x22, 0x9b, 0x04, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x73, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x3c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x42, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x73, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x77, 0x69, 0x74, 0x68, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x1a, 0xd5, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06,
	0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d,
	0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0a, 0x64, 0x61,
	0x74, 0x61, 0x4d, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x1a, 0x27, 0x0a, 0x08, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6a, 0x6f, 0x62,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x77, 0x69, 0x74, 0x68, 0x4a, 0x6f, 0x62,
	0x73, 0x22, 0x66, 0x0a, 0x0d, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x07, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd6, 0x02, 0x0a, 0x19, 0x52, 0x65,
	0x71, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x69, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x54, 0x0a, 0x0b, 0x6a, 0x6f, 0x62, 0x73, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x69, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x0a, 0x4a, 0x6f, 0x62, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a, 0xc3, 0x01, 0x0a,
	0x09, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f,
	0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4a, 0x6f, 0x62, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xc1, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x20, 0x0a, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3a, 0x0a, 0x08,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x08,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x4c, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43,
	0x6f, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x58, 0x0a, 0x13, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x65, 0x78, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x41, 0x0a,
	0x0d, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x52, 0x0c, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x32, 0x9a, 0x05, 0x0a, 0x0d, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x50, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x1a, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x43, 0x0a, 0x0a, 0x50, 0x61, 0x73, 0x73, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x50, 0x61, 0x73, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x47, 0x0a, 0x0c, 0x52, 0x65, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x52, 0x65,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x50, 0x0a, 0x0c, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x1a, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x73, 0x70, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x50, 0x0a, 0x0c, 0x41, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x41, 0x70, 0x70, 0x65, 0x61, 0x6c, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x73, 0x70, 0x41, 0x70, 0x70, 0x65, 0x61, 0x6c,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x4a, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x73, 0x12, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x73, 0x1a, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x73, 0x12, 0x5b, 0x0a, 0x16, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x69, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x29, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65,
	0x71, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x69, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x5c, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x78, 0x74, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x12, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x65, 0x78, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x40, 0x5a,
	0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65,
	0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_review_service_proto_rawDescOnce sync.Once
	file_tanlive_review_service_proto_rawDescData = file_tanlive_review_service_proto_rawDesc
)

func file_tanlive_review_service_proto_rawDescGZIP() []byte {
	file_tanlive_review_service_proto_rawDescOnce.Do(func() {
		file_tanlive_review_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_review_service_proto_rawDescData)
	})
	return file_tanlive_review_service_proto_rawDescData
}

var file_tanlive_review_service_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_tanlive_review_service_proto_goTypes = []interface{}{
	(*ReqCreateReview)(nil),                     // 0: tanlive.review.ReqCreateReview
	(*RspCreateReview)(nil),                     // 1: tanlive.review.RspCreateReview
	(*ReqPassReview)(nil),                       // 2: tanlive.review.ReqPassReview
	(*ReqRejectReview)(nil),                     // 3: tanlive.review.ReqRejectReview
	(*ReqRevokeReview)(nil),                     // 4: tanlive.review.ReqRevokeReview
	(*RspRevokeReview)(nil),                     // 5: tanlive.review.RspRevokeReview
	(*ReqAppealReview)(nil),                     // 6: tanlive.review.ReqAppealReview
	(*RspAppealReview)(nil),                     // 7: tanlive.review.RspAppealReview
	(*ReqGetReviews)(nil),                       // 8: tanlive.review.ReqGetReviews
	(*RspGetReviews)(nil),                       // 9: tanlive.review.RspGetReviews
	(*ReqHandleCiReviewCallback)(nil),           // 10: tanlive.review.ReqHandleCiReviewCallback
	(*ReqCreateTextReview)(nil),                 // 11: tanlive.review.ReqCreateTextReview
	(*RspCreateTextReview)(nil),                 // 12: tanlive.review.RspCreateTextReview
	(*ReqCreateReview_ReviewInfo)(nil),          // 13: tanlive.review.ReqCreateReview.ReviewInfo
	(*ReqCreateReview_ReviewJob)(nil),           // 14: tanlive.review.ReqCreateReview.ReviewJob
	(*ReqPassReview_Para)(nil),                  // 15: tanlive.review.ReqPassReview.Para
	(*ReqRejectReview_Para)(nil),                // 16: tanlive.review.ReqRejectReview.Para
	(*ReqRevokeReview_Para)(nil),                // 17: tanlive.review.ReqRevokeReview.Para
	(*ReqAppealReview_Para)(nil),                // 18: tanlive.review.ReqAppealReview.Para
	(*ReqGetReviews_Filter)(nil),                // 19: tanlive.review.ReqGetReviews.Filter
	(*ReqGetReviews_Relation)(nil),              // 20: tanlive.review.ReqGetReviews.Relation
	(*ReqHandleCiReviewCallback_JobDetail)(nil), // 21: tanlive.review.ReqHandleCiReviewCallback.JobDetail
	(*base.Paginator)(nil),                      // 22: tanlive.base.Paginator
	(*base.OrderBy)(nil),                        // 23: tanlive.base.OrderBy
	(*FullReview)(nil),                          // 24: tanlive.review.FullReview
	(ReviewStrategy)(0),                         // 25: tanlive.review.ReviewStrategy
	(CosBucketType)(0),                          // 26: tanlive.review.CosBucketType
	(*RejectReason)(nil),                        // 27: tanlive.review.RejectReason
	(base.DataType)(0),                          // 28: tanlive.base.DataType
	(ReviewType)(0),                             // 29: tanlive.review.ReviewType
	(*ReviewRemark)(nil),                        // 30: tanlive.review.ReviewRemark
	(ReviewState)(0),                            // 31: tanlive.review.ReviewState
	(*emptypb.Empty)(nil),                       // 32: google.protobuf.Empty
}
var file_tanlive_review_service_proto_depIdxs = []int32{
	13, // 0: tanlive.review.ReqCreateReview.review_info:type_name -> tanlive.review.ReqCreateReview.ReviewInfo
	14, // 1: tanlive.review.ReqCreateReview.review_jobs:type_name -> tanlive.review.ReqCreateReview.ReviewJob
	15, // 2: tanlive.review.ReqPassReview.para:type_name -> tanlive.review.ReqPassReview.Para
	16, // 3: tanlive.review.ReqRejectReview.para:type_name -> tanlive.review.ReqRejectReview.Para
	17, // 4: tanlive.review.ReqRevokeReview.para:type_name -> tanlive.review.ReqRevokeReview.Para
	18, // 5: tanlive.review.ReqAppealReview.para:type_name -> tanlive.review.ReqAppealReview.Para
	22, // 6: tanlive.review.ReqGetReviews.page:type_name -> tanlive.base.Paginator
	19, // 7: tanlive.review.ReqGetReviews.filter:type_name -> tanlive.review.ReqGetReviews.Filter
	20, // 8: tanlive.review.ReqGetReviews.relation:type_name -> tanlive.review.ReqGetReviews.Relation
	23, // 9: tanlive.review.ReqGetReviews.order_by:type_name -> tanlive.base.OrderBy
	24, // 10: tanlive.review.RspGetReviews.reviews:type_name -> tanlive.review.FullReview
	21, // 11: tanlive.review.ReqHandleCiReviewCallback.jobs_detail:type_name -> tanlive.review.ReqHandleCiReviewCallback.JobDetail
	25, // 12: tanlive.review.ReqCreateTextReview.strategy:type_name -> tanlive.review.ReviewStrategy
	26, // 13: tanlive.review.ReqCreateTextReview.bucket_type:type_name -> tanlive.review.CosBucketType
	27, // 14: tanlive.review.RspCreateTextReview.reject_reason:type_name -> tanlive.review.RejectReason
	28, // 15: tanlive.review.ReqCreateReview.ReviewInfo.data_type:type_name -> tanlive.base.DataType
	29, // 16: tanlive.review.ReqCreateReview.ReviewInfo.review_type:type_name -> tanlive.review.ReviewType
	27, // 17: tanlive.review.ReqCreateReview.ReviewInfo.last_reject_reason:type_name -> tanlive.review.RejectReason
	30, // 18: tanlive.review.ReqCreateReview.ReviewInfo.remark:type_name -> tanlive.review.ReviewRemark
	26, // 19: tanlive.review.ReqCreateReview.ReviewJob.bucket_type:type_name -> tanlive.review.CosBucketType
	25, // 20: tanlive.review.ReqCreateReview.ReviewJob.strategy:type_name -> tanlive.review.ReviewStrategy
	30, // 21: tanlive.review.ReqPassReview.Para.remark:type_name -> tanlive.review.ReviewRemark
	27, // 22: tanlive.review.ReqRejectReview.Para.reject_reason:type_name -> tanlive.review.RejectReason
	30, // 23: tanlive.review.ReqRejectReview.Para.remark:type_name -> tanlive.review.ReviewRemark
	27, // 24: tanlive.review.ReqAppealReview.Para.appeal_reason:type_name -> tanlive.review.RejectReason
	31, // 25: tanlive.review.ReqGetReviews.Filter.review_state:type_name -> tanlive.review.ReviewState
	28, // 26: tanlive.review.ReqGetReviews.Filter.data_type:type_name -> tanlive.base.DataType
	0,  // 27: tanlive.review.ReviewService.CreateReview:input_type -> tanlive.review.ReqCreateReview
	2,  // 28: tanlive.review.ReviewService.PassReview:input_type -> tanlive.review.ReqPassReview
	3,  // 29: tanlive.review.ReviewService.RejectReview:input_type -> tanlive.review.ReqRejectReview
	4,  // 30: tanlive.review.ReviewService.RevokeReview:input_type -> tanlive.review.ReqRevokeReview
	6,  // 31: tanlive.review.ReviewService.AppealReview:input_type -> tanlive.review.ReqAppealReview
	8,  // 32: tanlive.review.ReviewService.GetReviews:input_type -> tanlive.review.ReqGetReviews
	10, // 33: tanlive.review.ReviewService.HandleCiReviewCallback:input_type -> tanlive.review.ReqHandleCiReviewCallback
	11, // 34: tanlive.review.ReviewService.CreateTextReview:input_type -> tanlive.review.ReqCreateTextReview
	1,  // 35: tanlive.review.ReviewService.CreateReview:output_type -> tanlive.review.RspCreateReview
	32, // 36: tanlive.review.ReviewService.PassReview:output_type -> google.protobuf.Empty
	32, // 37: tanlive.review.ReviewService.RejectReview:output_type -> google.protobuf.Empty
	5,  // 38: tanlive.review.ReviewService.RevokeReview:output_type -> tanlive.review.RspRevokeReview
	7,  // 39: tanlive.review.ReviewService.AppealReview:output_type -> tanlive.review.RspAppealReview
	9,  // 40: tanlive.review.ReviewService.GetReviews:output_type -> tanlive.review.RspGetReviews
	32, // 41: tanlive.review.ReviewService.HandleCiReviewCallback:output_type -> google.protobuf.Empty
	12, // 42: tanlive.review.ReviewService.CreateTextReview:output_type -> tanlive.review.RspCreateTextReview
	35, // [35:43] is the sub-list for method output_type
	27, // [27:35] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_tanlive_review_service_proto_init() }
func file_tanlive_review_service_proto_init() {
	if File_tanlive_review_service_proto != nil {
		return
	}
	file_tanlive_review_review_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_review_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqPassReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqRejectReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqRevokeReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspRevokeReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqAppealReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspAppealReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetReviews); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetReviews); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqHandleCiReviewCallback); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateTextReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateTextReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateReview_ReviewInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateReview_ReviewJob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqPassReview_Para); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqRejectReview_Para); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqRevokeReview_Para); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqAppealReview_Para); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetReviews_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetReviews_Relation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_review_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqHandleCiReviewCallback_JobDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tanlive_review_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*ReqCreateReview_ReviewJob_TextContent)(nil),
		(*ReqCreateReview_ReviewJob_FilePath)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_review_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_review_service_proto_goTypes,
		DependencyIndexes: file_tanlive_review_service_proto_depIdxs,
		MessageInfos:      file_tanlive_review_service_proto_msgTypes,
	}.Build()
	File_tanlive_review_service_proto = out.File
	file_tanlive_review_service_proto_rawDesc = nil
	file_tanlive_review_service_proto_goTypes = nil
	file_tanlive_review_service_proto_depIdxs = nil
}
