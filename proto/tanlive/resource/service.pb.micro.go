// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/resource/service.proto

package resource

import (
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for ResourceService service

func NewResourceServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for ResourceService service

type ResourceService interface {
}

type resourceService struct {
	c    client.Client
	name string
}

func NewResourceService(name string, c client.Client) ResourceService {
	return &resourceService{
		c:    c,
		name: name,
	}
}

// Server API for ResourceService service

type ResourceServiceHandler interface {
}

func RegisterResourceServiceHandler(s server.Server, hdlr ResourceServiceHandler, opts ...server.HandlerOption) error {
	type resourceService interface {
	}
	type ResourceService struct {
		resourceService
	}
	h := &resourceServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&ResourceService{h}, opts...))
}

type resourceServiceHandler struct {
	ResourceServiceHandler
}
