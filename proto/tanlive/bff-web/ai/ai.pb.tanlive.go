// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package ai

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	ai "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	iam "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Type":     "required",
		"Url":      "required_if=type 1,omitempty,url",
		"Text":     "required_if=type 2",
		"FilePath": "required_if=type 3",
		"FileName": "required_if=type 3",
	}, &FeedbackReference{})
}

func (x *FeedbackReference) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *Chat) MaskInLog() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := proto.Clone(x).(*Chat)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *Chat) MaskInRpc() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *Chat) MaskInBff() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatInfo) MaskInLog() any {
	if x == nil {
		return (*ChatInfo)(nil)
	}

	y := proto.Clone(x).(*ChatInfo)
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*iam.UserInfo)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ChatInfo) MaskInRpc() any {
	if x == nil {
		return (*ChatInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*iam.UserInfo)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ChatInfo) MaskInBff() any {
	if x == nil {
		return (*ChatInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*iam.UserInfo)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ChatDetail) MaskInLog() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := proto.Clone(x).(*ChatDetail)
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Messages[k] = vv.MaskInLog().(*ai.EventChatMessage)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*iam.UserInfo)
	}
	if v, ok := any(y.FinishDate).(interface{ MaskInLog() any }); ok {
		y.FinishDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Records[k] = vv.MaskInLog().(*ai.ChatSendRecordInfo)
		}
	}

	return y
}

func (x *ChatDetail) MaskInRpc() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Messages[k] = vv.MaskInRpc().(*ai.EventChatMessage)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*iam.UserInfo)
	}
	if v, ok := any(y.FinishDate).(interface{ MaskInRpc() any }); ok {
		y.FinishDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Records[k] = vv.MaskInRpc().(*ai.ChatSendRecordInfo)
		}
	}

	return y
}

func (x *ChatDetail) MaskInBff() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Messages[k] = vv.MaskInBff().(*ai.EventChatMessage)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*iam.UserInfo)
	}
	if v, ok := any(y.FinishDate).(interface{ MaskInBff() any }); ok {
		y.FinishDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Records[k] = vv.MaskInBff().(*ai.ChatSendRecordInfo)
		}
	}

	return y
}

func (x *CollectionQA) MaskInLog() any {
	if x == nil {
		return (*CollectionQA)(nil)
	}

	y := proto.Clone(x).(*CollectionQA)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.Assistant)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*DocOperator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*DocOperator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedStates[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.SharedTeams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedTeams[k] = vv.MaskInLog().(*DocShareTeamReceiver)
		}
	}
	for k, v := range y.SharedUsers {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedUsers[k] = vv.MaskInLog().(*DocShareUserReceiver)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *CollectionQA) MaskInRpc() any {
	if x == nil {
		return (*CollectionQA)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.Assistant)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*DocOperator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*DocOperator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedStates[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.SharedTeams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedTeams[k] = vv.MaskInRpc().(*DocShareTeamReceiver)
		}
	}
	for k, v := range y.SharedUsers {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedUsers[k] = vv.MaskInRpc().(*DocShareUserReceiver)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *CollectionQA) MaskInBff() any {
	if x == nil {
		return (*CollectionQA)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.Assistant)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*DocOperator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*DocOperator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedStates[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.SharedTeams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedTeams[k] = vv.MaskInBff().(*DocShareTeamReceiver)
		}
	}
	for k, v := range y.SharedUsers {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedUsers[k] = vv.MaskInBff().(*DocShareUserReceiver)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *CollectionTextFile) MaskInLog() any {
	if x == nil {
		return (*CollectionTextFile)(nil)
	}

	y := proto.Clone(x).(*CollectionTextFile)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*DocOperator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*DocOperator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedStates[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.SharedTeams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedTeams[k] = vv.MaskInLog().(*DocShareTeamReceiver)
		}
	}
	for k, v := range y.SharedUsers {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedUsers[k] = vv.MaskInLog().(*DocShareUserReceiver)
		}
	}

	return y
}

func (x *CollectionTextFile) MaskInRpc() any {
	if x == nil {
		return (*CollectionTextFile)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*DocOperator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*DocOperator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedStates[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.SharedTeams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedTeams[k] = vv.MaskInRpc().(*DocShareTeamReceiver)
		}
	}
	for k, v := range y.SharedUsers {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedUsers[k] = vv.MaskInRpc().(*DocShareUserReceiver)
		}
	}

	return y
}

func (x *CollectionTextFile) MaskInBff() any {
	if x == nil {
		return (*CollectionTextFile)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*DocOperator)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*DocOperator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.SharedStates {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedStates[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.SharedTeams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedTeams[k] = vv.MaskInBff().(*DocShareTeamReceiver)
		}
	}
	for k, v := range y.SharedUsers {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedUsers[k] = vv.MaskInBff().(*DocShareUserReceiver)
		}
	}

	return y
}

func (x *ChatShare) MaskInLog() any {
	if x == nil {
		return (*ChatShare)(nil)
	}

	y := proto.Clone(x).(*ChatShare)
	if v, ok := any(y.ShareDate).(interface{ MaskInLog() any }); ok {
		y.ShareDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInLog() any }); ok {
		y.ExpireDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInLog() any }); ok {
		y.LastAccessTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShare) MaskInRpc() any {
	if x == nil {
		return (*ChatShare)(nil)
	}

	y := x
	if v, ok := any(y.ShareDate).(interface{ MaskInRpc() any }); ok {
		y.ShareDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInRpc() any }); ok {
		y.ExpireDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInRpc() any }); ok {
		y.LastAccessTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShare) MaskInBff() any {
	if x == nil {
		return (*ChatShare)(nil)
	}

	y := x
	if v, ok := any(y.ShareDate).(interface{ MaskInBff() any }); ok {
		y.ShareDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInBff() any }); ok {
		y.ExpireDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInBff() any }); ok {
		y.LastAccessTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShareAccess) MaskInLog() any {
	if x == nil {
		return (*ChatShareAccess)(nil)
	}

	y := proto.Clone(x).(*ChatShareAccess)
	if v, ok := any(y.AccessDate).(interface{ MaskInLog() any }); ok {
		y.AccessDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShareAccess) MaskInRpc() any {
	if x == nil {
		return (*ChatShareAccess)(nil)
	}

	y := x
	if v, ok := any(y.AccessDate).(interface{ MaskInRpc() any }); ok {
		y.AccessDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShareAccess) MaskInBff() any {
	if x == nil {
		return (*ChatShareAccess)(nil)
	}

	y := x
	if v, ok := any(y.AccessDate).(interface{ MaskInBff() any }); ok {
		y.AccessDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *Chat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Messages {
		if sanitizer, ok := any(x.Messages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.FinishDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Records {
		if sanitizer, ok := any(x.Records[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *CollectionQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.SharedStates {
		if sanitizer, ok := any(x.SharedStates[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SharedTeams {
		if sanitizer, ok := any(x.SharedTeams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SharedUsers {
		if sanitizer, ok := any(x.SharedUsers[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *CollectionTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SharedStates {
		if sanitizer, ok := any(x.SharedStates[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SharedTeams {
		if sanitizer, ok := any(x.SharedTeams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SharedUsers {
		if sanitizer, ok := any(x.SharedUsers[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatShare) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ShareDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.ExpireDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastAccessTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatShareAccess) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AccessDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
