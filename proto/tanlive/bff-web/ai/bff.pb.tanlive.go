// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package ai

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	ai "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iam "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	team "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId":   "required",
		"RatingScale": "required",
	}, &ReqRateAiAnswer{})
}

func (x *ReqRateAiAnswer) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
		"Question":    "required",
		"Answer":      "required",
		"References":  "omitempty,dive",
	}, &ReqCreateFeedback{})
}

func (x *ReqCreateFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AnswerId":   "required",
		"Answer":     "required",
		"References": "omitempty,dive",
	}, &ReqSaveUserFeedbackByQuestion{})
}

func (x *ReqSaveUserFeedbackByQuestion) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AnswerId":     "required_without=FeedbackId",
		"AnswerRating": "required",
		"DocId":        "omitempty,dive,required",
		"FeedbackId":   "required_without=AnswerId",
	}, &ReqSaveOpFeedback{})
}

func (x *ReqSaveOpFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ObjectType": "oneof=1 2 3 4 5",
	}, &ReqModifyCustomLabels{})
}

func (x *ReqModifyCustomLabels) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ObjectType": "oneof=1 2 3 4 5",
	}, &ReqListCustomLabel{})
}

func (x *ReqListCustomLabel) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Language": "omitempty,oneof=zh en",
	}, &ReqListAssistant{})
}

func (x *ReqListAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantIds": "omitempty,dive,required",
		"Region":       "required",
	}, &ReqSearchChatUsers{})
}

func (x *ReqSearchChatUsers) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqListChat_Filter{})
}

func (x *ReqListChat_Filter) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "min=1",
	}, &ReqGetChatDetail{})
}

func (x *ReqGetChatDetail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqGetChatMessageDetail{})
}

func (x *ReqGetChatMessageDetail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Limit": "max=200",
	}, &ReqListQA{})
}

func (x *ReqListQA) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Language": "omitempty,oneof=zh en",
	}, &ReqListeDocShareConfigSender{})
}

func (x *ReqListeDocShareConfigSender) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Language": "omitempty,oneof=zh en",
	}, &ReqListAssistantCanShareDoc{})
}

func (x *ReqListAssistantCanShareDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
	}, &ReqCreateDocShareConfigReceiverAssistant{})
}

func (x *ReqCreateDocShareConfigReceiverAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
	}, &ReqGetMiniProgramAssistantLimit{})
}

func (x *ReqGetMiniProgramAssistantLimit) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Code": "required",
	}, &ReqBindUserPhoneByCode{})
}

func (x *ReqBindUserPhoneByCode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Code": "required",
	}, &ReqBindMiniProgramPhoneAccount{})
}

func (x *ReqBindMiniProgramPhoneAccount) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Token": "required",
	}, &ReqBindMiniProgramNormalAccount{})
}

func (x *ReqBindMiniProgramNormalAccount) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqUpdateObjectCustomLabels{})
}

func (x *ReqUpdateObjectCustomLabels) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"LiveAgentId": "required",
		"ChatId":      "required",
	}, &ReqSwitchChatLiveAgent{})
}

func (x *ReqSwitchChatLiveAgent) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Limit": "max=200",
	}, &ReqListTextFiles{})
}

func (x *ReqListTextFiles) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,max=200,dive",
	}, &ReqCreateQAs{})
}

func (x *ReqCreateQAs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Contributor":      "omitempty,dive",
		"State":            "omitempty,oneof=1 2",
		"AssistantId":      "omitempty",
		"ShareAssistantId": "omitempty",
		"Labels":           "omitempty,dive",
		"MatchPatterns":    "required,dive,required",
	}, &ReqCreateQA{})
}

func (x *ReqCreateQA) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FileName":         "required",
		"AssistantId":      "omitempty",
		"Contributor":      "omitempty,dive",
		"Type":             "omitempty,oneof=2 3",
		"ShareAssistantId": "omitempty",
	}, &ReqCreateTextFile{})
}

func (x *ReqCreateTextFile) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,max=1000,dive",
	}, &ReqCreateTextFiles{})
}

func (x *ReqCreateTextFiles) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Contributor": "omitempty,dive",
		"Id":          "required",
	}, &ReqUpdateQA{})
}

func (x *ReqUpdateQA) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,max=200,dive",
	}, &ReqUpdateQAs{})
}

func (x *ReqUpdateQAs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id":       "required",
		"FileName": "omitempty,max=4096",
	}, &ReqUpdateTextFile{})
}

func (x *ReqUpdateTextFile) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,max=200,dive",
	}, &ReqUpdateTextFiles{})
}

func (x *ReqUpdateTextFiles) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Search":      "required",
		"AssistantId": "required",
	}, &ReqSearchCollection{})
}

func (x *ReqSearchCollection) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"HashUserId": "required",
	}, &ReqDescribeTencentToken{})
}

func (x *ReqDescribeTencentToken) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"HashUserId":    "required",
		"TitleNameSort": "omitempty,oneof=asc desc",
	}, &ReqDescribeDocList{})
}

func (x *ReqDescribeDocList) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"HashUserId": "required",
	}, &ReqDelTencentDocAuth{})
}

func (x *ReqDelTencentDocAuth) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FileIds":    "required",
		"HashUserId": "required",
		"Path":       "omitempty,dirpath",
	}, &ReqImportTencentDoc{})
}

func (x *ReqImportTencentDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocIds": "required",
	}, &ReqReimportTencentDoc{})
}

func (x *ReqReimportTencentDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AfterTime":  "required",
		"HashUserId": "required",
	}, &ReqImportTencentDocWebClip{})
}

func (x *ReqImportTencentDocWebClip) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Name": "required",
		"Type": "required",
	}, &ReqModifyDocTab{})
}

func (x *ReqModifyDocTab) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqCloneDoc{})
}

func (x *ReqCloneDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"State": "required,oneof=1 2",
	}, &ReqOnOffDocs{})
}

func (x *ReqOnOffDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqGetFeedbacks{})
}

func (x *ReqGetFeedbacks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FeedbackId": "required",
	}, &ReqFindFeedback{})
}

func (x *ReqFindFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FeedbackIds": "required",
		"AssistantId": "required",
	}, &ReqAcceptFeedback{})
}

func (x *ReqAcceptFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqGetFeedbackLogs{})
}

func (x *ReqGetFeedbackLogs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqDescribeChatRegionCode{})
}

func (x *ReqDescribeChatRegionCode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqDescribeFeedbackRegionCode{})
}

func (x *ReqDescribeFeedbackRegionCode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text":        "required",
		"QuestionId":  "required",
		"AssistantId": "required",
		"Threshold":   "min=0,max=1",
		"Temperature": "min=0,max=2",
	}, &ReqSearchChat{})
}

func (x *ReqSearchChat) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text":        "required",
		"AssistantId": "required",
	}, &ReqSearchChatStream{})
}

func (x *ReqSearchChatStream) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Message": "required",
		"UserId":  "required",
	}, &RspSearchChat{})
}

func (x *RspSearchChat) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Urls": "required",
	}, &ReqProxyChatHtmlUrl{})
}

func (x *ReqProxyChatHtmlUrl) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FileName":         "required",
		"AssistantId":      "omitempty",
		"Contributor":      "omitempty,dive",
		"ShareAssistantId": "omitempty",
	}, &ReqImportTextFile{})
}

func (x *ReqImportTextFile) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Contributor":      "omitempty,dive",
		"AssistantId":      "omitempty",
		"ShareAssistantId": "omitempty",
	}, &ReqImportQA{})
}

func (x *ReqImportQA) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id":     "required",
		"Target": "required",
	}, &ReqConvertCustomLabel{})
}

func (x *ReqConvertCustomLabel) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqGetCustomLabelValueTopN{})
}

func (x *ReqGetCustomLabelValueTopN) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
		"Config":      "required",
	}, &ReqUpdateMyAssistant{})
}

func (x *ReqUpdateMyAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required",
	}, &ReqGetAssistantChunkConfig{})
}

func (x *ReqGetAssistantChunkConfig) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required",
	}, &ReqGetDocChunks{})
}

func (x *ReqGetDocChunks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId":    "required",
		"AutoPara": "required",
	}, &ReqAutoChunkDoc{})
}

func (x *ReqAutoChunkDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId":      "required",
		"ManualPara": "required",
	}, &ReqManualChunkDoc{})
}

func (x *ReqManualChunkDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required,dive,required",
	}, &ReqGetChunkDocTasks{})
}

func (x *ReqGetChunkDocTasks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required",
	}, &ReqGetDocEmbeddingModels{})
}

func (x *ReqGetDocEmbeddingModels) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Mask": "required",
	}, &ReqBatchUpdateDocAttr{})
}

func (x *ReqBatchUpdateDocAttr) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Lang": "required",
	}, &ReqCreateNebulaTask{})
}

func (x *ReqCreateNebulaTask) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Uuid":  "required",
		"Query": "required",
	}, &ReqDescribeNebulaProjection{})
}

func (x *ReqDescribeNebulaProjection) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"QuestionId": "required",
	}, &ReqChatSubscribe{})
}

func (x *ReqChatSubscribe) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text":        "required",
		"AssistantId": "required",
	}, &ReqCreateChatQuestion{})
}

func (x *ReqCreateChatQuestion) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId": "required",
	}, &ReqDescribeMessageFileState{})
}

func (x *ReqDescribeMessageFileState) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Name":  "required",
		"Limit": "min=1,max=200",
	}, &ReqListTeamCanShareDoc{})
}

func (x *ReqListTeamCanShareDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Name": "required",
	}, &ReqListUserCanShareDoc{})
}

func (x *ReqListUserCanShareDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ChatId":     "required",
		"MessageIds": "required,dive,required",
	}, &ReqCreateChatShare{})
}

func (x *ReqCreateChatShare) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ShareId":     "required",
		"ChatType":    "required",
		"AssistantId": "required",
	}, &ReqContinueChatFromShare{})
}

func (x *ReqContinueChatFromShare) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ShareId": "required",
		"Status":  "required",
	}, &ReqUpdateChatShareStatus{})
}

func (x *ReqUpdateChatShareStatus) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ShareId": "required",
	}, &ReqGetPublicChatShare{})
}

func (x *ReqGetPublicChatShare) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspReceiveChatMessage) MaskInLog() any {
	if x == nil {
		return (*RspReceiveChatMessage)(nil)
	}

	y := proto.Clone(x).(*RspReceiveChatMessage)
	if v, ok := any(y.Answer).(interface{ MaskInLog() any }); ok {
		y.Answer = v.MaskInLog().(*ai.ChatMessage)
	}

	return y
}

func (x *RspReceiveChatMessage) MaskInRpc() any {
	if x == nil {
		return (*RspReceiveChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Answer).(interface{ MaskInRpc() any }); ok {
		y.Answer = v.MaskInRpc().(*ai.ChatMessage)
	}

	return y
}

func (x *RspReceiveChatMessage) MaskInBff() any {
	if x == nil {
		return (*RspReceiveChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Answer).(interface{ MaskInBff() any }); ok {
		y.Answer = v.MaskInBff().(*ai.ChatMessage)
	}

	return y
}

func (x *RspCreateChat) MaskInLog() any {
	if x == nil {
		return (*RspCreateChat)(nil)
	}

	y := proto.Clone(x).(*RspCreateChat)
	if v, ok := any(y.Answer).(interface{ MaskInLog() any }); ok {
		y.Answer = v.MaskInLog().(*ai.ChatMessage)
	}

	return y
}

func (x *RspCreateChat) MaskInRpc() any {
	if x == nil {
		return (*RspCreateChat)(nil)
	}

	y := x
	if v, ok := any(y.Answer).(interface{ MaskInRpc() any }); ok {
		y.Answer = v.MaskInRpc().(*ai.ChatMessage)
	}

	return y
}

func (x *RspCreateChat) MaskInBff() any {
	if x == nil {
		return (*RspCreateChat)(nil)
	}

	y := x
	if v, ok := any(y.Answer).(interface{ MaskInBff() any }); ok {
		y.Answer = v.MaskInBff().(*ai.ChatMessage)
	}

	return y
}

func (x *RspDescribeChatMessages) MaskInLog() any {
	if x == nil {
		return (*RspDescribeChatMessages)(nil)
	}

	y := proto.Clone(x).(*RspDescribeChatMessages)
	for k, v := range y.ChatMessages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ChatMessages[k] = vv.MaskInLog().(*ai.EventChatMessage)
		}
	}

	return y
}

func (x *RspDescribeChatMessages) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeChatMessages)(nil)
	}

	y := x
	for k, v := range y.ChatMessages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ChatMessages[k] = vv.MaskInRpc().(*ai.EventChatMessage)
		}
	}

	return y
}

func (x *RspDescribeChatMessages) MaskInBff() any {
	if x == nil {
		return (*RspDescribeChatMessages)(nil)
	}

	y := x
	for k, v := range y.ChatMessages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ChatMessages[k] = vv.MaskInBff().(*ai.EventChatMessage)
		}
	}

	return y
}

func (x *RspDescribeChats) MaskInLog() any {
	if x == nil {
		return (*RspDescribeChats)(nil)
	}

	y := proto.Clone(x).(*RspDescribeChats)
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chats[k] = vv.MaskInLog().(*Chat)
		}
	}

	return y
}

func (x *RspDescribeChats) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeChats)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chats[k] = vv.MaskInRpc().(*Chat)
		}
	}

	return y
}

func (x *RspDescribeChats) MaskInBff() any {
	if x == nil {
		return (*RspDescribeChats)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chats[k] = vv.MaskInBff().(*Chat)
		}
	}

	return y
}

func (x *ReqCreateFeedback) MaskInLog() any {
	if x == nil {
		return (*ReqCreateFeedback)(nil)
	}

	y := proto.Clone(x).(*ReqCreateFeedback)
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.References[k] = vv.MaskInLog().(*FeedbackReference)
		}
	}

	return y
}

func (x *ReqCreateFeedback) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateFeedback)(nil)
	}

	y := x
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.References[k] = vv.MaskInRpc().(*FeedbackReference)
		}
	}

	return y
}

func (x *ReqCreateFeedback) MaskInBff() any {
	if x == nil {
		return (*ReqCreateFeedback)(nil)
	}

	y := x
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.References[k] = vv.MaskInBff().(*FeedbackReference)
		}
	}

	return y
}

func (x *ReqSaveUserFeedbackByQuestion) MaskInLog() any {
	if x == nil {
		return (*ReqSaveUserFeedbackByQuestion)(nil)
	}

	y := proto.Clone(x).(*ReqSaveUserFeedbackByQuestion)
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.References[k] = vv.MaskInLog().(*FeedbackReference)
		}
	}

	return y
}

func (x *ReqSaveUserFeedbackByQuestion) MaskInRpc() any {
	if x == nil {
		return (*ReqSaveUserFeedbackByQuestion)(nil)
	}

	y := x
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.References[k] = vv.MaskInRpc().(*FeedbackReference)
		}
	}

	return y
}

func (x *ReqSaveUserFeedbackByQuestion) MaskInBff() any {
	if x == nil {
		return (*ReqSaveUserFeedbackByQuestion)(nil)
	}

	y := x
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.References[k] = vv.MaskInBff().(*FeedbackReference)
		}
	}

	return y
}

func (x *ReqSaveOpFeedback) MaskInLog() any {
	if x == nil {
		return (*ReqSaveOpFeedback)(nil)
	}

	y := proto.Clone(x).(*ReqSaveOpFeedback)
	if v, ok := any(y.OpComment).(interface{ MaskInLog() any }); ok {
		y.OpComment = v.MaskInLog().(*ai.FeedbackComment)
	}

	return y
}

func (x *ReqSaveOpFeedback) MaskInRpc() any {
	if x == nil {
		return (*ReqSaveOpFeedback)(nil)
	}

	y := x
	if v, ok := any(y.OpComment).(interface{ MaskInRpc() any }); ok {
		y.OpComment = v.MaskInRpc().(*ai.FeedbackComment)
	}

	return y
}

func (x *ReqSaveOpFeedback) MaskInBff() any {
	if x == nil {
		return (*ReqSaveOpFeedback)(nil)
	}

	y := x
	if v, ok := any(y.OpComment).(interface{ MaskInBff() any }); ok {
		y.OpComment = v.MaskInBff().(*ai.FeedbackComment)
	}

	return y
}

func (x *RspStopQuestionReply) MaskInLog() any {
	if x == nil {
		return (*RspStopQuestionReply)(nil)
	}

	y := proto.Clone(x).(*RspStopQuestionReply)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ai.ChatMessage)
	}

	return y
}

func (x *RspStopQuestionReply) MaskInRpc() any {
	if x == nil {
		return (*RspStopQuestionReply)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ai.ChatMessage)
	}

	return y
}

func (x *RspStopQuestionReply) MaskInBff() any {
	if x == nil {
		return (*RspStopQuestionReply)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ai.ChatMessage)
	}

	return y
}

func (x *ReqModifyCustomLabels) MaskInLog() any {
	if x == nil {
		return (*ReqModifyCustomLabels)(nil)
	}

	y := proto.Clone(x).(*ReqModifyCustomLabels)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqModifyCustomLabels) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqModifyCustomLabels) MaskInBff() any {
	if x == nil {
		return (*ReqModifyCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInLog() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := proto.Clone(x).(*RspListCustomLabel)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInRpc() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInBff() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListAssistant)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.Assistant)
		}
	}

	return y
}

func (x *RspListAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.Assistant)
		}
	}

	return y
}

func (x *RspListAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.Assistant)
		}
	}

	return y
}

func (x *RspSearchChatUsers) MaskInLog() any {
	if x == nil {
		return (*RspSearchChatUsers)(nil)
	}

	y := proto.Clone(x).(*RspSearchChatUsers)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*iam.UserInfo)
		}
	}

	return y
}

func (x *RspSearchChatUsers) MaskInRpc() any {
	if x == nil {
		return (*RspSearchChatUsers)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*iam.UserInfo)
		}
	}

	return y
}

func (x *RspSearchChatUsers) MaskInBff() any {
	if x == nil {
		return (*RspSearchChatUsers)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*iam.UserInfo)
		}
	}

	return y
}

func (x *ReqListChat) MaskInLog() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := proto.Clone(x).(*ReqListChat)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInLog() any }); ok {
		y.UpdateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*ai.OrderByLabel)
	}

	return y
}

func (x *ReqListChat) MaskInRpc() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInRpc() any }); ok {
		y.UpdateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*ai.OrderByLabel)
	}

	return y
}

func (x *ReqListChat) MaskInBff() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInBff() any }); ok {
		y.UpdateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*ai.OrderByLabel)
	}

	return y
}

func (x *RspListChat) MaskInLog() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := proto.Clone(x).(*RspListChat)
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chats[k] = vv.MaskInLog().(*ChatInfo)
		}
	}

	return y
}

func (x *RspListChat) MaskInRpc() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chats[k] = vv.MaskInRpc().(*ChatInfo)
		}
	}

	return y
}

func (x *RspListChat) MaskInBff() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chats[k] = vv.MaskInBff().(*ChatInfo)
		}
	}

	return y
}

func (x *ReqGetChatDetail) MaskInLog() any {
	if x == nil {
		return (*ReqGetChatDetail)(nil)
	}

	y := proto.Clone(x).(*ReqGetChatDetail)
	if v, ok := any(y.SendRange).(interface{ MaskInLog() any }); ok {
		y.SendRange = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetChatDetail) MaskInRpc() any {
	if x == nil {
		return (*ReqGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.SendRange).(interface{ MaskInRpc() any }); ok {
		y.SendRange = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetChatDetail) MaskInBff() any {
	if x == nil {
		return (*ReqGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.SendRange).(interface{ MaskInBff() any }); ok {
		y.SendRange = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *RspGetChatDetail) MaskInLog() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := proto.Clone(x).(*RspGetChatDetail)
	if v, ok := any(y.ChatDetail).(interface{ MaskInLog() any }); ok {
		y.ChatDetail = v.MaskInLog().(*ChatDetail)
	}

	return y
}

func (x *RspGetChatDetail) MaskInRpc() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.ChatDetail).(interface{ MaskInRpc() any }); ok {
		y.ChatDetail = v.MaskInRpc().(*ChatDetail)
	}

	return y
}

func (x *RspGetChatDetail) MaskInBff() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.ChatDetail).(interface{ MaskInBff() any }); ok {
		y.ChatDetail = v.MaskInBff().(*ChatDetail)
	}

	return y
}

func (x *SearchCollectionItem) MaskInLog() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := proto.Clone(x).(*SearchCollectionItem)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*DocOperator)
	}

	return y
}

func (x *SearchCollectionItem) MaskInRpc() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*DocOperator)
	}

	return y
}

func (x *SearchCollectionItem) MaskInBff() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*DocOperator)
	}

	return y
}

func (x *RspGetChatMessageDetail) MaskInLog() any {
	if x == nil {
		return (*RspGetChatMessageDetail)(nil)
	}

	y := proto.Clone(x).(*RspGetChatMessageDetail)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ai.EventChatHashMessage)
	}
	for k, v := range y.CollectionItems {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CollectionItems[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Logs[k] = vv.MaskInLog().(*ai.ChatMessageLog)
		}
	}
	if v, ok := any(y.CollectionTime).(interface{ MaskInLog() any }); ok {
		y.CollectionTime = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *RspGetChatMessageDetail) MaskInRpc() any {
	if x == nil {
		return (*RspGetChatMessageDetail)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ai.EventChatHashMessage)
	}
	for k, v := range y.CollectionItems {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CollectionItems[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Logs[k] = vv.MaskInRpc().(*ai.ChatMessageLog)
		}
	}
	if v, ok := any(y.CollectionTime).(interface{ MaskInRpc() any }); ok {
		y.CollectionTime = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *RspGetChatMessageDetail) MaskInBff() any {
	if x == nil {
		return (*RspGetChatMessageDetail)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ai.EventChatHashMessage)
	}
	for k, v := range y.CollectionItems {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CollectionItems[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Logs[k] = vv.MaskInBff().(*ai.ChatMessageLog)
		}
	}
	if v, ok := any(y.CollectionTime).(interface{ MaskInBff() any }); ok {
		y.CollectionTime = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *ReqListQA) MaskInLog() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := proto.Clone(x).(*ReqListQA)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UpdateBy[k] = vv.MaskInLog().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CreateBy[k] = vv.MaskInLog().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInLog() any }); ok {
		y.TipFilter = v.MaskInLog().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *ReqListQA) MaskInRpc() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UpdateBy[k] = vv.MaskInRpc().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CreateBy[k] = vv.MaskInRpc().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInRpc() any }); ok {
		y.TipFilter = v.MaskInRpc().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *ReqListQA) MaskInBff() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UpdateBy[k] = vv.MaskInBff().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CreateBy[k] = vv.MaskInBff().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInBff() any }); ok {
		y.TipFilter = v.MaskInBff().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *RspListQA) MaskInLog() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := proto.Clone(x).(*RspListQA)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*CollectionQA)
		}
	}

	return y
}

func (x *RspListQA) MaskInRpc() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*CollectionQA)
		}
	}

	return y
}

func (x *RspListQA) MaskInBff() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*CollectionQA)
		}
	}

	return y
}

func (x *RspListeDocShareConfigSender) MaskInLog() any {
	if x == nil {
		return (*RspListeDocShareConfigSender)(nil)
	}

	y := proto.Clone(x).(*RspListeDocShareConfigSender)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*RspListeDocShareConfigSender_SharedAssistant)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}

	return y
}

func (x *RspListeDocShareConfigSender) MaskInRpc() any {
	if x == nil {
		return (*RspListeDocShareConfigSender)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*RspListeDocShareConfigSender_SharedAssistant)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}

	return y
}

func (x *RspListeDocShareConfigSender) MaskInBff() any {
	if x == nil {
		return (*RspListeDocShareConfigSender)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*RspListeDocShareConfigSender_SharedAssistant)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListAssistantCanShareDoc)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqCreateDocShareConfigReceiverAssistant)
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserShares[k] = vv.MaskInLog().(*ReqCreateDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserShares[k] = vv.MaskInRpc().(*ReqCreateDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserShares[k] = vv.MaskInBff().(*ReqCreateDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListDocShareConfigReceiverAssistant)
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserShares[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserShares[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserShares[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant_UserShare) MaskInLog() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant_UserShare)(nil)
	}

	y := proto.Clone(x).(*RspListDocShareConfigReceiverAssistant_UserShare)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant_UserShare) MaskInRpc() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant_UserShare)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant_UserShare) MaskInBff() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant_UserShare)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInLog() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := proto.Clone(x).(*RspListDocShareConfigReceiverUserTeam)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInRpc() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInBff() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}

	return y
}

func (x *RspBatchUserAssistantLimit) MaskInLog() any {
	if x == nil {
		return (*RspBatchUserAssistantLimit)(nil)
	}

	y := proto.Clone(x).(*RspBatchUserAssistantLimit)
	for k, v := range y.UserLimits {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserLimits[k] = vv.MaskInLog().(*RspBatchUserAssistantLimit_UserLimit)
		}
	}

	return y
}

func (x *RspBatchUserAssistantLimit) MaskInRpc() any {
	if x == nil {
		return (*RspBatchUserAssistantLimit)(nil)
	}

	y := x
	for k, v := range y.UserLimits {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserLimits[k] = vv.MaskInRpc().(*RspBatchUserAssistantLimit_UserLimit)
		}
	}

	return y
}

func (x *RspBatchUserAssistantLimit) MaskInBff() any {
	if x == nil {
		return (*RspBatchUserAssistantLimit)(nil)
	}

	y := x
	for k, v := range y.UserLimits {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserLimits[k] = vv.MaskInBff().(*RspBatchUserAssistantLimit_UserLimit)
		}
	}

	return y
}

func (x *ReqUpdateObjectCustomLabels) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateObjectCustomLabels)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateObjectCustomLabels)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateObjectCustomLabels) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateObjectCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateObjectCustomLabels) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateObjectCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInLog() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := proto.Clone(x).(*RspListChatLiveAgent)
	if v, ok := any(y.ChatLiveAgent).(interface{ MaskInLog() any }); ok {
		y.ChatLiveAgent = v.MaskInLog().(*ai.ChatLiveAgentInfo)
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInRpc() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := x
	if v, ok := any(y.ChatLiveAgent).(interface{ MaskInRpc() any }); ok {
		y.ChatLiveAgent = v.MaskInRpc().(*ai.ChatLiveAgentInfo)
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInBff() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := x
	if v, ok := any(y.ChatLiveAgent).(interface{ MaskInBff() any }); ok {
		y.ChatLiveAgent = v.MaskInBff().(*ai.ChatLiveAgentInfo)
	}

	return y
}

func (x *ReqListTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqListTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqListTextFiles)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UpdateBy[k] = vv.MaskInLog().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Search).(interface{ MaskInLog() any }); ok {
		y.Search = v.MaskInLog().(*ReqListTextFiles_Search)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CreateBy[k] = vv.MaskInLog().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInLog() any }); ok {
		y.TipFilter = v.MaskInLog().(*ReqListTextFiles_TipFilter)
	}

	return y
}

func (x *ReqListTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqListTextFiles)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UpdateBy[k] = vv.MaskInRpc().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Search).(interface{ MaskInRpc() any }); ok {
		y.Search = v.MaskInRpc().(*ReqListTextFiles_Search)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CreateBy[k] = vv.MaskInRpc().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInRpc() any }); ok {
		y.TipFilter = v.MaskInRpc().(*ReqListTextFiles_TipFilter)
	}

	return y
}

func (x *ReqListTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqListTextFiles)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UpdateBy[k] = vv.MaskInBff().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Search).(interface{ MaskInBff() any }); ok {
		y.Search = v.MaskInBff().(*ReqListTextFiles_Search)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CreateBy[k] = vv.MaskInBff().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInBff() any }); ok {
		y.TipFilter = v.MaskInBff().(*ReqListTextFiles_TipFilter)
	}

	return y
}

func (x *RspListTextFiles) MaskInLog() any {
	if x == nil {
		return (*RspListTextFiles)(nil)
	}

	y := proto.Clone(x).(*RspListTextFiles)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*CollectionTextFile)
		}
	}

	return y
}

func (x *RspListTextFiles) MaskInRpc() any {
	if x == nil {
		return (*RspListTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*CollectionTextFile)
		}
	}

	return y
}

func (x *RspListTextFiles) MaskInBff() any {
	if x == nil {
		return (*RspListTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*CollectionTextFile)
		}
	}

	return y
}

func (x *ReqCreateQAs) MaskInLog() any {
	if x == nil {
		return (*ReqCreateQAs)(nil)
	}

	y := proto.Clone(x).(*ReqCreateQAs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqCreateQA)
		}
	}

	return y
}

func (x *ReqCreateQAs) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqCreateQA)
		}
	}

	return y
}

func (x *ReqCreateQAs) MaskInBff() any {
	if x == nil {
		return (*ReqCreateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqCreateQA)
		}
	}

	return y
}

func (x *ReqCreateQA) MaskInLog() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := proto.Clone(x).(*ReqCreateQA)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqCreateQA) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqCreateQA) MaskInBff() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqCreateTextFile) MaskInLog() any {
	if x == nil {
		return (*ReqCreateTextFile)(nil)
	}

	y := proto.Clone(x).(*ReqCreateTextFile)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqCreateTextFile) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateTextFile)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqCreateTextFile) MaskInBff() any {
	if x == nil {
		return (*ReqCreateTextFile)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqCreateTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqCreateTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqCreateTextFiles)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqCreateTextFile)
		}
	}

	return y
}

func (x *ReqCreateTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqCreateTextFile)
		}
	}

	return y
}

func (x *ReqCreateTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqCreateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqCreateTextFile)
		}
	}

	return y
}

func (x *ReqUpdateQA) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateQA)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateQA) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateQA) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateQAs) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateQAs)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateQAs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *ReqUpdateQAs) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *ReqUpdateQAs) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateTextFile)
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := x
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := x
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqUpdateTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateTextFiles)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqUpdateTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqUpdateTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInLog() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := proto.Clone(x).(*ReqCreateDocQuery)
	if v, ok := any(y.Doc).(interface{ MaskInLog() any }); ok {
		y.Doc = v.MaskInLog().(*ReqListTextFiles)
	}
	if v, ok := any(y.Qa).(interface{ MaskInLog() any }); ok {
		y.Qa = v.MaskInLog().(*ReqListQA)
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInRpc() any }); ok {
		y.Doc = v.MaskInRpc().(*ReqListTextFiles)
	}
	if v, ok := any(y.Qa).(interface{ MaskInRpc() any }); ok {
		y.Qa = v.MaskInRpc().(*ReqListQA)
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInBff() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInBff() any }); ok {
		y.Doc = v.MaskInBff().(*ReqListTextFiles)
	}
	if v, ok := any(y.Qa).(interface{ MaskInBff() any }); ok {
		y.Qa = v.MaskInBff().(*ReqListQA)
	}

	return y
}

func (x *RspSearchCollection) MaskInLog() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := proto.Clone(x).(*RspSearchCollection)
	if v, ok := any(y.Start).(interface{ MaskInLog() any }); ok {
		y.Start = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInLog() any }); ok {
		y.End = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Item {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Item[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *RspSearchCollection) MaskInRpc() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := x
	if v, ok := any(y.Start).(interface{ MaskInRpc() any }); ok {
		y.Start = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInRpc() any }); ok {
		y.End = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Item {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Item[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *RspSearchCollection) MaskInBff() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := x
	if v, ok := any(y.Start).(interface{ MaskInBff() any }); ok {
		y.Start = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInBff() any }); ok {
		y.End = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Item {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Item[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *ReqValidateQAs) MaskInLog() any {
	if x == nil {
		return (*ReqValidateQAs)(nil)
	}

	y := proto.Clone(x).(*ReqValidateQAs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqValidateQAs_Item)
		}
	}

	return y
}

func (x *ReqValidateQAs) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqValidateQAs_Item)
		}
	}

	return y
}

func (x *ReqValidateQAs) MaskInBff() any {
	if x == nil {
		return (*ReqValidateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqValidateQAs_Item)
		}
	}

	return y
}

func (x *ReqValidateQAs_Item) MaskInLog() any {
	if x == nil {
		return (*ReqValidateQAs_Item)(nil)
	}

	y := proto.Clone(x).(*ReqValidateQAs_Item)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}

	return y
}

func (x *ReqValidateQAs_Item) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateQAs_Item)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}

	return y
}

func (x *ReqValidateQAs_Item) MaskInBff() any {
	if x == nil {
		return (*ReqValidateQAs_Item)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspValidateQAs) MaskInLog() any {
	if x == nil {
		return (*RspValidateQAs)(nil)
	}

	y := proto.Clone(x).(*RspValidateQAs)
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Errors[k] = vv.MaskInLog().(*RspValidateQAs_Err)
		}
	}

	return y
}

func (x *RspValidateQAs) MaskInRpc() any {
	if x == nil {
		return (*RspValidateQAs)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Errors[k] = vv.MaskInRpc().(*RspValidateQAs_Err)
		}
	}

	return y
}

func (x *RspValidateQAs) MaskInBff() any {
	if x == nil {
		return (*RspValidateQAs)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Errors[k] = vv.MaskInBff().(*RspValidateQAs_Err)
		}
	}

	return y
}

func (x *ReqValidateTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqValidateTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqValidateTextFiles)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqValidateTextFiles_Item)
		}
	}

	return y
}

func (x *ReqValidateTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqValidateTextFiles_Item)
		}
	}

	return y
}

func (x *ReqValidateTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqValidateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqValidateTextFiles_Item)
		}
	}

	return y
}

func (x *RspValidateTextFiles) MaskInLog() any {
	if x == nil {
		return (*RspValidateTextFiles)(nil)
	}

	y := proto.Clone(x).(*RspValidateTextFiles)
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Errors[k] = vv.MaskInLog().(*RspValidateTextFiles_Err)
		}
	}

	return y
}

func (x *RspValidateTextFiles) MaskInRpc() any {
	if x == nil {
		return (*RspValidateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Errors[k] = vv.MaskInRpc().(*RspValidateTextFiles_Err)
		}
	}

	return y
}

func (x *RspValidateTextFiles) MaskInBff() any {
	if x == nil {
		return (*RspValidateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Errors[k] = vv.MaskInBff().(*RspValidateTextFiles_Err)
		}
	}

	return y
}

func (x *RspListContributor) MaskInLog() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := proto.Clone(x).(*RspListContributor)
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributors[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListContributor) MaskInRpc() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributors[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListContributor) MaskInBff() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributors[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListOperator) MaskInLog() any {
	if x == nil {
		return (*RspListOperator)(nil)
	}

	y := proto.Clone(x).(*RspListOperator)
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Operators[k] = vv.MaskInLog().(*DocOperator)
		}
	}

	return y
}

func (x *RspListOperator) MaskInRpc() any {
	if x == nil {
		return (*RspListOperator)(nil)
	}

	y := x
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Operators[k] = vv.MaskInRpc().(*DocOperator)
		}
	}

	return y
}

func (x *RspListOperator) MaskInBff() any {
	if x == nil {
		return (*RspListOperator)(nil)
	}

	y := x
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Operators[k] = vv.MaskInBff().(*DocOperator)
		}
	}

	return y
}

func (x *RspListSharedAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListSharedAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListSharedAssistant)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*Assistant)
		}
	}

	return y
}

func (x *RspListSharedAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListSharedAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*Assistant)
		}
	}

	return y
}

func (x *RspListSharedAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListSharedAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*Assistant)
		}
	}

	return y
}

func (x *RspListSharedTeam) MaskInLog() any {
	if x == nil {
		return (*RspListSharedTeam)(nil)
	}

	y := proto.Clone(x).(*RspListSharedTeam)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListSharedTeam_SharedTeam)
		}
	}

	return y
}

func (x *RspListSharedTeam) MaskInRpc() any {
	if x == nil {
		return (*RspListSharedTeam)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListSharedTeam_SharedTeam)
		}
	}

	return y
}

func (x *RspListSharedTeam) MaskInBff() any {
	if x == nil {
		return (*RspListSharedTeam)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListSharedTeam_SharedTeam)
		}
	}

	return y
}

func (x *RspListSharedUser) MaskInLog() any {
	if x == nil {
		return (*RspListSharedUser)(nil)
	}

	y := proto.Clone(x).(*RspListSharedUser)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListSharedUser_SharedUser)
		}
	}

	return y
}

func (x *RspListSharedUser) MaskInRpc() any {
	if x == nil {
		return (*RspListSharedUser)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListSharedUser_SharedUser)
		}
	}

	return y
}

func (x *RspListSharedUser) MaskInBff() any {
	if x == nil {
		return (*RspListSharedUser)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListSharedUser_SharedUser)
		}
	}

	return y
}

func (x *RspListCollectionFileName) MaskInLog() any {
	if x == nil {
		return (*RspListCollectionFileName)(nil)
	}

	y := proto.Clone(x).(*RspListCollectionFileName)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*RspListCollectionFileName_Item)
		}
	}

	return y
}

func (x *RspListCollectionFileName) MaskInRpc() any {
	if x == nil {
		return (*RspListCollectionFileName)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*RspListCollectionFileName_Item)
		}
	}

	return y
}

func (x *RspListCollectionFileName) MaskInBff() any {
	if x == nil {
		return (*RspListCollectionFileName)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*RspListCollectionFileName_Item)
		}
	}

	return y
}

func (x *RspListExternalSourceUser) MaskInLog() any {
	if x == nil {
		return (*RspListExternalSourceUser)(nil)
	}

	y := proto.Clone(x).(*RspListExternalSourceUser)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*ExternalSourceUser)
		}
	}

	return y
}

func (x *RspListExternalSourceUser) MaskInRpc() any {
	if x == nil {
		return (*RspListExternalSourceUser)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*ExternalSourceUser)
		}
	}

	return y
}

func (x *RspListExternalSourceUser) MaskInBff() any {
	if x == nil {
		return (*RspListExternalSourceUser)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*ExternalSourceUser)
		}
	}

	return y
}

func (x *RspDescribeDocList) MaskInLog() any {
	if x == nil {
		return (*RspDescribeDocList)(nil)
	}

	y := proto.Clone(x).(*RspDescribeDocList)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*TencentDoc)
		}
	}

	return y
}

func (x *RspDescribeDocList) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeDocList)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*TencentDoc)
		}
	}

	return y
}

func (x *RspDescribeDocList) MaskInBff() any {
	if x == nil {
		return (*RspDescribeDocList)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*TencentDoc)
		}
	}

	return y
}

func (x *RspReimportTencentDoc) MaskInLog() any {
	if x == nil {
		return (*RspReimportTencentDoc)(nil)
	}

	y := proto.Clone(x).(*RspReimportTencentDoc)
	for k, v := range y.Failed {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Failed[k] = vv.MaskInLog().(*RspReimportTencentDoc_FailInfo)
		}
	}

	return y
}

func (x *RspReimportTencentDoc) MaskInRpc() any {
	if x == nil {
		return (*RspReimportTencentDoc)(nil)
	}

	y := x
	for k, v := range y.Failed {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Failed[k] = vv.MaskInRpc().(*RspReimportTencentDoc_FailInfo)
		}
	}

	return y
}

func (x *RspReimportTencentDoc) MaskInBff() any {
	if x == nil {
		return (*RspReimportTencentDoc)(nil)
	}

	y := x
	for k, v := range y.Failed {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Failed[k] = vv.MaskInBff().(*RspReimportTencentDoc_FailInfo)
		}
	}

	return y
}

func (x *RspReimportTencentDoc_FailInfo) MaskInLog() any {
	if x == nil {
		return (*RspReimportTencentDoc_FailInfo)(nil)
	}

	y := proto.Clone(x).(*RspReimportTencentDoc_FailInfo)
	if v, ok := any(y.User).(interface{ MaskInLog() any }); ok {
		y.User = v.MaskInLog().(*ai.ExternalSourceUser)
	}

	return y
}

func (x *RspReimportTencentDoc_FailInfo) MaskInRpc() any {
	if x == nil {
		return (*RspReimportTencentDoc_FailInfo)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInRpc() any }); ok {
		y.User = v.MaskInRpc().(*ai.ExternalSourceUser)
	}

	return y
}

func (x *RspReimportTencentDoc_FailInfo) MaskInBff() any {
	if x == nil {
		return (*RspReimportTencentDoc_FailInfo)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInBff() any }); ok {
		y.User = v.MaskInBff().(*ai.ExternalSourceUser)
	}

	return y
}

func (x *ReqImportTencentDocWebClip) MaskInLog() any {
	if x == nil {
		return (*ReqImportTencentDocWebClip)(nil)
	}

	y := proto.Clone(x).(*ReqImportTencentDocWebClip)
	if v, ok := any(y.AfterTime).(interface{ MaskInLog() any }); ok {
		y.AfterTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ReqImportTencentDocWebClip) MaskInRpc() any {
	if x == nil {
		return (*ReqImportTencentDocWebClip)(nil)
	}

	y := x
	if v, ok := any(y.AfterTime).(interface{ MaskInRpc() any }); ok {
		y.AfterTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ReqImportTencentDocWebClip) MaskInBff() any {
	if x == nil {
		return (*ReqImportTencentDocWebClip)(nil)
	}

	y := x
	if v, ok := any(y.AfterTime).(interface{ MaskInBff() any }); ok {
		y.AfterTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspImportTencentDocWebClip) MaskInLog() any {
	if x == nil {
		return (*RspImportTencentDocWebClip)(nil)
	}

	y := proto.Clone(x).(*RspImportTencentDocWebClip)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*TencentDoc)
		}
	}

	return y
}

func (x *RspImportTencentDocWebClip) MaskInRpc() any {
	if x == nil {
		return (*RspImportTencentDocWebClip)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*TencentDoc)
		}
	}

	return y
}

func (x *RspImportTencentDocWebClip) MaskInBff() any {
	if x == nil {
		return (*RspImportTencentDocWebClip)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*TencentDoc)
		}
	}

	return y
}

func (x *RspDescribeDocTab) MaskInLog() any {
	if x == nil {
		return (*RspDescribeDocTab)(nil)
	}

	y := proto.Clone(x).(*RspDescribeDocTab)
	for k, v := range y.Tabs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tabs[k] = vv.MaskInLog().(*RspDescribeDocTab_DocTab)
		}
	}

	return y
}

func (x *RspDescribeDocTab) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeDocTab)(nil)
	}

	y := x
	for k, v := range y.Tabs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tabs[k] = vv.MaskInRpc().(*RspDescribeDocTab_DocTab)
		}
	}

	return y
}

func (x *RspDescribeDocTab) MaskInBff() any {
	if x == nil {
		return (*RspDescribeDocTab)(nil)
	}

	y := x
	for k, v := range y.Tabs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tabs[k] = vv.MaskInBff().(*RspDescribeDocTab_DocTab)
		}
	}

	return y
}

func (x *RspOnOffDocs) MaskInLog() any {
	if x == nil {
		return (*RspOnOffDocs)(nil)
	}

	y := proto.Clone(x).(*RspOnOffDocs)
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInLog().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.RepeatCollections[k] = vv.MaskInLog().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.QaNumExceed {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.QaNumExceed[k] = vv.MaskInLog().(*RspOnOffDocs_QaContainsMatchCount)
		}
	}

	return y
}

func (x *RspOnOffDocs) MaskInRpc() any {
	if x == nil {
		return (*RspOnOffDocs)(nil)
	}

	y := x
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInRpc().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.RepeatCollections[k] = vv.MaskInRpc().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.QaNumExceed {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.QaNumExceed[k] = vv.MaskInRpc().(*RspOnOffDocs_QaContainsMatchCount)
		}
	}

	return y
}

func (x *RspOnOffDocs) MaskInBff() any {
	if x == nil {
		return (*RspOnOffDocs)(nil)
	}

	y := x
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInBff().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.RepeatCollections[k] = vv.MaskInBff().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.QaNumExceed {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.QaNumExceed[k] = vv.MaskInBff().(*RspOnOffDocs_QaContainsMatchCount)
		}
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInLog() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := proto.Clone(x).(*ReqGetFeedbacks)
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	if v, ok := any(y.HandledAtRange).(interface{ MaskInLog() any }); ok {
		y.HandledAtRange = v.MaskInLog().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInRpc() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := x
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	if v, ok := any(y.HandledAtRange).(interface{ MaskInRpc() any }); ok {
		y.HandledAtRange = v.MaskInRpc().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInBff() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := x
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	if v, ok := any(y.HandledAtRange).(interface{ MaskInBff() any }); ok {
		y.HandledAtRange = v.MaskInBff().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}

	return y
}

func (x *RspGetFeedbacks) MaskInLog() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := proto.Clone(x).(*RspGetFeedbacks)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*RspGetFeedbacks_Item)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetFeedbacks) MaskInRpc() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*RspGetFeedbacks_Item)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetFeedbacks) MaskInBff() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*RspGetFeedbacks_Item)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetFeedbacks_Item) MaskInLog() any {
	if x == nil {
		return (*RspGetFeedbacks_Item)(nil)
	}

	y := proto.Clone(x).(*RspGetFeedbacks_Item)
	if v, ok := any(y.Feedback).(interface{ MaskInLog() any }); ok {
		y.Feedback = v.MaskInLog().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.References[k] = vv.MaskInLog().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInLog() any }); ok {
		y.OriginalQuestion = v.MaskInLog().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInLog() any }); ok {
		y.OriginalAnswer = v.MaskInLog().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInLog().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInLog().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspGetFeedbacks_Item) MaskInRpc() any {
	if x == nil {
		return (*RspGetFeedbacks_Item)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInRpc() any }); ok {
		y.Feedback = v.MaskInRpc().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.References[k] = vv.MaskInRpc().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInRpc() any }); ok {
		y.OriginalQuestion = v.MaskInRpc().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInRpc() any }); ok {
		y.OriginalAnswer = v.MaskInRpc().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInRpc().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInRpc().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspGetFeedbacks_Item) MaskInBff() any {
	if x == nil {
		return (*RspGetFeedbacks_Item)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInBff() any }); ok {
		y.Feedback = v.MaskInBff().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.References[k] = vv.MaskInBff().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInBff() any }); ok {
		y.OriginalQuestion = v.MaskInBff().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInBff() any }); ok {
		y.OriginalAnswer = v.MaskInBff().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInBff().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInBff().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspFindFeedback) MaskInLog() any {
	if x == nil {
		return (*RspFindFeedback)(nil)
	}

	y := proto.Clone(x).(*RspFindFeedback)
	if v, ok := any(y.Feedback).(interface{ MaskInLog() any }); ok {
		y.Feedback = v.MaskInLog().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.References[k] = vv.MaskInLog().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*iam.UserInfo)
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInLog() any }); ok {
		y.OriginalQuestion = v.MaskInLog().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInLog() any }); ok {
		y.OriginalAnswer = v.MaskInLog().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInLog().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInLog().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspFindFeedback) MaskInRpc() any {
	if x == nil {
		return (*RspFindFeedback)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInRpc() any }); ok {
		y.Feedback = v.MaskInRpc().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.References[k] = vv.MaskInRpc().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*iam.UserInfo)
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInRpc() any }); ok {
		y.OriginalQuestion = v.MaskInRpc().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInRpc() any }); ok {
		y.OriginalAnswer = v.MaskInRpc().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInRpc().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInRpc().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspFindFeedback) MaskInBff() any {
	if x == nil {
		return (*RspFindFeedback)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInBff() any }); ok {
		y.Feedback = v.MaskInBff().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.References[k] = vv.MaskInBff().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*iam.UserInfo)
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInBff() any }); ok {
		y.OriginalQuestion = v.MaskInBff().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInBff() any }); ok {
		y.OriginalAnswer = v.MaskInBff().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInBff().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInBff().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspAcceptFeedback) MaskInLog() any {
	if x == nil {
		return (*RspAcceptFeedback)(nil)
	}

	y := proto.Clone(x).(*RspAcceptFeedback)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspAcceptFeedback_Result)
		}
	}

	return y
}

func (x *RspAcceptFeedback) MaskInRpc() any {
	if x == nil {
		return (*RspAcceptFeedback)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspAcceptFeedback_Result)
		}
	}

	return y
}

func (x *RspAcceptFeedback) MaskInBff() any {
	if x == nil {
		return (*RspAcceptFeedback)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspAcceptFeedback_Result)
		}
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInLog() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := proto.Clone(x).(*ReqGetFeedbackLogs)
	if v, ok := any(y.CreateIdentity).(interface{ MaskInLog() any }); ok {
		y.CreateIdentity = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInRpc() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := x
	if v, ok := any(y.CreateIdentity).(interface{ MaskInRpc() any }); ok {
		y.CreateIdentity = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInBff() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := x
	if v, ok := any(y.CreateIdentity).(interface{ MaskInBff() any }); ok {
		y.CreateIdentity = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInLog() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := proto.Clone(x).(*RspGetFeedbackLogs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ai.FullFeedbackLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInRpc() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ai.FullFeedbackLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInBff() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ai.FullFeedbackLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetTextFile) MaskInLog() any {
	if x == nil {
		return (*RspGetTextFile)(nil)
	}

	y := proto.Clone(x).(*RspGetTextFile)
	if v, ok := any(y.Item).(interface{ MaskInLog() any }); ok {
		y.Item = v.MaskInLog().(*CollectionTextFile)
	}

	return y
}

func (x *RspGetTextFile) MaskInRpc() any {
	if x == nil {
		return (*RspGetTextFile)(nil)
	}

	y := x
	if v, ok := any(y.Item).(interface{ MaskInRpc() any }); ok {
		y.Item = v.MaskInRpc().(*CollectionTextFile)
	}

	return y
}

func (x *RspGetTextFile) MaskInBff() any {
	if x == nil {
		return (*RspGetTextFile)(nil)
	}

	y := x
	if v, ok := any(y.Item).(interface{ MaskInBff() any }); ok {
		y.Item = v.MaskInBff().(*CollectionTextFile)
	}

	return y
}

func (x *RspSearchChat) MaskInLog() any {
	if x == nil {
		return (*RspSearchChat)(nil)
	}

	y := proto.Clone(x).(*RspSearchChat)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ai.EventChatMessage)
	}

	return y
}

func (x *RspSearchChat) MaskInRpc() any {
	if x == nil {
		return (*RspSearchChat)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ai.EventChatMessage)
	}

	return y
}

func (x *RspSearchChat) MaskInBff() any {
	if x == nil {
		return (*RspSearchChat)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ai.EventChatMessage)
	}

	return y
}

func (x *RspProxyChatHtmlUrl) MaskInLog() any {
	if x == nil {
		return (*RspProxyChatHtmlUrl)(nil)
	}

	y := proto.Clone(x).(*RspProxyChatHtmlUrl)
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contents[k] = vv.MaskInLog().(*RspProxyChatHtmlUrl_Content)
		}
	}

	return y
}

func (x *RspProxyChatHtmlUrl) MaskInRpc() any {
	if x == nil {
		return (*RspProxyChatHtmlUrl)(nil)
	}

	y := x
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contents[k] = vv.MaskInRpc().(*RspProxyChatHtmlUrl_Content)
		}
	}

	return y
}

func (x *RspProxyChatHtmlUrl) MaskInBff() any {
	if x == nil {
		return (*RspProxyChatHtmlUrl)(nil)
	}

	y := x
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contents[k] = vv.MaskInBff().(*RspProxyChatHtmlUrl_Content)
		}
	}

	return y
}

func (x *ReqImportTextFile) MaskInLog() any {
	if x == nil {
		return (*ReqImportTextFile)(nil)
	}

	y := proto.Clone(x).(*ReqImportTextFile)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqImportTextFile) MaskInRpc() any {
	if x == nil {
		return (*ReqImportTextFile)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqImportTextFile) MaskInBff() any {
	if x == nil {
		return (*ReqImportTextFile)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqImportTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqImportTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqImportTextFiles)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqImportTextFile)
		}
	}

	return y
}

func (x *ReqImportTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqImportTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqImportTextFile)
		}
	}

	return y
}

func (x *ReqImportTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqImportTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqImportTextFile)
		}
	}

	return y
}

func (x *ReqCreateFileExportTask) MaskInLog() any {
	if x == nil {
		return (*ReqCreateFileExportTask)(nil)
	}

	y := proto.Clone(x).(*ReqCreateFileExportTask)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqListTextFiles)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Fields[k] = vv.MaskInLog().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateFileExportTask) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateFileExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqListTextFiles)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Fields[k] = vv.MaskInRpc().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateFileExportTask) MaskInBff() any {
	if x == nil {
		return (*ReqCreateFileExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqListTextFiles)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Fields[k] = vv.MaskInBff().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateQaExportTask) MaskInLog() any {
	if x == nil {
		return (*ReqCreateQaExportTask)(nil)
	}

	y := proto.Clone(x).(*ReqCreateQaExportTask)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqListQA)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Fields[k] = vv.MaskInLog().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateQaExportTask) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateQaExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqListQA)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Fields[k] = vv.MaskInRpc().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateQaExportTask) MaskInBff() any {
	if x == nil {
		return (*ReqCreateQaExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqListQA)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Fields[k] = vv.MaskInBff().(*ExportField)
		}
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInLog() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := proto.Clone(x).(*RspDescribeExportTasks)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*ai.ExportTask)
		}
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*ai.ExportTask)
		}
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInBff() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*ai.ExportTask)
		}
	}

	return y
}

func (x *ReqCreateChatExportTask) MaskInLog() any {
	if x == nil {
		return (*ReqCreateChatExportTask)(nil)
	}

	y := proto.Clone(x).(*ReqCreateChatExportTask)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqListChat)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Fields[k] = vv.MaskInLog().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateChatExportTask) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateChatExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqListChat)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Fields[k] = vv.MaskInRpc().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateChatExportTask) MaskInBff() any {
	if x == nil {
		return (*ReqCreateChatExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqListChat)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Fields[k] = vv.MaskInBff().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateChatMessageExportTask) MaskInLog() any {
	if x == nil {
		return (*ReqCreateChatMessageExportTask)(nil)
	}

	y := proto.Clone(x).(*ReqCreateChatMessageExportTask)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqListChat)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Fields[k] = vv.MaskInLog().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateChatMessageExportTask) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateChatMessageExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqListChat)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Fields[k] = vv.MaskInRpc().(*ExportField)
		}
	}

	return y
}

func (x *ReqCreateChatMessageExportTask) MaskInBff() any {
	if x == nil {
		return (*ReqCreateChatMessageExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqListChat)
	}
	for k, v := range y.Fields {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Fields[k] = vv.MaskInBff().(*ExportField)
		}
	}

	return y
}

func (x *ReqImportQA) MaskInLog() any {
	if x == nil {
		return (*ReqImportQA)(nil)
	}

	y := proto.Clone(x).(*ReqImportQA)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqImportQA) MaskInRpc() any {
	if x == nil {
		return (*ReqImportQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqImportQA) MaskInBff() any {
	if x == nil {
		return (*ReqImportQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqImportQAs) MaskInLog() any {
	if x == nil {
		return (*ReqImportQAs)(nil)
	}

	y := proto.Clone(x).(*ReqImportQAs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqImportQA)
		}
	}

	return y
}

func (x *ReqImportQAs) MaskInRpc() any {
	if x == nil {
		return (*ReqImportQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqImportQA)
		}
	}

	return y
}

func (x *ReqImportQAs) MaskInBff() any {
	if x == nil {
		return (*ReqImportQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqImportQA)
		}
	}

	return y
}

func (x *ReqConvertCustomLabel) MaskInLog() any {
	if x == nil {
		return (*ReqConvertCustomLabel)(nil)
	}

	y := proto.Clone(x).(*ReqConvertCustomLabel)
	if v, ok := any(y.Target).(interface{ MaskInLog() any }); ok {
		y.Target = v.MaskInLog().(*ai.CustomLabel)
	}

	return y
}

func (x *ReqConvertCustomLabel) MaskInRpc() any {
	if x == nil {
		return (*ReqConvertCustomLabel)(nil)
	}

	y := x
	if v, ok := any(y.Target).(interface{ MaskInRpc() any }); ok {
		y.Target = v.MaskInRpc().(*ai.CustomLabel)
	}

	return y
}

func (x *ReqConvertCustomLabel) MaskInBff() any {
	if x == nil {
		return (*ReqConvertCustomLabel)(nil)
	}

	y := x
	if v, ok := any(y.Target).(interface{ MaskInBff() any }); ok {
		y.Target = v.MaskInBff().(*ai.CustomLabel)
	}

	return y
}

func (x *RspGetCustomLabelValueTopN) MaskInLog() any {
	if x == nil {
		return (*RspGetCustomLabelValueTopN)(nil)
	}

	y := proto.Clone(x).(*RspGetCustomLabelValueTopN)
	for k, v := range y.Values {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Values[k] = vv.MaskInLog().(*ai.LabelValue)
		}
	}

	return y
}

func (x *RspGetCustomLabelValueTopN) MaskInRpc() any {
	if x == nil {
		return (*RspGetCustomLabelValueTopN)(nil)
	}

	y := x
	for k, v := range y.Values {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Values[k] = vv.MaskInRpc().(*ai.LabelValue)
		}
	}

	return y
}

func (x *RspGetCustomLabelValueTopN) MaskInBff() any {
	if x == nil {
		return (*RspGetCustomLabelValueTopN)(nil)
	}

	y := x
	for k, v := range y.Values {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Values[k] = vv.MaskInBff().(*ai.LabelValue)
		}
	}

	return y
}

func (x *RspGetMyAssistants) MaskInLog() any {
	if x == nil {
		return (*RspGetMyAssistants)(nil)
	}

	y := proto.Clone(x).(*RspGetMyAssistants)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.FullAssistant)
		}
	}

	return y
}

func (x *RspGetMyAssistants) MaskInRpc() any {
	if x == nil {
		return (*RspGetMyAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.FullAssistant)
		}
	}

	return y
}

func (x *RspGetMyAssistants) MaskInBff() any {
	if x == nil {
		return (*RspGetMyAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.FullAssistant)
		}
	}

	return y
}

func (x *ReqUpdateMyAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateMyAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateMyAssistant)
	if v, ok := any(y.Config).(interface{ MaskInLog() any }); ok {
		y.Config = v.MaskInLog().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateMyAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateMyAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInRpc() any }); ok {
		y.Config = v.MaskInRpc().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateMyAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateMyAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInBff() any }); ok {
		y.Config = v.MaskInBff().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantOptions)
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.InteractiveCode[k] = vv.MaskInLog().(*ai.InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.VisibleChain[k] = vv.MaskInLog().(*ai.VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInLog().(*ai.EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ChatModelV2[k] = vv.MaskInLog().(*ai.ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInLog().(*ai.SearchEngineOption)
		}
	}
	for k, v := range y.QuickActions {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.QuickActions[k] = vv.MaskInLog().(*ai.QuickAction)
		}
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := x
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.InteractiveCode[k] = vv.MaskInRpc().(*ai.InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.VisibleChain[k] = vv.MaskInRpc().(*ai.VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInRpc().(*ai.EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ChatModelV2[k] = vv.MaskInRpc().(*ai.ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInRpc().(*ai.SearchEngineOption)
		}
	}
	for k, v := range y.QuickActions {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.QuickActions[k] = vv.MaskInRpc().(*ai.QuickAction)
		}
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := x
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.InteractiveCode[k] = vv.MaskInBff().(*ai.InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.VisibleChain[k] = vv.MaskInBff().(*ai.VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInBff().(*ai.EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ChatModelV2[k] = vv.MaskInBff().(*ai.ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInBff().(*ai.SearchEngineOption)
		}
	}
	for k, v := range y.QuickActions {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.QuickActions[k] = vv.MaskInBff().(*ai.QuickAction)
		}
	}

	return y
}

func (x *RspGetAssistantChunkConfig) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantChunkConfig)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantChunkConfig)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.FullAssistant)
		}
	}

	return y
}

func (x *RspGetAssistantChunkConfig) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantChunkConfig)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.FullAssistant)
		}
	}

	return y
}

func (x *RspGetAssistantChunkConfig) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantChunkConfig)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.FullAssistant)
		}
	}

	return y
}

func (x *RspGetDocChunks) MaskInLog() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := proto.Clone(x).(*RspGetDocChunks)
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.AssistantChunks[k] = vv.MaskInLog().(*ai.AssistantChunks)
		}
	}

	return y
}

func (x *RspGetDocChunks) MaskInRpc() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := x
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.AssistantChunks[k] = vv.MaskInRpc().(*ai.AssistantChunks)
		}
	}

	return y
}

func (x *RspGetDocChunks) MaskInBff() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := x
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.AssistantChunks[k] = vv.MaskInBff().(*ai.AssistantChunks)
		}
	}

	return y
}

func (x *ReqAutoChunkDoc) MaskInLog() any {
	if x == nil {
		return (*ReqAutoChunkDoc)(nil)
	}

	y := proto.Clone(x).(*ReqAutoChunkDoc)
	if v, ok := any(y.AutoPara).(interface{ MaskInLog() any }); ok {
		y.AutoPara = v.MaskInLog().(*ai.AutoChunkPara)
	}

	return y
}

func (x *ReqAutoChunkDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqAutoChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.AutoPara).(interface{ MaskInRpc() any }); ok {
		y.AutoPara = v.MaskInRpc().(*ai.AutoChunkPara)
	}

	return y
}

func (x *ReqAutoChunkDoc) MaskInBff() any {
	if x == nil {
		return (*ReqAutoChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.AutoPara).(interface{ MaskInBff() any }); ok {
		y.AutoPara = v.MaskInBff().(*ai.AutoChunkPara)
	}

	return y
}

func (x *RspAutoChunkDoc) MaskInLog() any {
	if x == nil {
		return (*RspAutoChunkDoc)(nil)
	}

	y := proto.Clone(x).(*RspAutoChunkDoc)
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chunks[k] = vv.MaskInLog().(*ai.ChunkItem)
		}
	}

	return y
}

func (x *RspAutoChunkDoc) MaskInRpc() any {
	if x == nil {
		return (*RspAutoChunkDoc)(nil)
	}

	y := x
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chunks[k] = vv.MaskInRpc().(*ai.ChunkItem)
		}
	}

	return y
}

func (x *RspAutoChunkDoc) MaskInBff() any {
	if x == nil {
		return (*RspAutoChunkDoc)(nil)
	}

	y := x
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chunks[k] = vv.MaskInBff().(*ai.ChunkItem)
		}
	}

	return y
}

func (x *ReqManualChunkDoc) MaskInLog() any {
	if x == nil {
		return (*ReqManualChunkDoc)(nil)
	}

	y := proto.Clone(x).(*ReqManualChunkDoc)
	if v, ok := any(y.ManualPara).(interface{ MaskInLog() any }); ok {
		y.ManualPara = v.MaskInLog().(*ai.ManualChunkPara)
	}

	return y
}

func (x *ReqManualChunkDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqManualChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.ManualPara).(interface{ MaskInRpc() any }); ok {
		y.ManualPara = v.MaskInRpc().(*ai.ManualChunkPara)
	}

	return y
}

func (x *ReqManualChunkDoc) MaskInBff() any {
	if x == nil {
		return (*ReqManualChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.ManualPara).(interface{ MaskInBff() any }); ok {
		y.ManualPara = v.MaskInBff().(*ai.ManualChunkPara)
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInLog() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := proto.Clone(x).(*RspGetChunkDocTasks)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*ai.DocChunkTask)
		}
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInRpc() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*ai.DocChunkTask)
		}
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInBff() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*ai.DocChunkTask)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInLog() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := proto.Clone(x).(*RspGetDocEmbeddingModels)
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInLog().(*ai.EmbeddingModelCount)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInRpc() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := x
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInRpc().(*ai.EmbeddingModelCount)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInBff() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := x
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInBff().(*ai.EmbeddingModelCount)
		}
	}

	return y
}

func (x *RspGetAssistantConfig) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantConfig)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantConfig)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetAssistantConfig) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantConfig)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetAssistantConfig) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantConfig)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetAssistantsMiniprogram) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantsMiniprogram)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantsMiniprogram)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetAssistantsMiniprogram) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantsMiniprogram)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}

	return y
}

func (x *RspGetAssistantsMiniprogram) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantsMiniprogram)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}

	return y
}

func (x *ReqBatchUpdateDocAttr) MaskInLog() any {
	if x == nil {
		return (*ReqBatchUpdateDocAttr)(nil)
	}

	y := proto.Clone(x).(*ReqBatchUpdateDocAttr)
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqBatchUpdateDocAttr) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchUpdateDocAttr)(nil)
	}

	y := x
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqBatchUpdateDocAttr) MaskInBff() any {
	if x == nil {
		return (*ReqBatchUpdateDocAttr)(nil)
	}

	y := x
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *RspGetTextFileTip) MaskInLog() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := proto.Clone(x).(*RspGetTextFileTip)
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TableOverSize[k] = vv.MaskInLog().(*ai.TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *RspGetTextFileTip) MaskInRpc() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := x
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TableOverSize[k] = vv.MaskInRpc().(*ai.TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *RspGetTextFileTip) MaskInBff() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := x
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TableOverSize[k] = vv.MaskInBff().(*ai.TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *RspDescribeNebulaTaskList) MaskInLog() any {
	if x == nil {
		return (*RspDescribeNebulaTaskList)(nil)
	}

	y := proto.Clone(x).(*RspDescribeNebulaTaskList)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*RspDescribeNebulaTaskList_Task)
		}
	}

	return y
}

func (x *RspDescribeNebulaTaskList) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeNebulaTaskList)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*RspDescribeNebulaTaskList_Task)
		}
	}

	return y
}

func (x *RspDescribeNebulaTaskList) MaskInBff() any {
	if x == nil {
		return (*RspDescribeNebulaTaskList)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*RspDescribeNebulaTaskList_Task)
		}
	}

	return y
}

func (x *RspDescribeMessageFileState) MaskInLog() any {
	if x == nil {
		return (*RspDescribeMessageFileState)(nil)
	}

	y := proto.Clone(x).(*RspDescribeMessageFileState)
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Files[k] = vv.MaskInLog().(*ai.ChatMessageFile)
		}
	}

	return y
}

func (x *RspDescribeMessageFileState) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeMessageFileState)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Files[k] = vv.MaskInRpc().(*ai.ChatMessageFile)
		}
	}

	return y
}

func (x *RspDescribeMessageFileState) MaskInBff() any {
	if x == nil {
		return (*RspDescribeMessageFileState)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Files[k] = vv.MaskInBff().(*ai.ChatMessageFile)
		}
	}

	return y
}

func (x *RspListNebulaAssistants) MaskInLog() any {
	if x == nil {
		return (*RspListNebulaAssistants)(nil)
	}

	y := proto.Clone(x).(*RspListNebulaAssistants)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.AssistantV2)
		}
	}

	return y
}

func (x *RspListNebulaAssistants) MaskInRpc() any {
	if x == nil {
		return (*RspListNebulaAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.AssistantV2)
		}
	}

	return y
}

func (x *RspListNebulaAssistants) MaskInBff() any {
	if x == nil {
		return (*RspListNebulaAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.AssistantV2)
		}
	}

	return y
}

func (x *RspListNebulaContributors) MaskInLog() any {
	if x == nil {
		return (*RspListNebulaContributors)(nil)
	}

	y := proto.Clone(x).(*RspListNebulaContributors)
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributors[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListNebulaContributors) MaskInRpc() any {
	if x == nil {
		return (*RspListNebulaContributors)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributors[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListNebulaContributors) MaskInBff() any {
	if x == nil {
		return (*RspListNebulaContributors)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributors[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListTeamCanShareDoc)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListUserCanShareDoc)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *RspGetPublicChatShare) MaskInLog() any {
	if x == nil {
		return (*RspGetPublicChatShare)(nil)
	}

	y := proto.Clone(x).(*RspGetPublicChatShare)
	if v, ok := any(y.ShareDate).(interface{ MaskInLog() any }); ok {
		y.ShareDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInLog() any }); ok {
		y.ExpireDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInLog() any }); ok {
		y.LastAccessTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Messages[k] = vv.MaskInLog().(*ai.EventChatMessage)
		}
	}

	return y
}

func (x *RspGetPublicChatShare) MaskInRpc() any {
	if x == nil {
		return (*RspGetPublicChatShare)(nil)
	}

	y := x
	if v, ok := any(y.ShareDate).(interface{ MaskInRpc() any }); ok {
		y.ShareDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInRpc() any }); ok {
		y.ExpireDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInRpc() any }); ok {
		y.LastAccessTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Messages[k] = vv.MaskInRpc().(*ai.EventChatMessage)
		}
	}

	return y
}

func (x *RspGetPublicChatShare) MaskInBff() any {
	if x == nil {
		return (*RspGetPublicChatShare)(nil)
	}

	y := x
	if v, ok := any(y.ShareDate).(interface{ MaskInBff() any }); ok {
		y.ShareDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInBff() any }); ok {
		y.ExpireDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInBff() any }); ok {
		y.LastAccessTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Messages[k] = vv.MaskInBff().(*ai.EventChatMessage)
		}
	}

	return y
}

func (x *RspReceiveChatMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Answer).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateChat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Answer).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeChatMessages) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ChatMessages {
		if sanitizer, ok := any(x.ChatMessages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeChats) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chats {
		if sanitizer, ok := any(x.Chats[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.References {
		if sanitizer, ok := any(x.References[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqSaveUserFeedbackByQuestion) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.References {
		if sanitizer, ok := any(x.References[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqSaveOpFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.OpComment).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspStopQuestionReply) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqModifyCustomLabels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListCustomLabel) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspSearchChatUsers) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListChat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListChat) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chats {
		if sanitizer, ok := any(x.Chats[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetChatDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.SendRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetChatDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ChatDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *SearchCollectionItem) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetChatMessageDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.CollectionItems {
		if sanitizer, ok := any(x.CollectionItems[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Logs {
		if sanitizer, ok := any(x.Logs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CollectionTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UpdateBy {
		if sanitizer, ok := any(x.UpdateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.CreateBy {
		if sanitizer, ok := any(x.CreateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.TipFilter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListeDocShareConfigSender) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListAssistantCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateDocShareConfigReceiverAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserShares {
		if sanitizer, ok := any(x.UserShares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocShareConfigReceiverAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserShares {
		if sanitizer, ok := any(x.UserShares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocShareConfigReceiverAssistant_UserShare) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocShareConfigReceiverUserTeam) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspBatchUserAssistantLimit) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserLimits {
		if sanitizer, ok := any(x.UserLimits[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateObjectCustomLabels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListChatLiveAgent) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ChatLiveAgent).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UpdateBy {
		if sanitizer, ok := any(x.UpdateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Search).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.CreateBy {
		if sanitizer, ok := any(x.CreateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.TipFilter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateDocQuery) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Doc).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Qa).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSearchCollection) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Start).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.End).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Item {
		if sanitizer, ok := any(x.Item[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqValidateQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqValidateQAs_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspValidateQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Errors {
		if sanitizer, ok := any(x.Errors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqValidateTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspValidateTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Errors {
		if sanitizer, ok := any(x.Errors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListContributor) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributors {
		if sanitizer, ok := any(x.Contributors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListOperator) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Operators {
		if sanitizer, ok := any(x.Operators[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListSharedAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListSharedTeam) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListSharedUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListCollectionFileName) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListExternalSourceUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeDocList) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspReimportTencentDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Failed {
		if sanitizer, ok := any(x.Failed[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspReimportTencentDoc_FailInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.User).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqImportTencentDocWebClip) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AfterTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspImportTencentDocWebClip) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeDocTab) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tabs {
		if sanitizer, ok := any(x.Tabs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspOnOffDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.PreRepeatCollections {
		if sanitizer, ok := any(x.PreRepeatCollections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.RepeatCollections {
		if sanitizer, ok := any(x.RepeatCollections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.QaNumExceed {
		if sanitizer, ok := any(x.QaNumExceed[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetFeedbacks) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.HandledAtRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetFeedbacks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetFeedbacks_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Feedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.References {
		if sanitizer, ok := any(x.References[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OriginalQuestion).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OriginalAnswer).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.ExpectedDocs {
		if sanitizer, ok := any(x.ExpectedDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ExpectedMgmtDocs {
		if sanitizer, ok := any(x.ExpectedMgmtDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspFindFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Feedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.References {
		if sanitizer, ok := any(x.References[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OriginalQuestion).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OriginalAnswer).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.ExpectedDocs {
		if sanitizer, ok := any(x.ExpectedDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ExpectedMgmtDocs {
		if sanitizer, ok := any(x.ExpectedMgmtDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspAcceptFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetFeedbackLogs) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateIdentity).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetFeedbackLogs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Item).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSearchChat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspProxyChatHtmlUrl) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contents {
		if sanitizer, ok := any(x.Contents[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqImportTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqImportTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateFileExportTask) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Fields {
		if sanitizer, ok := any(x.Fields[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateQaExportTask) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Fields {
		if sanitizer, ok := any(x.Fields[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeExportTasks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateChatExportTask) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Fields {
		if sanitizer, ok := any(x.Fields[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateChatMessageExportTask) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Fields {
		if sanitizer, ok := any(x.Fields[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqImportQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqImportQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqConvertCustomLabel) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Target).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetCustomLabelValueTopN) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Values {
		if sanitizer, ok := any(x.Values[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetMyAssistants) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateMyAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Config).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetAssistantOptions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.InteractiveCode {
		if sanitizer, ok := any(x.InteractiveCode[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.VisibleChain {
		if sanitizer, ok := any(x.VisibleChain[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.EmbeddingModel {
		if sanitizer, ok := any(x.EmbeddingModel[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ChatModelV2 {
		if sanitizer, ok := any(x.ChatModelV2[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SearchEngineV2 {
		if sanitizer, ok := any(x.SearchEngineV2[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.QuickActions {
		if sanitizer, ok := any(x.QuickActions[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantChunkConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetDocChunks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.AssistantChunks {
		if sanitizer, ok := any(x.AssistantChunks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqAutoChunkDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AutoPara).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspAutoChunkDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chunks {
		if sanitizer, ok := any(x.Chunks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqManualChunkDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ManualPara).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetChunkDocTasks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetDocEmbeddingModels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.EmbeddingModels {
		if sanitizer, ok := any(x.EmbeddingModels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantsMiniprogram) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqBatchUpdateDocAttr) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetTextFileTip) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TableOverSize {
		if sanitizer, ok := any(x.TableOverSize[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeNebulaTaskList) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeMessageFileState) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Files {
		if sanitizer, ok := any(x.Files[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListNebulaAssistants) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListNebulaContributors) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributors {
		if sanitizer, ok := any(x.Contributors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListTeamCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListUserCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetPublicChatShare) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ShareDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.ExpireDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastAccessTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Messages {
		if sanitizer, ok := any(x.Messages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type AiBffHandler interface {
	// 查询message附件状态
	DescribeMessageFileState(context.Context, *ReqDescribeMessageFileState, *RspDescribeMessageFileState) error
	// 创建会话question
	CreateChatQuestion(context.Context, *ReqCreateChatQuestion, *RspCreateChatQuestion) error
	// 重新开始导出任务
	RestartExportTask(context.Context, *ReqRestartExportTask, *emptypb.Empty) error
	// 导出会话消息
	CreateChatMessageExportTask(context.Context, *ReqCreateChatMessageExportTask, *RspExportTask) error
	// 导出会话
	CreateChatExportTask(context.Context, *ReqCreateChatExportTask, *RspExportTask) error
	// 查询导出任务
	DescribeExportTasks(context.Context, *ReqDescribeExportTasks, *RspDescribeExportTasks) error
	// 导出qa
	CreateQaExportTask(context.Context, *ReqCreateQaExportTask, *RspExportTask) error
	// 导出文本文件
	CreateFileExportTask(context.Context, *ReqCreateFileExportTask, *RspExportTask) error
	// 发送消息
	ReceiveChatMessage(context.Context, *ReqReceiveChatMessage, *RspReceiveChatMessage) error
	// 创建会话
	CreateChat(context.Context, *ReqCreateChat, *RspCreateChat) error
	// 获取会话消息列表
	DescribeChatMessages(context.Context, *ReqDescribeChatMessages, *RspDescribeChatMessages) error
	// 获取会话列表
	DescribeChats(context.Context, *ReqDescribeChats, *RspDescribeChats) error
	// 删除会话
	DeleteChat(context.Context, *ReqDeleteChat, *emptypb.Empty) error
	// 重发会话消息
	ResendChatMessage(context.Context, *ReqResendChatMessage, *emptypb.Empty) error
	// 评价AI回答
	RateAiAnswer(context.Context, *ReqRateAiAnswer, *emptypb.Empty) error
	// 创建用户反馈（助手维度）
	CreateFeedback(context.Context, *ReqCreateFeedback, *RspCreateFeedback) error
	// 保存用户反馈（问题维度）
	SaveUserFeedbackByQuestion(context.Context, *ReqSaveUserFeedbackByQuestion, *RspCreateFeedback) error
	// 保存运营反馈
	SaveOpFeedback(context.Context, *ReqSaveOpFeedback, *RspCreateFeedback) error
	// 停止消息发送
	StopQuestionReply(context.Context, *ReqStopQuestionReply, *RspStopQuestionReply) error
	// 获取自定义标签列表
	ListCustomLabel(context.Context, *ReqListCustomLabel, *RspListCustomLabel) error
	// 插入或更新自定义标签
	ModifyCustomLabels(context.Context, *ReqModifyCustomLabels, *RspModifyCustomLabels) error
	// 删除自定义标签
	DeleteCustomLabels(context.Context, *ReqDeleteCustomLabels, *emptypb.Empty) error
	// 更新对象的自定义标签
	UpdateObjectCustomLabels(context.Context, *ReqUpdateObjectCustomLabels, *emptypb.Empty) error
	// 转换标签
	ConvertCustomLabel(context.Context, *ReqConvertCustomLabel, *RspConvertCustomLabel) error
	// 获取标签topn值,默认top10
	GetCustomLabelValueTopN(context.Context, *ReqGetCustomLabelValueTopN, *RspGetCustomLabelValueTopN) error
	// 获取管理的ai助手列表
	ListAssistant(context.Context, *ReqListAssistant, *RspListAssistant) error
	// 搜索AI对话的用户列表
	SearchChatUsers(context.Context, *ReqSearchChatUsers, *RspSearchChatUsers) error
	// AI对话管理列表
	ListChat(context.Context, *ReqListChat, *RspListChat) error
	// AI对话详情
	GetChatDetail(context.Context, *ReqGetChatDetail, *RspGetChatDetail) error
	// 获取消息详情
	GetChatMessageDetail(context.Context, *ReqGetChatMessageDetail, *RspGetChatMessageDetail) error
	// 查询QA列表
	ListQA(context.Context, *ReqListQA, *RspListQA) error
	// 导入文本/文件
	ImportTextFiles(context.Context, *ReqImportTextFiles, *RspImportTextFiles) error
	// 导入文本/文件
	ImportQAs(context.Context, *ReqImportQAs, *RspImportQAs) error
	// 创建助手分享
	CreateAssistantShare(context.Context, *ReqCreateAssistantShare, *RspCreateAssistantShare) error
	// 创建助手发送方设置
	CreateDocShareConfigSender(context.Context, *ReqCreateDocShareConfigSender, *RspCreateDocShareConfigSender) error
	// 查询助手发送方设置
	ListeDocShareConfigSender(context.Context, *ReqListeDocShareConfigSender, *RspListeDocShareConfigSender) error
	// 查询已经设置并开启知识库接收的助手
	ListMyAssistantIds(context.Context, *ReqListMyAssistantIds, *RspListMyAssistantIds) error
	// 查询可分享的助手列表
	ListAssistantCanShareDoc(context.Context, *ReqListAssistantCanShareDoc, *RspListAssistantCanShareDoc) error
	// 创建助手接收方设置
	CreateDocShareConfigReceiverAssistant(context.Context, *ReqCreateDocShareConfigReceiverAssistant, *RspCreateDocShareConfigReceiverAssistant) error
	// 查询助手接收方设置
	ListDocShareConfigReceiverAssistant(context.Context, *ReqListDocShareConfigReceiverAssistant, *RspListDocShareConfigReceiverAssistant) error
	// 创建个人/团队接收方设置
	CreateDocShareConfigReceiverUserTeam(context.Context, *ReqCreateDocShareConfigReceiverUserTeam, *RspCreateDocShareConfigReceiverUserTeam) error
	// 查询个人/团队接收方设置
	ListDocShareConfigReceiverUserTeam(context.Context, *ReqListDocShareConfigReceiverUserTeam, *RspListDocShareConfigReceiverUserTeam) error
	// 绑定用户小程序unionid
	BindMiniProgramUniID(context.Context, *ReqBindMiniProgramUniID, *emptypb.Empty) error
	// 一键绑定用户和小程序unionid
	BindOnceUserMiniProgram(context.Context, *ReqBindOnceUserMiniProgram, *RspBindOnceUserMiniProgram) error
	// 绑定用户小程序手机号
	BindMiniProgramPhoneAccount(context.Context, *ReqBindMiniProgramPhoneAccount, *RspBindMiniProgramPhoneAccount) error
	// 绑定用户小程序普通账号
	BindMiniProgramNormalAccount(context.Context, *ReqBindMiniProgramNormalAccount, *RspBindMiniProgramNormalAccount) error
	// 获取用户账号union_id绑定状态
	GetAccountUnionIdStatus(context.Context, *ReqGetAccountUnionIdStatus, *RspGetAccountUnionIdStatus) error
	// 通过选择账号绑定小程序，替换临时账号
	BindSelectedAccount(context.Context, *ReqGetMiniProgramUserInfo, *RspGetMiniProgramUserInfo) error
	// 获取当前用户是否一键绑定提示
	GetAccountOnceBindStatus(context.Context, *ReqGetAccountOnceBindStatus, *RspGetAccountOnceBindStatus) error
	// 获取webview 迁移至小程序token
	GetWebViewToMiniProgramToken(context.Context, *ReqGetWebViewToMiniProgramToken, *RspGetWebViewToMiniProgramToken) error
	// 绑定用户小程序临时映射unionid
	BindUnitokenByCode(context.Context, *ReqBindUnitokenByCode, *emptypb.Empty) error
	// 获取小程序登录url
	GetMiniProgramLoginURL(context.Context, *ReqGetMiniProgramLoginURL, *RspGetMiniProgramLoginURL) error
	// 获取小程序文档相关权限
	GetMiniProgramAuth(context.Context, *ReqGetMiniProgramAuth, *RspGetMiniProgramAuth) error
	// 获取小程序助手限制情况
	GetMiniProgramAssistantLimit(context.Context, *ReqGetMiniProgramAssistantLimit, *RspGetMiniProgramAssistantLimit) error
	// 批量获取用户对应助手的限制情况
	BatchUserAssistantLimit(context.Context, *ReqBatchUserAssistantLimit, *RspBatchUserAssistantLimit) error
	// 绑定手机号通过小程序code
	BindUserPhoneByCode(context.Context, *ReqBindUserPhoneByCode, *RspBindUserPhoneByCode) error
	// 查询文本/文件列表
	ListTextFiles(context.Context, *ReqListTextFiles, *RspListTextFiles) error
	// id查询文本/文件详情
	GetTextFile(context.Context, *ReqGetTextFile, *RspGetTextFile) error
	// 创建QA
	CreateQAs(context.Context, *ReqCreateQAs, *RspCreateQAs) error
	// 创建文本或文件
	CreateTextFiles(context.Context, *ReqCreateTextFiles, *RspCreateTextFiles) error
	// 更新QA
	UpdateQAs(context.Context, *ReqUpdateQAs, *emptypb.Empty) error
	// 更新文本或文件
	UpdateTextFiles(context.Context, *ReqUpdateTextFiles, *emptypb.Empty) error
	// 批量更新doc的特定字段值
	BatchUpdateDocAttr(context.Context, *ReqBatchUpdateDocAttr, *RspBatchUpdateDocAttr) error
	// 删除Doc，包括QA，文本或文件
	DeleteDocs(context.Context, *ReqDeleteDocs, *RspDeleteDocs) error
	// 重新解析文件
	ReparseTextFiles(context.Context, *ReqReparseTextFiles, *RspReparseTextFiles) error
	CreateDocQuery(context.Context, *ReqCreateDocQuery, *RspCreateDocQuery) error
	// collection向量查询
	SearchCollection(context.Context, *ReqSearchCollection, *RspSearchCollection) error
	// 校验待创建QA
	ValidateQAs(context.Context, *ReqValidateQAs, *RspValidateQAs) error
	// 校验待创建文本文件
	ValidateTextFiles(context.Context, *ReqValidateTextFiles, *RspValidateTextFiles) error
	// 查询贡献者列表
	ListContributor(context.Context, *ReqListContributor, *RspListContributor) error
	// 查询更新人列表
	ListOperator(context.Context, *ReqListOperator, *RspListOperator) error
	// 查询已分享的助手列表，用于表头筛选
	ListSharedAssistant(context.Context, *ReqListSharedAssistant, *RspListSharedAssistant) error
	// 查询已分享的团队列表，用于表头筛选
	ListSharedTeam(context.Context, *ReqListSharedTeam, *RspListSharedTeam) error
	// 查询已分享的用户列表，用于表头筛选
	ListSharedUser(context.Context, *ReqListSharedUser, *RspListSharedUser) error
	// 查询文件列表
	ListCollectionFileName(context.Context, *ReqListCollectionFileName, *RspListCollectionFileName) error
	// 创建腾讯文档授权链接
	CreateTencentDocAuthUrl(context.Context, *ReqCreateTencentDocAuthUrl, *RspCreateTencentDocAuthUrl) error
	// 腾讯文档授权code处理
	AuthTencentCode(context.Context, *ReqAuthTencentCode, *RspAuthTencentCode) error
	// 查询腾讯文档缓存是否为空
	DescribeTencentToken(context.Context, *ReqDescribeTencentToken, *RspDescribeTencentToken) error
	// 查询腾讯文档授权列表
	ListExternalSourceUser(context.Context, *ReqListExternalSourceUser, *RspListExternalSourceUser) error
	// 查询选中的腾讯文档file_id
	DescribeMyDoc(context.Context, *ReqDescribeMyDoc, *RspDescribeMyDoc) error
	// 获取腾讯文档列表
	DescribeDocList(context.Context, *ReqDescribeDocList, *RspDescribeDocList) error
	// 查询腾讯文档任务状态
	DescribeTencentDocTask(context.Context, *ReqDescribeTencentDocTask, *RspDescribeTencentDocTask) error
	// 删除腾讯文档授权
	DelTencentDocAuth(context.Context, *ReqDelTencentDocAuth, *emptypb.Empty) error
	// 腾讯文档导入
	ImportTencentDoc(context.Context, *ReqImportTencentDoc, *RspImportTencentDoc) error
	// 重新导入腾讯文档
	ReimportTencentDoc(context.Context, *ReqReimportTencentDoc, *RspReimportTencentDoc) error
	// 导入腾讯文档网页剪辑
	ImportTencentDocWebClip(context.Context, *ReqImportTencentDocWebClip, *RspImportTencentDocWebClip) error
	// 创建文档标签
	ModifyDocTab(context.Context, *ReqModifyDocTab, *RspModifyDocTab) error
	// 获取文档标签
	DescribeDocTab(context.Context, *ReqDescribeDocTab, *RspDescribeDocTab) error
	// 批量导入绿技行
	CreateGTBDocText(context.Context, *ReqCreateGTBDocText, *RspCreateGTBDocText) error
	// 查询是否为绿技行用户
	DescribeAccountIsGTB(context.Context, *ReqDescribeAccountIsGTB, *RspDescribeAccountIsGTB) error
	// 克隆QA/文本/文件
	CloneDoc(context.Context, *ReqCloneDoc, *RspCloneDoc) error
	// 启用/禁用doc
	OnOffDocs(context.Context, *ReqOnOffDocs, *RspOnOffDocs) error
	// 获取人工坐席列表
	ListChatLiveAgent(context.Context, *ReqListChatLiveAgent, *RspListChatLiveAgent) error
	// 切换人工坐席
	SwitchChatLiveAgent(context.Context, *ReqSwitchChatLiveAgent, *RspSwitchChatLiveAgent) error
	// 查询用户反馈列表
	GetFeedbacks(context.Context, *ReqGetFeedbacks, *RspGetFeedbacks) error
	// 查询用户反馈详情
	FindFeedback(context.Context, *ReqFindFeedback, *RspFindFeedback) error
	// 采用用户反馈
	AcceptFeedback(context.Context, *ReqAcceptFeedback, *RspAcceptFeedback) error
	// 获取所有会话的地区编码
	DescribeChatRegionCode(context.Context, *ReqDescribeChatRegionCode, *RspDescribeChatRegionCode) error
	// 获取所有教学反馈的地区编码
	DescribeFeedbackRegionCode(context.Context, *ReqDescribeFeedbackRegionCode, *RspDescribeFeedbackRegionCode) error
	// 查询用户反馈日志列表
	GetFeedbackLogs(context.Context, *ReqGetFeedbackLogs, *RspGetFeedbackLogs) error
	// 搜索chat
	SearchChat(context.Context, *ReqSearchChat, *RspSearchChat) error
	// 获取助手url网页title
	ProxyChatHtmlUrl(context.Context, *ReqProxyChatHtmlUrl, *RspProxyChatHtmlUrl) error
	// 确认AI服务协议
	ConfirmAiServiceTerms(context.Context, *ReqConfirmAiServiceTerms, *emptypb.Empty) error
	// 获取我的AI服务协议确认情况
	GetMyAiServiceTermsConfirmation(context.Context, *emptypb.Empty, *RspGetMyAiServiceTermsConfirmation) error
	// 获取我的助手列表
	GetMyAssistants(context.Context, *ReqGetMyAssistants, *RspGetMyAssistants) error
	// 获取我团队的助手列表
	GetMyTeamAssistants(context.Context, *ReqGetMyAssistants, *RspGetMyAssistants) error
	// 更新我的助手
	UpdateMyAssistant(context.Context, *ReqUpdateMyAssistant, *emptypb.Empty) error
	// 更新我团队的助手
	UpdateMyTeamAssistant(context.Context, *ReqUpdateMyAssistant, *emptypb.Empty) error
	// 获取助手分段配置
	GetAssistantChunkConfig(context.Context, *ReqGetAssistantChunkConfig, *RspGetAssistantChunkConfig) error
	// 查询文档分段信息
	GetDocChunks(context.Context, *ReqGetDocChunks, *RspGetDocChunks) error
	// 自动文档分段
	AutoChunkDoc(context.Context, *ReqAutoChunkDoc, *RspAutoChunkDoc) error
	// 手动文档分段
	ManualChunkDoc(context.Context, *ReqManualChunkDoc, *RspManualChunkDoc) error
	// 查询文档分段任务列表
	GetChunkDocTasks(context.Context, *ReqGetChunkDocTasks, *RspGetChunkDocTasks) error
	// 查询文档的向量化模型
	GetDocEmbeddingModels(context.Context, *ReqGetDocEmbeddingModels, *RspGetDocEmbeddingModels) error
	// 小程序获取助手配置
	GetAssistantsMiniprogram(context.Context, *ReqGetAssistantsMiniprogram, *RspGetAssistantsMiniprogram) error
	// 查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
	GetTextFileTip(context.Context, *ReqGetTextFileTip, *RspGetTextFileTip) error
	// 查询QA的知识提示（问题超长，内容重复）等信息
	GetQaTip(context.Context, *ReqGetQaTip, *RspGetQaTip) error
	// 创建星云任务
	CreateNebulaTask(context.Context, *ReqCreateNebulaTask, *RspCreateNebulaTask) error
	// 查看星云任务详情
	DescribeNebulaTask(context.Context, *ReqDescribeNebulaTask, *RspDescribeNebulaTask) error
	// 查看星云任务列表
	DescribeNebulaTaskList(context.Context, *ReqDescribeNebulaTaskList, *RspDescribeNebulaTaskList) error
	// 查看投影坐标
	DescribeNebulaProjection(context.Context, *ReqDescribeNebulaProjection, *RspDescribeNebulaProjection) error
	// 查看投影元数据
	DescribeNebulaData(context.Context, *ReqDescribeNebulaData, *RspDescribeNebulaData) error
	// 知识星云，获取助手列表
	ListNebulaAssistants(context.Context, *ReqListNebulaAssistants, *RspListNebulaAssistants) error
	// 知识星云，获取助手下的贡献者筛选项
	ListNebulaContributors(context.Context, *ReqListNebulaContributors, *RspListNebulaContributors) error
	// 创建聊天分享
	CreateChatShare(context.Context, *ReqCreateChatShare, *RspCreateChatShare) error
	// 从分享继续聊天
	ContinueChatFromShare(context.Context, *ReqContinueChatFromShare, *RspContinueChatFromShare) error
	// 查询可分享的团队列表
	ListTeamCanShareDoc(context.Context, *ReqListTeamCanShareDoc, *RspListTeamCanShareDoc) error
	// 查询可分享的个人列表
	ListUserCanShareDoc(context.Context, *ReqListUserCanShareDoc, *RspListUserCanShareDoc) error
}

func RegisterAiBff(s bff.Server, h AiBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Ai"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/ai/describe_message_file_state", h.DescribeMessageFileState).Name("DescribeMessageFileState")
	bff.AddRoute(group, http.MethodPost, "/ai/create_chat_question", h.CreateChatQuestion).Name("CreateChatQuestion")
	bff.AddRoute(group, http.MethodPost, "/ai/restart_export_task", h.RestartExportTask).Name("RestartExportTask")
	bff.AddRoute(group, http.MethodPost, "/ai/create_message_export_task", h.CreateChatMessageExportTask).Name("CreateChatMessageExportTask")
	bff.AddRoute(group, http.MethodPost, "/ai/create_chat_export_task", h.CreateChatExportTask).Name("CreateChatExportTask")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_export_tasks", h.DescribeExportTasks).Name("DescribeExportTasks")
	bff.AddRoute(group, http.MethodPost, "/ai/create_qa_export_task", h.CreateQaExportTask).Name("CreateQaExportTask")
	bff.AddRoute(group, http.MethodPost, "/ai/create_file_export_task", h.CreateFileExportTask).Name("CreateFileExportTask")
	bff.AddRoute(group, http.MethodPost, "/ai/receive_chat_message", h.ReceiveChatMessage).Name("ReceiveChatMessage")
	bff.AddRoute(group, http.MethodPost, "/ai/create_chat", h.CreateChat).Name("CreateChat")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_chat_messages", h.DescribeChatMessages).Name("DescribeChatMessages")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_chats", h.DescribeChats).Name("DescribeChats")
	bff.AddRoute(group, http.MethodPost, "/ai/delete_chat", h.DeleteChat).Name("DeleteChat")
	bff.AddRoute(group, http.MethodPost, "/ai/resend_chat_message", h.ResendChatMessage).Name("ResendChatMessage")
	bff.AddRoute(group, http.MethodPost, "/ai/rate_ai_answer", h.RateAiAnswer).Name("RateAiAnswer")
	bff.AddRoute(group, http.MethodPost, "/ai/create_feedback", h.CreateFeedback).Name("CreateFeedback")
	bff.AddRoute(group, http.MethodPost, "/ai/save_user_feedback_by_question", h.SaveUserFeedbackByQuestion).Name("SaveUserFeedbackByQuestion")
	bff.AddRoute(group, http.MethodPost, "/ai/save_op_feedback", h.SaveOpFeedback).Name("SaveOpFeedback")
	bff.AddRoute(group, http.MethodPost, "/ai/stop_reply", h.StopQuestionReply).Name("StopQuestionReply")
	bff.AddRoute(group, http.MethodPost, "/ai/get_custom_labels", h.ListCustomLabel).Name("ListCustomLabel")
	bff.AddRoute(group, http.MethodPost, "/ai/modify_custom_labels", h.ModifyCustomLabels).Name("ModifyCustomLabels")
	bff.AddRoute(group, http.MethodPost, "/ai/delete_custom_labels", h.DeleteCustomLabels).Name("DeleteCustomLabels")
	bff.AddRoute(group, http.MethodPost, "/ai/update_object_custom_labels", h.UpdateObjectCustomLabels).Name("UpdateObjectCustomLabels")
	bff.AddRoute(group, http.MethodPost, "/ai/convert_custom_label", h.ConvertCustomLabel).Name("ConvertCustomLabel")
	bff.AddRoute(group, http.MethodPost, "/ai/get_custom_label_value_topn", h.GetCustomLabelValueTopN).Name("GetCustomLabelValueTopN")
	bff.AddRoute(group, http.MethodPost, "/ai/list_assistant", h.ListAssistant).Name("ListAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/search_chat_users", h.SearchChatUsers).Name("SearchChatUsers")
	bff.AddRoute(group, http.MethodPost, "/ai/list_chat", h.ListChat).Name("ListChat")
	bff.AddRoute(group, http.MethodPost, "/ai/get_chat_detail", h.GetChatDetail).Name("GetChatDetail")
	bff.AddRoute(group, http.MethodPost, "/ai/get_chat_message_detail", h.GetChatMessageDetail).Name("GetChatMessageDetail")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_qa", h.ListQA).Name("ListQA")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/import_text_files", h.ImportTextFiles).Name("ImportTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/import_qas", h.ImportQAs).Name("ImportQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/create_assistant_share", h.CreateAssistantShare).Name("CreateAssistantShare")
	bff.AddRoute(group, http.MethodPost, "/ai/create_assistant_sender", h.CreateDocShareConfigSender).Name("CreateDocShareConfigSender")
	bff.AddRoute(group, http.MethodPost, "/ai/list_assistant_sender", h.ListeDocShareConfigSender).Name("ListeDocShareConfigSender")
	bff.AddRoute(group, http.MethodPost, "/ai/list_my_assistant_ids", h.ListMyAssistantIds).Name("ListMyAssistantIds")
	bff.AddRoute(group, http.MethodPost, "/ai/list_assistant_to_share_doc", h.ListAssistantCanShareDoc).Name("ListAssistantCanShareDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/create_assistant_receiver", h.CreateDocShareConfigReceiverAssistant).Name("CreateDocShareConfigReceiverAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/list_assistant_receiver", h.ListDocShareConfigReceiverAssistant).Name("ListDocShareConfigReceiverAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/create_user_team_receiver", h.CreateDocShareConfigReceiverUserTeam).Name("CreateDocShareConfigReceiverUserTeam")
	bff.AddRoute(group, http.MethodPost, "/ai/list_user_team_receiver", h.ListDocShareConfigReceiverUserTeam).Name("ListDocShareConfigReceiverUserTeam")
	bff.AddRoute(group, http.MethodPost, "/ai/bind_mini_program_uniid", h.BindMiniProgramUniID).Name("BindMiniProgramUniID")
	bff.AddRoute(group, http.MethodPost, "/ai/bind_once_user_mini_program", h.BindOnceUserMiniProgram).Name("BindOnceUserMiniProgram")
	bff.AddRoute(group, http.MethodPost, "/ai/bind_mini_program_phone_account", h.BindMiniProgramPhoneAccount).Name("BindMiniProgramPhoneAccount")
	bff.AddRoute(group, http.MethodPost, "/ai/bind_mini_program_normal_account", h.BindMiniProgramNormalAccount).Name("BindMiniProgramNormalAccount")
	bff.AddRoute(group, http.MethodPost, "/ai/get_account_unionid_status", h.GetAccountUnionIdStatus).Name("GetAccountUnionIdStatus")
	bff.AddRoute(group, http.MethodPost, "/ai/get_mini_program_user_info", h.BindSelectedAccount).Name("BindSelectedAccount")
	bff.AddRoute(group, http.MethodPost, "/ai/get_account_once_bind_status", h.GetAccountOnceBindStatus).Name("GetAccountOnceBindStatus")
	bff.AddRoute(group, http.MethodPost, "/ai/get_webview_to_mini_program_token", h.GetWebViewToMiniProgramToken).Name("GetWebViewToMiniProgramToken")
	bff.AddRoute(group, http.MethodPost, "/ai/bind_unitoke_by_code", h.BindUnitokenByCode).Name("BindUnitokenByCode")
	bff.AddRoute(group, http.MethodPost, "/ai/get_mini_program_login_url", h.GetMiniProgramLoginURL).Name("GetMiniProgramLoginURL")
	bff.AddRoute(group, http.MethodPost, "/ai/get_mini_program_auth", h.GetMiniProgramAuth).Name("GetMiniProgramAuth")
	bff.AddRoute(group, http.MethodPost, "/ai/get_mini_program_assistant_limit", h.GetMiniProgramAssistantLimit).Name("GetMiniProgramAssistantLimit")
	bff.AddRoute(group, http.MethodPost, "/ai/batch_user_assistant_limit", h.BatchUserAssistantLimit).Name("BatchUserAssistantLimit")
	bff.AddRoute(group, http.MethodPost, "/ai/bind_user_phone_by_code", h.BindUserPhoneByCode).Name("BindUserPhoneByCode")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_text_files", h.ListTextFiles).Name("ListTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/get_text_file", h.GetTextFile).Name("GetTextFile")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_qas", h.CreateQAs).Name("CreateQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_text_files", h.CreateTextFiles).Name("CreateTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/update_qas", h.UpdateQAs).Name("UpdateQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/update_text_files", h.UpdateTextFiles).Name("UpdateTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/batch_update_docs", h.BatchUpdateDocAttr).Name("BatchUpdateDocAttr")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/delete_docs", h.DeleteDocs).Name("DeleteDocs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/reparse_text_files", h.ReparseTextFiles).Name("ReparseTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_doc_query", h.CreateDocQuery).Name("CreateDocQuery")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/search_collection", h.SearchCollection).Name("SearchCollection")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/validate_qas", h.ValidateQAs).Name("ValidateQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/validate_text_files", h.ValidateTextFiles).Name("ValidateTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_contributor", h.ListContributor).Name("ListContributor")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_operator", h.ListOperator).Name("ListOperator")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_shared_assistant", h.ListSharedAssistant).Name("ListSharedAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_shared_team", h.ListSharedTeam).Name("ListSharedTeam")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_shared_user", h.ListSharedUser).Name("ListSharedUser")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_filename", h.ListCollectionFileName).Name("ListCollectionFileName")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_tencent_doc_auth_url", h.CreateTencentDocAuthUrl).Name("CreateTencentDocAuthUrl")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/auth_tencent_code", h.AuthTencentCode).Name("AuthTencentCode")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/describe_tencent_token", h.DescribeTencentToken).Name("DescribeTencentToken")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_external_source_user", h.ListExternalSourceUser).Name("ListExternalSourceUser")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/describe_my_doc", h.DescribeMyDoc).Name("DescribeMyDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/describe_doc_list", h.DescribeDocList).Name("DescribeDocList")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/describe_tencent_doc_task", h.DescribeTencentDocTask).Name("DescribeTencentDocTask")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/del_tencent_doc_auth", h.DelTencentDocAuth).Name("DelTencentDocAuth")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/import_tencent_doc", h.ImportTencentDoc).Name("ImportTencentDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/reimport_tencent_doc", h.ReimportTencentDoc).Name("ReimportTencentDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/import_tencent_doc_webclip", h.ImportTencentDocWebClip).Name("ImportTencentDocWebClip")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/modify_doc_tab", h.ModifyDocTab).Name("ModifyDocTab")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/describe_doc_tab", h.DescribeDocTab).Name("DescribeDocTab")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_gtb_doc_text", h.CreateGTBDocText).Name("CreateGTBDocText")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/describe_account_is_gtb", h.DescribeAccountIsGTB).Name("DescribeAccountIsGTB")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/clone_doc", h.CloneDoc).Name("CloneDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/onoff_docs", h.OnOffDocs).Name("OnOffDocs")
	bff.AddRoute(group, http.MethodPost, "/ai/list_chat_live_agent", h.ListChatLiveAgent).Name("ListChatLiveAgent")
	bff.AddRoute(group, http.MethodPost, "/ai/switch_chat_live_agent", h.SwitchChatLiveAgent).Name("SwitchChatLiveAgent")
	bff.AddRoute(group, http.MethodPost, "/ai/get_feedbacks", h.GetFeedbacks).Name("GetFeedbacks")
	bff.AddRoute(group, http.MethodPost, "/ai/find_feedback", h.FindFeedback).Name("FindFeedback")
	bff.AddRoute(group, http.MethodPost, "/ai/accept_feedback", h.AcceptFeedback).Name("AcceptFeedback")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_chat_region_code", h.DescribeChatRegionCode).Name("DescribeChatRegionCode")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_feedback_region_code", h.DescribeFeedbackRegionCode).Name("DescribeFeedbackRegionCode")
	bff.AddRoute(group, http.MethodPost, "/ai/get_feedback_logs", h.GetFeedbackLogs).Name("GetFeedbackLogs")
	bff.AddRoute(group, http.MethodPost, "/ai/search_chat", h.SearchChat).Name("SearchChat")
	bff.AddRoute(group, http.MethodPost, "/ai/proxy_chat_url", h.ProxyChatHtmlUrl).Name("ProxyChatHtmlUrl")
	bff.AddRoute(group, http.MethodPost, "/ai/confirm_ai_service_terms", h.ConfirmAiServiceTerms).Name("ConfirmAiServiceTerms")
	bff.AddRoute(group, http.MethodPost, "/ai/get_my_ai_service_terms_confirmation", h.GetMyAiServiceTermsConfirmation).Name("GetMyAiServiceTermsConfirmation")
	bff.AddRoute(group, http.MethodPost, "/ai/get_my_assistants", h.GetMyAssistants).Name("GetMyAssistants")
	bff.AddRoute(group, http.MethodPost, "/ai/get_my_assistants_team", h.GetMyTeamAssistants).Name("GetMyTeamAssistants")
	bff.AddRoute(group, http.MethodPost, "/ai/update_my_assistant", h.UpdateMyAssistant).Name("UpdateMyAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/update_my_assistant_team", h.UpdateMyTeamAssistant).Name("UpdateMyTeamAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/get_assistant_chunk_config", h.GetAssistantChunkConfig).Name("GetAssistantChunkConfig")
	bff.AddRoute(group, http.MethodPost, "/ai/get_doc_chunks", h.GetDocChunks).Name("GetDocChunks")
	bff.AddRoute(group, http.MethodPost, "/ai/auto_chunk_doc", h.AutoChunkDoc).Name("AutoChunkDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/manual_chunk_doc", h.ManualChunkDoc).Name("ManualChunkDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/get_chunk_doc_tasks", h.GetChunkDocTasks).Name("GetChunkDocTasks")
	bff.AddRoute(group, http.MethodPost, "/ai/get_doc_embedding_models", h.GetDocEmbeddingModels).Name("GetDocEmbeddingModels")
	bff.AddRoute(group, http.MethodPost, "/ai/get_assistants_miniprogram", h.GetAssistantsMiniprogram).Name("GetAssistantsMiniprogram")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/get_text_file_tip", h.GetTextFileTip).Name("GetTextFileTip")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/get_qa_tip", h.GetQaTip).Name("GetQaTip")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/create_task", h.CreateNebulaTask).Name("CreateNebulaTask")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/describe_task", h.DescribeNebulaTask).Name("DescribeNebulaTask")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/describe_task_list", h.DescribeNebulaTaskList).Name("DescribeNebulaTaskList")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/describe_projection", h.DescribeNebulaProjection).Name("DescribeNebulaProjection")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/describe_medata", h.DescribeNebulaData).Name("DescribeNebulaData")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/list_assistants", h.ListNebulaAssistants).Name("ListNebulaAssistants")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/list_contributors", h.ListNebulaContributors).Name("ListNebulaContributors")
	bff.AddRoute(group, http.MethodPost, "/ai/chat_share_create", h.CreateChatShare).Name("CreateChatShare")
	bff.AddRoute(group, http.MethodPost, "/ai/chat_share_continue", h.ContinueChatFromShare).Name("ContinueChatFromShare")
	bff.AddRoute(group, http.MethodPost, "/ai/list_team_to_share_doc", h.ListTeamCanShareDoc).Name("ListTeamCanShareDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/list_user_to_share_doc", h.ListUserCanShareDoc).Name("ListUserCanShareDoc")
	return group
}

type AiGuestBffHandler interface {
	// 获取助手配置
	GetAssistantConfig(context.Context, *ReqGetAssistantConfig, *RspGetAssistantConfig) error
	// 获取助手下拉选项
	GetAssistantOptions(context.Context, *emptypb.Empty, *RspGetAssistantOptions) error
	// 获取公开分享详情
	GetPublicChatShare(context.Context, *ReqGetPublicChatShare, *RspGetPublicChatShare) error
}

func RegisterAiGuestBff(s bff.Server, h AiGuestBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("AiGuest"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/ai/get_assistant_config", h.GetAssistantConfig).Name("GetAssistantConfig")
	bff.AddRoute(group, http.MethodPost, "/ai/get_assistant_options", h.GetAssistantOptions).Name("GetAssistantOptions")
	bff.AddRoute(group, http.MethodPost, "/ai/chat_share_get", h.GetPublicChatShare).Name("GetPublicChatShare")
	return group
}
