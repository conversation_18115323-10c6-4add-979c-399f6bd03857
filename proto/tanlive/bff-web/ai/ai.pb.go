// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/ai/ai.proto

package ai

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	ai "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iam "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AiMessageDoType int32

const (
	AiMessageDoType_AI_MESSAGE_DO_TYPE_UNSPECIFIED AiMessageDoType = 0
	// 返回
	AiMessageDoType_AI_MESSAGE_DO_TYPE_UNSPECIFIED_RETURN AiMessageDoType = 1
	// 继续执行
	AiMessageDoType_AI_MESSAGE_DO_TYPE_UNSPECIFIED_NEXT AiMessageDoType = 2
	// 返回后执行，开启协程执行
	AiMessageDoType_AI_MESSAGE_DO_TYPE_UNSPECIFIED_RETURN_NEXT AiMessageDoType = 3
)

// Enum value maps for AiMessageDoType.
var (
	AiMessageDoType_name = map[int32]string{
		0: "AI_MESSAGE_DO_TYPE_UNSPECIFIED",
		1: "AI_MESSAGE_DO_TYPE_UNSPECIFIED_RETURN",
		2: "AI_MESSAGE_DO_TYPE_UNSPECIFIED_NEXT",
		3: "AI_MESSAGE_DO_TYPE_UNSPECIFIED_RETURN_NEXT",
	}
	AiMessageDoType_value = map[string]int32{
		"AI_MESSAGE_DO_TYPE_UNSPECIFIED":             0,
		"AI_MESSAGE_DO_TYPE_UNSPECIFIED_RETURN":      1,
		"AI_MESSAGE_DO_TYPE_UNSPECIFIED_NEXT":        2,
		"AI_MESSAGE_DO_TYPE_UNSPECIFIED_RETURN_NEXT": 3,
	}
)

func (x AiMessageDoType) Enum() *AiMessageDoType {
	p := new(AiMessageDoType)
	*p = x
	return p
}

func (x AiMessageDoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AiMessageDoType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_bff_web_ai_ai_proto_enumTypes[0].Descriptor()
}

func (AiMessageDoType) Type() protoreflect.EnumType {
	return &file_tanlive_bff_web_ai_ai_proto_enumTypes[0]
}

func (x AiMessageDoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AiMessageDoType.Descriptor instead.
func (AiMessageDoType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{0}
}

type MiniProgramSourceType int32

const (
	MiniProgramSourceType_SourceTypePC       MiniProgramSourceType = 0
	MiniProgramSourceType_SourceTypeH5Scheme MiniProgramSourceType = 1
	MiniProgramSourceType_SourceTypeH5Url    MiniProgramSourceType = 2
)

// Enum value maps for MiniProgramSourceType.
var (
	MiniProgramSourceType_name = map[int32]string{
		0: "SourceTypePC",
		1: "SourceTypeH5Scheme",
		2: "SourceTypeH5Url",
	}
	MiniProgramSourceType_value = map[string]int32{
		"SourceTypePC":       0,
		"SourceTypeH5Scheme": 1,
		"SourceTypeH5Url":    2,
	}
)

func (x MiniProgramSourceType) Enum() *MiniProgramSourceType {
	p := new(MiniProgramSourceType)
	*p = x
	return p
}

func (x MiniProgramSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MiniProgramSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_bff_web_ai_ai_proto_enumTypes[1].Descriptor()
}

func (MiniProgramSourceType) Type() protoreflect.EnumType {
	return &file_tanlive_bff_web_ai_ai_proto_enumTypes[1]
}

func (x MiniProgramSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MiniProgramSourceType.Descriptor instead.
func (MiniProgramSourceType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{1}
}

// 分享类型
type ShareType int32

const (
	ShareType_SHARE_TYPE_UNSPECIFIED ShareType = 0
	// 链接分享
	ShareType_SHARE_TYPE_LINK ShareType = 1
	// 二维码分享
	ShareType_SHARE_TYPE_QR_CODE ShareType = 2
	// 小程序码分享
	ShareType_SHARE_TYPE_MINI_PROGRAM ShareType = 3
)

// Enum value maps for ShareType.
var (
	ShareType_name = map[int32]string{
		0: "SHARE_TYPE_UNSPECIFIED",
		1: "SHARE_TYPE_LINK",
		2: "SHARE_TYPE_QR_CODE",
		3: "SHARE_TYPE_MINI_PROGRAM",
	}
	ShareType_value = map[string]int32{
		"SHARE_TYPE_UNSPECIFIED":  0,
		"SHARE_TYPE_LINK":         1,
		"SHARE_TYPE_QR_CODE":      2,
		"SHARE_TYPE_MINI_PROGRAM": 3,
	}
)

func (x ShareType) Enum() *ShareType {
	p := new(ShareType)
	*p = x
	return p
}

func (x ShareType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShareType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_bff_web_ai_ai_proto_enumTypes[2].Descriptor()
}

func (ShareType) Type() protoreflect.EnumType {
	return &file_tanlive_bff_web_ai_ai_proto_enumTypes[2]
}

func (x ShareType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShareType.Descriptor instead.
func (ShareType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{2}
}

// 分享状态
type ShareStatus int32

const (
	ShareStatus_SHARE_STATUS_UNSPECIFIED ShareStatus = 0
	// 有效
	ShareStatus_SHARE_STATUS_ACTIVE ShareStatus = 1
	// 已失效（手动失效）
	ShareStatus_SHARE_STATUS_INACTIVE ShareStatus = 2
	// 已过期
	ShareStatus_SHARE_STATUS_EXPIRED ShareStatus = 3
)

// Enum value maps for ShareStatus.
var (
	ShareStatus_name = map[int32]string{
		0: "SHARE_STATUS_UNSPECIFIED",
		1: "SHARE_STATUS_ACTIVE",
		2: "SHARE_STATUS_INACTIVE",
		3: "SHARE_STATUS_EXPIRED",
	}
	ShareStatus_value = map[string]int32{
		"SHARE_STATUS_UNSPECIFIED": 0,
		"SHARE_STATUS_ACTIVE":      1,
		"SHARE_STATUS_INACTIVE":    2,
		"SHARE_STATUS_EXPIRED":     3,
	}
)

func (x ShareStatus) Enum() *ShareStatus {
	p := new(ShareStatus)
	*p = x
	return p
}

func (x ShareStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShareStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_bff_web_ai_ai_proto_enumTypes[3].Descriptor()
}

func (ShareStatus) Type() protoreflect.EnumType {
	return &file_tanlive_bff_web_ai_ai_proto_enumTypes[3]
}

func (x ShareStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShareStatus.Descriptor instead.
func (ShareStatus) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{3}
}

type Chat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title      string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	CreateBy   uint64                 `protobuf:"varint,3,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	IsShared   bool                   `protobuf:"varint,5,opt,name=is_shared,json=isShared,proto3" json:"is_shared,omitempty"`
}

func (x *Chat) Reset() {
	*x = Chat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chat) ProtoMessage() {}

func (x *Chat) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chat.ProtoReflect.Descriptor instead.
func (*Chat) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{0}
}

func (x *Chat) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Chat) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Chat) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *Chat) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *Chat) GetIsShared() bool {
	if x != nil {
		return x.IsShared
	}
	return false
}

// 对话管理端信息
type ChatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	CreateBy      *iam.UserInfo          `protobuf:"bytes,3,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	UpdateDate    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	CreateDate    *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	ChatType      ai.ChatType            `protobuf:"varint,6,opt,name=chat_type,json=chatType,proto3,enum=tanlive.ai.ChatType" json:"chat_type,omitempty"`
	AssistantId   uint64                 `protobuf:"varint,7,opt,name=assistant_id,json=assistantId,proto3" json:"assistant_id,omitempty"`
	AssistantName string                 `protobuf:"bytes,8,opt,name=assistant_name,json=assistantName,proto3" json:"assistant_name,omitempty"`
	ChatState     ai.ChatCurrentState    `protobuf:"varint,9,opt,name=chat_state,json=chatState,proto3,enum=tanlive.ai.ChatCurrentState" json:"chat_state,omitempty"`
	// 当前服务状态
	SupportType ai.ChatSupportType `protobuf:"varint,10,opt,name=support_type,json=supportType,proto3,enum=tanlive.ai.ChatSupportType" json:"support_type,omitempty"`
	// 对话中问题数量
	QuestionCnt uint32 `protobuf:"varint,11,opt,name=question_cnt,json=questionCnt,proto3" json:"question_cnt,omitempty"`
	// 自定义标签kv对
	Labels          []*ai.CustomLabel `protobuf:"bytes,12,rep,name=labels,proto3" json:"labels,omitempty"`
	RegionCode      string            `protobuf:"bytes,13,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	RatingScale     ai.RatingScale    `protobuf:"varint,14,opt,name=rating_scale,json=ratingScale,proto3,enum=tanlive.ai.RatingScale" json:"rating_scale,omitempty"`
	DocHits         float32           `protobuf:"fixed32,15,opt,name=doc_hits,json=docHits,proto3" json:"doc_hits,omitempty"`
	AvgDuration     float32           `protobuf:"fixed32,16,opt,name=avg_duration,json=avgDuration,proto3" json:"avg_duration,omitempty"`
	RejectJobResult uint32            `protobuf:"varint,17,opt,name=reject_job_result,json=rejectJobResult,proto3" json:"reject_job_result,omitempty"`
	// 是否转过人工服务
	IsManual int32 `protobuf:"varint,18,opt,name=is_manual,json=isManual,proto3" json:"is_manual,omitempty"`
}

func (x *ChatInfo) Reset() {
	*x = ChatInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatInfo) ProtoMessage() {}

func (x *ChatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatInfo.ProtoReflect.Descriptor instead.
func (*ChatInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{1}
}

func (x *ChatInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ChatInfo) GetCreateBy() *iam.UserInfo {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *ChatInfo) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *ChatInfo) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *ChatInfo) GetChatType() ai.ChatType {
	if x != nil {
		return x.ChatType
	}
	return ai.ChatType(0)
}

func (x *ChatInfo) GetAssistantId() uint64 {
	if x != nil {
		return x.AssistantId
	}
	return 0
}

func (x *ChatInfo) GetAssistantName() string {
	if x != nil {
		return x.AssistantName
	}
	return ""
}

func (x *ChatInfo) GetChatState() ai.ChatCurrentState {
	if x != nil {
		return x.ChatState
	}
	return ai.ChatCurrentState(0)
}

func (x *ChatInfo) GetSupportType() ai.ChatSupportType {
	if x != nil {
		return x.SupportType
	}
	return ai.ChatSupportType(0)
}

func (x *ChatInfo) GetQuestionCnt() uint32 {
	if x != nil {
		return x.QuestionCnt
	}
	return 0
}

func (x *ChatInfo) GetLabels() []*ai.CustomLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ChatInfo) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *ChatInfo) GetRatingScale() ai.RatingScale {
	if x != nil {
		return x.RatingScale
	}
	return ai.RatingScale(0)
}

func (x *ChatInfo) GetDocHits() float32 {
	if x != nil {
		return x.DocHits
	}
	return 0
}

func (x *ChatInfo) GetAvgDuration() float32 {
	if x != nil {
		return x.AvgDuration
	}
	return 0
}

func (x *ChatInfo) GetRejectJobResult() uint32 {
	if x != nil {
		return x.RejectJobResult
	}
	return 0
}

func (x *ChatInfo) GetIsManual() int32 {
	if x != nil {
		return x.IsManual
	}
	return 0
}

type TencentDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title          string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Url            string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	FileName       string `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileId         string `protobuf:"bytes,4,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	FileType       string `protobuf:"bytes,5,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileCreateUser string `protobuf:"bytes,6,opt,name=file_create_user,json=fileCreateUser,proto3" json:"file_create_user,omitempty"`
	FileOwnerName  string `protobuf:"bytes,7,opt,name=file_owner_name,json=fileOwnerName,proto3" json:"file_owner_name,omitempty"`
	FileCreateTime string `protobuf:"bytes,8,opt,name=file_create_time,json=fileCreateTime,proto3" json:"file_create_time,omitempty"`
	FileModifyTime string `protobuf:"bytes,9,opt,name=file_modify_time,json=fileModifyTime,proto3" json:"file_modify_time,omitempty"`
	FileBrowseTime string `protobuf:"bytes,10,opt,name=file_browse_time,json=fileBrowseTime,proto3" json:"file_browse_time,omitempty"`
	FileUrl        string `protobuf:"bytes,12,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
}

func (x *TencentDoc) Reset() {
	*x = TencentDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TencentDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TencentDoc) ProtoMessage() {}

func (x *TencentDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TencentDoc.ProtoReflect.Descriptor instead.
func (*TencentDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{2}
}

func (x *TencentDoc) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *TencentDoc) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *TencentDoc) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *TencentDoc) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *TencentDoc) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *TencentDoc) GetFileCreateUser() string {
	if x != nil {
		return x.FileCreateUser
	}
	return ""
}

func (x *TencentDoc) GetFileOwnerName() string {
	if x != nil {
		return x.FileOwnerName
	}
	return ""
}

func (x *TencentDoc) GetFileCreateTime() string {
	if x != nil {
		return x.FileCreateTime
	}
	return ""
}

func (x *TencentDoc) GetFileModifyTime() string {
	if x != nil {
		return x.FileModifyTime
	}
	return ""
}

func (x *TencentDoc) GetFileBrowseTime() string {
	if x != nil {
		return x.FileBrowseTime
	}
	return ""
}

func (x *TencentDoc) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

type Assistant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	NameEn       string  `protobuf:"bytes,3,opt,name=name_en,json=nameEn,proto3" json:"name_en,omitempty"`
	WebsiteRoute string  `protobuf:"bytes,4,opt,name=website_route,json=websiteRoute,proto3" json:"website_route,omitempty"`
	SearchDebug  bool    `protobuf:"varint,5,opt,name=search_debug,json=searchDebug,proto3" json:"search_debug,omitempty"`
	Threshold    float32 `protobuf:"fixed32,6,opt,name=threshold,proto3" json:"threshold,omitempty"`
	TopN         int32   `protobuf:"varint,7,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	TextWeight   float32 `protobuf:"fixed32,8,opt,name=text_weight,json=textWeight,proto3" json:"text_weight,omitempty"`
	// 关键词召回条数
	TextRecallTopN int32 `protobuf:"varint,9,opt,name=text_recall_top_n,json=textRecallTopN,proto3" json:"text_recall_top_n,omitempty"`
	CleanChunks    bool  `protobuf:"varint,10,opt,name=clean_chunks,json=cleanChunks,proto3" json:"clean_chunks,omitempty"`
}

func (x *Assistant) Reset() {
	*x = Assistant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Assistant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Assistant) ProtoMessage() {}

func (x *Assistant) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Assistant.ProtoReflect.Descriptor instead.
func (*Assistant) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{3}
}

func (x *Assistant) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Assistant) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Assistant) GetNameEn() string {
	if x != nil {
		return x.NameEn
	}
	return ""
}

func (x *Assistant) GetWebsiteRoute() string {
	if x != nil {
		return x.WebsiteRoute
	}
	return ""
}

func (x *Assistant) GetSearchDebug() bool {
	if x != nil {
		return x.SearchDebug
	}
	return false
}

func (x *Assistant) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *Assistant) GetTopN() int32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *Assistant) GetTextWeight() float32 {
	if x != nil {
		return x.TextWeight
	}
	return 0
}

func (x *Assistant) GetTextRecallTopN() int32 {
	if x != nil {
		return x.TextRecallTopN
	}
	return 0
}

func (x *Assistant) GetCleanChunks() bool {
	if x != nil {
		return x.CleanChunks
	}
	return false
}

type ChatDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title      string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Messages   []*ai.EventChatMessage `protobuf:"bytes,3,rep,name=messages,proto3" json:"messages,omitempty"`
	CreateBy   *iam.UserInfo          `protobuf:"bytes,5,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	FinishDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=finish_date,json=finishDate,proto3" json:"finish_date,omitempty"`
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	ChatType   ai.ChatType            `protobuf:"varint,8,opt,name=chat_type,json=chatType,proto3,enum=tanlive.ai.ChatType" json:"chat_type,omitempty"`
	ChatState  ai.ChatCurrentState    `protobuf:"varint,9,opt,name=chat_state,json=chatState,proto3,enum=tanlive.ai.ChatCurrentState" json:"chat_state,omitempty"`
	// 当前服务状态
	SupportType ai.ChatSupportType `protobuf:"varint,10,opt,name=support_type,json=supportType,proto3,enum=tanlive.ai.ChatSupportType" json:"support_type,omitempty"`
	// 微信客服助手头像
	AssistantAvatar string `protobuf:"bytes,11,opt,name=assistant_avatar,json=assistantAvatar,proto3" json:"assistant_avatar,omitempty"`
	// 非web端时通过次字段返回消息详情
	Records     []*ai.ChatSendRecordInfo `protobuf:"bytes,12,rep,name=records,proto3" json:"records,omitempty"`
	AssistantId uint64                   `protobuf:"varint,13,opt,name=assistant_id,json=assistantId,proto3" json:"assistant_id,omitempty"`
}

func (x *ChatDetail) Reset() {
	*x = ChatDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatDetail) ProtoMessage() {}

func (x *ChatDetail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatDetail.ProtoReflect.Descriptor instead.
func (*ChatDetail) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{4}
}

func (x *ChatDetail) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatDetail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ChatDetail) GetMessages() []*ai.EventChatMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *ChatDetail) GetCreateBy() *iam.UserInfo {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *ChatDetail) GetFinishDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishDate
	}
	return nil
}

func (x *ChatDetail) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *ChatDetail) GetChatType() ai.ChatType {
	if x != nil {
		return x.ChatType
	}
	return ai.ChatType(0)
}

func (x *ChatDetail) GetChatState() ai.ChatCurrentState {
	if x != nil {
		return x.ChatState
	}
	return ai.ChatCurrentState(0)
}

func (x *ChatDetail) GetSupportType() ai.ChatSupportType {
	if x != nil {
		return x.SupportType
	}
	return ai.ChatSupportType(0)
}

func (x *ChatDetail) GetAssistantAvatar() string {
	if x != nil {
		return x.AssistantAvatar
	}
	return ""
}

func (x *ChatDetail) GetRecords() []*ai.ChatSendRecordInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *ChatDetail) GetAssistantId() uint64 {
	if x != nil {
		return x.AssistantId
	}
	return 0
}

type DocShareTeamReceiver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	NameEn string `protobuf:"bytes,3,opt,name=name_en,json=nameEn,proto3" json:"name_en,omitempty"`
}

func (x *DocShareTeamReceiver) Reset() {
	*x = DocShareTeamReceiver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocShareTeamReceiver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocShareTeamReceiver) ProtoMessage() {}

func (x *DocShareTeamReceiver) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocShareTeamReceiver.ProtoReflect.Descriptor instead.
func (*DocShareTeamReceiver) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{5}
}

func (x *DocShareTeamReceiver) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DocShareTeamReceiver) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DocShareTeamReceiver) GetNameEn() string {
	if x != nil {
		return x.NameEn
	}
	return ""
}

type DocShareUserReceiver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DocShareUserReceiver) Reset() {
	*x = DocShareUserReceiver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocShareUserReceiver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocShareUserReceiver) ProtoMessage() {}

func (x *DocShareUserReceiver) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocShareUserReceiver.ProtoReflect.Descriptor instead.
func (*DocShareUserReceiver) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{6}
}

func (x *DocShareUserReceiver) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DocShareUserReceiver) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CollectionQA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 问题
	Question string `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	// 答案
	Answer string `protobuf:"bytes,3,opt,name=answer,proto3" json:"answer,omitempty"`
	// 用户端
	Assistants []*ai.Assistant         `protobuf:"bytes,4,rep,name=assistants,proto3" json:"assistants,omitempty"`
	States     []*ai.DocAssistantState `protobuf:"bytes,5,rep,name=states,proto3" json:"states,omitempty"`
	// 贡献者
	Contributor []*ai.Contributor `protobuf:"bytes,6,rep,name=contributor,proto3" json:"contributor,omitempty"`
	// 参考资料
	Reference []*ai.DocReference `protobuf:"bytes,7,rep,name=reference,proto3" json:"reference,omitempty"`
	// 命中次数
	HitCount   uint32                 `protobuf:"varint,8,opt,name=hit_count,json=hitCount,proto3" json:"hit_count,omitempty"`
	CreateBy   *DocOperator           `protobuf:"bytes,9,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	UpdateBy   *DocOperator           `protobuf:"bytes,10,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	UpdateDate *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 同步至ai侧的版本滞后数，为0代表已同步
	VersionLag uint64 `protobuf:"varint,13,opt,name=version_lag,json=versionLag,proto3" json:"version_lag,omitempty"`
	// 分享的用户端
	SharedStates []*ai.DocAssistantState `protobuf:"bytes,14,rep,name=shared_states,json=sharedStates,proto3" json:"shared_states,omitempty"`
	// 分享的团队
	SharedTeams []*DocShareTeamReceiver `protobuf:"bytes,15,rep,name=shared_teams,json=sharedTeams,proto3" json:"shared_teams,omitempty"`
	// 分享的用户
	SharedUsers []*DocShareUserReceiver `protobuf:"bytes,16,rep,name=shared_users,json=sharedUsers,proto3" json:"shared_users,omitempty"`
	// 是否显示贡献者
	ShowContributor uint32 `protobuf:"varint,17,opt,name=show_contributor,json=showContributor,proto3" json:"show_contributor,omitempty"`
	// 状态(不包含助手对应的状态)
	State  ai.DocState       `protobuf:"varint,18,opt,name=state,proto3,enum=tanlive.ai.DocState" json:"state,omitempty"`
	Labels []*ai.CustomLabel `protobuf:"bytes,19,rep,name=labels,proto3" json:"labels,omitempty"`
	// 匹配模式
	MatchPatterns    []ai.DocMatchPattern `protobuf:"varint,20,rep,packed,name=match_patterns,json=matchPatterns,proto3,enum=tanlive.ai.DocMatchPattern" json:"match_patterns,omitempty"`
	QuestionOversize bool                 `protobuf:"varint,21,opt,name=question_oversize,json=questionOversize,proto3" json:"question_oversize,omitempty"`
	HasRepeated      bool                 `protobuf:"varint,22,opt,name=has_repeated,json=hasRepeated,proto3" json:"has_repeated,omitempty"`
	// 是否收到分享
	ReceivedShare bool `protobuf:"varint,23,opt,name=received_share,json=receivedShare,proto3" json:"received_share,omitempty"`
}

func (x *CollectionQA) Reset() {
	*x = CollectionQA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionQA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionQA) ProtoMessage() {}

func (x *CollectionQA) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionQA.ProtoReflect.Descriptor instead.
func (*CollectionQA) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{7}
}

func (x *CollectionQA) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CollectionQA) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *CollectionQA) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *CollectionQA) GetAssistants() []*ai.Assistant {
	if x != nil {
		return x.Assistants
	}
	return nil
}

func (x *CollectionQA) GetStates() []*ai.DocAssistantState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *CollectionQA) GetContributor() []*ai.Contributor {
	if x != nil {
		return x.Contributor
	}
	return nil
}

func (x *CollectionQA) GetReference() []*ai.DocReference {
	if x != nil {
		return x.Reference
	}
	return nil
}

func (x *CollectionQA) GetHitCount() uint32 {
	if x != nil {
		return x.HitCount
	}
	return 0
}

func (x *CollectionQA) GetCreateBy() *DocOperator {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *CollectionQA) GetUpdateBy() *DocOperator {
	if x != nil {
		return x.UpdateBy
	}
	return nil
}

func (x *CollectionQA) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *CollectionQA) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *CollectionQA) GetVersionLag() uint64 {
	if x != nil {
		return x.VersionLag
	}
	return 0
}

func (x *CollectionQA) GetSharedStates() []*ai.DocAssistantState {
	if x != nil {
		return x.SharedStates
	}
	return nil
}

func (x *CollectionQA) GetSharedTeams() []*DocShareTeamReceiver {
	if x != nil {
		return x.SharedTeams
	}
	return nil
}

func (x *CollectionQA) GetSharedUsers() []*DocShareUserReceiver {
	if x != nil {
		return x.SharedUsers
	}
	return nil
}

func (x *CollectionQA) GetShowContributor() uint32 {
	if x != nil {
		return x.ShowContributor
	}
	return 0
}

func (x *CollectionQA) GetState() ai.DocState {
	if x != nil {
		return x.State
	}
	return ai.DocState(0)
}

func (x *CollectionQA) GetLabels() []*ai.CustomLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CollectionQA) GetMatchPatterns() []ai.DocMatchPattern {
	if x != nil {
		return x.MatchPatterns
	}
	return nil
}

func (x *CollectionQA) GetQuestionOversize() bool {
	if x != nil {
		return x.QuestionOversize
	}
	return false
}

func (x *CollectionQA) GetHasRepeated() bool {
	if x != nil {
		return x.HasRepeated
	}
	return false
}

func (x *CollectionQA) GetReceivedShare() bool {
	if x != nil {
		return x.ReceivedShare
	}
	return false
}

type DocOperator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type base.IdentityType `protobuf:"varint,1,opt,name=type,proto3,enum=tanlive.base.IdentityType" json:"type,omitempty"`
	// 用户账户: 用户id
	// 团队账户: 用户id
	// 运营端账户: 运营端用户id
	Id uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 用户名称
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	// 用户所属团队名称
	TeamName string `protobuf:"bytes,4,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
	// 用户id，只有为团队用户时，才需要
	UserId uint64 `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *DocOperator) Reset() {
	*x = DocOperator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocOperator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocOperator) ProtoMessage() {}

func (x *DocOperator) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocOperator.ProtoReflect.Descriptor instead.
func (*DocOperator) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{8}
}

func (x *DocOperator) GetType() base.IdentityType {
	if x != nil {
		return x.Type
	}
	return base.IdentityType(0)
}

func (x *DocOperator) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DocOperator) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *DocOperator) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *DocOperator) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CollectionTextFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文件/文本名称
	FileName string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// 文件/文本内容
	Text string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	// 用户端
	Assistants []*ai.Assistant `protobuf:"bytes,4,rep,name=assistants,proto3" json:"assistants,omitempty"`
	// 状态(不包含助手对应的状态)
	State ai.DocState `protobuf:"varint,5,opt,name=state,proto3,enum=tanlive.ai.DocState" json:"state,omitempty"`
	// 贡献者
	Contributor []*ai.Contributor `protobuf:"bytes,6,rep,name=contributor,proto3" json:"contributor,omitempty"`
	// 文件url
	Url string `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	// 命中次数
	HitCount uint32 `protobuf:"varint,8,opt,name=hit_count,json=hitCount,proto3" json:"hit_count,omitempty"`
	// ugc类型
	UgcType base.DataType `protobuf:"varint,9,opt,name=ugc_type,json=ugcType,proto3,enum=tanlive.base.DataType" json:"ugc_type,omitempty"`
	// ugc的id
	UgcId      uint64                 `protobuf:"varint,10,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
	CreateBy   *DocOperator           `protobuf:"bytes,11,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	UpdateBy   *DocOperator           `protobuf:"bytes,12,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	UpdateDate *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 同步至ai侧的版本滞后数，为0代表已同步
	VersionLag uint64 `protobuf:"varint,15,opt,name=version_lag,json=versionLag,proto3" json:"version_lag,omitempty"`
	// 解析进度，0.5 = 50%
	ParseProgress float32 `protobuf:"fixed32,16,opt,name=parse_progress,json=parseProgress,proto3" json:"parse_progress,omitempty"`
	// 是否显示贡献者
	ShowContributor uint32                  `protobuf:"varint,17,opt,name=show_contributor,json=showContributor,proto3" json:"show_contributor,omitempty"`
	States          []*ai.DocAssistantState `protobuf:"bytes,18,rep,name=states,proto3" json:"states,omitempty"`
	SharedStates    []*ai.DocAssistantState `protobuf:"bytes,19,rep,name=shared_states,json=sharedStates,proto3" json:"shared_states,omitempty"`
	Labels          []*ai.CustomLabel       `protobuf:"bytes,20,rep,name=labels,proto3" json:"labels,omitempty"`
	// 参考资料
	Reference []*ai.DocReference `protobuf:"bytes,21,rep,name=reference,proto3" json:"reference,omitempty"`
	// 是否可以作为参考资料下载
	DownloadAsRef ai.DocFileDownloadAsRef `protobuf:"varint,22,opt,name=download_as_ref,json=downloadAsRef,proto3,enum=tanlive.ai.DocFileDownloadAsRef" json:"download_as_ref,omitempty"`
	ParseMode     ai.DocParseMode         `protobuf:"varint,23,opt,name=parse_mode,json=parseMode,proto3,enum=tanlive.ai.DocParseMode" json:"parse_mode,omitempty"`
	// 知识提示
	// 是否有超长标题表格
	HasOverSizedTables bool `protobuf:"varint,30,opt,name=has_over_sized_tables,json=hasOverSizedTables,proto3" json:"has_over_sized_tables,omitempty"`
	// 是否内容重复（租户内）
	HasRepeated bool `protobuf:"varint,31,opt,name=has_repeated,json=hasRepeated,proto3" json:"has_repeated,omitempty"`
	// 外部数据源信息
	DataSourceState uint32 `protobuf:"varint,32,opt,name=data_source_state,json=dataSourceState,proto3" json:"data_source_state,omitempty"`
	// 分享的团队
	SharedTeams []*DocShareTeamReceiver `protobuf:"bytes,24,rep,name=shared_teams,json=sharedTeams,proto3" json:"shared_teams,omitempty"`
	// 分享的用户
	SharedUsers []*DocShareUserReceiver `protobuf:"bytes,25,rep,name=shared_users,json=sharedUsers,proto3" json:"shared_users,omitempty"`
	// 是否收到分享
	ReceivedShare bool `protobuf:"varint,26,opt,name=received_share,json=receivedShare,proto3" json:"received_share,omitempty"`
}

func (x *CollectionTextFile) Reset() {
	*x = CollectionTextFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionTextFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionTextFile) ProtoMessage() {}

func (x *CollectionTextFile) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionTextFile.ProtoReflect.Descriptor instead.
func (*CollectionTextFile) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{9}
}

func (x *CollectionTextFile) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CollectionTextFile) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *CollectionTextFile) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *CollectionTextFile) GetAssistants() []*ai.Assistant {
	if x != nil {
		return x.Assistants
	}
	return nil
}

func (x *CollectionTextFile) GetState() ai.DocState {
	if x != nil {
		return x.State
	}
	return ai.DocState(0)
}

func (x *CollectionTextFile) GetContributor() []*ai.Contributor {
	if x != nil {
		return x.Contributor
	}
	return nil
}

func (x *CollectionTextFile) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CollectionTextFile) GetHitCount() uint32 {
	if x != nil {
		return x.HitCount
	}
	return 0
}

func (x *CollectionTextFile) GetUgcType() base.DataType {
	if x != nil {
		return x.UgcType
	}
	return base.DataType(0)
}

func (x *CollectionTextFile) GetUgcId() uint64 {
	if x != nil {
		return x.UgcId
	}
	return 0
}

func (x *CollectionTextFile) GetCreateBy() *DocOperator {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *CollectionTextFile) GetUpdateBy() *DocOperator {
	if x != nil {
		return x.UpdateBy
	}
	return nil
}

func (x *CollectionTextFile) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *CollectionTextFile) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *CollectionTextFile) GetVersionLag() uint64 {
	if x != nil {
		return x.VersionLag
	}
	return 0
}

func (x *CollectionTextFile) GetParseProgress() float32 {
	if x != nil {
		return x.ParseProgress
	}
	return 0
}

func (x *CollectionTextFile) GetShowContributor() uint32 {
	if x != nil {
		return x.ShowContributor
	}
	return 0
}

func (x *CollectionTextFile) GetStates() []*ai.DocAssistantState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *CollectionTextFile) GetSharedStates() []*ai.DocAssistantState {
	if x != nil {
		return x.SharedStates
	}
	return nil
}

func (x *CollectionTextFile) GetLabels() []*ai.CustomLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CollectionTextFile) GetReference() []*ai.DocReference {
	if x != nil {
		return x.Reference
	}
	return nil
}

func (x *CollectionTextFile) GetDownloadAsRef() ai.DocFileDownloadAsRef {
	if x != nil {
		return x.DownloadAsRef
	}
	return ai.DocFileDownloadAsRef(0)
}

func (x *CollectionTextFile) GetParseMode() ai.DocParseMode {
	if x != nil {
		return x.ParseMode
	}
	return ai.DocParseMode(0)
}

func (x *CollectionTextFile) GetHasOverSizedTables() bool {
	if x != nil {
		return x.HasOverSizedTables
	}
	return false
}

func (x *CollectionTextFile) GetHasRepeated() bool {
	if x != nil {
		return x.HasRepeated
	}
	return false
}

func (x *CollectionTextFile) GetDataSourceState() uint32 {
	if x != nil {
		return x.DataSourceState
	}
	return 0
}

func (x *CollectionTextFile) GetSharedTeams() []*DocShareTeamReceiver {
	if x != nil {
		return x.SharedTeams
	}
	return nil
}

func (x *CollectionTextFile) GetSharedUsers() []*DocShareUserReceiver {
	if x != nil {
		return x.SharedUsers
	}
	return nil
}

func (x *CollectionTextFile) GetReceivedShare() bool {
	if x != nil {
		return x.ReceivedShare
	}
	return false
}

type ExportField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key     string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Id      uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Example string `protobuf:"bytes,3,opt,name=example,proto3" json:"example,omitempty"`
	Label   string `protobuf:"bytes,4,opt,name=label,proto3" json:"label,omitempty"`
	Rule    string `protobuf:"bytes,5,opt,name=rule,proto3" json:"rule,omitempty"`
	Tips    string `protobuf:"bytes,6,opt,name=tips,proto3" json:"tips,omitempty"`
}

func (x *ExportField) Reset() {
	*x = ExportField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportField) ProtoMessage() {}

func (x *ExportField) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportField.ProtoReflect.Descriptor instead.
func (*ExportField) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{10}
}

func (x *ExportField) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ExportField) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExportField) GetExample() string {
	if x != nil {
		return x.Example
	}
	return ""
}

func (x *ExportField) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *ExportField) GetRule() string {
	if x != nil {
		return x.Rule
	}
	return ""
}

func (x *ExportField) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

// 反馈参考文献
type FeedbackReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文献类型
	Type ai.ReferenceType `protobuf:"varint,1,opt,name=type,proto3,enum=tanlive.ai.ReferenceType" json:"type,omitempty"`
	// 跳转链接（type为1时必填）
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// 文本（type为2时必填）
	Text string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	// 文件路径（type为3时必填）
	FilePath string `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	// 文件名称（type为3时必填）
	FileName string `protobuf:"bytes,5,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
}

func (x *FeedbackReference) Reset() {
	*x = FeedbackReference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedbackReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackReference) ProtoMessage() {}

func (x *FeedbackReference) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackReference.ProtoReflect.Descriptor instead.
func (*FeedbackReference) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{11}
}

func (x *FeedbackReference) GetType() ai.ReferenceType {
	if x != nil {
		return x.Type
	}
	return ai.ReferenceType(0)
}

func (x *FeedbackReference) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FeedbackReference) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *FeedbackReference) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *FeedbackReference) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

// 聊天分享信息
type ChatShare struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分享记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分享唯一标识
	ShareId string `protobuf:"bytes,2,opt,name=share_id,json=shareId,proto3" json:"share_id,omitempty"`
	// 原会话ID
	ChatId uint64 `protobuf:"varint,3,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`
	// 分享类型
	ShareType ShareType `protobuf:"varint,4,opt,name=share_type,json=shareType,proto3,enum=tanlive.bff_web.ai.ShareType" json:"share_type,omitempty"`
	// 分享状态
	ShareStatus ShareStatus `protobuf:"varint,5,opt,name=share_status,json=shareStatus,proto3,enum=tanlive.bff_web.ai.ShareStatus" json:"share_status,omitempty"`
	// 分享者ID
	SharedBy uint64 `protobuf:"varint,6,opt,name=shared_by,json=sharedBy,proto3" json:"shared_by,omitempty"`
	// 访问次数
	AccessCount int32 `protobuf:"varint,7,opt,name=access_count,json=accessCount,proto3" json:"access_count,omitempty"`
	// 分享创建时间
	ShareDate *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=share_date,json=shareDate,proto3" json:"share_date,omitempty"`
	// 分享过期时间
	ExpireDate *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=expire_date,json=expireDate,proto3" json:"expire_date,omitempty"`
	// 最后访问时间
	LastAccessTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=last_access_time,json=lastAccessTime,proto3" json:"last_access_time,omitempty"`
	// 助手ID
	AssistantId uint64 `protobuf:"varint,11,opt,name=assistant_id,json=assistantId,proto3" json:"assistant_id,omitempty"`
	// 分享链接
	ShareUrl string `protobuf:"bytes,12,opt,name=share_url,json=shareUrl,proto3" json:"share_url,omitempty"`
}

func (x *ChatShare) Reset() {
	*x = ChatShare{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatShare) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatShare) ProtoMessage() {}

func (x *ChatShare) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatShare.ProtoReflect.Descriptor instead.
func (*ChatShare) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{12}
}

func (x *ChatShare) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatShare) GetShareId() string {
	if x != nil {
		return x.ShareId
	}
	return ""
}

func (x *ChatShare) GetChatId() uint64 {
	if x != nil {
		return x.ChatId
	}
	return 0
}

func (x *ChatShare) GetShareType() ShareType {
	if x != nil {
		return x.ShareType
	}
	return ShareType_SHARE_TYPE_UNSPECIFIED
}

func (x *ChatShare) GetShareStatus() ShareStatus {
	if x != nil {
		return x.ShareStatus
	}
	return ShareStatus_SHARE_STATUS_UNSPECIFIED
}

func (x *ChatShare) GetSharedBy() uint64 {
	if x != nil {
		return x.SharedBy
	}
	return 0
}

func (x *ChatShare) GetAccessCount() int32 {
	if x != nil {
		return x.AccessCount
	}
	return 0
}

func (x *ChatShare) GetShareDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ShareDate
	}
	return nil
}

func (x *ChatShare) GetExpireDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireDate
	}
	return nil
}

func (x *ChatShare) GetLastAccessTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastAccessTime
	}
	return nil
}

func (x *ChatShare) GetAssistantId() uint64 {
	if x != nil {
		return x.AssistantId
	}
	return 0
}

func (x *ChatShare) GetShareUrl() string {
	if x != nil {
		return x.ShareUrl
	}
	return ""
}

// 分享访问记录
type ChatShareAccess struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分享ID
	ShareId string `protobuf:"bytes,2,opt,name=share_id,json=shareId,proto3" json:"share_id,omitempty"`
	// 访问者ID
	AccessBy uint64 `protobuf:"varint,3,opt,name=access_by,json=accessBy,proto3" json:"access_by,omitempty"`
	// 访问时间
	AccessDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=access_date,json=accessDate,proto3" json:"access_date,omitempty"`
	// 是否点击了"接着聊"
	IsContinued bool `protobuf:"varint,7,opt,name=is_continued,json=isContinued,proto3" json:"is_continued,omitempty"`
	// 新建的会话ID
	NewChatId uint64 `protobuf:"varint,8,opt,name=new_chat_id,json=newChatId,proto3" json:"new_chat_id,omitempty"`
}

func (x *ChatShareAccess) Reset() {
	*x = ChatShareAccess{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatShareAccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatShareAccess) ProtoMessage() {}

func (x *ChatShareAccess) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatShareAccess.ProtoReflect.Descriptor instead.
func (*ChatShareAccess) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{13}
}

func (x *ChatShareAccess) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatShareAccess) GetShareId() string {
	if x != nil {
		return x.ShareId
	}
	return ""
}

func (x *ChatShareAccess) GetAccessBy() uint64 {
	if x != nil {
		return x.AccessBy
	}
	return 0
}

func (x *ChatShareAccess) GetAccessDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AccessDate
	}
	return nil
}

func (x *ChatShareAccess) GetIsContinued() bool {
	if x != nil {
		return x.IsContinued
	}
	return false
}

func (x *ChatShareAccess) GetNewChatId() uint64 {
	if x != nil {
		return x.NewChatId
	}
	return 0
}

// HTTP请求信息
type HttpRequestInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 远程地址
	RemoteAddr string `protobuf:"bytes,1,opt,name=remote_addr,json=remoteAddr,proto3" json:"remote_addr,omitempty"`
	// User-Agent
	UserAgent string `protobuf:"bytes,2,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
}

func (x *HttpRequestInfo) Reset() {
	*x = HttpRequestInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HttpRequestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequestInfo) ProtoMessage() {}

func (x *HttpRequestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequestInfo.ProtoReflect.Descriptor instead.
func (*HttpRequestInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{14}
}

func (x *HttpRequestInfo) GetRemoteAddr() string {
	if x != nil {
		return x.RemoteAddr
	}
	return ""
}

func (x *HttpRequestInfo) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

type ExternalSourceUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	HashUserId string `protobuf:"bytes,1,opt,name=hash_user_id,json=hashUserId,proto3" json:"hash_user_id,omitempty"`
	// 用户昵称
	Nickname string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 用户头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 用户状态
	AuthState ai.ExternalSourceUserAuthState `protobuf:"varint,4,opt,name=auth_state,json=authState,proto3,enum=tanlive.ai.ExternalSourceUserAuthState" json:"auth_state,omitempty"`
	// 用户授权来源(xw qq)
	AuthSource string `protobuf:"bytes,5,opt,name=auth_source,json=authSource,proto3" json:"auth_source,omitempty"`
}

func (x *ExternalSourceUser) Reset() {
	*x = ExternalSourceUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalSourceUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalSourceUser) ProtoMessage() {}

func (x *ExternalSourceUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_ai_ai_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalSourceUser.ProtoReflect.Descriptor instead.
func (*ExternalSourceUser) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_ai_ai_proto_rawDescGZIP(), []int{15}
}

func (x *ExternalSourceUser) GetHashUserId() string {
	if x != nil {
		return x.HashUserId
	}
	return ""
}

func (x *ExternalSourceUser) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *ExternalSourceUser) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ExternalSourceUser) GetAuthState() ai.ExternalSourceUserAuthState {
	if x != nil {
		return x.AuthState
	}
	return ai.ExternalSourceUserAuthState(0)
}

func (x *ExternalSourceUser) GetAuthSource() string {
	if x != nil {
		return x.AuthSource
	}
	return ""
}

var File_tanlive_bff_web_ai_ai_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_ai_ai_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x61, 0x69, 0x2f, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x61,
	0x69, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x61, 0x69, 0x2f, 0x61,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75,
	0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x69, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x01, 0x0a, 0x04, 0x43, 0x68, 0x61, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x22, 0x90, 0x06, 0x0a,
	0x08, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x32, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a,
	0x09, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x68,
	0x61, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x63, 0x68,
	0x61, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3a, 0x0a, 0x0c,
	0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e,
	0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x0b, 0x72, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f,
	0x68, 0x69, 0x74, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x48,
	0x69, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x76, 0x67, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61, 0x76, 0x67, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0f, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x22,
	0xf2, 0x02, 0x0a, 0x0a, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x44, 0x6f, 0x63, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69,
	0x6c, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x42,
	0x72, 0x6f, 0x77, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c,
	0x65, 0x55, 0x72, 0x6c, 0x22, 0xb2, 0x02, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x12,
	0x23, 0x0a, 0x0d, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64,
	0x65, 0x62, 0x75, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x44, 0x65, 0x62, 0x75, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0a, 0x74, 0x65, 0x78, 0x74, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x29, 0x0a, 0x11, 0x74,
	0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x6f, 0x70, 0x5f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x63, 0x61,
	0x6c, 0x6c, 0x54, 0x6f, 0x70, 0x4e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x5f,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x63, 0x6c,
	0x65, 0x61, 0x6e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x22, 0xd2, 0x04, 0x0a, 0x0a, 0x43, 0x68,
	0x61, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x38,
	0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b,
	0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x66,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x63, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x68, 0x61,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x63, 0x68, 0x61,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x38, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e,
	0x43, 0x68, 0x61, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x53,
	0x0a, 0x14, 0x44, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x72, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d,
	0x65, 0x45, 0x6e, 0x22, 0x3a, 0x0a, 0x14, 0x44, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x72, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x88, 0x09, 0x0a, 0x0c, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x41,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x52,
	0x0a, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x41, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f,
	0x72, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x36, 0x0a,
	0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f,
	0x63, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x68, 0x69, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79,
	0x12, 0x3c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x67, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f,
	0x63, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x4b, 0x0a,
	0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x0f, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x52, 0x0b, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x4b, 0x0a, 0x0c, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x72, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x68, 0x6f, 0x77, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44,
	0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x42, 0x0a, 0x0e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e,
	0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x6e, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x61, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f,
	0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x64, 0x53, 0x68, 0x61, 0x72, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x0b, 0x44,
	0x6f, 0x63, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xff, 0x0a, 0x0a,
	0x12, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x52,
	0x0a, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x68, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x31, 0x0a, 0x08, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x75, 0x67, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52,
	0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3c, 0x0a, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x61,
	0x69, 0x2e, 0x44, 0x6f, 0x63, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x67,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c,
	0x61, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x73,
	0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x68, 0x6f,
	0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x12,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61,
	0x69, 0x2e, 0x44, 0x6f, 0x63, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0d, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x13, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e,
	0x44, 0x6f, 0x63, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12,
	0x2f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x12, 0x36, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x15, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69,
	0x2e, 0x44, 0x6f, 0x63, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44,
	0x6f, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x73,
	0x52, 0x65, 0x66, 0x52, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x73, 0x52,
	0x65, 0x66, 0x12, 0x37, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x50, 0x61, 0x72, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x09, 0x70, 0x61, 0x72, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x68,
	0x61, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x68, 0x61, 0x73, 0x4f,
	0x76, 0x65, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x61, 0x73, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x64, 0x61,
	0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x4b, 0x0a,
	0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x18, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x52, 0x0b, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x4b, 0x0a, 0x0c, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x72, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x64, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x53, 0x68, 0x61, 0x72, 0x65, 0x22, 0x87,
	0x01, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x72, 0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x70, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x70, 0x73, 0x22, 0x9e, 0x02, 0x0a, 0x11, 0x46, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x3b,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0x82, 0x88, 0x27, 0x20, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x74, 0x79, 0x70, 0x65, 0x20, 0x31,
	0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x75, 0x72, 0x6c, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x16, 0x82, 0x88, 0x27, 0x12, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x69, 0x66, 0x3d, 0x74, 0x79, 0x70, 0x65, 0x20, 0x32, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x33, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x16, 0x82, 0x88, 0x27, 0x12, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x69, 0x66, 0x3d, 0x74, 0x79, 0x70, 0x65, 0x20, 0x33, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x82, 0x88, 0x27, 0x12, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x74, 0x79, 0x70, 0x65, 0x20, 0x33, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x8f, 0x04, 0x0a, 0x09, 0x43, 0x68,
	0x61, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x68, 0x61, 0x72, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x68, 0x61, 0x72, 0x65,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65,
	0x62, 0x2e, 0x61, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09,
	0x73, 0x68, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65,
	0x62, 0x2e, 0x61, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x42, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73,
	0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x68, 0x61, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x22, 0xd9, 0x01, 0x0a, 0x0f,
	0x43, 0x68, 0x61, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x68, 0x61, 0x72, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x69,
	0x6e, 0x75, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x43, 0x6f,
	0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x5f, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6e, 0x65,
	0x77, 0x43, 0x68, 0x61, 0x74, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x0f, 0x48, 0x74, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x22, 0xd3, 0x01, 0x0a, 0x12, 0x45,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x61, 0x73, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x46, 0x0a, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x61, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2a, 0xb9, 0x01, 0x0a, 0x0f, 0x41, 0x69, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x6f,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x49, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41,
	0x47, 0x45, 0x5f, 0x44, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x49, 0x5f, 0x4d,
	0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52,
	0x4e, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x49, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47,
	0x45, 0x5f, 0x44, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x10, 0x02, 0x12, 0x2e, 0x0a, 0x2a,
	0x41, 0x49, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x4f, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x52,
	0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x10, 0x03, 0x2a, 0x56, 0x0a, 0x15,
	0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x50, 0x43, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x35, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x10, 0x01, 0x12,
	0x13, 0x0a, 0x0f, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x35, 0x55,
	0x72, 0x6c, 0x10, 0x02, 0x2a, 0x71, 0x0a, 0x09, 0x53, 0x68, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x4b,
	0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x51, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x48,
	0x41, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x03, 0x2a, 0x79, 0x0a, 0x0b, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x19, 0x0a,
	0x15, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x48, 0x41, 0x52,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x03, 0x42, 0x3c, 0x5a, 0x3a, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e,
	0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x69,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_web_ai_ai_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_ai_ai_proto_rawDescData = file_tanlive_bff_web_ai_ai_proto_rawDesc
)

func file_tanlive_bff_web_ai_ai_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_ai_ai_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_ai_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_ai_ai_proto_rawDescData)
	})
	return file_tanlive_bff_web_ai_ai_proto_rawDescData
}

var file_tanlive_bff_web_ai_ai_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_tanlive_bff_web_ai_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_tanlive_bff_web_ai_ai_proto_goTypes = []interface{}{
	(AiMessageDoType)(0),                // 0: tanlive.bff_web.ai.AiMessageDoType
	(MiniProgramSourceType)(0),          // 1: tanlive.bff_web.ai.MiniProgramSourceType
	(ShareType)(0),                      // 2: tanlive.bff_web.ai.ShareType
	(ShareStatus)(0),                    // 3: tanlive.bff_web.ai.ShareStatus
	(*Chat)(nil),                        // 4: tanlive.bff_web.ai.Chat
	(*ChatInfo)(nil),                    // 5: tanlive.bff_web.ai.ChatInfo
	(*TencentDoc)(nil),                  // 6: tanlive.bff_web.ai.TencentDoc
	(*Assistant)(nil),                   // 7: tanlive.bff_web.ai.Assistant
	(*ChatDetail)(nil),                  // 8: tanlive.bff_web.ai.ChatDetail
	(*DocShareTeamReceiver)(nil),        // 9: tanlive.bff_web.ai.DocShareTeamReceiver
	(*DocShareUserReceiver)(nil),        // 10: tanlive.bff_web.ai.DocShareUserReceiver
	(*CollectionQA)(nil),                // 11: tanlive.bff_web.ai.CollectionQA
	(*DocOperator)(nil),                 // 12: tanlive.bff_web.ai.DocOperator
	(*CollectionTextFile)(nil),          // 13: tanlive.bff_web.ai.CollectionTextFile
	(*ExportField)(nil),                 // 14: tanlive.bff_web.ai.ExportField
	(*FeedbackReference)(nil),           // 15: tanlive.bff_web.ai.FeedbackReference
	(*ChatShare)(nil),                   // 16: tanlive.bff_web.ai.ChatShare
	(*ChatShareAccess)(nil),             // 17: tanlive.bff_web.ai.ChatShareAccess
	(*HttpRequestInfo)(nil),             // 18: tanlive.bff_web.ai.HttpRequestInfo
	(*ExternalSourceUser)(nil),          // 19: tanlive.bff_web.ai.ExternalSourceUser
	(*timestamppb.Timestamp)(nil),       // 20: google.protobuf.Timestamp
	(*iam.UserInfo)(nil),                // 21: tanlive.iam.UserInfo
	(ai.ChatType)(0),                    // 22: tanlive.ai.ChatType
	(ai.ChatCurrentState)(0),            // 23: tanlive.ai.ChatCurrentState
	(ai.ChatSupportType)(0),             // 24: tanlive.ai.ChatSupportType
	(*ai.CustomLabel)(nil),              // 25: tanlive.ai.CustomLabel
	(ai.RatingScale)(0),                 // 26: tanlive.ai.RatingScale
	(*ai.EventChatMessage)(nil),         // 27: tanlive.ai.EventChatMessage
	(*ai.ChatSendRecordInfo)(nil),       // 28: tanlive.ai.ChatSendRecordInfo
	(*ai.Assistant)(nil),                // 29: tanlive.ai.Assistant
	(*ai.DocAssistantState)(nil),        // 30: tanlive.ai.DocAssistantState
	(*ai.Contributor)(nil),              // 31: tanlive.ai.Contributor
	(*ai.DocReference)(nil),             // 32: tanlive.ai.DocReference
	(ai.DocState)(0),                    // 33: tanlive.ai.DocState
	(ai.DocMatchPattern)(0),             // 34: tanlive.ai.DocMatchPattern
	(base.IdentityType)(0),              // 35: tanlive.base.IdentityType
	(base.DataType)(0),                  // 36: tanlive.base.DataType
	(ai.DocFileDownloadAsRef)(0),        // 37: tanlive.ai.DocFileDownloadAsRef
	(ai.DocParseMode)(0),                // 38: tanlive.ai.DocParseMode
	(ai.ReferenceType)(0),               // 39: tanlive.ai.ReferenceType
	(ai.ExternalSourceUserAuthState)(0), // 40: tanlive.ai.ExternalSourceUserAuthState
}
var file_tanlive_bff_web_ai_ai_proto_depIdxs = []int32{
	20, // 0: tanlive.bff_web.ai.Chat.create_date:type_name -> google.protobuf.Timestamp
	21, // 1: tanlive.bff_web.ai.ChatInfo.create_by:type_name -> tanlive.iam.UserInfo
	20, // 2: tanlive.bff_web.ai.ChatInfo.update_date:type_name -> google.protobuf.Timestamp
	20, // 3: tanlive.bff_web.ai.ChatInfo.create_date:type_name -> google.protobuf.Timestamp
	22, // 4: tanlive.bff_web.ai.ChatInfo.chat_type:type_name -> tanlive.ai.ChatType
	23, // 5: tanlive.bff_web.ai.ChatInfo.chat_state:type_name -> tanlive.ai.ChatCurrentState
	24, // 6: tanlive.bff_web.ai.ChatInfo.support_type:type_name -> tanlive.ai.ChatSupportType
	25, // 7: tanlive.bff_web.ai.ChatInfo.labels:type_name -> tanlive.ai.CustomLabel
	26, // 8: tanlive.bff_web.ai.ChatInfo.rating_scale:type_name -> tanlive.ai.RatingScale
	27, // 9: tanlive.bff_web.ai.ChatDetail.messages:type_name -> tanlive.ai.EventChatMessage
	21, // 10: tanlive.bff_web.ai.ChatDetail.create_by:type_name -> tanlive.iam.UserInfo
	20, // 11: tanlive.bff_web.ai.ChatDetail.finish_date:type_name -> google.protobuf.Timestamp
	20, // 12: tanlive.bff_web.ai.ChatDetail.create_date:type_name -> google.protobuf.Timestamp
	22, // 13: tanlive.bff_web.ai.ChatDetail.chat_type:type_name -> tanlive.ai.ChatType
	23, // 14: tanlive.bff_web.ai.ChatDetail.chat_state:type_name -> tanlive.ai.ChatCurrentState
	24, // 15: tanlive.bff_web.ai.ChatDetail.support_type:type_name -> tanlive.ai.ChatSupportType
	28, // 16: tanlive.bff_web.ai.ChatDetail.records:type_name -> tanlive.ai.ChatSendRecordInfo
	29, // 17: tanlive.bff_web.ai.CollectionQA.assistants:type_name -> tanlive.ai.Assistant
	30, // 18: tanlive.bff_web.ai.CollectionQA.states:type_name -> tanlive.ai.DocAssistantState
	31, // 19: tanlive.bff_web.ai.CollectionQA.contributor:type_name -> tanlive.ai.Contributor
	32, // 20: tanlive.bff_web.ai.CollectionQA.reference:type_name -> tanlive.ai.DocReference
	12, // 21: tanlive.bff_web.ai.CollectionQA.create_by:type_name -> tanlive.bff_web.ai.DocOperator
	12, // 22: tanlive.bff_web.ai.CollectionQA.update_by:type_name -> tanlive.bff_web.ai.DocOperator
	20, // 23: tanlive.bff_web.ai.CollectionQA.update_date:type_name -> google.protobuf.Timestamp
	20, // 24: tanlive.bff_web.ai.CollectionQA.create_date:type_name -> google.protobuf.Timestamp
	30, // 25: tanlive.bff_web.ai.CollectionQA.shared_states:type_name -> tanlive.ai.DocAssistantState
	9,  // 26: tanlive.bff_web.ai.CollectionQA.shared_teams:type_name -> tanlive.bff_web.ai.DocShareTeamReceiver
	10, // 27: tanlive.bff_web.ai.CollectionQA.shared_users:type_name -> tanlive.bff_web.ai.DocShareUserReceiver
	33, // 28: tanlive.bff_web.ai.CollectionQA.state:type_name -> tanlive.ai.DocState
	25, // 29: tanlive.bff_web.ai.CollectionQA.labels:type_name -> tanlive.ai.CustomLabel
	34, // 30: tanlive.bff_web.ai.CollectionQA.match_patterns:type_name -> tanlive.ai.DocMatchPattern
	35, // 31: tanlive.bff_web.ai.DocOperator.type:type_name -> tanlive.base.IdentityType
	29, // 32: tanlive.bff_web.ai.CollectionTextFile.assistants:type_name -> tanlive.ai.Assistant
	33, // 33: tanlive.bff_web.ai.CollectionTextFile.state:type_name -> tanlive.ai.DocState
	31, // 34: tanlive.bff_web.ai.CollectionTextFile.contributor:type_name -> tanlive.ai.Contributor
	36, // 35: tanlive.bff_web.ai.CollectionTextFile.ugc_type:type_name -> tanlive.base.DataType
	12, // 36: tanlive.bff_web.ai.CollectionTextFile.create_by:type_name -> tanlive.bff_web.ai.DocOperator
	12, // 37: tanlive.bff_web.ai.CollectionTextFile.update_by:type_name -> tanlive.bff_web.ai.DocOperator
	20, // 38: tanlive.bff_web.ai.CollectionTextFile.update_date:type_name -> google.protobuf.Timestamp
	20, // 39: tanlive.bff_web.ai.CollectionTextFile.create_date:type_name -> google.protobuf.Timestamp
	30, // 40: tanlive.bff_web.ai.CollectionTextFile.states:type_name -> tanlive.ai.DocAssistantState
	30, // 41: tanlive.bff_web.ai.CollectionTextFile.shared_states:type_name -> tanlive.ai.DocAssistantState
	25, // 42: tanlive.bff_web.ai.CollectionTextFile.labels:type_name -> tanlive.ai.CustomLabel
	32, // 43: tanlive.bff_web.ai.CollectionTextFile.reference:type_name -> tanlive.ai.DocReference
	37, // 44: tanlive.bff_web.ai.CollectionTextFile.download_as_ref:type_name -> tanlive.ai.DocFileDownloadAsRef
	38, // 45: tanlive.bff_web.ai.CollectionTextFile.parse_mode:type_name -> tanlive.ai.DocParseMode
	9,  // 46: tanlive.bff_web.ai.CollectionTextFile.shared_teams:type_name -> tanlive.bff_web.ai.DocShareTeamReceiver
	10, // 47: tanlive.bff_web.ai.CollectionTextFile.shared_users:type_name -> tanlive.bff_web.ai.DocShareUserReceiver
	39, // 48: tanlive.bff_web.ai.FeedbackReference.type:type_name -> tanlive.ai.ReferenceType
	2,  // 49: tanlive.bff_web.ai.ChatShare.share_type:type_name -> tanlive.bff_web.ai.ShareType
	3,  // 50: tanlive.bff_web.ai.ChatShare.share_status:type_name -> tanlive.bff_web.ai.ShareStatus
	20, // 51: tanlive.bff_web.ai.ChatShare.share_date:type_name -> google.protobuf.Timestamp
	20, // 52: tanlive.bff_web.ai.ChatShare.expire_date:type_name -> google.protobuf.Timestamp
	20, // 53: tanlive.bff_web.ai.ChatShare.last_access_time:type_name -> google.protobuf.Timestamp
	20, // 54: tanlive.bff_web.ai.ChatShareAccess.access_date:type_name -> google.protobuf.Timestamp
	40, // 55: tanlive.bff_web.ai.ExternalSourceUser.auth_state:type_name -> tanlive.ai.ExternalSourceUserAuthState
	56, // [56:56] is the sub-list for method output_type
	56, // [56:56] is the sub-list for method input_type
	56, // [56:56] is the sub-list for extension type_name
	56, // [56:56] is the sub-list for extension extendee
	0,  // [0:56] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_ai_ai_proto_init() }
func file_tanlive_bff_web_ai_ai_proto_init() {
	if File_tanlive_bff_web_ai_ai_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_ai_ai_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Chat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TencentDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Assistant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocShareTeamReceiver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocShareUserReceiver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionQA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocOperator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionTextFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedbackReference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatShare); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatShareAccess); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HttpRequestInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_ai_ai_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExternalSourceUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_ai_ai_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_bff_web_ai_ai_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_ai_ai_proto_depIdxs,
		EnumInfos:         file_tanlive_bff_web_ai_ai_proto_enumTypes,
		MessageInfos:      file_tanlive_bff_web_ai_ai_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_ai_ai_proto = out.File
	file_tanlive_bff_web_ai_ai_proto_rawDesc = nil
	file_tanlive_bff_web_ai_ai_proto_goTypes = nil
	file_tanlive_bff_web_ai_ai_proto_depIdxs = nil
}
