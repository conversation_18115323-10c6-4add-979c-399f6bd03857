syntax = "proto3";

package tanlive.bff_web.ai;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/ai";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "tanlive/ai/service.proto";
import "tanlive/bff-web/ai/ai.proto";
import "tanlive/ai/ai.proto";
import "tanlive/options.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/iam/iam.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "tanlive/errors/ai.proto";
import "tanlive/cms/cms.proto";
import "tanlive/team/team.proto";

message ReqChatResendSubscribe {
  string chat_id = 1;
  // 问题
  string text = 2;
  // 助手id
  string assistant_id = 3;
  // 图片
  repeated string image_urls = 4;
  // 服务端是否自动断开
  bool auto_disconnect = 5;
  // 问题id
  string question_id = 6;
  // duration
  int32 duration = 7;
}

message ReqReceiveChatMessage {
  uint64 chat_id = 1;
  // 问题
  string text = 2;
  // 助手id
  uint64 assistant_id = 3;
  // 图片
  repeated string image_urls = 4;
  // 是否小程序
  bool mini_program = 5;
  // hash id
  string publish_hash_id = 6;
  // 重新回答的问题id
  uint64 question_id = 7;
  // 是否返回answer
  bool  show_answer = 8;
  // 语言 zh en
  string lang = 9;
}

message RspReceiveChatMessage {
  // 问题的id
  uint64 message_id = 1;
  uint64 answer_id = 2;
  tanlive.ai.ChatMessage answer = 3;
}

message ReqCreateChat{
  // 会话标题
  string  title = 1;
  // 助手id
  uint64 assistant_id = 2;
  // 图片
  repeated string image_urls = 3;
  // 是否小程序
  bool mini_program = 5;
  // hash_id
  string publish_hash_id = 6;
  // 是否返回answer
  bool  show_answer = 7;
  // 语言 zh en
  string lang = 8;
}

message RspCreateChat{
  // 会话id
  uint64 chat_id = 1;
  // 第一个问题的id
  uint64 message_id = 2;
  uint64 answer_id = 3;
  tanlive.ai.ChatMessage answer = 4;
}


message ReqDescribeChatMessages{
  uint64 chat_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  bool without_docs = 4;
  uint64 question_id = 5;
}

message RspDescribeChatMessages{
  repeated tanlive.ai.EventChatMessage chat_messages = 1;
  uint32 total_count = 2;
}

message RspDescribeChats{
  repeated Chat chats = 1;
  uint32 total_count = 2;
}

message ReqDescribeChats{
  uint32 offset = 1;
  uint32 limit = 2;
  // 助手id
  uint64 assistant_id = 3;
}

message ReqDeleteChat {
  uint64 chat_id = 2;
}

message ReqRateAiAnswer {
  // 消息ID
  uint64 message_id = 1 [(validator) = "required"];
  // 评价等级
  tanlive.ai.RatingScale rating_scale = 2 [(validator) = "required"];
}

message ReqCreateFeedback {
  // 助手ID
  uint64 assistant_id = 1 [(validator) = "required"];
  // 问题
  string question = 2 [(validator) = "required"];
  // 答案
  string answer = 3 [(validator) = "required"];
  // 参考文献
  repeated FeedbackReference references = 4 [(validator) = "omitempty,dive"];
}

message RspCreateFeedback {
  // 反馈ID
  uint64 feedback_id = 1;
}

message ReqSaveUserFeedbackByQuestion {
  // 答案ID
  uint64 answer_id = 1 [(validator) = "required"];
  // 答案
  string answer = 2 [(validator) = "required"];
  // 参考文献
  repeated FeedbackReference references = 3 [(validator) = "omitempty,dive"];
}

message ReqSaveOpFeedback {
  // 答案ID
  uint64 answer_id = 1 [(validator) = "required_without=FeedbackId"];
  // AI回答评价
  tanlive.ai.FeedbackAnswerRating answer_rating = 2 [(validator) = "required"];
  // 是否命中预期知识
  bool hit_expected_doc = 3;
  // 预期命中的知识
  repeated uint64 doc_id = 4 [(validator) = "omitempty,dive,required"];
  // 分析备注
  tanlive.ai.FeedbackComment op_comment = 5;
  // 答案ID
  uint64 feedback_id = 6 [(validator) = "required_without=AnswerId"];
}

message ReqResendChatMessage {
  // 问题的id
  uint64 message_id = 1;
  // 助手id
  uint64 assistant_id = 2;
  // hash id
  string publish_hash_id = 3;
}

message ReqStopQuestionReply{
  uint64 id = 1;
  string hash_id = 2;
  uint64 question_id = 3;
}

message RspStopQuestionReply{
  tanlive.ai.ChatMessage message = 1;
}

message ReqDeleteCustomLabels{
  repeated uint64 ids = 1;
}

message ReqModifyCustomLabels{
  repeated tanlive.ai.CustomLabel labels = 1;
  tanlive.ai.CustomLabelObjectType object_type = 3 [(validator) = "oneof=1 2 3 4 5"];
}

message RspModifyCustomLabels{
}

message ReqListCustomLabel{
  uint32 offset = 1;
  uint32 limit = 2;
  tanlive.ai.CustomLabelObjectType object_type = 3[(validator) = "oneof=1 2 3 4 5"];
  repeated uint64 id = 4;
}

message RspListCustomLabel{
  repeated tanlive.ai.CustomLabel labels = 1;
  uint32 total_count = 2;
}

message ReqListAssistant{
  uint32 offset = 1;
  uint32 limit = 2;
  tanlive.ai.ChatType type = 3;
  // 搜索名称关键词
  string name = 4;
  // 语言，为空会从header里取
  string language = 5 [(validator) = "omitempty,oneof=zh en"];
}

message RspListAssistant{
  uint32 total_count = 1;
  repeated tanlive.ai.Assistant assistants = 2;
}

message ReqSearchChatUsers {
  // 分页偏移量
  uint32 offset = 1;
  // 分页大小
  uint32 limit = 2;
  // 助手id
  repeated uint64 assistant_ids = 3 [(validator) = "omitempty,dive,required"];
  // 搜索关键词
  string keyword = 4;
  // 地区
  base.Region region = 5 [(validator) = "required"];
  repeated uint64 user_ids = 6 ;
}

message RspSearchChatUsers {
  // 用户列表
  repeated tanlive.iam.UserInfo users = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqListChat {
  message Filter {
    // 用户id
    repeated uint64 user_ids = 1;
    // 对话内容
    repeated string chat_titles = 2;
    // 地区
    base.Region region = 3 [(validator) = "required"];
    tanlive.ai.ChatType chat_type = 4;
    repeated string nicknames = 5;
    repeated tanlive.ai.RatingScale rating_scale = 6;
    // 筛选审核 1 违规 2 敏感 3 正常
    uint32 reject_job_result = 7;
    // 国家或地区编码
    repeated string region_codes = 8;
    // 是否转过人工服务 1 否 2 是
    int32 is_manual = 9;
  }
  uint32 offset = 1;
  uint32 limit = 2;
  repeated string order_by = 3;
  Filter filter = 4 ;
  // 创建时间区间
  tanlive.base.TimeRange create_date_range = 5;
  // 处理时间区间
  tanlive.base.TimeRange update_date_range = 6;
  // 自定义标签kv对
  repeated tanlive.ai.LabelFilter labels = 7;
  // 助手id
  repeated uint64 assistant_ids = 8 ;
  // 自定义标签排序，只能当个标签排序
  tanlive.ai.OrderByLabel order_by_label = 15;
  // 筛选ids
  repeated uint64 ids = 12;
}

message RspListChat {
  repeated ChatInfo chats = 1;
  uint32 total_count = 2;
}

message ReqGetChatDetail {
  // chat_id
  uint64 id = 1 [(validator) = "min=1"];
  uint32 offset = 2;
  uint32 limit = 3;
  // 消息内容搜索关键词
  string keyword = 4;
  // 创建时间区间
  tanlive.base.TimeRange send_range = 5;
  // 问题ID
  uint64 question_id = 6;
}

message RspGetChatDetail {
  ChatDetail chat_detail = 1;
  uint32 totalCount = 2;
}

message ReqGetChatMessageDetail{
  uint64 id = 1 [(validator) = "required"];
}

message SearchCollectionItem{
  string text = 1;
  string question = 2;
  float score = 3;
  string file_name = 4;
  string url = 5;
  repeated tanlive.ai.Contributor contributor = 6;
  DocOperator  update_by = 10;
  string id = 11;
  // 召回类型
  tanlive.ai.SearchCollectionType type = 12;
  // 是否相关
  bool is_related = 13;
  // 文件类型
  tanlive.ai.DocType doc_type = 14;
  tanlive.ai.DocDataSource data_source = 15;
  uint64 doc_id = 16;
  string doc_name = 17;
}

message MessageConfig {
  bool chat_or_sql = 1;
  int32 history_rounds = 2;
  string prompt_prefix = 3;
  float threshold = 5;
  string search_engine = 6;
  int32 search_top_n = 7;
  int32 doc_top_n = 8;
  string sql_model = 10;
}

message RspGetChatMessageDetail{
  tanlive.ai.EventChatHashMessage message = 1;
  repeated SearchCollectionItem collection_items = 2;
  repeated tanlive.ai.ChatMessageLog logs = 3;
  base.TimeRange collection_time = 4;
  bool clean_chunks = 5;
}

message ReqListQA{
  repeated tanlive.ai.DocSharedState shared_state = 1;
  tanlive.ai.DocState state = 2;
  repeated tanlive.ai.Contributor contributor = 3;
  repeated tanlive.ai.Operator update_by = 4;
  repeated base.OrderBy order_by = 5;
  uint32 offset = 6;
  uint32 limit = 7 [(validator) = "max=200"];
  string search = 8;
  // 助手id
  repeated uint64 assistant_id = 9;
  // 不在助手中
  repeated uint64 excluded_assistant_id = 10;
  // 重复 doc 紧凑排序
  bool group_repeated = 11;
  uint32 show_contributor = 12;
  // 分享的助手
  repeated uint64 share_assistant_id = 13;
  repeated tanlive.ai.LabelFilter labels = 14;
  // 自定义标签排序，只能当个标签排序
  tanlive.ai.OrderByLabel order_by_label = 15;
  repeated uint64 ids = 16;
  // TQL表达式（高级搜索）
  string tql_expression = 31;
  repeated tanlive.ai.Operator create_by = 32;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 33;

  // 知识提示过滤条件，用来筛选问题超长等问题的记录
  message TipFilter {
    bool warning = 1;
  }
  TipFilter tip_filter = 34;
  // 是否返回知识提示
  bool with_tips = 35;
  // 分享的团队id
  repeated uint64 share_team_id = 36;
  // 分享的用户id
  repeated uint64 share_user_id = 37;
}

message RspListQA{
  uint32 total_count = 1;
  repeated CollectionQA items = 2;
}

message ReqCreateAssistantShare{
  repeated uint64 assistant_id = 1;
  uint64 doc_id = 2;
  repeated uint64 doc_ids = 3;
  uint64 query_id = 4;
  // 分享给个人的ID列表
  repeated uint64 user_id = 5;
  // 分享给团队的ID列表
  repeated uint64 team_id = 6;
}

message RspCreateAssistantShare{
  bool async = 1;
}

message ReqCreateDocShareConfigSender{
  repeated uint64 share_assistant_id = 2;
  repeated uint64 share_user_id = 3;
  repeated uint64 share_team_id = 4;
}

message RspCreateDocShareConfigSender{}

message ReqListeDocShareConfigSender{
  // 搜索名称关键词
  string name = 1;
  // 语言，为空会从header里取
  string language = 2 [(validator) = "omitempty,oneof=zh en"];
}

message RspListeDocShareConfigSender{
  message SharedAssistant{
    uint64 id = 1;
    string name = 2;
    string name_en = 3;
    bool is_selected = 4;
  }
  message SharedUserTeam{
    uint64 id = 1;
    string name = 2;
    bool is_selected = 3;
  }

  repeated SharedAssistant assistants = 1;
  repeated SharedUserTeam users = 2;
  repeated SharedUserTeam teams = 3;
}

message ReqListMyAssistantIds{
}

message RspListMyAssistantIds{
  repeated uint64 share_assistant_ids = 1;
}

message ReqListAssistantCanShareDoc{
  uint64 doc_id = 1;
  // 搜索名称关键词
  string name = 2;
  // 语言，为空会从header里取
  string language = 3 [(validator) = "omitempty,oneof=zh en"];
}

message RspListAssistantCanShareDoc{
  message SharedAssistant{
    uint64 id = 1;
    string name = 2;
    string name_en = 3;
    bool is_selected = 4;
  }

  repeated SharedAssistant assistants = 1;
}

message ReqCreateDocShareConfigReceiverAssistant{

  message UserShare {
    repeated uint64 user_id = 1;
    repeated uint64 team_id = 2;
    tanlive.ai.DocShareState state = 3;
  }

  uint64 assistant_id = 1 [(validator) = "required"];
  tanlive.ai.DocShareAcceptState receiver_state = 2 ;
  repeated UserShare user_shares = 3;
  tanlive.ai.DocShareState  other_state = 4 ;

}

message RspCreateDocShareConfigReceiverAssistant{}

message ReqListDocShareConfigReceiverAssistant{
  uint64 assistant_id = 1;
}

message RspListDocShareConfigReceiverAssistant{
  message Members{
    uint64 id = 1;
    string name = 2;
  }
  message UserShare {
    repeated Members users = 1;
    repeated Members teams = 2;
    tanlive.ai.DocShareState state = 3;
    uint64 group_id = 5;
  }

  uint64 assistant_id = 1;
  tanlive.ai.DocShareAcceptState receiver_state = 2;
  repeated UserShare user_shares = 3;
  tanlive.ai.DocShareState  other_state = 4;
}

message ReqCreateDocShareConfigReceiverUserTeam{
  repeated uint64 team_id = 1;
  repeated uint64 user_id = 2;
}

message RspCreateDocShareConfigReceiverUserTeam{}

message ReqListDocShareConfigReceiverUserTeam{
}

message RspListDocShareConfigReceiverUserTeam{
  message Members{
    uint64 id = 1;
    string name = 2;
  }
  repeated Members teams = 1;
  repeated Members users = 2;
}

message ReqGetMiniProgramAuth{
  string auth_id = 1;
  // pc、h5来源
  string ref_auth_id = 2;
}

message RspGetMiniProgramAuth{
  bool has_team = 1;
  bool has_doc_auth = 2;
  bool has_assistant = 3;
  // 是否还在绑定微信中
  bool has_bind_wx = 4;
  string uin_token = 5;
  bool has_doc_auth_read = 6;
  bool has_doc_auth_write = 7;
}

message ReqGetMiniProgramAssistantLimit{
  uint64 assistant_id = 1 [(validator) = "required"];
}

message RspGetMiniProgramAssistantLimit{
  // 如果限制，需要检测手机号，true 空手机， false 不空；
  bool empty_phone = 1;
  // 是否可以进入 true 可， false 不可
  bool can_in = 2;
}

message ReqBatchUserAssistantLimit{
  uint64 assistant_id = 1;
  repeated string token = 2;
}

message RspBatchUserAssistantLimit{
  message UserLimit{
    string token = 1;
    // 如果限制，需要检测手机号，true 空手机， false 不空；
    bool empty_phone = 2;
    // 是否可以进入 true 可， false 不可
    bool can_in = 3;
  }

  repeated UserLimit user_limits = 1;
}

message ReqBindUserPhoneByCode{
  string code = 1 [(validator) = "required"];
  uint64 assistant_id = 2;
}

message RspBindUserPhoneByCode{
  bool can_in = 1;
  bool bind_other_account = 2;
  bool is_have_phone = 3;
  string user_name = 4;
}

message ReqBindMiniProgramUniID{
  string uin_token = 1;
}

message ReqGetWebViewToMiniProgramToken{
}

message ReqBindOnceUserMiniProgram{
  string code = 1;
}

message RspBindOnceUserMiniProgram{
  string token = 1;
}

message ReqBindMiniProgramPhoneAccount{
  string code = 1 [(validator) = "required"];
}

message RspBindMiniProgramPhoneAccount{
  string token = 1;
  bool is_have_phone = 2;
  string user_name = 3;
  string phone = 4;
}

message ReqBindMiniProgramNormalAccount{
  string token = 1 [(validator) = "required"];
  string webview_uni_token = 2;
}

message RspBindMiniProgramNormalAccount{
  string token = 1;
}

message ReqGetAccountUnionIdStatus{
  string code = 1;
}

message RspGetAccountUnionIdStatus{
  bool bind_account_status = 1;
}

message ReqGetMiniProgramUserInfo{
  string select_token = 1;
}

message RspGetMiniProgramUserInfo{
  string token = 1;
}

message ReqGetAccountOnceBindStatus{
  string code = 1;
}

message RspGetAccountOnceBindStatus{
  bool has_once_bind = 1;
}

message RspGetWebViewToMiniProgramToken{
  string token = 1;
  string webview_uni_token = 2;
}

message ReqBindUnitokenByCode{
  string code = 1;
}

message ReqGetMiniProgramLoginURL{
  // 0 pc 1 h5
  uint32 type = 1;
}
message RspGetMiniProgramLoginURL{
  string url = 1;
}

message ReqUpdateObjectCustomLabels{
  // 打标签的对象 id
  repeated uint64 id = 1 [(validator) = "required"];
  // 自定义标签kv对
  repeated tanlive.ai.CustomLabel labels = 2;
  // 打标签的对象类型
  tanlive.ai.CustomLabelObjectType object_type = 3;
}

message ReqListChatLiveAgent {
  uint64 chat_id = 1;
}

message RspListChatLiveAgent {
  tanlive.ai.ChatLiveAgentInfo chatLiveAgent = 1;
}

message ReqSwitchChatLiveAgent {
  // 人工客服id
  uint64 live_agent_id = 1 [(validator) = "required"];
  // 会话id
  uint64 chat_id = 2 [(validator) = "required"];
}

message RspSwitchChatLiveAgent {
  // 切换结果
  tanlive.ai.SwitchChatState state = 1;
}

message ReqListTextFiles{
  repeated uint64 assistant_id = 1;
  tanlive.ai.DocState state = 2;
  repeated tanlive.ai.Contributor contributor = 3;
  repeated tanlive.ai.Operator update_by = 4;
  repeated base.OrderBy order_by = 5;
  uint32 offset = 6;
  uint32 limit = 7 [(validator) = "max=200"];
  message Search{
    string text = 1; // 文本内容搜索
    string file_name = 2; // 文件名搜索
  }
  Search search = 8;
  repeated uint64 id = 9;
  // 不在助手中
  repeated uint64 excluded_assistant_id = 10;
  // 重复 doc 紧凑排序
  bool group_repeated = 11;
  uint32 show_contributor = 12;
  repeated tanlive.ai.DocSharedState shared_state = 13;
  // 分享的助手
  repeated uint64 share_assistant_id = 14;
  repeated tanlive.ai.LabelFilter labels = 15;
  // 自定义标签排序，只能当个标签排序
  tanlive.ai.OrderByLabel order_by_label = 16;
  repeated uint64 ids = 17;
  // TQL表达式（高级搜索）
  string tql_expression = 31;
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 32;
  repeated tanlive.ai.Operator create_by = 33;
  repeated tanlive.ai.DocParseMode parse_mode = 34;
  // 查询解析失败的数据
  tanlive.ai.DocState parse_state = 35;

  // 知识提示过滤条件
  message TipFilter {
    // 警告条件组
    bool warning = 1;
  }
  TipFilter tip_filter = 36;
  // 数据源过滤
  tanlive.ai.DocDataSource data_source = 37;
  // 数据源同步状态
  uint32 data_source_state = 38;
  // 分享的团队id
  repeated uint64 share_team_id = 39;
  // 分享的用户id
  repeated uint64 share_user_id = 40;
}

message RspListTextFiles{
  uint32 total_count = 1;
  repeated CollectionTextFile items = 2;
  uint32 fail_parse_count = 3;
}

message ReqCreateQAs{
  repeated ReqCreateQA items = 6 [(validator) = "required,max=200,dive"];
}

message RspCreateQAs{
  repeated uint64 id = 1;
}

message ReqCreateQA{
  string question = 1;
  string answer = 2;
  repeated tanlive.ai.DocReference reference = 3;
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 5 [(validator) = "omitempty,dive"];
  tanlive.ai.DocState state = 6 [(validator) = "omitempty,oneof=1 2"];
  repeated uint64 assistant_id = 9 [(validator) = "omitempty"];
  // 是否显示贡献者
  uint32 show_contributor = 10;
  repeated uint64 share_assistant_id = 11 [(validator) = "omitempty"];
  repeated tanlive.ai.CustomLabel labels = 12 [(validator) = "omitempty,dive"];
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 13 [(validator) = "required,dive,required"];
}

message ReqCreateTextFile{
  // 文件/文本名称
  string file_name = 1 [(validator) = "required"];
  // 文件/文本内容
  string text = 2;
  // 助手
  repeated uint64 assistant_id = 3 [(validator) = "omitempty"];
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 4 [(validator) = "omitempty,dive"];
  // 文件url
  string url = 5;
  // ugc类型
  base.DataType ugc_type = 6;
  // ugc的id
  uint64 ugc_id = 7;
  // 文件解析后地址
  string parsed_url = 8;
  // 状态 1: 启用 2: 停用 状态设置只对文本有效
  tanlive.ai.DocState state = 9;
  // 类型 2:文本 3:文件
  uint32 type = 10 [(validator) = "omitempty,oneof=2 3"];
  // 是否显示贡献者
  uint32 show_contributor = 11;
  repeated uint64 share_assistant_id = 12 [(validator) = "omitempty"];
  repeated tanlive.ai.CustomLabel labels = 13;
  repeated tanlive.ai.DocReference reference = 14;
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 15;
  tanlive.ai.DocParseMode parse_mode = 16;
  // 数据源
  tanlive.ai.DocDataSource data_source = 20;
}

message ReqCreateTextFiles{
  // 解析模式
  repeated ReqCreateTextFile items = 1 [(validator) = "required,max=1000,dive"];
}

message RspCreateTextFiles{
  repeated uint64 id = 9;
}

message ReqUpdateQA{
  string question = 1;
  string answer = 2;
  repeated tanlive.ai.DocReference reference = 3;
  repeated tanlive.ai.DocAssistantState states = 4;
  repeated tanlive.ai.Contributor contributor = 7 [(validator) = "omitempty,dive"];
  uint64 id = 8 [(validator) = "required"];
  google.protobuf.FieldMask mask = 9;
  // 是否显示贡献者
  uint32 show_contributor = 10;
  // 标签
  repeated tanlive.ai.CustomLabel labels = 11;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 12;
}

message ReqUpdateQAs{
  repeated ReqUpdateQA items = 1 [(validator) = "required,max=200,dive"];
}

message ReqUpdateTextFile{
  uint64 id = 1 [(validator) = "required"];
  string file_name = 2 [(validator) = "omitempty,max=4096"];
  string text = 3;
  string url = 4;
  repeated tanlive.ai.DocAssistantState states = 6;
  repeated tanlive.ai.Contributor contributor = 7;
  base.DataType ugc_type = 8;
  uint64 ugc_id = 9;
  // 解析后的文件url
  string parsed_url = 10;
  google.protobuf.FieldMask mask = 11;
  // 是否显示贡献者
  uint32 show_contributor = 12;
  // 标签
  repeated tanlive.ai.CustomLabel labels = 13;
  repeated tanlive.ai.DocReference reference = 14;
  // 是否可以作为参考资料下载
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 15;
}

message ReqUpdateTextFiles{
  repeated ReqUpdateTextFile items = 1 [(validator) = "required,max=200,dive"];
}

message ReqDeleteDocs{
  repeated uint64 id = 1 ;
  uint64 query_id = 2;
}

message RspDeleteDocs {
  bool async = 1;
}

message ReqReparseTextFiles {
  repeated uint64 ids = 1 ;
  tanlive.ai.DocParseMode parse_mode = 2;
  uint64 query_id = 3;
}

message RspReparseTextFiles {
  bool async = 1 ;
}

message ReqCreateDocQuery {
  ReqListTextFiles doc = 1;
  ReqListQA qa = 2;
}

message RspCreateDocQuery {
  uint64 query_id = 1;
  bool is_empty = 2;
  uint32 total_count = 3;
}

message ReqSearchCollection{
  string search = 1 [(validator) = "required"];
  repeated uint64 assistant_id = 2 [(validator) = "required"];
  tanlive.ai.DocType doc_type = 3;
  float threshold = 4;
  uint32 top_n = 5;
  uint32 offset = 6;
  uint32 limit = 7;
  float text_weight = 8;
  uint32 text_recall_top_n = 9;
  // 关键词召回匹配目标
  tanlive.ai.TextRecallQuery text_recall_query = 10;
  // 关键词召回模式
  tanlive.ai.TextRecallPattern text_recall_pattern = 11;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 12;
  bool clean_chunks = 13;
  float temperature = 14;
}

message RspSearchCollection{
  google.protobuf.Timestamp start = 2;
  google.protobuf.Timestamp end = 3;
  uint32 total_count = 4;
  tanlive.ai.DocMatchPattern match_pattern = 5;
  repeated SearchCollectionItem item = 6;
}

message ReqValidateQAs{
  message Item{
    string question = 1;
    string answer = 2;
    repeated tanlive.ai.DocReference reference = 3;
    repeated uint64 assistant_id = 4;
    // 贡献者
    repeated tanlive.ai.Contributor contributor = 5;
    tanlive.ai.DocState state = 6;
  }
  repeated Item items = 1;
}

message RspValidateQAs{
  message Err{
    errors.AiError code = 1;
    string message = 2;
    uint64 id = 3;
  }
  repeated Err errors = 1;
}

message ReqValidateTextFiles{
  message Item{
    string file_name = 1;
    string text = 2;
  }
  repeated Item items = 1;
  tanlive.ai.DocDataSource data_source = 2;
}

message RspValidateTextFiles{
  message Err{
    errors.AiError code = 1;
    string message = 2;
    uint64 id = 3;
  }
  repeated Err errors = 1;
}

message ReqListContributor{
  string search = 1;
  tanlive.ai.ListDocFilterType type = 2;
  tanlive.ai.DocDataSource data_source = 3;
}

message RspListContributor{
  repeated tanlive.ai.Contributor contributors = 1;
}

message ReqListOperator{
  string search = 1;
  tanlive.ai.ListDocFilterType type = 2;
  // 是否为创建人，false代表更新人，true代表创建人
  bool creator = 3;
  tanlive.ai.DocDataSource data_source = 4;
}

message RspListOperator{
  repeated DocOperator operators = 1;
}

message ReqListSharedAssistant{
  string search = 1;
  tanlive.ai.ListDocFilterType type = 2;
  tanlive.ai.DocDataSource data_source = 3;
}

message RspListSharedAssistant{
  repeated Assistant assistants = 1;
}

message ReqListSharedTeam{
  string search = 1;
  tanlive.ai.ListDocFilterType type = 2;
  tanlive.ai.DocDataSource data_source = 3;
}

message RspListSharedTeam{
  message SharedTeam{
    uint64 id = 1;
    string name = 2;
  }

  repeated SharedTeam teams = 1;
}

message ReqListSharedUser{
  string search = 1;
  tanlive.ai.ListDocFilterType type = 2;
  tanlive.ai.DocDataSource data_source = 3;
}

message RspListSharedUser{
  message SharedUser{
    uint64 id = 1;
    string name = 2;
  }

  repeated SharedUser users = 1;
}

message ReqListCollectionFileName{
  // 文件名模糊搜索匹配
  string search = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  // 文件名精确匹配搜索
  repeated string full_search = 4;
  tanlive.ai.DocDataSource data_source = 5;
}

message RspListCollectionFileName{
  uint32 total_count = 1;
  message Item{
    // 文件绑定的url/path
    string url = 1;
    // 文件名称
    string name = 2;
    // 文件id
    uint64 id = 3;
  }
  repeated Item items = 2;
}


message ReqCreateTencentDocAuthUrl{
}

message RspCreateTencentDocAuthUrl{
  string url = 1;
}

message ReqAuthTencentCode{
  string code = 1;
  string path = 2;
}

message RspAuthTencentCode{}

message ReqDescribeTencentToken{
  string hash_user_id = 1 [(validator) = "required"];
}

message RspDescribeTencentToken{
  bool is_empty = 1;
}

message ReqListExternalSourceUser{
}

message RspListExternalSourceUser{
  repeated ExternalSourceUser users = 1;
}

message ReqDescribeMyDoc{}

message RspDescribeMyDoc{
  repeated string file_ids = 1;
}

message ReqDescribeDocList{
  string state = 2;
  string search = 3;
  uint32 start = 4;
  string folder_id = 5;
  string hash_user_id = 6 [(validator) = "required"];
  string title_name_sort = 10 [(validator) = "omitempty,oneof=asc desc"]; // asc desc
}

message RspDescribeDocList{
  repeated TencentDoc docs = 1;
  uint32 next = 2;
}

message ReqDescribeTencentDocTask{
}

message RspDescribeTencentDocTask{
  bool is_running = 1;
}

message ReqDelTencentDocAuth{
  string hash_user_id = 1 [(validator) = "required"];
}


message ReqImportTencentDoc{
  repeated string file_ids = 1  [(validator) = "required"];
  string hash_user_id = 2 [(validator) = "required"];
  string path = 3 [(validator) = "omitempty,dirpath"];
}

message RspImportTencentDoc{
  string uuid = 1;
}

// 重新导入腾讯文档(doc_id 知识库的文档id)
message ReqReimportTencentDoc{
  repeated uint64 doc_ids = 1 [(validator) = "required"];
}

message RspReimportTencentDoc{
  message FailInfo{
    uint64 doc_id = 1;
    string file_name = 2;
    tanlive.ai.ExternalSourceUser user = 3;
  }
  repeated FailInfo failed = 2;
}

message ReqImportTencentDocWebClip{
  google.protobuf.Timestamp after_time = 1 [(validator) = "required"];
  string hash_user_id = 2 [(validator) = "required"];
}

message RspImportTencentDocWebClip{
  repeated TencentDoc docs = 1;
}

message ReqModifyDocTab{
  uint64 id = 1;
  string name = 2 [(validator) = "required"];
  uint32 type = 5 [(validator) = "required"];
}

message RspModifyDocTab{
  uint64 admin_type = 1;
  uint64 account_id = 2;
}

message ReqDescribeDocTab{
}

message RspDescribeDocTab{
  message DocTab {
    uint64 id = 1;
    string name = 2;
    uint32 type = 3;
    bool is_show = 4;
    bool has_expired = 5;
  }
  repeated DocTab tabs = 1;
}

message ReqCreateGTBDocText{
  bool is_pull_all = 1;
}

message RspCreateGTBDocText{
  string uuid = 1;
}

message ReqDescribeAccountIsGTB{
}

message RspDescribeAccountIsGTB{
  bool is_gtb = 1;
}

message ReqCloneDoc{
  repeated uint64 id = 1 [(validator) = "required"];
  uint64 query_id = 2;
}

message RspCloneDoc{
  repeated uint64 id = 1;
}

message ReqOnOffDocs{
  repeated uint64 id = 1 ;
  tanlive.ai.DocState state = 2 [(validator) = "required,oneof=1 2"];
  uint64 query_id = 3;
}

message RspOnOffDocs{
  message RepeatCollection{
    uint64 id = 1;
    map<uint64, string> file_name = 2;
  }
  repeated RepeatCollection pre_repeat_collections = 1;
  repeated RepeatCollection repeat_collections = 2;
  message QaContainsMatchCount{
    uint64 assistant_id = 1;
    uint64 cnt = 2;
  }
  repeated QaContainsMatchCount qa_num_exceed = 3;
  bool async = 4;
}


message ReqGetFeedbacks {
  // 地区
  base.Region region = 1 [(validator) = "required"];
  // 上传用户ID
  repeated uint64 create_by = 2;
  // 状态筛选
  repeated tanlive.ai.FeedbackState state = 3;
  // 创建时间区间
  tanlive.base.TimeRange create_date_range = 4;
  // 处理时间区间
  tanlive.base.TimeRange handled_at_range = 5;
  // 分页偏移量
  uint32 offset = 6;
  // 分页大小
  uint32 limit = 7;
  // 排序
  repeated base.OrderBy order_by = 8;
  // 助手ID
  repeated uint64 assistant_ids = 9 ;
  repeated string region_codes = 10;
}

message RspGetFeedbacks {
  message Item {
    // 反馈详情
    tanlive.ai.Feedback feedback = 1;
    // 参考文献
    repeated tanlive.ai.FeedbackReference references = 2;
    // 原始问题
    tanlive.ai.ChatMessage original_question = 3;
    // 原始回答
    tanlive.ai.ChatMessage original_answer = 4;
    // 预期命中的知识
    repeated tanlive.ai.ChatMessageDoc expected_docs = 5;
    // 预期命中的知识（碳LIVE运营）
    repeated tanlive.ai.ChatMessageDoc expected_mgmt_docs = 6;
  }
  // 反馈列表
  repeated Item items = 1;
  // 总数
  uint32 total_count = 2;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 3;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 4;
}

message ReqFindFeedback {
  // 用户反馈ID
  uint64 feedback_id = 1 [(validator) = "required"];
}

message RspFindFeedback {
  // 反馈详情
  tanlive.ai.Feedback feedback = 1;
  // 参考文献
  repeated tanlive.ai.FeedbackReference references = 2;
  // 上传用户
  tanlive.iam.UserInfo create_by = 3;
  // 原始问题
  tanlive.ai.ChatMessage original_question = 4;
  // 原始回答
  tanlive.ai.ChatMessage original_answer = 5;
  // 预期命中的知识
  repeated tanlive.ai.ChatMessageDoc expected_docs = 6;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 7;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 8;
  // 预期命中的知识（碳LIVE运营）
  repeated tanlive.ai.ChatMessageDoc expected_mgmt_docs = 9;
}

message ReqAcceptFeedback {
  // 反馈ID
  repeated uint64 feedback_ids = 1 [(validator) = "required"];
  uint64 assistant_id = 2 [(validator) = "required"];
}

message RspAcceptFeedback {
  message Result {
    // 反馈ID
    uint64 feedback_id = 1;
    // 错误码
    int32 code = 2;
  }
  // 结果
  repeated Result results = 1;
}

message ReqGetFeedbackLogs {
  // 地区
  base.Region region = 1 [(validator) = "required"];
  // 反馈ID
  uint64 feedback_id = 2;
  // 操作人
  tanlive.base.Identity create_identity = 3;
  // 操作类型
  repeated tanlive.ai.FeedbackAction action = 4;
  // 操作时间区间
  tanlive.base.TimeRange create_date_range = 5;
  // 分页偏移量
  uint32 offset = 6;
  // 分页大小
  uint32 limit = 7;
  // 排序
  repeated base.OrderBy order_by = 8;
}

message RspGetFeedbackLogs {
  // 日志列表
  repeated tanlive.ai.FullFeedbackLog items = 1;
  // 总数
  uint32 total_count = 2;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 3;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 4;
}

message ReqDescribeChatRegionCode {
  // 地区
  base.Region region = 1 [(validator) = "required"];
  repeated uint64 assistant_ids = 2 ;
}

message RspDescribeChatRegionCode {
  repeated string region_codes = 1;
}

message ReqDescribeFeedbackRegionCode {
  // 地区
  base.Region region = 1 [(validator) = "required"];
}

message RspDescribeFeedbackRegionCode {
  repeated string region_codes = 1;
}

message ReqGetTextFile{
  uint64 id = 1;
}

message RspGetTextFile{
  CollectionTextFile item = 1;
}
message ReqSearchChat {
  string text = 1 [(validator) = "required"];
  uint64 question_id = 2 [(validator) = "required"];
  // 助手id
  uint64 assistant_id = 3 [(validator) = "required"];
  // topN
  uint32 top_n = 4;
  // 阈值
  float threshold = 5[(validator) = "min=0,max=1"];
  // 关键词召回条数
  int32 text_recall_top_n = 7;
  // 温度
  float temperature = 8[(validator) = "min=0,max=2"];
  bool clean_chunks = 9;
  // 关键词召回匹配目标
  tanlive.ai.TextRecallQuery text_recall_query = 50;
  // 关键词召回模式
  tanlive.ai.TextRecallPattern text_recall_pattern = 51;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 52;
}

message ReqSearchChatStream {
  string text = 1 [(validator) = "required"];
  // 助手id
  uint64 assistant_id = 3 [(validator) = "required"];
  // topN
  uint32 top_n = 4;
  // 阈值
  float threshold = 5;
  // 关键词召回条数
  int32 text_recall_top_n = 7;
  // 温度
  float temperature = 8;
  bool clean_chunks = 9;
  // 关键词召回匹配目标
  tanlive.ai.TextRecallQuery text_recall_query = 10;
  // 关键词召回模式
  tanlive.ai.TextRecallPattern text_recall_pattern = 11;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 12;
  bool stream = 13;
}

message RspSearchChat {
  tanlive.ai.EventChatMessage message = 1 [(tanlive.validator) = "required"];
  uint64 user_id = 2 [(tanlive.validator) = "required"];
  // 是否是推送运营端
  bool is_op = 3;
  // 是否仅搜索
  bool is_only_search = 4;
}

message ReqProxyChatHtmlUrl{
  repeated string urls = 1 [(validator) = "required"];
  // 助手id
  uint64 assistant_id = 2;
}

message RspProxyChatHtmlUrl{
  message Content {
    string url = 1;
    string title = 2;
  }
  repeated Content contents = 1;
}

message ReqImportTextFile{
  // 文件/文本名称
  string file_name = 1 [(validator) = "required"];
  // 文件/文本内容
  string text = 2;
  // 助手
  repeated uint64 assistant_id = 3 [(validator) = "omitempty"];
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 4 [(validator) = "omitempty,dive"];
  // 是否显示贡献者
  uint32 show_contributor = 11;
  repeated uint64 share_assistant_id = 12 [(validator) = "omitempty"];
  repeated tanlive.ai.CustomLabel labels = 13;
  // 用于指定哪些字段需要更新
  google.protobuf.FieldMask mask = 14;
  repeated tanlive.ai.DocReference reference = 15;
  // 是否可以作为参考资料下载
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 16;
}

message ReqImportTextFiles{
  repeated ReqImportTextFile items = 1;
}

message RspImportTextFiles{
}

message RspExportTask{
  uint64 task_id = 1;
}

message ReqCreateFileExportTask {
  ReqListTextFiles filter = 1;
  repeated ExportField fields = 2;
}

message ReqCreateQaExportTask{
  ReqListQA filter = 1;
  repeated ExportField fields = 2;
}

message ReqDescribeExportTasks{
  repeated tanlive.ai.ExportTaskType type = 1;
  tanlive.ai.TaskOperationType operation_type = 2;
}

message RspDescribeExportTasks{
  repeated tanlive.ai.ExportTask tasks = 1;
}

message ReqCreateChatExportTask {
  ReqListChat filter = 1;
  repeated ExportField fields = 2;
}

message ReqCreateChatMessageExportTask {
  ReqListChat filter = 1;
  repeated ExportField fields = 2;
}

message ReqImportQA{
  string question = 1;
  string answer = 2;
  repeated tanlive.ai.DocReference reference = 3;
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 5 [(validator) = "omitempty,dive"];
  repeated uint64 assistant_id = 9 [(validator) = "omitempty"];
  // 是否显示贡献者
  uint32 show_contributor = 10;
  repeated uint64 share_assistant_id = 11 [(validator) = "omitempty"];
  repeated tanlive.ai.CustomLabel labels = 12;
  // 用于指定哪些字段需要更新
  google.protobuf.FieldMask mask = 13;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 14;
}

message ReqImportQAs{
  repeated ReqImportQA items = 1;
}

message RspImportQAs{

}

message ReqConvertCustomLabel{
  uint64 id = 1  [(validator) = "required"];
  tanlive.ai.CustomLabel target = 2  [(validator) = "required"];
  bool dry_run = 3;
}

message RspConvertCustomLabel{
  uint32 reserved = 1;
  uint32 deleted = 2;
}

message ReqGetCustomLabelValueTopN{
  uint64 id = 1  [(validator) = "required"];
}

message RspGetCustomLabelValueTopN{
  repeated tanlive.ai.LabelValue values = 1;
}

message ReqRestartExportTask {
  uint64 task_id = 1;
}

message ReqConfirmAiServiceTerms {
  // 是否同意
  bool is_agreed = 1;
}

message RspGetMyAiServiceTermsConfirmation {
  // 开通助手身份：0未开通；1个人开通；2团队开通
  int32 assistant_identity = 1;
  // 是否已弹窗
  bool popup = 2;
  // 是否已同意
  bool is_agreed = 3;
  // 协议类型
  cms.TermsType terms_type = 4;
  // 协议文档路径
  string terms_doc = 5;
  // 团队是否已认证
  bool is_verified = 6;
}

message ReqGetMyAssistants {
  // 分页偏移量
  uint32 offset = 1;
  // 分页大小
  uint32 limit = 2;
  // 助手ID
  repeated uint64 assistant_id = 3;
}

message RspGetMyAssistants {
  // 助手列表
  repeated tanlive.ai.FullAssistant assistants = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqUpdateMyAssistant {
  // 助手ID
  uint64 assistant_id = 1 [(validator) = "required"];
  // 配置详情
  tanlive.ai.AssistantConfig config = 2 [(validator) = "required"];
  // 更新字段列表，例如："name,prompt_prefix,search_engine"
  google.protobuf.FieldMask mask = 3;
}

message RspGetAssistantOptions {
  // 对话模型
  repeated string chat_model = 1;
  // ChatOrSql模型
  repeated string chat_or_sql_model = 2;
  // 解析图谱模型
  repeated string graph_parse_mode = 3;
  // 搜索引擎
  repeated string search_engine = 4;
  // 互动暗号
  repeated tanlive.ai.InteractiveCodeOption interactive_code = 5;
  // 链路查询
  repeated tanlive.ai.VisibleChainOption visible_chain = 6;
  // 问题建议模型
  repeated string ask_suggestion_model = 7;
  // 向量化模型
  repeated tanlive.ai.EmbeddingModelOption embedding_model = 8;
  // 对话模型
  repeated tanlive.ai.ChatModelOption chat_model_v2 = 9;
  // 对话模型
  repeated tanlive.ai.SearchEngineOption search_engine_v2 = 10;
  // 快捷指令
  repeated tanlive.ai.QuickAction quick_actions = 11;
  // 小程序URL白名单
  repeated string mini_white_url = 12;
}

message ReqGetAssistantChunkConfig {
  // 分页偏移量
  uint32 offset = 1;
  // 分页大小
  uint32 limit = 2;
  // 文档ID
  uint64 doc_id = 3 [(validator) = "required"];
  // 向量化模型
  string collection_lang = 4;
}

message RspGetAssistantChunkConfig {
  // 助手列表
  repeated tanlive.ai.FullAssistant assistants = 1;
  // 总数
  uint32 total_count = 2;
  // 向量化模型数量
  uint32 collection_lang_count = 5;
}

message ReqGetDocChunks {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 助手ID
  repeated uint64 assistant_id = 2;
}

message RspGetDocChunks {
  // 助手的分段列表
  repeated tanlive.ai.AssistantChunks assistant_chunks = 1;
}

message ReqAutoChunkDoc {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 新文本（如果文本未改动不需要传值）
  string new_text = 2;
  // 自动分段参数
  tanlive.ai.AutoChunkPara auto_para = 3 [(validator) = "required"];
}

message RspAutoChunkDoc {
  // 分段列表
  repeated tanlive.ai.ChunkItem chunks = 1;
}

message ReqManualChunkDoc {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 新文本（如果文本未改动不需要传值）
  string new_text = 2;
  // 手动分段参数
  tanlive.ai.ManualChunkPara manual_para = 3 [(validator) = "required"];
}

message RspManualChunkDoc {
  // 任务ID
  uint64 task_id = 1;
}

message ReqGetChunkDocTasks {
  // 文档ID
  repeated uint64 doc_id = 1 [(validator) = "required,dive,required"];
}

message RspGetChunkDocTasks {
  // 任务列表
  repeated tanlive.ai.DocChunkTask tasks = 1;
}

message ReqGetDocEmbeddingModels {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
}

message RspGetDocEmbeddingModels {
  // 向量化模型列表
  repeated tanlive.ai.EmbeddingModelCount embedding_models = 1;
}

message ReqGetAssistantConfig {
  // 仅查询碳LIVE应用助手
  bool only_tanlive_app = 1;
  // 通过路由查询
  string route_path = 2;
  // 助手ID
  repeated uint64 assistant_id = 3;
  // 应用ID
  string app_id = 4;
  // 仅查询碳LIVE小程序助手
  bool only_tanlive_miniprogram = 5;
}

message RspGetAssistantConfig {
  // 助手列表
  repeated tanlive.ai.FullAssistant assistants = 1;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 2;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 3;
}

message ReqGetAssistantsMiniprogram {
  // true 只查看最近使用的助手，如果没有则用默认配置填充
  bool only_recently_use = 1;
  // 分页偏移量
  uint32 offset = 2;
  // 分页大小
  uint32 limit = 3;
  // 搜索助手名称或机构名称
  string search = 4;
}

message RspGetAssistantsMiniprogram {
  // 助手列表
  repeated tanlive.ai.FullAssistant assistants = 1;
  // 用户卡片列表
  repeated tanlive.iam.UserCard user_cards = 2;
  // 团队卡片列表
  repeated tanlive.team.TeamCard team_cards = 3;
  uint32 total_count = 4;
}

message ReqBatchUpdateDocAttr{
  repeated uint64 id = 1 ;
  google.protobuf.FieldMask mask = 2 [(validator) = "required"];
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 4;
  // 是否显示贡献者
  uint32 show_contributor = 5;
  // 参考资料下载方式
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 6;
  // 修改关联的助手，增加的助手启用/禁用状态和已有的助手状态一致
  repeated uint64 assistant_id = 7;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 8;
  // 参考资料
  repeated tanlive.ai.DocReference reference = 9;
  uint64 query_id = 10;
}

message RspBatchUpdateDocAttr {
  bool async = 1;
}

message RspUpdateDocAttrInBulk{

}

// 获取文件文本知识提示请求
message ReqGetTextFileTip {
  // 文档ID
  uint64 id = 1;
}

// 获取文件文本知识提示响应
message RspGetTextFileTip {
  // 表头过长的表格
  repeated tanlive.ai.TextFileTipTableOverSize table_over_size = 1;
  // 解析状态
  tanlive.ai.DocState state = 2;
  // 内容重复信息
  repeated string repeated = 3;
}

// 获取QA知识提示请求
message ReqGetQaTip {
  // 文档ID
  uint64 id = 1;
}


// 获取QA知识提示响应
message RspGetQaTip {
  // 问题超长提示
  bool question_over_size = 1;
  // 内容重复信息
  repeated string repeated = 2;
}

message ReqCreateNebulaTask{
  string lang = 1 [(validator) = "required"];
  repeated uint64 assistant_id = 5;
  repeated uint64 query_assistant_id = 6;
  string filter_text = 7;
  bool has_k = 8;
  bool has_q = 9;

  string clustering_method = 17;
  repeated int32 min_samples_range = 18;
  repeated float eps_range = 19;
  repeated int32 n_clusters_range = 20;
}

message RspCreateNebulaTask{
  string uuid = 1;
}

message ReqDescribeNebulaTask{
  string uuid = 1 ;
  string lang = 2;
  repeated uint64 assistant_id = 3;
  repeated uint64 query_assistant_id = 4;
}

message RspDescribeNebulaTask{
  string uuid = 1;
  uint32 state = 2;
  string end_date = 3;
  string start_date = 4;
  repeated string calcu_result = 5;
  string filter_text = 6;
  string cluster_list = 7;
  string connect_info = 8;
}

message ReqDescribeNebulaTaskList{
  uint32 offset = 1;
  uint32 limit = 2;
  string to_model = 4;
  repeated string uuid = 5;
}

message RspDescribeNebulaTaskList{
  message Task {
    string uuid = 1;
    uint32 state = 2;
    string end_date = 5;
    string start_date = 6;
    string lang = 7;
    string filter_text = 8;
    uint32 is_read = 9;
  }
  repeated Task tasks = 1;
  uint32 total_count = 2;
  uint32 unread_num = 3;
}

message ReqDescribeNebulaProjection{
  string uuid = 1 [(validator) = "required"];
  string query = 2 [(validator) = "required"];
  string algorithm = 3;
}

message RspDescribeNebulaProjection{
  repeated double projection = 1;
}

message ReqDescribeNebulaData{
  repeated string content_hash = 1;
  uint64 id = 2;
}

message RspDescribeNebulaData{
  repeated string content = 1;
  string query_name = 2;
}

// 接收quesiton消息
message ReqChatSubscribe {
  // 服务端是否自动断开
  bool auto_disconnect = 5;
  // 问题id
  string question_id = 6 [(validator) = "required"];
  // duration
  int32 duration = 7;
  // 订阅的hashId
  string hash_id = 8;
  // 是否合并chunks
  bool combine_chunks = 9;
}

message ReqCreateChatQuestion {
  uint64 chat_id = 1;
  // 问题
  string text = 2 [(validator) = "required"];
  // 助手id
  uint64 assistant_id = 3 [(validator) = "required"];
  // 文件urls
  repeated string file_urls = 4;
  // 是否是agent暗号
  bool is_agent_command = 5;
  // 语言 zh en
  string lang = 6;
}

message RspCreateChatQuestion {
  uint64 chat_id = 1;
  uint64 question_id = 2;
  string lang = 3;
  bool is_file_ready = 4;
}

message ReqDescribeMessageFileState {
  uint64 message_id = 1 [(validator) = "required"];
}

message RspDescribeMessageFileState {
  repeated tanlive.ai.ChatMessageFile files = 1;
  bool is_file_ready = 2;
}

message ReqListNebulaAssistants{
  string search = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}

message RspListNebulaAssistants{
  repeated tanlive.ai.AssistantV2 assistants = 1;
  uint32 total_count = 2;
}

message ReqListNebulaContributors{
  repeated uint64 assistant_id = 1;
}

message RspListNebulaContributors{
  repeated tanlive.ai.Contributor contributors = 1;
}

message ReqListTeamCanShareDoc{
  // 搜索名称关键词
  string name = 1 [(validator) = "required"];
  // 分页偏移量
  uint64 offset = 2;
  // 分页大小
  uint64 limit = 3 [(validator) = "min=1,max=200"];
  // 文档ID
  uint64 doc_id = 4;
}

message RspListTeamCanShareDoc{
  message Teams{
    uint64 id = 1;
    string name = 2;
    string name_en = 3;
    bool is_selected = 4;
  }

  repeated Teams teams = 1;
}

message ReqListUserCanShareDoc{
  // 搜索名称关键词
  string name = 1 [(validator) = "required"];
  // 文档ID
  uint64 doc_id = 2;
}

message RspListUserCanShareDoc{
  message Users{
    uint64 id = 1;
    string name = 2;
    bool is_selected = 3;
  }

  repeated Users users = 1;
}

// AI模块接口
service AiBff {
  option (tanlive.bff) = true;
  // 查询message附件状态
  rpc DescribeMessageFileState(ReqDescribeMessageFileState) returns (RspDescribeMessageFileState) {
    option (google.api.http) = {
      post: "/ai/describe_message_file_state"
      body: "*"
    };
  };

  // 创建会话question
  rpc CreateChatQuestion(ReqCreateChatQuestion) returns (RspCreateChatQuestion) {
    option (google.api.http) = {
      post: "/ai/create_chat_question"
      body: "*"
    };
  };

  // 重新开始导出任务
  rpc RestartExportTask(ReqRestartExportTask) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/restart_export_task"
      body: "*"
    };
  }

  // 导出会话消息
  rpc CreateChatMessageExportTask(ReqCreateChatMessageExportTask) returns (RspExportTask) {
    option (google.api.http) = {
      post: "/ai/create_message_export_task"
      body: "*"
    };
  };
  // 导出会话
  rpc CreateChatExportTask(ReqCreateChatExportTask) returns (RspExportTask) {
    option (google.api.http) = {
      post: "/ai/create_chat_export_task"
      body: "*"
    };
  };
  // 查询导出任务
  rpc DescribeExportTasks(ReqDescribeExportTasks) returns (RspDescribeExportTasks){
    option (google.api.http) = {
      post: "/ai/describe_export_tasks"
      body: "*"
    };
  };

  // 导出qa
  rpc CreateQaExportTask(ReqCreateQaExportTask) returns (RspExportTask) {
    option (google.api.http) = {
      post: "/ai/create_qa_export_task"
      body: "*"
    };
  };
  // 导出文本文件
  rpc CreateFileExportTask(ReqCreateFileExportTask) returns (RspExportTask) {
    option (google.api.http) = {
      post: "/ai/create_file_export_task"
      body: "*"
    };
  };
  // 发送消息
  rpc ReceiveChatMessage(ReqReceiveChatMessage) returns (RspReceiveChatMessage) {
    option (google.api.http) = {
      post: "/ai/receive_chat_message"
      body: "*"
    };
  };

  // 创建会话
  rpc CreateChat(ReqCreateChat) returns (RspCreateChat) {
    option (google.api.http) = {
      post: "/ai/create_chat"
      body: "*"
    };
  };

  // 获取会话消息列表
  rpc DescribeChatMessages(ReqDescribeChatMessages) returns (RspDescribeChatMessages) {
    option (google.api.http) = {
      post: "/ai/describe_chat_messages"
      body: "*"
    };
  };

  // 获取会话列表
  rpc DescribeChats(ReqDescribeChats) returns (RspDescribeChats) {
    option (google.api.http) = {
      post: "/ai/describe_chats"
      body: "*"
    };
  };

  // 删除会话
  rpc DeleteChat(ReqDeleteChat) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/delete_chat"
      body: "*"
    };
  };

  // 重发会话消息
  rpc ResendChatMessage(ReqResendChatMessage) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/resend_chat_message"
      body: "*"
    };
  };

  // 评价AI回答
  rpc RateAiAnswer(ReqRateAiAnswer) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/rate_ai_answer"
      body: "*"
    };
  };

  // 创建用户反馈（助手维度）
  rpc CreateFeedback(ReqCreateFeedback) returns (RspCreateFeedback) {
    option (google.api.http) = {
      post: "/ai/create_feedback"
      body: "*"
    };
  };

  // 保存用户反馈（问题维度）
  rpc SaveUserFeedbackByQuestion(ReqSaveUserFeedbackByQuestion) returns (RspCreateFeedback) {
    option (google.api.http) = {
      post: "/ai/save_user_feedback_by_question"
      body: "*"
    };
  };

  // 保存运营反馈
  rpc SaveOpFeedback(ReqSaveOpFeedback) returns (RspCreateFeedback) {
    option (google.api.http) = {
      post: "/ai/save_op_feedback"
      body: "*"
    };
  };

  // 停止消息发送
  rpc StopQuestionReply(ReqStopQuestionReply) returns (RspStopQuestionReply) {
    option (google.api.http) = {
      post: "/ai/stop_reply"
      body: "*"
    };
  };
  // 获取自定义标签列表
  rpc ListCustomLabel(ReqListCustomLabel) returns (RspListCustomLabel){
    option (google.api.http) = {
      post: "/ai/get_custom_labels"
      body: "*"
    };
  }
  // 插入或更新自定义标签
  rpc ModifyCustomLabels(ReqModifyCustomLabels) returns (RspModifyCustomLabels){
    option (google.api.http) = {
      post: "/ai/modify_custom_labels"
      body: "*"
    };
  }
  // 删除自定义标签
  rpc DeleteCustomLabels(ReqDeleteCustomLabels) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/delete_custom_labels"
      body: "*"
    };
  }
  // 更新对象的自定义标签
  rpc UpdateObjectCustomLabels(ReqUpdateObjectCustomLabels) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/update_object_custom_labels"
      body: "*"
    };
  }

  // 转换标签
  rpc ConvertCustomLabel(ReqConvertCustomLabel) returns (RspConvertCustomLabel){
    option (google.api.http) = {
      post: "/ai/convert_custom_label"
      body: "*"
    };
  }

  // 获取标签topn值,默认top10
  rpc GetCustomLabelValueTopN(ReqGetCustomLabelValueTopN) returns (RspGetCustomLabelValueTopN){
    option (google.api.http) = {
      post: "/ai/get_custom_label_value_topn"
      body: "*"
    };
  }

  // 获取管理的ai助手列表
  rpc ListAssistant(ReqListAssistant) returns (RspListAssistant){
    option (google.api.http) = {
      post: "/ai/list_assistant"
      body: "*"
    };
  }

  // 搜索AI对话的用户列表
  rpc SearchChatUsers(ReqSearchChatUsers) returns (RspSearchChatUsers) {
    option (google.api.http) = {
      post: "/ai/search_chat_users"
      body: "*"
    };
  }

  // AI对话管理列表
  rpc ListChat(ReqListChat) returns (RspListChat){
    option (google.api.http) = {
      post: "/ai/list_chat"
      body: "*"
    };
  }
  // AI对话详情
  rpc GetChatDetail(ReqGetChatDetail) returns (RspGetChatDetail){
    option (google.api.http) = {
      post: "/ai/get_chat_detail"
      body: "*"
    };
  }
  // 获取消息详情
  rpc GetChatMessageDetail(ReqGetChatMessageDetail) returns (RspGetChatMessageDetail){
    option (google.api.http) = {
      post: "/ai/get_chat_message_detail"
      body: "*"
    };
  }
  // 查询QA列表
  rpc ListQA(ReqListQA) returns (RspListQA){
    option (google.api.http) = {
      post: "/ai/collection/list_qa"
      body: "*"
    };
  }
  // 导入文本/文件
  rpc ImportTextFiles(ReqImportTextFiles) returns (RspImportTextFiles){
    option (google.api.http) = {
      post: "/ai/collection/import_text_files"
      body: "*"
    };
  }

  // 导入文本/文件
  rpc ImportQAs(ReqImportQAs) returns (RspImportQAs){
    option (google.api.http) = {
      post: "/ai/collection/import_qas"
      body: "*"
    };
  }

  // 创建助手分享
  rpc CreateAssistantShare(ReqCreateAssistantShare) returns (RspCreateAssistantShare){
    option (google.api.http) = {
      post: "/ai/create_assistant_share"
      body: "*"
    };
  }
  // 创建助手发送方设置
  rpc CreateDocShareConfigSender(ReqCreateDocShareConfigSender) returns (RspCreateDocShareConfigSender){
    option (google.api.http) = {
      post: "/ai/create_assistant_sender"
      body: "*"
    };
  }
  // 查询助手发送方设置
  rpc ListeDocShareConfigSender(ReqListeDocShareConfigSender) returns (RspListeDocShareConfigSender){
    option (google.api.http) = {
      post: "/ai/list_assistant_sender"
      body: "*"
    };
  }
  // 查询已经设置并开启知识库接收的助手
  rpc ListMyAssistantIds(ReqListMyAssistantIds) returns (RspListMyAssistantIds){
    option (google.api.http) = {
      post: "/ai/list_my_assistant_ids"
      body: "*"
    };
  }
  // 查询可分享的助手列表
  rpc ListAssistantCanShareDoc(ReqListAssistantCanShareDoc) returns (RspListAssistantCanShareDoc){
    option (google.api.http) = {
      post: "/ai/list_assistant_to_share_doc"
      body: "*"
    };
  }
  // 创建助手接收方设置
  rpc CreateDocShareConfigReceiverAssistant(ReqCreateDocShareConfigReceiverAssistant) returns (RspCreateDocShareConfigReceiverAssistant){
    option (google.api.http) = {
      post: "/ai/create_assistant_receiver"
      body: "*"
    };
  }
  // 查询助手接收方设置
  rpc ListDocShareConfigReceiverAssistant(ReqListDocShareConfigReceiverAssistant) returns (RspListDocShareConfigReceiverAssistant){
    option (google.api.http) = {
      post: "/ai/list_assistant_receiver"
      body: "*"
    };
  }
  // 创建个人/团队接收方设置
  rpc CreateDocShareConfigReceiverUserTeam(ReqCreateDocShareConfigReceiverUserTeam) returns (RspCreateDocShareConfigReceiverUserTeam){
    option (google.api.http) = {
      post: "/ai/create_user_team_receiver"
      body: "*"
    };
  }
  // 查询个人/团队接收方设置
  rpc ListDocShareConfigReceiverUserTeam(ReqListDocShareConfigReceiverUserTeam) returns (RspListDocShareConfigReceiverUserTeam){
    option (google.api.http) = {
      post: "/ai/list_user_team_receiver"
      body: "*"
    };
  }

  // 绑定用户小程序unionid
  rpc BindMiniProgramUniID(ReqBindMiniProgramUniID) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/bind_mini_program_uniid"
      body: "*"
    };
  }

  // 一键绑定用户和小程序unionid
  rpc BindOnceUserMiniProgram(ReqBindOnceUserMiniProgram) returns (RspBindOnceUserMiniProgram){
    option (google.api.http) = {
      post: "/ai/bind_once_user_mini_program"
      body: "*"
    };
  }

  // 绑定用户小程序手机号
  rpc BindMiniProgramPhoneAccount(ReqBindMiniProgramPhoneAccount) returns (RspBindMiniProgramPhoneAccount){
    option (google.api.http) = {
      post: "/ai/bind_mini_program_phone_account"
      body: "*"
    };
  }

  // 绑定用户小程序普通账号
  rpc BindMiniProgramNormalAccount(ReqBindMiniProgramNormalAccount) returns (RspBindMiniProgramNormalAccount){
    option (google.api.http) = {
      post: "/ai/bind_mini_program_normal_account"
      body: "*"
    };
  }

  // 获取用户账号union_id绑定状态
  rpc GetAccountUnionIdStatus(ReqGetAccountUnionIdStatus) returns (RspGetAccountUnionIdStatus){
    option (google.api.http) = {
      post: "/ai/get_account_unionid_status"
      body: "*"
    };
  }

  // 通过选择账号绑定小程序，替换临时账号
  rpc BindSelectedAccount(ReqGetMiniProgramUserInfo) returns (RspGetMiniProgramUserInfo){
    option (google.api.http) = {
      post: "/ai/get_mini_program_user_info"
      body: "*"
    };
  }

  // 获取当前用户是否一键绑定提示
  rpc GetAccountOnceBindStatus(ReqGetAccountOnceBindStatus) returns (RspGetAccountOnceBindStatus){
    option (google.api.http) = {
      post: "/ai/get_account_once_bind_status"
      body: "*"
    };
  }

  // 获取webview 迁移至小程序token
  rpc GetWebViewToMiniProgramToken(ReqGetWebViewToMiniProgramToken) returns (RspGetWebViewToMiniProgramToken){
    option (google.api.http) = {
      post: "/ai/get_webview_to_mini_program_token"
      body: "*"
    };
  }

  // 绑定用户小程序临时映射unionid
  rpc BindUnitokenByCode(ReqBindUnitokenByCode) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/bind_unitoke_by_code"
      body: "*"
    };
  }

  // 获取小程序登录url
  rpc GetMiniProgramLoginURL(ReqGetMiniProgramLoginURL) returns (RspGetMiniProgramLoginURL){
    option (google.api.http) = {
      post: "/ai/get_mini_program_login_url"
      body: "*"
    };
  }

  // 获取小程序文档相关权限
  rpc GetMiniProgramAuth(ReqGetMiniProgramAuth) returns (RspGetMiniProgramAuth){
    option (google.api.http) = {
      post: "/ai/get_mini_program_auth"
      body: "*"
    };
  }

  // 获取小程序助手限制情况
  rpc GetMiniProgramAssistantLimit(ReqGetMiniProgramAssistantLimit) returns (RspGetMiniProgramAssistantLimit){
    option (google.api.http) = {
      post: "/ai/get_mini_program_assistant_limit"
      body: "*"
    };
  }

  // 批量获取用户对应助手的限制情况
  rpc BatchUserAssistantLimit(ReqBatchUserAssistantLimit) returns (RspBatchUserAssistantLimit){
    option (google.api.http) = {
      post: "/ai/batch_user_assistant_limit"
      body: "*"
    };
  }

  // 绑定手机号通过小程序code
  rpc BindUserPhoneByCode(ReqBindUserPhoneByCode) returns (RspBindUserPhoneByCode){
    option (google.api.http) = {
      post: "/ai/bind_user_phone_by_code"
      body: "*"
    };
  }

  // 查询文本/文件列表
  rpc ListTextFiles(ReqListTextFiles) returns (RspListTextFiles){
    option (google.api.http) = {
      post: "/ai/collection/list_text_files"
      body: "*"
    };
  }

  // id查询文本/文件详情
  rpc GetTextFile(ReqGetTextFile) returns (RspGetTextFile){
    option (google.api.http) = {
      post: "/ai/collection/get_text_file"
      body: "*"
    };
  }

  // 创建QA
  rpc CreateQAs(ReqCreateQAs) returns (RspCreateQAs){
    option (google.api.http) = {
      post: "/ai/collection/create_qas"
      body: "*"
    };
  }

  // 创建文本或文件
  rpc CreateTextFiles(ReqCreateTextFiles) returns (RspCreateTextFiles){
    option (google.api.http) = {
      post: "/ai/collection/create_text_files"
      body: "*"
    };
  }

  // 更新QA
  rpc UpdateQAs(ReqUpdateQAs) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/collection/update_qas"
      body: "*"
    };
  }

  // 更新文本或文件
  rpc UpdateTextFiles(ReqUpdateTextFiles) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/collection/update_text_files"
      body: "*"
    };
  }

  // 批量更新doc的特定字段值
  rpc BatchUpdateDocAttr(ReqBatchUpdateDocAttr) returns (RspBatchUpdateDocAttr){
    option (google.api.http) = {
      post: "/ai/collection/batch_update_docs"
      body: "*"
    };
  }

  // 删除Doc，包括QA，文本或文件
  rpc DeleteDocs(ReqDeleteDocs) returns (RspDeleteDocs){
    option (google.api.http) = {
      post: "/ai/collection/delete_docs"
      body: "*"
    };
  }

  // 重新解析文件
  rpc ReparseTextFiles(ReqReparseTextFiles) returns (RspReparseTextFiles){
    option (google.api.http) = {
      post: "/ai/collection/reparse_text_files"
      body: "*"
    };
  }

  rpc CreateDocQuery(ReqCreateDocQuery) returns (RspCreateDocQuery){
    option (google.api.http) = {
      post: "/ai/collection/create_doc_query"
      body: "*"
    };
  }

  // collection向量查询
  rpc SearchCollection(ReqSearchCollection) returns (RspSearchCollection){
    option (google.api.http) = {
      post: "/ai/collection/search_collection"
      body: "*"
    };
  }

  // 校验待创建QA
  rpc ValidateQAs(ReqValidateQAs) returns (RspValidateQAs){
    option (google.api.http) = {
      post: "/ai/collection/validate_qas"
      body: "*"
    };
  }

  // 校验待创建文本文件
  rpc ValidateTextFiles(ReqValidateTextFiles) returns (RspValidateTextFiles){
    option (google.api.http) = {
      post: "/ai/collection/validate_text_files"
      body: "*"
    };
  }

  // 查询贡献者列表
  rpc ListContributor(ReqListContributor) returns (RspListContributor){
    option (google.api.http) = {
      post: "/ai/collection/list_contributor"
      body: "*"
    };
  }

  // 查询更新人列表
  rpc ListOperator(ReqListOperator) returns (RspListOperator){
    option (google.api.http) = {
      post: "/ai/collection/list_operator"
      body: "*"
    };
  }

  // 查询已分享的助手列表，用于表头筛选
  rpc ListSharedAssistant(ReqListSharedAssistant) returns (RspListSharedAssistant){
    option (google.api.http) = {
      post: "/ai/collection/list_shared_assistant"
      body: "*"
    };
  }

  // 查询已分享的团队列表，用于表头筛选
  rpc ListSharedTeam(ReqListSharedTeam) returns (RspListSharedTeam){
    option (google.api.http) = {
      post: "/ai/collection/list_shared_team"
      body: "*"
    };
  }

  // 查询已分享的用户列表，用于表头筛选
  rpc ListSharedUser(ReqListSharedUser) returns (RspListSharedUser){
    option (google.api.http) = {
      post: "/ai/collection/list_shared_user"
      body: "*"
    };
  }

  // 查询文件列表
  rpc ListCollectionFileName(ReqListCollectionFileName) returns (RspListCollectionFileName){
    option (google.api.http) = {
      post: "/ai/collection/list_filename"
      body: "*"
    };
  }

  // 创建腾讯文档授权链接
  rpc CreateTencentDocAuthUrl(ReqCreateTencentDocAuthUrl) returns (RspCreateTencentDocAuthUrl){
    option (google.api.http) = {
      post: "/ai/collection/create_tencent_doc_auth_url"
      body: "*"
    };
  }

  // 腾讯文档授权code处理
  rpc AuthTencentCode(ReqAuthTencentCode) returns (RspAuthTencentCode){
    option (google.api.http) = {
      post: "/ai/collection/auth_tencent_code"
      body: "*"
    };
  }

  // 查询腾讯文档缓存是否为空
  rpc DescribeTencentToken(ReqDescribeTencentToken) returns (RspDescribeTencentToken){
    option (google.api.http) = {
      post: "/ai/collection/describe_tencent_token"
      body: "*"
    };
  }

  // 查询腾讯文档授权列表
  rpc ListExternalSourceUser(ReqListExternalSourceUser) returns (RspListExternalSourceUser){
    option (google.api.http) = {
      post: "/ai/collection/list_external_source_user"
      body: "*"
    };
  }

  // 查询选中的腾讯文档file_id
  rpc DescribeMyDoc(ReqDescribeMyDoc) returns (RspDescribeMyDoc){
    option (google.api.http) = {
      post: "/ai/collection/describe_my_doc"
      body: "*"
    };
  }

  // 获取腾讯文档列表
  rpc DescribeDocList(ReqDescribeDocList) returns (RspDescribeDocList){
    option (google.api.http) = {
      post: "/ai/collection/describe_doc_list"
      body: "*"
    };
  }

  // 查询腾讯文档任务状态
  rpc DescribeTencentDocTask (ReqDescribeTencentDocTask) returns (RspDescribeTencentDocTask){
    option (google.api.http) = {
      post: "/ai/collection/describe_tencent_doc_task"
      body: "*"
    };
  }

  // 删除腾讯文档授权
  rpc DelTencentDocAuth (ReqDelTencentDocAuth) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/ai/collection/del_tencent_doc_auth"
      body: "*"
    };
  }

  // 腾讯文档导入
  rpc ImportTencentDoc(ReqImportTencentDoc) returns (RspImportTencentDoc){
    option (google.api.http) = {
      post: "/ai/collection/import_tencent_doc"
      body: "*"
    };
  }

  // 重新导入腾讯文档
  rpc ReimportTencentDoc(ReqReimportTencentDoc) returns (RspReimportTencentDoc){
    option (google.api.http) = {
      post: "/ai/collection/reimport_tencent_doc"
      body: "*"
    };
  }

  // 导入腾讯文档网页剪辑
  rpc ImportTencentDocWebClip(ReqImportTencentDocWebClip) returns (RspImportTencentDocWebClip){
    option (google.api.http) = {
      post: "/ai/collection/import_tencent_doc_webclip"
      body: "*"
    };
  }

  // 创建文档标签
  rpc ModifyDocTab(ReqModifyDocTab) returns (RspModifyDocTab){
    option (google.api.http) = {
      post: "/ai/collection/modify_doc_tab"
      body: "*"
    };
  }

  // 获取文档标签
  rpc DescribeDocTab(ReqDescribeDocTab) returns (RspDescribeDocTab){
    option (google.api.http) = {
      post: "/ai/collection/describe_doc_tab"
      body: "*"
    };
  }

  // 批量导入绿技行
  rpc CreateGTBDocText(ReqCreateGTBDocText) returns (RspCreateGTBDocText){
    option (google.api.http) = {
      post: "/ai/collection/create_gtb_doc_text"
      body: "*"
    };
  }

  // 查询是否为绿技行用户
  rpc DescribeAccountIsGTB(ReqDescribeAccountIsGTB) returns (RspDescribeAccountIsGTB){
    option (google.api.http) = {
      post: "/ai/collection/describe_account_is_gtb"
      body: "*"
    };
  }

  // 克隆QA/文本/文件
  rpc CloneDoc(ReqCloneDoc) returns (RspCloneDoc){
    option (google.api.http) = {
      post: "/ai/collection/clone_doc"
      body: "*"
    };
  }

  // 启用/禁用doc
  rpc OnOffDocs(ReqOnOffDocs) returns (RspOnOffDocs){
    option (google.api.http) = {
      post: "/ai/collection/onoff_docs"
      body: "*"
    };
  }

  // 获取人工坐席列表
  rpc ListChatLiveAgent(ReqListChatLiveAgent) returns (RspListChatLiveAgent){
    option (google.api.http) = {
      post: "/ai/list_chat_live_agent"
      body: "*"
    };
  }

  // 切换人工坐席
  rpc SwitchChatLiveAgent(ReqSwitchChatLiveAgent) returns (RspSwitchChatLiveAgent){
    option (google.api.http) = {
      post: "/ai/switch_chat_live_agent"
      body: "*"
    };
  }

  // 查询用户反馈列表
  rpc GetFeedbacks(ReqGetFeedbacks) returns (RspGetFeedbacks){
    option (google.api.http) = {
      post: "/ai/get_feedbacks"
      body: "*"
    };
  }

  // 查询用户反馈详情
  rpc FindFeedback(ReqFindFeedback) returns (RspFindFeedback){
    option (google.api.http) = {
      post: "/ai/find_feedback"
      body: "*"
    };
  }

  // 采用用户反馈
  rpc AcceptFeedback(ReqAcceptFeedback) returns (RspAcceptFeedback){
    option (google.api.http) = {
      post: "/ai/accept_feedback"
      body: "*"
    };
  }

  // 获取所有会话的地区编码
  rpc DescribeChatRegionCode(ReqDescribeChatRegionCode) returns (RspDescribeChatRegionCode){
    option (google.api.http) = {
      post: "/ai/describe_chat_region_code"
      body: "*"
    };
  }

  // 获取所有教学反馈的地区编码
  rpc DescribeFeedbackRegionCode(ReqDescribeFeedbackRegionCode) returns (RspDescribeFeedbackRegionCode){
    option (google.api.http) = {
      post: "/ai/describe_feedback_region_code"
      body: "*"
    };
  }

  // 查询用户反馈日志列表
  rpc GetFeedbackLogs(ReqGetFeedbackLogs) returns (RspGetFeedbackLogs){
    option (google.api.http) = {
      post: "/ai/get_feedback_logs"
      body: "*"
    };
  }

  // 搜索chat
  rpc SearchChat(ReqSearchChat) returns (RspSearchChat){
    option (google.api.http) = {
      post: "/ai/search_chat"
      body: "*"
    };
  }

  // 获取助手url网页title
  rpc ProxyChatHtmlUrl(ReqProxyChatHtmlUrl) returns (RspProxyChatHtmlUrl){
    option (google.api.http) = {
      post: "/ai/proxy_chat_url"
      body: "*"
    };
  }

  // 确认AI服务协议
  rpc ConfirmAiServiceTerms(ReqConfirmAiServiceTerms) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/confirm_ai_service_terms"
      body: "*"
    };
  }

  // 获取我的AI服务协议确认情况
  rpc GetMyAiServiceTermsConfirmation(google.protobuf.Empty) returns (RspGetMyAiServiceTermsConfirmation) {
    option (google.api.http) = {
      post: "/ai/get_my_ai_service_terms_confirmation"
      body: "*"
    };
  }

  // 获取我的助手列表
  rpc GetMyAssistants(ReqGetMyAssistants) returns (RspGetMyAssistants) {
    option (google.api.http) = {
      post: "/ai/get_my_assistants"
      body: "*"
    };
  }

  // 获取我团队的助手列表
  rpc GetMyTeamAssistants(ReqGetMyAssistants) returns (RspGetMyAssistants) {
    option (google.api.http) = {
      post: "/ai/get_my_assistants_team"
      body: "*"
    };
  }

  // 更新我的助手
  rpc UpdateMyAssistant(ReqUpdateMyAssistant) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/update_my_assistant"
      body: "*"
    };
  }

  // 更新我团队的助手
  rpc UpdateMyTeamAssistant(ReqUpdateMyAssistant) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/ai/update_my_assistant_team"
      body: "*"
    };
  }

  // 获取助手分段配置
  rpc GetAssistantChunkConfig(ReqGetAssistantChunkConfig) returns (RspGetAssistantChunkConfig) {
    option (google.api.http) = {
      post: "/ai/get_assistant_chunk_config"
      body: "*"
    };
  }

  // 查询文档分段信息
  rpc GetDocChunks(ReqGetDocChunks) returns (RspGetDocChunks) {
    option (google.api.http) = {
      post: "/ai/get_doc_chunks"
      body: "*"
    };
  }

  // 自动文档分段
  rpc AutoChunkDoc(ReqAutoChunkDoc) returns (RspAutoChunkDoc) {
    option (google.api.http) = {
      post: "/ai/auto_chunk_doc"
      body: "*"
    };
  }

  // 手动文档分段
  rpc ManualChunkDoc(ReqManualChunkDoc) returns (RspManualChunkDoc) {
    option (google.api.http) = {
      post: "/ai/manual_chunk_doc"
      body: "*"
    };
  }

  // 查询文档分段任务列表
  rpc GetChunkDocTasks(ReqGetChunkDocTasks) returns (RspGetChunkDocTasks) {
    option (google.api.http) = {
      post: "/ai/get_chunk_doc_tasks"
      body: "*"
    };
  }

  // 查询文档的向量化模型
  rpc GetDocEmbeddingModels(ReqGetDocEmbeddingModels) returns (RspGetDocEmbeddingModels) {
    option (google.api.http) = {
      post: "/ai/get_doc_embedding_models"
      body: "*"
    };
  }

  // 小程序获取助手配置
  rpc GetAssistantsMiniprogram(ReqGetAssistantsMiniprogram) returns (RspGetAssistantsMiniprogram) {
    option (google.api.http) = {
      post: "/ai/get_assistants_miniprogram"
      body: "*"
    };
  }

  // 查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
  rpc GetTextFileTip(ReqGetTextFileTip) returns (RspGetTextFileTip) {
    option (google.api.http) = {
      post: "/ai/collection/get_text_file_tip"
      body: "*"
    };
  }

  // 查询QA的知识提示（问题超长，内容重复）等信息
  rpc GetQaTip(ReqGetQaTip) returns (RspGetQaTip) {
    option (google.api.http) = {
      post: "/ai/collection/get_qa_tip"
      body: "*"
    };
  }

  // 创建星云任务
  rpc CreateNebulaTask(ReqCreateNebulaTask) returns (RspCreateNebulaTask) {
    option (google.api.http) = {
      post: "/ai/nebula/create_task"
      body: "*"
    };
  }

  // 查看星云任务详情
  rpc DescribeNebulaTask(ReqDescribeNebulaTask) returns (RspDescribeNebulaTask) {
    option (google.api.http) = {
      post: "/ai/nebula/describe_task"
      body: "*"
    };
  }

  // 查看星云任务列表
  rpc DescribeNebulaTaskList (ReqDescribeNebulaTaskList) returns (RspDescribeNebulaTaskList) {
    option (google.api.http) = {
      post: "/ai/nebula/describe_task_list"
      body: "*"
    };
  }

  // 查看投影坐标
  rpc DescribeNebulaProjection (ReqDescribeNebulaProjection) returns (RspDescribeNebulaProjection) {
    option (google.api.http) = {
      post: "/ai/nebula/describe_projection"
      body: "*"
    };
  }

  // 查看投影元数据
  rpc DescribeNebulaData (ReqDescribeNebulaData) returns (RspDescribeNebulaData) {
    option (google.api.http) = {
      post: "/ai/nebula/describe_medata"
      body: "*"
    };
  }

  // 知识星云，获取助手列表
  rpc ListNebulaAssistants(ReqListNebulaAssistants) returns (RspListNebulaAssistants) {
    option (google.api.http) = {
      post: "/ai/nebula/list_assistants"
      body: "*"
    };
  }

  // 知识星云，获取助手下的贡献者筛选项
  rpc ListNebulaContributors(ReqListNebulaContributors) returns (RspListNebulaContributors) {
    option (google.api.http) = {
      post: "/ai/nebula/list_contributors"
      body: "*"
    };
  }

  // 创建聊天分享
  rpc CreateChatShare(ReqCreateChatShare) returns (RspCreateChatShare) {
    option (google.api.http) = {
      post: "/ai/chat_share_create"
      body: "*"
    };
  }

  // 从分享继续聊天
  rpc ContinueChatFromShare(ReqContinueChatFromShare) returns (RspContinueChatFromShare) {
    option (google.api.http) = {
      post: "/ai/chat_share_continue"
      body: "*"
    };
  }

  // 查询可分享的团队列表
  rpc ListTeamCanShareDoc(ReqListTeamCanShareDoc) returns (RspListTeamCanShareDoc){
    option (google.api.http) = {
      post: "/ai/list_team_to_share_doc"
      body: "*"
    };
  }

  // 查询可分享的个人列表
  rpc ListUserCanShareDoc(ReqListUserCanShareDoc) returns (RspListUserCanShareDoc){
    option (google.api.http) = {
      post: "/ai/list_user_to_share_doc"
      body: "*"
    };
  }
}

// AI模块游客接口
service AiGuestBff {
  option (tanlive.bff) = true;

  // 获取助手配置
  rpc GetAssistantConfig(ReqGetAssistantConfig) returns (RspGetAssistantConfig) {
    option (google.api.http) = {
      post: "/ai/get_assistant_config"
      body: "*"
    };
  }

  // 获取助手下拉选项
  rpc GetAssistantOptions(google.protobuf.Empty) returns (RspGetAssistantOptions) {
    option (google.api.http) = {
      post: "/ai/get_assistant_options"
      body: "*"
    };
  }

  // 获取公开分享详情
  rpc GetPublicChatShare(ReqGetPublicChatShare) returns (RspGetPublicChatShare) {
    option (google.api.http) = {
      post: "/ai/chat_share_get"
      body: "*"
    };
  }
}

// 创建聊天分享请求
message ReqCreateChatShare {
  // 会话ID
  uint64 chat_id = 1 [(validator) = "required"];
  // 要分享的消息ID列表
  repeated uint64 message_ids = 2 [(validator) = "required,dive,required"];
  // 分享类型
  ShareType share_type = 3;
  // 过期天数，0表示永久有效
  int32 expire_days = 4;
}

// 创建聊天分享响应
message RspCreateChatShare {
  // 分享ID
  string share_id = 1;
}

// 从分享继续聊天请求
message ReqContinueChatFromShare {
  // 分享ID
  string share_id = 1 [(validator) = "required"];
  tanlive.ai.ChatType chat_type = 2 [(validator) = "required"];
  // 助手ID
  uint64 assistant_id = 3 [(validator) = "required"];
}

// 从分享继续聊天响应
message RspContinueChatFromShare {
  // 新会话ID
  uint64 chat_id = 1;
  // 会话标题
  string title = 2;
  // 助手ID
  uint64 assistant_id = 3;
}

// 更新分享状态请求
message ReqUpdateChatShareStatus {
  // 分享ID
  string share_id = 1 [(validator) = "required"];
  // 新状态
  ShareStatus status = 2 [(validator) = "required"];
}

// 获取公开分享详情请求（无需登录）
message ReqGetPublicChatShare {
  // 分享ID
  string share_id = 1 [(validator) = "required"];
}

// 获取公开分享详情响应
message RspGetPublicChatShare {
  // 分享ID
  string share_id = 1;
  // 原会话ID
  uint64 chat_id = 2;
  // 助手ID
  uint64 assistant_id = 3;
  // 分享类型
  ShareType share_type = 4;
  // 分享状态
  ShareStatus share_status = 5;
  // 分享者ID
  uint64 shared_by = 6;
  // 访问次数
  int32 access_count = 7;
  // 分享创建时间
  google.protobuf.Timestamp share_date = 8;
  // 分享过期时间
  google.protobuf.Timestamp expire_date = 9;
  // 最后访问时间
  google.protobuf.Timestamp last_access_time = 10;
  // 分享的消息列表
  repeated tanlive.ai.EventChatMessage messages = 11;
}
