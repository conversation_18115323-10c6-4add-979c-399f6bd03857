// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/search/search.proto

package search

import (
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 搜索筛选项
type SearchOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 筛选项名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 筛选项关联的id，比如图谱id
	ReferId int64 `protobuf:"varint,2,opt,name=refer_id,json=referId,proto3" json:"refer_id,omitempty"`
}

func (x *SearchOption) Reset() {
	*x = SearchOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_search_search_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchOption) ProtoMessage() {}

func (x *SearchOption) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_search_search_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchOption.ProtoReflect.Descriptor instead.
func (*SearchOption) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_search_search_proto_rawDescGZIP(), []int{0}
}

func (x *SearchOption) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchOption) GetReferId() int64 {
	if x != nil {
		return x.ReferId
	}
	return 0
}

// 搜索提示语
type SearchPrompt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *SearchPrompt) Reset() {
	*x = SearchPrompt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_search_search_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPrompt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPrompt) ProtoMessage() {}

func (x *SearchPrompt) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_search_search_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPrompt.ProtoReflect.Descriptor instead.
func (*SearchPrompt) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_search_search_proto_rawDescGZIP(), []int{1}
}

func (x *SearchPrompt) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

var File_tanlive_bff_web_search_search_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_search_search_proto_rawDesc = []byte{
	0x0a, 0x23, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x17, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3d, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42,
	0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f,
	0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_web_search_search_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_search_search_proto_rawDescData = file_tanlive_bff_web_search_search_proto_rawDesc
)

func file_tanlive_bff_web_search_search_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_search_search_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_search_search_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_search_search_proto_rawDescData)
	})
	return file_tanlive_bff_web_search_search_proto_rawDescData
}

var file_tanlive_bff_web_search_search_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tanlive_bff_web_search_search_proto_goTypes = []interface{}{
	(*SearchOption)(nil), // 0: tanlive.bff_web.search.SearchOption
	(*SearchPrompt)(nil), // 1: tanlive.bff_web.search.SearchPrompt
}
var file_tanlive_bff_web_search_search_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_search_search_proto_init() }
func file_tanlive_bff_web_search_search_proto_init() {
	if File_tanlive_bff_web_search_search_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_search_search_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_search_search_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPrompt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_search_search_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_bff_web_search_search_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_search_search_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_web_search_search_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_search_search_proto = out.File
	file_tanlive_bff_web_search_search_proto_rawDesc = nil
	file_tanlive_bff_web_search_search_proto_goTypes = nil
	file_tanlive_bff_web_search_search_proto_depIdxs = nil
}
