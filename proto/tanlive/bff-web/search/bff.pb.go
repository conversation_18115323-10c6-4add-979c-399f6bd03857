// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/search/bff.proto

package search

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	search "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/search"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqDescribeAtlasSearchOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索对应的模块类型 1:团队,2:产品
	TargetType search.SearchOptionTargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=tanlive.search.SearchOptionTargetType" json:"target_type,omitempty"`
}

func (x *ReqDescribeAtlasSearchOptions) Reset() {
	*x = ReqDescribeAtlasSearchOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_search_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeAtlasSearchOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeAtlasSearchOptions) ProtoMessage() {}

func (x *ReqDescribeAtlasSearchOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_search_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeAtlasSearchOptions.ProtoReflect.Descriptor instead.
func (*ReqDescribeAtlasSearchOptions) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_search_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqDescribeAtlasSearchOptions) GetTargetType() search.SearchOptionTargetType {
	if x != nil {
		return x.TargetType
	}
	return search.SearchOptionTargetType(0)
}

type RspDescribeAtlasSearchOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Options []*SearchOption `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
}

func (x *RspDescribeAtlasSearchOptions) Reset() {
	*x = RspDescribeAtlasSearchOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_search_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeAtlasSearchOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeAtlasSearchOptions) ProtoMessage() {}

func (x *RspDescribeAtlasSearchOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_search_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeAtlasSearchOptions.ProtoReflect.Descriptor instead.
func (*RspDescribeAtlasSearchOptions) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_search_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspDescribeAtlasSearchOptions) GetOptions() []*SearchOption {
	if x != nil {
		return x.Options
	}
	return nil
}

type ReqDescribeSearchPrompts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索对应的模块类型
	TargetType search.SearchOptionTargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=tanlive.search.SearchOptionTargetType" json:"target_type,omitempty"`
	// 搜索对应的筛选项类型
	ReferType search.SearchOptionReferType `protobuf:"varint,2,opt,name=refer_type,json=referType,proto3,enum=tanlive.search.SearchOptionReferType" json:"refer_type,omitempty"`
}

func (x *ReqDescribeSearchPrompts) Reset() {
	*x = ReqDescribeSearchPrompts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_search_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSearchPrompts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSearchPrompts) ProtoMessage() {}

func (x *ReqDescribeSearchPrompts) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_search_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSearchPrompts.ProtoReflect.Descriptor instead.
func (*ReqDescribeSearchPrompts) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_search_bff_proto_rawDescGZIP(), []int{2}
}

func (x *ReqDescribeSearchPrompts) GetTargetType() search.SearchOptionTargetType {
	if x != nil {
		return x.TargetType
	}
	return search.SearchOptionTargetType(0)
}

func (x *ReqDescribeSearchPrompts) GetReferType() search.SearchOptionReferType {
	if x != nil {
		return x.ReferType
	}
	return search.SearchOptionReferType(0)
}

type RspDescribeSearchPrompts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prompts *SearchPrompt `protobuf:"bytes,1,opt,name=prompts,proto3" json:"prompts,omitempty"`
}

func (x *RspDescribeSearchPrompts) Reset() {
	*x = RspDescribeSearchPrompts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_search_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSearchPrompts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSearchPrompts) ProtoMessage() {}

func (x *RspDescribeSearchPrompts) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_search_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSearchPrompts.ProtoReflect.Descriptor instead.
func (*RspDescribeSearchPrompts) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_search_bff_proto_rawDescGZIP(), []int{3}
}

func (x *RspDescribeSearchPrompts) GetPrompts() *SearchPrompt {
	if x != nil {
		return x.Prompts
	}
	return nil
}

var File_tanlive_bff_web_search_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_search_bff_proto_rawDesc = []byte{
	0x0a, 0x20, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x68, 0x0a, 0x1d, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5f, 0x0a, 0x1d, 0x52,
	0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa9, 0x01, 0x0a,
	0x18, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5a, 0x0a, 0x18, 0x52, 0x73, 0x70, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x73, 0x12, 0x3e, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x52, 0x07, 0x70, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x73, 0x32, 0x11, 0x0a, 0x09, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x66,
	0x66, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x32, 0xfd, 0x02, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x47, 0x75, 0x65, 0x73, 0x74, 0x42, 0x66, 0x66, 0x12, 0xbb, 0x01, 0x0a, 0x1a, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74,
	0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x1a, 0x35, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a,
	0x01, 0x2a, 0x22, 0x24, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x5f, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xa6, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x73, 0x12, 0x30, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x73, 0x1a, 0x30, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73,
	0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01,
	0x2a, 0x22, 0x1e, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77,
	0x65, 0x62, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_tanlive_bff_web_search_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_search_bff_proto_rawDescData = file_tanlive_bff_web_search_bff_proto_rawDesc
)

func file_tanlive_bff_web_search_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_search_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_search_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_search_bff_proto_rawDescData)
	})
	return file_tanlive_bff_web_search_bff_proto_rawDescData
}

var file_tanlive_bff_web_search_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_tanlive_bff_web_search_bff_proto_goTypes = []interface{}{
	(*ReqDescribeAtlasSearchOptions)(nil), // 0: tanlive.bff_web.search.ReqDescribeAtlasSearchOptions
	(*RspDescribeAtlasSearchOptions)(nil), // 1: tanlive.bff_web.search.RspDescribeAtlasSearchOptions
	(*ReqDescribeSearchPrompts)(nil),      // 2: tanlive.bff_web.search.ReqDescribeSearchPrompts
	(*RspDescribeSearchPrompts)(nil),      // 3: tanlive.bff_web.search.RspDescribeSearchPrompts
	(search.SearchOptionTargetType)(0),    // 4: tanlive.search.SearchOptionTargetType
	(*SearchOption)(nil),                  // 5: tanlive.bff_web.search.SearchOption
	(search.SearchOptionReferType)(0),     // 6: tanlive.search.SearchOptionReferType
	(*SearchPrompt)(nil),                  // 7: tanlive.bff_web.search.SearchPrompt
}
var file_tanlive_bff_web_search_bff_proto_depIdxs = []int32{
	4, // 0: tanlive.bff_web.search.ReqDescribeAtlasSearchOptions.target_type:type_name -> tanlive.search.SearchOptionTargetType
	5, // 1: tanlive.bff_web.search.RspDescribeAtlasSearchOptions.options:type_name -> tanlive.bff_web.search.SearchOption
	4, // 2: tanlive.bff_web.search.ReqDescribeSearchPrompts.target_type:type_name -> tanlive.search.SearchOptionTargetType
	6, // 3: tanlive.bff_web.search.ReqDescribeSearchPrompts.refer_type:type_name -> tanlive.search.SearchOptionReferType
	7, // 4: tanlive.bff_web.search.RspDescribeSearchPrompts.prompts:type_name -> tanlive.bff_web.search.SearchPrompt
	0, // 5: tanlive.bff_web.search.SearchGuestBff.DescribeAtlasSearchOptions:input_type -> tanlive.bff_web.search.ReqDescribeAtlasSearchOptions
	2, // 6: tanlive.bff_web.search.SearchGuestBff.DescribeSearchPrompts:input_type -> tanlive.bff_web.search.ReqDescribeSearchPrompts
	1, // 7: tanlive.bff_web.search.SearchGuestBff.DescribeAtlasSearchOptions:output_type -> tanlive.bff_web.search.RspDescribeAtlasSearchOptions
	3, // 8: tanlive.bff_web.search.SearchGuestBff.DescribeSearchPrompts:output_type -> tanlive.bff_web.search.RspDescribeSearchPrompts
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_search_bff_proto_init() }
func file_tanlive_bff_web_search_bff_proto_init() {
	if File_tanlive_bff_web_search_bff_proto != nil {
		return
	}
	file_tanlive_bff_web_search_search_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_search_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeAtlasSearchOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_search_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeAtlasSearchOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_search_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSearchPrompts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_search_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSearchPrompts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_search_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_tanlive_bff_web_search_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_search_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_web_search_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_search_bff_proto = out.File
	file_tanlive_bff_web_search_bff_proto_rawDesc = nil
	file_tanlive_bff_web_search_bff_proto_goTypes = nil
	file_tanlive_bff_web_search_bff_proto_depIdxs = nil
}
