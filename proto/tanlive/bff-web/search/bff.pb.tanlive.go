// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package search

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	proto "google.golang.org/protobuf/proto"
	http "net/http"
)

func (x *RspDescribeAtlasSearchOptions) MaskInLog() any {
	if x == nil {
		return (*RspDescribeAtlasSearchOptions)(nil)
	}

	y := proto.Clone(x).(*RspDescribeAtlasSearchOptions)
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Options[k] = vv.MaskInLog().(*SearchOption)
		}
	}

	return y
}

func (x *RspDescribeAtlasSearchOptions) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeAtlasSearchOptions)(nil)
	}

	y := x
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Options[k] = vv.MaskInRpc().(*SearchOption)
		}
	}

	return y
}

func (x *RspDescribeAtlasSearchOptions) MaskInBff() any {
	if x == nil {
		return (*RspDescribeAtlasSearchOptions)(nil)
	}

	y := x
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Options[k] = vv.MaskInBff().(*SearchOption)
		}
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSearchPrompts)
	if v, ok := any(y.Prompts).(interface{ MaskInLog() any }); ok {
		y.Prompts = v.MaskInLog().(*SearchPrompt)
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := x
	if v, ok := any(y.Prompts).(interface{ MaskInRpc() any }); ok {
		y.Prompts = v.MaskInRpc().(*SearchPrompt)
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := x
	if v, ok := any(y.Prompts).(interface{ MaskInBff() any }); ok {
		y.Prompts = v.MaskInBff().(*SearchPrompt)
	}

	return y
}

func (x *RspDescribeAtlasSearchOptions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Options {
		if sanitizer, ok := any(x.Options[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeSearchPrompts) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Prompts).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

type SearchBffHandler interface {
}

func RegisterSearchBff(s bff.Server, h SearchBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Search"), xhttp.GroupPrefix(""))
	return group
}

type SearchGuestBffHandler interface {
	// 查询所有的图谱搜索筛选项
	DescribeAtlasSearchOptions(context.Context, *ReqDescribeAtlasSearchOptions, *RspDescribeAtlasSearchOptions) error
	// 查询搜索提示语
	DescribeSearchPrompts(context.Context, *ReqDescribeSearchPrompts, *RspDescribeSearchPrompts) error
}

func RegisterSearchGuestBff(s bff.Server, h SearchGuestBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("SearchGuest"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/search/describe_atlas_search_option", h.DescribeAtlasSearchOptions).Name("DescribeAtlasSearchOptions")
	bff.AddRoute(group, http.MethodPost, "/search/describe_search_prompt", h.DescribeSearchPrompts).Name("DescribeSearchPrompts")
	return group
}
