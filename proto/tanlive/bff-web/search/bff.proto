syntax = "proto3";

package tanlive.bff_web.search;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/search";

import "google/api/annotations.proto";
import "tanlive/bff-web/search/search.proto";
import "tanlive/options.proto";
import "tanlive/search/search.proto";

// search模块接口
service SearchBff {
  option (tanlive.bff) = true;
}

// search模块游客接口
service SearchGuestBff {
  option (tanlive.bff) = true;

  // 查询所有的图谱搜索筛选项
  rpc DescribeAtlasSearchOptions(ReqDescribeAtlasSearchOptions) returns (RspDescribeAtlasSearchOptions){
    option (google.api.http) = {
      post: "/search/describe_atlas_search_option"
      body: "*"
    };
  };

  // 查询搜索提示语
  rpc DescribeSearchPrompts(ReqDescribeSearchPrompts) returns (RspDescribeSearchPrompts){
    option (google.api.http) = {
      post: "/search/describe_search_prompt"
      body: "*"
    };
  };
}

message ReqDescribeAtlasSearchOptions{
  // 搜索对应的模块类型 1:团队,2:产品
  tanlive.search.SearchOptionTargetType target_type = 1;
}

message RspDescribeAtlasSearchOptions{
  repeated SearchOption options = 1;
}

message ReqDescribeSearchPrompts{
  // 搜索对应的模块类型
  tanlive.search.SearchOptionTargetType target_type = 1;
  // 搜索对应的筛选项类型
  tanlive.search.SearchOptionReferType refer_type = 2;
}

message RspDescribeSearchPrompts{
  SearchPrompt prompts = 1;
}