// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package team

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Keyword": "required",
	}, &ReqSearchTeamsInAiShareSetting{})
}

func (x *ReqSearchTeamsInAiShareSetting) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspSearchTeamsInAiShareSetting) MaskInLog() any {
	if x == nil {
		return (*RspSearchTeamsInAiShareSetting)(nil)
	}

	y := proto.Clone(x).(*RspSearchTeamsInAiShareSetting)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*FullTeam)
		}
	}

	return y
}

func (x *RspSearchTeamsInAiShareSetting) MaskInRpc() any {
	if x == nil {
		return (*RspSearchTeamsInAiShareSetting)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*FullTeam)
		}
	}

	return y
}

func (x *RspSearchTeamsInAiShareSetting) MaskInBff() any {
	if x == nil {
		return (*RspSearchTeamsInAiShareSetting)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*FullTeam)
		}
	}

	return y
}

func (x *RspSearchTeamsInAiShareSetting) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type TeamBffHandler interface {
	// 更新ugc自定义路由
	UpdateUgcCustomRoute(context.Context, *ReqUpdateUgcCustomRoute, *emptypb.Empty) error
	// 搜索团队（AI知识分享默认设置场景）
	SearchTeamsInAiShareSetting(context.Context, *ReqSearchTeamsInAiShareSetting, *RspSearchTeamsInAiShareSetting) error
}

func RegisterTeamBff(s bff.Server, h TeamBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Team"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/team/update_ugc_custom_route", h.UpdateUgcCustomRoute).Name("UpdateUgcCustomRoute")
	bff.AddRoute(group, http.MethodPost, "/team/search_teams_in_ai_share_setting", h.SearchTeamsInAiShareSetting).Name("SearchTeamsInAiShareSetting")
	return group
}
