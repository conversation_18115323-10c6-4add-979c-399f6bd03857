// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package team

import (
	team "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	proto "google.golang.org/protobuf/proto"
)

func (x *FullTeam) MaskInLog() any {
	if x == nil {
		return (*FullTeam)(nil)
	}

	y := proto.Clone(x).(*FullTeam)
	if v, ok := any(y.TeamInfo).(interface{ MaskInLog() any }); ok {
		y.TeamInfo = v.MaskInLog().(*team.TeamInfo)
	}

	return y
}

func (x *FullTeam) MaskInRpc() any {
	if x == nil {
		return (*FullTeam)(nil)
	}

	y := x
	if v, ok := any(y.TeamInfo).(interface{ MaskInRpc() any }); ok {
		y.TeamInfo = v.MaskInRpc().(*team.TeamInfo)
	}

	return y
}

func (x *FullTeam) MaskInBff() any {
	if x == nil {
		return (*FullTeam)(nil)
	}

	y := x
	if v, ok := any(y.TeamInfo).(interface{ MaskInBff() any }); ok {
		y.TeamInfo = v.MaskInBff().(*team.TeamInfo)
	}

	return y
}

func (x *FullTeam) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TeamInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
