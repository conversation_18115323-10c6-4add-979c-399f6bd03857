// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/team/bff.proto

package team

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqUpdateUgcCustomRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UgcId    uint64 `protobuf:"varint,1,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
	UgcRoute string `protobuf:"bytes,2,opt,name=ugc_route,json=ugcRoute,proto3" json:"ugc_route,omitempty"`
}

func (x *ReqUpdateUgcCustomRoute) Reset() {
	*x = ReqUpdateUgcCustomRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_team_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateUgcCustomRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateUgcCustomRoute) ProtoMessage() {}

func (x *ReqUpdateUgcCustomRoute) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_team_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateUgcCustomRoute.ProtoReflect.Descriptor instead.
func (*ReqUpdateUgcCustomRoute) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_team_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqUpdateUgcCustomRoute) GetUgcId() uint64 {
	if x != nil {
		return x.UgcId
	}
	return 0
}

func (x *ReqUpdateUgcCustomRoute) GetUgcRoute() string {
	if x != nil {
		return x.UgcRoute
	}
	return ""
}

type ReqSearchTeamsInAiShareSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索关键词
	Keyword string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// 分页偏移量
	Offset uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	// 分页大小
	Limit uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *ReqSearchTeamsInAiShareSetting) Reset() {
	*x = ReqSearchTeamsInAiShareSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_team_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchTeamsInAiShareSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchTeamsInAiShareSetting) ProtoMessage() {}

func (x *ReqSearchTeamsInAiShareSetting) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_team_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchTeamsInAiShareSetting.ProtoReflect.Descriptor instead.
func (*ReqSearchTeamsInAiShareSetting) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_team_bff_proto_rawDescGZIP(), []int{1}
}

func (x *ReqSearchTeamsInAiShareSetting) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ReqSearchTeamsInAiShareSetting) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqSearchTeamsInAiShareSetting) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type RspSearchTeamsInAiShareSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队列表
	Teams []*FullTeam `protobuf:"bytes,1,rep,name=teams,proto3" json:"teams,omitempty"`
	// 总数
	TotalCount uint32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspSearchTeamsInAiShareSetting) Reset() {
	*x = RspSearchTeamsInAiShareSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_team_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSearchTeamsInAiShareSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSearchTeamsInAiShareSetting) ProtoMessage() {}

func (x *RspSearchTeamsInAiShareSetting) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_team_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSearchTeamsInAiShareSetting.ProtoReflect.Descriptor instead.
func (*RspSearchTeamsInAiShareSetting) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_team_bff_proto_rawDescGZIP(), []int{2}
}

func (x *RspSearchTeamsInAiShareSetting) GetTeams() []*FullTeam {
	if x != nil {
		return x.Teams
	}
	return nil
}

func (x *RspSearchTeamsInAiShareSetting) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_tanlive_bff_web_team_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_team_bff_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x14, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65,
	0x62, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77,
	0x65, 0x62, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4d, 0x0a, 0x17, 0x52, 0x65, 0x71,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x67, 0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75,
	0x67, 0x63, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x67, 0x63, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x22, 0x76, 0x0a, 0x1e, 0x52, 0x65, 0x71, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x49, 0x6e, 0x41, 0x69, 0x53, 0x68,
	0x61, 0x72, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x0a, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x22, 0x77, 0x0a, 0x1e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61,
	0x6d, 0x73, 0x49, 0x6e, 0x41, 0x69, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x34, 0x0a, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x77, 0x65, 0x62, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x61,
	0x6d, 0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0xd8, 0x02, 0x0a, 0x07, 0x54, 0x65,
	0x61, 0x6d, 0x42, 0x66, 0x66, 0x12, 0x87, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x55, 0x67, 0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x2d,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x67, 0x63, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a,
	0x22, 0x1d, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75,
	0x67, 0x63, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x12,
	0xbc, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x49,
	0x6e, 0x41, 0x69, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x34, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65,
	0x62, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x54, 0x65, 0x61, 0x6d, 0x73, 0x49, 0x6e, 0x41, 0x69, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x1a, 0x34, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x49, 0x6e, 0x41, 0x69, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x31, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x61, 0x69,
	0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x1a, 0x04,
	0xd0, 0xc6, 0x27, 0x01, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76,
	0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65, 0x62, 0x2f,
	0x74, 0x65, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_web_team_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_team_bff_proto_rawDescData = file_tanlive_bff_web_team_bff_proto_rawDesc
)

func file_tanlive_bff_web_team_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_team_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_team_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_team_bff_proto_rawDescData)
	})
	return file_tanlive_bff_web_team_bff_proto_rawDescData
}

var file_tanlive_bff_web_team_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_tanlive_bff_web_team_bff_proto_goTypes = []interface{}{
	(*ReqUpdateUgcCustomRoute)(nil),        // 0: tanlive.bff_web.team.ReqUpdateUgcCustomRoute
	(*ReqSearchTeamsInAiShareSetting)(nil), // 1: tanlive.bff_web.team.ReqSearchTeamsInAiShareSetting
	(*RspSearchTeamsInAiShareSetting)(nil), // 2: tanlive.bff_web.team.RspSearchTeamsInAiShareSetting
	(*FullTeam)(nil),                       // 3: tanlive.bff_web.team.FullTeam
	(*emptypb.Empty)(nil),                  // 4: google.protobuf.Empty
}
var file_tanlive_bff_web_team_bff_proto_depIdxs = []int32{
	3, // 0: tanlive.bff_web.team.RspSearchTeamsInAiShareSetting.teams:type_name -> tanlive.bff_web.team.FullTeam
	0, // 1: tanlive.bff_web.team.TeamBff.UpdateUgcCustomRoute:input_type -> tanlive.bff_web.team.ReqUpdateUgcCustomRoute
	1, // 2: tanlive.bff_web.team.TeamBff.SearchTeamsInAiShareSetting:input_type -> tanlive.bff_web.team.ReqSearchTeamsInAiShareSetting
	4, // 3: tanlive.bff_web.team.TeamBff.UpdateUgcCustomRoute:output_type -> google.protobuf.Empty
	2, // 4: tanlive.bff_web.team.TeamBff.SearchTeamsInAiShareSetting:output_type -> tanlive.bff_web.team.RspSearchTeamsInAiShareSetting
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_team_bff_proto_init() }
func file_tanlive_bff_web_team_bff_proto_init() {
	if File_tanlive_bff_web_team_bff_proto != nil {
		return
	}
	file_tanlive_bff_web_team_team_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_team_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateUgcCustomRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_team_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchTeamsInAiShareSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_team_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSearchTeamsInAiShareSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_team_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_bff_web_team_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_team_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_web_team_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_team_bff_proto = out.File
	file_tanlive_bff_web_team_bff_proto_rawDesc = nil
	file_tanlive_bff_web_team_bff_proto_goTypes = nil
	file_tanlive_bff_web_team_bff_proto_depIdxs = nil
}
