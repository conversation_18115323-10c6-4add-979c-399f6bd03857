// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/team/team.proto

package team

import (
	team "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 完整团队信息
type FullTeam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队信息
	TeamInfo *team.TeamInfo `protobuf:"bytes,1,opt,name=team_info,json=teamInfo,proto3" json:"team_info,omitempty"`
}

func (x *FullTeam) Reset() {
	*x = FullTeam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_team_team_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FullTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullTeam) ProtoMessage() {}

func (x *FullTeam) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_team_team_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullTeam.ProtoReflect.Descriptor instead.
func (*FullTeam) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_team_team_proto_rawDescGZIP(), []int{0}
}

func (x *FullTeam) GetTeamInfo() *team.TeamInfo {
	if x != nil {
		return x.TeamInfo
	}
	return nil
}

var File_tanlive_bff_web_team_team_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_team_team_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x14, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x3f, 0x0a, 0x08, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x33, 0x0a, 0x09,
	0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x54,
	0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65,
	0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x74, 0x65, 0x61,
	0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_web_team_team_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_team_team_proto_rawDescData = file_tanlive_bff_web_team_team_proto_rawDesc
)

func file_tanlive_bff_web_team_team_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_team_team_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_team_team_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_team_team_proto_rawDescData)
	})
	return file_tanlive_bff_web_team_team_proto_rawDescData
}

var file_tanlive_bff_web_team_team_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tanlive_bff_web_team_team_proto_goTypes = []interface{}{
	(*FullTeam)(nil),      // 0: tanlive.bff_web.team.FullTeam
	(*team.TeamInfo)(nil), // 1: tanlive.team.TeamInfo
}
var file_tanlive_bff_web_team_team_proto_depIdxs = []int32{
	1, // 0: tanlive.bff_web.team.FullTeam.team_info:type_name -> tanlive.team.TeamInfo
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_team_team_proto_init() }
func file_tanlive_bff_web_team_team_proto_init() {
	if File_tanlive_bff_web_team_team_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_team_team_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FullTeam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_team_team_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_bff_web_team_team_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_team_team_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_web_team_team_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_team_team_proto = out.File
	file_tanlive_bff_web_team_team_proto_rawDesc = nil
	file_tanlive_bff_web_team_team_proto_goTypes = nil
	file_tanlive_bff_web_team_team_proto_depIdxs = nil
}
