syntax = "proto3";

package tanlive.bff_web.team;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/team";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "tanlive/bff-web/team/team.proto";
import "tanlive/options.proto";

message ReqUpdateUgcCustomRoute {
  uint64 ugc_id = 1;
  string ugc_route=2;
}

message ReqSearchTeamsInAiShareSetting {
  // 搜索关键词
  string keyword = 1 [(validator) = "required"];
  // 分页偏移量
  uint32 offset = 2;
  // 分页大小
  uint32 limit = 3;
}

message RspSearchTeamsInAiShareSetting {
  // 团队列表
  repeated FullTeam teams = 1;
  // 总数
  uint32 total_count = 2;
}

// 团队接口
service TeamBff {
  option (tanlive.bff) = true;

  // 更新ugc自定义路由
  rpc UpdateUgcCustomRoute(ReqUpdateUgcCustomRoute) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/team/update_ugc_custom_route",
      body: "*",
    };
  };

  // 搜索团队（AI知识分享默认设置场景）
  rpc SearchTeamsInAiShareSetting(ReqSearchTeamsInAiShareSetting) returns (RspSearchTeamsInAiShareSetting) {
    option (google.api.http) = {
      post: "/team/search_teams_in_ai_share_setting",
      body: "*",
    };
  };
}
