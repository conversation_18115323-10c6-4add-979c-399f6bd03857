// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package search

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	support "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support"
	proto "google.golang.org/protobuf/proto"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Urls": "required",
	}, &ReqProxy{})
}

func (x *ReqProxy) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Platform": "required",
		"Request":  "required",
	}, &ReqSendMapRequest{})
}

func (x *ReqSendMapRequest) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Name":      "required",
		"DataType":  "required",
		"DataField": "required",
	}, &ReqSearchPlaces{})
}

func (x *ReqSearchPlaces) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspProxy) MaskInLog() any {
	if x == nil {
		return (*RspProxy)(nil)
	}

	y := proto.Clone(x).(*RspProxy)
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contents[k] = vv.MaskInLog().(*RspProxy_Content)
		}
	}

	return y
}

func (x *RspProxy) MaskInRpc() any {
	if x == nil {
		return (*RspProxy)(nil)
	}

	y := x
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contents[k] = vv.MaskInRpc().(*RspProxy_Content)
		}
	}

	return y
}

func (x *RspProxy) MaskInBff() any {
	if x == nil {
		return (*RspProxy)(nil)
	}

	y := x
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contents[k] = vv.MaskInBff().(*RspProxy_Content)
		}
	}

	return y
}

func (x *ReqSendMapRequest) MaskInLog() any {
	if x == nil {
		return (*ReqSendMapRequest)(nil)
	}

	y := proto.Clone(x).(*ReqSendMapRequest)
	if v, ok := any(y.Request).(interface{ MaskInLog() any }); ok {
		y.Request = v.MaskInLog().(*support.MapRequest)
	}

	return y
}

func (x *ReqSendMapRequest) MaskInRpc() any {
	if x == nil {
		return (*ReqSendMapRequest)(nil)
	}

	y := x
	if v, ok := any(y.Request).(interface{ MaskInRpc() any }); ok {
		y.Request = v.MaskInRpc().(*support.MapRequest)
	}

	return y
}

func (x *ReqSendMapRequest) MaskInBff() any {
	if x == nil {
		return (*ReqSendMapRequest)(nil)
	}

	y := x
	if v, ok := any(y.Request).(interface{ MaskInBff() any }); ok {
		y.Request = v.MaskInBff().(*support.MapRequest)
	}

	return y
}

func (x *RspSendMapRequest) MaskInLog() any {
	if x == nil {
		return (*RspSendMapRequest)(nil)
	}

	y := proto.Clone(x).(*RspSendMapRequest)
	if v, ok := any(y.Response).(interface{ MaskInLog() any }); ok {
		y.Response = v.MaskInLog().(*support.MapResponse)
	}

	return y
}

func (x *RspSendMapRequest) MaskInRpc() any {
	if x == nil {
		return (*RspSendMapRequest)(nil)
	}

	y := x
	if v, ok := any(y.Response).(interface{ MaskInRpc() any }); ok {
		y.Response = v.MaskInRpc().(*support.MapResponse)
	}

	return y
}

func (x *RspSendMapRequest) MaskInBff() any {
	if x == nil {
		return (*RspSendMapRequest)(nil)
	}

	y := x
	if v, ok := any(y.Response).(interface{ MaskInBff() any }); ok {
		y.Response = v.MaskInBff().(*support.MapResponse)
	}

	return y
}

func (x *ReqGetPlaceSelector) MaskInLog() any {
	if x == nil {
		return (*ReqGetPlaceSelector)(nil)
	}

	y := proto.Clone(x).(*ReqGetPlaceSelector)
	switch v := y.Condition.(type) {
	case *ReqGetPlaceSelector_ByDataType:
		if vv, ok := any(v.ByDataType).(interface{ MaskInLog() any }); ok {
			v.ByDataType = vv.MaskInLog().(*support.GetPlaceCondByDataType)
		}
	case *ReqGetPlaceSelector_ByPlaceId:
		if vv, ok := any(v.ByPlaceId).(interface{ MaskInLog() any }); ok {
			v.ByPlaceId = vv.MaskInLog().(*support.GetPlaceCondByPlaceId)
		}
	}

	return y
}

func (x *ReqGetPlaceSelector) MaskInRpc() any {
	if x == nil {
		return (*ReqGetPlaceSelector)(nil)
	}

	y := x
	switch v := y.Condition.(type) {
	case *ReqGetPlaceSelector_ByDataType:
		if vv, ok := any(v.ByDataType).(interface{ MaskInRpc() any }); ok {
			v.ByDataType = vv.MaskInRpc().(*support.GetPlaceCondByDataType)
		}
	case *ReqGetPlaceSelector_ByPlaceId:
		if vv, ok := any(v.ByPlaceId).(interface{ MaskInRpc() any }); ok {
			v.ByPlaceId = vv.MaskInRpc().(*support.GetPlaceCondByPlaceId)
		}
	}

	return y
}

func (x *ReqGetPlaceSelector) MaskInBff() any {
	if x == nil {
		return (*ReqGetPlaceSelector)(nil)
	}

	y := x
	switch v := y.Condition.(type) {
	case *ReqGetPlaceSelector_ByDataType:
		if vv, ok := any(v.ByDataType).(interface{ MaskInBff() any }); ok {
			v.ByDataType = vv.MaskInBff().(*support.GetPlaceCondByDataType)
		}
	case *ReqGetPlaceSelector_ByPlaceId:
		if vv, ok := any(v.ByPlaceId).(interface{ MaskInBff() any }); ok {
			v.ByPlaceId = vv.MaskInBff().(*support.GetPlaceCondByPlaceId)
		}
	}

	return y
}

func (x *RspGetPlaceSelector) MaskInLog() any {
	if x == nil {
		return (*RspGetPlaceSelector)(nil)
	}

	y := proto.Clone(x).(*RspGetPlaceSelector)
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Places[k] = vv.MaskInLog().(*support.Place)
		}
	}

	return y
}

func (x *RspGetPlaceSelector) MaskInRpc() any {
	if x == nil {
		return (*RspGetPlaceSelector)(nil)
	}

	y := x
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Places[k] = vv.MaskInRpc().(*support.Place)
		}
	}

	return y
}

func (x *RspGetPlaceSelector) MaskInBff() any {
	if x == nil {
		return (*RspGetPlaceSelector)(nil)
	}

	y := x
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Places[k] = vv.MaskInBff().(*support.Place)
		}
	}

	return y
}

func (x *RspSearchPlaces) MaskInLog() any {
	if x == nil {
		return (*RspSearchPlaces)(nil)
	}

	y := proto.Clone(x).(*RspSearchPlaces)
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Places[k] = vv.MaskInLog().(*support.PlaceWithParents)
		}
	}

	return y
}

func (x *RspSearchPlaces) MaskInRpc() any {
	if x == nil {
		return (*RspSearchPlaces)(nil)
	}

	y := x
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Places[k] = vv.MaskInRpc().(*support.PlaceWithParents)
		}
	}

	return y
}

func (x *RspSearchPlaces) MaskInBff() any {
	if x == nil {
		return (*RspSearchPlaces)(nil)
	}

	y := x
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Places[k] = vv.MaskInBff().(*support.PlaceWithParents)
		}
	}

	return y
}

func (x *RspProxy) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contents {
		if sanitizer, ok := any(x.Contents[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqSendMapRequest) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Request).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSendMapRequest) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Response).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetPlaceSelector) SanitizeXSS() {
	if x == nil {
		return
	}

	switch oneof := x.Condition.(type) {
	case *ReqGetPlaceSelector_ByDataType:
		if sanitizer, ok := any(oneof.ByDataType).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Condition = oneof
	case *ReqGetPlaceSelector_ByPlaceId:
		if sanitizer, ok := any(oneof.ByPlaceId).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Condition = oneof
	}
}

func (x *RspGetPlaceSelector) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Places {
		if sanitizer, ok := any(x.Places[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspSearchPlaces) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Places {
		if sanitizer, ok := any(x.Places[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type SupportBffHandler interface {
}

func RegisterSupportBff(s bff.Server, h SupportBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Support"), xhttp.GroupPrefix(""))
	return group
}

type SupportGuestBffHandler interface {
	// 发送地图请求
	SendMapRequest(context.Context, *ReqSendMapRequest, *RspSendMapRequest) error
	// 查询地点选择器数据
	GetPlaceSelector(context.Context, *ReqGetPlaceSelector, *RspGetPlaceSelector) error
	// 搜索地点
	SearchPlaces(context.Context, *ReqSearchPlaces, *RspSearchPlaces) error
	// 代理请求访问网页
	Proxy(context.Context, *ReqProxy, *RspProxy) error
}

func RegisterSupportGuestBff(s bff.Server, h SupportGuestBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("SupportGuest"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/support/send_map_request", h.SendMapRequest).Name("SendMapRequest")
	bff.AddRoute(group, http.MethodPost, "/support/get_place_selector", h.GetPlaceSelector).Name("GetPlaceSelector")
	bff.AddRoute(group, http.MethodPost, "/support/search_places", h.SearchPlaces).Name("SearchPlaces")
	bff.AddRoute(group, http.MethodPost, "/support/proxy", h.Proxy).Name("Proxy")
	return group
}
