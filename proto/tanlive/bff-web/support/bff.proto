syntax = "proto3";

package tanlive.bff_web.search;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/search";

import "google/api/annotations.proto";
import "tanlive/base/ugc.proto";
import "tanlive/options.proto";
import "tanlive/support/support.proto";

// support模块接口
service SupportBff {
  option (tanlive.bff) = true;
}

// support模块游客接口
service SupportGuestBff {
  option (tanlive.bff) = true;

  // 发送地图请求
  rpc SendMapRequest(ReqSendMapRequest) returns (RspSendMapRequest) {
    option (google.api.http) = {
      post: "/support/send_map_request"
      body: "*"
    };
  };

  // 查询地点选择器数据
  rpc GetPlaceSelector(ReqGetPlaceSelector) returns (RspGetPlaceSelector) {
    option (google.api.http) = {
      post: "/support/get_place_selector"
      body: "*"
    };
  };

  // 搜索地点
  rpc SearchPlaces(ReqSearchPlaces) returns (RspSearchPlaces) {
    option (google.api.http) = {
      post: "/support/search_places"
      body: "*"
    };
  };

  // 代理请求访问网页
  rpc Proxy(ReqProxy) returns (RspProxy){
    option (google.api.http) = {
      post: "/support/proxy"
      body: "*"
    };
  };
}

message ReqProxy{
  repeated string urls = 1 [(validator) = "required"];
}

message RspProxy{
  message Content {
    string url = 1;
    string title =2;
  }
  repeated Content contents = 1;
}


message ReqSendMapRequest {
  // 地图平台
  support.MapPlatform platform = 1 [(validator) = "required"];
  // 请求
  support.MapRequest request = 2 [(validator) = "required"];
  // 谷歌地图客户端
  support.GoogleMapClient platform1_client = 3;
}

message RspSendMapRequest {
  // 响应
  support.MapResponse response = 1;
}

message ReqGetPlaceSelector {
  // 查询条件
  oneof condition {
    // 通过数据类型查询
    tanlive.support.GetPlaceCondByDataType by_data_type = 1;
    // 通过地点ID查询
    tanlive.support.GetPlaceCondByPlaceId by_place_id = 2;
  }
}

message RspGetPlaceSelector {
  // 地点列表
  repeated tanlive.support.Place places = 1;
}

message ReqSearchPlaces {
  // 按名称模糊搜索
  string name = 1 [(validator) = "required"];
  // 数据类型
  base.DataType data_type = 2 [(validator) = "required"];
  // 数据字段
  // 团队：location、service_region 资源：location、target_regions
  string data_field = 3 [(validator) = "required"];
}

message RspSearchPlaces {
  // 地点列表
  repeated support.PlaceWithParents places = 1;
}
