// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/support/bff.proto

package search

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	support "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqProxy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Urls []string `protobuf:"bytes,1,rep,name=urls,proto3" json:"urls,omitempty"`
}

func (x *ReqProxy) Reset() {
	*x = ReqProxy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqProxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqProxy) ProtoMessage() {}

func (x *ReqProxy) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqProxy.ProtoReflect.Descriptor instead.
func (*ReqProxy) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqProxy) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

type RspProxy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Contents []*RspProxy_Content `protobuf:"bytes,1,rep,name=contents,proto3" json:"contents,omitempty"`
}

func (x *RspProxy) Reset() {
	*x = RspProxy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspProxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspProxy) ProtoMessage() {}

func (x *RspProxy) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspProxy.ProtoReflect.Descriptor instead.
func (*RspProxy) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspProxy) GetContents() []*RspProxy_Content {
	if x != nil {
		return x.Contents
	}
	return nil
}

type ReqSendMapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地图平台
	Platform support.MapPlatform `protobuf:"varint,1,opt,name=platform,proto3,enum=tanlive.support.MapPlatform" json:"platform,omitempty"`
	// 请求
	Request *support.MapRequest `protobuf:"bytes,2,opt,name=request,proto3" json:"request,omitempty"`
	// 谷歌地图客户端
	Platform1Client support.GoogleMapClient `protobuf:"varint,3,opt,name=platform1_client,json=platform1Client,proto3,enum=tanlive.support.GoogleMapClient" json:"platform1_client,omitempty"`
}

func (x *ReqSendMapRequest) Reset() {
	*x = ReqSendMapRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSendMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSendMapRequest) ProtoMessage() {}

func (x *ReqSendMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSendMapRequest.ProtoReflect.Descriptor instead.
func (*ReqSendMapRequest) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{2}
}

func (x *ReqSendMapRequest) GetPlatform() support.MapPlatform {
	if x != nil {
		return x.Platform
	}
	return support.MapPlatform(0)
}

func (x *ReqSendMapRequest) GetRequest() *support.MapRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *ReqSendMapRequest) GetPlatform1Client() support.GoogleMapClient {
	if x != nil {
		return x.Platform1Client
	}
	return support.GoogleMapClient(0)
}

type RspSendMapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应
	Response *support.MapResponse `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *RspSendMapRequest) Reset() {
	*x = RspSendMapRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSendMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSendMapRequest) ProtoMessage() {}

func (x *RspSendMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSendMapRequest.ProtoReflect.Descriptor instead.
func (*RspSendMapRequest) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{3}
}

func (x *RspSendMapRequest) GetResponse() *support.MapResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

type ReqGetPlaceSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 查询条件
	//
	// Types that are assignable to Condition:
	//
	//	*ReqGetPlaceSelector_ByDataType
	//	*ReqGetPlaceSelector_ByPlaceId
	Condition isReqGetPlaceSelector_Condition `protobuf_oneof:"condition"`
}

func (x *ReqGetPlaceSelector) Reset() {
	*x = ReqGetPlaceSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetPlaceSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetPlaceSelector) ProtoMessage() {}

func (x *ReqGetPlaceSelector) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetPlaceSelector.ProtoReflect.Descriptor instead.
func (*ReqGetPlaceSelector) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{4}
}

func (m *ReqGetPlaceSelector) GetCondition() isReqGetPlaceSelector_Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (x *ReqGetPlaceSelector) GetByDataType() *support.GetPlaceCondByDataType {
	if x, ok := x.GetCondition().(*ReqGetPlaceSelector_ByDataType); ok {
		return x.ByDataType
	}
	return nil
}

func (x *ReqGetPlaceSelector) GetByPlaceId() *support.GetPlaceCondByPlaceId {
	if x, ok := x.GetCondition().(*ReqGetPlaceSelector_ByPlaceId); ok {
		return x.ByPlaceId
	}
	return nil
}

type isReqGetPlaceSelector_Condition interface {
	isReqGetPlaceSelector_Condition()
}

type ReqGetPlaceSelector_ByDataType struct {
	// 通过数据类型查询
	ByDataType *support.GetPlaceCondByDataType `protobuf:"bytes,1,opt,name=by_data_type,json=byDataType,proto3,oneof"`
}

type ReqGetPlaceSelector_ByPlaceId struct {
	// 通过地点ID查询
	ByPlaceId *support.GetPlaceCondByPlaceId `protobuf:"bytes,2,opt,name=by_place_id,json=byPlaceId,proto3,oneof"`
}

func (*ReqGetPlaceSelector_ByDataType) isReqGetPlaceSelector_Condition() {}

func (*ReqGetPlaceSelector_ByPlaceId) isReqGetPlaceSelector_Condition() {}

type RspGetPlaceSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地点列表
	Places []*support.Place `protobuf:"bytes,1,rep,name=places,proto3" json:"places,omitempty"`
}

func (x *RspGetPlaceSelector) Reset() {
	*x = RspGetPlaceSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetPlaceSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetPlaceSelector) ProtoMessage() {}

func (x *RspGetPlaceSelector) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetPlaceSelector.ProtoReflect.Descriptor instead.
func (*RspGetPlaceSelector) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{5}
}

func (x *RspGetPlaceSelector) GetPlaces() []*support.Place {
	if x != nil {
		return x.Places
	}
	return nil
}

type ReqSearchPlaces struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 按名称模糊搜索
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 数据类型
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 数据字段
	// 团队：location、service_region 资源：location、target_regions
	DataField string `protobuf:"bytes,3,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
}

func (x *ReqSearchPlaces) Reset() {
	*x = ReqSearchPlaces{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchPlaces) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchPlaces) ProtoMessage() {}

func (x *ReqSearchPlaces) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchPlaces.ProtoReflect.Descriptor instead.
func (*ReqSearchPlaces) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{6}
}

func (x *ReqSearchPlaces) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReqSearchPlaces) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqSearchPlaces) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

type RspSearchPlaces struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地点列表
	Places []*support.PlaceWithParents `protobuf:"bytes,1,rep,name=places,proto3" json:"places,omitempty"`
}

func (x *RspSearchPlaces) Reset() {
	*x = RspSearchPlaces{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSearchPlaces) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSearchPlaces) ProtoMessage() {}

func (x *RspSearchPlaces) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSearchPlaces.ProtoReflect.Descriptor instead.
func (*RspSearchPlaces) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{7}
}

func (x *RspSearchPlaces) GetPlaces() []*support.PlaceWithParents {
	if x != nil {
		return x.Places
	}
	return nil
}

type RspProxy_Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *RspProxy_Content) Reset() {
	*x = RspProxy_Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspProxy_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspProxy_Content) ProtoMessage() {}

func (x *RspProxy_Content) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_support_bff_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspProxy_Content.ProtoReflect.Descriptor instead.
func (*RspProxy_Content) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_support_bff_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RspProxy_Content) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RspProxy_Content) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

var File_tanlive_bff_web_support_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_support_bff_proto_rawDesc = []byte{
	0x0a, 0x21, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2c, 0x0a, 0x08, 0x52, 0x65, 0x71, 0x50, 0x72,
	0x6f, 0x78, 0x79, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x04, 0x75, 0x72, 0x6c, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x08, 0x52, 0x73, 0x70, 0x50, 0x72, 0x6f,
	0x78, 0x79, 0x12, 0x44, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73,
	0x70, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x31, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0xed, 0x01, 0x0a, 0x11,
	0x52, 0x65, 0x71, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x46, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x43, 0x0a, 0x07, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b,
	0x0a, 0x10, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x31, 0x5f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x4d, 0x61, 0x70, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x31, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x22, 0x4d, 0x0a, 0x11, 0x52,
	0x73, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb9, 0x01, 0x0a, 0x13, 0x52,
	0x65, 0x71, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x4b, 0x0a, 0x0c, 0x62, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x79, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x48, 0x0a, 0x0b, 0x62, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x64, 0x42, 0x79, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x48, 0x00, 0x52, 0x09,
	0x62, 0x79, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x45, 0x0a, 0x13, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2e, 0x0a,
	0x06, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x22, 0xa3, 0x01,
	0x0a, 0x0f, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x63, 0x65,
	0x73, 0x12, 0x20, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x22, 0x4c, 0x0a, 0x0f, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x57, 0x69,
	0x74, 0x68, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x73, 0x32, 0x12, 0x0a, 0x0a, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x66, 0x66, 0x1a,
	0x04, 0xd0, 0xc6, 0x27, 0x01, 0x32, 0xab, 0x04, 0x0a, 0x0f, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x47, 0x75, 0x65, 0x73, 0x74, 0x42, 0x66, 0x66, 0x12, 0x8c, 0x01, 0x0a, 0x0e, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x61, 0x70,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2b, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x2b, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a,
	0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x67, 0x65, 0x74,
	0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12,
	0x83, 0x01, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x73,
	0x12, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x1a, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x73, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x73, 0x12, 0x66, 0x0a, 0x05, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x20,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x50, 0x72, 0x6f, 0x78, 0x79,
	0x1a, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x50, 0x72, 0x6f,
	0x78, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x1a, 0x04, 0xd0,
	0xc6, 0x27, 0x01, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_web_support_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_support_bff_proto_rawDescData = file_tanlive_bff_web_support_bff_proto_rawDesc
)

func file_tanlive_bff_web_support_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_support_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_support_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_support_bff_proto_rawDescData)
	})
	return file_tanlive_bff_web_support_bff_proto_rawDescData
}

var file_tanlive_bff_web_support_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_tanlive_bff_web_support_bff_proto_goTypes = []interface{}{
	(*ReqProxy)(nil),                       // 0: tanlive.bff_web.search.ReqProxy
	(*RspProxy)(nil),                       // 1: tanlive.bff_web.search.RspProxy
	(*ReqSendMapRequest)(nil),              // 2: tanlive.bff_web.search.ReqSendMapRequest
	(*RspSendMapRequest)(nil),              // 3: tanlive.bff_web.search.RspSendMapRequest
	(*ReqGetPlaceSelector)(nil),            // 4: tanlive.bff_web.search.ReqGetPlaceSelector
	(*RspGetPlaceSelector)(nil),            // 5: tanlive.bff_web.search.RspGetPlaceSelector
	(*ReqSearchPlaces)(nil),                // 6: tanlive.bff_web.search.ReqSearchPlaces
	(*RspSearchPlaces)(nil),                // 7: tanlive.bff_web.search.RspSearchPlaces
	(*RspProxy_Content)(nil),               // 8: tanlive.bff_web.search.RspProxy.Content
	(support.MapPlatform)(0),               // 9: tanlive.support.MapPlatform
	(*support.MapRequest)(nil),             // 10: tanlive.support.MapRequest
	(support.GoogleMapClient)(0),           // 11: tanlive.support.GoogleMapClient
	(*support.MapResponse)(nil),            // 12: tanlive.support.MapResponse
	(*support.GetPlaceCondByDataType)(nil), // 13: tanlive.support.GetPlaceCondByDataType
	(*support.GetPlaceCondByPlaceId)(nil),  // 14: tanlive.support.GetPlaceCondByPlaceId
	(*support.Place)(nil),                  // 15: tanlive.support.Place
	(base.DataType)(0),                     // 16: tanlive.base.DataType
	(*support.PlaceWithParents)(nil),       // 17: tanlive.support.PlaceWithParents
}
var file_tanlive_bff_web_support_bff_proto_depIdxs = []int32{
	8,  // 0: tanlive.bff_web.search.RspProxy.contents:type_name -> tanlive.bff_web.search.RspProxy.Content
	9,  // 1: tanlive.bff_web.search.ReqSendMapRequest.platform:type_name -> tanlive.support.MapPlatform
	10, // 2: tanlive.bff_web.search.ReqSendMapRequest.request:type_name -> tanlive.support.MapRequest
	11, // 3: tanlive.bff_web.search.ReqSendMapRequest.platform1_client:type_name -> tanlive.support.GoogleMapClient
	12, // 4: tanlive.bff_web.search.RspSendMapRequest.response:type_name -> tanlive.support.MapResponse
	13, // 5: tanlive.bff_web.search.ReqGetPlaceSelector.by_data_type:type_name -> tanlive.support.GetPlaceCondByDataType
	14, // 6: tanlive.bff_web.search.ReqGetPlaceSelector.by_place_id:type_name -> tanlive.support.GetPlaceCondByPlaceId
	15, // 7: tanlive.bff_web.search.RspGetPlaceSelector.places:type_name -> tanlive.support.Place
	16, // 8: tanlive.bff_web.search.ReqSearchPlaces.data_type:type_name -> tanlive.base.DataType
	17, // 9: tanlive.bff_web.search.RspSearchPlaces.places:type_name -> tanlive.support.PlaceWithParents
	2,  // 10: tanlive.bff_web.search.SupportGuestBff.SendMapRequest:input_type -> tanlive.bff_web.search.ReqSendMapRequest
	4,  // 11: tanlive.bff_web.search.SupportGuestBff.GetPlaceSelector:input_type -> tanlive.bff_web.search.ReqGetPlaceSelector
	6,  // 12: tanlive.bff_web.search.SupportGuestBff.SearchPlaces:input_type -> tanlive.bff_web.search.ReqSearchPlaces
	0,  // 13: tanlive.bff_web.search.SupportGuestBff.Proxy:input_type -> tanlive.bff_web.search.ReqProxy
	3,  // 14: tanlive.bff_web.search.SupportGuestBff.SendMapRequest:output_type -> tanlive.bff_web.search.RspSendMapRequest
	5,  // 15: tanlive.bff_web.search.SupportGuestBff.GetPlaceSelector:output_type -> tanlive.bff_web.search.RspGetPlaceSelector
	7,  // 16: tanlive.bff_web.search.SupportGuestBff.SearchPlaces:output_type -> tanlive.bff_web.search.RspSearchPlaces
	1,  // 17: tanlive.bff_web.search.SupportGuestBff.Proxy:output_type -> tanlive.bff_web.search.RspProxy
	14, // [14:18] is the sub-list for method output_type
	10, // [10:14] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_support_bff_proto_init() }
func file_tanlive_bff_web_support_bff_proto_init() {
	if File_tanlive_bff_web_support_bff_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_support_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqProxy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_support_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspProxy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_support_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSendMapRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_support_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSendMapRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_support_bff_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetPlaceSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_support_bff_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetPlaceSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_support_bff_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchPlaces); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_support_bff_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSearchPlaces); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_support_bff_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspProxy_Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tanlive_bff_web_support_bff_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ReqGetPlaceSelector_ByDataType)(nil),
		(*ReqGetPlaceSelector_ByPlaceId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_support_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_tanlive_bff_web_support_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_support_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_web_support_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_support_bff_proto = out.File
	file_tanlive_bff_web_support_bff_proto_rawDesc = nil
	file_tanlive_bff_web_support_bff_proto_goTypes = nil
	file_tanlive_bff_web_support_bff_proto_depIdxs = nil
}
