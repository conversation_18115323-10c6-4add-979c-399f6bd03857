// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package tag

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	proto "google.golang.org/protobuf/proto"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TaggableType": "min=1",
		"NotifyType":   "min=0,max=1",
	}, &ReqGetSystemTags{})
}

func (x *ReqGetSystemTags) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspGetSystemTags) MaskInLog() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := proto.Clone(x).(*RspGetSystemTags)
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagSet[k] = vv.MaskInLog().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *RspGetSystemTags) MaskInRpc() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagSet[k] = vv.MaskInRpc().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *RspGetSystemTags) MaskInBff() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagSet[k] = vv.MaskInBff().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *RspGetSystemTags) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagSet {
		if sanitizer, ok := any(x.TagSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type TagGuestBffHandler interface {
	// 获取全量系统标签
	GetSystemTags(context.Context, *ReqGetSystemTags, *RspGetSystemTags) error
}

func RegisterTagGuestBff(s bff.Server, h TagGuestBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("TagGuest"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/tag/get_system_tags", h.GetSystemTags).Name("GetSystemTags")
	return group
}
