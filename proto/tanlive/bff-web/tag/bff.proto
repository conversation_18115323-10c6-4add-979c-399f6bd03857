syntax = "proto3";

package tanlive.bff_web.tag;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/tag";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "tanlive/bff-web/tag/tag.proto";
import "tanlive/tag/tag.proto";
import "tanlive/options.proto";
import "tanlive/base/ugc.proto";

message ReqGetSystemTags{
  // 标签索引类型
  tanlive.tag.TaggableType taggable_type = 1 [(tanlive.validator) = "min=1"];
  // 语言类型zh、en
  string language = 2;
  // 排序
  repeated string order_by = 3;
  // 定向推送用的筛选条件 1 系统标签
  tanlive.tag.TagCreateType notify_type = 4 [(tanlive.validator) = "min=0,max=1"];
  // 标签名称模糊搜索
  string tag_name = 5;
  // 是否直接显示标签id，不会被编码为hashId
  bool is_direct_display_id = 6;
}

message RspGetSystemTags{
  repeated Tag tag_set = 1;
  message Tag {
    // 标签id
    uint64 id = 1;
    // 标签名称
    string name = 2;
    // 直接显示的标签id
    uint64 direct_display_id = 3;
  }
}

// tag接口
service TagGuestBff {
  option (tanlive.bff) = true;

  // 获取全量系统标签
  rpc GetSystemTags(ReqGetSystemTags) returns (RspGetSystemTags) {
    option (google.api.http) = {
      post: "/tag/get_system_tags"
      body: "*"
    };
  };

}
