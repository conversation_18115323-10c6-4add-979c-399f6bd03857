// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/tag/bff.proto

package tag

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	tag "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqGetSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签索引类型
	TaggableType tag.TaggableType `protobuf:"varint,1,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 语言类型zh、en
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	// 排序
	OrderBy []string `protobuf:"bytes,3,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// 定向推送用的筛选条件 1 系统标签
	NotifyType tag.TagCreateType `protobuf:"varint,4,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 标签名称模糊搜索
	TagName string `protobuf:"bytes,5,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 是否直接显示标签id，不会被编码为hashId
	IsDirectDisplayId bool `protobuf:"varint,6,opt,name=is_direct_display_id,json=isDirectDisplayId,proto3" json:"is_direct_display_id,omitempty"`
}

func (x *ReqGetSystemTags) Reset() {
	*x = ReqGetSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_tag_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetSystemTags) ProtoMessage() {}

func (x *ReqGetSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_tag_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetSystemTags.ProtoReflect.Descriptor instead.
func (*ReqGetSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_tag_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqGetSystemTags) GetTaggableType() tag.TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return tag.TaggableType(0)
}

func (x *ReqGetSystemTags) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqGetSystemTags) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ReqGetSystemTags) GetNotifyType() tag.TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return tag.TagCreateType(0)
}

func (x *ReqGetSystemTags) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *ReqGetSystemTags) GetIsDirectDisplayId() bool {
	if x != nil {
		return x.IsDirectDisplayId
	}
	return false
}

type RspGetSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagSet []*RspGetSystemTags_Tag `protobuf:"bytes,1,rep,name=tag_set,json=tagSet,proto3" json:"tag_set,omitempty"`
}

func (x *RspGetSystemTags) Reset() {
	*x = RspGetSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_tag_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetSystemTags) ProtoMessage() {}

func (x *RspGetSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_tag_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetSystemTags.ProtoReflect.Descriptor instead.
func (*RspGetSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_tag_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspGetSystemTags) GetTagSet() []*RspGetSystemTags_Tag {
	if x != nil {
		return x.TagSet
	}
	return nil
}

type RspGetSystemTags_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 直接显示的标签id
	DirectDisplayId uint64 `protobuf:"varint,3,opt,name=direct_display_id,json=directDisplayId,proto3" json:"direct_display_id,omitempty"`
}

func (x *RspGetSystemTags_Tag) Reset() {
	*x = RspGetSystemTags_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_tag_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetSystemTags_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetSystemTags_Tag) ProtoMessage() {}

func (x *RspGetSystemTags_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_tag_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetSystemTags_Tag.ProtoReflect.Descriptor instead.
func (*RspGetSystemTags_Tag) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_tag_bff_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RspGetSystemTags_Tag) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspGetSystemTags_Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RspGetSystemTags_Tag) GetDirectDisplayId() uint64 {
	if x != nil {
		return x.DirectDisplayId
	}
	return 0
}

var File_tanlive_bff_web_tag_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_tag_bff_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x74, 0x61, 0x67, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1d, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65, 0x62,
	0x2f, 0x74, 0x61, 0x67, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x74, 0x61, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xae, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x74, 0x61, 0x67,
	0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x09, 0x82, 0x88, 0x27,
	0x05, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x4c, 0x0a, 0x0b, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x82, 0x88,
	0x27, 0x0b, 0x6d, 0x69, 0x6e, 0x3d, 0x30, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x31, 0x52, 0x0a, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x49, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x10, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61,
	0x67, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61,
	0x67, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x06, 0x74, 0x61, 0x67, 0x53, 0x65, 0x74, 0x1a, 0x55,
	0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x49, 0x64, 0x32, 0x93, 0x01, 0x0a, 0x0b, 0x54, 0x61, 0x67, 0x47, 0x75, 0x65,
	0x73, 0x74, 0x42, 0x66, 0x66, 0x12, 0x7e, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71,
	0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x1a, 0x25, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x54, 0x61, 0x67, 0x73, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22,
	0x14, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x61, 0x67, 0x73, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x42, 0x3d, 0x5a, 0x3b, 0x65,
	0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62,
	0x66, 0x66, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x74, 0x61, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_tanlive_bff_web_tag_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_tag_bff_proto_rawDescData = file_tanlive_bff_web_tag_bff_proto_rawDesc
)

func file_tanlive_bff_web_tag_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_tag_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_tag_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_tag_bff_proto_rawDescData)
	})
	return file_tanlive_bff_web_tag_bff_proto_rawDescData
}

var file_tanlive_bff_web_tag_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_tanlive_bff_web_tag_bff_proto_goTypes = []interface{}{
	(*ReqGetSystemTags)(nil),     // 0: tanlive.bff_web.tag.ReqGetSystemTags
	(*RspGetSystemTags)(nil),     // 1: tanlive.bff_web.tag.RspGetSystemTags
	(*RspGetSystemTags_Tag)(nil), // 2: tanlive.bff_web.tag.RspGetSystemTags.Tag
	(tag.TaggableType)(0),        // 3: tanlive.tag.TaggableType
	(tag.TagCreateType)(0),       // 4: tanlive.tag.TagCreateType
}
var file_tanlive_bff_web_tag_bff_proto_depIdxs = []int32{
	3, // 0: tanlive.bff_web.tag.ReqGetSystemTags.taggable_type:type_name -> tanlive.tag.TaggableType
	4, // 1: tanlive.bff_web.tag.ReqGetSystemTags.notify_type:type_name -> tanlive.tag.TagCreateType
	2, // 2: tanlive.bff_web.tag.RspGetSystemTags.tag_set:type_name -> tanlive.bff_web.tag.RspGetSystemTags.Tag
	0, // 3: tanlive.bff_web.tag.TagGuestBff.GetSystemTags:input_type -> tanlive.bff_web.tag.ReqGetSystemTags
	1, // 4: tanlive.bff_web.tag.TagGuestBff.GetSystemTags:output_type -> tanlive.bff_web.tag.RspGetSystemTags
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_tag_bff_proto_init() }
func file_tanlive_bff_web_tag_bff_proto_init() {
	if File_tanlive_bff_web_tag_bff_proto != nil {
		return
	}
	file_tanlive_bff_web_tag_tag_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_tag_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_tag_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_tag_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetSystemTags_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_tag_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_bff_web_tag_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_tag_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_web_tag_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_tag_bff_proto = out.File
	file_tanlive_bff_web_tag_bff_proto_rawDesc = nil
	file_tanlive_bff_web_tag_bff_proto_goTypes = nil
	file_tanlive_bff_web_tag_bff_proto_depIdxs = nil
}
