// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/iam/bff.proto

package iam

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iam "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 登录
type ReqLogin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 登录类型
	LoginType LoginType `protobuf:"varint,1,opt,name=login_type,json=loginType,proto3,enum=tanlive.bff_web.iam.LoginType" json:"login_type,omitempty"`
	// 密码登录凭证
	AccountCredentials *ReqLogin_AccountCredentials `protobuf:"bytes,2,opt,name=account_credentials,json=accountCredentials,proto3" json:"account_credentials,omitempty"`
	// 手机号登录凭证
	PhoneCredentials *ReqLogin_PhoneCredentials `protobuf:"bytes,3,opt,name=phone_credentials,json=phoneCredentials,proto3" json:"phone_credentials,omitempty"`
	// 邮箱登录凭证
	EmailCredentials *ReqLogin_EmailCredentials `protobuf:"bytes,4,opt,name=email_credentials,json=emailCredentials,proto3" json:"email_credentials,omitempty"`
	// 第三方登录凭证
	ThirdCredentials *ReqLogin_ThirdCredentials `protobuf:"bytes,5,opt,name=third_credentials,json=thirdCredentials,proto3" json:"third_credentials,omitempty"`
	// 第三方凭证(用于绑定)
	BindThirdTicket string `protobuf:"bytes,6,opt,name=bind_third_ticket,json=bindThirdTicket,proto3" json:"bind_third_ticket,omitempty"`
	// 腾讯云验证码参数
	TcloudCaptcha *base.TcloudCaptcha `protobuf:"bytes,7,opt,name=tcloud_captcha,json=tcloudCaptcha,proto3" json:"tcloud_captcha,omitempty"`
}

func (x *ReqLogin) Reset() {
	*x = ReqLogin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqLogin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqLogin) ProtoMessage() {}

func (x *ReqLogin) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqLogin.ProtoReflect.Descriptor instead.
func (*ReqLogin) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqLogin) GetLoginType() LoginType {
	if x != nil {
		return x.LoginType
	}
	return LoginType_LOGIN_TYPE_UNSPECIFIED
}

func (x *ReqLogin) GetAccountCredentials() *ReqLogin_AccountCredentials {
	if x != nil {
		return x.AccountCredentials
	}
	return nil
}

func (x *ReqLogin) GetPhoneCredentials() *ReqLogin_PhoneCredentials {
	if x != nil {
		return x.PhoneCredentials
	}
	return nil
}

func (x *ReqLogin) GetEmailCredentials() *ReqLogin_EmailCredentials {
	if x != nil {
		return x.EmailCredentials
	}
	return nil
}

func (x *ReqLogin) GetThirdCredentials() *ReqLogin_ThirdCredentials {
	if x != nil {
		return x.ThirdCredentials
	}
	return nil
}

func (x *ReqLogin) GetBindThirdTicket() string {
	if x != nil {
		return x.BindThirdTicket
	}
	return ""
}

func (x *ReqLogin) GetTcloudCaptcha() *base.TcloudCaptcha {
	if x != nil {
		return x.TcloudCaptcha
	}
	return nil
}

type RspLogin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户信息结构体
	UserInfo *RspLogin_UserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	// 该用户可显示的菜单列表
	Menus []*Menu `protobuf:"bytes,2,rep,name=menus,proto3" json:"menus,omitempty"`
	// 该账号是否为未登录过状态
	NewUser bool `protobuf:"varint,3,opt,name=new_user,json=newUser,proto3" json:"new_user,omitempty"`
}

func (x *RspLogin) Reset() {
	*x = RspLogin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspLogin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspLogin) ProtoMessage() {}

func (x *RspLogin) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspLogin.ProtoReflect.Descriptor instead.
func (*RspLogin) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspLogin) GetUserInfo() *RspLogin_UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *RspLogin) GetMenus() []*Menu {
	if x != nil {
		return x.Menus
	}
	return nil
}

func (x *RspLogin) GetNewUser() bool {
	if x != nil {
		return x.NewUser
	}
	return false
}

type ReqSendAuthOtp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 场景（仅1、2、4、6、7、9有效）
	Scene iam.OtpScene `protobuf:"varint,1,opt,name=scene,proto3,enum=tanlive.iam.OtpScene" json:"scene,omitempty"`
	// 接收人类型
	ReceiverKind OtpReceiverKind `protobuf:"varint,2,opt,name=receiver_kind,json=receiverKind,proto3,enum=tanlive.bff_web.iam.OtpReceiverKind" json:"receiver_kind,omitempty"`
	// 接收人手机号
	ReceiverPhone string `protobuf:"bytes,3,opt,name=receiver_phone,json=receiverPhone,proto3" json:"receiver_phone,omitempty"`
	// 接收人邮箱
	ReceiverEmail string `protobuf:"bytes,4,opt,name=receiver_email,json=receiverEmail,proto3" json:"receiver_email,omitempty"`
	// 腾讯云验证码参数
	TcloudCaptcha *base.TcloudCaptcha `protobuf:"bytes,5,opt,name=tcloud_captcha,json=tcloudCaptcha,proto3" json:"tcloud_captcha,omitempty"`
}

func (x *ReqSendAuthOtp) Reset() {
	*x = ReqSendAuthOtp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSendAuthOtp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSendAuthOtp) ProtoMessage() {}

func (x *ReqSendAuthOtp) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSendAuthOtp.ProtoReflect.Descriptor instead.
func (*ReqSendAuthOtp) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{2}
}

func (x *ReqSendAuthOtp) GetScene() iam.OtpScene {
	if x != nil {
		return x.Scene
	}
	return iam.OtpScene(0)
}

func (x *ReqSendAuthOtp) GetReceiverKind() OtpReceiverKind {
	if x != nil {
		return x.ReceiverKind
	}
	return OtpReceiverKind_OTP_RECEIVER_KIND_UNSPECIFIED
}

func (x *ReqSendAuthOtp) GetReceiverPhone() string {
	if x != nil {
		return x.ReceiverPhone
	}
	return ""
}

func (x *ReqSendAuthOtp) GetReceiverEmail() string {
	if x != nil {
		return x.ReceiverEmail
	}
	return ""
}

func (x *ReqSendAuthOtp) GetTcloudCaptcha() *base.TcloudCaptcha {
	if x != nil {
		return x.TcloudCaptcha
	}
	return nil
}

type RspSendAuthOtp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	TaskNo string `protobuf:"bytes,1,opt,name=task_no,json=taskNo,proto3" json:"task_no,omitempty"`
	// 腾讯云SDK错误码
	SdkError string `protobuf:"bytes,2,opt,name=sdk_error,json=sdkError,proto3" json:"sdk_error,omitempty"`
}

func (x *RspSendAuthOtp) Reset() {
	*x = RspSendAuthOtp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSendAuthOtp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSendAuthOtp) ProtoMessage() {}

func (x *RspSendAuthOtp) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSendAuthOtp.ProtoReflect.Descriptor instead.
func (*RspSendAuthOtp) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{3}
}

func (x *RspSendAuthOtp) GetTaskNo() string {
	if x != nil {
		return x.TaskNo
	}
	return ""
}

func (x *RspSendAuthOtp) GetSdkError() string {
	if x != nil {
		return x.SdkError
	}
	return ""
}

type ReqSendTfaOtp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 接收者类型
	ReceiverKind OtpReceiverKind `protobuf:"varint,1,opt,name=receiver_kind,json=receiverKind,proto3,enum=tanlive.bff_web.iam.OtpReceiverKind" json:"receiver_kind,omitempty"`
	// 腾讯云验证码参数
	TcloudCaptcha *base.TcloudCaptcha `protobuf:"bytes,5,opt,name=tcloud_captcha,json=tcloudCaptcha,proto3" json:"tcloud_captcha,omitempty"`
}

func (x *ReqSendTfaOtp) Reset() {
	*x = ReqSendTfaOtp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSendTfaOtp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSendTfaOtp) ProtoMessage() {}

func (x *ReqSendTfaOtp) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSendTfaOtp.ProtoReflect.Descriptor instead.
func (*ReqSendTfaOtp) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{4}
}

func (x *ReqSendTfaOtp) GetReceiverKind() OtpReceiverKind {
	if x != nil {
		return x.ReceiverKind
	}
	return OtpReceiverKind_OTP_RECEIVER_KIND_UNSPECIFIED
}

func (x *ReqSendTfaOtp) GetTcloudCaptcha() *base.TcloudCaptcha {
	if x != nil {
		return x.TcloudCaptcha
	}
	return nil
}

type RspSendTfaOtp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	TaskNo string `protobuf:"bytes,1,opt,name=task_no,json=taskNo,proto3" json:"task_no,omitempty"`
	// 腾讯云SDK错误码
	SdkError string `protobuf:"bytes,2,opt,name=sdk_error,json=sdkError,proto3" json:"sdk_error,omitempty"`
}

func (x *RspSendTfaOtp) Reset() {
	*x = RspSendTfaOtp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSendTfaOtp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSendTfaOtp) ProtoMessage() {}

func (x *RspSendTfaOtp) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSendTfaOtp.ProtoReflect.Descriptor instead.
func (*RspSendTfaOtp) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{5}
}

func (x *RspSendTfaOtp) GetTaskNo() string {
	if x != nil {
		return x.TaskNo
	}
	return ""
}

func (x *RspSendTfaOtp) GetSdkError() string {
	if x != nil {
		return x.SdkError
	}
	return ""
}

type RspGetMyTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 2FA令牌（未创建或已过期返回null）
	TfaToken *iam.TfaToken `protobuf:"bytes,1,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *RspGetMyTfa) Reset() {
	*x = RspGetMyTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetMyTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetMyTfa) ProtoMessage() {}

func (x *RspGetMyTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetMyTfa.ProtoReflect.Descriptor instead.
func (*RspGetMyTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{6}
}

func (x *RspGetMyTfa) GetTfaToken() *iam.TfaToken {
	if x != nil {
		return x.TfaToken
	}
	return nil
}

type ReqCreatePhoneTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 一次性密码
	Otp string `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *ReqCreatePhoneTfa) Reset() {
	*x = ReqCreatePhoneTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreatePhoneTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreatePhoneTfa) ProtoMessage() {}

func (x *ReqCreatePhoneTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreatePhoneTfa.ProtoReflect.Descriptor instead.
func (*ReqCreatePhoneTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{7}
}

func (x *ReqCreatePhoneTfa) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type RspCreateTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 2FA令牌
	TfaToken *iam.TfaToken `protobuf:"bytes,1,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *RspCreateTfa) Reset() {
	*x = RspCreateTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateTfa) ProtoMessage() {}

func (x *RspCreateTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateTfa.ProtoReflect.Descriptor instead.
func (*RspCreateTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{8}
}

func (x *RspCreateTfa) GetTfaToken() *iam.TfaToken {
	if x != nil {
		return x.TfaToken
	}
	return nil
}

type ReqCreatePasswordTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 密码（需要RSA加密）
	Password string `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *ReqCreatePasswordTfa) Reset() {
	*x = ReqCreatePasswordTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreatePasswordTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreatePasswordTfa) ProtoMessage() {}

func (x *ReqCreatePasswordTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreatePasswordTfa.ProtoReflect.Descriptor instead.
func (*ReqCreatePasswordTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{9}
}

func (x *ReqCreatePasswordTfa) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type ReqCreateEmailTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 一次性密码
	Otp string `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *ReqCreateEmailTfa) Reset() {
	*x = ReqCreateEmailTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateEmailTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateEmailTfa) ProtoMessage() {}

func (x *ReqCreateEmailTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateEmailTfa.ProtoReflect.Descriptor instead.
func (*ReqCreateEmailTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{10}
}

func (x *ReqCreateEmailTfa) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type RspCreateWeixinTfaQrcode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 二维码信息
	Qrcode *iam.WeixinQrcode `protobuf:"bytes,1,opt,name=qrcode,proto3" json:"qrcode,omitempty"`
}

func (x *RspCreateWeixinTfaQrcode) Reset() {
	*x = RspCreateWeixinTfaQrcode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateWeixinTfaQrcode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateWeixinTfaQrcode) ProtoMessage() {}

func (x *RspCreateWeixinTfaQrcode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateWeixinTfaQrcode.ProtoReflect.Descriptor instead.
func (*RspCreateWeixinTfaQrcode) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{11}
}

func (x *RspCreateWeixinTfaQrcode) GetQrcode() *iam.WeixinQrcode {
	if x != nil {
		return x.Qrcode
	}
	return nil
}

type ReqGetCreateWeixinTfaState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 场景值
	SceneStr string `protobuf:"bytes,1,opt,name=scene_str,json=sceneStr,proto3" json:"scene_str,omitempty"`
}

func (x *ReqGetCreateWeixinTfaState) Reset() {
	*x = ReqGetCreateWeixinTfaState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetCreateWeixinTfaState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetCreateWeixinTfaState) ProtoMessage() {}

func (x *ReqGetCreateWeixinTfaState) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetCreateWeixinTfaState.ProtoReflect.Descriptor instead.
func (*ReqGetCreateWeixinTfaState) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{12}
}

func (x *ReqGetCreateWeixinTfaState) GetSceneStr() string {
	if x != nil {
		return x.SceneStr
	}
	return ""
}

type RspGetCreateWeixinTfaState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 状态
	State iam.CreateWeixinTfaState `protobuf:"varint,1,opt,name=state,proto3,enum=tanlive.iam.CreateWeixinTfaState" json:"state,omitempty"`
	// 2FA令牌
	TfaToken *iam.TfaToken `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *RspGetCreateWeixinTfaState) Reset() {
	*x = RspGetCreateWeixinTfaState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetCreateWeixinTfaState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetCreateWeixinTfaState) ProtoMessage() {}

func (x *RspGetCreateWeixinTfaState) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetCreateWeixinTfaState.ProtoReflect.Descriptor instead.
func (*RspGetCreateWeixinTfaState) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{13}
}

func (x *RspGetCreateWeixinTfaState) GetState() iam.CreateWeixinTfaState {
	if x != nil {
		return x.State
	}
	return iam.CreateWeixinTfaState(0)
}

func (x *RspGetCreateWeixinTfaState) GetTfaToken() *iam.TfaToken {
	if x != nil {
		return x.TfaToken
	}
	return nil
}

type ReqCreateWeixinBrowserTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 微信静默授权返回的code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ReqCreateWeixinBrowserTfa) Reset() {
	*x = ReqCreateWeixinBrowserTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateWeixinBrowserTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateWeixinBrowserTfa) ProtoMessage() {}

func (x *ReqCreateWeixinBrowserTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateWeixinBrowserTfa.ProtoReflect.Descriptor instead.
func (*ReqCreateWeixinBrowserTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{14}
}

func (x *ReqCreateWeixinBrowserTfa) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type ReqModifyMyUsername struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户名（需要RSA加密）
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqModifyMyUsername) Reset() {
	*x = ReqModifyMyUsername{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyMyUsername) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyMyUsername) ProtoMessage() {}

func (x *ReqModifyMyUsername) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyMyUsername.ProtoReflect.Descriptor instead.
func (*ReqModifyMyUsername) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{15}
}

func (x *ReqModifyMyUsername) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ReqModifyMyUsername) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqModifyMyPassword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 密码（需要RSA加密）
	Password string `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqModifyMyPassword) Reset() {
	*x = ReqModifyMyPassword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyMyPassword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyMyPassword) ProtoMessage() {}

func (x *ReqModifyMyPassword) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyMyPassword.ProtoReflect.Descriptor instead.
func (*ReqModifyMyPassword) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{16}
}

func (x *ReqModifyMyPassword) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ReqModifyMyPassword) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqModifyMyPhone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 手机号
	Phone string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
	// 验证码
	Otp string `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *ReqModifyMyPhone) Reset() {
	*x = ReqModifyMyPhone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyMyPhone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyMyPhone) ProtoMessage() {}

func (x *ReqModifyMyPhone) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyMyPhone.ProtoReflect.Descriptor instead.
func (*ReqModifyMyPhone) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{17}
}

func (x *ReqModifyMyPhone) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ReqModifyMyPhone) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

func (x *ReqModifyMyPhone) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type ReqModifyMyEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 邮箱
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
	// 腾讯云验证码参数
	TcloudCaptcha *base.TcloudCaptcha `protobuf:"bytes,5,opt,name=tcloud_captcha,json=tcloudCaptcha,proto3" json:"tcloud_captcha,omitempty"`
}

func (x *ReqModifyMyEmail) Reset() {
	*x = ReqModifyMyEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyMyEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyMyEmail) ProtoMessage() {}

func (x *ReqModifyMyEmail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyMyEmail.ProtoReflect.Descriptor instead.
func (*ReqModifyMyEmail) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{18}
}

func (x *ReqModifyMyEmail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ReqModifyMyEmail) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

func (x *ReqModifyMyEmail) GetTcloudCaptcha() *base.TcloudCaptcha {
	if x != nil {
		return x.TcloudCaptcha
	}
	return nil
}

type RspModifyMyEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	TaskNo string `protobuf:"bytes,1,opt,name=task_no,json=taskNo,proto3" json:"task_no,omitempty"`
	// 腾讯云SDK错误码
	SdkError string `protobuf:"bytes,2,opt,name=sdk_error,json=sdkError,proto3" json:"sdk_error,omitempty"`
}

func (x *RspModifyMyEmail) Reset() {
	*x = RspModifyMyEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspModifyMyEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspModifyMyEmail) ProtoMessage() {}

func (x *RspModifyMyEmail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspModifyMyEmail.ProtoReflect.Descriptor instead.
func (*RspModifyMyEmail) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{19}
}

func (x *RspModifyMyEmail) GetTaskNo() string {
	if x != nil {
		return x.TaskNo
	}
	return ""
}

func (x *RspModifyMyEmail) GetSdkError() string {
	if x != nil {
		return x.SdkError
	}
	return ""
}

type ReqActiveMyEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 激活邮件参数值
	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ReqActiveMyEmail) Reset() {
	*x = ReqActiveMyEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqActiveMyEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqActiveMyEmail) ProtoMessage() {}

func (x *ReqActiveMyEmail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqActiveMyEmail.ProtoReflect.Descriptor instead.
func (*ReqActiveMyEmail) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{20}
}

func (x *ReqActiveMyEmail) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ReqCreateBindMyWeixinQrcode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 2FA令牌
	TfaToken string `protobuf:"bytes,1,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqCreateBindMyWeixinQrcode) Reset() {
	*x = ReqCreateBindMyWeixinQrcode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateBindMyWeixinQrcode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateBindMyWeixinQrcode) ProtoMessage() {}

func (x *ReqCreateBindMyWeixinQrcode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateBindMyWeixinQrcode.ProtoReflect.Descriptor instead.
func (*ReqCreateBindMyWeixinQrcode) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{21}
}

func (x *ReqCreateBindMyWeixinQrcode) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type RspCreateBindMyWeixinQrcode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 二维码信息
	Qrcode *iam.WeixinQrcode `protobuf:"bytes,1,opt,name=qrcode,proto3" json:"qrcode,omitempty"`
}

func (x *RspCreateBindMyWeixinQrcode) Reset() {
	*x = RspCreateBindMyWeixinQrcode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateBindMyWeixinQrcode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateBindMyWeixinQrcode) ProtoMessage() {}

func (x *RspCreateBindMyWeixinQrcode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateBindMyWeixinQrcode.ProtoReflect.Descriptor instead.
func (*RspCreateBindMyWeixinQrcode) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{22}
}

func (x *RspCreateBindMyWeixinQrcode) GetQrcode() *iam.WeixinQrcode {
	if x != nil {
		return x.Qrcode
	}
	return nil
}

type ReqGetBindMyWeixinState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 场景值
	SceneStr string `protobuf:"bytes,1,opt,name=scene_str,json=sceneStr,proto3" json:"scene_str,omitempty"`
}

func (x *ReqGetBindMyWeixinState) Reset() {
	*x = ReqGetBindMyWeixinState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetBindMyWeixinState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetBindMyWeixinState) ProtoMessage() {}

func (x *ReqGetBindMyWeixinState) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetBindMyWeixinState.ProtoReflect.Descriptor instead.
func (*ReqGetBindMyWeixinState) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{23}
}

func (x *ReqGetBindMyWeixinState) GetSceneStr() string {
	if x != nil {
		return x.SceneStr
	}
	return ""
}

type RspGetBindMyWeixinState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 状态
	State iam.BindWeixinState `protobuf:"varint,1,opt,name=state,proto3,enum=tanlive.iam.BindWeixinState" json:"state,omitempty"`
}

func (x *RspGetBindMyWeixinState) Reset() {
	*x = RspGetBindMyWeixinState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetBindMyWeixinState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetBindMyWeixinState) ProtoMessage() {}

func (x *RspGetBindMyWeixinState) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetBindMyWeixinState.ProtoReflect.Descriptor instead.
func (*RspGetBindMyWeixinState) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{24}
}

func (x *RspGetBindMyWeixinState) GetState() iam.BindWeixinState {
	if x != nil {
		return x.State
	}
	return iam.BindWeixinState(0)
}

type ReqBindMyWeixinByOauth2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 微信静默授权返回的code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqBindMyWeixinByOauth2) Reset() {
	*x = ReqBindMyWeixinByOauth2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBindMyWeixinByOauth2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBindMyWeixinByOauth2) ProtoMessage() {}

func (x *ReqBindMyWeixinByOauth2) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBindMyWeixinByOauth2.ProtoReflect.Descriptor instead.
func (*ReqBindMyWeixinByOauth2) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{25}
}

func (x *ReqBindMyWeixinByOauth2) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ReqBindMyWeixinByOauth2) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqUnbindMyWeixin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 2FA令牌
	TfaToken string `protobuf:"bytes,1,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqUnbindMyWeixin) Reset() {
	*x = ReqUnbindMyWeixin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUnbindMyWeixin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUnbindMyWeixin) ProtoMessage() {}

func (x *ReqUnbindMyWeixin) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUnbindMyWeixin.ProtoReflect.Descriptor instead.
func (*ReqUnbindMyWeixin) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{26}
}

func (x *ReqUnbindMyWeixin) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqGetGoogleAuthUrl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 认证场景
	Scene iam.GoogleAuthScene `protobuf:"varint,1,opt,name=scene,proto3,enum=tanlive.iam.GoogleAuthScene" json:"scene,omitempty"`
	// state参数
	State string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *ReqGetGoogleAuthUrl) Reset() {
	*x = ReqGetGoogleAuthUrl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetGoogleAuthUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetGoogleAuthUrl) ProtoMessage() {}

func (x *ReqGetGoogleAuthUrl) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetGoogleAuthUrl.ProtoReflect.Descriptor instead.
func (*ReqGetGoogleAuthUrl) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{27}
}

func (x *ReqGetGoogleAuthUrl) GetScene() iam.GoogleAuthScene {
	if x != nil {
		return x.Scene
	}
	return iam.GoogleAuthScene(0)
}

func (x *ReqGetGoogleAuthUrl) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type RspGetGoogleAuthUrl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 认证URL
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *RspGetGoogleAuthUrl) Reset() {
	*x = RspGetGoogleAuthUrl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetGoogleAuthUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetGoogleAuthUrl) ProtoMessage() {}

func (x *RspGetGoogleAuthUrl) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetGoogleAuthUrl.ProtoReflect.Descriptor instead.
func (*RspGetGoogleAuthUrl) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{28}
}

func (x *RspGetGoogleAuthUrl) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ReqCreateGoogleTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 谷歌静默授权返回的code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ReqCreateGoogleTfa) Reset() {
	*x = ReqCreateGoogleTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateGoogleTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateGoogleTfa) ProtoMessage() {}

func (x *ReqCreateGoogleTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateGoogleTfa.ProtoReflect.Descriptor instead.
func (*ReqCreateGoogleTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{29}
}

func (x *ReqCreateGoogleTfa) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type ReqBindMyGoogle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 谷歌回调code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqBindMyGoogle) Reset() {
	*x = ReqBindMyGoogle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBindMyGoogle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBindMyGoogle) ProtoMessage() {}

func (x *ReqBindMyGoogle) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBindMyGoogle.ProtoReflect.Descriptor instead.
func (*ReqBindMyGoogle) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{30}
}

func (x *ReqBindMyGoogle) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ReqBindMyGoogle) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqUnbindMyGoogle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 2FA令牌
	TfaToken string `protobuf:"bytes,1,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqUnbindMyGoogle) Reset() {
	*x = ReqUnbindMyGoogle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUnbindMyGoogle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUnbindMyGoogle) ProtoMessage() {}

func (x *ReqUnbindMyGoogle) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUnbindMyGoogle.ProtoReflect.Descriptor instead.
func (*ReqUnbindMyGoogle) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{31}
}

func (x *ReqUnbindMyGoogle) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqLogin_AccountCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户名
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// 密码
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *ReqLogin_AccountCredentials) Reset() {
	*x = ReqLogin_AccountCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqLogin_AccountCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqLogin_AccountCredentials) ProtoMessage() {}

func (x *ReqLogin_AccountCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqLogin_AccountCredentials.ProtoReflect.Descriptor instead.
func (*ReqLogin_AccountCredentials) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ReqLogin_AccountCredentials) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ReqLogin_AccountCredentials) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type ReqLogin_PhoneCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 手机号
	Phone string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	// 验证码
	AuthCode string `protobuf:"bytes,2,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`
}

func (x *ReqLogin_PhoneCredentials) Reset() {
	*x = ReqLogin_PhoneCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqLogin_PhoneCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqLogin_PhoneCredentials) ProtoMessage() {}

func (x *ReqLogin_PhoneCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqLogin_PhoneCredentials.ProtoReflect.Descriptor instead.
func (*ReqLogin_PhoneCredentials) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ReqLogin_PhoneCredentials) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ReqLogin_PhoneCredentials) GetAuthCode() string {
	if x != nil {
		return x.AuthCode
	}
	return ""
}

type ReqLogin_EmailCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 邮箱
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// 验证码
	AuthCode string `protobuf:"bytes,2,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`
}

func (x *ReqLogin_EmailCredentials) Reset() {
	*x = ReqLogin_EmailCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqLogin_EmailCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqLogin_EmailCredentials) ProtoMessage() {}

func (x *ReqLogin_EmailCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqLogin_EmailCredentials.ProtoReflect.Descriptor instead.
func (*ReqLogin_EmailCredentials) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{0, 2}
}

func (x *ReqLogin_EmailCredentials) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ReqLogin_EmailCredentials) GetAuthCode() string {
	if x != nil {
		return x.AuthCode
	}
	return ""
}

type ReqLogin_ThirdCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 第三方凭证
	ThirdTicket string `protobuf:"bytes,1,opt,name=third_ticket,json=thirdTicket,proto3" json:"third_ticket,omitempty"`
}

func (x *ReqLogin_ThirdCredentials) Reset() {
	*x = ReqLogin_ThirdCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqLogin_ThirdCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqLogin_ThirdCredentials) ProtoMessage() {}

func (x *ReqLogin_ThirdCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqLogin_ThirdCredentials.ProtoReflect.Descriptor instead.
func (*ReqLogin_ThirdCredentials) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{0, 3}
}

func (x *ReqLogin_ThirdCredentials) GetThirdTicket() string {
	if x != nil {
		return x.ThirdTicket
	}
	return ""
}

type RspLogin_UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image       string            `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	IdentitySet base.IdentityType `protobuf:"varint,2,opt,name=identity_set,json=identitySet,proto3,enum=tanlive.base.IdentityType" json:"identity_set,omitempty"`
	Level       string            `protobuf:"bytes,3,opt,name=level,proto3" json:"level,omitempty"`
	UserName    string            `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	FirmName    string            `protobuf:"bytes,5,opt,name=firm_name,json=firmName,proto3" json:"firm_name,omitempty"`
}

func (x *RspLogin_UserInfo) Reset() {
	*x = RspLogin_UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspLogin_UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspLogin_UserInfo) ProtoMessage() {}

func (x *RspLogin_UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_bff_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspLogin_UserInfo.ProtoReflect.Descriptor instead.
func (*RspLogin_UserInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_bff_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RspLogin_UserInfo) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *RspLogin_UserInfo) GetIdentitySet() base.IdentityType {
	if x != nil {
		return x.IdentitySet
	}
	return base.IdentityType(0)
}

func (x *RspLogin_UserInfo) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *RspLogin_UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *RspLogin_UserInfo) GetFirmName() string {
	if x != nil {
		return x.FirmName
	}
	return ""
}

var File_tanlive_bff_web_iam_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_iam_bff_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x69, 0x61, 0x6d, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66,
	0x2d, 0x77, 0x65, 0x62, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x69, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x61, 0x6d, 0x2f,
	0x69, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x9c, 0x08, 0x0a, 0x08, 0x52, 0x65, 0x71, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x4b, 0x0a,
	0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x79, 0x0a, 0x13, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65,
	0x71, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x72,
	0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x42, 0x16, 0x82, 0x88, 0x27, 0x12, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x54, 0x79, 0x70, 0x65, 0x20,
	0x31, 0x52, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x73, 0x0a, 0x11, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x63,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x42, 0x16, 0x82, 0x88, 0x27, 0x12, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69,
	0x66, 0x3d, 0x54, 0x79, 0x70, 0x65, 0x20, 0x32, 0x52, 0x10, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x43,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x73, 0x0a, 0x11, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x73, 0x42, 0x16, 0x82, 0x88, 0x27, 0x12, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x54, 0x79, 0x70, 0x65, 0x20, 0x33, 0x52, 0x10, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12,
	0x73, 0x0a, 0x11, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x65, 0x71, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x43,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x42, 0x16, 0x82, 0x88, 0x27, 0x12,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x54, 0x79, 0x70, 0x65,
	0x20, 0x34, 0x52, 0x10, 0x74, 0x68, 0x69, 0x72, 0x64, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x74, 0x68, 0x69,
	0x72, 0x64, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x62, 0x69, 0x6e, 0x64, 0x54, 0x68, 0x69, 0x72, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x12, 0x50, 0x0a, 0x0e, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x0d, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x1a, 0x68, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x28, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x1a, 0x61, 0x0a, 0x10,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x12, 0x22, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x12, 0x29, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x1a,
	0x67, 0x0a, 0x10, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x2c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x29, 0x0a,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08,
	0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x35, 0x0a, 0x10, 0x54, 0x68, 0x69, 0x72,
	0x64, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x74, 0x68, 0x69, 0x72, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22,
	0xcd, 0x02, 0x0a, 0x08, 0x52, 0x73, 0x70, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x43, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65,
	0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2f, 0x0a, 0x05, 0x6d, 0x65, 0x6e, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4d, 0x65, 0x6e, 0x75, 0x52, 0x05, 0x6d, 0x65, 0x6e,
	0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6e, 0x65, 0x77, 0x55, 0x73, 0x65, 0x72, 0x1a, 0xaf, 0x01,
	0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x3d, 0x0a, 0x0c, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x72, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xcc, 0x03, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68, 0x4f,
	0x74, 0x70, 0x12, 0x4b, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x4f, 0x74, 0x70, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x42, 0x1e, 0x82, 0x88, 0x27, 0x1a, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x3d, 0x31, 0x20, 0x32,
	0x20, 0x34, 0x20, 0x36, 0x20, 0x37, 0x20, 0x39, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x12,
	0x57, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x6b, 0x69, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4f, 0x74, 0x70,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x60, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x39, 0x82, 0x88, 0x27, 0x2a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69,
	0x66, 0x3d, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64, 0x20, 0x31,
	0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x8a, 0x88, 0x27, 0x07, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x60, 0x0a, 0x0e, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x39, 0x82, 0x88, 0x27, 0x2a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x69, 0x66, 0x3d, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64,
	0x20, 0x32, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x8a, 0x88, 0x27, 0x07, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x0d, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x50, 0x0a, 0x0e,
	0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x0d, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x22, 0x46,
	0x0a, 0x0e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68, 0x4f, 0x74, 0x70,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x64, 0x6b,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x64,
	0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0xba, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x53, 0x65,
	0x6e, 0x64, 0x54, 0x66, 0x61, 0x4f, 0x74, 0x70, 0x12, 0x57, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65,
	0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x72, 0x4b, 0x69, 0x6e, 0x64, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4b, 0x69, 0x6e,
	0x64, 0x12, 0x50, 0x0a, 0x0e, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x43,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x0d, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x22, 0x45, 0x0a, 0x0d, 0x52, 0x73, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x66,
	0x61, 0x4f, 0x74, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x6f, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x64, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x64, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x41, 0x0a, 0x0b, 0x52, 0x73,
	0x70, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x54, 0x66, 0x61, 0x12, 0x32, 0x0a, 0x09, 0x74, 0x66, 0x61,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x54, 0x66, 0x61, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x33, 0x0a,
	0x11, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x54,
	0x66, 0x61, 0x12, 0x1e, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x03, 0x6f,
	0x74, 0x70, 0x22, 0x42, 0x0a, 0x0c, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x66, 0x61, 0x12, 0x32, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x54, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x74, 0x66,
	0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x4c, 0x0a, 0x14, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x54, 0x66, 0x61, 0x12, 0x34,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88,
	0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x22, 0x33, 0x0a, 0x11, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x66, 0x61, 0x12, 0x1e, 0x0a, 0x03, 0x6f, 0x74, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x22, 0x4d, 0x0a, 0x18, 0x52, 0x73, 0x70,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x51,
	0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x71, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65,
	0x52, 0x06, 0x71, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x47, 0x0a, 0x1a, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66,
	0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f,
	0x73, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x53, 0x74,
	0x72, 0x22, 0x89, 0x01, 0x0a, 0x1a, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x37, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x74, 0x66, 0x61,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x54, 0x66, 0x61, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x3d, 0x0a,
	0x19, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e,
	0x42, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x20, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x84, 0x01, 0x0a,
	0x13, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x79, 0x55, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x0a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x09,
	0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27,
	0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x82, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x4d, 0x79, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x34, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a,
	0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08,
	0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x71,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x33, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x82, 0x88,
	0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x8a, 0x88, 0x27, 0x07, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52,
	0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2a, 0x0a, 0x03, 0x6f, 0x74, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x52, 0x03, 0x6f, 0x74, 0x70, 0x22, 0xd0, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x4d, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x82, 0x88, 0x27, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x8a, 0x88, 0x27,
	0x07, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x35, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x74, 0x66,
	0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x50, 0x0a, 0x0e, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0d, 0x74, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x22, 0x48, 0x0a, 0x10, 0x52, 0x73, 0x70, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x22, 0x36, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d,
	0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x22, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x54, 0x0a, 0x1b, 0x52, 0x65,
	0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69,
	0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x66, 0x61,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x50, 0x0a, 0x1b, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6e,
	0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x31, 0x0a, 0x06, 0x71, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x57, 0x65,
	0x69, 0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x71, 0x72, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x44, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64,
	0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a,
	0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x53, 0x74, 0x72, 0x22, 0x4d, 0x0a, 0x17, 0x52, 0x73, 0x70, 0x47,
	0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x72, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x42, 0x69,
	0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x79, 0x4f, 0x61, 0x75, 0x74,
	0x68, 0x32, 0x12, 0x20, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x4a, 0x0a, 0x11, 0x52,
	0x65, 0x71, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e,
	0x12, 0x35, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x74,
	0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7b, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x47, 0x65,
	0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x40,
	0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x22, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x27, 0x0a, 0x13, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x28, 0x0a,
	0x12, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x54, 0x66, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x76, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x42, 0x69,
	0x6e, 0x64, 0x4d, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22,
	0x4a, 0x0a, 0x11, 0x52, 0x65, 0x71, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0x92, 0x15, 0x0a, 0x06,
	0x49, 0x61, 0x6d, 0x42, 0x66, 0x66, 0x12, 0x50, 0x0a, 0x06, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74,
	0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x69, 0x61,
	0x6d, 0x2f, 0x6c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x12, 0x72, 0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64,
	0x54, 0x66, 0x61, 0x4f, 0x74, 0x70, 0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71,
	0x53, 0x65, 0x6e, 0x64, 0x54, 0x66, 0x61, 0x4f, 0x74, 0x70, 0x1a, 0x22, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x66, 0x61, 0x4f, 0x74, 0x70, 0x22, 0x1c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x69, 0x61, 0x6d, 0x2f,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x32, 0x66, 0x61, 0x5f, 0x6f, 0x74, 0x70, 0x12, 0x60, 0x0a, 0x08,
	0x47, 0x65, 0x74, 0x4d, 0x79, 0x54, 0x66, 0x61, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x54,
	0x66, 0x61, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f,
	0x69, 0x61, 0x6d, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x79, 0x5f, 0x32, 0x66, 0x61, 0x12, 0x7d,
	0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x54, 0x66, 0x61,
	0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x54, 0x66, 0x61, 0x1a, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x66, 0x61, 0x22, 0x20, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x32, 0x66, 0x61, 0x12, 0x86, 0x01,
	0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x54, 0x66, 0x61, 0x12, 0x29, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x54, 0x66, 0x61, 0x1a, 0x21,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x66,
	0x61, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x69,
	0x61, 0x6d, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x5f, 0x32, 0x66, 0x61, 0x12, 0x7d, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x66, 0x61, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x66, 0x61,
	0x1a, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x66, 0x61, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15,
	0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x32, 0x66, 0x61, 0x12, 0x88, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73,
	0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61,
	0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01,
	0x2a, 0x22, 0x1d, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x77,
	0x65, 0x69, 0x78, 0x69, 0x6e, 0x5f, 0x32, 0x66, 0x61, 0x5f, 0x71, 0x72, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0xa8, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65,
	0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x1a, 0x2f, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x2b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x69, 0x61, 0x6d, 0x2f,
	0x67, 0x65, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x65, 0x69, 0x78, 0x69,
	0x6e, 0x5f, 0x32, 0x66, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x16,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x72, 0x6f, 0x77,
	0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x2e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x72, 0x6f, 0x77,
	0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x1a, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x66, 0x61, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x77, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x5f, 0x62, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72,
	0x5f, 0x32, 0x66, 0x61, 0x12, 0x78, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x79,
	0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x79, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x5f, 0x6d, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x78,
	0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x79, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x4d, 0x79, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22,
	0x17, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x6d, 0x79, 0x5f,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x6f, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x4d, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19,
	0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x5f, 0x6d, 0x79, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x7e, 0x0a, 0x0d, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x4d, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x79, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x1a, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x4d, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19,
	0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x5f, 0x6d, 0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x6f, 0x0a, 0x0d, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x4d, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x65, 0x71, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x79, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x6d, 0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0xac, 0x01, 0x0a, 0x18, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69,
	0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65,
	0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69,
	0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x1a, 0x30, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57,
	0x65, 0x69, 0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x6d, 0x79, 0x5f, 0x77, 0x65, 0x69, 0x78,
	0x69, 0x6e, 0x5f, 0x71, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x42, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x2c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x42,
	0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x1a, 0x2c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77,
	0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e,
	0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x28,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x69, 0x61, 0x6d, 0x2f,
	0x67, 0x65, 0x74, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x6d, 0x79, 0x5f, 0x77, 0x65, 0x69, 0x78,
	0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x14, 0x42, 0x69, 0x6e,
	0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x79, 0x4f, 0x61, 0x75, 0x74, 0x68,
	0x32, 0x12, 0x2c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x42, 0x69, 0x6e, 0x64, 0x4d,
	0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x79, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a,
	0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x6d, 0x79,
	0x5f, 0x77, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x5f, 0x62, 0x79, 0x5f, 0x6f, 0x61, 0x75, 0x74, 0x68,
	0x32, 0x12, 0x72, 0x0a, 0x0e, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69,
	0x78, 0x69, 0x6e, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x6e, 0x62,
	0x69, 0x6e, 0x64, 0x4d, 0x79, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15,
	0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x75, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x6d, 0x79, 0x5f, 0x77,
	0x65, 0x69, 0x78, 0x69, 0x6e, 0x12, 0x80, 0x01, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x54, 0x66, 0x61, 0x12, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x54,
	0x66, 0x61, 0x1a, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x66, 0x61, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a,
	0x22, 0x16, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x32, 0x66, 0x61, 0x12, 0x6c, 0x0a, 0x0c, 0x42, 0x69, 0x6e, 0x64,
	0x4d, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x65, 0x71, 0x42, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01,
	0x2a, 0x22, 0x13, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x6d, 0x79, 0x5f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x72, 0x0a, 0x0e, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64,
	0x4d, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x65, 0x71, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a,
	0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x75, 0x6e, 0x62, 0x69, 0x6e, 0x64,
	0x5f, 0x6d, 0x79, 0x5f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01,
	0x32, 0x84, 0x03, 0x0a, 0x0b, 0x49, 0x61, 0x6d, 0x47, 0x75, 0x65, 0x73, 0x74, 0x42, 0x66, 0x66,
	0x12, 0x5c, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x65, 0x71, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x1a, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x73, 0x70, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x3a,
	0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x82,
	0x01, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68, 0x4f, 0x74, 0x70, 0x12, 0x23,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68,
	0x4f, 0x74, 0x70, 0x1a, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x6e,
	0x64, 0x41, 0x75, 0x74, 0x68, 0x4f, 0x74, 0x70, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23,
	0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2f, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x12, 0x8b, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x65, 0x71, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x55,
	0x72, 0x6c, 0x1a, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x77, 0x65, 0x62, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x22, 0x23, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x67, 0x65,
	0x74, 0x5f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x75, 0x72,
	0x6c, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01, 0x42, 0x3d, 0x5a, 0x3b, 0x65, 0x2e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77,
	0x65, 0x62, 0x2f, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_web_iam_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_iam_bff_proto_rawDescData = file_tanlive_bff_web_iam_bff_proto_rawDesc
)

func file_tanlive_bff_web_iam_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_iam_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_iam_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_iam_bff_proto_rawDescData)
	})
	return file_tanlive_bff_web_iam_bff_proto_rawDescData
}

var file_tanlive_bff_web_iam_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_tanlive_bff_web_iam_bff_proto_goTypes = []interface{}{
	(*ReqLogin)(nil),                    // 0: tanlive.bff_web.iam.ReqLogin
	(*RspLogin)(nil),                    // 1: tanlive.bff_web.iam.RspLogin
	(*ReqSendAuthOtp)(nil),              // 2: tanlive.bff_web.iam.ReqSendAuthOtp
	(*RspSendAuthOtp)(nil),              // 3: tanlive.bff_web.iam.RspSendAuthOtp
	(*ReqSendTfaOtp)(nil),               // 4: tanlive.bff_web.iam.ReqSendTfaOtp
	(*RspSendTfaOtp)(nil),               // 5: tanlive.bff_web.iam.RspSendTfaOtp
	(*RspGetMyTfa)(nil),                 // 6: tanlive.bff_web.iam.RspGetMyTfa
	(*ReqCreatePhoneTfa)(nil),           // 7: tanlive.bff_web.iam.ReqCreatePhoneTfa
	(*RspCreateTfa)(nil),                // 8: tanlive.bff_web.iam.RspCreateTfa
	(*ReqCreatePasswordTfa)(nil),        // 9: tanlive.bff_web.iam.ReqCreatePasswordTfa
	(*ReqCreateEmailTfa)(nil),           // 10: tanlive.bff_web.iam.ReqCreateEmailTfa
	(*RspCreateWeixinTfaQrcode)(nil),    // 11: tanlive.bff_web.iam.RspCreateWeixinTfaQrcode
	(*ReqGetCreateWeixinTfaState)(nil),  // 12: tanlive.bff_web.iam.ReqGetCreateWeixinTfaState
	(*RspGetCreateWeixinTfaState)(nil),  // 13: tanlive.bff_web.iam.RspGetCreateWeixinTfaState
	(*ReqCreateWeixinBrowserTfa)(nil),   // 14: tanlive.bff_web.iam.ReqCreateWeixinBrowserTfa
	(*ReqModifyMyUsername)(nil),         // 15: tanlive.bff_web.iam.ReqModifyMyUsername
	(*ReqModifyMyPassword)(nil),         // 16: tanlive.bff_web.iam.ReqModifyMyPassword
	(*ReqModifyMyPhone)(nil),            // 17: tanlive.bff_web.iam.ReqModifyMyPhone
	(*ReqModifyMyEmail)(nil),            // 18: tanlive.bff_web.iam.ReqModifyMyEmail
	(*RspModifyMyEmail)(nil),            // 19: tanlive.bff_web.iam.RspModifyMyEmail
	(*ReqActiveMyEmail)(nil),            // 20: tanlive.bff_web.iam.ReqActiveMyEmail
	(*ReqCreateBindMyWeixinQrcode)(nil), // 21: tanlive.bff_web.iam.ReqCreateBindMyWeixinQrcode
	(*RspCreateBindMyWeixinQrcode)(nil), // 22: tanlive.bff_web.iam.RspCreateBindMyWeixinQrcode
	(*ReqGetBindMyWeixinState)(nil),     // 23: tanlive.bff_web.iam.ReqGetBindMyWeixinState
	(*RspGetBindMyWeixinState)(nil),     // 24: tanlive.bff_web.iam.RspGetBindMyWeixinState
	(*ReqBindMyWeixinByOauth2)(nil),     // 25: tanlive.bff_web.iam.ReqBindMyWeixinByOauth2
	(*ReqUnbindMyWeixin)(nil),           // 26: tanlive.bff_web.iam.ReqUnbindMyWeixin
	(*ReqGetGoogleAuthUrl)(nil),         // 27: tanlive.bff_web.iam.ReqGetGoogleAuthUrl
	(*RspGetGoogleAuthUrl)(nil),         // 28: tanlive.bff_web.iam.RspGetGoogleAuthUrl
	(*ReqCreateGoogleTfa)(nil),          // 29: tanlive.bff_web.iam.ReqCreateGoogleTfa
	(*ReqBindMyGoogle)(nil),             // 30: tanlive.bff_web.iam.ReqBindMyGoogle
	(*ReqUnbindMyGoogle)(nil),           // 31: tanlive.bff_web.iam.ReqUnbindMyGoogle
	(*ReqLogin_AccountCredentials)(nil), // 32: tanlive.bff_web.iam.ReqLogin.AccountCredentials
	(*ReqLogin_PhoneCredentials)(nil),   // 33: tanlive.bff_web.iam.ReqLogin.PhoneCredentials
	(*ReqLogin_EmailCredentials)(nil),   // 34: tanlive.bff_web.iam.ReqLogin.EmailCredentials
	(*ReqLogin_ThirdCredentials)(nil),   // 35: tanlive.bff_web.iam.ReqLogin.ThirdCredentials
	(*RspLogin_UserInfo)(nil),           // 36: tanlive.bff_web.iam.RspLogin.UserInfo
	(LoginType)(0),                      // 37: tanlive.bff_web.iam.LoginType
	(*base.TcloudCaptcha)(nil),          // 38: tanlive.base.TcloudCaptcha
	(*Menu)(nil),                        // 39: tanlive.bff_web.iam.Menu
	(iam.OtpScene)(0),                   // 40: tanlive.iam.OtpScene
	(OtpReceiverKind)(0),                // 41: tanlive.bff_web.iam.OtpReceiverKind
	(*iam.TfaToken)(nil),                // 42: tanlive.iam.TfaToken
	(*iam.WeixinQrcode)(nil),            // 43: tanlive.iam.WeixinQrcode
	(iam.CreateWeixinTfaState)(0),       // 44: tanlive.iam.CreateWeixinTfaState
	(iam.BindWeixinState)(0),            // 45: tanlive.iam.BindWeixinState
	(iam.GoogleAuthScene)(0),            // 46: tanlive.iam.GoogleAuthScene
	(base.IdentityType)(0),              // 47: tanlive.base.IdentityType
	(*emptypb.Empty)(nil),               // 48: google.protobuf.Empty
}
var file_tanlive_bff_web_iam_bff_proto_depIdxs = []int32{
	37, // 0: tanlive.bff_web.iam.ReqLogin.login_type:type_name -> tanlive.bff_web.iam.LoginType
	32, // 1: tanlive.bff_web.iam.ReqLogin.account_credentials:type_name -> tanlive.bff_web.iam.ReqLogin.AccountCredentials
	33, // 2: tanlive.bff_web.iam.ReqLogin.phone_credentials:type_name -> tanlive.bff_web.iam.ReqLogin.PhoneCredentials
	34, // 3: tanlive.bff_web.iam.ReqLogin.email_credentials:type_name -> tanlive.bff_web.iam.ReqLogin.EmailCredentials
	35, // 4: tanlive.bff_web.iam.ReqLogin.third_credentials:type_name -> tanlive.bff_web.iam.ReqLogin.ThirdCredentials
	38, // 5: tanlive.bff_web.iam.ReqLogin.tcloud_captcha:type_name -> tanlive.base.TcloudCaptcha
	36, // 6: tanlive.bff_web.iam.RspLogin.user_info:type_name -> tanlive.bff_web.iam.RspLogin.UserInfo
	39, // 7: tanlive.bff_web.iam.RspLogin.menus:type_name -> tanlive.bff_web.iam.Menu
	40, // 8: tanlive.bff_web.iam.ReqSendAuthOtp.scene:type_name -> tanlive.iam.OtpScene
	41, // 9: tanlive.bff_web.iam.ReqSendAuthOtp.receiver_kind:type_name -> tanlive.bff_web.iam.OtpReceiverKind
	38, // 10: tanlive.bff_web.iam.ReqSendAuthOtp.tcloud_captcha:type_name -> tanlive.base.TcloudCaptcha
	41, // 11: tanlive.bff_web.iam.ReqSendTfaOtp.receiver_kind:type_name -> tanlive.bff_web.iam.OtpReceiverKind
	38, // 12: tanlive.bff_web.iam.ReqSendTfaOtp.tcloud_captcha:type_name -> tanlive.base.TcloudCaptcha
	42, // 13: tanlive.bff_web.iam.RspGetMyTfa.tfa_token:type_name -> tanlive.iam.TfaToken
	42, // 14: tanlive.bff_web.iam.RspCreateTfa.tfa_token:type_name -> tanlive.iam.TfaToken
	43, // 15: tanlive.bff_web.iam.RspCreateWeixinTfaQrcode.qrcode:type_name -> tanlive.iam.WeixinQrcode
	44, // 16: tanlive.bff_web.iam.RspGetCreateWeixinTfaState.state:type_name -> tanlive.iam.CreateWeixinTfaState
	42, // 17: tanlive.bff_web.iam.RspGetCreateWeixinTfaState.tfa_token:type_name -> tanlive.iam.TfaToken
	38, // 18: tanlive.bff_web.iam.ReqModifyMyEmail.tcloud_captcha:type_name -> tanlive.base.TcloudCaptcha
	43, // 19: tanlive.bff_web.iam.RspCreateBindMyWeixinQrcode.qrcode:type_name -> tanlive.iam.WeixinQrcode
	45, // 20: tanlive.bff_web.iam.RspGetBindMyWeixinState.state:type_name -> tanlive.iam.BindWeixinState
	46, // 21: tanlive.bff_web.iam.ReqGetGoogleAuthUrl.scene:type_name -> tanlive.iam.GoogleAuthScene
	47, // 22: tanlive.bff_web.iam.RspLogin.UserInfo.identity_set:type_name -> tanlive.base.IdentityType
	48, // 23: tanlive.bff_web.iam.IamBff.Logout:input_type -> google.protobuf.Empty
	4,  // 24: tanlive.bff_web.iam.IamBff.SendTfaOtp:input_type -> tanlive.bff_web.iam.ReqSendTfaOtp
	48, // 25: tanlive.bff_web.iam.IamBff.GetMyTfa:input_type -> google.protobuf.Empty
	7,  // 26: tanlive.bff_web.iam.IamBff.CreatePhoneTfa:input_type -> tanlive.bff_web.iam.ReqCreatePhoneTfa
	9,  // 27: tanlive.bff_web.iam.IamBff.CreatePasswordTfa:input_type -> tanlive.bff_web.iam.ReqCreatePasswordTfa
	10, // 28: tanlive.bff_web.iam.IamBff.CreateEmailTfa:input_type -> tanlive.bff_web.iam.ReqCreateEmailTfa
	48, // 29: tanlive.bff_web.iam.IamBff.CreateWeixinTfaQrcode:input_type -> google.protobuf.Empty
	12, // 30: tanlive.bff_web.iam.IamBff.GetCreateWeixinTfaState:input_type -> tanlive.bff_web.iam.ReqGetCreateWeixinTfaState
	14, // 31: tanlive.bff_web.iam.IamBff.CreateWeixinBrowserTfa:input_type -> tanlive.bff_web.iam.ReqCreateWeixinBrowserTfa
	15, // 32: tanlive.bff_web.iam.IamBff.ModifyMyUsername:input_type -> tanlive.bff_web.iam.ReqModifyMyUsername
	16, // 33: tanlive.bff_web.iam.IamBff.ModifyMyPassword:input_type -> tanlive.bff_web.iam.ReqModifyMyPassword
	17, // 34: tanlive.bff_web.iam.IamBff.ModifyMyPhone:input_type -> tanlive.bff_web.iam.ReqModifyMyPhone
	18, // 35: tanlive.bff_web.iam.IamBff.ModifyMyEmail:input_type -> tanlive.bff_web.iam.ReqModifyMyEmail
	20, // 36: tanlive.bff_web.iam.IamBff.ActiveMyEmail:input_type -> tanlive.bff_web.iam.ReqActiveMyEmail
	21, // 37: tanlive.bff_web.iam.IamBff.CreateBindMyWeixinQrcode:input_type -> tanlive.bff_web.iam.ReqCreateBindMyWeixinQrcode
	23, // 38: tanlive.bff_web.iam.IamBff.GetBindMyWeixinState:input_type -> tanlive.bff_web.iam.ReqGetBindMyWeixinState
	25, // 39: tanlive.bff_web.iam.IamBff.BindMyWeixinByOauth2:input_type -> tanlive.bff_web.iam.ReqBindMyWeixinByOauth2
	26, // 40: tanlive.bff_web.iam.IamBff.UnbindMyWeixin:input_type -> tanlive.bff_web.iam.ReqUnbindMyWeixin
	29, // 41: tanlive.bff_web.iam.IamBff.CreateGoogleTfa:input_type -> tanlive.bff_web.iam.ReqCreateGoogleTfa
	30, // 42: tanlive.bff_web.iam.IamBff.BindMyGoogle:input_type -> tanlive.bff_web.iam.ReqBindMyGoogle
	31, // 43: tanlive.bff_web.iam.IamBff.UnbindMyGoogle:input_type -> tanlive.bff_web.iam.ReqUnbindMyGoogle
	0,  // 44: tanlive.bff_web.iam.IamGuestBff.Login:input_type -> tanlive.bff_web.iam.ReqLogin
	2,  // 45: tanlive.bff_web.iam.IamGuestBff.SendAuthOtp:input_type -> tanlive.bff_web.iam.ReqSendAuthOtp
	27, // 46: tanlive.bff_web.iam.IamGuestBff.GetGoogleAuthUrl:input_type -> tanlive.bff_web.iam.ReqGetGoogleAuthUrl
	48, // 47: tanlive.bff_web.iam.IamBff.Logout:output_type -> google.protobuf.Empty
	5,  // 48: tanlive.bff_web.iam.IamBff.SendTfaOtp:output_type -> tanlive.bff_web.iam.RspSendTfaOtp
	6,  // 49: tanlive.bff_web.iam.IamBff.GetMyTfa:output_type -> tanlive.bff_web.iam.RspGetMyTfa
	8,  // 50: tanlive.bff_web.iam.IamBff.CreatePhoneTfa:output_type -> tanlive.bff_web.iam.RspCreateTfa
	8,  // 51: tanlive.bff_web.iam.IamBff.CreatePasswordTfa:output_type -> tanlive.bff_web.iam.RspCreateTfa
	8,  // 52: tanlive.bff_web.iam.IamBff.CreateEmailTfa:output_type -> tanlive.bff_web.iam.RspCreateTfa
	11, // 53: tanlive.bff_web.iam.IamBff.CreateWeixinTfaQrcode:output_type -> tanlive.bff_web.iam.RspCreateWeixinTfaQrcode
	13, // 54: tanlive.bff_web.iam.IamBff.GetCreateWeixinTfaState:output_type -> tanlive.bff_web.iam.RspGetCreateWeixinTfaState
	8,  // 55: tanlive.bff_web.iam.IamBff.CreateWeixinBrowserTfa:output_type -> tanlive.bff_web.iam.RspCreateTfa
	48, // 56: tanlive.bff_web.iam.IamBff.ModifyMyUsername:output_type -> google.protobuf.Empty
	48, // 57: tanlive.bff_web.iam.IamBff.ModifyMyPassword:output_type -> google.protobuf.Empty
	48, // 58: tanlive.bff_web.iam.IamBff.ModifyMyPhone:output_type -> google.protobuf.Empty
	19, // 59: tanlive.bff_web.iam.IamBff.ModifyMyEmail:output_type -> tanlive.bff_web.iam.RspModifyMyEmail
	48, // 60: tanlive.bff_web.iam.IamBff.ActiveMyEmail:output_type -> google.protobuf.Empty
	22, // 61: tanlive.bff_web.iam.IamBff.CreateBindMyWeixinQrcode:output_type -> tanlive.bff_web.iam.RspCreateBindMyWeixinQrcode
	24, // 62: tanlive.bff_web.iam.IamBff.GetBindMyWeixinState:output_type -> tanlive.bff_web.iam.RspGetBindMyWeixinState
	48, // 63: tanlive.bff_web.iam.IamBff.BindMyWeixinByOauth2:output_type -> google.protobuf.Empty
	48, // 64: tanlive.bff_web.iam.IamBff.UnbindMyWeixin:output_type -> google.protobuf.Empty
	8,  // 65: tanlive.bff_web.iam.IamBff.CreateGoogleTfa:output_type -> tanlive.bff_web.iam.RspCreateTfa
	48, // 66: tanlive.bff_web.iam.IamBff.BindMyGoogle:output_type -> google.protobuf.Empty
	48, // 67: tanlive.bff_web.iam.IamBff.UnbindMyGoogle:output_type -> google.protobuf.Empty
	1,  // 68: tanlive.bff_web.iam.IamGuestBff.Login:output_type -> tanlive.bff_web.iam.RspLogin
	3,  // 69: tanlive.bff_web.iam.IamGuestBff.SendAuthOtp:output_type -> tanlive.bff_web.iam.RspSendAuthOtp
	28, // 70: tanlive.bff_web.iam.IamGuestBff.GetGoogleAuthUrl:output_type -> tanlive.bff_web.iam.RspGetGoogleAuthUrl
	47, // [47:71] is the sub-list for method output_type
	23, // [23:47] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_iam_bff_proto_init() }
func file_tanlive_bff_web_iam_bff_proto_init() {
	if File_tanlive_bff_web_iam_bff_proto != nil {
		return
	}
	file_tanlive_bff_web_iam_iam_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_iam_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqLogin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspLogin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSendAuthOtp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSendAuthOtp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSendTfaOtp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSendTfaOtp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetMyTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreatePhoneTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreatePasswordTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateEmailTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateWeixinTfaQrcode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetCreateWeixinTfaState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetCreateWeixinTfaState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateWeixinBrowserTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyMyUsername); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyMyPassword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyMyPhone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyMyEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspModifyMyEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqActiveMyEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateBindMyWeixinQrcode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateBindMyWeixinQrcode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetBindMyWeixinState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetBindMyWeixinState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBindMyWeixinByOauth2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUnbindMyWeixin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetGoogleAuthUrl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetGoogleAuthUrl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateGoogleTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBindMyGoogle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUnbindMyGoogle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqLogin_AccountCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqLogin_PhoneCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqLogin_EmailCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqLogin_ThirdCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_iam_bff_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspLogin_UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_iam_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_tanlive_bff_web_iam_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_iam_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_web_iam_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_iam_bff_proto = out.File
	file_tanlive_bff_web_iam_bff_proto_rawDesc = nil
	file_tanlive_bff_web_iam_bff_proto_goTypes = nil
	file_tanlive_bff_web_iam_bff_proto_depIdxs = nil
}
