syntax = "proto3";

package tanlive.bff_web.iam;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/iam";

// 登录类型
enum LoginType {
  LOGIN_TYPE_UNSPECIFIED = 0;
  // 用户名登录
  LOGIN_TYPE_USERNAME = 1;
  // 手机验证码登录
  LOGIN_TYPE_PHONE = 2;
  // 邮箱验证码登录
  LOGIN_TYPE_EMAIL = 3;
  // 第三方登录
  LOGIN_TYPE_THIRD = 4;
}

// 菜单信息
message Menu {
  string item_code = 1;
  string item_name = 2;
  fixed32 level = 3;
  string item_desc = 4;
  string code = 5;
  string name = 6;
  bool can_view = 7;
  bool can_authorize = 8;
  bool only_holder = 9;
  fixed32 auth_action = 10;
}

// 验证码接收者类型
enum OtpReceiverKind {
  OTP_RECEIVER_KIND_UNSPECIFIED = 0;
  // 手机号码
  OTP_RECEIVER_KIND_PHONE = 1;
  // 邮件
  OTP_RECEIVER_KIND_EMAIL = 2;
}
