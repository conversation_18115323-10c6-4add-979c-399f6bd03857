// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/iam/iam.proto

package iam

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 登录类型
type LoginType int32

const (
	LoginType_LOGIN_TYPE_UNSPECIFIED LoginType = 0
	// 用户名登录
	LoginType_LOGIN_TYPE_USERNAME LoginType = 1
	// 手机验证码登录
	LoginType_LOGIN_TYPE_PHONE LoginType = 2
	// 邮箱验证码登录
	LoginType_LOGIN_TYPE_EMAIL LoginType = 3
	// 第三方登录
	LoginType_LOGIN_TYPE_THIRD LoginType = 4
)

// Enum value maps for LoginType.
var (
	LoginType_name = map[int32]string{
		0: "LOGIN_TYPE_UNSPECIFIED",
		1: "LOGIN_TYPE_USERNAME",
		2: "LOGIN_TYPE_PHONE",
		3: "LOGIN_TYPE_EMAIL",
		4: "LOGIN_TYPE_THIRD",
	}
	LoginType_value = map[string]int32{
		"LOGIN_TYPE_UNSPECIFIED": 0,
		"LOGIN_TYPE_USERNAME":    1,
		"LOGIN_TYPE_PHONE":       2,
		"LOGIN_TYPE_EMAIL":       3,
		"LOGIN_TYPE_THIRD":       4,
	}
)

func (x LoginType) Enum() *LoginType {
	p := new(LoginType)
	*p = x
	return p
}

func (x LoginType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoginType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_bff_web_iam_iam_proto_enumTypes[0].Descriptor()
}

func (LoginType) Type() protoreflect.EnumType {
	return &file_tanlive_bff_web_iam_iam_proto_enumTypes[0]
}

func (x LoginType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoginType.Descriptor instead.
func (LoginType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_iam_proto_rawDescGZIP(), []int{0}
}

// 验证码接收者类型
type OtpReceiverKind int32

const (
	OtpReceiverKind_OTP_RECEIVER_KIND_UNSPECIFIED OtpReceiverKind = 0
	// 手机号码
	OtpReceiverKind_OTP_RECEIVER_KIND_PHONE OtpReceiverKind = 1
	// 邮件
	OtpReceiverKind_OTP_RECEIVER_KIND_EMAIL OtpReceiverKind = 2
)

// Enum value maps for OtpReceiverKind.
var (
	OtpReceiverKind_name = map[int32]string{
		0: "OTP_RECEIVER_KIND_UNSPECIFIED",
		1: "OTP_RECEIVER_KIND_PHONE",
		2: "OTP_RECEIVER_KIND_EMAIL",
	}
	OtpReceiverKind_value = map[string]int32{
		"OTP_RECEIVER_KIND_UNSPECIFIED": 0,
		"OTP_RECEIVER_KIND_PHONE":       1,
		"OTP_RECEIVER_KIND_EMAIL":       2,
	}
)

func (x OtpReceiverKind) Enum() *OtpReceiverKind {
	p := new(OtpReceiverKind)
	*p = x
	return p
}

func (x OtpReceiverKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OtpReceiverKind) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_bff_web_iam_iam_proto_enumTypes[1].Descriptor()
}

func (OtpReceiverKind) Type() protoreflect.EnumType {
	return &file_tanlive_bff_web_iam_iam_proto_enumTypes[1]
}

func (x OtpReceiverKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OtpReceiverKind.Descriptor instead.
func (OtpReceiverKind) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_iam_proto_rawDescGZIP(), []int{1}
}

// 菜单信息
type Menu struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemCode     string `protobuf:"bytes,1,opt,name=item_code,json=itemCode,proto3" json:"item_code,omitempty"`
	ItemName     string `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Level        uint32 `protobuf:"fixed32,3,opt,name=level,proto3" json:"level,omitempty"`
	ItemDesc     string `protobuf:"bytes,4,opt,name=item_desc,json=itemDesc,proto3" json:"item_desc,omitempty"`
	Code         string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	Name         string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	CanView      bool   `protobuf:"varint,7,opt,name=can_view,json=canView,proto3" json:"can_view,omitempty"`
	CanAuthorize bool   `protobuf:"varint,8,opt,name=can_authorize,json=canAuthorize,proto3" json:"can_authorize,omitempty"`
	OnlyHolder   bool   `protobuf:"varint,9,opt,name=only_holder,json=onlyHolder,proto3" json:"only_holder,omitempty"`
	AuthAction   uint32 `protobuf:"fixed32,10,opt,name=auth_action,json=authAction,proto3" json:"auth_action,omitempty"`
}

func (x *Menu) Reset() {
	*x = Menu{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_iam_iam_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Menu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Menu) ProtoMessage() {}

func (x *Menu) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_iam_iam_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Menu.ProtoReflect.Descriptor instead.
func (*Menu) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_iam_iam_proto_rawDescGZIP(), []int{0}
}

func (x *Menu) GetItemCode() string {
	if x != nil {
		return x.ItemCode
	}
	return ""
}

func (x *Menu) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *Menu) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Menu) GetItemDesc() string {
	if x != nil {
		return x.ItemDesc
	}
	return ""
}

func (x *Menu) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Menu) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Menu) GetCanView() bool {
	if x != nil {
		return x.CanView
	}
	return false
}

func (x *Menu) GetCanAuthorize() bool {
	if x != nil {
		return x.CanAuthorize
	}
	return false
}

func (x *Menu) GetOnlyHolder() bool {
	if x != nil {
		return x.OnlyHolder
	}
	return false
}

func (x *Menu) GetAuthAction() uint32 {
	if x != nil {
		return x.AuthAction
	}
	return 0
}

var File_tanlive_bff_web_iam_iam_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_iam_iam_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x69, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x69, 0x61, 0x6d, 0x22, 0x9d, 0x02, 0x0a, 0x04, 0x4d, 0x65, 0x6e, 0x75, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69,
	0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x07, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x6e, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x61, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x61, 0x6e, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x79, 0x48, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2a, 0x82, 0x01, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17,
	0x0a, 0x13, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x4f, 0x47, 0x49, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x02, 0x12, 0x14, 0x0a,
	0x10, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x54, 0x48, 0x49, 0x52, 0x44, 0x10, 0x04, 0x2a, 0x6e, 0x0a, 0x0f, 0x4f, 0x74, 0x70,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x21, 0x0a, 0x1d,
	0x4f, 0x54, 0x50, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x4b, 0x49, 0x4e,
	0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1b, 0x0a, 0x17, 0x4f, 0x54, 0x50, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x52, 0x5f,
	0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17,
	0x4f, 0x54, 0x50, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x4b, 0x49, 0x4e,
	0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x42, 0x3d, 0x5a, 0x3b, 0x65, 0x2e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e,
	0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66,
	0x2d, 0x77, 0x65, 0x62, 0x2f, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_web_iam_iam_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_iam_iam_proto_rawDescData = file_tanlive_bff_web_iam_iam_proto_rawDesc
)

func file_tanlive_bff_web_iam_iam_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_iam_iam_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_iam_iam_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_iam_iam_proto_rawDescData)
	})
	return file_tanlive_bff_web_iam_iam_proto_rawDescData
}

var file_tanlive_bff_web_iam_iam_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_tanlive_bff_web_iam_iam_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tanlive_bff_web_iam_iam_proto_goTypes = []interface{}{
	(LoginType)(0),       // 0: tanlive.bff_web.iam.LoginType
	(OtpReceiverKind)(0), // 1: tanlive.bff_web.iam.OtpReceiverKind
	(*Menu)(nil),         // 2: tanlive.bff_web.iam.Menu
}
var file_tanlive_bff_web_iam_iam_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_iam_iam_proto_init() }
func file_tanlive_bff_web_iam_iam_proto_init() {
	if File_tanlive_bff_web_iam_iam_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_iam_iam_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Menu); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_iam_iam_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_bff_web_iam_iam_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_iam_iam_proto_depIdxs,
		EnumInfos:         file_tanlive_bff_web_iam_iam_proto_enumTypes,
		MessageInfos:      file_tanlive_bff_web_iam_iam_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_iam_iam_proto = out.File
	file_tanlive_bff_web_iam_iam_proto_rawDesc = nil
	file_tanlive_bff_web_iam_iam_proto_goTypes = nil
	file_tanlive_bff_web_iam_iam_proto_depIdxs = nil
}
