// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package iam

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iam "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"LoginType":          "required",
		"AccountCredentials": "required_if=Type 1",
		"PhoneCredentials":   "required_if=Type 2",
		"EmailCredentials":   "required_if=Type 3",
		"ThirdCredentials":   "required_if=Type 4",
		"TcloudCaptcha":      "required",
	}, &ReqLogin{})
}

func (x *ReqLogin) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Username": "required",
		"Password": "required",
	}, &ReqLogin_AccountCredentials{})
}

func (x *ReqLogin_AccountCredentials) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Phone":    "required",
		"AuthCode": "required",
	}, &ReqLogin_PhoneCredentials{})
}

func (x *ReqLogin_PhoneCredentials) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Email":    "required,email",
		"AuthCode": "required",
	}, &ReqLogin_EmailCredentials{})
}

func (x *ReqLogin_EmailCredentials) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Scene":         "required,oneof=1 2 4 6 7 9",
		"ReceiverKind":  "required",
		"ReceiverPhone": "required_if=ReceiverKind 1,omitempty,phone",
		"ReceiverEmail": "required_if=ReceiverKind 2,omitempty,email",
		"TcloudCaptcha": "required",
	}, &ReqSendAuthOtp{})
}

func (x *ReqSendAuthOtp) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ReceiverKind":  "required",
		"TcloudCaptcha": "required",
	}, &ReqSendTfaOtp{})
}

func (x *ReqSendTfaOtp) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Otp": "required",
	}, &ReqCreatePhoneTfa{})
}

func (x *ReqCreatePhoneTfa) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Password": "required",
	}, &ReqCreatePasswordTfa{})
}

func (x *ReqCreatePasswordTfa) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Otp": "required",
	}, &ReqCreateEmailTfa{})
}

func (x *ReqCreateEmailTfa) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"SceneStr": "required",
	}, &ReqGetCreateWeixinTfaState{})
}

func (x *ReqGetCreateWeixinTfaState) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Code": "required",
	}, &ReqCreateWeixinBrowserTfa{})
}

func (x *ReqCreateWeixinBrowserTfa) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Username": "required",
		"TfaToken": "required",
	}, &ReqModifyMyUsername{})
}

func (x *ReqModifyMyUsername) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Password": "required",
		"TfaToken": "required",
	}, &ReqModifyMyPassword{})
}

func (x *ReqModifyMyPassword) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Phone":    "required,phone",
		"TfaToken": "required",
		"Otp":      "required",
	}, &ReqModifyMyPhone{})
}

func (x *ReqModifyMyPhone) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Email":         "required,email",
		"TfaToken":      "required",
		"TcloudCaptcha": "required",
	}, &ReqModifyMyEmail{})
}

func (x *ReqModifyMyEmail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Value": "required",
	}, &ReqActiveMyEmail{})
}

func (x *ReqActiveMyEmail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TfaToken": "required",
	}, &ReqCreateBindMyWeixinQrcode{})
}

func (x *ReqCreateBindMyWeixinQrcode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"SceneStr": "required",
	}, &ReqGetBindMyWeixinState{})
}

func (x *ReqGetBindMyWeixinState) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Code":     "required",
		"TfaToken": "required",
	}, &ReqBindMyWeixinByOauth2{})
}

func (x *ReqBindMyWeixinByOauth2) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TfaToken": "required",
	}, &ReqUnbindMyWeixin{})
}

func (x *ReqUnbindMyWeixin) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Scene": "required",
		"State": "required",
	}, &ReqGetGoogleAuthUrl{})
}

func (x *ReqGetGoogleAuthUrl) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Code":     "required",
		"TfaToken": "required",
	}, &ReqBindMyGoogle{})
}

func (x *ReqBindMyGoogle) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TfaToken": "required",
	}, &ReqUnbindMyGoogle{})
}

func (x *ReqUnbindMyGoogle) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqLogin) MaskInLog() any {
	if x == nil {
		return (*ReqLogin)(nil)
	}

	y := proto.Clone(x).(*ReqLogin)
	if v, ok := any(y.AccountCredentials).(interface{ MaskInLog() any }); ok {
		y.AccountCredentials = v.MaskInLog().(*ReqLogin_AccountCredentials)
	}
	if v, ok := any(y.PhoneCredentials).(interface{ MaskInLog() any }); ok {
		y.PhoneCredentials = v.MaskInLog().(*ReqLogin_PhoneCredentials)
	}
	if v, ok := any(y.EmailCredentials).(interface{ MaskInLog() any }); ok {
		y.EmailCredentials = v.MaskInLog().(*ReqLogin_EmailCredentials)
	}
	if v, ok := any(y.ThirdCredentials).(interface{ MaskInLog() any }); ok {
		y.ThirdCredentials = v.MaskInLog().(*ReqLogin_ThirdCredentials)
	}
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInLog() any }); ok {
		y.TcloudCaptcha = v.MaskInLog().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqLogin) MaskInRpc() any {
	if x == nil {
		return (*ReqLogin)(nil)
	}

	y := x
	if v, ok := any(y.AccountCredentials).(interface{ MaskInRpc() any }); ok {
		y.AccountCredentials = v.MaskInRpc().(*ReqLogin_AccountCredentials)
	}
	if v, ok := any(y.PhoneCredentials).(interface{ MaskInRpc() any }); ok {
		y.PhoneCredentials = v.MaskInRpc().(*ReqLogin_PhoneCredentials)
	}
	if v, ok := any(y.EmailCredentials).(interface{ MaskInRpc() any }); ok {
		y.EmailCredentials = v.MaskInRpc().(*ReqLogin_EmailCredentials)
	}
	if v, ok := any(y.ThirdCredentials).(interface{ MaskInRpc() any }); ok {
		y.ThirdCredentials = v.MaskInRpc().(*ReqLogin_ThirdCredentials)
	}
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInRpc() any }); ok {
		y.TcloudCaptcha = v.MaskInRpc().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqLogin) MaskInBff() any {
	if x == nil {
		return (*ReqLogin)(nil)
	}

	y := x
	if v, ok := any(y.AccountCredentials).(interface{ MaskInBff() any }); ok {
		y.AccountCredentials = v.MaskInBff().(*ReqLogin_AccountCredentials)
	}
	if v, ok := any(y.PhoneCredentials).(interface{ MaskInBff() any }); ok {
		y.PhoneCredentials = v.MaskInBff().(*ReqLogin_PhoneCredentials)
	}
	if v, ok := any(y.EmailCredentials).(interface{ MaskInBff() any }); ok {
		y.EmailCredentials = v.MaskInBff().(*ReqLogin_EmailCredentials)
	}
	if v, ok := any(y.ThirdCredentials).(interface{ MaskInBff() any }); ok {
		y.ThirdCredentials = v.MaskInBff().(*ReqLogin_ThirdCredentials)
	}
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInBff() any }); ok {
		y.TcloudCaptcha = v.MaskInBff().(*base.TcloudCaptcha)
	}

	return y
}

func (x *RspLogin) MaskInLog() any {
	if x == nil {
		return (*RspLogin)(nil)
	}

	y := proto.Clone(x).(*RspLogin)
	if v, ok := any(y.UserInfo).(interface{ MaskInLog() any }); ok {
		y.UserInfo = v.MaskInLog().(*RspLogin_UserInfo)
	}
	for k, v := range y.Menus {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Menus[k] = vv.MaskInLog().(*Menu)
		}
	}

	return y
}

func (x *RspLogin) MaskInRpc() any {
	if x == nil {
		return (*RspLogin)(nil)
	}

	y := x
	if v, ok := any(y.UserInfo).(interface{ MaskInRpc() any }); ok {
		y.UserInfo = v.MaskInRpc().(*RspLogin_UserInfo)
	}
	for k, v := range y.Menus {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Menus[k] = vv.MaskInRpc().(*Menu)
		}
	}

	return y
}

func (x *RspLogin) MaskInBff() any {
	if x == nil {
		return (*RspLogin)(nil)
	}

	y := x
	if v, ok := any(y.UserInfo).(interface{ MaskInBff() any }); ok {
		y.UserInfo = v.MaskInBff().(*RspLogin_UserInfo)
	}
	for k, v := range y.Menus {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Menus[k] = vv.MaskInBff().(*Menu)
		}
	}

	return y
}

func (x *ReqSendAuthOtp) MaskInLog() any {
	if x == nil {
		return (*ReqSendAuthOtp)(nil)
	}

	y := proto.Clone(x).(*ReqSendAuthOtp)
	y.ReceiverPhone = mask.Mask(y.ReceiverPhone, "phone")
	y.ReceiverEmail = mask.Mask(y.ReceiverEmail, "email")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInLog() any }); ok {
		y.TcloudCaptcha = v.MaskInLog().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqSendAuthOtp) MaskInRpc() any {
	if x == nil {
		return (*ReqSendAuthOtp)(nil)
	}

	y := x
	y.ReceiverPhone = mask.Mask(y.ReceiverPhone, "phone")
	y.ReceiverEmail = mask.Mask(y.ReceiverEmail, "email")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInRpc() any }); ok {
		y.TcloudCaptcha = v.MaskInRpc().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqSendAuthOtp) MaskInBff() any {
	if x == nil {
		return (*ReqSendAuthOtp)(nil)
	}

	y := x
	y.ReceiverPhone = mask.Mask(y.ReceiverPhone, "phone")
	y.ReceiverEmail = mask.Mask(y.ReceiverEmail, "email")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInBff() any }); ok {
		y.TcloudCaptcha = v.MaskInBff().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqSendTfaOtp) MaskInLog() any {
	if x == nil {
		return (*ReqSendTfaOtp)(nil)
	}

	y := proto.Clone(x).(*ReqSendTfaOtp)
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInLog() any }); ok {
		y.TcloudCaptcha = v.MaskInLog().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqSendTfaOtp) MaskInRpc() any {
	if x == nil {
		return (*ReqSendTfaOtp)(nil)
	}

	y := x
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInRpc() any }); ok {
		y.TcloudCaptcha = v.MaskInRpc().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqSendTfaOtp) MaskInBff() any {
	if x == nil {
		return (*ReqSendTfaOtp)(nil)
	}

	y := x
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInBff() any }); ok {
		y.TcloudCaptcha = v.MaskInBff().(*base.TcloudCaptcha)
	}

	return y
}

func (x *RspGetMyTfa) MaskInLog() any {
	if x == nil {
		return (*RspGetMyTfa)(nil)
	}

	y := proto.Clone(x).(*RspGetMyTfa)
	if v, ok := any(y.TfaToken).(interface{ MaskInLog() any }); ok {
		y.TfaToken = v.MaskInLog().(*iam.TfaToken)
	}

	return y
}

func (x *RspGetMyTfa) MaskInRpc() any {
	if x == nil {
		return (*RspGetMyTfa)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInRpc() any }); ok {
		y.TfaToken = v.MaskInRpc().(*iam.TfaToken)
	}

	return y
}

func (x *RspGetMyTfa) MaskInBff() any {
	if x == nil {
		return (*RspGetMyTfa)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInBff() any }); ok {
		y.TfaToken = v.MaskInBff().(*iam.TfaToken)
	}

	return y
}

func (x *RspCreateTfa) MaskInLog() any {
	if x == nil {
		return (*RspCreateTfa)(nil)
	}

	y := proto.Clone(x).(*RspCreateTfa)
	if v, ok := any(y.TfaToken).(interface{ MaskInLog() any }); ok {
		y.TfaToken = v.MaskInLog().(*iam.TfaToken)
	}

	return y
}

func (x *RspCreateTfa) MaskInRpc() any {
	if x == nil {
		return (*RspCreateTfa)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInRpc() any }); ok {
		y.TfaToken = v.MaskInRpc().(*iam.TfaToken)
	}

	return y
}

func (x *RspCreateTfa) MaskInBff() any {
	if x == nil {
		return (*RspCreateTfa)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInBff() any }); ok {
		y.TfaToken = v.MaskInBff().(*iam.TfaToken)
	}

	return y
}

func (x *ReqCreatePasswordTfa) MaskInLog() any {
	if x == nil {
		return (*ReqCreatePasswordTfa)(nil)
	}

	y := proto.Clone(x).(*ReqCreatePasswordTfa)
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqCreatePasswordTfa) MaskInRpc() any {
	if x == nil {
		return (*ReqCreatePasswordTfa)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqCreatePasswordTfa) MaskInBff() any {
	if x == nil {
		return (*ReqCreatePasswordTfa)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *RspCreateWeixinTfaQrcode) MaskInLog() any {
	if x == nil {
		return (*RspCreateWeixinTfaQrcode)(nil)
	}

	y := proto.Clone(x).(*RspCreateWeixinTfaQrcode)
	if v, ok := any(y.Qrcode).(interface{ MaskInLog() any }); ok {
		y.Qrcode = v.MaskInLog().(*iam.WeixinQrcode)
	}

	return y
}

func (x *RspCreateWeixinTfaQrcode) MaskInRpc() any {
	if x == nil {
		return (*RspCreateWeixinTfaQrcode)(nil)
	}

	y := x
	if v, ok := any(y.Qrcode).(interface{ MaskInRpc() any }); ok {
		y.Qrcode = v.MaskInRpc().(*iam.WeixinQrcode)
	}

	return y
}

func (x *RspCreateWeixinTfaQrcode) MaskInBff() any {
	if x == nil {
		return (*RspCreateWeixinTfaQrcode)(nil)
	}

	y := x
	if v, ok := any(y.Qrcode).(interface{ MaskInBff() any }); ok {
		y.Qrcode = v.MaskInBff().(*iam.WeixinQrcode)
	}

	return y
}

func (x *RspGetCreateWeixinTfaState) MaskInLog() any {
	if x == nil {
		return (*RspGetCreateWeixinTfaState)(nil)
	}

	y := proto.Clone(x).(*RspGetCreateWeixinTfaState)
	if v, ok := any(y.TfaToken).(interface{ MaskInLog() any }); ok {
		y.TfaToken = v.MaskInLog().(*iam.TfaToken)
	}

	return y
}

func (x *RspGetCreateWeixinTfaState) MaskInRpc() any {
	if x == nil {
		return (*RspGetCreateWeixinTfaState)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInRpc() any }); ok {
		y.TfaToken = v.MaskInRpc().(*iam.TfaToken)
	}

	return y
}

func (x *RspGetCreateWeixinTfaState) MaskInBff() any {
	if x == nil {
		return (*RspGetCreateWeixinTfaState)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInBff() any }); ok {
		y.TfaToken = v.MaskInBff().(*iam.TfaToken)
	}

	return y
}

func (x *ReqModifyMyUsername) MaskInLog() any {
	if x == nil {
		return (*ReqModifyMyUsername)(nil)
	}

	y := proto.Clone(x).(*ReqModifyMyUsername)
	y.Username = mask.Mask(y.Username, "username")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqModifyMyUsername) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyMyUsername)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqModifyMyUsername) MaskInBff() any {
	if x == nil {
		return (*ReqModifyMyUsername)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqModifyMyPassword) MaskInLog() any {
	if x == nil {
		return (*ReqModifyMyPassword)(nil)
	}

	y := proto.Clone(x).(*ReqModifyMyPassword)
	y.Password = mask.Mask(y.Password, "secret")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqModifyMyPassword) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyMyPassword)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqModifyMyPassword) MaskInBff() any {
	if x == nil {
		return (*ReqModifyMyPassword)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqModifyMyPhone) MaskInLog() any {
	if x == nil {
		return (*ReqModifyMyPhone)(nil)
	}

	y := proto.Clone(x).(*ReqModifyMyPhone)
	y.Phone = mask.Mask(y.Phone, "phone")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqModifyMyPhone) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyMyPhone)(nil)
	}

	y := x
	y.Phone = mask.Mask(y.Phone, "phone")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqModifyMyPhone) MaskInBff() any {
	if x == nil {
		return (*ReqModifyMyPhone)(nil)
	}

	y := x
	y.Phone = mask.Mask(y.Phone, "phone")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqModifyMyEmail) MaskInLog() any {
	if x == nil {
		return (*ReqModifyMyEmail)(nil)
	}

	y := proto.Clone(x).(*ReqModifyMyEmail)
	y.Email = mask.Mask(y.Email, "email")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInLog() any }); ok {
		y.TcloudCaptcha = v.MaskInLog().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqModifyMyEmail) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyMyEmail)(nil)
	}

	y := x
	y.Email = mask.Mask(y.Email, "email")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInRpc() any }); ok {
		y.TcloudCaptcha = v.MaskInRpc().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqModifyMyEmail) MaskInBff() any {
	if x == nil {
		return (*ReqModifyMyEmail)(nil)
	}

	y := x
	y.Email = mask.Mask(y.Email, "email")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")
	if v, ok := any(y.TcloudCaptcha).(interface{ MaskInBff() any }); ok {
		y.TcloudCaptcha = v.MaskInBff().(*base.TcloudCaptcha)
	}

	return y
}

func (x *ReqCreateBindMyWeixinQrcode) MaskInLog() any {
	if x == nil {
		return (*ReqCreateBindMyWeixinQrcode)(nil)
	}

	y := proto.Clone(x).(*ReqCreateBindMyWeixinQrcode)
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqCreateBindMyWeixinQrcode) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateBindMyWeixinQrcode)(nil)
	}

	y := x
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqCreateBindMyWeixinQrcode) MaskInBff() any {
	if x == nil {
		return (*ReqCreateBindMyWeixinQrcode)(nil)
	}

	y := x
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *RspCreateBindMyWeixinQrcode) MaskInLog() any {
	if x == nil {
		return (*RspCreateBindMyWeixinQrcode)(nil)
	}

	y := proto.Clone(x).(*RspCreateBindMyWeixinQrcode)
	if v, ok := any(y.Qrcode).(interface{ MaskInLog() any }); ok {
		y.Qrcode = v.MaskInLog().(*iam.WeixinQrcode)
	}

	return y
}

func (x *RspCreateBindMyWeixinQrcode) MaskInRpc() any {
	if x == nil {
		return (*RspCreateBindMyWeixinQrcode)(nil)
	}

	y := x
	if v, ok := any(y.Qrcode).(interface{ MaskInRpc() any }); ok {
		y.Qrcode = v.MaskInRpc().(*iam.WeixinQrcode)
	}

	return y
}

func (x *RspCreateBindMyWeixinQrcode) MaskInBff() any {
	if x == nil {
		return (*RspCreateBindMyWeixinQrcode)(nil)
	}

	y := x
	if v, ok := any(y.Qrcode).(interface{ MaskInBff() any }); ok {
		y.Qrcode = v.MaskInBff().(*iam.WeixinQrcode)
	}

	return y
}

func (x *ReqBindMyWeixinByOauth2) MaskInLog() any {
	if x == nil {
		return (*ReqBindMyWeixinByOauth2)(nil)
	}

	y := proto.Clone(x).(*ReqBindMyWeixinByOauth2)
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqBindMyWeixinByOauth2) MaskInRpc() any {
	if x == nil {
		return (*ReqBindMyWeixinByOauth2)(nil)
	}

	y := x
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqBindMyWeixinByOauth2) MaskInBff() any {
	if x == nil {
		return (*ReqBindMyWeixinByOauth2)(nil)
	}

	y := x
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqUnbindMyWeixin) MaskInLog() any {
	if x == nil {
		return (*ReqUnbindMyWeixin)(nil)
	}

	y := proto.Clone(x).(*ReqUnbindMyWeixin)
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqUnbindMyWeixin) MaskInRpc() any {
	if x == nil {
		return (*ReqUnbindMyWeixin)(nil)
	}

	y := x
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqUnbindMyWeixin) MaskInBff() any {
	if x == nil {
		return (*ReqUnbindMyWeixin)(nil)
	}

	y := x
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqBindMyGoogle) MaskInLog() any {
	if x == nil {
		return (*ReqBindMyGoogle)(nil)
	}

	y := proto.Clone(x).(*ReqBindMyGoogle)
	y.Code = mask.Mask(y.Code, "secret")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqBindMyGoogle) MaskInRpc() any {
	if x == nil {
		return (*ReqBindMyGoogle)(nil)
	}

	y := x
	y.Code = mask.Mask(y.Code, "secret")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqBindMyGoogle) MaskInBff() any {
	if x == nil {
		return (*ReqBindMyGoogle)(nil)
	}

	y := x
	y.Code = mask.Mask(y.Code, "secret")
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqUnbindMyGoogle) MaskInLog() any {
	if x == nil {
		return (*ReqUnbindMyGoogle)(nil)
	}

	y := proto.Clone(x).(*ReqUnbindMyGoogle)
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqUnbindMyGoogle) MaskInRpc() any {
	if x == nil {
		return (*ReqUnbindMyGoogle)(nil)
	}

	y := x
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqUnbindMyGoogle) MaskInBff() any {
	if x == nil {
		return (*ReqUnbindMyGoogle)(nil)
	}

	y := x
	y.TfaToken = mask.Mask(y.TfaToken, "secret")

	return y
}

func (x *ReqLogin) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AccountCredentials).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.PhoneCredentials).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EmailCredentials).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.ThirdCredentials).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.TcloudCaptcha).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspLogin) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.UserInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Menus {
		if sanitizer, ok := any(x.Menus[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqSendAuthOtp) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TcloudCaptcha).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqSendTfaOtp) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TcloudCaptcha).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetMyTfa) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TfaToken).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateTfa) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TfaToken).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateWeixinTfaQrcode) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Qrcode).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetCreateWeixinTfaState) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TfaToken).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqModifyMyEmail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TcloudCaptcha).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateBindMyWeixinQrcode) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Qrcode).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

type IamBffHandler interface {
	// 登出
	Logout(context.Context, *emptypb.Empty, *emptypb.Empty) error
	// 发送二次验证的验证码
	SendTfaOtp(context.Context, *ReqSendTfaOtp, *RspSendTfaOtp) error
	// 查询我的的二次验证
	GetMyTfa(context.Context, *emptypb.Empty, *RspGetMyTfa) error
	// 创建手机二次验证
	CreatePhoneTfa(context.Context, *ReqCreatePhoneTfa, *RspCreateTfa) error
	// 创建密码二次验证
	CreatePasswordTfa(context.Context, *ReqCreatePasswordTfa, *RspCreateTfa) error
	// 创建邮箱二次验证
	CreateEmailTfa(context.Context, *ReqCreateEmailTfa, *RspCreateTfa) error
	// 创建微信二次验证二维码
	CreateWeixinTfaQrcode(context.Context, *emptypb.Empty, *RspCreateWeixinTfaQrcode) error
	// 查询创建微信二次验证状态
	GetCreateWeixinTfaState(context.Context, *ReqGetCreateWeixinTfaState, *RspGetCreateWeixinTfaState) error
	// 创建微信浏览器二次验证
	CreateWeixinBrowserTfa(context.Context, *ReqCreateWeixinBrowserTfa, *RspCreateTfa) error
	// 修改我的用户名
	ModifyMyUsername(context.Context, *ReqModifyMyUsername, *emptypb.Empty) error
	// 修改我的密码
	ModifyMyPassword(context.Context, *ReqModifyMyPassword, *emptypb.Empty) error
	// 修改我的手机号
	ModifyMyPhone(context.Context, *ReqModifyMyPhone, *emptypb.Empty) error
	// 修改我的邮箱
	ModifyMyEmail(context.Context, *ReqModifyMyEmail, *RspModifyMyEmail) error
	// 激活我的邮箱
	ActiveMyEmail(context.Context, *ReqActiveMyEmail, *emptypb.Empty) error
	// 创建绑定我的微信二维码
	CreateBindMyWeixinQrcode(context.Context, *ReqCreateBindMyWeixinQrcode, *RspCreateBindMyWeixinQrcode) error
	// 查询绑定我的微信状态
	GetBindMyWeixinState(context.Context, *ReqGetBindMyWeixinState, *RspGetBindMyWeixinState) error
	// 绑定我的微信（在微信浏览器中）
	BindMyWeixinByOauth2(context.Context, *ReqBindMyWeixinByOauth2, *emptypb.Empty) error
	// 解绑我的微信
	UnbindMyWeixin(context.Context, *ReqUnbindMyWeixin, *emptypb.Empty) error
	// 创建谷歌二次认证
	CreateGoogleTfa(context.Context, *ReqCreateGoogleTfa, *RspCreateTfa) error
	// 绑定我的谷歌账号
	BindMyGoogle(context.Context, *ReqBindMyGoogle, *emptypb.Empty) error
	// 解绑我的谷歌账号
	UnbindMyGoogle(context.Context, *ReqUnbindMyGoogle, *emptypb.Empty) error
}

func RegisterIamBff(s bff.Server, h IamBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Iam"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/iam/logout", h.Logout).Name("Logout")
	bff.AddRoute(group, http.MethodPost, "/iam/send_2fa_otp", h.SendTfaOtp).Name("SendTfaOtp")
	bff.AddRoute(group, http.MethodPost, "/iam/get_my_2fa", h.GetMyTfa).Name("GetMyTfa")
	bff.AddRoute(group, http.MethodPost, "/iam/create_phone_2fa", h.CreatePhoneTfa).Name("CreatePhoneTfa")
	bff.AddRoute(group, http.MethodPost, "/iam/create_password_2fa", h.CreatePasswordTfa).Name("CreatePasswordTfa")
	bff.AddRoute(group, http.MethodPost, "/iam/create_email_2fa", h.CreateEmailTfa).Name("CreateEmailTfa")
	bff.AddRoute(group, http.MethodPost, "/iam/create_weixin_2fa_qrcode", h.CreateWeixinTfaQrcode).Name("CreateWeixinTfaQrcode")
	bff.AddRoute(group, http.MethodPost, "/iam/get_create_weixin_2fa_state", h.GetCreateWeixinTfaState).Name("GetCreateWeixinTfaState")
	bff.AddRoute(group, http.MethodPost, "/iam/create_weixin_browser_2fa", h.CreateWeixinBrowserTfa).Name("CreateWeixinBrowserTfa")
	bff.AddRoute(group, http.MethodPost, "/iam/modify_my_username", h.ModifyMyUsername).Name("ModifyMyUsername")
	bff.AddRoute(group, http.MethodPost, "/iam/modify_my_password", h.ModifyMyPassword).Name("ModifyMyPassword")
	bff.AddRoute(group, http.MethodPost, "/iam/modify_my_phone", h.ModifyMyPhone).Name("ModifyMyPhone")
	bff.AddRoute(group, http.MethodPost, "/iam/modify_my_email", h.ModifyMyEmail).Name("ModifyMyEmail")
	bff.AddRoute(group, http.MethodPost, "/iam/active_my_email", h.ActiveMyEmail).Name("ActiveMyEmail")
	bff.AddRoute(group, http.MethodPost, "/iam/create_bind_my_weixin_qrcode", h.CreateBindMyWeixinQrcode).Name("CreateBindMyWeixinQrcode")
	bff.AddRoute(group, http.MethodPost, "/iam/get_bind_my_weixin_state", h.GetBindMyWeixinState).Name("GetBindMyWeixinState")
	bff.AddRoute(group, http.MethodPost, "/iam/bind_my_weixin_by_oauth2", h.BindMyWeixinByOauth2).Name("BindMyWeixinByOauth2")
	bff.AddRoute(group, http.MethodPost, "/iam/unbind_my_weixin", h.UnbindMyWeixin).Name("UnbindMyWeixin")
	bff.AddRoute(group, http.MethodPost, "/iam/create_google_2fa", h.CreateGoogleTfa).Name("CreateGoogleTfa")
	bff.AddRoute(group, http.MethodPost, "/iam/bind_my_google", h.BindMyGoogle).Name("BindMyGoogle")
	bff.AddRoute(group, http.MethodPost, "/iam/unbind_my_google", h.UnbindMyGoogle).Name("UnbindMyGoogle")
	return group
}

type IamGuestBffHandler interface {
	// 登入
	Login(context.Context, *ReqLogin, *RspLogin) error
	// 发送验证码（未登录）
	SendAuthOtp(context.Context, *ReqSendAuthOtp, *RspSendAuthOtp) error
	// 获取谷歌认证URL
	GetGoogleAuthUrl(context.Context, *ReqGetGoogleAuthUrl, *RspGetGoogleAuthUrl) error
}

func RegisterIamGuestBff(s bff.Server, h IamGuestBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("IamGuest"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/iam/login", h.Login).Name("Login")
	bff.AddRoute(group, http.MethodPost, "/notify/send_one_time_password", h.SendAuthOtp).Name("SendAuthOtp")
	bff.AddRoute(group, http.MethodPost, "/iam/get_google_auth_url", h.GetGoogleAuthUrl).Name("GetGoogleAuthUrl")
	return group
}
