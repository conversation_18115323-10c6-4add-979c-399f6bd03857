syntax = "proto3";

package tanlive.bff_web.iam;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/iam";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "tanlive/base/base.proto";
import "tanlive/base/tcloud.proto";
import "tanlive/bff-web/iam/iam.proto";
import "tanlive/iam/iam.proto";
import "tanlive/options.proto";

// 登录
message ReqLogin {
  message AccountCredentials {
    // 用户名
    string username = 1 [(tanlive.validator) = "required"];
    // 密码
    string password = 2 [(tanlive.validator) = "required"];
  }
  message PhoneCredentials {
    // 手机号
    string phone = 1 [(tanlive.validator) = "required"];
    // 验证码
    string auth_code = 2 [(tanlive.validator) = "required"];
  }
  message EmailCredentials {
    // 邮箱
    string email = 1 [(tanlive.validator) = "required,email"];
    // 验证码
    string auth_code = 2 [(tanlive.validator) = "required"];
  }
  message ThirdCredentials {
    // 第三方凭证
    string third_ticket = 1;
  }

  // 登录类型
  LoginType login_type = 1 [(tanlive.validator) = "required"];
  // 密码登录凭证
  AccountCredentials account_credentials = 2 [(tanlive.validator) = "required_if=Type 1"];
  // 手机号登录凭证
  PhoneCredentials phone_credentials = 3 [(tanlive.validator) = "required_if=Type 2"];
  // 邮箱登录凭证
  EmailCredentials email_credentials = 4 [(tanlive.validator) = "required_if=Type 3"];
  // 第三方登录凭证
  ThirdCredentials third_credentials = 5 [(tanlive.validator) = "required_if=Type 4"];
  // 第三方凭证(用于绑定)
  string bind_third_ticket = 6;
  // 腾讯云验证码参数
  tanlive.base.TcloudCaptcha tcloud_captcha = 7 [(tanlive.validator) = "required"];
}

message RspLogin {
  message UserInfo {
    string image = 1;
    tanlive.base.IdentityType identity_set = 2;
    string level = 3;
    string user_name = 4;
    string firm_name = 5;
  }
  // 用户信息结构体
  UserInfo user_info = 1;
  // 该用户可显示的菜单列表
  repeated Menu menus = 2;
  //该账号是否为未登录过状态
  bool new_user = 3;
}

message ReqSendAuthOtp {
  // 场景（仅1、2、4、6、7、9有效）
  tanlive.iam.OtpScene scene = 1 [(validator) = "required,oneof=1 2 4 6 7 9"];
  // 接收人类型
  OtpReceiverKind receiver_kind = 2 [(validator) = "required"];
  // 接收人手机号
  string receiver_phone = 3 [(validator) = "required_if=ReceiverKind 1,omitempty,phone", (mask).rule = "phone"];
  // 接收人邮箱
  string receiver_email = 4 [(validator) = "required_if=ReceiverKind 2,omitempty,email", (mask).rule = "email"];
  // 腾讯云验证码参数
  tanlive.base.TcloudCaptcha tcloud_captcha = 5 [(validator) = "required"];
}

message RspSendAuthOtp {
  // 任务ID
  string task_no = 1;
  // 腾讯云SDK错误码
  string sdk_error = 2;
}

message ReqSendTfaOtp {
  // 接收者类型
  OtpReceiverKind receiver_kind = 1 [(validator) = "required"];
  // 腾讯云验证码参数
  base.TcloudCaptcha tcloud_captcha = 5 [(validator) = "required"];
}

message RspSendTfaOtp {
  // 任务ID
  string task_no = 1;
  // 腾讯云SDK错误码
  string sdk_error = 2;
}

message RspGetMyTfa {
  // 2FA令牌（未创建或已过期返回null）
  tanlive.iam.TfaToken tfa_token = 1;
}

message ReqCreatePhoneTfa {
  // 一次性密码
  string otp = 1 [(validator) = "required"];
}

message RspCreateTfa {
  // 2FA令牌
  tanlive.iam.TfaToken tfa_token = 1;
}

message ReqCreatePasswordTfa {
  // 密码（需要RSA加密）
  string password = 1 [(validator) = "required", (mask).rule = "secret"];
}

message ReqCreateEmailTfa {
  // 一次性密码
  string otp = 1 [(validator) = "required"];
}

message RspCreateWeixinTfaQrcode {
  // 二维码信息
  tanlive.iam.WeixinQrcode qrcode = 1;
}

message ReqGetCreateWeixinTfaState {
  // 场景值
  string scene_str = 1 [(validator) = "required"];
}

message RspGetCreateWeixinTfaState {
  // 状态
  tanlive.iam.CreateWeixinTfaState state = 1;
  // 2FA令牌
  tanlive.iam.TfaToken tfa_token = 2;
}

message ReqCreateWeixinBrowserTfa {
  // 微信静默授权返回的code
  string code = 1 [(validator) = "required"];
}

message ReqModifyMyUsername {
  // 用户名（需要RSA加密）
  string username = 1 [(validator) = "required", (mask).rule = "username"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required", (mask).rule = "secret"];
}

message ReqModifyMyPassword {
  // 密码（需要RSA加密）
  string password = 1 [(validator) = "required", (mask).rule = "secret"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required", (mask).rule = "secret"];
}

message ReqModifyMyPhone {
  // 手机号
  string phone = 1 [(validator) = "required,phone", (mask).rule = "phone"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required", (mask).rule = "secret"];
  // 验证码
  string otp = 3 [(validator) = "required", (mask).rule = "secret"];
}

message ReqModifyMyEmail {
  // 邮箱
  string email = 1 [(validator) = "required,email", (mask).rule = "email"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required", (mask).rule = "secret"];
  // 腾讯云验证码参数
  base.TcloudCaptcha tcloud_captcha = 5 [(validator) = "required"];
}

message RspModifyMyEmail {
  // 任务ID
  string task_no = 1;
  // 腾讯云SDK错误码
  string sdk_error = 2;
}

message ReqActiveMyEmail {
  // 激活邮件参数值
  string value = 1 [(validator) = "required"];
}

message ReqCreateBindMyWeixinQrcode {
  // 2FA令牌
  string tfa_token = 1 [(validator) = "required", (mask).rule = "secret"];
}

message RspCreateBindMyWeixinQrcode {
  // 二维码信息
  tanlive.iam.WeixinQrcode qrcode = 1;
}

message ReqGetBindMyWeixinState {
  // 场景值
  string scene_str = 1 [(validator) = "required"];
}

message RspGetBindMyWeixinState {
  // 状态
  tanlive.iam.BindWeixinState state = 1;
}

message ReqBindMyWeixinByOauth2 {
  // 微信静默授权返回的code
  string code = 1 [(validator) = "required"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required", (mask).rule = "secret"];
}

message ReqUnbindMyWeixin {
  // 2FA令牌
  string tfa_token = 1 [(validator) = "required", (mask).rule = "secret"];
}

message ReqGetGoogleAuthUrl {
  // 认证场景
  tanlive.iam.GoogleAuthScene scene = 1 [(validator) = "required"];
  // state参数
  string state = 2 [(validator) = "required"];
}

message RspGetGoogleAuthUrl {
  // 认证URL
  string url = 1;
}

message ReqCreateGoogleTfa {
  // 谷歌静默授权返回的code
  string code = 1;
}

message ReqBindMyGoogle {
  // 谷歌回调code
  string code = 1 [(validator) = "required", (mask).rule = "secret"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required", (mask).rule = "secret"];
}

message ReqUnbindMyGoogle {
  // 2FA令牌
  string tfa_token = 1 [(validator) = "required", (mask).rule = "secret"];
}

// iam模块接口
service IamBff {
  option (tanlive.bff) = true;

  // 登出
  rpc Logout(google.protobuf.Empty) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/logout"
      body: "*"
    };
  };

  // 发送二次验证的验证码
  rpc SendTfaOtp(ReqSendTfaOtp) returns (RspSendTfaOtp) {
    option (google.api.http) = {
      post: "/iam/send_2fa_otp",
      body: "*",
    };
  };

  // 查询我的的二次验证
  rpc GetMyTfa(google.protobuf.Empty) returns (RspGetMyTfa) {
    option (google.api.http) = {
      post: "/iam/get_my_2fa",
      body: "*",
    };
  };

  // 创建手机二次验证
  rpc CreatePhoneTfa(ReqCreatePhoneTfa) returns (RspCreateTfa) {
    option (google.api.http) = {
      post: "/iam/create_phone_2fa",
      body: "*",
    };
  };

  // 创建密码二次验证
  rpc CreatePasswordTfa(ReqCreatePasswordTfa) returns (RspCreateTfa) {
    option (google.api.http) = {
      post: "/iam/create_password_2fa",
      body: "*",
    };
  };

  // 创建邮箱二次验证
  rpc CreateEmailTfa(ReqCreateEmailTfa) returns (RspCreateTfa) {
    option (google.api.http) = {
      post: "/iam/create_email_2fa",
      body: "*",
    };
  };

  // 创建微信二次验证二维码
  rpc CreateWeixinTfaQrcode(google.protobuf.Empty) returns (RspCreateWeixinTfaQrcode) {
    option (google.api.http) = {
      post: "/iam/create_weixin_2fa_qrcode",
      body: "*",
    };
  };

  // 查询创建微信二次验证状态
  rpc GetCreateWeixinTfaState(ReqGetCreateWeixinTfaState) returns (RspGetCreateWeixinTfaState) {
    option (google.api.http) = {
      post: "/iam/get_create_weixin_2fa_state",
      body: "*",
    };
  };

  // 创建微信浏览器二次验证
  rpc CreateWeixinBrowserTfa(ReqCreateWeixinBrowserTfa) returns (RspCreateTfa) {
    option (google.api.http) = {
      post: "/iam/create_weixin_browser_2fa",
      body: "*",
    };
  };

  // 修改我的用户名
  rpc ModifyMyUsername(ReqModifyMyUsername) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/modify_my_username",
      body: "*",
    };
  };

  // 修改我的密码
  rpc ModifyMyPassword(ReqModifyMyPassword) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/modify_my_password",
      body: "*",
    };
  };

  // 修改我的手机号
  rpc ModifyMyPhone(ReqModifyMyPhone) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/modify_my_phone",
      body: "*",
    };
  };

  // 修改我的邮箱
  rpc ModifyMyEmail(ReqModifyMyEmail) returns (RspModifyMyEmail) {
    option (google.api.http) = {
      post: "/iam/modify_my_email",
      body: "*",
    };
  };

  // 激活我的邮箱
  rpc ActiveMyEmail(ReqActiveMyEmail) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/active_my_email",
      body: "*",
    };
  };

  // 创建绑定我的微信二维码
  rpc CreateBindMyWeixinQrcode(ReqCreateBindMyWeixinQrcode) returns (RspCreateBindMyWeixinQrcode) {
    option (google.api.http) = {
      post: "/iam/create_bind_my_weixin_qrcode",
      body: "*",
    };
  };

  // 查询绑定我的微信状态
  rpc GetBindMyWeixinState(ReqGetBindMyWeixinState) returns (RspGetBindMyWeixinState) {
    option (google.api.http) = {
      post: "/iam/get_bind_my_weixin_state",
      body: "*",
    };
  };

  // 绑定我的微信（在微信浏览器中）
  rpc BindMyWeixinByOauth2(ReqBindMyWeixinByOauth2) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/bind_my_weixin_by_oauth2",
      body: "*",
    };
  };

  // 解绑我的微信
  rpc UnbindMyWeixin(ReqUnbindMyWeixin) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/unbind_my_weixin",
      body: "*",
    };
  };

  // 创建谷歌二次认证
  rpc CreateGoogleTfa(ReqCreateGoogleTfa) returns (RspCreateTfa) {
    option (google.api.http) = {
      post: "/iam/create_google_2fa",
      body: "*",
    };
  };

  // 绑定我的谷歌账号
  rpc BindMyGoogle(ReqBindMyGoogle) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/bind_my_google",
      body: "*",
    };
  };

  // 解绑我的谷歌账号
  rpc UnbindMyGoogle(ReqUnbindMyGoogle) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/iam/unbind_my_google",
      body: "*",
    };
  };
}

// iam模块游客接口
service IamGuestBff {
  option (tanlive.bff) = true;

  // 登入
  rpc Login(ReqLogin) returns (RspLogin) {
    option (google.api.http) = {
      post: "/iam/login"
      body: "*"
    };
  };

  // 发送验证码（未登录）
  rpc SendAuthOtp(ReqSendAuthOtp) returns (RspSendAuthOtp) {
    option (google.api.http) = {
      /* 为兼容原接口，URL保持原样 */
      post: "/notify/send_one_time_password",
      body: "*",
    };
  };

  // 获取谷歌认证URL
  rpc GetGoogleAuthUrl(ReqGetGoogleAuthUrl) returns (RspGetGoogleAuthUrl) {
    option (google.api.http) = {
      post: "/iam/get_google_auth_url",
      body: "*",
    };
  };
}
