syntax = "proto3";

package tanlive.bff_web.notify;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/notify";

import "google/api/annotations.proto";
import "tanlive/options.proto";

message ReqGetSmsSendStatus {
  // 任务ID
  string task_no = 1 [(tanlive.validator) = "required"];
}

message RspGetSmsSendStatus {
  // 状态：SENDING（发送中）、SUCCESS（成功）、FAIL（失败）
  string report_status = 1;
  // 用户接收短信状态码错误信息，参考：https://cloud.tencent.com/document/api/382/59177#.E5.9B.9E.E6.89.A7.E7.8A.B6.E6.80.81.E9.94.99.E8.AF.AF.E7.A0.81
  string errmsg = 2;
}

message ReqGetEmailSendStatus {
  // 任务ID
  string task_no = 1 [(tanlive.validator) = "required"];
}

message RspGetEmailSendStatus {
  // 事件类型：
  // delivering 投递中
  // deferred 邮件被收件人邮件服务商延迟传递，正在重试中
  // delivered 递送成功，如您未收到请检查垃圾箱
  // dropped 邮件无法送达，请尝试其他邮箱地址
  // bounce 收件人邮件服务商拒收此邮件，请检查邮箱地址或尝试其他邮箱地址
  string event = 1;
  // 事件产生的时间戳
  int32 timestamp = 2;
}

// notify模块接口
service NotifyBff {
  option (tanlive.bff) = true;
}

// notify模块游客接口
service NotifyGuestBff {
  option (tanlive.bff) = true;

  // 查询短信发送状态
  rpc GetSmsSendStatus(ReqGetSmsSendStatus) returns (RspGetSmsSendStatus) {
    option (google.api.http) = {
      post: "/notify/get_sms_send_status",
      body: "*",
    };
  };

  // 查询邮件发送状态
  rpc GetEmailSendStatus(ReqGetEmailSendStatus) returns (RspGetEmailSendStatus) {
    option (google.api.http) = {
      post: "/notify/get_email_send_status",
      body: "*",
    };
  };
}
