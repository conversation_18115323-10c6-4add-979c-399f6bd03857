// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package notify

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TaskNo": "required",
	}, &ReqGetSmsSendStatus{})
}

func (x *ReqGetSmsSendStatus) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TaskNo": "required",
	}, &ReqGetEmailSendStatus{})
}

func (x *ReqGetEmailSendStatus) Validate() error {
	return validator.Validator().Struct(x)
}

type NotifyBffHandler interface {
}

func RegisterNotifyBff(s bff.Server, h NotifyBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Notify"), xhttp.GroupPrefix(""))
	return group
}

type NotifyGuestBffHandler interface {
	// 查询短信发送状态
	GetSmsSendStatus(context.Context, *ReqGetSmsSendStatus, *RspGetSmsSendStatus) error
	// 查询邮件发送状态
	GetEmailSendStatus(context.Context, *ReqGetEmailSendStatus, *RspGetEmailSendStatus) error
}

func RegisterNotifyGuestBff(s bff.Server, h NotifyGuestBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("NotifyGuest"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/notify/get_sms_send_status", h.GetSmsSendStatus).Name("GetSmsSendStatus")
	bff.AddRoute(group, http.MethodPost, "/notify/get_email_send_status", h.GetEmailSendStatus).Name("GetEmailSendStatus")
	return group
}
