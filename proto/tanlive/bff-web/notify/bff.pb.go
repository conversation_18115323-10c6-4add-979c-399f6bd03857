// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-web/notify/bff.proto

package notify

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqGetSmsSendStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	TaskNo string `protobuf:"bytes,1,opt,name=task_no,json=taskNo,proto3" json:"task_no,omitempty"`
}

func (x *ReqGetSmsSendStatus) Reset() {
	*x = ReqGetSmsSendStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_notify_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetSmsSendStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetSmsSendStatus) ProtoMessage() {}

func (x *ReqGetSmsSendStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_notify_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetSmsSendStatus.ProtoReflect.Descriptor instead.
func (*ReqGetSmsSendStatus) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_notify_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqGetSmsSendStatus) GetTaskNo() string {
	if x != nil {
		return x.TaskNo
	}
	return ""
}

type RspGetSmsSendStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 状态：SENDING（发送中）、SUCCESS（成功）、FAIL（失败）
	ReportStatus string `protobuf:"bytes,1,opt,name=report_status,json=reportStatus,proto3" json:"report_status,omitempty"`
	// 用户接收短信状态码错误信息，参考：https://cloud.tencent.com/document/api/382/59177#.E5.9B.9E.E6.89.A7.E7.8A.B6.E6.80.81.E9.94.99.E8.AF.AF.E7.A0.81
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg,omitempty"`
}

func (x *RspGetSmsSendStatus) Reset() {
	*x = RspGetSmsSendStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_notify_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetSmsSendStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetSmsSendStatus) ProtoMessage() {}

func (x *RspGetSmsSendStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_notify_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetSmsSendStatus.ProtoReflect.Descriptor instead.
func (*RspGetSmsSendStatus) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_notify_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspGetSmsSendStatus) GetReportStatus() string {
	if x != nil {
		return x.ReportStatus
	}
	return ""
}

func (x *RspGetSmsSendStatus) GetErrmsg() string {
	if x != nil {
		return x.Errmsg
	}
	return ""
}

type ReqGetEmailSendStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	TaskNo string `protobuf:"bytes,1,opt,name=task_no,json=taskNo,proto3" json:"task_no,omitempty"`
}

func (x *ReqGetEmailSendStatus) Reset() {
	*x = ReqGetEmailSendStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_notify_bff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetEmailSendStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetEmailSendStatus) ProtoMessage() {}

func (x *ReqGetEmailSendStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_notify_bff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetEmailSendStatus.ProtoReflect.Descriptor instead.
func (*ReqGetEmailSendStatus) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_notify_bff_proto_rawDescGZIP(), []int{2}
}

func (x *ReqGetEmailSendStatus) GetTaskNo() string {
	if x != nil {
		return x.TaskNo
	}
	return ""
}

type RspGetEmailSendStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 事件类型：
	// delivering 投递中
	// deferred 邮件被收件人邮件服务商延迟传递，正在重试中
	// delivered 递送成功，如您未收到请检查垃圾箱
	// dropped 邮件无法送达，请尝试其他邮箱地址
	// bounce 收件人邮件服务商拒收此邮件，请检查邮箱地址或尝试其他邮箱地址
	Event string `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
	// 事件产生的时间戳
	Timestamp int32 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *RspGetEmailSendStatus) Reset() {
	*x = RspGetEmailSendStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_web_notify_bff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetEmailSendStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetEmailSendStatus) ProtoMessage() {}

func (x *RspGetEmailSendStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_web_notify_bff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetEmailSendStatus.ProtoReflect.Descriptor instead.
func (*RspGetEmailSendStatus) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_web_notify_bff_proto_rawDescGZIP(), []int{3}
}

func (x *RspGetEmailSendStatus) GetEvent() string {
	if x != nil {
		return x.Event
	}
	return ""
}

func (x *RspGetEmailSendStatus) GetTimestamp() int32 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_tanlive_bff_web_notify_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_web_notify_bff_proto_rawDesc = []byte{
	0x0a, 0x20, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x77, 0x65, 0x62, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x3c, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x6e, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x6f, 0x22, 0x52, 0x0a,
	0x13, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6d, 0x73,
	0x67, 0x22, 0x3e, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x07, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x4e,
	0x6f, 0x22, 0x4b, 0x0a, 0x15, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x32, 0x11,
	0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x42, 0x66, 0x66, 0x1a, 0x04, 0xd0, 0xc6, 0x27,
	0x01, 0x32, 0xcc, 0x02, 0x0a, 0x0e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x47, 0x75, 0x65, 0x73,
	0x74, 0x42, 0x66, 0x66, 0x12, 0x94, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x6d, 0x73, 0x53,
	0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x6e, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x2b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x66, 0x66, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e,
	0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x6d, 0x73, 0x5f,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x9c, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66,
	0x5f, 0x77, 0x65, 0x62, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x1a, 0x2d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x77, 0x65, 0x62, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65,
	0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73,
	0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x04, 0xd0, 0xc6, 0x27, 0x01,
	0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74,
	0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_web_notify_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_web_notify_bff_proto_rawDescData = file_tanlive_bff_web_notify_bff_proto_rawDesc
)

func file_tanlive_bff_web_notify_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_web_notify_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_web_notify_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_web_notify_bff_proto_rawDescData)
	})
	return file_tanlive_bff_web_notify_bff_proto_rawDescData
}

var file_tanlive_bff_web_notify_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_tanlive_bff_web_notify_bff_proto_goTypes = []interface{}{
	(*ReqGetSmsSendStatus)(nil),   // 0: tanlive.bff_web.notify.ReqGetSmsSendStatus
	(*RspGetSmsSendStatus)(nil),   // 1: tanlive.bff_web.notify.RspGetSmsSendStatus
	(*ReqGetEmailSendStatus)(nil), // 2: tanlive.bff_web.notify.ReqGetEmailSendStatus
	(*RspGetEmailSendStatus)(nil), // 3: tanlive.bff_web.notify.RspGetEmailSendStatus
}
var file_tanlive_bff_web_notify_bff_proto_depIdxs = []int32{
	0, // 0: tanlive.bff_web.notify.NotifyGuestBff.GetSmsSendStatus:input_type -> tanlive.bff_web.notify.ReqGetSmsSendStatus
	2, // 1: tanlive.bff_web.notify.NotifyGuestBff.GetEmailSendStatus:input_type -> tanlive.bff_web.notify.ReqGetEmailSendStatus
	1, // 2: tanlive.bff_web.notify.NotifyGuestBff.GetSmsSendStatus:output_type -> tanlive.bff_web.notify.RspGetSmsSendStatus
	3, // 3: tanlive.bff_web.notify.NotifyGuestBff.GetEmailSendStatus:output_type -> tanlive.bff_web.notify.RspGetEmailSendStatus
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_bff_web_notify_bff_proto_init() }
func file_tanlive_bff_web_notify_bff_proto_init() {
	if File_tanlive_bff_web_notify_bff_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_web_notify_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetSmsSendStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_notify_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetSmsSendStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_notify_bff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetEmailSendStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_web_notify_bff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetEmailSendStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_web_notify_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_tanlive_bff_web_notify_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_web_notify_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_web_notify_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_web_notify_bff_proto = out.File
	file_tanlive_bff_web_notify_bff_proto_rawDesc = nil
	file_tanlive_bff_web_notify_bff_proto_goTypes = nil
	file_tanlive_bff_web_notify_bff_proto_depIdxs = nil
}
