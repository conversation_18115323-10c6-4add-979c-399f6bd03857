// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/iam/service.proto

package iam

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	notify "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqGetUsersPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region base.Region `protobuf:"varint,1,opt,name=region,proto3,enum=tanlive.base.Region" json:"region,omitempty"`
	// 过滤器
	Filter *ReqGetUsersPage_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// 分页器
	Page *base.Paginator `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	// 是否返回total_count
	WithTotalCount bool `protobuf:"varint,4,opt,name=with_total_count,json=withTotalCount,proto3" json:"with_total_count,omitempty"`
}

func (x *ReqGetUsersPage) Reset() {
	*x = ReqGetUsersPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetUsersPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetUsersPage) ProtoMessage() {}

func (x *ReqGetUsersPage) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetUsersPage.ProtoReflect.Descriptor instead.
func (*ReqGetUsersPage) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqGetUsersPage) GetRegion() base.Region {
	if x != nil {
		return x.Region
	}
	return base.Region(0)
}

func (x *ReqGetUsersPage) GetFilter() *ReqGetUsersPage_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqGetUsersPage) GetPage() *base.Paginator {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ReqGetUsersPage) GetWithTotalCount() bool {
	if x != nil {
		return x.WithTotalCount
	}
	return false
}

type RspGetUsersPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户列表
	Users []*UserInfo `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	// 总数
	TotalCount uint32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspGetUsersPage) Reset() {
	*x = RspGetUsersPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetUsersPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetUsersPage) ProtoMessage() {}

func (x *RspGetUsersPage) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetUsersPage.ProtoReflect.Descriptor instead.
func (*RspGetUsersPage) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{1}
}

func (x *RspGetUsersPage) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *RspGetUsersPage) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqGetUsersByKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []uint64 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
}

func (x *ReqGetUsersByKey) Reset() {
	*x = ReqGetUsersByKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetUsersByKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetUsersByKey) ProtoMessage() {}

func (x *ReqGetUsersByKey) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetUsersByKey.ProtoReflect.Descriptor instead.
func (*ReqGetUsersByKey) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReqGetUsersByKey) GetId() []uint64 {
	if x != nil {
		return x.Id
	}
	return nil
}

type RspGetUsersByKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserSet []*UserInfo `protobuf:"bytes,1,rep,name=user_set,json=userSet,proto3" json:"user_set,omitempty"`
}

func (x *RspGetUsersByKey) Reset() {
	*x = RspGetUsersByKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetUsersByKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetUsersByKey) ProtoMessage() {}

func (x *RspGetUsersByKey) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetUsersByKey.ProtoReflect.Descriptor instead.
func (*RspGetUsersByKey) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{3}
}

func (x *RspGetUsersByKey) GetUserSet() []*UserInfo {
	if x != nil {
		return x.UserSet
	}
	return nil
}

type ReqGetUserByUnionID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UnionId string `protobuf:"bytes,1,opt,name=union_id,json=unionId,proto3" json:"union_id,omitempty"`
}

func (x *ReqGetUserByUnionID) Reset() {
	*x = ReqGetUserByUnionID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetUserByUnionID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetUserByUnionID) ProtoMessage() {}

func (x *ReqGetUserByUnionID) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetUserByUnionID.ProtoReflect.Descriptor instead.
func (*ReqGetUserByUnionID) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{4}
}

func (x *ReqGetUserByUnionID) GetUnionId() string {
	if x != nil {
		return x.UnionId
	}
	return ""
}

type RspGetUserByUnionID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserSet *UserInfo `protobuf:"bytes,1,opt,name=user_set,json=userSet,proto3" json:"user_set,omitempty"`
}

func (x *RspGetUserByUnionID) Reset() {
	*x = RspGetUserByUnionID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetUserByUnionID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetUserByUnionID) ProtoMessage() {}

func (x *RspGetUserByUnionID) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetUserByUnionID.ProtoReflect.Descriptor instead.
func (*RspGetUserByUnionID) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{5}
}

func (x *RspGetUserByUnionID) GetUserSet() *UserInfo {
	if x != nil {
		return x.UserSet
	}
	return nil
}

type ReqCheckApiPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Path   string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	AsTeam bool   `protobuf:"varint,3,opt,name=as_team,json=asTeam,proto3" json:"as_team,omitempty"`
}

func (x *ReqCheckApiPermission) Reset() {
	*x = ReqCheckApiPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCheckApiPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCheckApiPermission) ProtoMessage() {}

func (x *ReqCheckApiPermission) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCheckApiPermission.ProtoReflect.Descriptor instead.
func (*ReqCheckApiPermission) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{6}
}

func (x *ReqCheckApiPermission) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqCheckApiPermission) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ReqCheckApiPermission) GetAsTeam() bool {
	if x != nil {
		return x.AsTeam
	}
	return false
}

type RspCheckApiPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Allowed bool `protobuf:"varint,1,opt,name=allowed,proto3" json:"allowed,omitempty"`
}

func (x *RspCheckApiPermission) Reset() {
	*x = RspCheckApiPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCheckApiPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCheckApiPermission) ProtoMessage() {}

func (x *RspCheckApiPermission) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCheckApiPermission.ProtoReflect.Descriptor instead.
func (*RspCheckApiPermission) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{7}
}

func (x *RspCheckApiPermission) GetAllowed() bool {
	if x != nil {
		return x.Allowed
	}
	return false
}

type ReqValidateLoginCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifier     string        `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	IdentifierType UserIndexType `protobuf:"varint,2,opt,name=identifier_type,json=identifierType,proto3,enum=tanlive.iam.UserIndexType" json:"identifier_type,omitempty"`
	Password       string        `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *ReqValidateLoginCredentials) Reset() {
	*x = ReqValidateLoginCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqValidateLoginCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqValidateLoginCredentials) ProtoMessage() {}

func (x *ReqValidateLoginCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqValidateLoginCredentials.ProtoReflect.Descriptor instead.
func (*ReqValidateLoginCredentials) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{8}
}

func (x *ReqValidateLoginCredentials) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *ReqValidateLoginCredentials) GetIdentifierType() UserIndexType {
	if x != nil {
		return x.IdentifierType
	}
	return UserIndexType_USER_INDEX_TYPE_UNSPECIFIED
}

func (x *ReqValidateLoginCredentials) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type RspValidateLoginCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *UserInfo `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *RspValidateLoginCredentials) Reset() {
	*x = RspValidateLoginCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspValidateLoginCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspValidateLoginCredentials) ProtoMessage() {}

func (x *RspValidateLoginCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspValidateLoginCredentials.ProtoReflect.Descriptor instead.
func (*RspValidateLoginCredentials) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{9}
}

func (x *RspValidateLoginCredentials) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

type ReqBindUserThirdIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    uint64         `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ThirdUser *ThirdUserInfo `protobuf:"bytes,2,opt,name=third_user,json=thirdUser,proto3" json:"third_user,omitempty"`
}

func (x *ReqBindUserThirdIndex) Reset() {
	*x = ReqBindUserThirdIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBindUserThirdIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBindUserThirdIndex) ProtoMessage() {}

func (x *ReqBindUserThirdIndex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBindUserThirdIndex.ProtoReflect.Descriptor instead.
func (*ReqBindUserThirdIndex) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{10}
}

func (x *ReqBindUserThirdIndex) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqBindUserThirdIndex) GetThirdUser() *ThirdUserInfo {
	if x != nil {
		return x.ThirdUser
	}
	return nil
}

type ReqUpdateUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *UserInfo              `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Mask *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=mask,proto3" json:"mask,omitempty"`
}

func (x *ReqUpdateUserInfo) Reset() {
	*x = ReqUpdateUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateUserInfo) ProtoMessage() {}

func (x *ReqUpdateUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateUserInfo.ProtoReflect.Descriptor instead.
func (*ReqUpdateUserInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{11}
}

func (x *ReqUpdateUserInfo) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ReqUpdateUserInfo) GetMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.Mask
	}
	return nil
}

type ReqGetIpRegion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ReqGetIpRegion) Reset() {
	*x = ReqGetIpRegion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetIpRegion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetIpRegion) ProtoMessage() {}

func (x *ReqGetIpRegion) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetIpRegion.ProtoReflect.Descriptor instead.
func (*ReqGetIpRegion) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{12}
}

func (x *ReqGetIpRegion) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type RspGetIpRegion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpRegion *IpRegion `protobuf:"bytes,1,opt,name=ip_region,json=ipRegion,proto3" json:"ip_region,omitempty"`
}

func (x *RspGetIpRegion) Reset() {
	*x = RspGetIpRegion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetIpRegion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetIpRegion) ProtoMessage() {}

func (x *RspGetIpRegion) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetIpRegion.ProtoReflect.Descriptor instead.
func (*RspGetIpRegion) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{13}
}

func (x *RspGetIpRegion) GetIpRegion() *IpRegion {
	if x != nil {
		return x.IpRegion
	}
	return nil
}

type ReqUpdateUserWhenTeamCreated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 管理员角色ID
	AdminRoleId uint64 `protobuf:"varint,2,opt,name=admin_role_id,json=adminRoleId,proto3" json:"admin_role_id,omitempty"`
}

func (x *ReqUpdateUserWhenTeamCreated) Reset() {
	*x = ReqUpdateUserWhenTeamCreated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateUserWhenTeamCreated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateUserWhenTeamCreated) ProtoMessage() {}

func (x *ReqUpdateUserWhenTeamCreated) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateUserWhenTeamCreated.ProtoReflect.Descriptor instead.
func (*ReqUpdateUserWhenTeamCreated) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{14}
}

func (x *ReqUpdateUserWhenTeamCreated) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqUpdateUserWhenTeamCreated) GetAdminRoleId() uint64 {
	if x != nil {
		return x.AdminRoleId
	}
	return 0
}

type ReqCreateOneTimePassword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 使用场景
	Scene OtpScene `protobuf:"varint,1,opt,name=scene,proto3,enum=tanlive.iam.OtpScene" json:"scene,omitempty"`
	// 接收者
	Receiver *OtpReceiver `protobuf:"bytes,2,opt,name=receiver,proto3" json:"receiver,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqCreateOneTimePassword) Reset() {
	*x = ReqCreateOneTimePassword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateOneTimePassword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateOneTimePassword) ProtoMessage() {}

func (x *ReqCreateOneTimePassword) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateOneTimePassword.ProtoReflect.Descriptor instead.
func (*ReqCreateOneTimePassword) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{15}
}

func (x *ReqCreateOneTimePassword) GetScene() OtpScene {
	if x != nil {
		return x.Scene
	}
	return OtpScene_OTP_SCENE_UNSPECIFIED
}

func (x *ReqCreateOneTimePassword) GetReceiver() *OtpReceiver {
	if x != nil {
		return x.Receiver
	}
	return nil
}

func (x *ReqCreateOneTimePassword) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type ReqCheckUserHavePhone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqCheckUserHavePhone) Reset() {
	*x = ReqCheckUserHavePhone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCheckUserHavePhone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCheckUserHavePhone) ProtoMessage() {}

func (x *ReqCheckUserHavePhone) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCheckUserHavePhone.ProtoReflect.Descriptor instead.
func (*ReqCheckUserHavePhone) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{16}
}

func (x *ReqCheckUserHavePhone) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type RspCheckUserHavePhone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HavePhone bool `protobuf:"varint,1,opt,name=have_phone,json=havePhone,proto3" json:"have_phone,omitempty"`
}

func (x *RspCheckUserHavePhone) Reset() {
	*x = RspCheckUserHavePhone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCheckUserHavePhone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCheckUserHavePhone) ProtoMessage() {}

func (x *RspCheckUserHavePhone) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCheckUserHavePhone.ProtoReflect.Descriptor instead.
func (*RspCheckUserHavePhone) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{17}
}

func (x *RspCheckUserHavePhone) GetHavePhone() bool {
	if x != nil {
		return x.HavePhone
	}
	return false
}

type RspCreateOneTimePassword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流结果
	RateLimitResult *base.RateLimitResult `protobuf:"bytes,1,opt,name=rate_limit_result,json=rateLimitResult,proto3" json:"rate_limit_result,omitempty"`
	// 发送验证码结果
	Result *notify.DeliveryResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *RspCreateOneTimePassword) Reset() {
	*x = RspCreateOneTimePassword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateOneTimePassword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateOneTimePassword) ProtoMessage() {}

func (x *RspCreateOneTimePassword) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateOneTimePassword.ProtoReflect.Descriptor instead.
func (*RspCreateOneTimePassword) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{18}
}

func (x *RspCreateOneTimePassword) GetRateLimitResult() *base.RateLimitResult {
	if x != nil {
		return x.RateLimitResult
	}
	return nil
}

func (x *RspCreateOneTimePassword) GetResult() *notify.DeliveryResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type ReqValidateOneTimePassword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 使用场景
	Scene OtpScene `protobuf:"varint,1,opt,name=scene,proto3,enum=tanlive.iam.OtpScene" json:"scene,omitempty"`
	// 接收者
	Receiver *OtpReceiver `protobuf:"bytes,2,opt,name=receiver,proto3" json:"receiver,omitempty"`
	// 一次性密码
	Otp string `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *ReqValidateOneTimePassword) Reset() {
	*x = ReqValidateOneTimePassword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqValidateOneTimePassword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqValidateOneTimePassword) ProtoMessage() {}

func (x *ReqValidateOneTimePassword) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqValidateOneTimePassword.ProtoReflect.Descriptor instead.
func (*ReqValidateOneTimePassword) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{19}
}

func (x *ReqValidateOneTimePassword) GetScene() OtpScene {
	if x != nil {
		return x.Scene
	}
	return OtpScene_OTP_SCENE_UNSPECIFIED
}

func (x *ReqValidateOneTimePassword) GetReceiver() *OtpReceiver {
	if x != nil {
		return x.Receiver
	}
	return nil
}

func (x *ReqValidateOneTimePassword) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type ReqCreateUserTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 2FA参数
	//
	// Types that are assignable to Para:
	//
	//	*ReqCreateUserTfa_PhoneTfa
	//	*ReqCreateUserTfa_PasswordTfa
	//	*ReqCreateUserTfa_EmailTfa
	//	*ReqCreateUserTfa_WeixinBrowserTfa
	//	*ReqCreateUserTfa_GoogleTfa
	Para isReqCreateUserTfa_Para `protobuf_oneof:"para"`
}

func (x *ReqCreateUserTfa) Reset() {
	*x = ReqCreateUserTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateUserTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateUserTfa) ProtoMessage() {}

func (x *ReqCreateUserTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateUserTfa.ProtoReflect.Descriptor instead.
func (*ReqCreateUserTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{20}
}

func (x *ReqCreateUserTfa) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (m *ReqCreateUserTfa) GetPara() isReqCreateUserTfa_Para {
	if m != nil {
		return m.Para
	}
	return nil
}

func (x *ReqCreateUserTfa) GetPhoneTfa() *ReqCreateUserTfa_PhonePara {
	if x, ok := x.GetPara().(*ReqCreateUserTfa_PhoneTfa); ok {
		return x.PhoneTfa
	}
	return nil
}

func (x *ReqCreateUserTfa) GetPasswordTfa() *ReqCreateUserTfa_PasswordPara {
	if x, ok := x.GetPara().(*ReqCreateUserTfa_PasswordTfa); ok {
		return x.PasswordTfa
	}
	return nil
}

func (x *ReqCreateUserTfa) GetEmailTfa() *ReqCreateUserTfa_EmailPara {
	if x, ok := x.GetPara().(*ReqCreateUserTfa_EmailTfa); ok {
		return x.EmailTfa
	}
	return nil
}

func (x *ReqCreateUserTfa) GetWeixinBrowserTfa() *ReqCreateUserTfa_WeixinBrowserPara {
	if x, ok := x.GetPara().(*ReqCreateUserTfa_WeixinBrowserTfa); ok {
		return x.WeixinBrowserTfa
	}
	return nil
}

func (x *ReqCreateUserTfa) GetGoogleTfa() *ReqCreateUserTfa_GooglePara {
	if x, ok := x.GetPara().(*ReqCreateUserTfa_GoogleTfa); ok {
		return x.GoogleTfa
	}
	return nil
}

type isReqCreateUserTfa_Para interface {
	isReqCreateUserTfa_Para()
}

type ReqCreateUserTfa_PhoneTfa struct {
	// 手机验证
	PhoneTfa *ReqCreateUserTfa_PhonePara `protobuf:"bytes,2,opt,name=phone_tfa,json=phoneTfa,proto3,oneof"`
}

type ReqCreateUserTfa_PasswordTfa struct {
	// 密码验证
	PasswordTfa *ReqCreateUserTfa_PasswordPara `protobuf:"bytes,3,opt,name=password_tfa,json=passwordTfa,proto3,oneof"`
}

type ReqCreateUserTfa_EmailTfa struct {
	// 邮箱验证
	EmailTfa *ReqCreateUserTfa_EmailPara `protobuf:"bytes,4,opt,name=email_tfa,json=emailTfa,proto3,oneof"`
}

type ReqCreateUserTfa_WeixinBrowserTfa struct {
	// 微信浏览器验证参数
	WeixinBrowserTfa *ReqCreateUserTfa_WeixinBrowserPara `protobuf:"bytes,5,opt,name=weixin_browser_tfa,json=weixinBrowserTfa,proto3,oneof"`
}

type ReqCreateUserTfa_GoogleTfa struct {
	// 谷歌验证参数
	GoogleTfa *ReqCreateUserTfa_GooglePara `protobuf:"bytes,6,opt,name=google_tfa,json=googleTfa,proto3,oneof"`
}

func (*ReqCreateUserTfa_PhoneTfa) isReqCreateUserTfa_Para() {}

func (*ReqCreateUserTfa_PasswordTfa) isReqCreateUserTfa_Para() {}

func (*ReqCreateUserTfa_EmailTfa) isReqCreateUserTfa_Para() {}

func (*ReqCreateUserTfa_WeixinBrowserTfa) isReqCreateUserTfa_Para() {}

func (*ReqCreateUserTfa_GoogleTfa) isReqCreateUserTfa_Para() {}

type RspCreateUserTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 2FA令牌
	TfaToken *TfaToken `protobuf:"bytes,1,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *RspCreateUserTfa) Reset() {
	*x = RspCreateUserTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateUserTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateUserTfa) ProtoMessage() {}

func (x *RspCreateUserTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateUserTfa.ProtoReflect.Descriptor instead.
func (*RspCreateUserTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{21}
}

func (x *RspCreateUserTfa) GetTfaToken() *TfaToken {
	if x != nil {
		return x.TfaToken
	}
	return nil
}

type ReqGetUserTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqGetUserTfa) Reset() {
	*x = ReqGetUserTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetUserTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetUserTfa) ProtoMessage() {}

func (x *ReqGetUserTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetUserTfa.ProtoReflect.Descriptor instead.
func (*ReqGetUserTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{22}
}

func (x *ReqGetUserTfa) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type RspGetUserTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 2FA令牌
	TfaToken *TfaToken `protobuf:"bytes,1,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *RspGetUserTfa) Reset() {
	*x = RspGetUserTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetUserTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetUserTfa) ProtoMessage() {}

func (x *RspGetUserTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetUserTfa.ProtoReflect.Descriptor instead.
func (*RspGetUserTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{23}
}

func (x *RspGetUserTfa) GetTfaToken() *TfaToken {
	if x != nil {
		return x.TfaToken
	}
	return nil
}

type ReqCreateWeixinTfaQrcode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqCreateWeixinTfaQrcode) Reset() {
	*x = ReqCreateWeixinTfaQrcode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateWeixinTfaQrcode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateWeixinTfaQrcode) ProtoMessage() {}

func (x *ReqCreateWeixinTfaQrcode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateWeixinTfaQrcode.ProtoReflect.Descriptor instead.
func (*ReqCreateWeixinTfaQrcode) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{24}
}

func (x *ReqCreateWeixinTfaQrcode) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqCreateWeixinTfaQrcode) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type RspCreateWeixinTfaQrcode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 二维码信息
	Qrcode *WeixinQrcode `protobuf:"bytes,1,opt,name=qrcode,proto3" json:"qrcode,omitempty"`
}

func (x *RspCreateWeixinTfaQrcode) Reset() {
	*x = RspCreateWeixinTfaQrcode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateWeixinTfaQrcode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateWeixinTfaQrcode) ProtoMessage() {}

func (x *RspCreateWeixinTfaQrcode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateWeixinTfaQrcode.ProtoReflect.Descriptor instead.
func (*RspCreateWeixinTfaQrcode) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{25}
}

func (x *RspCreateWeixinTfaQrcode) GetQrcode() *WeixinQrcode {
	if x != nil {
		return x.Qrcode
	}
	return nil
}

type ReqCreateWeixinTfa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 场景值
	SceneStr string `protobuf:"bytes,1,opt,name=scene_str,json=sceneStr,proto3" json:"scene_str,omitempty"`
}

func (x *ReqCreateWeixinTfa) Reset() {
	*x = ReqCreateWeixinTfa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateWeixinTfa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateWeixinTfa) ProtoMessage() {}

func (x *ReqCreateWeixinTfa) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateWeixinTfa.ProtoReflect.Descriptor instead.
func (*ReqCreateWeixinTfa) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{26}
}

func (x *ReqCreateWeixinTfa) GetSceneStr() string {
	if x != nil {
		return x.SceneStr
	}
	return ""
}

type ReqGetCreateWeixinTfaState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 场景值
	SceneStr string `protobuf:"bytes,2,opt,name=scene_str,json=sceneStr,proto3" json:"scene_str,omitempty"`
}

func (x *ReqGetCreateWeixinTfaState) Reset() {
	*x = ReqGetCreateWeixinTfaState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetCreateWeixinTfaState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetCreateWeixinTfaState) ProtoMessage() {}

func (x *ReqGetCreateWeixinTfaState) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetCreateWeixinTfaState.ProtoReflect.Descriptor instead.
func (*ReqGetCreateWeixinTfaState) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{27}
}

func (x *ReqGetCreateWeixinTfaState) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqGetCreateWeixinTfaState) GetSceneStr() string {
	if x != nil {
		return x.SceneStr
	}
	return ""
}

type RspGetCreateWeixinTfaState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 状态
	State CreateWeixinTfaState `protobuf:"varint,1,opt,name=state,proto3,enum=tanlive.iam.CreateWeixinTfaState" json:"state,omitempty"`
	// 2FA令牌
	TfaToken *TfaToken `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *RspGetCreateWeixinTfaState) Reset() {
	*x = RspGetCreateWeixinTfaState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetCreateWeixinTfaState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetCreateWeixinTfaState) ProtoMessage() {}

func (x *RspGetCreateWeixinTfaState) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetCreateWeixinTfaState.ProtoReflect.Descriptor instead.
func (*RspGetCreateWeixinTfaState) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{28}
}

func (x *RspGetCreateWeixinTfaState) GetState() CreateWeixinTfaState {
	if x != nil {
		return x.State
	}
	return CreateWeixinTfaState_CREATE_WEIXIN_TFA_STATE_UNSPECIFIED
}

func (x *RspGetCreateWeixinTfaState) GetTfaToken() *TfaToken {
	if x != nil {
		return x.TfaToken
	}
	return nil
}

type ReqValidateTwoFactorAuth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqValidateTwoFactorAuth) Reset() {
	*x = ReqValidateTwoFactorAuth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqValidateTwoFactorAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqValidateTwoFactorAuth) ProtoMessage() {}

func (x *ReqValidateTwoFactorAuth) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqValidateTwoFactorAuth.ProtoReflect.Descriptor instead.
func (*ReqValidateTwoFactorAuth) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{29}
}

func (x *ReqValidateTwoFactorAuth) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqValidateTwoFactorAuth) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqModifyUserUsername struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户名
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,3,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqModifyUserUsername) Reset() {
	*x = ReqModifyUserUsername{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyUserUsername) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyUserUsername) ProtoMessage() {}

func (x *ReqModifyUserUsername) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyUserUsername.ProtoReflect.Descriptor instead.
func (*ReqModifyUserUsername) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{30}
}

func (x *ReqModifyUserUsername) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqModifyUserUsername) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ReqModifyUserUsername) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqModifyUserPassword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 密码
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,3,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqModifyUserPassword) Reset() {
	*x = ReqModifyUserPassword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyUserPassword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyUserPassword) ProtoMessage() {}

func (x *ReqModifyUserPassword) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyUserPassword.ProtoReflect.Descriptor instead.
func (*ReqModifyUserPassword) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{31}
}

func (x *ReqModifyUserPassword) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqModifyUserPassword) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ReqModifyUserPassword) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqModifyUserPhone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 手机号
	Phone string `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,3,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
	// 验证码
	Otp string `protobuf:"bytes,4,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *ReqModifyUserPhone) Reset() {
	*x = ReqModifyUserPhone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyUserPhone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyUserPhone) ProtoMessage() {}

func (x *ReqModifyUserPhone) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyUserPhone.ProtoReflect.Descriptor instead.
func (*ReqModifyUserPhone) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{32}
}

func (x *ReqModifyUserPhone) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqModifyUserPhone) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ReqModifyUserPhone) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

func (x *ReqModifyUserPhone) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type ReqModifyUserEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 邮箱
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,3,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqModifyUserEmail) Reset() {
	*x = ReqModifyUserEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyUserEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyUserEmail) ProtoMessage() {}

func (x *ReqModifyUserEmail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyUserEmail.ProtoReflect.Descriptor instead.
func (*ReqModifyUserEmail) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{33}
}

func (x *ReqModifyUserEmail) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqModifyUserEmail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ReqModifyUserEmail) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

func (x *ReqModifyUserEmail) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type RspModifyUserEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发送结果
	Result *notify.DeliveryResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *RspModifyUserEmail) Reset() {
	*x = RspModifyUserEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspModifyUserEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspModifyUserEmail) ProtoMessage() {}

func (x *RspModifyUserEmail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspModifyUserEmail.ProtoReflect.Descriptor instead.
func (*RspModifyUserEmail) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{34}
}

func (x *RspModifyUserEmail) GetResult() *notify.DeliveryResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type ReqActiveUserEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 激活邮件参数
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ReqActiveUserEmail) Reset() {
	*x = ReqActiveUserEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqActiveUserEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqActiveUserEmail) ProtoMessage() {}

func (x *ReqActiveUserEmail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqActiveUserEmail.ProtoReflect.Descriptor instead.
func (*ReqActiveUserEmail) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{35}
}

func (x *ReqActiveUserEmail) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqActiveUserEmail) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ReqCreateBindUserWeixinQrcode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqCreateBindUserWeixinQrcode) Reset() {
	*x = ReqCreateBindUserWeixinQrcode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateBindUserWeixinQrcode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateBindUserWeixinQrcode) ProtoMessage() {}

func (x *ReqCreateBindUserWeixinQrcode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateBindUserWeixinQrcode.ProtoReflect.Descriptor instead.
func (*ReqCreateBindUserWeixinQrcode) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{36}
}

func (x *ReqCreateBindUserWeixinQrcode) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqCreateBindUserWeixinQrcode) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

func (x *ReqCreateBindUserWeixinQrcode) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type RspCreateBindUserWeixinQrcode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 二维码信息
	Qrcode *WeixinQrcode `protobuf:"bytes,1,opt,name=qrcode,proto3" json:"qrcode,omitempty"`
}

func (x *RspCreateBindUserWeixinQrcode) Reset() {
	*x = RspCreateBindUserWeixinQrcode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateBindUserWeixinQrcode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateBindUserWeixinQrcode) ProtoMessage() {}

func (x *RspCreateBindUserWeixinQrcode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateBindUserWeixinQrcode.ProtoReflect.Descriptor instead.
func (*RspCreateBindUserWeixinQrcode) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{37}
}

func (x *RspCreateBindUserWeixinQrcode) GetQrcode() *WeixinQrcode {
	if x != nil {
		return x.Qrcode
	}
	return nil
}

type ReqGetBindUserWeixinState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 场景值
	SceneStr string `protobuf:"bytes,2,opt,name=scene_str,json=sceneStr,proto3" json:"scene_str,omitempty"`
}

func (x *ReqGetBindUserWeixinState) Reset() {
	*x = ReqGetBindUserWeixinState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetBindUserWeixinState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetBindUserWeixinState) ProtoMessage() {}

func (x *ReqGetBindUserWeixinState) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetBindUserWeixinState.ProtoReflect.Descriptor instead.
func (*ReqGetBindUserWeixinState) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{38}
}

func (x *ReqGetBindUserWeixinState) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqGetBindUserWeixinState) GetSceneStr() string {
	if x != nil {
		return x.SceneStr
	}
	return ""
}

type RspGetBindUserWeixinState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 状态
	State BindWeixinState `protobuf:"varint,1,opt,name=state,proto3,enum=tanlive.iam.BindWeixinState" json:"state,omitempty"`
}

func (x *RspGetBindUserWeixinState) Reset() {
	*x = RspGetBindUserWeixinState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetBindUserWeixinState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetBindUserWeixinState) ProtoMessage() {}

func (x *RspGetBindUserWeixinState) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetBindUserWeixinState.ProtoReflect.Descriptor instead.
func (*RspGetBindUserWeixinState) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{39}
}

func (x *RspGetBindUserWeixinState) GetState() BindWeixinState {
	if x != nil {
		return x.State
	}
	return BindWeixinState_BIND_WEIXIN_STATE_UNSPECIFIED
}

type ReqBindUserWeixin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// OpenID
	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 二维码场景值
	SceneStr string `protobuf:"bytes,2,opt,name=scene_str,json=sceneStr,proto3" json:"scene_str,omitempty"`
}

func (x *ReqBindUserWeixin) Reset() {
	*x = ReqBindUserWeixin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBindUserWeixin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBindUserWeixin) ProtoMessage() {}

func (x *ReqBindUserWeixin) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBindUserWeixin.ProtoReflect.Descriptor instead.
func (*ReqBindUserWeixin) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{40}
}

func (x *ReqBindUserWeixin) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ReqBindUserWeixin) GetSceneStr() string {
	if x != nil {
		return x.SceneStr
	}
	return ""
}

type ReqBindUserWeixinByOauth2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 微信回调code
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,3,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqBindUserWeixinByOauth2) Reset() {
	*x = ReqBindUserWeixinByOauth2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBindUserWeixinByOauth2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBindUserWeixinByOauth2) ProtoMessage() {}

func (x *ReqBindUserWeixinByOauth2) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBindUserWeixinByOauth2.ProtoReflect.Descriptor instead.
func (*ReqBindUserWeixinByOauth2) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{41}
}

func (x *ReqBindUserWeixinByOauth2) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqBindUserWeixinByOauth2) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ReqBindUserWeixinByOauth2) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqUnbindUserWeixin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqUnbindUserWeixin) Reset() {
	*x = ReqUnbindUserWeixin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUnbindUserWeixin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUnbindUserWeixin) ProtoMessage() {}

func (x *ReqUnbindUserWeixin) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUnbindUserWeixin.ProtoReflect.Descriptor instead.
func (*ReqUnbindUserWeixin) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{42}
}

func (x *ReqUnbindUserWeixin) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqUnbindUserWeixin) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqServeWeixinOfficialAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求方法
	Method string `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	// 请求query参数
	Query map[string]string `protobuf:"bytes,2,rep,name=query,proto3" json:"query,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 请求body
	Body []byte `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *ReqServeWeixinOfficialAccount) Reset() {
	*x = ReqServeWeixinOfficialAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqServeWeixinOfficialAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqServeWeixinOfficialAccount) ProtoMessage() {}

func (x *ReqServeWeixinOfficialAccount) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqServeWeixinOfficialAccount.ProtoReflect.Descriptor instead.
func (*ReqServeWeixinOfficialAccount) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{43}
}

func (x *ReqServeWeixinOfficialAccount) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ReqServeWeixinOfficialAccount) GetQuery() map[string]string {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *ReqServeWeixinOfficialAccount) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

type RspServeWeixinOfficialAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应类型
	ContentType string `protobuf:"bytes,1,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	// 响应体
	Body []byte `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *RspServeWeixinOfficialAccount) Reset() {
	*x = RspServeWeixinOfficialAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspServeWeixinOfficialAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspServeWeixinOfficialAccount) ProtoMessage() {}

func (x *RspServeWeixinOfficialAccount) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspServeWeixinOfficialAccount.ProtoReflect.Descriptor instead.
func (*RspServeWeixinOfficialAccount) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{44}
}

func (x *RspServeWeixinOfficialAccount) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *RspServeWeixinOfficialAccount) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

type ReqGetGoogleAuthUrl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 场景
	Scene GoogleAuthScene `protobuf:"varint,1,opt,name=scene,proto3,enum=tanlive.iam.GoogleAuthScene" json:"scene,omitempty"`
	// state参数
	State string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *ReqGetGoogleAuthUrl) Reset() {
	*x = ReqGetGoogleAuthUrl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetGoogleAuthUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetGoogleAuthUrl) ProtoMessage() {}

func (x *ReqGetGoogleAuthUrl) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetGoogleAuthUrl.ProtoReflect.Descriptor instead.
func (*ReqGetGoogleAuthUrl) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{45}
}

func (x *ReqGetGoogleAuthUrl) GetScene() GoogleAuthScene {
	if x != nil {
		return x.Scene
	}
	return GoogleAuthScene_GOOGLE_AUTH_SCENE_UNSPECIFIED
}

func (x *ReqGetGoogleAuthUrl) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type RspGetGoogleAuthUrl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 认证URL
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *RspGetGoogleAuthUrl) Reset() {
	*x = RspGetGoogleAuthUrl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetGoogleAuthUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetGoogleAuthUrl) ProtoMessage() {}

func (x *RspGetGoogleAuthUrl) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetGoogleAuthUrl.ProtoReflect.Descriptor instead.
func (*RspGetGoogleAuthUrl) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{46}
}

func (x *RspGetGoogleAuthUrl) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ReqBindUserGoogle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 谷歌回调code
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,3,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqBindUserGoogle) Reset() {
	*x = ReqBindUserGoogle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBindUserGoogle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBindUserGoogle) ProtoMessage() {}

func (x *ReqBindUserGoogle) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBindUserGoogle.ProtoReflect.Descriptor instead.
func (*ReqBindUserGoogle) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{47}
}

func (x *ReqBindUserGoogle) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqBindUserGoogle) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ReqBindUserGoogle) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqUnbindUserGoogle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 2FA令牌
	TfaToken string `protobuf:"bytes,2,opt,name=tfa_token,json=tfaToken,proto3" json:"tfa_token,omitempty"`
}

func (x *ReqUnbindUserGoogle) Reset() {
	*x = ReqUnbindUserGoogle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUnbindUserGoogle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUnbindUserGoogle) ProtoMessage() {}

func (x *ReqUnbindUserGoogle) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUnbindUserGoogle.ProtoReflect.Descriptor instead.
func (*ReqUnbindUserGoogle) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{48}
}

func (x *ReqUnbindUserGoogle) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqUnbindUserGoogle) GetTfaToken() string {
	if x != nil {
		return x.TfaToken
	}
	return ""
}

type ReqMakeUniqueUsername struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户名
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
}

func (x *ReqMakeUniqueUsername) Reset() {
	*x = ReqMakeUniqueUsername{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqMakeUniqueUsername) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqMakeUniqueUsername) ProtoMessage() {}

func (x *ReqMakeUniqueUsername) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqMakeUniqueUsername.ProtoReflect.Descriptor instead.
func (*ReqMakeUniqueUsername) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{49}
}

func (x *ReqMakeUniqueUsername) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type RspMakeUniqueUsername struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户名
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
}

func (x *RspMakeUniqueUsername) Reset() {
	*x = RspMakeUniqueUsername{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspMakeUniqueUsername) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspMakeUniqueUsername) ProtoMessage() {}

func (x *RspMakeUniqueUsername) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspMakeUniqueUsername.ProtoReflect.Descriptor instead.
func (*RspMakeUniqueUsername) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{50}
}

func (x *RspMakeUniqueUsername) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type ReqDisableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId []uint64 `protobuf:"varint,1,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqDisableUser) Reset() {
	*x = ReqDisableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDisableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDisableUser) ProtoMessage() {}

func (x *ReqDisableUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDisableUser.ProtoReflect.Descriptor instead.
func (*ReqDisableUser) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{51}
}

func (x *ReqDisableUser) GetUserId() []uint64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

type RspDisableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*RspDisableUser_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *RspDisableUser) Reset() {
	*x = RspDisableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDisableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDisableUser) ProtoMessage() {}

func (x *RspDisableUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDisableUser.ProtoReflect.Descriptor instead.
func (*RspDisableUser) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{52}
}

func (x *RspDisableUser) GetResults() []*RspDisableUser_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type ReqEnableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserId []uint64 `protobuf:"varint,1,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqEnableUser) Reset() {
	*x = ReqEnableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqEnableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqEnableUser) ProtoMessage() {}

func (x *ReqEnableUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqEnableUser.ProtoReflect.Descriptor instead.
func (*ReqEnableUser) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{53}
}

func (x *ReqEnableUser) GetUserId() []uint64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

type RspEnableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*RspEnableUser_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *RspEnableUser) Reset() {
	*x = RspEnableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspEnableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspEnableUser) ProtoMessage() {}

func (x *RspEnableUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspEnableUser.ProtoReflect.Descriptor instead.
func (*RspEnableUser) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{54}
}

func (x *RspEnableUser) GetResults() []*RspEnableUser_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type ReqGetUserIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// 是否精确匹配
	ExactMatch bool `protobuf:"varint,2,opt,name=exact_match,json=exactMatch,proto3" json:"exact_match,omitempty"`
	// 地域
	Region base.Region `protobuf:"varint,3,opt,name=region,proto3,enum=tanlive.base.Region" json:"region,omitempty"`
}

func (x *ReqGetUserIds) Reset() {
	*x = ReqGetUserIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetUserIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetUserIds) ProtoMessage() {}

func (x *ReqGetUserIds) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetUserIds.ProtoReflect.Descriptor instead.
func (*ReqGetUserIds) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{55}
}

func (x *ReqGetUserIds) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ReqGetUserIds) GetExactMatch() bool {
	if x != nil {
		return x.ExactMatch
	}
	return false
}

func (x *ReqGetUserIds) GetRegion() base.Region {
	if x != nil {
		return x.Region
	}
	return base.Region(0)
}

type RspGetUserIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []uint64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *RspGetUserIds) Reset() {
	*x = RspGetUserIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetUserIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetUserIds) ProtoMessage() {}

func (x *RspGetUserIds) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetUserIds.ProtoReflect.Descriptor instead.
func (*RspGetUserIds) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{56}
}

func (x *RspGetUserIds) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ReqGetOauthClient struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 客户端ID
	ClientId []string `protobuf:"bytes,1,rep,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *ReqGetOauthClient) Reset() {
	*x = ReqGetOauthClient{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetOauthClient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetOauthClient) ProtoMessage() {}

func (x *ReqGetOauthClient) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetOauthClient.ProtoReflect.Descriptor instead.
func (*ReqGetOauthClient) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{57}
}

func (x *ReqGetOauthClient) GetClientId() []string {
	if x != nil {
		return x.ClientId
	}
	return nil
}

type RspGetOauthClient struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 客户端信息
	Clients []*OauthClient `protobuf:"bytes,1,rep,name=clients,proto3" json:"clients,omitempty"`
}

func (x *RspGetOauthClient) Reset() {
	*x = RspGetOauthClient{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetOauthClient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetOauthClient) ProtoMessage() {}

func (x *RspGetOauthClient) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetOauthClient.ProtoReflect.Descriptor instead.
func (*RspGetOauthClient) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{58}
}

func (x *RspGetOauthClient) GetClients() []*OauthClient {
	if x != nil {
		return x.Clients
	}
	return nil
}

// 过滤器
type ReqGetUsersPage_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设置user_id范围
	WithinUserId []uint64 `protobuf:"varint,1,rep,packed,name=within_user_id,json=withinUserId,proto3" json:"within_user_id,omitempty"`
	// 搜索关键词
	Keyword string `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// 微信union_id
	UnionId string `protobuf:"bytes,3,opt,name=union_id,json=unionId,proto3" json:"union_id,omitempty"`
}

func (x *ReqGetUsersPage_Filter) Reset() {
	*x = ReqGetUsersPage_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetUsersPage_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetUsersPage_Filter) ProtoMessage() {}

func (x *ReqGetUsersPage_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetUsersPage_Filter.ProtoReflect.Descriptor instead.
func (*ReqGetUsersPage_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ReqGetUsersPage_Filter) GetWithinUserId() []uint64 {
	if x != nil {
		return x.WithinUserId
	}
	return nil
}

func (x *ReqGetUsersPage_Filter) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ReqGetUsersPage_Filter) GetUnionId() string {
	if x != nil {
		return x.UnionId
	}
	return ""
}

// 手机验证参数
type ReqCreateUserTfa_PhonePara struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 一次性密码
	Otp string `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *ReqCreateUserTfa_PhonePara) Reset() {
	*x = ReqCreateUserTfa_PhonePara{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateUserTfa_PhonePara) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateUserTfa_PhonePara) ProtoMessage() {}

func (x *ReqCreateUserTfa_PhonePara) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateUserTfa_PhonePara.ProtoReflect.Descriptor instead.
func (*ReqCreateUserTfa_PhonePara) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ReqCreateUserTfa_PhonePara) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

// 密码验证参数
type ReqCreateUserTfa_PasswordPara struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 密码
	Password string `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *ReqCreateUserTfa_PasswordPara) Reset() {
	*x = ReqCreateUserTfa_PasswordPara{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateUserTfa_PasswordPara) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateUserTfa_PasswordPara) ProtoMessage() {}

func (x *ReqCreateUserTfa_PasswordPara) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateUserTfa_PasswordPara.ProtoReflect.Descriptor instead.
func (*ReqCreateUserTfa_PasswordPara) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{20, 1}
}

func (x *ReqCreateUserTfa_PasswordPara) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// 邮箱验证参数
type ReqCreateUserTfa_EmailPara struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 一次性密码
	Otp string `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *ReqCreateUserTfa_EmailPara) Reset() {
	*x = ReqCreateUserTfa_EmailPara{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateUserTfa_EmailPara) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateUserTfa_EmailPara) ProtoMessage() {}

func (x *ReqCreateUserTfa_EmailPara) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateUserTfa_EmailPara.ProtoReflect.Descriptor instead.
func (*ReqCreateUserTfa_EmailPara) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{20, 2}
}

func (x *ReqCreateUserTfa_EmailPara) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

// 微信浏览器验证参数
type ReqCreateUserTfa_WeixinBrowserPara struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 微信回调code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ReqCreateUserTfa_WeixinBrowserPara) Reset() {
	*x = ReqCreateUserTfa_WeixinBrowserPara{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateUserTfa_WeixinBrowserPara) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateUserTfa_WeixinBrowserPara) ProtoMessage() {}

func (x *ReqCreateUserTfa_WeixinBrowserPara) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateUserTfa_WeixinBrowserPara.ProtoReflect.Descriptor instead.
func (*ReqCreateUserTfa_WeixinBrowserPara) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{20, 3}
}

func (x *ReqCreateUserTfa_WeixinBrowserPara) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// 谷歌验证参数
type ReqCreateUserTfa_GooglePara struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 谷歌回调code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ReqCreateUserTfa_GooglePara) Reset() {
	*x = ReqCreateUserTfa_GooglePara{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateUserTfa_GooglePara) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateUserTfa_GooglePara) ProtoMessage() {}

func (x *ReqCreateUserTfa_GooglePara) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateUserTfa_GooglePara.ProtoReflect.Descriptor instead.
func (*ReqCreateUserTfa_GooglePara) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{20, 4}
}

func (x *ReqCreateUserTfa_GooglePara) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type RspDisableUser_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Id       uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
}

func (x *RspDisableUser_Result) Reset() {
	*x = RspDisableUser_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDisableUser_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDisableUser_Result) ProtoMessage() {}

func (x *RspDisableUser_Result) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDisableUser_Result.ProtoReflect.Descriptor instead.
func (*RspDisableUser_Result) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{52, 0}
}

func (x *RspDisableUser_Result) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RspDisableUser_Result) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDisableUser_Result) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type RspEnableUser_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Id       uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
}

func (x *RspEnableUser_Result) Reset() {
	*x = RspEnableUser_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspEnableUser_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspEnableUser_Result) ProtoMessage() {}

func (x *RspEnableUser_Result) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspEnableUser_Result.ProtoReflect.Descriptor instead.
func (*RspEnableUser_Result) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_service_proto_rawDescGZIP(), []int{54, 0}
}

func (x *RspEnableUser_Result) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RspEnableUser_Result) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspEnableUser_Result) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

var File_tanlive_iam_service_proto protoreflect.FileDescriptor

var file_tanlive_iam_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75,
	0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x69, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xd6, 0x02, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x50, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x50,
	0x61, 0x67, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x77, 0x69, 0x74, 0x68, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x80, 0x01, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0e, 0x77, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x1b, 0x82, 0x88,
	0x27, 0x17, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2c, 0x64, 0x69, 0x76, 0x65,
	0x2c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0c, 0x77, 0x69, 0x74, 0x68, 0x69,
	0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x0f,
	0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x50, 0x61, 0x67, 0x65, 0x12,
	0x2b, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x30, 0x0a,
	0x10, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4b, 0x65,
	0x79, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x44, 0x0a, 0x10, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79,
	0x4b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x53, 0x65, 0x74, 0x22, 0x30, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x42, 0x79, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x19, 0x0a, 0x08,
	0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x47, 0x0a, 0x13, 0x52, 0x73, 0x70, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x30,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x53, 0x65, 0x74,
	0x22, 0x79, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x70, 0x69, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x73, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x73, 0x54, 0x65, 0x61, 0x6d, 0x22, 0x31, 0x0a, 0x15, 0x52,
	0x73, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x70, 0x69, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x22, 0xd2,
	0x01, 0x0a, 0x1b, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x36,
	0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x16, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x8a, 0x88, 0x27, 0x06, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x8a, 0x88, 0x27,
	0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x22, 0x48, 0x0a, 0x1b, 0x52, 0x73, 0x70, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x73, 0x12, 0x29, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x87, 0x01,
	0x0a, 0x15, 0x52, 0x65, 0x71, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x54, 0x68, 0x69,
	0x72, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x47,
	0x0a, 0x0a, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x74, 0x68,
	0x69, 0x72, 0x64, 0x55, 0x73, 0x65, 0x72, 0x22, 0x8a, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x71, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04,
	0x6d, 0x61, 0x73, 0x6b, 0x22, 0x38, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x49, 0x70,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x16, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x8a, 0x88, 0x27, 0x06, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x52, 0x02, 0x69, 0x70, 0x22, 0x44,
	0x0a, 0x0e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x32, 0x0a, 0x09, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x69, 0x70, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x22, 0x5b, 0x0a, 0x1c, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x57, 0x68, 0x65, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x6f, 0x6c, 0x65, 0x49,
	0x64, 0x22, 0xbb, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f,
	0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x39,
	0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4f, 0x74, 0x70, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x20, 0x0a,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22,
	0x3e, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x48,
	0x61, 0x76, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x36, 0x0a, 0x15, 0x52, 0x73, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x48,
	0x61, 0x76, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x61, 0x76, 0x65,
	0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x68, 0x61,
	0x76, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x18, 0x52, 0x73, 0x70, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x49, 0x0a, 0x11, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x52,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0f,
	0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x36, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc7, 0x01, 0x0a, 0x1a, 0x52, 0x65, 0x71, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x39, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x4f, 0x74, 0x70, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x12, 0x42, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x03, 0x6f, 0x74,
	0x70, 0x22, 0xb5, 0x06, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x54, 0x0a,
	0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x74, 0x66, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x54, 0x66, 0x61, 0x12, 0x5d, 0x0a, 0x0c, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x5f,
	0x74, 0x66, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x54,
	0x66, 0x61, 0x12, 0x54, 0x0a, 0x09, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x66, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x66, 0x61, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x08,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x66, 0x61, 0x12, 0x6d, 0x0a, 0x12, 0x77, 0x65, 0x69, 0x78,
	0x69, 0x6e, 0x5f, 0x62, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x66, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x54, 0x66, 0x61, 0x2e, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x72, 0x6f, 0x77, 0x73, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x10, 0x77, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x72, 0x6f,
	0x77, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x57, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x5f, 0x74, 0x66, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x54, 0x66, 0x61,
	0x1a, 0x38, 0x0a, 0x09, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x12, 0x2b, 0x0a,
	0x03, 0x6f, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0x82, 0x88, 0x27, 0x15,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6c, 0x65, 0x6e, 0x3d, 0x36, 0x2c, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x1a, 0x44, 0x0a, 0x0c, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x12, 0x34, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x1a, 0x38, 0x0a, 0x09, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x12, 0x2b, 0x0a,
	0x03, 0x6f, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0x82, 0x88, 0x27, 0x15,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6c, 0x65, 0x6e, 0x3d, 0x36, 0x2c, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x1a, 0x35, 0x0a, 0x11, 0x57, 0x65,
	0x69, 0x78, 0x69, 0x6e, 0x42, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x12,
	0x20, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x1a, 0x2e, 0x0a, 0x0a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x12,
	0x20, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x42, 0x06, 0x0a, 0x04, 0x70, 0x61, 0x72, 0x61, 0x22, 0x46, 0x0a, 0x10, 0x52, 0x73, 0x70,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x32, 0x0a,
	0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x54,
	0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x36, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54,
	0x66, 0x61, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x0d, 0x52, 0x73, 0x70,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x32, 0x0a, 0x09, 0x74, 0x66,
	0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x54, 0x66, 0x61, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x63,
	0x0a, 0x18, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69,
	0x6e, 0x54, 0x66, 0x61, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x6c,
	0x61, 0x6e, 0x67, 0x22, 0x4d, 0x0a, 0x18, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x31, 0x0a, 0x06, 0x71, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x57, 0x65,
	0x69, 0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x71, 0x72, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x3f, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x53, 0x74, 0x72, 0x22, 0x6e, 0x0a, 0x1a, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x53, 0x74, 0x72, 0x22, 0x89, 0x01, 0x0a, 0x1a, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x74,
	0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x54, 0x66, 0x61,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22,
	0x6c, 0x0a, 0x18, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x77,
	0x6f, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x25, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xaa, 0x01,
	0x0a, 0x15, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x55,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3f,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x23, 0x82, 0x88, 0x27, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x8a, 0x88, 0x27, 0x0a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa8, 0x01, 0x0a, 0x15, 0x52,
	0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0x82,
	0x88, 0x27, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x66,
	0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x74, 0x66, 0x61,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xc7, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x25, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1d, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x2c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x8a, 0x88, 0x27, 0x07, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x2a, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88,
	0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x22,
	0xbd, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x33, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x82, 0x88,
	0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x8a, 0x88, 0x27, 0x07, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22,
	0x4c, 0x0a, 0x12, 0x52, 0x73, 0x70, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x36, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x5f, 0x0a,
	0x12, 0x52, 0x65, 0x71, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x93,
	0x01, 0x0a, 0x1d, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x20, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x22, 0x52, 0x0a, 0x1d, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51,
	0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x71, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65,
	0x52, 0x06, 0x71, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x6d, 0x0a, 0x19, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x09,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x73,
	0x63, 0x65, 0x6e, 0x65, 0x53, 0x74, 0x72, 0x22, 0x4f, 0x0a, 0x19, 0x52, 0x73, 0x70, 0x47, 0x65,
	0x74, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x65, 0x0a, 0x11, 0x52, 0x65, 0x71, 0x42,
	0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x12, 0x25, 0x0a,
	0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x73, 0x74,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x53, 0x74, 0x72, 0x22,
	0x8f, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x71, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57,
	0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x79, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x12, 0x25, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x67, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xd2, 0x01, 0x0a, 0x1d, 0x52,
	0x65, 0x71, 0x53, 0x65, 0x72, 0x76, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x4f, 0x66, 0x66,
	0x69, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x4b, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x72, 0x76, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e,
	0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x38, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x56, 0x0a, 0x1d, 0x52, 0x73, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69,
	0x6e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22, 0x7b, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x47, 0x65,
	0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x40,
	0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x22, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x27, 0x0a, 0x13, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x93, 0x01,
	0x0a, 0x11, 0x52, 0x65, 0x71, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x67, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x66, 0x61, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x08, 0x74, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x4f, 0x0a, 0x15,
	0x52, 0x65, 0x71, 0x4d, 0x61, 0x6b, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x8a, 0x88, 0x27, 0x0a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x43, 0x0a,
	0x15, 0x52, 0x73, 0x70, 0x4d, 0x61, 0x6b, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x8a, 0x88, 0x27, 0x0a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x45, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x1a, 0x82, 0x88, 0x27, 0x16, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x98, 0x01, 0x0a, 0x0e, 0x52, 0x73,
	0x70, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x48, 0x0a, 0x06, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x44, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x1a, 0x82, 0x88, 0x27, 0x16, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x96, 0x01, 0x0a, 0x0d, 0x52,
	0x73, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x48, 0x0a, 0x06, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x96, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x61, 0x63, 0x74, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x65, 0x78, 0x61, 0x63, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x3a, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x21, 0x0a, 0x0d,
	0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22,
	0x4c, 0x0a, 0x11, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0x82, 0x88, 0x27, 0x16, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x47, 0x0a,
	0x11, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x32, 0x8e, 0x19, 0x0a, 0x0a, 0x49, 0x61, 0x6d, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x50,
	0x61, 0x67, 0x65, 0x1a, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x50, 0x61, 0x67,
	0x65, 0x12, 0x4d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4b,
	0x65, 0x79, 0x12, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4b, 0x65,
	0x79, 0x1a, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4b, 0x65, 0x79,
	0x12, 0x56, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x55, 0x6e, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x12, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x55,
	0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x1a, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x79, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x5c, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x41, 0x70, 0x69, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x70, 0x69, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x1a, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x73, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x70, 0x69, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x6e, 0x0a, 0x18, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x73, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x1a, 0x28, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x68, 0x69, 0x72, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x22, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x42, 0x69,
	0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x54, 0x68, 0x69, 0x72, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x48, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x47, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x1a, 0x1b,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70,
	0x47, 0x65, 0x74, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x5e, 0x0a, 0x19, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x57, 0x68, 0x65, 0x6e, 0x54, 0x65, 0x61,
	0x6d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x29, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x57, 0x68, 0x65, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5c, 0x0a, 0x12, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x48, 0x61, 0x76, 0x65, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x65, 0x71, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x48, 0x61, 0x76, 0x65,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x1a, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72,
	0x48, 0x61, 0x76, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x65, 0x0a, 0x15, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x1a, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x5a, 0x0a, 0x17, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x27, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4d, 0x0a, 0x0d,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x1d, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x1a, 0x1d, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x44, 0x0a, 0x0a, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66, 0x61, 0x12, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x66, 0x61, 0x1a, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x66,
	0x61, 0x12, 0x65, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69,
	0x6e, 0x54, 0x66, 0x61, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x51, 0x72, 0x63, 0x6f, 0x64,
	0x65, 0x1a, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54,
	0x66, 0x61, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x4a, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x12, 0x1f, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x6b, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65,
	0x71, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e,
	0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x1a, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x56, 0x0a, 0x15, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x77, 0x6f,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x77, 0x6f, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74,
	0x68, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x50, 0x0a, 0x12, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65,
	0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x50, 0x0a, 0x12, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4a, 0x0a,
	0x0f, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52,
	0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x53, 0x0a, 0x0f, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1f, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x1a, 0x1f, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x4a,
	0x0a, 0x0f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x52, 0x65, 0x71, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x74, 0x0a, 0x1a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78,
	0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51, 0x72,
	0x63, 0x6f, 0x64, 0x65, 0x1a, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x68, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57,
	0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x42,
	0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x1a, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57,
	0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x42, 0x69,
	0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x12, 0x1e, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x42, 0x69,
	0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x58, 0x0a, 0x16, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x79, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x12, 0x26,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71,
	0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x42, 0x79,
	0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c,
	0x0a, 0x10, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x78,
	0x69, 0x6e, 0x12, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x52, 0x65, 0x71, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65,
	0x69, 0x78, 0x69, 0x6e, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x74, 0x0a, 0x1a,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x4f, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x57, 0x65, 0x69,
	0x78, 0x69, 0x6e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x56, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41,
	0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x1a, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x48, 0x0a, 0x0e, 0x42, 0x69,
	0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x1e, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x42, 0x69,
	0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x10, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x5c, 0x0a, 0x12, 0x4d, 0x61, 0x6b, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x61, 0x6b, 0x65, 0x55, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x22, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x4d, 0x61,
	0x6b, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x47, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65,
	0x71, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x1a, 0x1b, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x0a, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x1a, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x52, 0x73, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x44, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x1a, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x50, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4f, 0x61, 0x75, 0x74,
	0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x4f, 0x61, 0x75, 0x74,
	0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x1a, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x4f, 0x61, 0x75, 0x74,
	0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x3d, 0x5a, 0x3b, 0x65, 0x2e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_iam_service_proto_rawDescOnce sync.Once
	file_tanlive_iam_service_proto_rawDescData = file_tanlive_iam_service_proto_rawDesc
)

func file_tanlive_iam_service_proto_rawDescGZIP() []byte {
	file_tanlive_iam_service_proto_rawDescOnce.Do(func() {
		file_tanlive_iam_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_iam_service_proto_rawDescData)
	})
	return file_tanlive_iam_service_proto_rawDescData
}

var file_tanlive_iam_service_proto_msgTypes = make([]protoimpl.MessageInfo, 68)
var file_tanlive_iam_service_proto_goTypes = []interface{}{
	(*ReqGetUsersPage)(nil),                    // 0: tanlive.iam.ReqGetUsersPage
	(*RspGetUsersPage)(nil),                    // 1: tanlive.iam.RspGetUsersPage
	(*ReqGetUsersByKey)(nil),                   // 2: tanlive.iam.ReqGetUsersByKey
	(*RspGetUsersByKey)(nil),                   // 3: tanlive.iam.RspGetUsersByKey
	(*ReqGetUserByUnionID)(nil),                // 4: tanlive.iam.ReqGetUserByUnionID
	(*RspGetUserByUnionID)(nil),                // 5: tanlive.iam.RspGetUserByUnionID
	(*ReqCheckApiPermission)(nil),              // 6: tanlive.iam.ReqCheckApiPermission
	(*RspCheckApiPermission)(nil),              // 7: tanlive.iam.RspCheckApiPermission
	(*ReqValidateLoginCredentials)(nil),        // 8: tanlive.iam.ReqValidateLoginCredentials
	(*RspValidateLoginCredentials)(nil),        // 9: tanlive.iam.RspValidateLoginCredentials
	(*ReqBindUserThirdIndex)(nil),              // 10: tanlive.iam.ReqBindUserThirdIndex
	(*ReqUpdateUserInfo)(nil),                  // 11: tanlive.iam.ReqUpdateUserInfo
	(*ReqGetIpRegion)(nil),                     // 12: tanlive.iam.ReqGetIpRegion
	(*RspGetIpRegion)(nil),                     // 13: tanlive.iam.RspGetIpRegion
	(*ReqUpdateUserWhenTeamCreated)(nil),       // 14: tanlive.iam.ReqUpdateUserWhenTeamCreated
	(*ReqCreateOneTimePassword)(nil),           // 15: tanlive.iam.ReqCreateOneTimePassword
	(*ReqCheckUserHavePhone)(nil),              // 16: tanlive.iam.ReqCheckUserHavePhone
	(*RspCheckUserHavePhone)(nil),              // 17: tanlive.iam.RspCheckUserHavePhone
	(*RspCreateOneTimePassword)(nil),           // 18: tanlive.iam.RspCreateOneTimePassword
	(*ReqValidateOneTimePassword)(nil),         // 19: tanlive.iam.ReqValidateOneTimePassword
	(*ReqCreateUserTfa)(nil),                   // 20: tanlive.iam.ReqCreateUserTfa
	(*RspCreateUserTfa)(nil),                   // 21: tanlive.iam.RspCreateUserTfa
	(*ReqGetUserTfa)(nil),                      // 22: tanlive.iam.ReqGetUserTfa
	(*RspGetUserTfa)(nil),                      // 23: tanlive.iam.RspGetUserTfa
	(*ReqCreateWeixinTfaQrcode)(nil),           // 24: tanlive.iam.ReqCreateWeixinTfaQrcode
	(*RspCreateWeixinTfaQrcode)(nil),           // 25: tanlive.iam.RspCreateWeixinTfaQrcode
	(*ReqCreateWeixinTfa)(nil),                 // 26: tanlive.iam.ReqCreateWeixinTfa
	(*ReqGetCreateWeixinTfaState)(nil),         // 27: tanlive.iam.ReqGetCreateWeixinTfaState
	(*RspGetCreateWeixinTfaState)(nil),         // 28: tanlive.iam.RspGetCreateWeixinTfaState
	(*ReqValidateTwoFactorAuth)(nil),           // 29: tanlive.iam.ReqValidateTwoFactorAuth
	(*ReqModifyUserUsername)(nil),              // 30: tanlive.iam.ReqModifyUserUsername
	(*ReqModifyUserPassword)(nil),              // 31: tanlive.iam.ReqModifyUserPassword
	(*ReqModifyUserPhone)(nil),                 // 32: tanlive.iam.ReqModifyUserPhone
	(*ReqModifyUserEmail)(nil),                 // 33: tanlive.iam.ReqModifyUserEmail
	(*RspModifyUserEmail)(nil),                 // 34: tanlive.iam.RspModifyUserEmail
	(*ReqActiveUserEmail)(nil),                 // 35: tanlive.iam.ReqActiveUserEmail
	(*ReqCreateBindUserWeixinQrcode)(nil),      // 36: tanlive.iam.ReqCreateBindUserWeixinQrcode
	(*RspCreateBindUserWeixinQrcode)(nil),      // 37: tanlive.iam.RspCreateBindUserWeixinQrcode
	(*ReqGetBindUserWeixinState)(nil),          // 38: tanlive.iam.ReqGetBindUserWeixinState
	(*RspGetBindUserWeixinState)(nil),          // 39: tanlive.iam.RspGetBindUserWeixinState
	(*ReqBindUserWeixin)(nil),                  // 40: tanlive.iam.ReqBindUserWeixin
	(*ReqBindUserWeixinByOauth2)(nil),          // 41: tanlive.iam.ReqBindUserWeixinByOauth2
	(*ReqUnbindUserWeixin)(nil),                // 42: tanlive.iam.ReqUnbindUserWeixin
	(*ReqServeWeixinOfficialAccount)(nil),      // 43: tanlive.iam.ReqServeWeixinOfficialAccount
	(*RspServeWeixinOfficialAccount)(nil),      // 44: tanlive.iam.RspServeWeixinOfficialAccount
	(*ReqGetGoogleAuthUrl)(nil),                // 45: tanlive.iam.ReqGetGoogleAuthUrl
	(*RspGetGoogleAuthUrl)(nil),                // 46: tanlive.iam.RspGetGoogleAuthUrl
	(*ReqBindUserGoogle)(nil),                  // 47: tanlive.iam.ReqBindUserGoogle
	(*ReqUnbindUserGoogle)(nil),                // 48: tanlive.iam.ReqUnbindUserGoogle
	(*ReqMakeUniqueUsername)(nil),              // 49: tanlive.iam.ReqMakeUniqueUsername
	(*RspMakeUniqueUsername)(nil),              // 50: tanlive.iam.RspMakeUniqueUsername
	(*ReqDisableUser)(nil),                     // 51: tanlive.iam.ReqDisableUser
	(*RspDisableUser)(nil),                     // 52: tanlive.iam.RspDisableUser
	(*ReqEnableUser)(nil),                      // 53: tanlive.iam.ReqEnableUser
	(*RspEnableUser)(nil),                      // 54: tanlive.iam.RspEnableUser
	(*ReqGetUserIds)(nil),                      // 55: tanlive.iam.ReqGetUserIds
	(*RspGetUserIds)(nil),                      // 56: tanlive.iam.RspGetUserIds
	(*ReqGetOauthClient)(nil),                  // 57: tanlive.iam.ReqGetOauthClient
	(*RspGetOauthClient)(nil),                  // 58: tanlive.iam.RspGetOauthClient
	(*ReqGetUsersPage_Filter)(nil),             // 59: tanlive.iam.ReqGetUsersPage.Filter
	(*ReqCreateUserTfa_PhonePara)(nil),         // 60: tanlive.iam.ReqCreateUserTfa.PhonePara
	(*ReqCreateUserTfa_PasswordPara)(nil),      // 61: tanlive.iam.ReqCreateUserTfa.PasswordPara
	(*ReqCreateUserTfa_EmailPara)(nil),         // 62: tanlive.iam.ReqCreateUserTfa.EmailPara
	(*ReqCreateUserTfa_WeixinBrowserPara)(nil), // 63: tanlive.iam.ReqCreateUserTfa.WeixinBrowserPara
	(*ReqCreateUserTfa_GooglePara)(nil),        // 64: tanlive.iam.ReqCreateUserTfa.GooglePara
	nil,                                        // 65: tanlive.iam.ReqServeWeixinOfficialAccount.QueryEntry
	(*RspDisableUser_Result)(nil),              // 66: tanlive.iam.RspDisableUser.Result
	(*RspEnableUser_Result)(nil),               // 67: tanlive.iam.RspEnableUser.Result
	(base.Region)(0),                           // 68: tanlive.base.Region
	(*base.Paginator)(nil),                     // 69: tanlive.base.Paginator
	(*UserInfo)(nil),                           // 70: tanlive.iam.UserInfo
	(UserIndexType)(0),                         // 71: tanlive.iam.UserIndexType
	(*ThirdUserInfo)(nil),                      // 72: tanlive.iam.ThirdUserInfo
	(*fieldmaskpb.FieldMask)(nil),              // 73: google.protobuf.FieldMask
	(*IpRegion)(nil),                           // 74: tanlive.iam.IpRegion
	(OtpScene)(0),                              // 75: tanlive.iam.OtpScene
	(*OtpReceiver)(nil),                        // 76: tanlive.iam.OtpReceiver
	(*base.RateLimitResult)(nil),               // 77: tanlive.base.RateLimitResult
	(*notify.DeliveryResult)(nil),              // 78: tanlive.notify.DeliveryResult
	(*TfaToken)(nil),                           // 79: tanlive.iam.TfaToken
	(*WeixinQrcode)(nil),                       // 80: tanlive.iam.WeixinQrcode
	(CreateWeixinTfaState)(0),                  // 81: tanlive.iam.CreateWeixinTfaState
	(BindWeixinState)(0),                       // 82: tanlive.iam.BindWeixinState
	(GoogleAuthScene)(0),                       // 83: tanlive.iam.GoogleAuthScene
	(*OauthClient)(nil),                        // 84: tanlive.iam.OauthClient
	(*emptypb.Empty)(nil),                      // 85: google.protobuf.Empty
}
var file_tanlive_iam_service_proto_depIdxs = []int32{
	68, // 0: tanlive.iam.ReqGetUsersPage.region:type_name -> tanlive.base.Region
	59, // 1: tanlive.iam.ReqGetUsersPage.filter:type_name -> tanlive.iam.ReqGetUsersPage.Filter
	69, // 2: tanlive.iam.ReqGetUsersPage.page:type_name -> tanlive.base.Paginator
	70, // 3: tanlive.iam.RspGetUsersPage.users:type_name -> tanlive.iam.UserInfo
	70, // 4: tanlive.iam.RspGetUsersByKey.user_set:type_name -> tanlive.iam.UserInfo
	70, // 5: tanlive.iam.RspGetUserByUnionID.user_set:type_name -> tanlive.iam.UserInfo
	71, // 6: tanlive.iam.ReqValidateLoginCredentials.identifier_type:type_name -> tanlive.iam.UserIndexType
	70, // 7: tanlive.iam.RspValidateLoginCredentials.user:type_name -> tanlive.iam.UserInfo
	72, // 8: tanlive.iam.ReqBindUserThirdIndex.third_user:type_name -> tanlive.iam.ThirdUserInfo
	70, // 9: tanlive.iam.ReqUpdateUserInfo.user:type_name -> tanlive.iam.UserInfo
	73, // 10: tanlive.iam.ReqUpdateUserInfo.mask:type_name -> google.protobuf.FieldMask
	74, // 11: tanlive.iam.RspGetIpRegion.ip_region:type_name -> tanlive.iam.IpRegion
	75, // 12: tanlive.iam.ReqCreateOneTimePassword.scene:type_name -> tanlive.iam.OtpScene
	76, // 13: tanlive.iam.ReqCreateOneTimePassword.receiver:type_name -> tanlive.iam.OtpReceiver
	77, // 14: tanlive.iam.RspCreateOneTimePassword.rate_limit_result:type_name -> tanlive.base.RateLimitResult
	78, // 15: tanlive.iam.RspCreateOneTimePassword.result:type_name -> tanlive.notify.DeliveryResult
	75, // 16: tanlive.iam.ReqValidateOneTimePassword.scene:type_name -> tanlive.iam.OtpScene
	76, // 17: tanlive.iam.ReqValidateOneTimePassword.receiver:type_name -> tanlive.iam.OtpReceiver
	60, // 18: tanlive.iam.ReqCreateUserTfa.phone_tfa:type_name -> tanlive.iam.ReqCreateUserTfa.PhonePara
	61, // 19: tanlive.iam.ReqCreateUserTfa.password_tfa:type_name -> tanlive.iam.ReqCreateUserTfa.PasswordPara
	62, // 20: tanlive.iam.ReqCreateUserTfa.email_tfa:type_name -> tanlive.iam.ReqCreateUserTfa.EmailPara
	63, // 21: tanlive.iam.ReqCreateUserTfa.weixin_browser_tfa:type_name -> tanlive.iam.ReqCreateUserTfa.WeixinBrowserPara
	64, // 22: tanlive.iam.ReqCreateUserTfa.google_tfa:type_name -> tanlive.iam.ReqCreateUserTfa.GooglePara
	79, // 23: tanlive.iam.RspCreateUserTfa.tfa_token:type_name -> tanlive.iam.TfaToken
	79, // 24: tanlive.iam.RspGetUserTfa.tfa_token:type_name -> tanlive.iam.TfaToken
	80, // 25: tanlive.iam.RspCreateWeixinTfaQrcode.qrcode:type_name -> tanlive.iam.WeixinQrcode
	81, // 26: tanlive.iam.RspGetCreateWeixinTfaState.state:type_name -> tanlive.iam.CreateWeixinTfaState
	79, // 27: tanlive.iam.RspGetCreateWeixinTfaState.tfa_token:type_name -> tanlive.iam.TfaToken
	78, // 28: tanlive.iam.RspModifyUserEmail.result:type_name -> tanlive.notify.DeliveryResult
	80, // 29: tanlive.iam.RspCreateBindUserWeixinQrcode.qrcode:type_name -> tanlive.iam.WeixinQrcode
	82, // 30: tanlive.iam.RspGetBindUserWeixinState.state:type_name -> tanlive.iam.BindWeixinState
	65, // 31: tanlive.iam.ReqServeWeixinOfficialAccount.query:type_name -> tanlive.iam.ReqServeWeixinOfficialAccount.QueryEntry
	83, // 32: tanlive.iam.ReqGetGoogleAuthUrl.scene:type_name -> tanlive.iam.GoogleAuthScene
	66, // 33: tanlive.iam.RspDisableUser.results:type_name -> tanlive.iam.RspDisableUser.Result
	67, // 34: tanlive.iam.RspEnableUser.results:type_name -> tanlive.iam.RspEnableUser.Result
	68, // 35: tanlive.iam.ReqGetUserIds.region:type_name -> tanlive.base.Region
	84, // 36: tanlive.iam.RspGetOauthClient.clients:type_name -> tanlive.iam.OauthClient
	0,  // 37: tanlive.iam.IamService.GetUsersPage:input_type -> tanlive.iam.ReqGetUsersPage
	2,  // 38: tanlive.iam.IamService.GetUsersByKey:input_type -> tanlive.iam.ReqGetUsersByKey
	4,  // 39: tanlive.iam.IamService.GetUserByUnionID:input_type -> tanlive.iam.ReqGetUserByUnionID
	6,  // 40: tanlive.iam.IamService.CheckApiPermission:input_type -> tanlive.iam.ReqCheckApiPermission
	8,  // 41: tanlive.iam.IamService.ValidateLoginCredentials:input_type -> tanlive.iam.ReqValidateLoginCredentials
	10, // 42: tanlive.iam.IamService.BindUserThirdIndex:input_type -> tanlive.iam.ReqBindUserThirdIndex
	11, // 43: tanlive.iam.IamService.UpdateUserInfo:input_type -> tanlive.iam.ReqUpdateUserInfo
	12, // 44: tanlive.iam.IamService.GetIpRegion:input_type -> tanlive.iam.ReqGetIpRegion
	14, // 45: tanlive.iam.IamService.UpdateUserWhenTeamCreated:input_type -> tanlive.iam.ReqUpdateUserWhenTeamCreated
	16, // 46: tanlive.iam.IamService.CheckUserHavePhone:input_type -> tanlive.iam.ReqCheckUserHavePhone
	15, // 47: tanlive.iam.IamService.CreateOneTimePassword:input_type -> tanlive.iam.ReqCreateOneTimePassword
	19, // 48: tanlive.iam.IamService.ValidateOneTimePassword:input_type -> tanlive.iam.ReqValidateOneTimePassword
	20, // 49: tanlive.iam.IamService.CreateUserTfa:input_type -> tanlive.iam.ReqCreateUserTfa
	22, // 50: tanlive.iam.IamService.GetUserTfa:input_type -> tanlive.iam.ReqGetUserTfa
	24, // 51: tanlive.iam.IamService.CreateWeixinTfaQrcode:input_type -> tanlive.iam.ReqCreateWeixinTfaQrcode
	26, // 52: tanlive.iam.IamService.CreateWeixinTfa:input_type -> tanlive.iam.ReqCreateWeixinTfa
	27, // 53: tanlive.iam.IamService.GetCreateWeixinTfaState:input_type -> tanlive.iam.ReqGetCreateWeixinTfaState
	29, // 54: tanlive.iam.IamService.ValidateTwoFactorAuth:input_type -> tanlive.iam.ReqValidateTwoFactorAuth
	30, // 55: tanlive.iam.IamService.ModifyUserUsername:input_type -> tanlive.iam.ReqModifyUserUsername
	31, // 56: tanlive.iam.IamService.ModifyUserPassword:input_type -> tanlive.iam.ReqModifyUserPassword
	32, // 57: tanlive.iam.IamService.ModifyUserPhone:input_type -> tanlive.iam.ReqModifyUserPhone
	33, // 58: tanlive.iam.IamService.ModifyUserEmail:input_type -> tanlive.iam.ReqModifyUserEmail
	35, // 59: tanlive.iam.IamService.ActiveUserEmail:input_type -> tanlive.iam.ReqActiveUserEmail
	36, // 60: tanlive.iam.IamService.CreateBindUserWeixinQrcode:input_type -> tanlive.iam.ReqCreateBindUserWeixinQrcode
	38, // 61: tanlive.iam.IamService.GetBindUserWeixinState:input_type -> tanlive.iam.ReqGetBindUserWeixinState
	40, // 62: tanlive.iam.IamService.BindUserWeixin:input_type -> tanlive.iam.ReqBindUserWeixin
	41, // 63: tanlive.iam.IamService.BindUserWeixinByOauth2:input_type -> tanlive.iam.ReqBindUserWeixinByOauth2
	42, // 64: tanlive.iam.IamService.UnbindUserWeixin:input_type -> tanlive.iam.ReqUnbindUserWeixin
	43, // 65: tanlive.iam.IamService.ServeWeixinOfficialAccount:input_type -> tanlive.iam.ReqServeWeixinOfficialAccount
	45, // 66: tanlive.iam.IamService.GetGoogleAuthUrl:input_type -> tanlive.iam.ReqGetGoogleAuthUrl
	47, // 67: tanlive.iam.IamService.BindUserGoogle:input_type -> tanlive.iam.ReqBindUserGoogle
	48, // 68: tanlive.iam.IamService.UnbindUserGoogle:input_type -> tanlive.iam.ReqUnbindUserGoogle
	49, // 69: tanlive.iam.IamService.MakeUniqueUsername:input_type -> tanlive.iam.ReqMakeUniqueUsername
	51, // 70: tanlive.iam.IamService.DisableUser:input_type -> tanlive.iam.ReqDisableUser
	53, // 71: tanlive.iam.IamService.EnableUser:input_type -> tanlive.iam.ReqEnableUser
	55, // 72: tanlive.iam.IamService.GetUserIds:input_type -> tanlive.iam.ReqGetUserIds
	57, // 73: tanlive.iam.IamService.GetOauthClient:input_type -> tanlive.iam.ReqGetOauthClient
	1,  // 74: tanlive.iam.IamService.GetUsersPage:output_type -> tanlive.iam.RspGetUsersPage
	3,  // 75: tanlive.iam.IamService.GetUsersByKey:output_type -> tanlive.iam.RspGetUsersByKey
	5,  // 76: tanlive.iam.IamService.GetUserByUnionID:output_type -> tanlive.iam.RspGetUserByUnionID
	7,  // 77: tanlive.iam.IamService.CheckApiPermission:output_type -> tanlive.iam.RspCheckApiPermission
	9,  // 78: tanlive.iam.IamService.ValidateLoginCredentials:output_type -> tanlive.iam.RspValidateLoginCredentials
	85, // 79: tanlive.iam.IamService.BindUserThirdIndex:output_type -> google.protobuf.Empty
	85, // 80: tanlive.iam.IamService.UpdateUserInfo:output_type -> google.protobuf.Empty
	13, // 81: tanlive.iam.IamService.GetIpRegion:output_type -> tanlive.iam.RspGetIpRegion
	85, // 82: tanlive.iam.IamService.UpdateUserWhenTeamCreated:output_type -> google.protobuf.Empty
	17, // 83: tanlive.iam.IamService.CheckUserHavePhone:output_type -> tanlive.iam.RspCheckUserHavePhone
	18, // 84: tanlive.iam.IamService.CreateOneTimePassword:output_type -> tanlive.iam.RspCreateOneTimePassword
	85, // 85: tanlive.iam.IamService.ValidateOneTimePassword:output_type -> google.protobuf.Empty
	21, // 86: tanlive.iam.IamService.CreateUserTfa:output_type -> tanlive.iam.RspCreateUserTfa
	23, // 87: tanlive.iam.IamService.GetUserTfa:output_type -> tanlive.iam.RspGetUserTfa
	25, // 88: tanlive.iam.IamService.CreateWeixinTfaQrcode:output_type -> tanlive.iam.RspCreateWeixinTfaQrcode
	85, // 89: tanlive.iam.IamService.CreateWeixinTfa:output_type -> google.protobuf.Empty
	28, // 90: tanlive.iam.IamService.GetCreateWeixinTfaState:output_type -> tanlive.iam.RspGetCreateWeixinTfaState
	85, // 91: tanlive.iam.IamService.ValidateTwoFactorAuth:output_type -> google.protobuf.Empty
	85, // 92: tanlive.iam.IamService.ModifyUserUsername:output_type -> google.protobuf.Empty
	85, // 93: tanlive.iam.IamService.ModifyUserPassword:output_type -> google.protobuf.Empty
	85, // 94: tanlive.iam.IamService.ModifyUserPhone:output_type -> google.protobuf.Empty
	34, // 95: tanlive.iam.IamService.ModifyUserEmail:output_type -> tanlive.iam.RspModifyUserEmail
	85, // 96: tanlive.iam.IamService.ActiveUserEmail:output_type -> google.protobuf.Empty
	37, // 97: tanlive.iam.IamService.CreateBindUserWeixinQrcode:output_type -> tanlive.iam.RspCreateBindUserWeixinQrcode
	39, // 98: tanlive.iam.IamService.GetBindUserWeixinState:output_type -> tanlive.iam.RspGetBindUserWeixinState
	85, // 99: tanlive.iam.IamService.BindUserWeixin:output_type -> google.protobuf.Empty
	85, // 100: tanlive.iam.IamService.BindUserWeixinByOauth2:output_type -> google.protobuf.Empty
	85, // 101: tanlive.iam.IamService.UnbindUserWeixin:output_type -> google.protobuf.Empty
	44, // 102: tanlive.iam.IamService.ServeWeixinOfficialAccount:output_type -> tanlive.iam.RspServeWeixinOfficialAccount
	46, // 103: tanlive.iam.IamService.GetGoogleAuthUrl:output_type -> tanlive.iam.RspGetGoogleAuthUrl
	85, // 104: tanlive.iam.IamService.BindUserGoogle:output_type -> google.protobuf.Empty
	85, // 105: tanlive.iam.IamService.UnbindUserGoogle:output_type -> google.protobuf.Empty
	50, // 106: tanlive.iam.IamService.MakeUniqueUsername:output_type -> tanlive.iam.RspMakeUniqueUsername
	52, // 107: tanlive.iam.IamService.DisableUser:output_type -> tanlive.iam.RspDisableUser
	54, // 108: tanlive.iam.IamService.EnableUser:output_type -> tanlive.iam.RspEnableUser
	56, // 109: tanlive.iam.IamService.GetUserIds:output_type -> tanlive.iam.RspGetUserIds
	58, // 110: tanlive.iam.IamService.GetOauthClient:output_type -> tanlive.iam.RspGetOauthClient
	74, // [74:111] is the sub-list for method output_type
	37, // [37:74] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_tanlive_iam_service_proto_init() }
func file_tanlive_iam_service_proto_init() {
	if File_tanlive_iam_service_proto != nil {
		return
	}
	file_tanlive_iam_iam_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_iam_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetUsersPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetUsersPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetUsersByKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetUsersByKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetUserByUnionID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetUserByUnionID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCheckApiPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCheckApiPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqValidateLoginCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspValidateLoginCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBindUserThirdIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetIpRegion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetIpRegion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateUserWhenTeamCreated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateOneTimePassword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCheckUserHavePhone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCheckUserHavePhone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateOneTimePassword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqValidateOneTimePassword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateUserTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateUserTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetUserTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetUserTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateWeixinTfaQrcode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateWeixinTfaQrcode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateWeixinTfa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetCreateWeixinTfaState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetCreateWeixinTfaState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqValidateTwoFactorAuth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyUserUsername); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyUserPassword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyUserPhone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyUserEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspModifyUserEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqActiveUserEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateBindUserWeixinQrcode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateBindUserWeixinQrcode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetBindUserWeixinState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetBindUserWeixinState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBindUserWeixin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBindUserWeixinByOauth2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUnbindUserWeixin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqServeWeixinOfficialAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspServeWeixinOfficialAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetGoogleAuthUrl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetGoogleAuthUrl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBindUserGoogle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUnbindUserGoogle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqMakeUniqueUsername); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspMakeUniqueUsername); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDisableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDisableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqEnableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspEnableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetUserIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetUserIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetOauthClient); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetOauthClient); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetUsersPage_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateUserTfa_PhonePara); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateUserTfa_PasswordPara); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateUserTfa_EmailPara); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateUserTfa_WeixinBrowserPara); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateUserTfa_GooglePara); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDisableUser_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspEnableUser_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tanlive_iam_service_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*ReqCreateUserTfa_PhoneTfa)(nil),
		(*ReqCreateUserTfa_PasswordTfa)(nil),
		(*ReqCreateUserTfa_EmailTfa)(nil),
		(*ReqCreateUserTfa_WeixinBrowserTfa)(nil),
		(*ReqCreateUserTfa_GoogleTfa)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_iam_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   68,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_iam_service_proto_goTypes,
		DependencyIndexes: file_tanlive_iam_service_proto_depIdxs,
		MessageInfos:      file_tanlive_iam_service_proto_msgTypes,
	}.Build()
	File_tanlive_iam_service_proto = out.File
	file_tanlive_iam_service_proto_rawDesc = nil
	file_tanlive_iam_service_proto_goTypes = nil
	file_tanlive_iam_service_proto_depIdxs = nil
}
