// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package iam

import (
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	notify "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify"
	proto "google.golang.org/protobuf/proto"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"WithinUserId": "omitempty,dive,required",
	}, &ReqGetUsersPage_Filter{})
}

func (x *ReqGetUsersPage_Filter) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqGetUsersByKey{})
}

func (x *ReqGetUsersByKey) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
		"Path":   "required",
	}, &ReqCheckApiPermission{})
}

func (x *ReqCheckApiPermission) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Identifier":     "required",
		"IdentifierType": "required",
	}, &ReqValidateLoginCredentials{})
}

func (x *ReqValidateLoginCredentials) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":    "required",
		"ThirdUser": "required",
	}, &ReqBindUserThirdIndex{})
}

func (x *ReqBindUserThirdIndex) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"User": "required",
		"Mask": "required",
	}, &ReqUpdateUserInfo{})
}

func (x *ReqUpdateUserInfo) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Ip": "required",
	}, &ReqGetIpRegion{})
}

func (x *ReqGetIpRegion) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Scene":    "required",
		"Receiver": "required",
		"Lang":     "required",
	}, &ReqCreateOneTimePassword{})
}

func (x *ReqCreateOneTimePassword) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
	}, &ReqCheckUserHavePhone{})
}

func (x *ReqCheckUserHavePhone) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Scene":    "required",
		"Receiver": "required",
		"Otp":      "required",
	}, &ReqValidateOneTimePassword{})
}

func (x *ReqValidateOneTimePassword) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":           "required",
		"PhoneTfa":         "required",
		"PasswordTfa":      "required",
		"EmailTfa":         "required",
		"WeixinBrowserTfa": "required",
		"GoogleTfa":        "required",
	}, &ReqCreateUserTfa{})
}

func (x *ReqCreateUserTfa) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Otp": "required,len=6,number",
	}, &ReqCreateUserTfa_PhonePara{})
}

func (x *ReqCreateUserTfa_PhonePara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Password": "required",
	}, &ReqCreateUserTfa_PasswordPara{})
}

func (x *ReqCreateUserTfa_PasswordPara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Otp": "required,len=6,number",
	}, &ReqCreateUserTfa_EmailPara{})
}

func (x *ReqCreateUserTfa_EmailPara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Code": "required",
	}, &ReqCreateUserTfa_WeixinBrowserPara{})
}

func (x *ReqCreateUserTfa_WeixinBrowserPara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Code": "required",
	}, &ReqCreateUserTfa_GooglePara{})
}

func (x *ReqCreateUserTfa_GooglePara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
	}, &ReqGetUserTfa{})
}

func (x *ReqGetUserTfa) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
		"Lang":   "required",
	}, &ReqCreateWeixinTfaQrcode{})
}

func (x *ReqCreateWeixinTfaQrcode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"SceneStr": "required",
	}, &ReqCreateWeixinTfa{})
}

func (x *ReqCreateWeixinTfa) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"SceneStr": "required",
	}, &ReqGetCreateWeixinTfaState{})
}

func (x *ReqGetCreateWeixinTfaState) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"TfaToken": "required",
	}, &ReqValidateTwoFactorAuth{})
}

func (x *ReqValidateTwoFactorAuth) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"Username": "required,username",
		"TfaToken": "required",
	}, &ReqModifyUserUsername{})
}

func (x *ReqModifyUserUsername) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"Password": "required,password",
		"TfaToken": "required",
	}, &ReqModifyUserPassword{})
}

func (x *ReqModifyUserPassword) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"Phone":    "required,phone",
		"TfaToken": "required",
		"Otp":      "required",
	}, &ReqModifyUserPhone{})
}

func (x *ReqModifyUserPhone) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"Email":    "required,email",
		"TfaToken": "required",
		"Lang":     "required",
	}, &ReqModifyUserEmail{})
}

func (x *ReqModifyUserEmail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
		"Value":  "required",
	}, &ReqActiveUserEmail{})
}

func (x *ReqActiveUserEmail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"TfaToken": "required",
		"Lang":     "required",
	}, &ReqCreateBindUserWeixinQrcode{})
}

func (x *ReqCreateBindUserWeixinQrcode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"SceneStr": "required",
	}, &ReqGetBindUserWeixinState{})
}

func (x *ReqGetBindUserWeixinState) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"OpenId":   "required",
		"SceneStr": "required",
	}, &ReqBindUserWeixin{})
}

func (x *ReqBindUserWeixin) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"Code":     "required",
		"TfaToken": "required",
	}, &ReqBindUserWeixinByOauth2{})
}

func (x *ReqBindUserWeixinByOauth2) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"TfaToken": "required",
	}, &ReqUnbindUserWeixin{})
}

func (x *ReqUnbindUserWeixin) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Scene": "required",
		"State": "required",
	}, &ReqGetGoogleAuthUrl{})
}

func (x *ReqGetGoogleAuthUrl) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"Code":     "required",
		"TfaToken": "required",
	}, &ReqBindUserGoogle{})
}

func (x *ReqBindUserGoogle) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"TfaToken": "required",
	}, &ReqUnbindUserGoogle{})
}

func (x *ReqUnbindUserGoogle) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Username": "required",
	}, &ReqMakeUniqueUsername{})
}

func (x *ReqMakeUniqueUsername) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required,dive,required",
	}, &ReqDisableUser{})
}

func (x *ReqDisableUser) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required,dive,required",
	}, &ReqEnableUser{})
}

func (x *ReqEnableUser) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Username": "required",
		"Region":   "required",
	}, &ReqGetUserIds{})
}

func (x *ReqGetUserIds) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ClientId": "required,dive,required",
	}, &ReqGetOauthClient{})
}

func (x *ReqGetOauthClient) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqGetUsersPage) MaskInLog() any {
	if x == nil {
		return (*ReqGetUsersPage)(nil)
	}

	y := proto.Clone(x).(*ReqGetUsersPage)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetUsersPage_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqGetUsersPage) MaskInRpc() any {
	if x == nil {
		return (*ReqGetUsersPage)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetUsersPage_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqGetUsersPage) MaskInBff() any {
	if x == nil {
		return (*ReqGetUsersPage)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetUsersPage_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspGetUsersPage) MaskInLog() any {
	if x == nil {
		return (*RspGetUsersPage)(nil)
	}

	y := proto.Clone(x).(*RspGetUsersPage)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*UserInfo)
		}
	}

	return y
}

func (x *RspGetUsersPage) MaskInRpc() any {
	if x == nil {
		return (*RspGetUsersPage)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*UserInfo)
		}
	}

	return y
}

func (x *RspGetUsersPage) MaskInBff() any {
	if x == nil {
		return (*RspGetUsersPage)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*UserInfo)
		}
	}

	return y
}

func (x *RspGetUsersByKey) MaskInLog() any {
	if x == nil {
		return (*RspGetUsersByKey)(nil)
	}

	y := proto.Clone(x).(*RspGetUsersByKey)
	for k, v := range y.UserSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserSet[k] = vv.MaskInLog().(*UserInfo)
		}
	}

	return y
}

func (x *RspGetUsersByKey) MaskInRpc() any {
	if x == nil {
		return (*RspGetUsersByKey)(nil)
	}

	y := x
	for k, v := range y.UserSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserSet[k] = vv.MaskInRpc().(*UserInfo)
		}
	}

	return y
}

func (x *RspGetUsersByKey) MaskInBff() any {
	if x == nil {
		return (*RspGetUsersByKey)(nil)
	}

	y := x
	for k, v := range y.UserSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserSet[k] = vv.MaskInBff().(*UserInfo)
		}
	}

	return y
}

func (x *RspGetUserByUnionID) MaskInLog() any {
	if x == nil {
		return (*RspGetUserByUnionID)(nil)
	}

	y := proto.Clone(x).(*RspGetUserByUnionID)
	if v, ok := any(y.UserSet).(interface{ MaskInLog() any }); ok {
		y.UserSet = v.MaskInLog().(*UserInfo)
	}

	return y
}

func (x *RspGetUserByUnionID) MaskInRpc() any {
	if x == nil {
		return (*RspGetUserByUnionID)(nil)
	}

	y := x
	if v, ok := any(y.UserSet).(interface{ MaskInRpc() any }); ok {
		y.UserSet = v.MaskInRpc().(*UserInfo)
	}

	return y
}

func (x *RspGetUserByUnionID) MaskInBff() any {
	if x == nil {
		return (*RspGetUserByUnionID)(nil)
	}

	y := x
	if v, ok := any(y.UserSet).(interface{ MaskInBff() any }); ok {
		y.UserSet = v.MaskInBff().(*UserInfo)
	}

	return y
}

func (x *ReqValidateLoginCredentials) MaskInLog() any {
	if x == nil {
		return (*ReqValidateLoginCredentials)(nil)
	}

	y := proto.Clone(x).(*ReqValidateLoginCredentials)
	y.Identifier = mask.Mask(y.Identifier, "name")
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqValidateLoginCredentials) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateLoginCredentials)(nil)
	}

	y := x
	y.Identifier = mask.Mask(y.Identifier, "name")
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqValidateLoginCredentials) MaskInBff() any {
	if x == nil {
		return (*ReqValidateLoginCredentials)(nil)
	}

	y := x
	y.Identifier = mask.Mask(y.Identifier, "name")
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *RspValidateLoginCredentials) MaskInLog() any {
	if x == nil {
		return (*RspValidateLoginCredentials)(nil)
	}

	y := proto.Clone(x).(*RspValidateLoginCredentials)
	if v, ok := any(y.User).(interface{ MaskInLog() any }); ok {
		y.User = v.MaskInLog().(*UserInfo)
	}

	return y
}

func (x *RspValidateLoginCredentials) MaskInRpc() any {
	if x == nil {
		return (*RspValidateLoginCredentials)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInRpc() any }); ok {
		y.User = v.MaskInRpc().(*UserInfo)
	}

	return y
}

func (x *RspValidateLoginCredentials) MaskInBff() any {
	if x == nil {
		return (*RspValidateLoginCredentials)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInBff() any }); ok {
		y.User = v.MaskInBff().(*UserInfo)
	}

	return y
}

func (x *ReqBindUserThirdIndex) MaskInLog() any {
	if x == nil {
		return (*ReqBindUserThirdIndex)(nil)
	}

	y := proto.Clone(x).(*ReqBindUserThirdIndex)
	if v, ok := any(y.ThirdUser).(interface{ MaskInLog() any }); ok {
		y.ThirdUser = v.MaskInLog().(*ThirdUserInfo)
	}

	return y
}

func (x *ReqBindUserThirdIndex) MaskInRpc() any {
	if x == nil {
		return (*ReqBindUserThirdIndex)(nil)
	}

	y := x
	if v, ok := any(y.ThirdUser).(interface{ MaskInRpc() any }); ok {
		y.ThirdUser = v.MaskInRpc().(*ThirdUserInfo)
	}

	return y
}

func (x *ReqBindUserThirdIndex) MaskInBff() any {
	if x == nil {
		return (*ReqBindUserThirdIndex)(nil)
	}

	y := x
	if v, ok := any(y.ThirdUser).(interface{ MaskInBff() any }); ok {
		y.ThirdUser = v.MaskInBff().(*ThirdUserInfo)
	}

	return y
}

func (x *ReqUpdateUserInfo) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateUserInfo)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateUserInfo)
	if v, ok := any(y.User).(interface{ MaskInLog() any }); ok {
		y.User = v.MaskInLog().(*UserInfo)
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateUserInfo) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateUserInfo)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInRpc() any }); ok {
		y.User = v.MaskInRpc().(*UserInfo)
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateUserInfo) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateUserInfo)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInBff() any }); ok {
		y.User = v.MaskInBff().(*UserInfo)
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqGetIpRegion) MaskInLog() any {
	if x == nil {
		return (*ReqGetIpRegion)(nil)
	}

	y := proto.Clone(x).(*ReqGetIpRegion)
	y.Ip = mask.Mask(y.Ip, "ipv4")

	return y
}

func (x *ReqGetIpRegion) MaskInRpc() any {
	if x == nil {
		return (*ReqGetIpRegion)(nil)
	}

	y := x
	y.Ip = mask.Mask(y.Ip, "ipv4")

	return y
}

func (x *ReqGetIpRegion) MaskInBff() any {
	if x == nil {
		return (*ReqGetIpRegion)(nil)
	}

	y := x
	y.Ip = mask.Mask(y.Ip, "ipv4")

	return y
}

func (x *RspGetIpRegion) MaskInLog() any {
	if x == nil {
		return (*RspGetIpRegion)(nil)
	}

	y := proto.Clone(x).(*RspGetIpRegion)
	if v, ok := any(y.IpRegion).(interface{ MaskInLog() any }); ok {
		y.IpRegion = v.MaskInLog().(*IpRegion)
	}

	return y
}

func (x *RspGetIpRegion) MaskInRpc() any {
	if x == nil {
		return (*RspGetIpRegion)(nil)
	}

	y := x
	if v, ok := any(y.IpRegion).(interface{ MaskInRpc() any }); ok {
		y.IpRegion = v.MaskInRpc().(*IpRegion)
	}

	return y
}

func (x *RspGetIpRegion) MaskInBff() any {
	if x == nil {
		return (*RspGetIpRegion)(nil)
	}

	y := x
	if v, ok := any(y.IpRegion).(interface{ MaskInBff() any }); ok {
		y.IpRegion = v.MaskInBff().(*IpRegion)
	}

	return y
}

func (x *ReqCreateOneTimePassword) MaskInLog() any {
	if x == nil {
		return (*ReqCreateOneTimePassword)(nil)
	}

	y := proto.Clone(x).(*ReqCreateOneTimePassword)
	if v, ok := any(y.Receiver).(interface{ MaskInLog() any }); ok {
		y.Receiver = v.MaskInLog().(*OtpReceiver)
	}

	return y
}

func (x *ReqCreateOneTimePassword) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.Receiver).(interface{ MaskInRpc() any }); ok {
		y.Receiver = v.MaskInRpc().(*OtpReceiver)
	}

	return y
}

func (x *ReqCreateOneTimePassword) MaskInBff() any {
	if x == nil {
		return (*ReqCreateOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.Receiver).(interface{ MaskInBff() any }); ok {
		y.Receiver = v.MaskInBff().(*OtpReceiver)
	}

	return y
}

func (x *RspCreateOneTimePassword) MaskInLog() any {
	if x == nil {
		return (*RspCreateOneTimePassword)(nil)
	}

	y := proto.Clone(x).(*RspCreateOneTimePassword)
	if v, ok := any(y.RateLimitResult).(interface{ MaskInLog() any }); ok {
		y.RateLimitResult = v.MaskInLog().(*base.RateLimitResult)
	}
	if v, ok := any(y.Result).(interface{ MaskInLog() any }); ok {
		y.Result = v.MaskInLog().(*notify.DeliveryResult)
	}

	return y
}

func (x *RspCreateOneTimePassword) MaskInRpc() any {
	if x == nil {
		return (*RspCreateOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.RateLimitResult).(interface{ MaskInRpc() any }); ok {
		y.RateLimitResult = v.MaskInRpc().(*base.RateLimitResult)
	}
	if v, ok := any(y.Result).(interface{ MaskInRpc() any }); ok {
		y.Result = v.MaskInRpc().(*notify.DeliveryResult)
	}

	return y
}

func (x *RspCreateOneTimePassword) MaskInBff() any {
	if x == nil {
		return (*RspCreateOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.RateLimitResult).(interface{ MaskInBff() any }); ok {
		y.RateLimitResult = v.MaskInBff().(*base.RateLimitResult)
	}
	if v, ok := any(y.Result).(interface{ MaskInBff() any }); ok {
		y.Result = v.MaskInBff().(*notify.DeliveryResult)
	}

	return y
}

func (x *ReqValidateOneTimePassword) MaskInLog() any {
	if x == nil {
		return (*ReqValidateOneTimePassword)(nil)
	}

	y := proto.Clone(x).(*ReqValidateOneTimePassword)
	if v, ok := any(y.Receiver).(interface{ MaskInLog() any }); ok {
		y.Receiver = v.MaskInLog().(*OtpReceiver)
	}
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqValidateOneTimePassword) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.Receiver).(interface{ MaskInRpc() any }); ok {
		y.Receiver = v.MaskInRpc().(*OtpReceiver)
	}
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqValidateOneTimePassword) MaskInBff() any {
	if x == nil {
		return (*ReqValidateOneTimePassword)(nil)
	}

	y := x
	if v, ok := any(y.Receiver).(interface{ MaskInBff() any }); ok {
		y.Receiver = v.MaskInBff().(*OtpReceiver)
	}
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqCreateUserTfa) MaskInLog() any {
	if x == nil {
		return (*ReqCreateUserTfa)(nil)
	}

	y := proto.Clone(x).(*ReqCreateUserTfa)
	switch v := y.Para.(type) {
	case *ReqCreateUserTfa_PhoneTfa:
		if vv, ok := any(v.PhoneTfa).(interface{ MaskInLog() any }); ok {
			v.PhoneTfa = vv.MaskInLog().(*ReqCreateUserTfa_PhonePara)
		}
	case *ReqCreateUserTfa_PasswordTfa:
		if vv, ok := any(v.PasswordTfa).(interface{ MaskInLog() any }); ok {
			v.PasswordTfa = vv.MaskInLog().(*ReqCreateUserTfa_PasswordPara)
		}
	case *ReqCreateUserTfa_EmailTfa:
		if vv, ok := any(v.EmailTfa).(interface{ MaskInLog() any }); ok {
			v.EmailTfa = vv.MaskInLog().(*ReqCreateUserTfa_EmailPara)
		}
	case *ReqCreateUserTfa_WeixinBrowserTfa:
		if vv, ok := any(v.WeixinBrowserTfa).(interface{ MaskInLog() any }); ok {
			v.WeixinBrowserTfa = vv.MaskInLog().(*ReqCreateUserTfa_WeixinBrowserPara)
		}
	case *ReqCreateUserTfa_GoogleTfa:
		if vv, ok := any(v.GoogleTfa).(interface{ MaskInLog() any }); ok {
			v.GoogleTfa = vv.MaskInLog().(*ReqCreateUserTfa_GooglePara)
		}
	}

	return y
}

func (x *ReqCreateUserTfa) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateUserTfa)(nil)
	}

	y := x
	switch v := y.Para.(type) {
	case *ReqCreateUserTfa_PhoneTfa:
		if vv, ok := any(v.PhoneTfa).(interface{ MaskInRpc() any }); ok {
			v.PhoneTfa = vv.MaskInRpc().(*ReqCreateUserTfa_PhonePara)
		}
	case *ReqCreateUserTfa_PasswordTfa:
		if vv, ok := any(v.PasswordTfa).(interface{ MaskInRpc() any }); ok {
			v.PasswordTfa = vv.MaskInRpc().(*ReqCreateUserTfa_PasswordPara)
		}
	case *ReqCreateUserTfa_EmailTfa:
		if vv, ok := any(v.EmailTfa).(interface{ MaskInRpc() any }); ok {
			v.EmailTfa = vv.MaskInRpc().(*ReqCreateUserTfa_EmailPara)
		}
	case *ReqCreateUserTfa_WeixinBrowserTfa:
		if vv, ok := any(v.WeixinBrowserTfa).(interface{ MaskInRpc() any }); ok {
			v.WeixinBrowserTfa = vv.MaskInRpc().(*ReqCreateUserTfa_WeixinBrowserPara)
		}
	case *ReqCreateUserTfa_GoogleTfa:
		if vv, ok := any(v.GoogleTfa).(interface{ MaskInRpc() any }); ok {
			v.GoogleTfa = vv.MaskInRpc().(*ReqCreateUserTfa_GooglePara)
		}
	}

	return y
}

func (x *ReqCreateUserTfa) MaskInBff() any {
	if x == nil {
		return (*ReqCreateUserTfa)(nil)
	}

	y := x
	switch v := y.Para.(type) {
	case *ReqCreateUserTfa_PhoneTfa:
		if vv, ok := any(v.PhoneTfa).(interface{ MaskInBff() any }); ok {
			v.PhoneTfa = vv.MaskInBff().(*ReqCreateUserTfa_PhonePara)
		}
	case *ReqCreateUserTfa_PasswordTfa:
		if vv, ok := any(v.PasswordTfa).(interface{ MaskInBff() any }); ok {
			v.PasswordTfa = vv.MaskInBff().(*ReqCreateUserTfa_PasswordPara)
		}
	case *ReqCreateUserTfa_EmailTfa:
		if vv, ok := any(v.EmailTfa).(interface{ MaskInBff() any }); ok {
			v.EmailTfa = vv.MaskInBff().(*ReqCreateUserTfa_EmailPara)
		}
	case *ReqCreateUserTfa_WeixinBrowserTfa:
		if vv, ok := any(v.WeixinBrowserTfa).(interface{ MaskInBff() any }); ok {
			v.WeixinBrowserTfa = vv.MaskInBff().(*ReqCreateUserTfa_WeixinBrowserPara)
		}
	case *ReqCreateUserTfa_GoogleTfa:
		if vv, ok := any(v.GoogleTfa).(interface{ MaskInBff() any }); ok {
			v.GoogleTfa = vv.MaskInBff().(*ReqCreateUserTfa_GooglePara)
		}
	}

	return y
}

func (x *ReqCreateUserTfa_PasswordPara) MaskInLog() any {
	if x == nil {
		return (*ReqCreateUserTfa_PasswordPara)(nil)
	}

	y := proto.Clone(x).(*ReqCreateUserTfa_PasswordPara)
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqCreateUserTfa_PasswordPara) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateUserTfa_PasswordPara)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqCreateUserTfa_PasswordPara) MaskInBff() any {
	if x == nil {
		return (*ReqCreateUserTfa_PasswordPara)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *RspCreateUserTfa) MaskInLog() any {
	if x == nil {
		return (*RspCreateUserTfa)(nil)
	}

	y := proto.Clone(x).(*RspCreateUserTfa)
	if v, ok := any(y.TfaToken).(interface{ MaskInLog() any }); ok {
		y.TfaToken = v.MaskInLog().(*TfaToken)
	}

	return y
}

func (x *RspCreateUserTfa) MaskInRpc() any {
	if x == nil {
		return (*RspCreateUserTfa)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInRpc() any }); ok {
		y.TfaToken = v.MaskInRpc().(*TfaToken)
	}

	return y
}

func (x *RspCreateUserTfa) MaskInBff() any {
	if x == nil {
		return (*RspCreateUserTfa)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInBff() any }); ok {
		y.TfaToken = v.MaskInBff().(*TfaToken)
	}

	return y
}

func (x *RspGetUserTfa) MaskInLog() any {
	if x == nil {
		return (*RspGetUserTfa)(nil)
	}

	y := proto.Clone(x).(*RspGetUserTfa)
	if v, ok := any(y.TfaToken).(interface{ MaskInLog() any }); ok {
		y.TfaToken = v.MaskInLog().(*TfaToken)
	}

	return y
}

func (x *RspGetUserTfa) MaskInRpc() any {
	if x == nil {
		return (*RspGetUserTfa)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInRpc() any }); ok {
		y.TfaToken = v.MaskInRpc().(*TfaToken)
	}

	return y
}

func (x *RspGetUserTfa) MaskInBff() any {
	if x == nil {
		return (*RspGetUserTfa)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInBff() any }); ok {
		y.TfaToken = v.MaskInBff().(*TfaToken)
	}

	return y
}

func (x *RspCreateWeixinTfaQrcode) MaskInLog() any {
	if x == nil {
		return (*RspCreateWeixinTfaQrcode)(nil)
	}

	y := proto.Clone(x).(*RspCreateWeixinTfaQrcode)
	if v, ok := any(y.Qrcode).(interface{ MaskInLog() any }); ok {
		y.Qrcode = v.MaskInLog().(*WeixinQrcode)
	}

	return y
}

func (x *RspCreateWeixinTfaQrcode) MaskInRpc() any {
	if x == nil {
		return (*RspCreateWeixinTfaQrcode)(nil)
	}

	y := x
	if v, ok := any(y.Qrcode).(interface{ MaskInRpc() any }); ok {
		y.Qrcode = v.MaskInRpc().(*WeixinQrcode)
	}

	return y
}

func (x *RspCreateWeixinTfaQrcode) MaskInBff() any {
	if x == nil {
		return (*RspCreateWeixinTfaQrcode)(nil)
	}

	y := x
	if v, ok := any(y.Qrcode).(interface{ MaskInBff() any }); ok {
		y.Qrcode = v.MaskInBff().(*WeixinQrcode)
	}

	return y
}

func (x *RspGetCreateWeixinTfaState) MaskInLog() any {
	if x == nil {
		return (*RspGetCreateWeixinTfaState)(nil)
	}

	y := proto.Clone(x).(*RspGetCreateWeixinTfaState)
	if v, ok := any(y.TfaToken).(interface{ MaskInLog() any }); ok {
		y.TfaToken = v.MaskInLog().(*TfaToken)
	}

	return y
}

func (x *RspGetCreateWeixinTfaState) MaskInRpc() any {
	if x == nil {
		return (*RspGetCreateWeixinTfaState)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInRpc() any }); ok {
		y.TfaToken = v.MaskInRpc().(*TfaToken)
	}

	return y
}

func (x *RspGetCreateWeixinTfaState) MaskInBff() any {
	if x == nil {
		return (*RspGetCreateWeixinTfaState)(nil)
	}

	y := x
	if v, ok := any(y.TfaToken).(interface{ MaskInBff() any }); ok {
		y.TfaToken = v.MaskInBff().(*TfaToken)
	}

	return y
}

func (x *ReqModifyUserUsername) MaskInLog() any {
	if x == nil {
		return (*ReqModifyUserUsername)(nil)
	}

	y := proto.Clone(x).(*ReqModifyUserUsername)
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *ReqModifyUserUsername) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyUserUsername)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *ReqModifyUserUsername) MaskInBff() any {
	if x == nil {
		return (*ReqModifyUserUsername)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *ReqModifyUserPassword) MaskInLog() any {
	if x == nil {
		return (*ReqModifyUserPassword)(nil)
	}

	y := proto.Clone(x).(*ReqModifyUserPassword)
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqModifyUserPassword) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyUserPassword)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqModifyUserPassword) MaskInBff() any {
	if x == nil {
		return (*ReqModifyUserPassword)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")

	return y
}

func (x *ReqModifyUserPhone) MaskInLog() any {
	if x == nil {
		return (*ReqModifyUserPhone)(nil)
	}

	y := proto.Clone(x).(*ReqModifyUserPhone)
	y.Phone = mask.Mask(y.Phone, "phone")
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqModifyUserPhone) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyUserPhone)(nil)
	}

	y := x
	y.Phone = mask.Mask(y.Phone, "phone")
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqModifyUserPhone) MaskInBff() any {
	if x == nil {
		return (*ReqModifyUserPhone)(nil)
	}

	y := x
	y.Phone = mask.Mask(y.Phone, "phone")
	y.Otp = mask.Mask(y.Otp, "secret")

	return y
}

func (x *ReqModifyUserEmail) MaskInLog() any {
	if x == nil {
		return (*ReqModifyUserEmail)(nil)
	}

	y := proto.Clone(x).(*ReqModifyUserEmail)
	y.Email = mask.Mask(y.Email, "email")

	return y
}

func (x *ReqModifyUserEmail) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyUserEmail)(nil)
	}

	y := x
	y.Email = mask.Mask(y.Email, "email")

	return y
}

func (x *ReqModifyUserEmail) MaskInBff() any {
	if x == nil {
		return (*ReqModifyUserEmail)(nil)
	}

	y := x
	y.Email = mask.Mask(y.Email, "email")

	return y
}

func (x *RspModifyUserEmail) MaskInLog() any {
	if x == nil {
		return (*RspModifyUserEmail)(nil)
	}

	y := proto.Clone(x).(*RspModifyUserEmail)
	if v, ok := any(y.Result).(interface{ MaskInLog() any }); ok {
		y.Result = v.MaskInLog().(*notify.DeliveryResult)
	}

	return y
}

func (x *RspModifyUserEmail) MaskInRpc() any {
	if x == nil {
		return (*RspModifyUserEmail)(nil)
	}

	y := x
	if v, ok := any(y.Result).(interface{ MaskInRpc() any }); ok {
		y.Result = v.MaskInRpc().(*notify.DeliveryResult)
	}

	return y
}

func (x *RspModifyUserEmail) MaskInBff() any {
	if x == nil {
		return (*RspModifyUserEmail)(nil)
	}

	y := x
	if v, ok := any(y.Result).(interface{ MaskInBff() any }); ok {
		y.Result = v.MaskInBff().(*notify.DeliveryResult)
	}

	return y
}

func (x *RspCreateBindUserWeixinQrcode) MaskInLog() any {
	if x == nil {
		return (*RspCreateBindUserWeixinQrcode)(nil)
	}

	y := proto.Clone(x).(*RspCreateBindUserWeixinQrcode)
	if v, ok := any(y.Qrcode).(interface{ MaskInLog() any }); ok {
		y.Qrcode = v.MaskInLog().(*WeixinQrcode)
	}

	return y
}

func (x *RspCreateBindUserWeixinQrcode) MaskInRpc() any {
	if x == nil {
		return (*RspCreateBindUserWeixinQrcode)(nil)
	}

	y := x
	if v, ok := any(y.Qrcode).(interface{ MaskInRpc() any }); ok {
		y.Qrcode = v.MaskInRpc().(*WeixinQrcode)
	}

	return y
}

func (x *RspCreateBindUserWeixinQrcode) MaskInBff() any {
	if x == nil {
		return (*RspCreateBindUserWeixinQrcode)(nil)
	}

	y := x
	if v, ok := any(y.Qrcode).(interface{ MaskInBff() any }); ok {
		y.Qrcode = v.MaskInBff().(*WeixinQrcode)
	}

	return y
}

func (x *ReqBindUserGoogle) MaskInLog() any {
	if x == nil {
		return (*ReqBindUserGoogle)(nil)
	}

	y := proto.Clone(x).(*ReqBindUserGoogle)
	y.Code = mask.Mask(y.Code, "secret")

	return y
}

func (x *ReqBindUserGoogle) MaskInRpc() any {
	if x == nil {
		return (*ReqBindUserGoogle)(nil)
	}

	y := x
	y.Code = mask.Mask(y.Code, "secret")

	return y
}

func (x *ReqBindUserGoogle) MaskInBff() any {
	if x == nil {
		return (*ReqBindUserGoogle)(nil)
	}

	y := x
	y.Code = mask.Mask(y.Code, "secret")

	return y
}

func (x *ReqMakeUniqueUsername) MaskInLog() any {
	if x == nil {
		return (*ReqMakeUniqueUsername)(nil)
	}

	y := proto.Clone(x).(*ReqMakeUniqueUsername)
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *ReqMakeUniqueUsername) MaskInRpc() any {
	if x == nil {
		return (*ReqMakeUniqueUsername)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *ReqMakeUniqueUsername) MaskInBff() any {
	if x == nil {
		return (*ReqMakeUniqueUsername)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *RspMakeUniqueUsername) MaskInLog() any {
	if x == nil {
		return (*RspMakeUniqueUsername)(nil)
	}

	y := proto.Clone(x).(*RspMakeUniqueUsername)
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *RspMakeUniqueUsername) MaskInRpc() any {
	if x == nil {
		return (*RspMakeUniqueUsername)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *RspMakeUniqueUsername) MaskInBff() any {
	if x == nil {
		return (*RspMakeUniqueUsername)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *RspDisableUser) MaskInLog() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := proto.Clone(x).(*RspDisableUser)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspDisableUser) MaskInRpc() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspDisableUser) MaskInBff() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInLog() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := proto.Clone(x).(*RspEnableUser)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInRpc() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInBff() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *RspGetOauthClient) MaskInLog() any {
	if x == nil {
		return (*RspGetOauthClient)(nil)
	}

	y := proto.Clone(x).(*RspGetOauthClient)
	for k, v := range y.Clients {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Clients[k] = vv.MaskInLog().(*OauthClient)
		}
	}

	return y
}

func (x *RspGetOauthClient) MaskInRpc() any {
	if x == nil {
		return (*RspGetOauthClient)(nil)
	}

	y := x
	for k, v := range y.Clients {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Clients[k] = vv.MaskInRpc().(*OauthClient)
		}
	}

	return y
}

func (x *RspGetOauthClient) MaskInBff() any {
	if x == nil {
		return (*RspGetOauthClient)(nil)
	}

	y := x
	for k, v := range y.Clients {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Clients[k] = vv.MaskInBff().(*OauthClient)
		}
	}

	return y
}

func (x *ReqGetUsersPage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetUsersPage) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetUsersByKey) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserSet {
		if sanitizer, ok := any(x.UserSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetUserByUnionID) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.UserSet).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspValidateLoginCredentials) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.User).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqBindUserThirdIndex) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ThirdUser).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpdateUserInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.User).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetIpRegion) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.IpRegion).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqCreateOneTimePassword) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Receiver).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateOneTimePassword) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.RateLimitResult).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Result).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqValidateOneTimePassword) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Receiver).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqCreateUserTfa) SanitizeXSS() {
	if x == nil {
		return
	}

	switch oneof := x.Para.(type) {
	case *ReqCreateUserTfa_PhoneTfa:
		if sanitizer, ok := any(oneof.PhoneTfa).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Para = oneof
	case *ReqCreateUserTfa_PasswordTfa:
		if sanitizer, ok := any(oneof.PasswordTfa).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Para = oneof
	case *ReqCreateUserTfa_EmailTfa:
		if sanitizer, ok := any(oneof.EmailTfa).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Para = oneof
	case *ReqCreateUserTfa_WeixinBrowserTfa:
		if sanitizer, ok := any(oneof.WeixinBrowserTfa).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Para = oneof
	case *ReqCreateUserTfa_GoogleTfa:
		if sanitizer, ok := any(oneof.GoogleTfa).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Para = oneof
	}
}

func (x *RspCreateUserTfa) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TfaToken).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetUserTfa) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TfaToken).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateWeixinTfaQrcode) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Qrcode).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetCreateWeixinTfaState) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.TfaToken).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspModifyUserEmail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Result).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateBindUserWeixinQrcode) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Qrcode).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDisableUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspEnableUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetOauthClient) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Clients {
		if sanitizer, ok := any(x.Clients[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
