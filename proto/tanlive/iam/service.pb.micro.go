// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/iam/service.proto

package iam

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/notify"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/fieldmaskpb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for IamService service

func NewIamServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for IamService service

type IamService interface {
	// 获取用户分页
	GetUsersPage(ctx context.Context, in *ReqGetUsersPage, opts ...client.CallOption) (*RspGetUsersPage, error)
	// 通过主键查询用户（该接口会查询国际用户）
	GetUsersByKey(ctx context.Context, in *ReqGetUsersByKey, opts ...client.CallOption) (*RspGetUsersByKey, error)
	// 通过unionid查询用户（该接口查询国内国际用户）
	GetUserByUnionID(ctx context.Context, in *ReqGetUserByUnionID, opts ...client.CallOption) (*RspGetUserByUnionID, error)
	// 检查API权限
	CheckApiPermission(ctx context.Context, in *ReqCheckApiPermission, opts ...client.CallOption) (*RspCheckApiPermission, error)
	// 检查登录凭证
	ValidateLoginCredentials(ctx context.Context, in *ReqValidateLoginCredentials, opts ...client.CallOption) (*RspValidateLoginCredentials, error)
	// 绑定用户的第三方登录索引
	BindUserThirdIndex(ctx context.Context, in *ReqBindUserThirdIndex, opts ...client.CallOption) (*emptypb.Empty, error)
	// 更新用户信息
	UpdateUserInfo(ctx context.Context, in *ReqUpdateUserInfo, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询IP属地
	GetIpRegion(ctx context.Context, in *ReqGetIpRegion, opts ...client.CallOption) (*RspGetIpRegion, error)
	// 更新用户信息（仅用于团队创建成功）
	UpdateUserWhenTeamCreated(ctx context.Context, in *ReqUpdateUserWhenTeamCreated, opts ...client.CallOption) (*emptypb.Empty, error)
	// 检查用户是否拥有手机
	CheckUserHavePhone(ctx context.Context, in *ReqCheckUserHavePhone, opts ...client.CallOption) (*RspCheckUserHavePhone, error)
	// 创建一次性密码
	CreateOneTimePassword(ctx context.Context, in *ReqCreateOneTimePassword, opts ...client.CallOption) (*RspCreateOneTimePassword, error)
	// 校验一次性密码
	ValidateOneTimePassword(ctx context.Context, in *ReqValidateOneTimePassword, opts ...client.CallOption) (*emptypb.Empty, error)
	// 创建用户的二次验证
	CreateUserTfa(ctx context.Context, in *ReqCreateUserTfa, opts ...client.CallOption) (*RspCreateUserTfa, error)
	// 查询用户的二次验证
	GetUserTfa(ctx context.Context, in *ReqGetUserTfa, opts ...client.CallOption) (*RspGetUserTfa, error)
	// 创建微信二次验证二维码
	CreateWeixinTfaQrcode(ctx context.Context, in *ReqCreateWeixinTfaQrcode, opts ...client.CallOption) (*RspCreateWeixinTfaQrcode, error)
	// 创建微信二次验证（供v1使用）
	CreateWeixinTfa(ctx context.Context, in *ReqCreateWeixinTfa, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询创建微信二次验证状态
	GetCreateWeixinTfaState(ctx context.Context, in *ReqGetCreateWeixinTfaState, opts ...client.CallOption) (*RspGetCreateWeixinTfaState, error)
	// 校验二次验证（仅v1使用）
	ValidateTwoFactorAuth(ctx context.Context, in *ReqValidateTwoFactorAuth, opts ...client.CallOption) (*emptypb.Empty, error)
	// 修改用户的用户名
	ModifyUserUsername(ctx context.Context, in *ReqModifyUserUsername, opts ...client.CallOption) (*emptypb.Empty, error)
	// 修改用户的密码
	ModifyUserPassword(ctx context.Context, in *ReqModifyUserPassword, opts ...client.CallOption) (*emptypb.Empty, error)
	// 修改用户的手机号
	ModifyUserPhone(ctx context.Context, in *ReqModifyUserPhone, opts ...client.CallOption) (*emptypb.Empty, error)
	// 修改用户的邮箱
	ModifyUserEmail(ctx context.Context, in *ReqModifyUserEmail, opts ...client.CallOption) (*RspModifyUserEmail, error)
	// 激活用户的邮箱
	ActiveUserEmail(ctx context.Context, in *ReqActiveUserEmail, opts ...client.CallOption) (*emptypb.Empty, error)
	// 创建绑定用户微信的二维码
	CreateBindUserWeixinQrcode(ctx context.Context, in *ReqCreateBindUserWeixinQrcode, opts ...client.CallOption) (*RspCreateBindUserWeixinQrcode, error)
	// 查询绑定用户微信的状态
	GetBindUserWeixinState(ctx context.Context, in *ReqGetBindUserWeixinState, opts ...client.CallOption) (*RspGetBindUserWeixinState, error)
	// 绑定用户微信（供v1使用）
	BindUserWeixin(ctx context.Context, in *ReqBindUserWeixin, opts ...client.CallOption) (*emptypb.Empty, error)
	// 绑定用户微信（在微信浏览器中）
	BindUserWeixinByOauth2(ctx context.Context, in *ReqBindUserWeixinByOauth2, opts ...client.CallOption) (*emptypb.Empty, error)
	// 解绑用户微信
	UnbindUserWeixin(ctx context.Context, in *ReqUnbindUserWeixin, opts ...client.CallOption) (*emptypb.Empty, error)
	// 微信公众号服务端
	ServeWeixinOfficialAccount(ctx context.Context, in *ReqServeWeixinOfficialAccount, opts ...client.CallOption) (*RspServeWeixinOfficialAccount, error)
	// 获取谷歌认证URL
	GetGoogleAuthUrl(ctx context.Context, in *ReqGetGoogleAuthUrl, opts ...client.CallOption) (*RspGetGoogleAuthUrl, error)
	// 绑定用户谷歌账号
	BindUserGoogle(ctx context.Context, in *ReqBindUserGoogle, opts ...client.CallOption) (*emptypb.Empty, error)
	// 解绑用户谷歌账号
	UnbindUserGoogle(ctx context.Context, in *ReqUnbindUserGoogle, opts ...client.CallOption) (*emptypb.Empty, error)
	// 生成唯一用户名（v1调用）
	MakeUniqueUsername(ctx context.Context, in *ReqMakeUniqueUsername, opts ...client.CallOption) (*RspMakeUniqueUsername, error)
	// 冻结用户
	DisableUser(ctx context.Context, in *ReqDisableUser, opts ...client.CallOption) (*RspDisableUser, error)
	// 解冻用户
	EnableUser(ctx context.Context, in *ReqEnableUser, opts ...client.CallOption) (*RspEnableUser, error)
	// 获取用户id
	GetUserIds(ctx context.Context, in *ReqGetUserIds, opts ...client.CallOption) (*RspGetUserIds, error)
	// GetOauthClient 获取OAuth客户端
	GetOauthClient(ctx context.Context, in *ReqGetOauthClient, opts ...client.CallOption) (*RspGetOauthClient, error)
}

type iamService struct {
	c    client.Client
	name string
}

func NewIamService(name string, c client.Client) IamService {
	return &iamService{
		c:    c,
		name: name,
	}
}

func (c *iamService) GetUsersPage(ctx context.Context, in *ReqGetUsersPage, opts ...client.CallOption) (*RspGetUsersPage, error) {
	req := c.c.NewRequest(c.name, "IamService.GetUsersPage", in)
	out := new(RspGetUsersPage)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetUsersByKey(ctx context.Context, in *ReqGetUsersByKey, opts ...client.CallOption) (*RspGetUsersByKey, error) {
	req := c.c.NewRequest(c.name, "IamService.GetUsersByKey", in)
	out := new(RspGetUsersByKey)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetUserByUnionID(ctx context.Context, in *ReqGetUserByUnionID, opts ...client.CallOption) (*RspGetUserByUnionID, error) {
	req := c.c.NewRequest(c.name, "IamService.GetUserByUnionID", in)
	out := new(RspGetUserByUnionID)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) CheckApiPermission(ctx context.Context, in *ReqCheckApiPermission, opts ...client.CallOption) (*RspCheckApiPermission, error) {
	req := c.c.NewRequest(c.name, "IamService.CheckApiPermission", in)
	out := new(RspCheckApiPermission)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ValidateLoginCredentials(ctx context.Context, in *ReqValidateLoginCredentials, opts ...client.CallOption) (*RspValidateLoginCredentials, error) {
	req := c.c.NewRequest(c.name, "IamService.ValidateLoginCredentials", in)
	out := new(RspValidateLoginCredentials)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) BindUserThirdIndex(ctx context.Context, in *ReqBindUserThirdIndex, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.BindUserThirdIndex", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) UpdateUserInfo(ctx context.Context, in *ReqUpdateUserInfo, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.UpdateUserInfo", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetIpRegion(ctx context.Context, in *ReqGetIpRegion, opts ...client.CallOption) (*RspGetIpRegion, error) {
	req := c.c.NewRequest(c.name, "IamService.GetIpRegion", in)
	out := new(RspGetIpRegion)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) UpdateUserWhenTeamCreated(ctx context.Context, in *ReqUpdateUserWhenTeamCreated, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.UpdateUserWhenTeamCreated", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) CheckUserHavePhone(ctx context.Context, in *ReqCheckUserHavePhone, opts ...client.CallOption) (*RspCheckUserHavePhone, error) {
	req := c.c.NewRequest(c.name, "IamService.CheckUserHavePhone", in)
	out := new(RspCheckUserHavePhone)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) CreateOneTimePassword(ctx context.Context, in *ReqCreateOneTimePassword, opts ...client.CallOption) (*RspCreateOneTimePassword, error) {
	req := c.c.NewRequest(c.name, "IamService.CreateOneTimePassword", in)
	out := new(RspCreateOneTimePassword)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ValidateOneTimePassword(ctx context.Context, in *ReqValidateOneTimePassword, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.ValidateOneTimePassword", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) CreateUserTfa(ctx context.Context, in *ReqCreateUserTfa, opts ...client.CallOption) (*RspCreateUserTfa, error) {
	req := c.c.NewRequest(c.name, "IamService.CreateUserTfa", in)
	out := new(RspCreateUserTfa)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetUserTfa(ctx context.Context, in *ReqGetUserTfa, opts ...client.CallOption) (*RspGetUserTfa, error) {
	req := c.c.NewRequest(c.name, "IamService.GetUserTfa", in)
	out := new(RspGetUserTfa)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) CreateWeixinTfaQrcode(ctx context.Context, in *ReqCreateWeixinTfaQrcode, opts ...client.CallOption) (*RspCreateWeixinTfaQrcode, error) {
	req := c.c.NewRequest(c.name, "IamService.CreateWeixinTfaQrcode", in)
	out := new(RspCreateWeixinTfaQrcode)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) CreateWeixinTfa(ctx context.Context, in *ReqCreateWeixinTfa, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.CreateWeixinTfa", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetCreateWeixinTfaState(ctx context.Context, in *ReqGetCreateWeixinTfaState, opts ...client.CallOption) (*RspGetCreateWeixinTfaState, error) {
	req := c.c.NewRequest(c.name, "IamService.GetCreateWeixinTfaState", in)
	out := new(RspGetCreateWeixinTfaState)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ValidateTwoFactorAuth(ctx context.Context, in *ReqValidateTwoFactorAuth, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.ValidateTwoFactorAuth", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ModifyUserUsername(ctx context.Context, in *ReqModifyUserUsername, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.ModifyUserUsername", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ModifyUserPassword(ctx context.Context, in *ReqModifyUserPassword, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.ModifyUserPassword", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ModifyUserPhone(ctx context.Context, in *ReqModifyUserPhone, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.ModifyUserPhone", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ModifyUserEmail(ctx context.Context, in *ReqModifyUserEmail, opts ...client.CallOption) (*RspModifyUserEmail, error) {
	req := c.c.NewRequest(c.name, "IamService.ModifyUserEmail", in)
	out := new(RspModifyUserEmail)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ActiveUserEmail(ctx context.Context, in *ReqActiveUserEmail, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.ActiveUserEmail", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) CreateBindUserWeixinQrcode(ctx context.Context, in *ReqCreateBindUserWeixinQrcode, opts ...client.CallOption) (*RspCreateBindUserWeixinQrcode, error) {
	req := c.c.NewRequest(c.name, "IamService.CreateBindUserWeixinQrcode", in)
	out := new(RspCreateBindUserWeixinQrcode)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetBindUserWeixinState(ctx context.Context, in *ReqGetBindUserWeixinState, opts ...client.CallOption) (*RspGetBindUserWeixinState, error) {
	req := c.c.NewRequest(c.name, "IamService.GetBindUserWeixinState", in)
	out := new(RspGetBindUserWeixinState)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) BindUserWeixin(ctx context.Context, in *ReqBindUserWeixin, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.BindUserWeixin", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) BindUserWeixinByOauth2(ctx context.Context, in *ReqBindUserWeixinByOauth2, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.BindUserWeixinByOauth2", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) UnbindUserWeixin(ctx context.Context, in *ReqUnbindUserWeixin, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.UnbindUserWeixin", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) ServeWeixinOfficialAccount(ctx context.Context, in *ReqServeWeixinOfficialAccount, opts ...client.CallOption) (*RspServeWeixinOfficialAccount, error) {
	req := c.c.NewRequest(c.name, "IamService.ServeWeixinOfficialAccount", in)
	out := new(RspServeWeixinOfficialAccount)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetGoogleAuthUrl(ctx context.Context, in *ReqGetGoogleAuthUrl, opts ...client.CallOption) (*RspGetGoogleAuthUrl, error) {
	req := c.c.NewRequest(c.name, "IamService.GetGoogleAuthUrl", in)
	out := new(RspGetGoogleAuthUrl)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) BindUserGoogle(ctx context.Context, in *ReqBindUserGoogle, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.BindUserGoogle", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) UnbindUserGoogle(ctx context.Context, in *ReqUnbindUserGoogle, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "IamService.UnbindUserGoogle", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) MakeUniqueUsername(ctx context.Context, in *ReqMakeUniqueUsername, opts ...client.CallOption) (*RspMakeUniqueUsername, error) {
	req := c.c.NewRequest(c.name, "IamService.MakeUniqueUsername", in)
	out := new(RspMakeUniqueUsername)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) DisableUser(ctx context.Context, in *ReqDisableUser, opts ...client.CallOption) (*RspDisableUser, error) {
	req := c.c.NewRequest(c.name, "IamService.DisableUser", in)
	out := new(RspDisableUser)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) EnableUser(ctx context.Context, in *ReqEnableUser, opts ...client.CallOption) (*RspEnableUser, error) {
	req := c.c.NewRequest(c.name, "IamService.EnableUser", in)
	out := new(RspEnableUser)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetUserIds(ctx context.Context, in *ReqGetUserIds, opts ...client.CallOption) (*RspGetUserIds, error) {
	req := c.c.NewRequest(c.name, "IamService.GetUserIds", in)
	out := new(RspGetUserIds)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iamService) GetOauthClient(ctx context.Context, in *ReqGetOauthClient, opts ...client.CallOption) (*RspGetOauthClient, error) {
	req := c.c.NewRequest(c.name, "IamService.GetOauthClient", in)
	out := new(RspGetOauthClient)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for IamService service

type IamServiceHandler interface {
	// 获取用户分页
	GetUsersPage(context.Context, *ReqGetUsersPage, *RspGetUsersPage) error
	// 通过主键查询用户（该接口会查询国际用户）
	GetUsersByKey(context.Context, *ReqGetUsersByKey, *RspGetUsersByKey) error
	// 通过unionid查询用户（该接口查询国内国际用户）
	GetUserByUnionID(context.Context, *ReqGetUserByUnionID, *RspGetUserByUnionID) error
	// 检查API权限
	CheckApiPermission(context.Context, *ReqCheckApiPermission, *RspCheckApiPermission) error
	// 检查登录凭证
	ValidateLoginCredentials(context.Context, *ReqValidateLoginCredentials, *RspValidateLoginCredentials) error
	// 绑定用户的第三方登录索引
	BindUserThirdIndex(context.Context, *ReqBindUserThirdIndex, *emptypb.Empty) error
	// 更新用户信息
	UpdateUserInfo(context.Context, *ReqUpdateUserInfo, *emptypb.Empty) error
	// 查询IP属地
	GetIpRegion(context.Context, *ReqGetIpRegion, *RspGetIpRegion) error
	// 更新用户信息（仅用于团队创建成功）
	UpdateUserWhenTeamCreated(context.Context, *ReqUpdateUserWhenTeamCreated, *emptypb.Empty) error
	// 检查用户是否拥有手机
	CheckUserHavePhone(context.Context, *ReqCheckUserHavePhone, *RspCheckUserHavePhone) error
	// 创建一次性密码
	CreateOneTimePassword(context.Context, *ReqCreateOneTimePassword, *RspCreateOneTimePassword) error
	// 校验一次性密码
	ValidateOneTimePassword(context.Context, *ReqValidateOneTimePassword, *emptypb.Empty) error
	// 创建用户的二次验证
	CreateUserTfa(context.Context, *ReqCreateUserTfa, *RspCreateUserTfa) error
	// 查询用户的二次验证
	GetUserTfa(context.Context, *ReqGetUserTfa, *RspGetUserTfa) error
	// 创建微信二次验证二维码
	CreateWeixinTfaQrcode(context.Context, *ReqCreateWeixinTfaQrcode, *RspCreateWeixinTfaQrcode) error
	// 创建微信二次验证（供v1使用）
	CreateWeixinTfa(context.Context, *ReqCreateWeixinTfa, *emptypb.Empty) error
	// 查询创建微信二次验证状态
	GetCreateWeixinTfaState(context.Context, *ReqGetCreateWeixinTfaState, *RspGetCreateWeixinTfaState) error
	// 校验二次验证（仅v1使用）
	ValidateTwoFactorAuth(context.Context, *ReqValidateTwoFactorAuth, *emptypb.Empty) error
	// 修改用户的用户名
	ModifyUserUsername(context.Context, *ReqModifyUserUsername, *emptypb.Empty) error
	// 修改用户的密码
	ModifyUserPassword(context.Context, *ReqModifyUserPassword, *emptypb.Empty) error
	// 修改用户的手机号
	ModifyUserPhone(context.Context, *ReqModifyUserPhone, *emptypb.Empty) error
	// 修改用户的邮箱
	ModifyUserEmail(context.Context, *ReqModifyUserEmail, *RspModifyUserEmail) error
	// 激活用户的邮箱
	ActiveUserEmail(context.Context, *ReqActiveUserEmail, *emptypb.Empty) error
	// 创建绑定用户微信的二维码
	CreateBindUserWeixinQrcode(context.Context, *ReqCreateBindUserWeixinQrcode, *RspCreateBindUserWeixinQrcode) error
	// 查询绑定用户微信的状态
	GetBindUserWeixinState(context.Context, *ReqGetBindUserWeixinState, *RspGetBindUserWeixinState) error
	// 绑定用户微信（供v1使用）
	BindUserWeixin(context.Context, *ReqBindUserWeixin, *emptypb.Empty) error
	// 绑定用户微信（在微信浏览器中）
	BindUserWeixinByOauth2(context.Context, *ReqBindUserWeixinByOauth2, *emptypb.Empty) error
	// 解绑用户微信
	UnbindUserWeixin(context.Context, *ReqUnbindUserWeixin, *emptypb.Empty) error
	// 微信公众号服务端
	ServeWeixinOfficialAccount(context.Context, *ReqServeWeixinOfficialAccount, *RspServeWeixinOfficialAccount) error
	// 获取谷歌认证URL
	GetGoogleAuthUrl(context.Context, *ReqGetGoogleAuthUrl, *RspGetGoogleAuthUrl) error
	// 绑定用户谷歌账号
	BindUserGoogle(context.Context, *ReqBindUserGoogle, *emptypb.Empty) error
	// 解绑用户谷歌账号
	UnbindUserGoogle(context.Context, *ReqUnbindUserGoogle, *emptypb.Empty) error
	// 生成唯一用户名（v1调用）
	MakeUniqueUsername(context.Context, *ReqMakeUniqueUsername, *RspMakeUniqueUsername) error
	// 冻结用户
	DisableUser(context.Context, *ReqDisableUser, *RspDisableUser) error
	// 解冻用户
	EnableUser(context.Context, *ReqEnableUser, *RspEnableUser) error
	// 获取用户id
	GetUserIds(context.Context, *ReqGetUserIds, *RspGetUserIds) error
	// GetOauthClient 获取OAuth客户端
	GetOauthClient(context.Context, *ReqGetOauthClient, *RspGetOauthClient) error
}

func RegisterIamServiceHandler(s server.Server, hdlr IamServiceHandler, opts ...server.HandlerOption) error {
	type iamService interface {
		GetUsersPage(ctx context.Context, in *ReqGetUsersPage, out *RspGetUsersPage) error
		GetUsersByKey(ctx context.Context, in *ReqGetUsersByKey, out *RspGetUsersByKey) error
		GetUserByUnionID(ctx context.Context, in *ReqGetUserByUnionID, out *RspGetUserByUnionID) error
		CheckApiPermission(ctx context.Context, in *ReqCheckApiPermission, out *RspCheckApiPermission) error
		ValidateLoginCredentials(ctx context.Context, in *ReqValidateLoginCredentials, out *RspValidateLoginCredentials) error
		BindUserThirdIndex(ctx context.Context, in *ReqBindUserThirdIndex, out *emptypb.Empty) error
		UpdateUserInfo(ctx context.Context, in *ReqUpdateUserInfo, out *emptypb.Empty) error
		GetIpRegion(ctx context.Context, in *ReqGetIpRegion, out *RspGetIpRegion) error
		UpdateUserWhenTeamCreated(ctx context.Context, in *ReqUpdateUserWhenTeamCreated, out *emptypb.Empty) error
		CheckUserHavePhone(ctx context.Context, in *ReqCheckUserHavePhone, out *RspCheckUserHavePhone) error
		CreateOneTimePassword(ctx context.Context, in *ReqCreateOneTimePassword, out *RspCreateOneTimePassword) error
		ValidateOneTimePassword(ctx context.Context, in *ReqValidateOneTimePassword, out *emptypb.Empty) error
		CreateUserTfa(ctx context.Context, in *ReqCreateUserTfa, out *RspCreateUserTfa) error
		GetUserTfa(ctx context.Context, in *ReqGetUserTfa, out *RspGetUserTfa) error
		CreateWeixinTfaQrcode(ctx context.Context, in *ReqCreateWeixinTfaQrcode, out *RspCreateWeixinTfaQrcode) error
		CreateWeixinTfa(ctx context.Context, in *ReqCreateWeixinTfa, out *emptypb.Empty) error
		GetCreateWeixinTfaState(ctx context.Context, in *ReqGetCreateWeixinTfaState, out *RspGetCreateWeixinTfaState) error
		ValidateTwoFactorAuth(ctx context.Context, in *ReqValidateTwoFactorAuth, out *emptypb.Empty) error
		ModifyUserUsername(ctx context.Context, in *ReqModifyUserUsername, out *emptypb.Empty) error
		ModifyUserPassword(ctx context.Context, in *ReqModifyUserPassword, out *emptypb.Empty) error
		ModifyUserPhone(ctx context.Context, in *ReqModifyUserPhone, out *emptypb.Empty) error
		ModifyUserEmail(ctx context.Context, in *ReqModifyUserEmail, out *RspModifyUserEmail) error
		ActiveUserEmail(ctx context.Context, in *ReqActiveUserEmail, out *emptypb.Empty) error
		CreateBindUserWeixinQrcode(ctx context.Context, in *ReqCreateBindUserWeixinQrcode, out *RspCreateBindUserWeixinQrcode) error
		GetBindUserWeixinState(ctx context.Context, in *ReqGetBindUserWeixinState, out *RspGetBindUserWeixinState) error
		BindUserWeixin(ctx context.Context, in *ReqBindUserWeixin, out *emptypb.Empty) error
		BindUserWeixinByOauth2(ctx context.Context, in *ReqBindUserWeixinByOauth2, out *emptypb.Empty) error
		UnbindUserWeixin(ctx context.Context, in *ReqUnbindUserWeixin, out *emptypb.Empty) error
		ServeWeixinOfficialAccount(ctx context.Context, in *ReqServeWeixinOfficialAccount, out *RspServeWeixinOfficialAccount) error
		GetGoogleAuthUrl(ctx context.Context, in *ReqGetGoogleAuthUrl, out *RspGetGoogleAuthUrl) error
		BindUserGoogle(ctx context.Context, in *ReqBindUserGoogle, out *emptypb.Empty) error
		UnbindUserGoogle(ctx context.Context, in *ReqUnbindUserGoogle, out *emptypb.Empty) error
		MakeUniqueUsername(ctx context.Context, in *ReqMakeUniqueUsername, out *RspMakeUniqueUsername) error
		DisableUser(ctx context.Context, in *ReqDisableUser, out *RspDisableUser) error
		EnableUser(ctx context.Context, in *ReqEnableUser, out *RspEnableUser) error
		GetUserIds(ctx context.Context, in *ReqGetUserIds, out *RspGetUserIds) error
		GetOauthClient(ctx context.Context, in *ReqGetOauthClient, out *RspGetOauthClient) error
	}
	type IamService struct {
		iamService
	}
	h := &iamServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&IamService{h}, opts...))
}

type iamServiceHandler struct {
	IamServiceHandler
}

func (h *iamServiceHandler) GetUsersPage(ctx context.Context, in *ReqGetUsersPage, out *RspGetUsersPage) error {
	return h.IamServiceHandler.GetUsersPage(ctx, in, out)
}

func (h *iamServiceHandler) GetUsersByKey(ctx context.Context, in *ReqGetUsersByKey, out *RspGetUsersByKey) error {
	return h.IamServiceHandler.GetUsersByKey(ctx, in, out)
}

func (h *iamServiceHandler) GetUserByUnionID(ctx context.Context, in *ReqGetUserByUnionID, out *RspGetUserByUnionID) error {
	return h.IamServiceHandler.GetUserByUnionID(ctx, in, out)
}

func (h *iamServiceHandler) CheckApiPermission(ctx context.Context, in *ReqCheckApiPermission, out *RspCheckApiPermission) error {
	return h.IamServiceHandler.CheckApiPermission(ctx, in, out)
}

func (h *iamServiceHandler) ValidateLoginCredentials(ctx context.Context, in *ReqValidateLoginCredentials, out *RspValidateLoginCredentials) error {
	return h.IamServiceHandler.ValidateLoginCredentials(ctx, in, out)
}

func (h *iamServiceHandler) BindUserThirdIndex(ctx context.Context, in *ReqBindUserThirdIndex, out *emptypb.Empty) error {
	return h.IamServiceHandler.BindUserThirdIndex(ctx, in, out)
}

func (h *iamServiceHandler) UpdateUserInfo(ctx context.Context, in *ReqUpdateUserInfo, out *emptypb.Empty) error {
	return h.IamServiceHandler.UpdateUserInfo(ctx, in, out)
}

func (h *iamServiceHandler) GetIpRegion(ctx context.Context, in *ReqGetIpRegion, out *RspGetIpRegion) error {
	return h.IamServiceHandler.GetIpRegion(ctx, in, out)
}

func (h *iamServiceHandler) UpdateUserWhenTeamCreated(ctx context.Context, in *ReqUpdateUserWhenTeamCreated, out *emptypb.Empty) error {
	return h.IamServiceHandler.UpdateUserWhenTeamCreated(ctx, in, out)
}

func (h *iamServiceHandler) CheckUserHavePhone(ctx context.Context, in *ReqCheckUserHavePhone, out *RspCheckUserHavePhone) error {
	return h.IamServiceHandler.CheckUserHavePhone(ctx, in, out)
}

func (h *iamServiceHandler) CreateOneTimePassword(ctx context.Context, in *ReqCreateOneTimePassword, out *RspCreateOneTimePassword) error {
	return h.IamServiceHandler.CreateOneTimePassword(ctx, in, out)
}

func (h *iamServiceHandler) ValidateOneTimePassword(ctx context.Context, in *ReqValidateOneTimePassword, out *emptypb.Empty) error {
	return h.IamServiceHandler.ValidateOneTimePassword(ctx, in, out)
}

func (h *iamServiceHandler) CreateUserTfa(ctx context.Context, in *ReqCreateUserTfa, out *RspCreateUserTfa) error {
	return h.IamServiceHandler.CreateUserTfa(ctx, in, out)
}

func (h *iamServiceHandler) GetUserTfa(ctx context.Context, in *ReqGetUserTfa, out *RspGetUserTfa) error {
	return h.IamServiceHandler.GetUserTfa(ctx, in, out)
}

func (h *iamServiceHandler) CreateWeixinTfaQrcode(ctx context.Context, in *ReqCreateWeixinTfaQrcode, out *RspCreateWeixinTfaQrcode) error {
	return h.IamServiceHandler.CreateWeixinTfaQrcode(ctx, in, out)
}

func (h *iamServiceHandler) CreateWeixinTfa(ctx context.Context, in *ReqCreateWeixinTfa, out *emptypb.Empty) error {
	return h.IamServiceHandler.CreateWeixinTfa(ctx, in, out)
}

func (h *iamServiceHandler) GetCreateWeixinTfaState(ctx context.Context, in *ReqGetCreateWeixinTfaState, out *RspGetCreateWeixinTfaState) error {
	return h.IamServiceHandler.GetCreateWeixinTfaState(ctx, in, out)
}

func (h *iamServiceHandler) ValidateTwoFactorAuth(ctx context.Context, in *ReqValidateTwoFactorAuth, out *emptypb.Empty) error {
	return h.IamServiceHandler.ValidateTwoFactorAuth(ctx, in, out)
}

func (h *iamServiceHandler) ModifyUserUsername(ctx context.Context, in *ReqModifyUserUsername, out *emptypb.Empty) error {
	return h.IamServiceHandler.ModifyUserUsername(ctx, in, out)
}

func (h *iamServiceHandler) ModifyUserPassword(ctx context.Context, in *ReqModifyUserPassword, out *emptypb.Empty) error {
	return h.IamServiceHandler.ModifyUserPassword(ctx, in, out)
}

func (h *iamServiceHandler) ModifyUserPhone(ctx context.Context, in *ReqModifyUserPhone, out *emptypb.Empty) error {
	return h.IamServiceHandler.ModifyUserPhone(ctx, in, out)
}

func (h *iamServiceHandler) ModifyUserEmail(ctx context.Context, in *ReqModifyUserEmail, out *RspModifyUserEmail) error {
	return h.IamServiceHandler.ModifyUserEmail(ctx, in, out)
}

func (h *iamServiceHandler) ActiveUserEmail(ctx context.Context, in *ReqActiveUserEmail, out *emptypb.Empty) error {
	return h.IamServiceHandler.ActiveUserEmail(ctx, in, out)
}

func (h *iamServiceHandler) CreateBindUserWeixinQrcode(ctx context.Context, in *ReqCreateBindUserWeixinQrcode, out *RspCreateBindUserWeixinQrcode) error {
	return h.IamServiceHandler.CreateBindUserWeixinQrcode(ctx, in, out)
}

func (h *iamServiceHandler) GetBindUserWeixinState(ctx context.Context, in *ReqGetBindUserWeixinState, out *RspGetBindUserWeixinState) error {
	return h.IamServiceHandler.GetBindUserWeixinState(ctx, in, out)
}

func (h *iamServiceHandler) BindUserWeixin(ctx context.Context, in *ReqBindUserWeixin, out *emptypb.Empty) error {
	return h.IamServiceHandler.BindUserWeixin(ctx, in, out)
}

func (h *iamServiceHandler) BindUserWeixinByOauth2(ctx context.Context, in *ReqBindUserWeixinByOauth2, out *emptypb.Empty) error {
	return h.IamServiceHandler.BindUserWeixinByOauth2(ctx, in, out)
}

func (h *iamServiceHandler) UnbindUserWeixin(ctx context.Context, in *ReqUnbindUserWeixin, out *emptypb.Empty) error {
	return h.IamServiceHandler.UnbindUserWeixin(ctx, in, out)
}

func (h *iamServiceHandler) ServeWeixinOfficialAccount(ctx context.Context, in *ReqServeWeixinOfficialAccount, out *RspServeWeixinOfficialAccount) error {
	return h.IamServiceHandler.ServeWeixinOfficialAccount(ctx, in, out)
}

func (h *iamServiceHandler) GetGoogleAuthUrl(ctx context.Context, in *ReqGetGoogleAuthUrl, out *RspGetGoogleAuthUrl) error {
	return h.IamServiceHandler.GetGoogleAuthUrl(ctx, in, out)
}

func (h *iamServiceHandler) BindUserGoogle(ctx context.Context, in *ReqBindUserGoogle, out *emptypb.Empty) error {
	return h.IamServiceHandler.BindUserGoogle(ctx, in, out)
}

func (h *iamServiceHandler) UnbindUserGoogle(ctx context.Context, in *ReqUnbindUserGoogle, out *emptypb.Empty) error {
	return h.IamServiceHandler.UnbindUserGoogle(ctx, in, out)
}

func (h *iamServiceHandler) MakeUniqueUsername(ctx context.Context, in *ReqMakeUniqueUsername, out *RspMakeUniqueUsername) error {
	return h.IamServiceHandler.MakeUniqueUsername(ctx, in, out)
}

func (h *iamServiceHandler) DisableUser(ctx context.Context, in *ReqDisableUser, out *RspDisableUser) error {
	return h.IamServiceHandler.DisableUser(ctx, in, out)
}

func (h *iamServiceHandler) EnableUser(ctx context.Context, in *ReqEnableUser, out *RspEnableUser) error {
	return h.IamServiceHandler.EnableUser(ctx, in, out)
}

func (h *iamServiceHandler) GetUserIds(ctx context.Context, in *ReqGetUserIds, out *RspGetUserIds) error {
	return h.IamServiceHandler.GetUserIds(ctx, in, out)
}

func (h *iamServiceHandler) GetOauthClient(ctx context.Context, in *ReqGetOauthClient, out *RspGetOauthClient) error {
	return h.IamServiceHandler.GetOauthClient(ctx, in, out)
}
