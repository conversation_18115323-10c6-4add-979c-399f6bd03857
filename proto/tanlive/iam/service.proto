syntax = "proto3";

package tanlive.iam;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam";

import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/iam/iam.proto";
import "tanlive/notify/notify.proto";
import "tanlive/options.proto";

message ReqGetUsersPage {
  // 过滤器
  message Filter {
    // 设置user_id范围
    repeated uint64 within_user_id = 1 [(validator) = "omitempty,dive,required"];
    // 搜索关键词
    string keyword = 2;
    // 微信union_id
    string union_id = 3;
  }
  base.Region region = 1;
  // 过滤器
  Filter filter = 2;
  // 分页器
  base.Paginator page = 3;
  // 是否返回total_count
  bool with_total_count = 4;
}

message RspGetUsersPage {
  // 用户列表
  repeated UserInfo users = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqGetUsersByKey {
  repeated uint64 id = 1 [(validator) = "required"];
}

message RspGetUsersByKey {
  repeated UserInfo user_set = 1;
}

message ReqGetUserByUnionID{
  string union_id = 1;
}

message RspGetUserByUnionID {
  UserInfo user_set = 1;
}

message ReqCheckApiPermission {
  uint64 user_id = 1 [(validator) = "required"];
  string path = 2 [(validator) = "required"];
  bool as_team = 3 ;
}

message RspCheckApiPermission {
  bool allowed = 1;
}

message ReqValidateLoginCredentials {
  string identifier = 1 [(validator) = "required", (mask).rule = "name"];
  UserIndexType identifier_type = 2 [(validator) = "required"];
  string password = 3 [(mask).rule = "secret"];
}

message RspValidateLoginCredentials {
  UserInfo user = 1;
}

message ReqBindUserThirdIndex {
  uint64 user_id = 1 [(validator) = "required"];
  ThirdUserInfo third_user = 2 [(validator) = "required"];
}

message ReqUpdateUserInfo {
  UserInfo user = 1 [(validator) = "required"];
  google.protobuf.FieldMask mask = 2 [(validator) = "required"];
}

message ReqGetIpRegion {
  string ip = 1 [(validator) = "required", (mask).rule = "ipv4"];
}

message RspGetIpRegion {
  IpRegion ip_region = 1;
}

message ReqUpdateUserWhenTeamCreated {
  // 用户ID
  uint64 user_id = 1;
  // 管理员角色ID
  uint64 admin_role_id = 2;
}

message ReqCreateOneTimePassword {
  // 使用场景
  OtpScene scene = 1 [(validator) = "required"];
  // 接收者
  OtpReceiver receiver = 2 [(validator) = "required"];
  // 语言
  string lang = 3 [(validator) = "required"];
}

message ReqCheckUserHavePhone{
  uint64 user_id = 1 [(validator) = "required"];
}

message RspCheckUserHavePhone{
  bool have_phone = 1;
}

message RspCreateOneTimePassword {
  // 限流结果
  base.RateLimitResult rate_limit_result = 1;
  // 发送验证码结果
  notify.DeliveryResult result = 2;
}

message ReqValidateOneTimePassword {
  // 使用场景
  OtpScene scene = 1 [(validator) = "required"];
  // 接收者
  OtpReceiver receiver = 2 [(validator) = "required"];
  // 一次性密码
  string otp = 3 [(validator) = "required", (mask).rule = "secret"];
}

message ReqCreateUserTfa {
  // 手机验证参数
  message PhonePara {
    // 一次性密码
    string otp = 1 [(validator) = "required,len=6,number"];
  }
  // 密码验证参数
  message PasswordPara {
    // 密码
    string password = 1 [(validator) = "required", (mask).rule = "secret"];
  }
  // 邮箱验证参数
  message EmailPara {
    // 一次性密码
    string otp = 1 [(validator) = "required,len=6,number"];
  }
  // 微信浏览器验证参数
  message WeixinBrowserPara {
    // 微信回调code
    string code = 1 [(validator) = "required"];
  }
  // 谷歌验证参数
  message GooglePara {
    // 谷歌回调code
    string code = 1 [(validator) = "required"];
  }
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 2FA参数
  oneof para {
    // 手机验证
    PhonePara phone_tfa = 2 [(validator) = "required"];
    // 密码验证
    PasswordPara password_tfa = 3 [(validator) = "required"];
    // 邮箱验证
    EmailPara email_tfa = 4 [(validator) = "required"];
    // 微信浏览器验证参数
    WeixinBrowserPara weixin_browser_tfa = 5 [(validator) = "required"];
    // 谷歌验证参数
    GooglePara google_tfa = 6 [(validator) = "required"];
  }
}

message RspCreateUserTfa {
  // 2FA令牌
  TfaToken tfa_token = 1;
}

message ReqGetUserTfa {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
}

message RspGetUserTfa {
  // 2FA令牌
  TfaToken tfa_token = 1;
}

message ReqCreateWeixinTfaQrcode {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 语言
  string lang = 2 [(validator) = "required"];
}

message RspCreateWeixinTfaQrcode {
  // 二维码信息
  WeixinQrcode qrcode = 1;
}

message ReqCreateWeixinTfa {
  // 场景值
  string scene_str = 1 [(validator) = "required"];
}

message ReqGetCreateWeixinTfaState {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 场景值
  string scene_str = 2 [(validator) = "required"];
}

message RspGetCreateWeixinTfaState {
  // 状态
  CreateWeixinTfaState state = 1;
  // 2FA令牌
  TfaToken tfa_token = 2;
}

message ReqValidateTwoFactorAuth {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required"];
}

message ReqModifyUserUsername {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 用户名
  string username = 2 [(validator) = "required,username", (mask).rule = "username"];
  // 2FA令牌
  string tfa_token = 3 [(validator) = "required"];
}

message ReqModifyUserPassword {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 密码
  string password = 2 [(validator) = "required,password", (mask).rule = "secret"];
  // 2FA令牌
  string tfa_token = 3 [(validator) = "required"];
}

message ReqModifyUserPhone {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 手机号
  string phone = 2 [(validator) = "required,phone", (mask).rule = "phone"];
  // 2FA令牌
  string tfa_token = 3 [(validator) = "required"];
  // 验证码
  string otp = 4 [(validator) = "required", (mask).rule = "secret"];
}

message ReqModifyUserEmail {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 邮箱
  string email = 2 [(validator) = "required,email", (mask).rule = "email"];
  // 2FA令牌
  string tfa_token = 3 [(validator) = "required"];
  // 语言
  string lang = 4 [(validator) = "required"];
}

message RspModifyUserEmail {
  // 发送结果
  notify.DeliveryResult result = 1;
}

message ReqActiveUserEmail {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 激活邮件参数
  string value = 2 [(validator) = "required"];
}

message ReqCreateBindUserWeixinQrcode {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required"];
  // 语言
  string lang = 3 [(validator) = "required"];
}

message RspCreateBindUserWeixinQrcode {
  // 二维码信息
  WeixinQrcode qrcode = 1;
}

message ReqGetBindUserWeixinState {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 场景值
  string scene_str = 2 [(validator) = "required"];
}

message RspGetBindUserWeixinState {
  // 状态
  BindWeixinState state = 1;
}

message ReqBindUserWeixin {
  // OpenID
  string open_id = 1 [(validator) = "required"];
  // 二维码场景值
  string scene_str = 2 [(validator) = "required"];
}

message ReqBindUserWeixinByOauth2 {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 微信回调code
  string code = 2 [(validator) = "required"];
  // 2FA令牌
  string tfa_token = 3 [(validator) = "required"];
}

message ReqUnbindUserWeixin {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required"];
}

message ReqServeWeixinOfficialAccount {
  // 请求方法
  string method = 1;
  // 请求query参数
  map<string, string> query = 2;
  // 请求body
  bytes body = 3;
}

message RspServeWeixinOfficialAccount {
  // 响应类型
  string content_type = 1;
  // 响应体
  bytes body = 2;
}

message ReqGetGoogleAuthUrl {
  // 场景
  GoogleAuthScene scene = 1 [(validator) = "required"];
  // state参数
  string state = 2 [(validator) = "required"];
}

message RspGetGoogleAuthUrl {
  // 认证URL
  string url = 1;
}

message ReqBindUserGoogle {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 谷歌回调code
  string code = 2 [(validator) = "required", (mask).rule = "secret"];
  // 2FA令牌
  string tfa_token = 3 [(validator) = "required"];
}

message ReqUnbindUserGoogle {
  // 用户ID
  uint64 user_id = 1 [(validator) = "required"];
  // 2FA令牌
  string tfa_token = 2 [(validator) = "required"];
}

message ReqMakeUniqueUsername {
  // 用户名
  string username = 1 [(validator) = "required", (mask).rule = "username"];
}

message RspMakeUniqueUsername {
  // 用户名
  string username = 1 [(mask).rule = "username"];
}

message ReqDisableUser {
  // 用户ID
  repeated uint64 user_id = 1 [(validator) = "required,dive,required"];
}

message RspDisableUser {
  message Result {
    int32 code = 1;
    uint64 id = 2;
    string username = 3;
  }
  repeated Result results = 1;
}

message ReqEnableUser {
  // 用户ID
  repeated uint64 user_id = 1 [(validator) = "required,dive,required"];
}

message RspEnableUser {
  message Result {
    int32 code = 1;
    uint64 id = 2;
    string username = 3;
  }
  repeated Result results = 1;
}

message ReqGetUserIds {
  string username = 1 [(validator) = "required"];
  // 是否精确匹配
  bool exact_match = 2;
  // 地域
  base.Region region = 3 [(validator) = "required"];
}

message RspGetUserIds {
  repeated uint64 ids = 1;
}

message ReqGetOauthClient {
  // 客户端ID
  repeated string client_id = 1 [(validator) = "required,dive,required"];
}

message RspGetOauthClient {
  // 客户端信息
  repeated OauthClient clients = 1;
}

// Iam服务
service IamService {
  // 获取用户分页
  rpc GetUsersPage(ReqGetUsersPage) returns (RspGetUsersPage);
  // 通过主键查询用户（该接口会查询国际用户）
  rpc GetUsersByKey(ReqGetUsersByKey) returns (RspGetUsersByKey);
  // 通过unionid查询用户（该接口查询国内国际用户）
  rpc GetUserByUnionID(ReqGetUserByUnionID) returns (RspGetUserByUnionID);
  // 检查API权限
  rpc CheckApiPermission(ReqCheckApiPermission) returns (RspCheckApiPermission);
  // 检查登录凭证
  rpc ValidateLoginCredentials(ReqValidateLoginCredentials) returns (RspValidateLoginCredentials);
  // 绑定用户的第三方登录索引
  rpc BindUserThirdIndex(ReqBindUserThirdIndex) returns (google.protobuf.Empty);
  // 更新用户信息
  rpc UpdateUserInfo(ReqUpdateUserInfo) returns (google.protobuf.Empty);
  // 查询IP属地
  rpc GetIpRegion(ReqGetIpRegion) returns (RspGetIpRegion);
  // 更新用户信息（仅用于团队创建成功）
  rpc UpdateUserWhenTeamCreated(ReqUpdateUserWhenTeamCreated) returns (google.protobuf.Empty);
  // 检查用户是否拥有手机
  rpc CheckUserHavePhone(ReqCheckUserHavePhone) returns (RspCheckUserHavePhone);

  // 创建一次性密码
  rpc CreateOneTimePassword(ReqCreateOneTimePassword) returns (RspCreateOneTimePassword);
  // 校验一次性密码
  rpc ValidateOneTimePassword(ReqValidateOneTimePassword) returns (google.protobuf.Empty);
  // 创建用户的二次验证
  rpc CreateUserTfa(ReqCreateUserTfa) returns (RspCreateUserTfa);
  // 查询用户的二次验证
  rpc GetUserTfa(ReqGetUserTfa) returns (RspGetUserTfa);
  // 创建微信二次验证二维码
  rpc CreateWeixinTfaQrcode(ReqCreateWeixinTfaQrcode) returns (RspCreateWeixinTfaQrcode);
  // 创建微信二次验证（供v1使用）
  rpc CreateWeixinTfa(ReqCreateWeixinTfa) returns (google.protobuf.Empty);
  // 查询创建微信二次验证状态
  rpc GetCreateWeixinTfaState(ReqGetCreateWeixinTfaState) returns (RspGetCreateWeixinTfaState);
  // 校验二次验证（仅v1使用）
  rpc ValidateTwoFactorAuth(ReqValidateTwoFactorAuth) returns (google.protobuf.Empty);
  // 修改用户的用户名
  rpc ModifyUserUsername(ReqModifyUserUsername) returns (google.protobuf.Empty);
  // 修改用户的密码
  rpc ModifyUserPassword(ReqModifyUserPassword) returns (google.protobuf.Empty);
  // 修改用户的手机号
  rpc ModifyUserPhone(ReqModifyUserPhone) returns (google.protobuf.Empty);
  // 修改用户的邮箱
  rpc ModifyUserEmail(ReqModifyUserEmail) returns (RspModifyUserEmail);
  // 激活用户的邮箱
  rpc ActiveUserEmail(ReqActiveUserEmail) returns (google.protobuf.Empty);
  // 创建绑定用户微信的二维码
  rpc CreateBindUserWeixinQrcode(ReqCreateBindUserWeixinQrcode) returns (RspCreateBindUserWeixinQrcode);
  // 查询绑定用户微信的状态
  rpc GetBindUserWeixinState(ReqGetBindUserWeixinState) returns (RspGetBindUserWeixinState);
  // 绑定用户微信（供v1使用）
  rpc BindUserWeixin(ReqBindUserWeixin) returns (google.protobuf.Empty);
  // 绑定用户微信（在微信浏览器中）
  rpc BindUserWeixinByOauth2(ReqBindUserWeixinByOauth2) returns (google.protobuf.Empty);
  // 解绑用户微信
  rpc UnbindUserWeixin(ReqUnbindUserWeixin) returns (google.protobuf.Empty);
  // 微信公众号服务端
  rpc ServeWeixinOfficialAccount(ReqServeWeixinOfficialAccount) returns (RspServeWeixinOfficialAccount);
  // 获取谷歌认证URL
  rpc GetGoogleAuthUrl(ReqGetGoogleAuthUrl) returns (RspGetGoogleAuthUrl);
  // 绑定用户谷歌账号
  rpc BindUserGoogle(ReqBindUserGoogle) returns (google.protobuf.Empty);
  // 解绑用户谷歌账号
  rpc UnbindUserGoogle(ReqUnbindUserGoogle) returns (google.protobuf.Empty);
  // 生成唯一用户名（v1调用）
  rpc MakeUniqueUsername(ReqMakeUniqueUsername) returns (RspMakeUniqueUsername);
  // 冻结用户
  rpc DisableUser(ReqDisableUser) returns (RspDisableUser);
  // 解冻用户
  rpc EnableUser(ReqEnableUser) returns (RspEnableUser);

  // 获取用户id
  rpc GetUserIds(ReqGetUserIds) returns (RspGetUserIds);

  // GetOauthClient 获取OAuth客户端
  rpc GetOauthClient(ReqGetOauthClient) returns (RspGetOauthClient);
}
