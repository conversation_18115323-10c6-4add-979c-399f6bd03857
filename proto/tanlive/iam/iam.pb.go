// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/iam/iam.proto

package iam

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户状态
type UserState int32

const (
	UserState_USER_STATE_UNSPECIFIED UserState = 0
	// 正常
	UserState_USER_STATE_VALID UserState = 1
	// 冻结
	UserState_USER_STATE_FROZEN UserState = 2
)

// Enum value maps for UserState.
var (
	UserState_name = map[int32]string{
		0: "USER_STATE_UNSPECIFIED",
		1: "USER_STATE_VALID",
		2: "USER_STATE_FROZEN",
	}
	UserState_value = map[string]int32{
		"USER_STATE_UNSPECIFIED": 0,
		"USER_STATE_VALID":       1,
		"USER_STATE_FROZEN":      2,
	}
)

func (x UserState) Enum() *UserState {
	p := new(UserState)
	*p = x
	return p
}

func (x UserState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserState) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_iam_iam_proto_enumTypes[0].Descriptor()
}

func (UserState) Type() protoreflect.EnumType {
	return &file_tanlive_iam_iam_proto_enumTypes[0]
}

func (x UserState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserState.Descriptor instead.
func (UserState) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{0}
}

// 用户索引类型
type UserIndexType int32

const (
	UserIndexType_USER_INDEX_TYPE_UNSPECIFIED UserIndexType = 0
	// 邮箱
	UserIndexType_USER_INDEX_TYPE_EMAIL UserIndexType = 1
	// 手机号
	UserIndexType_USER_INDEX_TYPE_PHONE UserIndexType = 2
	// 用户名
	UserIndexType_USER_INDEX_TYPE_USERNAME UserIndexType = 3
	// 微信
	UserIndexType_USER_INDEX_TYPE_WECHAT UserIndexType = 4
	// QQ
	// USER_INDEX_TYPE_QQ = 5;
	// SaaS
	// USER_INDEX_TYPE_SAAS = 6;
	// google
	UserIndexType_USER_INDEX_TYPE_GOOGLE UserIndexType = 7
)

// Enum value maps for UserIndexType.
var (
	UserIndexType_name = map[int32]string{
		0: "USER_INDEX_TYPE_UNSPECIFIED",
		1: "USER_INDEX_TYPE_EMAIL",
		2: "USER_INDEX_TYPE_PHONE",
		3: "USER_INDEX_TYPE_USERNAME",
		4: "USER_INDEX_TYPE_WECHAT",
		7: "USER_INDEX_TYPE_GOOGLE",
	}
	UserIndexType_value = map[string]int32{
		"USER_INDEX_TYPE_UNSPECIFIED": 0,
		"USER_INDEX_TYPE_EMAIL":       1,
		"USER_INDEX_TYPE_PHONE":       2,
		"USER_INDEX_TYPE_USERNAME":    3,
		"USER_INDEX_TYPE_WECHAT":      4,
		"USER_INDEX_TYPE_GOOGLE":      7,
	}
)

func (x UserIndexType) Enum() *UserIndexType {
	p := new(UserIndexType)
	*p = x
	return p
}

func (x UserIndexType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserIndexType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_iam_iam_proto_enumTypes[1].Descriptor()
}

func (UserIndexType) Type() protoreflect.EnumType {
	return &file_tanlive_iam_iam_proto_enumTypes[1]
}

func (x UserIndexType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserIndexType.Descriptor instead.
func (UserIndexType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{1}
}

// 用户身份
type IdentitySet int32

const (
	IdentitySet_IDENTITY_SET_UNSPECIFIED IdentitySet = 0
	// 用户
	IdentitySet_IDENTITY_SET_USER IdentitySet = 1
	// 团队
	IdentitySet_IDENTITY_SET_TEAM IdentitySet = 2
)

// Enum value maps for IdentitySet.
var (
	IdentitySet_name = map[int32]string{
		0: "IDENTITY_SET_UNSPECIFIED",
		1: "IDENTITY_SET_USER",
		2: "IDENTITY_SET_TEAM",
	}
	IdentitySet_value = map[string]int32{
		"IDENTITY_SET_UNSPECIFIED": 0,
		"IDENTITY_SET_USER":        1,
		"IDENTITY_SET_TEAM":        2,
	}
)

func (x IdentitySet) Enum() *IdentitySet {
	p := new(IdentitySet)
	*p = x
	return p
}

func (x IdentitySet) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentitySet) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_iam_iam_proto_enumTypes[2].Descriptor()
}

func (IdentitySet) Type() protoreflect.EnumType {
	return &file_tanlive_iam_iam_proto_enumTypes[2]
}

func (x IdentitySet) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentitySet.Descriptor instead.
func (IdentitySet) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{2}
}

// 一次性密码使用场景（兼容老版的枚举值）
type OtpScene int32

const (
	OtpScene_OTP_SCENE_UNSPECIFIED OtpScene = 0
	// 注册
	OtpScene_OTP_SCENE_REGISTER OtpScene = 1
	// 修改手机号
	OtpScene_OTP_SCENE_MODIFY_PHONE OtpScene = 2
	// 忘记密码
	OtpScene_OTP_SCENE_FORGET_PASSWORD OtpScene = 4
	// 团队企业邮箱验证
	OtpScene_OTP_SCENE_TEAM_WORK_EMAIL_VERIFY OtpScene = 6
	// 登录场景
	OtpScene_OTP_SCENE_LOGIN OtpScene = 7
	// 二次验证
	OtpScene_OTP_SCENE_2FA OtpScene = 8
	// 修改邮箱
	OtpScene_OTP_SCENE_MODIFY_EMAIL OtpScene = 9
)

// Enum value maps for OtpScene.
var (
	OtpScene_name = map[int32]string{
		0: "OTP_SCENE_UNSPECIFIED",
		1: "OTP_SCENE_REGISTER",
		2: "OTP_SCENE_MODIFY_PHONE",
		4: "OTP_SCENE_FORGET_PASSWORD",
		6: "OTP_SCENE_TEAM_WORK_EMAIL_VERIFY",
		7: "OTP_SCENE_LOGIN",
		8: "OTP_SCENE_2FA",
		9: "OTP_SCENE_MODIFY_EMAIL",
	}
	OtpScene_value = map[string]int32{
		"OTP_SCENE_UNSPECIFIED":            0,
		"OTP_SCENE_REGISTER":               1,
		"OTP_SCENE_MODIFY_PHONE":           2,
		"OTP_SCENE_FORGET_PASSWORD":        4,
		"OTP_SCENE_TEAM_WORK_EMAIL_VERIFY": 6,
		"OTP_SCENE_LOGIN":                  7,
		"OTP_SCENE_2FA":                    8,
		"OTP_SCENE_MODIFY_EMAIL":           9,
	}
)

func (x OtpScene) Enum() *OtpScene {
	p := new(OtpScene)
	*p = x
	return p
}

func (x OtpScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OtpScene) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_iam_iam_proto_enumTypes[3].Descriptor()
}

func (OtpScene) Type() protoreflect.EnumType {
	return &file_tanlive_iam_iam_proto_enumTypes[3]
}

func (x OtpScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OtpScene.Descriptor instead.
func (OtpScene) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{3}
}

// 2FA(2-Factor Authentication)类型
type TfaType int32

const (
	TfaType_TFA_TYPE_UNSPECIFIED TfaType = 0
	// 手机验证
	TfaType_TFA_TYPE_PHONE TfaType = 1
	// 密码验证
	TfaType_TFA_TYPE_PASSWORD TfaType = 2
	// 微信验证
	TfaType_TFA_TYPE_WECHAT TfaType = 3
	// 邮箱验证
	TfaType_TFA_TYPE_EMAIL TfaType = 4
)

// Enum value maps for TfaType.
var (
	TfaType_name = map[int32]string{
		0: "TFA_TYPE_UNSPECIFIED",
		1: "TFA_TYPE_PHONE",
		2: "TFA_TYPE_PASSWORD",
		3: "TFA_TYPE_WECHAT",
		4: "TFA_TYPE_EMAIL",
	}
	TfaType_value = map[string]int32{
		"TFA_TYPE_UNSPECIFIED": 0,
		"TFA_TYPE_PHONE":       1,
		"TFA_TYPE_PASSWORD":    2,
		"TFA_TYPE_WECHAT":      3,
		"TFA_TYPE_EMAIL":       4,
	}
)

func (x TfaType) Enum() *TfaType {
	p := new(TfaType)
	*p = x
	return p
}

func (x TfaType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TfaType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_iam_iam_proto_enumTypes[4].Descriptor()
}

func (TfaType) Type() protoreflect.EnumType {
	return &file_tanlive_iam_iam_proto_enumTypes[4]
}

func (x TfaType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TfaType.Descriptor instead.
func (TfaType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{4}
}

// 绑定微信状态
type BindWeixinState int32

const (
	BindWeixinState_BIND_WEIXIN_STATE_UNSPECIFIED BindWeixinState = 0
	// 等待扫码
	BindWeixinState_BIND_WEIXIN_STATE_WAITING_SCAN BindWeixinState = 1
	// 绑定成功
	BindWeixinState_BIND_WEIXIN_STATE_SUCCESS BindWeixinState = 2
	// 当前微信已绑定其他账号
	BindWeixinState_BIND_WEIXIN_STATE_BOUND_OTHER_ACCOUNT BindWeixinState = 3
)

// Enum value maps for BindWeixinState.
var (
	BindWeixinState_name = map[int32]string{
		0: "BIND_WEIXIN_STATE_UNSPECIFIED",
		1: "BIND_WEIXIN_STATE_WAITING_SCAN",
		2: "BIND_WEIXIN_STATE_SUCCESS",
		3: "BIND_WEIXIN_STATE_BOUND_OTHER_ACCOUNT",
	}
	BindWeixinState_value = map[string]int32{
		"BIND_WEIXIN_STATE_UNSPECIFIED":         0,
		"BIND_WEIXIN_STATE_WAITING_SCAN":        1,
		"BIND_WEIXIN_STATE_SUCCESS":             2,
		"BIND_WEIXIN_STATE_BOUND_OTHER_ACCOUNT": 3,
	}
)

func (x BindWeixinState) Enum() *BindWeixinState {
	p := new(BindWeixinState)
	*p = x
	return p
}

func (x BindWeixinState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BindWeixinState) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_iam_iam_proto_enumTypes[5].Descriptor()
}

func (BindWeixinState) Type() protoreflect.EnumType {
	return &file_tanlive_iam_iam_proto_enumTypes[5]
}

func (x BindWeixinState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BindWeixinState.Descriptor instead.
func (BindWeixinState) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{5}
}

// 创建微信二次验证状态
type CreateWeixinTfaState int32

const (
	CreateWeixinTfaState_CREATE_WEIXIN_TFA_STATE_UNSPECIFIED CreateWeixinTfaState = 0
	// 等待扫码
	CreateWeixinTfaState_CREATE_WEIXIN_TFA_STATE_WAITING_SCAN CreateWeixinTfaState = 1
	// 创建成功
	CreateWeixinTfaState_CREATE_WEIXIN_TFA_STATE_SUCCESS CreateWeixinTfaState = 2
)

// Enum value maps for CreateWeixinTfaState.
var (
	CreateWeixinTfaState_name = map[int32]string{
		0: "CREATE_WEIXIN_TFA_STATE_UNSPECIFIED",
		1: "CREATE_WEIXIN_TFA_STATE_WAITING_SCAN",
		2: "CREATE_WEIXIN_TFA_STATE_SUCCESS",
	}
	CreateWeixinTfaState_value = map[string]int32{
		"CREATE_WEIXIN_TFA_STATE_UNSPECIFIED":  0,
		"CREATE_WEIXIN_TFA_STATE_WAITING_SCAN": 1,
		"CREATE_WEIXIN_TFA_STATE_SUCCESS":      2,
	}
)

func (x CreateWeixinTfaState) Enum() *CreateWeixinTfaState {
	p := new(CreateWeixinTfaState)
	*p = x
	return p
}

func (x CreateWeixinTfaState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateWeixinTfaState) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_iam_iam_proto_enumTypes[6].Descriptor()
}

func (CreateWeixinTfaState) Type() protoreflect.EnumType {
	return &file_tanlive_iam_iam_proto_enumTypes[6]
}

func (x CreateWeixinTfaState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateWeixinTfaState.Descriptor instead.
func (CreateWeixinTfaState) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{6}
}

// 谷歌认证场景
type GoogleAuthScene int32

const (
	GoogleAuthScene_GOOGLE_AUTH_SCENE_UNSPECIFIED GoogleAuthScene = 0
	// 谷歌登录
	GoogleAuthScene_GOOGLE_AUTH_SCENE_LOGIN GoogleAuthScene = 1
	// 绑定谷歌账号
	GoogleAuthScene_GOOGLE_AUTH_SCENE_BIND GoogleAuthScene = 2
	// 创建谷歌二次验证
	GoogleAuthScene_GOOGLE_AUTH_SCENE_2FA GoogleAuthScene = 3
)

// Enum value maps for GoogleAuthScene.
var (
	GoogleAuthScene_name = map[int32]string{
		0: "GOOGLE_AUTH_SCENE_UNSPECIFIED",
		1: "GOOGLE_AUTH_SCENE_LOGIN",
		2: "GOOGLE_AUTH_SCENE_BIND",
		3: "GOOGLE_AUTH_SCENE_2FA",
	}
	GoogleAuthScene_value = map[string]int32{
		"GOOGLE_AUTH_SCENE_UNSPECIFIED": 0,
		"GOOGLE_AUTH_SCENE_LOGIN":       1,
		"GOOGLE_AUTH_SCENE_BIND":        2,
		"GOOGLE_AUTH_SCENE_2FA":         3,
	}
)

func (x GoogleAuthScene) Enum() *GoogleAuthScene {
	p := new(GoogleAuthScene)
	*p = x
	return p
}

func (x GoogleAuthScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoogleAuthScene) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_iam_iam_proto_enumTypes[7].Descriptor()
}

func (GoogleAuthScene) Type() protoreflect.EnumType {
	return &file_tanlive_iam_iam_proto_enumTypes[7]
}

func (x GoogleAuthScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoogleAuthScene.Descriptor instead.
func (GoogleAuthScene) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{7}
}

// 用户信息
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户名
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// 头像
	Image string `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	// 团队ID
	FirmId uint64 `protobuf:"varint,4,opt,name=firm_id,json=firmId,proto3" json:"firm_id,omitempty"`
	// 身份
	IdentitySet base.IdentityType `protobuf:"varint,5,opt,name=identity_set,json=identitySet,proto3,enum=tanlive.base.IdentityType" json:"identity_set,omitempty"`
	// 等级
	Level string `protobuf:"bytes,6,opt,name=level,proto3" json:"level,omitempty"`
	// 国家
	Country string `protobuf:"bytes,7,opt,name=country,proto3" json:"country,omitempty"`
	// 省
	Province string `protobuf:"bytes,8,opt,name=province,proto3" json:"province,omitempty"`
	// 市
	City string `protobuf:"bytes,9,opt,name=city,proto3" json:"city,omitempty"`
	// 地区编码
	RegionCode string `protobuf:"bytes,10,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	// 时区
	Timezone string `protobuf:"bytes,11,opt,name=timezone,proto3" json:"timezone,omitempty"`
	// 微信unionID
	UnionId string `protobuf:"bytes,12,opt,name=union_id,json=unionId,proto3" json:"union_id,omitempty"`
	// 手机号
	Phone string `protobuf:"bytes,13,opt,name=phone,proto3" json:"phone,omitempty"`
	// 手机号哈希
	PhoneHash string `protobuf:"bytes,14,opt,name=phone_hash,json=phoneHash,proto3" json:"phone_hash,omitempty"`
	// 用户昵称
	NickName string `protobuf:"bytes,15,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserInfo) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UserInfo) GetFirmId() uint64 {
	if x != nil {
		return x.FirmId
	}
	return 0
}

func (x *UserInfo) GetIdentitySet() base.IdentityType {
	if x != nil {
		return x.IdentitySet
	}
	return base.IdentityType(0)
}

func (x *UserInfo) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *UserInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *UserInfo) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *UserInfo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *UserInfo) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *UserInfo) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *UserInfo) GetUnionId() string {
	if x != nil {
		return x.UnionId
	}
	return ""
}

func (x *UserInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfo) GetPhoneHash() string {
	if x != nil {
		return x.PhoneHash
	}
	return ""
}

func (x *UserInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

// 第三方用户信息
type ThirdUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId         string        `protobuf:"bytes,1,opt,name=open_id,json=openID,proto3" json:"open_id,omitempty"`
	UnionId        string        `protobuf:"bytes,2,opt,name=union_id,proto3" json:"union_id,omitempty"`
	IdentifierType UserIndexType `protobuf:"varint,3,opt,name=identifier_type,json=type,proto3,enum=tanlive.iam.UserIndexType" json:"identifier_type,omitempty"`
	ExpiresIn      int32         `protobuf:"varint,4,opt,name=expires_in,json=expiresIn,proto3" json:"expires_in,omitempty"`
	Timestamp      int64         `protobuf:"varint,5,opt,name=timestamp,json=timeStamp,proto3" json:"timestamp,omitempty"`
	Nickname       string        `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadImgUrl     string        `protobuf:"bytes,7,opt,name=head_img_url,json=headImgURL,proto3" json:"head_img_url,omitempty"`
	SendPassword   string        `protobuf:"bytes,8,opt,name=send_password,json=sendPassWord,proto3" json:"send_password,omitempty"`
	Username       string        `protobuf:"bytes,9,opt,name=username,proto3" json:"username,omitempty"`
}

func (x *ThirdUserInfo) Reset() {
	*x = ThirdUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdUserInfo) ProtoMessage() {}

func (x *ThirdUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdUserInfo.ProtoReflect.Descriptor instead.
func (*ThirdUserInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{1}
}

func (x *ThirdUserInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ThirdUserInfo) GetUnionId() string {
	if x != nil {
		return x.UnionId
	}
	return ""
}

func (x *ThirdUserInfo) GetIdentifierType() UserIndexType {
	if x != nil {
		return x.IdentifierType
	}
	return UserIndexType_USER_INDEX_TYPE_UNSPECIFIED
}

func (x *ThirdUserInfo) GetExpiresIn() int32 {
	if x != nil {
		return x.ExpiresIn
	}
	return 0
}

func (x *ThirdUserInfo) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ThirdUserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *ThirdUserInfo) GetHeadImgUrl() string {
	if x != nil {
		return x.HeadImgUrl
	}
	return ""
}

func (x *ThirdUserInfo) GetSendPassword() string {
	if x != nil {
		return x.SendPassword
	}
	return ""
}

func (x *ThirdUserInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

// IP属地
type IpRegion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Country     string `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	Province    string `protobuf:"bytes,2,opt,name=province,proto3" json:"province,omitempty"`
	City        string `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`
	District    string `protobuf:"bytes,4,opt,name=district,proto3" json:"district,omitempty"`
	Isp         string `protobuf:"bytes,5,opt,name=isp,proto3" json:"isp,omitempty"`
	BackboneIsp string `protobuf:"bytes,6,opt,name=backbone_isp,json=backboneIsp,proto3" json:"backbone_isp,omitempty"`
}

func (x *IpRegion) Reset() {
	*x = IpRegion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IpRegion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpRegion) ProtoMessage() {}

func (x *IpRegion) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpRegion.ProtoReflect.Descriptor instead.
func (*IpRegion) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{2}
}

func (x *IpRegion) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *IpRegion) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *IpRegion) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *IpRegion) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *IpRegion) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *IpRegion) GetBackboneIsp() string {
	if x != nil {
		return x.BackboneIsp
	}
	return ""
}

// 一次性密码
type Otp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 密码
	Password string `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
	// 有效期
	Ttl *durationpb.Duration `protobuf:"bytes,2,opt,name=ttl,proto3" json:"ttl,omitempty"`
}

func (x *Otp) Reset() {
	*x = Otp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Otp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Otp) ProtoMessage() {}

func (x *Otp) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Otp.ProtoReflect.Descriptor instead.
func (*Otp) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{3}
}

func (x *Otp) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Otp) GetTtl() *durationpb.Duration {
	if x != nil {
		return x.Ttl
	}
	return nil
}

// 一次性密码接收者
type OtpReceiver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 接收人
	//
	// Types that are assignable to Receiver:
	//
	//	*OtpReceiver_Email
	//	*OtpReceiver_Phone
	//	*OtpReceiver_EmailByUserId
	//	*OtpReceiver_PhoneByUserId
	Receiver isOtpReceiver_Receiver `protobuf_oneof:"receiver"`
}

func (x *OtpReceiver) Reset() {
	*x = OtpReceiver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OtpReceiver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OtpReceiver) ProtoMessage() {}

func (x *OtpReceiver) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OtpReceiver.ProtoReflect.Descriptor instead.
func (*OtpReceiver) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{4}
}

func (m *OtpReceiver) GetReceiver() isOtpReceiver_Receiver {
	if m != nil {
		return m.Receiver
	}
	return nil
}

func (x *OtpReceiver) GetEmail() string {
	if x, ok := x.GetReceiver().(*OtpReceiver_Email); ok {
		return x.Email
	}
	return ""
}

func (x *OtpReceiver) GetPhone() string {
	if x, ok := x.GetReceiver().(*OtpReceiver_Phone); ok {
		return x.Phone
	}
	return ""
}

func (x *OtpReceiver) GetEmailByUserId() uint64 {
	if x, ok := x.GetReceiver().(*OtpReceiver_EmailByUserId); ok {
		return x.EmailByUserId
	}
	return 0
}

func (x *OtpReceiver) GetPhoneByUserId() uint64 {
	if x, ok := x.GetReceiver().(*OtpReceiver_PhoneByUserId); ok {
		return x.PhoneByUserId
	}
	return 0
}

type isOtpReceiver_Receiver interface {
	isOtpReceiver_Receiver()
}

type OtpReceiver_Email struct {
	// 邮箱地址
	Email string `protobuf:"bytes,1,opt,name=email,proto3,oneof"`
}

type OtpReceiver_Phone struct {
	// 手机号码
	Phone string `protobuf:"bytes,2,opt,name=phone,proto3,oneof"`
}

type OtpReceiver_EmailByUserId struct {
	// 用户邮箱
	EmailByUserId uint64 `protobuf:"varint,3,opt,name=email_by_user_id,json=emailByUserId,proto3,oneof"`
}

type OtpReceiver_PhoneByUserId struct {
	// 用户手机号
	PhoneByUserId uint64 `protobuf:"varint,4,opt,name=phone_by_user_id,json=phoneByUserId,proto3,oneof"`
}

func (*OtpReceiver_Email) isOtpReceiver_Receiver() {}

func (*OtpReceiver_Phone) isOtpReceiver_Receiver() {}

func (*OtpReceiver_EmailByUserId) isOtpReceiver_Receiver() {}

func (*OtpReceiver_PhoneByUserId) isOtpReceiver_Receiver() {}

// 2FA(2-Factor Authentication) Token
type TfaToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 认证TOKEN
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 有效期
	Ttl *durationpb.Duration `protobuf:"bytes,2,opt,name=ttl,proto3" json:"ttl,omitempty"`
}

func (x *TfaToken) Reset() {
	*x = TfaToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TfaToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TfaToken) ProtoMessage() {}

func (x *TfaToken) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TfaToken.ProtoReflect.Descriptor instead.
func (*TfaToken) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{5}
}

func (x *TfaToken) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TfaToken) GetTtl() *durationpb.Duration {
	if x != nil {
		return x.Ttl
	}
	return nil
}

// 微信二维码信息
type WeixinQrcode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 获取的二维码ticket
	Ticket string `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	// 二维码解析后的URL
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// 过期时间（单位秒）
	ExpiresIn int32 `protobuf:"varint,3,opt,name=expires_in,json=expiresIn,proto3" json:"expires_in,omitempty"`
	// 场景值
	SceneStr string `protobuf:"bytes,4,opt,name=scene_str,json=sceneStr,proto3" json:"scene_str,omitempty"`
}

func (x *WeixinQrcode) Reset() {
	*x = WeixinQrcode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeixinQrcode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeixinQrcode) ProtoMessage() {}

func (x *WeixinQrcode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeixinQrcode.ProtoReflect.Descriptor instead.
func (*WeixinQrcode) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{6}
}

func (x *WeixinQrcode) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

func (x *WeixinQrcode) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *WeixinQrcode) GetExpiresIn() int32 {
	if x != nil {
		return x.ExpiresIn
	}
	return 0
}

func (x *WeixinQrcode) GetSceneStr() string {
	if x != nil {
		return x.SceneStr
	}
	return ""
}

// 用户卡片
type UserCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户名
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// 头像
	Image string `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	// 等级
	Level string `protobuf:"bytes,4,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *UserCard) Reset() {
	*x = UserCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCard) ProtoMessage() {}

func (x *UserCard) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCard.ProtoReflect.Descriptor instead.
func (*UserCard) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{7}
}

func (x *UserCard) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserCard) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserCard) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UserCard) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

// OAuth客户端
type OauthClient struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 客户端名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// logo
	LogoUrl string `protobuf:"bytes,2,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 客户端ID
	ClientId string `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 客户端密钥
	ClientSecret string `protobuf:"bytes,4,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
}

func (x *OauthClient) Reset() {
	*x = OauthClient{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_iam_iam_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OauthClient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OauthClient) ProtoMessage() {}

func (x *OauthClient) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_iam_iam_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OauthClient.ProtoReflect.Descriptor instead.
func (*OauthClient) Descriptor() ([]byte, []int) {
	return file_tanlive_iam_iam_proto_rawDescGZIP(), []int{8}
}

func (x *OauthClient) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OauthClient) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *OauthClient) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *OauthClient) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

var File_tanlive_iam_iam_proto protoreflect.FileDescriptor

var file_tanlive_iam_iam_proto_rawDesc = []byte{
	0x0a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x69, 0x61,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x69, 0x61, 0x6d, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbe, 0x03, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x2a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x8a, 0x88, 0x27, 0x0a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x66, 0x69, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x6e, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x69, 0x63, 0x6b,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xbb, 0x02, 0x0a, 0x0d, 0x54, 0x68, 0x69, 0x72, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x0f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x73, 0x5f, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x73, 0x49, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0c, 0x68, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x49, 0x6d, 0x67, 0x55,
	0x52, 0x4c, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x6e, 0x64, 0x50,
	0x61, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x08, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x63, 0x6b,
	0x62, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x73, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x62, 0x61, 0x63, 0x6b, 0x62, 0x6f, 0x6e, 0x65, 0x49, 0x73, 0x70, 0x22, 0x5c, 0x0a, 0x03, 0x4f,
	0x74, 0x70, 0x12, 0x28, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x2b, 0x0a, 0x03,
	0x74, 0x74, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x22, 0xf9, 0x01, 0x0a, 0x0b, 0x4f, 0x74,
	0x70, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x8a, 0x88, 0x27, 0x07,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x35, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1d, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x8a, 0x88, 0x27, 0x07, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x48, 0x00,
	0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x37, 0x0a, 0x10, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x62, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48,
	0x00, 0x52, 0x0d, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x37, 0x0a, 0x10, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x62, 0x79, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x22, 0x4d, 0x0a, 0x08, 0x54, 0x66, 0x61, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2b, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x03, 0x74, 0x74, 0x6c, 0x22, 0x74, 0x0a, 0x0c, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x51, 0x72,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x49, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x53, 0x74, 0x72, 0x22, 0x62, 0x0a, 0x08, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x8c,
	0x01, 0x0a, 0x0b, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0d, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0c, 0x8a, 0x88, 0x27, 0x08, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52,
	0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x2a, 0x54, 0x0a,
	0x09, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x5a, 0x45,
	0x4e, 0x10, 0x02, 0x2a, 0xbc, 0x01, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x44, 0x45, 0x58, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49,
	0x4e, 0x44, 0x45, 0x58, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10,
	0x01, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x45,
	0x43, 0x48, 0x41, 0x54, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49,
	0x4e, 0x44, 0x45, 0x58, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45,
	0x10, 0x07, 0x2a, 0x59, 0x0a, 0x0b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x65,
	0x74, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x45,
	0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x15, 0x0a, 0x11, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x54, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x54, 0x59, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x10, 0x02, 0x2a, 0xe2, 0x01,
	0x0a, 0x08, 0x4f, 0x74, 0x70, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x54,
	0x50, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x45,
	0x4e, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1a, 0x0a,
	0x16, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46,
	0x59, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x54, 0x50,
	0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x04, 0x12, 0x24, 0x0a, 0x20, 0x4f, 0x54, 0x50, 0x5f,
	0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x10, 0x06, 0x12, 0x13,
	0x0a, 0x0f, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x4c, 0x4f, 0x47, 0x49,
	0x4e, 0x10, 0x07, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45,
	0x5f, 0x32, 0x46, 0x41, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43,
	0x45, 0x4e, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x10, 0x09, 0x2a, 0x77, 0x0a, 0x07, 0x54, 0x66, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x14, 0x54, 0x46, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x46, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x54,
	0x46, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44,
	0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x46, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57,
	0x45, 0x43, 0x48, 0x41, 0x54, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x46, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x04, 0x2a, 0xa2, 0x01, 0x0a, 0x0f,
	0x42, 0x69, 0x6e, 0x64, 0x57, 0x65, 0x69, 0x78, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x21, 0x0a, 0x1d, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x57, 0x45, 0x49, 0x58, 0x49, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x57, 0x45, 0x49, 0x58, 0x49,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x43, 0x41, 0x4e, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x57,
	0x45, 0x49, 0x58, 0x49, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x57, 0x45,
	0x49, 0x58, 0x49, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x44,
	0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x03,
	0x2a, 0x8e, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x69, 0x78, 0x69,
	0x6e, 0x54, 0x66, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x58, 0x49, 0x4e, 0x5f, 0x54, 0x46, 0x41, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x45, 0x49,
	0x58, 0x49, 0x4e, 0x5f, 0x54, 0x46, 0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x41,
	0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x58, 0x49, 0x4e, 0x5f, 0x54, 0x46,
	0x41, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10,
	0x02, 0x2a, 0x88, 0x01, 0x0a, 0x0f, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x4f, 0x4f, 0x47,
	0x4c, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x4c, 0x4f,
	0x47, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x10,
	0x02, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x32, 0x46, 0x41, 0x10, 0x03, 0x42, 0x3d, 0x5a, 0x3b,
	0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_tanlive_iam_iam_proto_rawDescOnce sync.Once
	file_tanlive_iam_iam_proto_rawDescData = file_tanlive_iam_iam_proto_rawDesc
)

func file_tanlive_iam_iam_proto_rawDescGZIP() []byte {
	file_tanlive_iam_iam_proto_rawDescOnce.Do(func() {
		file_tanlive_iam_iam_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_iam_iam_proto_rawDescData)
	})
	return file_tanlive_iam_iam_proto_rawDescData
}

var file_tanlive_iam_iam_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_tanlive_iam_iam_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_tanlive_iam_iam_proto_goTypes = []interface{}{
	(UserState)(0),              // 0: tanlive.iam.UserState
	(UserIndexType)(0),          // 1: tanlive.iam.UserIndexType
	(IdentitySet)(0),            // 2: tanlive.iam.IdentitySet
	(OtpScene)(0),               // 3: tanlive.iam.OtpScene
	(TfaType)(0),                // 4: tanlive.iam.TfaType
	(BindWeixinState)(0),        // 5: tanlive.iam.BindWeixinState
	(CreateWeixinTfaState)(0),   // 6: tanlive.iam.CreateWeixinTfaState
	(GoogleAuthScene)(0),        // 7: tanlive.iam.GoogleAuthScene
	(*UserInfo)(nil),            // 8: tanlive.iam.UserInfo
	(*ThirdUserInfo)(nil),       // 9: tanlive.iam.ThirdUserInfo
	(*IpRegion)(nil),            // 10: tanlive.iam.IpRegion
	(*Otp)(nil),                 // 11: tanlive.iam.Otp
	(*OtpReceiver)(nil),         // 12: tanlive.iam.OtpReceiver
	(*TfaToken)(nil),            // 13: tanlive.iam.TfaToken
	(*WeixinQrcode)(nil),        // 14: tanlive.iam.WeixinQrcode
	(*UserCard)(nil),            // 15: tanlive.iam.UserCard
	(*OauthClient)(nil),         // 16: tanlive.iam.OauthClient
	(base.IdentityType)(0),      // 17: tanlive.base.IdentityType
	(*durationpb.Duration)(nil), // 18: google.protobuf.Duration
}
var file_tanlive_iam_iam_proto_depIdxs = []int32{
	17, // 0: tanlive.iam.UserInfo.identity_set:type_name -> tanlive.base.IdentityType
	1,  // 1: tanlive.iam.ThirdUserInfo.identifier_type:type_name -> tanlive.iam.UserIndexType
	18, // 2: tanlive.iam.Otp.ttl:type_name -> google.protobuf.Duration
	18, // 3: tanlive.iam.TfaToken.ttl:type_name -> google.protobuf.Duration
	4,  // [4:4] is the sub-list for method output_type
	4,  // [4:4] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_tanlive_iam_iam_proto_init() }
func file_tanlive_iam_iam_proto_init() {
	if File_tanlive_iam_iam_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_iam_iam_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_iam_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThirdUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_iam_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IpRegion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_iam_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Otp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_iam_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OtpReceiver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_iam_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TfaToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_iam_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeixinQrcode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_iam_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_iam_iam_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OauthClient); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tanlive_iam_iam_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*OtpReceiver_Email)(nil),
		(*OtpReceiver_Phone)(nil),
		(*OtpReceiver_EmailByUserId)(nil),
		(*OtpReceiver_PhoneByUserId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_iam_iam_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_iam_iam_proto_goTypes,
		DependencyIndexes: file_tanlive_iam_iam_proto_depIdxs,
		EnumInfos:         file_tanlive_iam_iam_proto_enumTypes,
		MessageInfos:      file_tanlive_iam_iam_proto_msgTypes,
	}.Build()
	File_tanlive_iam_iam_proto = out.File
	file_tanlive_iam_iam_proto_rawDesc = nil
	file_tanlive_iam_iam_proto_goTypes = nil
	file_tanlive_iam_iam_proto_depIdxs = nil
}
