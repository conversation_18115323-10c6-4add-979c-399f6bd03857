// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package iam

import (
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	proto "google.golang.org/protobuf/proto"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Email":         "required,email",
		"Phone":         "required,phone",
		"EmailByUserId": "required",
		"PhoneByUserId": "required",
	}, &OtpReceiver{})
}

func (x *OtpReceiver) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *UserInfo) MaskInLog() any {
	if x == nil {
		return (*UserInfo)(nil)
	}

	y := proto.Clone(x).(*UserInfo)
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *UserInfo) MaskInRpc() any {
	if x == nil {
		return (*UserInfo)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *UserInfo) MaskInBff() any {
	if x == nil {
		return (*UserInfo)(nil)
	}

	y := x
	y.Username = mask.Mask(y.Username, "username")

	return y
}

func (x *Otp) MaskInLog() any {
	if x == nil {
		return (*Otp)(nil)
	}

	y := proto.Clone(x).(*Otp)
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.Ttl).(interface{ MaskInLog() any }); ok {
		y.Ttl = v.MaskInLog().(*durationpb.Duration)
	}

	return y
}

func (x *Otp) MaskInRpc() any {
	if x == nil {
		return (*Otp)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.Ttl).(interface{ MaskInRpc() any }); ok {
		y.Ttl = v.MaskInRpc().(*durationpb.Duration)
	}

	return y
}

func (x *Otp) MaskInBff() any {
	if x == nil {
		return (*Otp)(nil)
	}

	y := x
	y.Password = mask.Mask(y.Password, "secret")
	if v, ok := any(y.Ttl).(interface{ MaskInBff() any }); ok {
		y.Ttl = v.MaskInBff().(*durationpb.Duration)
	}

	return y
}

func (x *OtpReceiver) MaskInLog() any {
	if x == nil {
		return (*OtpReceiver)(nil)
	}

	y := proto.Clone(x).(*OtpReceiver)
	switch v := y.Receiver.(type) {
	case *OtpReceiver_Email:
		v.Email = mask.Mask(v.Email, "email")
		y.Receiver = v
	case *OtpReceiver_Phone:
		v.Phone = mask.Mask(v.Phone, "phone")
		y.Receiver = v
	}

	return y
}

func (x *OtpReceiver) MaskInRpc() any {
	if x == nil {
		return (*OtpReceiver)(nil)
	}

	y := x
	switch v := y.Receiver.(type) {
	case *OtpReceiver_Email:
		v.Email = mask.Mask(v.Email, "email")
		y.Receiver = v
	case *OtpReceiver_Phone:
		v.Phone = mask.Mask(v.Phone, "phone")
		y.Receiver = v
	}

	return y
}

func (x *OtpReceiver) MaskInBff() any {
	if x == nil {
		return (*OtpReceiver)(nil)
	}

	y := x
	switch v := y.Receiver.(type) {
	case *OtpReceiver_Email:
		v.Email = mask.Mask(v.Email, "email")
		y.Receiver = v
	case *OtpReceiver_Phone:
		v.Phone = mask.Mask(v.Phone, "phone")
		y.Receiver = v
	}

	return y
}

func (x *TfaToken) MaskInLog() any {
	if x == nil {
		return (*TfaToken)(nil)
	}

	y := proto.Clone(x).(*TfaToken)
	if v, ok := any(y.Ttl).(interface{ MaskInLog() any }); ok {
		y.Ttl = v.MaskInLog().(*durationpb.Duration)
	}

	return y
}

func (x *TfaToken) MaskInRpc() any {
	if x == nil {
		return (*TfaToken)(nil)
	}

	y := x
	if v, ok := any(y.Ttl).(interface{ MaskInRpc() any }); ok {
		y.Ttl = v.MaskInRpc().(*durationpb.Duration)
	}

	return y
}

func (x *TfaToken) MaskInBff() any {
	if x == nil {
		return (*TfaToken)(nil)
	}

	y := x
	if v, ok := any(y.Ttl).(interface{ MaskInBff() any }); ok {
		y.Ttl = v.MaskInBff().(*durationpb.Duration)
	}

	return y
}

func (x *OauthClient) MaskInLog() any {
	if x == nil {
		return (*OauthClient)(nil)
	}

	y := proto.Clone(x).(*OauthClient)
	y.ClientSecret = mask.Mask(y.ClientSecret, "secret")

	return y
}

func (x *OauthClient) MaskInRpc() any {
	if x == nil {
		return (*OauthClient)(nil)
	}

	y := x
	y.ClientSecret = mask.Mask(y.ClientSecret, "secret")

	return y
}

func (x *OauthClient) MaskInBff() any {
	if x == nil {
		return (*OauthClient)(nil)
	}

	y := x
	y.ClientSecret = mask.Mask(y.ClientSecret, "secret")

	return y
}

func (x *Otp) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Ttl).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TfaToken) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Ttl).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
