syntax = "proto3";

package tanlive.iam;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam";

import "google/protobuf/duration.proto";
import "tanlive/base/base.proto";
import "tanlive/options.proto";

// 用户状态
enum UserState {
  USER_STATE_UNSPECIFIED = 0;
  // 正常
  USER_STATE_VALID = 1;
  // 冻结
  USER_STATE_FROZEN = 2;
}

// 用户索引类型
enum UserIndexType {
  USER_INDEX_TYPE_UNSPECIFIED = 0;
  // 邮箱
  USER_INDEX_TYPE_EMAIL = 1;
  // 手机号
  USER_INDEX_TYPE_PHONE = 2;
  // 用户名
  USER_INDEX_TYPE_USERNAME = 3;
  // 微信
  USER_INDEX_TYPE_WECHAT = 4;
  // QQ
  // USER_INDEX_TYPE_QQ = 5;
  // SaaS
  // USER_INDEX_TYPE_SAAS = 6;
  // google
  USER_INDEX_TYPE_GOOGLE = 7;
}

// 用户信息
message UserInfo {
  // 用户ID
  uint64 id = 1;
  // 用户名
  string username = 2 [(mask).rule = "username"];
  // 头像
  string image = 3;
  // 团队ID
  uint64 firm_id = 4;
  // 身份
  base.IdentityType identity_set = 5;
  // 等级
  string level = 6;
  // 国家
  string country = 7;
  // 省
  string province = 8;
  // 市
  string city = 9;
  // 地区编码
  string region_code = 10;
  // 时区
  string timezone = 11;
  // 微信unionID
  string union_id = 12;
  // 手机号
  string phone = 13;
  // 手机号哈希
  string phone_hash = 14;
  // 用户昵称
  string nick_name = 15;
}

// 第三方用户信息
message ThirdUserInfo {
  string open_id = 1 [json_name = "openID"];
  string union_id = 2 [json_name = "union_id"];
  UserIndexType identifier_type = 3 [json_name = "type"];
  int32 expires_in = 4 [json_name = "expiresIn"];
  int64 timestamp = 5 [json_name = "timeStamp"];
  string nickname = 6 [json_name = "nickname"];
  string head_img_url = 7 [json_name = "headImgURL"];
  string send_password = 8 [json_name = "sendPassWord"];
  string username = 9 [json_name = "username"];
}

// IP属地
message IpRegion {
  string country = 1;
  string province = 2;
  string city = 3;
  string district = 4;
  string isp = 5;
  string backbone_isp = 6;
}

// 用户身份
enum IdentitySet {
  IDENTITY_SET_UNSPECIFIED = 0;
  // 用户
  IDENTITY_SET_USER = 1;
  // 团队
  IDENTITY_SET_TEAM = 2;
}

// 一次性密码
message Otp {
  // 密码
  string password = 1 [(mask).rule = "secret"];
  // 有效期
  google.protobuf.Duration ttl = 2;
}

// 一次性密码使用场景（兼容老版的枚举值）
enum OtpScene {
  OTP_SCENE_UNSPECIFIED = 0;
  // 注册
  OTP_SCENE_REGISTER = 1;
  // 修改手机号
  OTP_SCENE_MODIFY_PHONE = 2;
  // 忘记密码
  OTP_SCENE_FORGET_PASSWORD = 4;
  // 团队企业邮箱验证
  OTP_SCENE_TEAM_WORK_EMAIL_VERIFY = 6;
  // 登录场景
  OTP_SCENE_LOGIN = 7;
  // 二次验证
  OTP_SCENE_2FA = 8;
  // 修改邮箱
  OTP_SCENE_MODIFY_EMAIL = 9;
}

// 一次性密码接收者
message OtpReceiver {
  // 接收人
  oneof receiver {
    // 邮箱地址
    string email = 1 [(validator) = "required,email", (mask).rule = "email"];
    // 手机号码
    string phone = 2 [(validator) = "required,phone", (mask).rule = "phone"];
    // 用户邮箱
    uint64 email_by_user_id = 3 [(validator) = "required"];
    // 用户手机号
    uint64 phone_by_user_id = 4 [(validator) = "required"];
  }
}

// 2FA(2-Factor Authentication) Token
message TfaToken {
  // 认证TOKEN
  string token = 1;
  // 有效期
  google.protobuf.Duration ttl = 2;
}

// 2FA(2-Factor Authentication)类型
enum TfaType {
  TFA_TYPE_UNSPECIFIED = 0;
  // 手机验证
  TFA_TYPE_PHONE = 1;
  // 密码验证
  TFA_TYPE_PASSWORD = 2;
  // 微信验证
  TFA_TYPE_WECHAT = 3;
  // 邮箱验证
  TFA_TYPE_EMAIL = 4;
}

// 绑定微信状态
enum BindWeixinState {
  BIND_WEIXIN_STATE_UNSPECIFIED = 0;
  // 等待扫码
  BIND_WEIXIN_STATE_WAITING_SCAN = 1;
  // 绑定成功
  BIND_WEIXIN_STATE_SUCCESS = 2;
  // 当前微信已绑定其他账号
  BIND_WEIXIN_STATE_BOUND_OTHER_ACCOUNT = 3;
}

// 创建微信二次验证状态
enum CreateWeixinTfaState {
  CREATE_WEIXIN_TFA_STATE_UNSPECIFIED = 0;
  // 等待扫码
  CREATE_WEIXIN_TFA_STATE_WAITING_SCAN = 1;
  // 创建成功
  CREATE_WEIXIN_TFA_STATE_SUCCESS = 2;
}

// 微信二维码信息
message WeixinQrcode {
  // 获取的二维码ticket
  string ticket = 1;
  // 二维码解析后的URL
  string url = 2;
  // 过期时间（单位秒）
  int32 expires_in = 3;
  // 场景值
  string scene_str = 4;
}

// 谷歌认证场景
enum GoogleAuthScene {
  GOOGLE_AUTH_SCENE_UNSPECIFIED = 0;
  // 谷歌登录
  GOOGLE_AUTH_SCENE_LOGIN = 1;
  // 绑定谷歌账号
  GOOGLE_AUTH_SCENE_BIND = 2;
  // 创建谷歌二次验证
  GOOGLE_AUTH_SCENE_2FA = 3;
}

// 用户卡片
message UserCard {
  // 用户ID
  uint64 id = 1;
  // 用户名
  string username = 2;
  // 头像
  string image = 3;
  // 等级
  string level = 4;
}

// OAuth客户端
message OauthClient {
  // 客户端名称
  string name = 1;
  // logo
  string logo_url = 2;
  // 客户端ID
  string client_id = 3;
  // 客户端密钥
  string client_secret = 4 [(mask).rule = "secret"];
}
