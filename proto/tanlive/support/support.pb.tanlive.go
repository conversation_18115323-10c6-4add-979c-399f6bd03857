// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package support

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	proto "google.golang.org/protobuf/proto"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DataType":   "required",
		"DataId":     "required",
		"SourceLang": "omitempty",
		"TargetLang": "required",
		"Content":    "required",
	}, &Translation{})
}

func (x *Translation) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Key":    "required",
		"Values": "required,min=1,dive,min=1",
	}, &Translation_Item{})
}

func (x *Translation_Item) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MapPlatform": "required",
		"MapPlaceId":  "required",
	}, &StructuredAddress{})
}

func (x *StructuredAddress) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Country":     "required",
		"MapPlatform": "required",
		"MapPlaceId":  "required",
	}, &StructuredFullAddress{})
}

func (x *StructuredFullAddress) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MapPlatform": "required",
		"MapPlaceId":  "required",
	}, &GeocodingItem{})
}

func (x *GeocodingItem) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Method": "required",
		"Path":   "required",
	}, &MapRequest{})
}

func (x *MapRequest) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DataType":  "required",
		"DataField": "required",
		"ParentId":  "omitempty",
	}, &GetPlaceCondByDataType{})
}

func (x *GetPlaceCondByDataType) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"PlaceId": "required",
	}, &GetPlaceCondByPlaceId{})
}

func (x *GetPlaceCondByPlaceId) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *Translation) MaskInLog() any {
	if x == nil {
		return (*Translation)(nil)
	}

	y := proto.Clone(x).(*Translation)
	for k, v := range y.Content {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Content[k] = vv.MaskInLog().(*Translation_Item)
		}
	}

	return y
}

func (x *Translation) MaskInRpc() any {
	if x == nil {
		return (*Translation)(nil)
	}

	y := x
	for k, v := range y.Content {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Content[k] = vv.MaskInRpc().(*Translation_Item)
		}
	}

	return y
}

func (x *Translation) MaskInBff() any {
	if x == nil {
		return (*Translation)(nil)
	}

	y := x
	for k, v := range y.Content {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Content[k] = vv.MaskInBff().(*Translation_Item)
		}
	}

	return y
}

func (x *FullPlace) MaskInLog() any {
	if x == nil {
		return (*FullPlace)(nil)
	}

	y := proto.Clone(x).(*FullPlace)
	if v, ok := any(y.Place).(interface{ MaskInLog() any }); ok {
		y.Place = v.MaskInLog().(*Place)
	}
	if v, ok := any(y.Source).(interface{ MaskInLog() any }); ok {
		y.Source = v.MaskInLog().(*PlaceSource)
	}
	for k, v := range y.Localizations {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Localizations[k] = vv.MaskInLog().(*PlaceLocalization)
		}
	}

	return y
}

func (x *FullPlace) MaskInRpc() any {
	if x == nil {
		return (*FullPlace)(nil)
	}

	y := x
	if v, ok := any(y.Place).(interface{ MaskInRpc() any }); ok {
		y.Place = v.MaskInRpc().(*Place)
	}
	if v, ok := any(y.Source).(interface{ MaskInRpc() any }); ok {
		y.Source = v.MaskInRpc().(*PlaceSource)
	}
	for k, v := range y.Localizations {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Localizations[k] = vv.MaskInRpc().(*PlaceLocalization)
		}
	}

	return y
}

func (x *FullPlace) MaskInBff() any {
	if x == nil {
		return (*FullPlace)(nil)
	}

	y := x
	if v, ok := any(y.Place).(interface{ MaskInBff() any }); ok {
		y.Place = v.MaskInBff().(*Place)
	}
	if v, ok := any(y.Source).(interface{ MaskInBff() any }); ok {
		y.Source = v.MaskInBff().(*PlaceSource)
	}
	for k, v := range y.Localizations {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Localizations[k] = vv.MaskInBff().(*PlaceLocalization)
		}
	}

	return y
}

func (x *StructuredAddress) MaskInLog() any {
	if x == nil {
		return (*StructuredAddress)(nil)
	}

	y := proto.Clone(x).(*StructuredAddress)
	if v, ok := any(y.Continent).(interface{ MaskInLog() any }); ok {
		y.Continent = v.MaskInLog().(*Place)
	}
	if v, ok := any(y.Country).(interface{ MaskInLog() any }); ok {
		y.Country = v.MaskInLog().(*Place)
	}
	if v, ok := any(y.Level1).(interface{ MaskInLog() any }); ok {
		y.Level1 = v.MaskInLog().(*Place)
	}
	if v, ok := any(y.Level2).(interface{ MaskInLog() any }); ok {
		y.Level2 = v.MaskInLog().(*Place)
	}
	if v, ok := any(y.Detail).(interface{ MaskInLog() any }); ok {
		y.Detail = v.MaskInLog().(*Place)
	}

	return y
}

func (x *StructuredAddress) MaskInRpc() any {
	if x == nil {
		return (*StructuredAddress)(nil)
	}

	y := x
	if v, ok := any(y.Continent).(interface{ MaskInRpc() any }); ok {
		y.Continent = v.MaskInRpc().(*Place)
	}
	if v, ok := any(y.Country).(interface{ MaskInRpc() any }); ok {
		y.Country = v.MaskInRpc().(*Place)
	}
	if v, ok := any(y.Level1).(interface{ MaskInRpc() any }); ok {
		y.Level1 = v.MaskInRpc().(*Place)
	}
	if v, ok := any(y.Level2).(interface{ MaskInRpc() any }); ok {
		y.Level2 = v.MaskInRpc().(*Place)
	}
	if v, ok := any(y.Detail).(interface{ MaskInRpc() any }); ok {
		y.Detail = v.MaskInRpc().(*Place)
	}

	return y
}

func (x *StructuredAddress) MaskInBff() any {
	if x == nil {
		return (*StructuredAddress)(nil)
	}

	y := x
	if v, ok := any(y.Continent).(interface{ MaskInBff() any }); ok {
		y.Continent = v.MaskInBff().(*Place)
	}
	if v, ok := any(y.Country).(interface{ MaskInBff() any }); ok {
		y.Country = v.MaskInBff().(*Place)
	}
	if v, ok := any(y.Level1).(interface{ MaskInBff() any }); ok {
		y.Level1 = v.MaskInBff().(*Place)
	}
	if v, ok := any(y.Level2).(interface{ MaskInBff() any }); ok {
		y.Level2 = v.MaskInBff().(*Place)
	}
	if v, ok := any(y.Detail).(interface{ MaskInBff() any }); ok {
		y.Detail = v.MaskInBff().(*Place)
	}

	return y
}

func (x *StructuredFullAddress) MaskInLog() any {
	if x == nil {
		return (*StructuredFullAddress)(nil)
	}

	y := proto.Clone(x).(*StructuredFullAddress)
	if v, ok := any(y.Country).(interface{ MaskInLog() any }); ok {
		y.Country = v.MaskInLog().(*FullPlace)
	}
	if v, ok := any(y.Level1).(interface{ MaskInLog() any }); ok {
		y.Level1 = v.MaskInLog().(*FullPlace)
	}
	if v, ok := any(y.Level2).(interface{ MaskInLog() any }); ok {
		y.Level2 = v.MaskInLog().(*FullPlace)
	}
	if v, ok := any(y.Detail).(interface{ MaskInLog() any }); ok {
		y.Detail = v.MaskInLog().(*FullPlace)
	}

	return y
}

func (x *StructuredFullAddress) MaskInRpc() any {
	if x == nil {
		return (*StructuredFullAddress)(nil)
	}

	y := x
	if v, ok := any(y.Country).(interface{ MaskInRpc() any }); ok {
		y.Country = v.MaskInRpc().(*FullPlace)
	}
	if v, ok := any(y.Level1).(interface{ MaskInRpc() any }); ok {
		y.Level1 = v.MaskInRpc().(*FullPlace)
	}
	if v, ok := any(y.Level2).(interface{ MaskInRpc() any }); ok {
		y.Level2 = v.MaskInRpc().(*FullPlace)
	}
	if v, ok := any(y.Detail).(interface{ MaskInRpc() any }); ok {
		y.Detail = v.MaskInRpc().(*FullPlace)
	}

	return y
}

func (x *StructuredFullAddress) MaskInBff() any {
	if x == nil {
		return (*StructuredFullAddress)(nil)
	}

	y := x
	if v, ok := any(y.Country).(interface{ MaskInBff() any }); ok {
		y.Country = v.MaskInBff().(*FullPlace)
	}
	if v, ok := any(y.Level1).(interface{ MaskInBff() any }); ok {
		y.Level1 = v.MaskInBff().(*FullPlace)
	}
	if v, ok := any(y.Level2).(interface{ MaskInBff() any }); ok {
		y.Level2 = v.MaskInBff().(*FullPlace)
	}
	if v, ok := any(y.Detail).(interface{ MaskInBff() any }); ok {
		y.Detail = v.MaskInBff().(*FullPlace)
	}

	return y
}

func (x *PlaceWithParents) MaskInLog() any {
	if x == nil {
		return (*PlaceWithParents)(nil)
	}

	y := proto.Clone(x).(*PlaceWithParents)
	if v, ok := any(y.Place).(interface{ MaskInLog() any }); ok {
		y.Place = v.MaskInLog().(*Place)
	}
	for k, v := range y.Parents {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Parents[k] = vv.MaskInLog().(*Place)
		}
	}

	return y
}

func (x *PlaceWithParents) MaskInRpc() any {
	if x == nil {
		return (*PlaceWithParents)(nil)
	}

	y := x
	if v, ok := any(y.Place).(interface{ MaskInRpc() any }); ok {
		y.Place = v.MaskInRpc().(*Place)
	}
	for k, v := range y.Parents {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Parents[k] = vv.MaskInRpc().(*Place)
		}
	}

	return y
}

func (x *PlaceWithParents) MaskInBff() any {
	if x == nil {
		return (*PlaceWithParents)(nil)
	}

	y := x
	if v, ok := any(y.Place).(interface{ MaskInBff() any }); ok {
		y.Place = v.MaskInBff().(*Place)
	}
	for k, v := range y.Parents {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Parents[k] = vv.MaskInBff().(*Place)
		}
	}

	return y
}

func (x *MapRequest) MaskInLog() any {
	if x == nil {
		return (*MapRequest)(nil)
	}

	y := proto.Clone(x).(*MapRequest)
	for k, v := range y.Header {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Header[k] = vv.MaskInLog().(*MapHeader)
		}
	}

	return y
}

func (x *MapRequest) MaskInRpc() any {
	if x == nil {
		return (*MapRequest)(nil)
	}

	y := x
	for k, v := range y.Header {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Header[k] = vv.MaskInRpc().(*MapHeader)
		}
	}

	return y
}

func (x *MapRequest) MaskInBff() any {
	if x == nil {
		return (*MapRequest)(nil)
	}

	y := x
	for k, v := range y.Header {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Header[k] = vv.MaskInBff().(*MapHeader)
		}
	}

	return y
}

func (x *MapResponse) MaskInLog() any {
	if x == nil {
		return (*MapResponse)(nil)
	}

	y := proto.Clone(x).(*MapResponse)
	for k, v := range y.Header {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Header[k] = vv.MaskInLog().(*MapHeader)
		}
	}

	return y
}

func (x *MapResponse) MaskInRpc() any {
	if x == nil {
		return (*MapResponse)(nil)
	}

	y := x
	for k, v := range y.Header {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Header[k] = vv.MaskInRpc().(*MapHeader)
		}
	}

	return y
}

func (x *MapResponse) MaskInBff() any {
	if x == nil {
		return (*MapResponse)(nil)
	}

	y := x
	for k, v := range y.Header {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Header[k] = vv.MaskInBff().(*MapHeader)
		}
	}

	return y
}

func (x *Translation) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Content {
		if sanitizer, ok := any(x.Content[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *FullPlace) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Place).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Source).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Localizations {
		if sanitizer, ok := any(x.Localizations[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *StructuredAddress) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Continent).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Country).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Level1).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Level2).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Detail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *StructuredFullAddress) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Country).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Level1).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Level2).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Detail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *PlaceWithParents) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Place).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Parents {
		if sanitizer, ok := any(x.Parents[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *MapRequest) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Header {
		if sanitizer, ok := any(x.Header[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *MapResponse) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Header {
		if sanitizer, ok := any(x.Header[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
