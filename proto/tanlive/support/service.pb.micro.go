// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/support/service.proto

package support

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	_ "google.golang.org/protobuf/types/known/anypb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for SupportService service

func NewSupportServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for SupportService service

type SupportService interface {
	// 翻译
	Translate(ctx context.Context, in *ReqTranslate, opts ...client.CallOption) (*RspTranslate, error)
	// 代理腾讯地图请求
	ProxyTencentMap(ctx context.Context, in *ReqProxyTencentMap, opts ...client.CallOption) (*RspProxyTencentMap, error)
	// 代理谷歌地图请求
	ProxyGoogleMap(ctx context.Context, in *ReqProxyGoogleMap, opts ...client.CallOption) (*RspProxyGoogleMap, error)
	// 查询地点选择器数据
	GetPlaceSelector(ctx context.Context, in *ReqGetPlaceSelector, opts ...client.CallOption) (*RspGetPlaceSelector, error)
	// 搜索地点
	SearchPlaces(ctx context.Context, in *ReqSearchPlaces, opts ...client.CallOption) (*RspSearchPlaces, error)
	// 地址解析
	Geocode(ctx context.Context, in *ReqGeocode, opts ...client.CallOption) (*RspGeocode, error)
	// 预解析
	PreGeocode(ctx context.Context, in *ReqGeocode, opts ...client.CallOption) (*RspPreGeocode, error)
	// 同步地址
	SyncAddress(ctx context.Context, in *ReqSyncAddress, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除地址
	DeleteAddress(ctx context.Context, in *ReqDeleteAddress, opts ...client.CallOption) (*emptypb.Empty, error)
	// 替换地址
	ReplaceAddress(ctx context.Context, in *ReqReplaceAddress, opts ...client.CallOption) (*emptypb.Empty, error)
	// 发布地址
	PublishAddress(ctx context.Context, in *ReqPublishAddress, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询地址（以数据项分组）
	GetAddressesByDataItem(ctx context.Context, in *ReqGetAddressesByDataItem, opts ...client.CallOption) (*RspGetAddressesByDataItem, error)
	// 通过地址筛选数据
	GetDataItemByAddresses(ctx context.Context, in *ReqGetDataItemByAddresses, opts ...client.CallOption) (*RspGetDataItemByAddresses, error)
	// 创建指标
	CreateMetric(ctx context.Context, in *ReqCreateMetric, opts ...client.CallOption) (*emptypb.Empty, error)
}

type supportService struct {
	c    client.Client
	name string
}

func NewSupportService(name string, c client.Client) SupportService {
	return &supportService{
		c:    c,
		name: name,
	}
}

func (c *supportService) Translate(ctx context.Context, in *ReqTranslate, opts ...client.CallOption) (*RspTranslate, error) {
	req := c.c.NewRequest(c.name, "SupportService.Translate", in)
	out := new(RspTranslate)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) ProxyTencentMap(ctx context.Context, in *ReqProxyTencentMap, opts ...client.CallOption) (*RspProxyTencentMap, error) {
	req := c.c.NewRequest(c.name, "SupportService.ProxyTencentMap", in)
	out := new(RspProxyTencentMap)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) ProxyGoogleMap(ctx context.Context, in *ReqProxyGoogleMap, opts ...client.CallOption) (*RspProxyGoogleMap, error) {
	req := c.c.NewRequest(c.name, "SupportService.ProxyGoogleMap", in)
	out := new(RspProxyGoogleMap)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) GetPlaceSelector(ctx context.Context, in *ReqGetPlaceSelector, opts ...client.CallOption) (*RspGetPlaceSelector, error) {
	req := c.c.NewRequest(c.name, "SupportService.GetPlaceSelector", in)
	out := new(RspGetPlaceSelector)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) SearchPlaces(ctx context.Context, in *ReqSearchPlaces, opts ...client.CallOption) (*RspSearchPlaces, error) {
	req := c.c.NewRequest(c.name, "SupportService.SearchPlaces", in)
	out := new(RspSearchPlaces)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) Geocode(ctx context.Context, in *ReqGeocode, opts ...client.CallOption) (*RspGeocode, error) {
	req := c.c.NewRequest(c.name, "SupportService.Geocode", in)
	out := new(RspGeocode)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) PreGeocode(ctx context.Context, in *ReqGeocode, opts ...client.CallOption) (*RspPreGeocode, error) {
	req := c.c.NewRequest(c.name, "SupportService.PreGeocode", in)
	out := new(RspPreGeocode)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) SyncAddress(ctx context.Context, in *ReqSyncAddress, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SupportService.SyncAddress", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) DeleteAddress(ctx context.Context, in *ReqDeleteAddress, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SupportService.DeleteAddress", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) ReplaceAddress(ctx context.Context, in *ReqReplaceAddress, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SupportService.ReplaceAddress", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) PublishAddress(ctx context.Context, in *ReqPublishAddress, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SupportService.PublishAddress", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) GetAddressesByDataItem(ctx context.Context, in *ReqGetAddressesByDataItem, opts ...client.CallOption) (*RspGetAddressesByDataItem, error) {
	req := c.c.NewRequest(c.name, "SupportService.GetAddressesByDataItem", in)
	out := new(RspGetAddressesByDataItem)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) GetDataItemByAddresses(ctx context.Context, in *ReqGetDataItemByAddresses, opts ...client.CallOption) (*RspGetDataItemByAddresses, error) {
	req := c.c.NewRequest(c.name, "SupportService.GetDataItemByAddresses", in)
	out := new(RspGetDataItemByAddresses)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *supportService) CreateMetric(ctx context.Context, in *ReqCreateMetric, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SupportService.CreateMetric", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for SupportService service

type SupportServiceHandler interface {
	// 翻译
	Translate(context.Context, *ReqTranslate, *RspTranslate) error
	// 代理腾讯地图请求
	ProxyTencentMap(context.Context, *ReqProxyTencentMap, *RspProxyTencentMap) error
	// 代理谷歌地图请求
	ProxyGoogleMap(context.Context, *ReqProxyGoogleMap, *RspProxyGoogleMap) error
	// 查询地点选择器数据
	GetPlaceSelector(context.Context, *ReqGetPlaceSelector, *RspGetPlaceSelector) error
	// 搜索地点
	SearchPlaces(context.Context, *ReqSearchPlaces, *RspSearchPlaces) error
	// 地址解析
	Geocode(context.Context, *ReqGeocode, *RspGeocode) error
	// 预解析
	PreGeocode(context.Context, *ReqGeocode, *RspPreGeocode) error
	// 同步地址
	SyncAddress(context.Context, *ReqSyncAddress, *emptypb.Empty) error
	// 删除地址
	DeleteAddress(context.Context, *ReqDeleteAddress, *emptypb.Empty) error
	// 替换地址
	ReplaceAddress(context.Context, *ReqReplaceAddress, *emptypb.Empty) error
	// 发布地址
	PublishAddress(context.Context, *ReqPublishAddress, *emptypb.Empty) error
	// 查询地址（以数据项分组）
	GetAddressesByDataItem(context.Context, *ReqGetAddressesByDataItem, *RspGetAddressesByDataItem) error
	// 通过地址筛选数据
	GetDataItemByAddresses(context.Context, *ReqGetDataItemByAddresses, *RspGetDataItemByAddresses) error
	// 创建指标
	CreateMetric(context.Context, *ReqCreateMetric, *emptypb.Empty) error
}

func RegisterSupportServiceHandler(s server.Server, hdlr SupportServiceHandler, opts ...server.HandlerOption) error {
	type supportService interface {
		Translate(ctx context.Context, in *ReqTranslate, out *RspTranslate) error
		ProxyTencentMap(ctx context.Context, in *ReqProxyTencentMap, out *RspProxyTencentMap) error
		ProxyGoogleMap(ctx context.Context, in *ReqProxyGoogleMap, out *RspProxyGoogleMap) error
		GetPlaceSelector(ctx context.Context, in *ReqGetPlaceSelector, out *RspGetPlaceSelector) error
		SearchPlaces(ctx context.Context, in *ReqSearchPlaces, out *RspSearchPlaces) error
		Geocode(ctx context.Context, in *ReqGeocode, out *RspGeocode) error
		PreGeocode(ctx context.Context, in *ReqGeocode, out *RspPreGeocode) error
		SyncAddress(ctx context.Context, in *ReqSyncAddress, out *emptypb.Empty) error
		DeleteAddress(ctx context.Context, in *ReqDeleteAddress, out *emptypb.Empty) error
		ReplaceAddress(ctx context.Context, in *ReqReplaceAddress, out *emptypb.Empty) error
		PublishAddress(ctx context.Context, in *ReqPublishAddress, out *emptypb.Empty) error
		GetAddressesByDataItem(ctx context.Context, in *ReqGetAddressesByDataItem, out *RspGetAddressesByDataItem) error
		GetDataItemByAddresses(ctx context.Context, in *ReqGetDataItemByAddresses, out *RspGetDataItemByAddresses) error
		CreateMetric(ctx context.Context, in *ReqCreateMetric, out *emptypb.Empty) error
	}
	type SupportService struct {
		supportService
	}
	h := &supportServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&SupportService{h}, opts...))
}

type supportServiceHandler struct {
	SupportServiceHandler
}

func (h *supportServiceHandler) Translate(ctx context.Context, in *ReqTranslate, out *RspTranslate) error {
	return h.SupportServiceHandler.Translate(ctx, in, out)
}

func (h *supportServiceHandler) ProxyTencentMap(ctx context.Context, in *ReqProxyTencentMap, out *RspProxyTencentMap) error {
	return h.SupportServiceHandler.ProxyTencentMap(ctx, in, out)
}

func (h *supportServiceHandler) ProxyGoogleMap(ctx context.Context, in *ReqProxyGoogleMap, out *RspProxyGoogleMap) error {
	return h.SupportServiceHandler.ProxyGoogleMap(ctx, in, out)
}

func (h *supportServiceHandler) GetPlaceSelector(ctx context.Context, in *ReqGetPlaceSelector, out *RspGetPlaceSelector) error {
	return h.SupportServiceHandler.GetPlaceSelector(ctx, in, out)
}

func (h *supportServiceHandler) SearchPlaces(ctx context.Context, in *ReqSearchPlaces, out *RspSearchPlaces) error {
	return h.SupportServiceHandler.SearchPlaces(ctx, in, out)
}

func (h *supportServiceHandler) Geocode(ctx context.Context, in *ReqGeocode, out *RspGeocode) error {
	return h.SupportServiceHandler.Geocode(ctx, in, out)
}

func (h *supportServiceHandler) PreGeocode(ctx context.Context, in *ReqGeocode, out *RspPreGeocode) error {
	return h.SupportServiceHandler.PreGeocode(ctx, in, out)
}

func (h *supportServiceHandler) SyncAddress(ctx context.Context, in *ReqSyncAddress, out *emptypb.Empty) error {
	return h.SupportServiceHandler.SyncAddress(ctx, in, out)
}

func (h *supportServiceHandler) DeleteAddress(ctx context.Context, in *ReqDeleteAddress, out *emptypb.Empty) error {
	return h.SupportServiceHandler.DeleteAddress(ctx, in, out)
}

func (h *supportServiceHandler) ReplaceAddress(ctx context.Context, in *ReqReplaceAddress, out *emptypb.Empty) error {
	return h.SupportServiceHandler.ReplaceAddress(ctx, in, out)
}

func (h *supportServiceHandler) PublishAddress(ctx context.Context, in *ReqPublishAddress, out *emptypb.Empty) error {
	return h.SupportServiceHandler.PublishAddress(ctx, in, out)
}

func (h *supportServiceHandler) GetAddressesByDataItem(ctx context.Context, in *ReqGetAddressesByDataItem, out *RspGetAddressesByDataItem) error {
	return h.SupportServiceHandler.GetAddressesByDataItem(ctx, in, out)
}

func (h *supportServiceHandler) GetDataItemByAddresses(ctx context.Context, in *ReqGetDataItemByAddresses, out *RspGetDataItemByAddresses) error {
	return h.SupportServiceHandler.GetDataItemByAddresses(ctx, in, out)
}

func (h *supportServiceHandler) CreateMetric(ctx context.Context, in *ReqCreateMetric, out *emptypb.Empty) error {
	return h.SupportServiceHandler.CreateMetric(ctx, in, out)
}
