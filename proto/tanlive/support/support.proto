syntax = "proto3";

package tanlive.support;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support";

import "tanlive/base/ugc.proto";
import "tanlive/options.proto";

// 翻译详情
message Translation {
  // 数据项
  message Item {
    // 键名
    string key = 1 [(validator)="required"];
    // 键值
    repeated string values = 2 [(validator)="required,min=1,dive,min=1"];
  }
  // 数据类型
  base.DataType data_type = 1 [(validator)="required"];
  // 数据ID
  uint64 data_id = 2 [(validator)="required"];
  // 源语言
  string source_lang = 3 [(validator)="omitempty"];
  // 目标语言
  string target_lang = 4 [(validator)="required"];
  // 内容
  repeated Item content = 5 [(validator)="required"];
}

// 行政区划类型
enum AdAreaType {
  AD_AREA_TYPE_UNSPECIFIED = 0;
  // 洲
  AD_AREA_TYPE_CONTINENT = 1;
  // 国家/地区
  AD_AREA_TYPE_COUNTRY = 2;
  // 一级行政区划
  AD_AREA_TYPE_LEVEL_1 = 3;
  // 二级行政区划
  AD_AREA_TYPE_LEVEL_2 = 4;
}

// 地点详情
message Place {
  // 主键
  uint64 id = 1;
  // 名称
  string name = 2;
  // 地址
  string address = 3;
  // 语言
  string lang = 4;
  // 是否为行政区划地点
  bool is_ad_area = 5;
  // 行政区划等级
  AdAreaType ad_area_type = 6;
  // 父级ID
  uint64 parent_id = 7;
  // 纬度
  double lat = 8;
  // 经度
  double lng = 9;
  // 2位ISO编码
  string iso_code2 = 10;
  // 3位ISO编码
  string iso_code3 = 11;
  // ISO编号
  string iso_number = 12;
}

// 地点本地化
message PlaceLocalization {
  // 主键
  uint64 id = 1;
  // 所属地点
  uint64 place_id = 2;
  // 语言
  string lang = 3;
  // 名称
  string name = 4;
  // 地址
  string address = 5;
}

// 地点数据源
message PlaceSource {
  // 主键
  uint64 id = 1;
  // 地图地点ID
  string map_place_id = 2;
  // 地图平台
  MapPlatform map_platform = 3;
  // 地点详情
  string place_detail = 4;
}

// 地点全部信息
message FullPlace {
  // 地点详情
  Place place = 1;
  // 数据源
  PlaceSource source = 2;
  // 多语言
  repeated PlaceLocalization localizations = 3;
}

// 原始地址详情
message RawAddress {
  // 大洲ID
  uint64 continent_id = 1;
  // 国家ID
  uint64 country_id = 2;
  // 一级行政区划ID
  uint64 level1_id = 3;
  // 二级行政区划ID
  uint64 level2_id = 4;
  // 详细地址ID
  uint64 detail_id = 5;
  // 地图平台
  MapPlatform map_platform = 6;
  // 地图地点ID
  string map_place_id = 7;
}

// 结构化地址
message StructuredAddress {
  // 大洲
  Place continent = 1;
  // 国家
  Place country = 2;
  // 一级行政区划
  Place level1 = 3;
  // 二级行政区划
  Place level2 = 4;
  // 详细地点
  Place detail = 5;
  // 地图平台
  MapPlatform map_platform = 6 [(validator) = "required"];
  // 地图地点ID
  string map_place_id = 7 [(validator) = "required"];
}

// 结构化地址全部信息
message StructuredFullAddress {
  // 国家
  FullPlace country = 1 [(validator) = "required"];
  // 一级行政区划
  FullPlace level1 = 2;
  // 二级行政区划
  FullPlace level2 = 3;
  // 详细地点
  FullPlace detail = 4;
  // 地图平台
  MapPlatform map_platform = 5 [(validator) = "required"];
  // 地图地点ID
  string map_place_id = 6 [(validator) = "required"];
}

// 地点详情
message PlaceWithParents {
  // 地点详情
  Place place = 1;
  // 父级列表
  repeated Place parents = 2;
}

// 地图平台
enum MapPlatform {
  MAP_PLATFORM_UNSPECIFIED = 0;
  // 谷歌地图
  MAP_PLATFORM_GOOGLE = 1;
  // 腾讯地图
  MAP_PLATFORM_TENCENT = 2;
}

// 谷歌地图客户端
enum GoogleMapClient {
  GOOGLE_MAP_CLIENT_UNSPECIFIED = 0;
  // Maps客户端
  GOOGLE_MAP_CLIENT_MAPS = 1;
  // Places客户端
  GOOGLE_MAP_CLIENT_PLACES = 2;
}

// 地址解析项
message GeocodingItem {
  // 地图平台
  MapPlatform map_platform = 1 [(validator) = "required"];
  // 地图地点ID
  string map_place_id = 2 [(validator) = "required"];
}

message MapHeader {
  // 键名
  string key = 1;
  // 键值
  string value = 2;
}

// 地图请求
message MapRequest {
  // 请求方法
  string method = 1 [(validator) = "required"];
  // 请求路径
  string path = 2 [(validator) = "required"];
  // 请求头
  repeated MapHeader header = 3;
  // 请求体
  string body = 4;
}

// 地图响应
message MapResponse {
  // 响应头
  repeated MapHeader header = 1;
  // 响应体
  string body = 2;
}

// 通过数据类型查询条件
message GetPlaceCondByDataType {
  // 数据类型
  base.DataType data_type = 1 [(validator) = "required"];
  // 数据字段
  // 团队：location、service_region 资源：location、target_regions
  string data_field = 2 [(validator) = "required"];
  // 父级ID，不传返回大洲数据
  uint64 parent_id = 3 [(validator) = "omitempty"];
}

// 通过地点ID查询条件
message GetPlaceCondByPlaceId {
  repeated uint64 place_id = 5 [(validator) = "required"];
}
