// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/support/support.proto

package support

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 行政区划类型
type AdAreaType int32

const (
	AdAreaType_AD_AREA_TYPE_UNSPECIFIED AdAreaType = 0
	// 洲
	AdAreaType_AD_AREA_TYPE_CONTINENT AdAreaType = 1
	// 国家/地区
	AdAreaType_AD_AREA_TYPE_COUNTRY AdAreaType = 2
	// 一级行政区划
	AdAreaType_AD_AREA_TYPE_LEVEL_1 AdAreaType = 3
	// 二级行政区划
	AdAreaType_AD_AREA_TYPE_LEVEL_2 AdAreaType = 4
)

// Enum value maps for AdAreaType.
var (
	AdAreaType_name = map[int32]string{
		0: "AD_AREA_TYPE_UNSPECIFIED",
		1: "AD_AREA_TYPE_CONTINENT",
		2: "AD_AREA_TYPE_COUNTRY",
		3: "AD_AREA_TYPE_LEVEL_1",
		4: "AD_AREA_TYPE_LEVEL_2",
	}
	AdAreaType_value = map[string]int32{
		"AD_AREA_TYPE_UNSPECIFIED": 0,
		"AD_AREA_TYPE_CONTINENT":   1,
		"AD_AREA_TYPE_COUNTRY":     2,
		"AD_AREA_TYPE_LEVEL_1":     3,
		"AD_AREA_TYPE_LEVEL_2":     4,
	}
)

func (x AdAreaType) Enum() *AdAreaType {
	p := new(AdAreaType)
	*p = x
	return p
}

func (x AdAreaType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdAreaType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_support_support_proto_enumTypes[0].Descriptor()
}

func (AdAreaType) Type() protoreflect.EnumType {
	return &file_tanlive_support_support_proto_enumTypes[0]
}

func (x AdAreaType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdAreaType.Descriptor instead.
func (AdAreaType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{0}
}

// 地图平台
type MapPlatform int32

const (
	MapPlatform_MAP_PLATFORM_UNSPECIFIED MapPlatform = 0
	// 谷歌地图
	MapPlatform_MAP_PLATFORM_GOOGLE MapPlatform = 1
	// 腾讯地图
	MapPlatform_MAP_PLATFORM_TENCENT MapPlatform = 2
)

// Enum value maps for MapPlatform.
var (
	MapPlatform_name = map[int32]string{
		0: "MAP_PLATFORM_UNSPECIFIED",
		1: "MAP_PLATFORM_GOOGLE",
		2: "MAP_PLATFORM_TENCENT",
	}
	MapPlatform_value = map[string]int32{
		"MAP_PLATFORM_UNSPECIFIED": 0,
		"MAP_PLATFORM_GOOGLE":      1,
		"MAP_PLATFORM_TENCENT":     2,
	}
)

func (x MapPlatform) Enum() *MapPlatform {
	p := new(MapPlatform)
	*p = x
	return p
}

func (x MapPlatform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MapPlatform) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_support_support_proto_enumTypes[1].Descriptor()
}

func (MapPlatform) Type() protoreflect.EnumType {
	return &file_tanlive_support_support_proto_enumTypes[1]
}

func (x MapPlatform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MapPlatform.Descriptor instead.
func (MapPlatform) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{1}
}

// 谷歌地图客户端
type GoogleMapClient int32

const (
	GoogleMapClient_GOOGLE_MAP_CLIENT_UNSPECIFIED GoogleMapClient = 0
	// Maps客户端
	GoogleMapClient_GOOGLE_MAP_CLIENT_MAPS GoogleMapClient = 1
	// Places客户端
	GoogleMapClient_GOOGLE_MAP_CLIENT_PLACES GoogleMapClient = 2
)

// Enum value maps for GoogleMapClient.
var (
	GoogleMapClient_name = map[int32]string{
		0: "GOOGLE_MAP_CLIENT_UNSPECIFIED",
		1: "GOOGLE_MAP_CLIENT_MAPS",
		2: "GOOGLE_MAP_CLIENT_PLACES",
	}
	GoogleMapClient_value = map[string]int32{
		"GOOGLE_MAP_CLIENT_UNSPECIFIED": 0,
		"GOOGLE_MAP_CLIENT_MAPS":        1,
		"GOOGLE_MAP_CLIENT_PLACES":      2,
	}
)

func (x GoogleMapClient) Enum() *GoogleMapClient {
	p := new(GoogleMapClient)
	*p = x
	return p
}

func (x GoogleMapClient) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoogleMapClient) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_support_support_proto_enumTypes[2].Descriptor()
}

func (GoogleMapClient) Type() protoreflect.EnumType {
	return &file_tanlive_support_support_proto_enumTypes[2]
}

func (x GoogleMapClient) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoogleMapClient.Descriptor instead.
func (GoogleMapClient) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{2}
}

// 翻译详情
type Translation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据类型
	DataType base.DataType `protobuf:"varint,1,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 数据ID
	DataId uint64 `protobuf:"varint,2,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	// 源语言
	SourceLang string `protobuf:"bytes,3,opt,name=source_lang,json=sourceLang,proto3" json:"source_lang,omitempty"`
	// 目标语言
	TargetLang string `protobuf:"bytes,4,opt,name=target_lang,json=targetLang,proto3" json:"target_lang,omitempty"`
	// 内容
	Content []*Translation_Item `protobuf:"bytes,5,rep,name=content,proto3" json:"content,omitempty"`
}

func (x *Translation) Reset() {
	*x = Translation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Translation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Translation) ProtoMessage() {}

func (x *Translation) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Translation.ProtoReflect.Descriptor instead.
func (*Translation) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{0}
}

func (x *Translation) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *Translation) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *Translation) GetSourceLang() string {
	if x != nil {
		return x.SourceLang
	}
	return ""
}

func (x *Translation) GetTargetLang() string {
	if x != nil {
		return x.TargetLang
	}
	return ""
}

func (x *Translation) GetContent() []*Translation_Item {
	if x != nil {
		return x.Content
	}
	return nil
}

// 地点详情
type Place struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主键
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang,omitempty"`
	// 是否为行政区划地点
	IsAdArea bool `protobuf:"varint,5,opt,name=is_ad_area,json=isAdArea,proto3" json:"is_ad_area,omitempty"`
	// 行政区划等级
	AdAreaType AdAreaType `protobuf:"varint,6,opt,name=ad_area_type,json=adAreaType,proto3,enum=tanlive.support.AdAreaType" json:"ad_area_type,omitempty"`
	// 父级ID
	ParentId uint64 `protobuf:"varint,7,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 纬度
	Lat float64 `protobuf:"fixed64,8,opt,name=lat,proto3" json:"lat,omitempty"`
	// 经度
	Lng float64 `protobuf:"fixed64,9,opt,name=lng,proto3" json:"lng,omitempty"`
	// 2位ISO编码
	IsoCode2 string `protobuf:"bytes,10,opt,name=iso_code2,json=isoCode2,proto3" json:"iso_code2,omitempty"`
	// 3位ISO编码
	IsoCode3 string `protobuf:"bytes,11,opt,name=iso_code3,json=isoCode3,proto3" json:"iso_code3,omitempty"`
	// ISO编号
	IsoNumber string `protobuf:"bytes,12,opt,name=iso_number,json=isoNumber,proto3" json:"iso_number,omitempty"`
}

func (x *Place) Reset() {
	*x = Place{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Place) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Place) ProtoMessage() {}

func (x *Place) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Place.ProtoReflect.Descriptor instead.
func (*Place) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{1}
}

func (x *Place) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Place) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Place) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Place) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *Place) GetIsAdArea() bool {
	if x != nil {
		return x.IsAdArea
	}
	return false
}

func (x *Place) GetAdAreaType() AdAreaType {
	if x != nil {
		return x.AdAreaType
	}
	return AdAreaType_AD_AREA_TYPE_UNSPECIFIED
}

func (x *Place) GetParentId() uint64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *Place) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *Place) GetLng() float64 {
	if x != nil {
		return x.Lng
	}
	return 0
}

func (x *Place) GetIsoCode2() string {
	if x != nil {
		return x.IsoCode2
	}
	return ""
}

func (x *Place) GetIsoCode3() string {
	if x != nil {
		return x.IsoCode3
	}
	return ""
}

func (x *Place) GetIsoNumber() string {
	if x != nil {
		return x.IsoNumber
	}
	return ""
}

// 地点本地化
type PlaceLocalization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主键
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 所属地点
	PlaceId uint64 `protobuf:"varint,2,opt,name=place_id,json=placeId,proto3" json:"place_id,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
	// 名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 地址
	Address string `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *PlaceLocalization) Reset() {
	*x = PlaceLocalization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceLocalization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceLocalization) ProtoMessage() {}

func (x *PlaceLocalization) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceLocalization.ProtoReflect.Descriptor instead.
func (*PlaceLocalization) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{2}
}

func (x *PlaceLocalization) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PlaceLocalization) GetPlaceId() uint64 {
	if x != nil {
		return x.PlaceId
	}
	return 0
}

func (x *PlaceLocalization) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *PlaceLocalization) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlaceLocalization) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

// 地点数据源
type PlaceSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主键
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 地图地点ID
	MapPlaceId string `protobuf:"bytes,2,opt,name=map_place_id,json=mapPlaceId,proto3" json:"map_place_id,omitempty"`
	// 地图平台
	MapPlatform MapPlatform `protobuf:"varint,3,opt,name=map_platform,json=mapPlatform,proto3,enum=tanlive.support.MapPlatform" json:"map_platform,omitempty"`
	// 地点详情
	PlaceDetail string `protobuf:"bytes,4,opt,name=place_detail,json=placeDetail,proto3" json:"place_detail,omitempty"`
}

func (x *PlaceSource) Reset() {
	*x = PlaceSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceSource) ProtoMessage() {}

func (x *PlaceSource) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceSource.ProtoReflect.Descriptor instead.
func (*PlaceSource) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{3}
}

func (x *PlaceSource) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PlaceSource) GetMapPlaceId() string {
	if x != nil {
		return x.MapPlaceId
	}
	return ""
}

func (x *PlaceSource) GetMapPlatform() MapPlatform {
	if x != nil {
		return x.MapPlatform
	}
	return MapPlatform_MAP_PLATFORM_UNSPECIFIED
}

func (x *PlaceSource) GetPlaceDetail() string {
	if x != nil {
		return x.PlaceDetail
	}
	return ""
}

// 地点全部信息
type FullPlace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地点详情
	Place *Place `protobuf:"bytes,1,opt,name=place,proto3" json:"place,omitempty"`
	// 数据源
	Source *PlaceSource `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	// 多语言
	Localizations []*PlaceLocalization `protobuf:"bytes,3,rep,name=localizations,proto3" json:"localizations,omitempty"`
}

func (x *FullPlace) Reset() {
	*x = FullPlace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FullPlace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullPlace) ProtoMessage() {}

func (x *FullPlace) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullPlace.ProtoReflect.Descriptor instead.
func (*FullPlace) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{4}
}

func (x *FullPlace) GetPlace() *Place {
	if x != nil {
		return x.Place
	}
	return nil
}

func (x *FullPlace) GetSource() *PlaceSource {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *FullPlace) GetLocalizations() []*PlaceLocalization {
	if x != nil {
		return x.Localizations
	}
	return nil
}

// 原始地址详情
type RawAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 大洲ID
	ContinentId uint64 `protobuf:"varint,1,opt,name=continent_id,json=continentId,proto3" json:"continent_id,omitempty"`
	// 国家ID
	CountryId uint64 `protobuf:"varint,2,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	// 一级行政区划ID
	Level1Id uint64 `protobuf:"varint,3,opt,name=level1_id,json=level1Id,proto3" json:"level1_id,omitempty"`
	// 二级行政区划ID
	Level2Id uint64 `protobuf:"varint,4,opt,name=level2_id,json=level2Id,proto3" json:"level2_id,omitempty"`
	// 详细地址ID
	DetailId uint64 `protobuf:"varint,5,opt,name=detail_id,json=detailId,proto3" json:"detail_id,omitempty"`
	// 地图平台
	MapPlatform MapPlatform `protobuf:"varint,6,opt,name=map_platform,json=mapPlatform,proto3,enum=tanlive.support.MapPlatform" json:"map_platform,omitempty"`
	// 地图地点ID
	MapPlaceId string `protobuf:"bytes,7,opt,name=map_place_id,json=mapPlaceId,proto3" json:"map_place_id,omitempty"`
}

func (x *RawAddress) Reset() {
	*x = RawAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawAddress) ProtoMessage() {}

func (x *RawAddress) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawAddress.ProtoReflect.Descriptor instead.
func (*RawAddress) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{5}
}

func (x *RawAddress) GetContinentId() uint64 {
	if x != nil {
		return x.ContinentId
	}
	return 0
}

func (x *RawAddress) GetCountryId() uint64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *RawAddress) GetLevel1Id() uint64 {
	if x != nil {
		return x.Level1Id
	}
	return 0
}

func (x *RawAddress) GetLevel2Id() uint64 {
	if x != nil {
		return x.Level2Id
	}
	return 0
}

func (x *RawAddress) GetDetailId() uint64 {
	if x != nil {
		return x.DetailId
	}
	return 0
}

func (x *RawAddress) GetMapPlatform() MapPlatform {
	if x != nil {
		return x.MapPlatform
	}
	return MapPlatform_MAP_PLATFORM_UNSPECIFIED
}

func (x *RawAddress) GetMapPlaceId() string {
	if x != nil {
		return x.MapPlaceId
	}
	return ""
}

// 结构化地址
type StructuredAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 大洲
	Continent *Place `protobuf:"bytes,1,opt,name=continent,proto3" json:"continent,omitempty"`
	// 国家
	Country *Place `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	// 一级行政区划
	Level1 *Place `protobuf:"bytes,3,opt,name=level1,proto3" json:"level1,omitempty"`
	// 二级行政区划
	Level2 *Place `protobuf:"bytes,4,opt,name=level2,proto3" json:"level2,omitempty"`
	// 详细地点
	Detail *Place `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail,omitempty"`
	// 地图平台
	MapPlatform MapPlatform `protobuf:"varint,6,opt,name=map_platform,json=mapPlatform,proto3,enum=tanlive.support.MapPlatform" json:"map_platform,omitempty"`
	// 地图地点ID
	MapPlaceId string `protobuf:"bytes,7,opt,name=map_place_id,json=mapPlaceId,proto3" json:"map_place_id,omitempty"`
}

func (x *StructuredAddress) Reset() {
	*x = StructuredAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StructuredAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StructuredAddress) ProtoMessage() {}

func (x *StructuredAddress) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StructuredAddress.ProtoReflect.Descriptor instead.
func (*StructuredAddress) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{6}
}

func (x *StructuredAddress) GetContinent() *Place {
	if x != nil {
		return x.Continent
	}
	return nil
}

func (x *StructuredAddress) GetCountry() *Place {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *StructuredAddress) GetLevel1() *Place {
	if x != nil {
		return x.Level1
	}
	return nil
}

func (x *StructuredAddress) GetLevel2() *Place {
	if x != nil {
		return x.Level2
	}
	return nil
}

func (x *StructuredAddress) GetDetail() *Place {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *StructuredAddress) GetMapPlatform() MapPlatform {
	if x != nil {
		return x.MapPlatform
	}
	return MapPlatform_MAP_PLATFORM_UNSPECIFIED
}

func (x *StructuredAddress) GetMapPlaceId() string {
	if x != nil {
		return x.MapPlaceId
	}
	return ""
}

// 结构化地址全部信息
type StructuredFullAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 国家
	Country *FullPlace `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	// 一级行政区划
	Level1 *FullPlace `protobuf:"bytes,2,opt,name=level1,proto3" json:"level1,omitempty"`
	// 二级行政区划
	Level2 *FullPlace `protobuf:"bytes,3,opt,name=level2,proto3" json:"level2,omitempty"`
	// 详细地点
	Detail *FullPlace `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail,omitempty"`
	// 地图平台
	MapPlatform MapPlatform `protobuf:"varint,5,opt,name=map_platform,json=mapPlatform,proto3,enum=tanlive.support.MapPlatform" json:"map_platform,omitempty"`
	// 地图地点ID
	MapPlaceId string `protobuf:"bytes,6,opt,name=map_place_id,json=mapPlaceId,proto3" json:"map_place_id,omitempty"`
}

func (x *StructuredFullAddress) Reset() {
	*x = StructuredFullAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StructuredFullAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StructuredFullAddress) ProtoMessage() {}

func (x *StructuredFullAddress) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StructuredFullAddress.ProtoReflect.Descriptor instead.
func (*StructuredFullAddress) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{7}
}

func (x *StructuredFullAddress) GetCountry() *FullPlace {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *StructuredFullAddress) GetLevel1() *FullPlace {
	if x != nil {
		return x.Level1
	}
	return nil
}

func (x *StructuredFullAddress) GetLevel2() *FullPlace {
	if x != nil {
		return x.Level2
	}
	return nil
}

func (x *StructuredFullAddress) GetDetail() *FullPlace {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *StructuredFullAddress) GetMapPlatform() MapPlatform {
	if x != nil {
		return x.MapPlatform
	}
	return MapPlatform_MAP_PLATFORM_UNSPECIFIED
}

func (x *StructuredFullAddress) GetMapPlaceId() string {
	if x != nil {
		return x.MapPlaceId
	}
	return ""
}

// 地点详情
type PlaceWithParents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地点详情
	Place *Place `protobuf:"bytes,1,opt,name=place,proto3" json:"place,omitempty"`
	// 父级列表
	Parents []*Place `protobuf:"bytes,2,rep,name=parents,proto3" json:"parents,omitempty"`
}

func (x *PlaceWithParents) Reset() {
	*x = PlaceWithParents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceWithParents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceWithParents) ProtoMessage() {}

func (x *PlaceWithParents) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceWithParents.ProtoReflect.Descriptor instead.
func (*PlaceWithParents) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{8}
}

func (x *PlaceWithParents) GetPlace() *Place {
	if x != nil {
		return x.Place
	}
	return nil
}

func (x *PlaceWithParents) GetParents() []*Place {
	if x != nil {
		return x.Parents
	}
	return nil
}

// 地址解析项
type GeocodingItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地图平台
	MapPlatform MapPlatform `protobuf:"varint,1,opt,name=map_platform,json=mapPlatform,proto3,enum=tanlive.support.MapPlatform" json:"map_platform,omitempty"`
	// 地图地点ID
	MapPlaceId string `protobuf:"bytes,2,opt,name=map_place_id,json=mapPlaceId,proto3" json:"map_place_id,omitempty"`
}

func (x *GeocodingItem) Reset() {
	*x = GeocodingItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeocodingItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeocodingItem) ProtoMessage() {}

func (x *GeocodingItem) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeocodingItem.ProtoReflect.Descriptor instead.
func (*GeocodingItem) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{9}
}

func (x *GeocodingItem) GetMapPlatform() MapPlatform {
	if x != nil {
		return x.MapPlatform
	}
	return MapPlatform_MAP_PLATFORM_UNSPECIFIED
}

func (x *GeocodingItem) GetMapPlaceId() string {
	if x != nil {
		return x.MapPlaceId
	}
	return ""
}

type MapHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 键名
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// 键值
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *MapHeader) Reset() {
	*x = MapHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapHeader) ProtoMessage() {}

func (x *MapHeader) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapHeader.ProtoReflect.Descriptor instead.
func (*MapHeader) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{10}
}

func (x *MapHeader) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *MapHeader) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 地图请求
type MapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求方法
	Method string `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	// 请求路径
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	// 请求头
	Header []*MapHeader `protobuf:"bytes,3,rep,name=header,proto3" json:"header,omitempty"`
	// 请求体
	Body string `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *MapRequest) Reset() {
	*x = MapRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapRequest) ProtoMessage() {}

func (x *MapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapRequest.ProtoReflect.Descriptor instead.
func (*MapRequest) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{11}
}

func (x *MapRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *MapRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *MapRequest) GetHeader() []*MapHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MapRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

// 地图响应
type MapResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应头
	Header []*MapHeader `protobuf:"bytes,1,rep,name=header,proto3" json:"header,omitempty"`
	// 响应体
	Body string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *MapResponse) Reset() {
	*x = MapResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapResponse) ProtoMessage() {}

func (x *MapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapResponse.ProtoReflect.Descriptor instead.
func (*MapResponse) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{12}
}

func (x *MapResponse) GetHeader() []*MapHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MapResponse) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

// 通过数据类型查询条件
type GetPlaceCondByDataType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据类型
	DataType base.DataType `protobuf:"varint,1,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 数据字段
	// 团队：location、service_region 资源：location、target_regions
	DataField string `protobuf:"bytes,2,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
	// 父级ID，不传返回大洲数据
	ParentId uint64 `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
}

func (x *GetPlaceCondByDataType) Reset() {
	*x = GetPlaceCondByDataType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlaceCondByDataType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlaceCondByDataType) ProtoMessage() {}

func (x *GetPlaceCondByDataType) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlaceCondByDataType.ProtoReflect.Descriptor instead.
func (*GetPlaceCondByDataType) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{13}
}

func (x *GetPlaceCondByDataType) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *GetPlaceCondByDataType) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

func (x *GetPlaceCondByDataType) GetParentId() uint64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

// 通过地点ID查询条件
type GetPlaceCondByPlaceId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlaceId []uint64 `protobuf:"varint,5,rep,packed,name=place_id,json=placeId,proto3" json:"place_id,omitempty"`
}

func (x *GetPlaceCondByPlaceId) Reset() {
	*x = GetPlaceCondByPlaceId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlaceCondByPlaceId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlaceCondByPlaceId) ProtoMessage() {}

func (x *GetPlaceCondByPlaceId) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlaceCondByPlaceId.ProtoReflect.Descriptor instead.
func (*GetPlaceCondByPlaceId) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{14}
}

func (x *GetPlaceCondByPlaceId) GetPlaceId() []uint64 {
	if x != nil {
		return x.PlaceId
	}
	return nil
}

// 数据项
type Translation_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 键名
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// 键值
	Values []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *Translation_Item) Reset() {
	*x = Translation_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_support_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Translation_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Translation_Item) ProtoMessage() {}

func (x *Translation_Item) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_support_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Translation_Item.ProtoReflect.Descriptor instead.
func (*Translation_Item) Descriptor() ([]byte, []int) {
	return file_tanlive_support_support_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Translation_Item) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Translation_Item) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_tanlive_support_support_proto protoreflect.FileDescriptor

var file_tanlive_support_support_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75,
	0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x80, 0x03, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x41, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x0b, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x82, 0x88, 0x27, 0x09, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x2d, 0x0a, 0x0b, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x49, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x1a, 0x5d, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1e, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1d, 0x82, 0x88, 0x27,
	0x19, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x2c,
	0x64, 0x69, 0x76, 0x65, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x22, 0xd0, 0x02, 0x0a, 0x05, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61,
	0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x1c,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x64, 0x41, 0x72, 0x65, 0x61, 0x12, 0x3d, 0x0a, 0x0c,
	0x61, 0x64, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x41, 0x64, 0x41, 0x72, 0x65, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x61, 0x64, 0x41, 0x72, 0x65, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6e,
	0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x6f, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x32, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x73, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x32, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x6f,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x33, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x73,
	0x6f, 0x43, 0x6f, 0x64, 0x65, 0x33, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x6f, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x6f, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x80, 0x01, 0x0a, 0x11, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4c,
	0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xa3, 0x01, 0x0a, 0x0b, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6d, 0x61, 0x70, 0x5f,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0c, 0x6d, 0x61,
	0x70, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x0b,
	0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xb9,
	0x01, 0x0a, 0x09, 0x46, 0x75, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x05,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x52, 0x05, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x48, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4c,
	0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x88, 0x02, 0x0a, 0x0a, 0x52,
	0x61, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e,
	0x74, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x31, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x32, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x32, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0c, 0x6d, 0x61, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x12, 0x20, 0x0a, 0x0c, 0x6d, 0x61, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x70, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x8a, 0x03, 0x0a, 0x11, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x75, 0x72, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x63,
	0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x30, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x06, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x31, 0x12, 0x2e, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x06, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x32, 0x12, 0x2e, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x4d, 0x0a, 0x0c, 0x6d, 0x61, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x50,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x12, 0x2e, 0x0a, 0x0c, 0x6d, 0x61, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0a, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x63, 0x65,
	0x49, 0x64, 0x22, 0xf6, 0x02, 0x0a, 0x15, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x64, 0x46, 0x75, 0x6c, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x42, 0x0a, 0x07,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x46, 0x75, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x32, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x06, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x31, 0x12, 0x32, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x63, 0x65,
	0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x50,
	0x6c, 0x61, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4d, 0x0a, 0x0c,
	0x6d, 0x61, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0b,
	0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2e, 0x0a, 0x0c, 0x6d,
	0x61, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x0a, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x10, 0x50,
	0x6c, 0x61, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x2c, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x05, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x30, 0x0a,
	0x07, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x07, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x22,
	0x8e, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x4d, 0x0a, 0x0c, 0x6d, 0x61, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x12, 0x2e, 0x0a, 0x0c, 0x6d, 0x61, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x0a, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64,
	0x22, 0x33, 0x0a, 0x09, 0x4d, 0x61, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x0a, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x20, 0x0a, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x32, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d,
	0x61, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x22, 0x55, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22, 0xb4, 0x01, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x42, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x0a, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x2a, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0x82, 0x88, 0x27, 0x09, 0x6f,
	0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x40, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x43, 0x6f,
	0x6e, 0x64, 0x42, 0x79, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0c, 0x82,
	0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x07, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x49, 0x64, 0x2a, 0x94, 0x01, 0x0a, 0x0a, 0x41, 0x64, 0x41, 0x72, 0x65, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x44, 0x5f, 0x41, 0x52, 0x45, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x44, 0x5f, 0x41, 0x52, 0x45, 0x41, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x18, 0x0a,
	0x14, 0x41, 0x44, 0x5f, 0x41, 0x52, 0x45, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x44, 0x5f, 0x41, 0x52,
	0x45, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x31, 0x10,
	0x03, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x44, 0x5f, 0x41, 0x52, 0x45, 0x41, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x32, 0x10, 0x04, 0x2a, 0x5e, 0x0a, 0x0b, 0x4d,
	0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x41,
	0x50, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x50, 0x5f,
	0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x10,
	0x01, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x41, 0x50, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x54, 0x45, 0x4e, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x2a, 0x6e, 0x0a, 0x0f, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x21,
	0x0a, 0x1d, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x50, 0x5f, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x50, 0x5f,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x50, 0x53, 0x10, 0x01, 0x12, 0x1c, 0x0a,
	0x18, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x50, 0x5f, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x53, 0x10, 0x02, 0x42, 0x41, 0x5a, 0x3f, 0x65,
	0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_support_support_proto_rawDescOnce sync.Once
	file_tanlive_support_support_proto_rawDescData = file_tanlive_support_support_proto_rawDesc
)

func file_tanlive_support_support_proto_rawDescGZIP() []byte {
	file_tanlive_support_support_proto_rawDescOnce.Do(func() {
		file_tanlive_support_support_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_support_support_proto_rawDescData)
	})
	return file_tanlive_support_support_proto_rawDescData
}

var file_tanlive_support_support_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_tanlive_support_support_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_tanlive_support_support_proto_goTypes = []interface{}{
	(AdAreaType)(0),                // 0: tanlive.support.AdAreaType
	(MapPlatform)(0),               // 1: tanlive.support.MapPlatform
	(GoogleMapClient)(0),           // 2: tanlive.support.GoogleMapClient
	(*Translation)(nil),            // 3: tanlive.support.Translation
	(*Place)(nil),                  // 4: tanlive.support.Place
	(*PlaceLocalization)(nil),      // 5: tanlive.support.PlaceLocalization
	(*PlaceSource)(nil),            // 6: tanlive.support.PlaceSource
	(*FullPlace)(nil),              // 7: tanlive.support.FullPlace
	(*RawAddress)(nil),             // 8: tanlive.support.RawAddress
	(*StructuredAddress)(nil),      // 9: tanlive.support.StructuredAddress
	(*StructuredFullAddress)(nil),  // 10: tanlive.support.StructuredFullAddress
	(*PlaceWithParents)(nil),       // 11: tanlive.support.PlaceWithParents
	(*GeocodingItem)(nil),          // 12: tanlive.support.GeocodingItem
	(*MapHeader)(nil),              // 13: tanlive.support.MapHeader
	(*MapRequest)(nil),             // 14: tanlive.support.MapRequest
	(*MapResponse)(nil),            // 15: tanlive.support.MapResponse
	(*GetPlaceCondByDataType)(nil), // 16: tanlive.support.GetPlaceCondByDataType
	(*GetPlaceCondByPlaceId)(nil),  // 17: tanlive.support.GetPlaceCondByPlaceId
	(*Translation_Item)(nil),       // 18: tanlive.support.Translation.Item
	(base.DataType)(0),             // 19: tanlive.base.DataType
}
var file_tanlive_support_support_proto_depIdxs = []int32{
	19, // 0: tanlive.support.Translation.data_type:type_name -> tanlive.base.DataType
	18, // 1: tanlive.support.Translation.content:type_name -> tanlive.support.Translation.Item
	0,  // 2: tanlive.support.Place.ad_area_type:type_name -> tanlive.support.AdAreaType
	1,  // 3: tanlive.support.PlaceSource.map_platform:type_name -> tanlive.support.MapPlatform
	4,  // 4: tanlive.support.FullPlace.place:type_name -> tanlive.support.Place
	6,  // 5: tanlive.support.FullPlace.source:type_name -> tanlive.support.PlaceSource
	5,  // 6: tanlive.support.FullPlace.localizations:type_name -> tanlive.support.PlaceLocalization
	1,  // 7: tanlive.support.RawAddress.map_platform:type_name -> tanlive.support.MapPlatform
	4,  // 8: tanlive.support.StructuredAddress.continent:type_name -> tanlive.support.Place
	4,  // 9: tanlive.support.StructuredAddress.country:type_name -> tanlive.support.Place
	4,  // 10: tanlive.support.StructuredAddress.level1:type_name -> tanlive.support.Place
	4,  // 11: tanlive.support.StructuredAddress.level2:type_name -> tanlive.support.Place
	4,  // 12: tanlive.support.StructuredAddress.detail:type_name -> tanlive.support.Place
	1,  // 13: tanlive.support.StructuredAddress.map_platform:type_name -> tanlive.support.MapPlatform
	7,  // 14: tanlive.support.StructuredFullAddress.country:type_name -> tanlive.support.FullPlace
	7,  // 15: tanlive.support.StructuredFullAddress.level1:type_name -> tanlive.support.FullPlace
	7,  // 16: tanlive.support.StructuredFullAddress.level2:type_name -> tanlive.support.FullPlace
	7,  // 17: tanlive.support.StructuredFullAddress.detail:type_name -> tanlive.support.FullPlace
	1,  // 18: tanlive.support.StructuredFullAddress.map_platform:type_name -> tanlive.support.MapPlatform
	4,  // 19: tanlive.support.PlaceWithParents.place:type_name -> tanlive.support.Place
	4,  // 20: tanlive.support.PlaceWithParents.parents:type_name -> tanlive.support.Place
	1,  // 21: tanlive.support.GeocodingItem.map_platform:type_name -> tanlive.support.MapPlatform
	13, // 22: tanlive.support.MapRequest.header:type_name -> tanlive.support.MapHeader
	13, // 23: tanlive.support.MapResponse.header:type_name -> tanlive.support.MapHeader
	19, // 24: tanlive.support.GetPlaceCondByDataType.data_type:type_name -> tanlive.base.DataType
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_tanlive_support_support_proto_init() }
func file_tanlive_support_support_proto_init() {
	if File_tanlive_support_support_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_support_support_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Translation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Place); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceLocalization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FullPlace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StructuredAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StructuredFullAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceWithParents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeocodingItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlaceCondByDataType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlaceCondByPlaceId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_support_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Translation_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_support_support_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_support_support_proto_goTypes,
		DependencyIndexes: file_tanlive_support_support_proto_depIdxs,
		EnumInfos:         file_tanlive_support_support_proto_enumTypes,
		MessageInfos:      file_tanlive_support_support_proto_msgTypes,
	}.Build()
	File_tanlive_support_support_proto = out.File
	file_tanlive_support_support_proto_rawDesc = nil
	file_tanlive_support_support_proto_goTypes = nil
	file_tanlive_support_support_proto_depIdxs = nil
}
