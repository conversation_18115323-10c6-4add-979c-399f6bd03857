syntax = "proto3";

package tanlive.support;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/support";

import "google/protobuf/empty.proto";
import "tanlive/base/ugc.proto";
import "tanlive/options.proto";
import "tanlive/support/support.proto";
import "google/protobuf/any.proto";


// 搜索服务
service SupportService {
  // 翻译
  rpc Translate(ReqTranslate) returns (RspTranslate);
  // 代理腾讯地图请求
  rpc ProxyTencentMap(ReqProxyTencentMap) returns (RspProxyTencentMap);
  // 代理谷歌地图请求
  rpc ProxyGoogleMap(ReqProxyGoogleMap) returns (RspProxyGoogleMap);
  // 查询地点选择器数据
  rpc GetPlaceSelector(ReqGetPlaceSelector) returns (RspGetPlaceSelector);
  // 搜索地点
  rpc SearchPlaces(ReqSearchPlaces) returns (RspSearchPlaces);
  // 地址解析
  rpc Geocode(ReqGeocode) returns (RspGeocode);
  // 预解析
  rpc PreGeocode(ReqGeocode) returns (RspPreGeocode);
  // 同步地址
  rpc SyncAddress(ReqSyncAddress) returns (google.protobuf.Empty);
  // 删除地址
  rpc DeleteAddress(ReqDeleteAddress) returns (google.protobuf.Empty);
  // 替换地址
  rpc ReplaceAddress(ReqReplaceAddress) returns (google.protobuf.Empty);
  // 发布地址
  rpc PublishAddress(ReqPublishAddress) returns (google.protobuf.Empty);
  // 查询地址（以数据项分组）
  rpc GetAddressesByDataItem(ReqGetAddressesByDataItem) returns (RspGetAddressesByDataItem);
  // 通过地址筛选数据
  rpc GetDataItemByAddresses(ReqGetDataItemByAddresses)returns (RspGetDataItemByAddresses);
  // 创建指标
  rpc CreateMetric(ReqCreateMetric) returns (google.protobuf.Empty);
}

message ReqCreateMetric {
  message Metric {
    string key = 1;
    int64 value_int = 2;
    float value_float = 3;
    string value_string =4;
    string value_long_string = 5;
  }
  repeated Metric metrics = 1;
  string type = 2;
}

message ReqTranslate {
  // 翻译列表
  repeated Translation translations = 1;
}

message RspTranslate {
  // 翻译列表
  repeated Translation translations = 1;
}

message ReqProxyTencentMap {
  // 请求
  MapRequest request = 1 [(validator) = "required"];
}

message RspProxyTencentMap {
  // 响应
  MapResponse response = 1;
}

message ReqProxyGoogleMap {
  // 请求
  MapRequest request = 1 [(validator) = "required"];
  // 客户端
  GoogleMapClient client = 2 [(validator) = "required"];
}

message RspProxyGoogleMap {
  // 响应
  MapResponse response = 1;
}

message ReqGetPlaceSelector {
  // 查询条件
  oneof condition {
    // 通过数据类型查询
    GetPlaceCondByDataType by_data_type = 1 [(validator) = "required"];
    // 通过地点ID查询
    GetPlaceCondByPlaceId by_place_id = 2 [(validator) = "required"];
  }
  // 指定语言
  string lang = 3 [(validator) = "required"];
}

message RspGetPlaceSelector {
  // 地点列表
  repeated Place places = 1;
}

message ReqSearchPlaces {
  // 按名称模糊搜索
  string name = 1 [(validator) = "required"];
  // 指定语言
  string lang = 2 [(validator) = "required"];
  // 数据类型
  base.DataType data_type = 3 [(validator) = "required"];
  // 数据字段
  // 团队：location、service_region 资源：location、target_regions
  string data_field = 4 [(validator) = "required"];
}

message RspSearchPlaces {
  // 地点列表
  repeated PlaceWithParents places = 1;
}

message ReqGeocode {
  // 地址解析项
  repeated GeocodingItem geocoding_items = 1 [(validator) = "required"];
}

message RspGeocode {
  message Result {
    // 错误码
    int32 code = 1;
    // 地址详情
    StructuredFullAddress address = 2;
  }
  // 结果
  repeated Result results = 1;
}

message RspPreGeocode {
  message Result {
    oneof address {
      // 已解析的原始地址
      RawAddress raw_address = 1;
      // 未解析的项
      GeocodingItem geocoding_item = 2;
    }
  }
  // 结果
  repeated Result results = 1;
}

message ReqSyncAddress {
  message Address {
    oneof address {
      // 原始地址
      RawAddress raw_address = 1;
      // 结构化地址
      StructuredFullAddress structured_address = 2;
    }
  }
  message Item {
    // 数据项
    base.DataItem data_item = 1 [(validator) = "required"];
    // 地址详情
    repeated Address addresses = 2 [(validator) = "required"];
    // 是否已发布
    bool is_published = 3;
  }
  // 地址列表
  repeated Item items = 1 [(validator) = "required"];
}

message ReqDeleteAddress {
  // 数据列表
  repeated base.DataItem data_items = 1;
}

message ReqReplaceAddress {
  message Item {
    // 源数据
    base.DataItem source = 1 [(validator) = "required"];
    // 目标数据
    base.DataItem target = 2 [(validator) = "required"];
    // 是否已发布
    bool is_published = 3;
  }
  repeated Item items = 1 [(validator) = "required"];
}

message ReqPublishAddress {
  message Item {
    // 数据想
    base.DataItem data_item = 1 [(validator) = "required"];
    // 是否已发布
    bool is_published = 2;
  }
  repeated Item items = 1 [(validator) = "required"];
}

message ReqGetAddressesByDataItem {
  // 数据项列表
  repeated base.DataItem data_items = 1 [(validator) = "required"];
  // 语言
  string lang = 2;
}

message RspGetAddressesByDataItem {
  message Item {
    // 数据项
    base.DataItem data_item = 1;
    // 地址列表
    repeated StructuredAddress addresses = 2;
  }
  repeated Item items = 1;
}

message ReqGetDataItemByAddresses {
  message Address {
    // 原始地址
    RawAddress raw_address = 1;
    // 数据字段
    string data_field = 3;
  }
  // 地址详情
  repeated Address addresses = 1 ;
  // 数据类型
  base.DataType data_type = 2;
  // 数据字段
  string data_field = 3;
}

message RspGetDataItemByAddresses {
  // 数据项列表
  repeated uint64 data_ids = 1 ;
}
