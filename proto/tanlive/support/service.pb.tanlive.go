// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package support

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Request": "required",
	}, &ReqProxyTencentMap{})
}

func (x *ReqProxyTencentMap) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Request": "required",
		"Client":  "required",
	}, &ReqProxyGoogleMap{})
}

func (x *ReqProxyGoogleMap) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ByDataType": "required",
		"ByPlaceId":  "required",
		"Lang":       "required",
	}, &ReqGetPlaceSelector{})
}

func (x *ReqGetPlaceSelector) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Name":      "required",
		"Lang":      "required",
		"DataType":  "required",
		"DataField": "required",
	}, &ReqSearchPlaces{})
}

func (x *ReqSearchPlaces) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"GeocodingItems": "required",
	}, &ReqGeocode{})
}

func (x *ReqGeocode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required",
	}, &ReqSyncAddress{})
}

func (x *ReqSyncAddress) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DataItem":  "required",
		"Addresses": "required",
	}, &ReqSyncAddress_Item{})
}

func (x *ReqSyncAddress_Item) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required",
	}, &ReqReplaceAddress{})
}

func (x *ReqReplaceAddress) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Source": "required",
		"Target": "required",
	}, &ReqReplaceAddress_Item{})
}

func (x *ReqReplaceAddress_Item) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required",
	}, &ReqPublishAddress{})
}

func (x *ReqPublishAddress) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DataItem": "required",
	}, &ReqPublishAddress_Item{})
}

func (x *ReqPublishAddress_Item) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DataItems": "required",
	}, &ReqGetAddressesByDataItem{})
}

func (x *ReqGetAddressesByDataItem) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqCreateMetric) MaskInLog() any {
	if x == nil {
		return (*ReqCreateMetric)(nil)
	}

	y := proto.Clone(x).(*ReqCreateMetric)
	for k, v := range y.Metrics {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Metrics[k] = vv.MaskInLog().(*ReqCreateMetric_Metric)
		}
	}

	return y
}

func (x *ReqCreateMetric) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateMetric)(nil)
	}

	y := x
	for k, v := range y.Metrics {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Metrics[k] = vv.MaskInRpc().(*ReqCreateMetric_Metric)
		}
	}

	return y
}

func (x *ReqCreateMetric) MaskInBff() any {
	if x == nil {
		return (*ReqCreateMetric)(nil)
	}

	y := x
	for k, v := range y.Metrics {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Metrics[k] = vv.MaskInBff().(*ReqCreateMetric_Metric)
		}
	}

	return y
}

func (x *ReqTranslate) MaskInLog() any {
	if x == nil {
		return (*ReqTranslate)(nil)
	}

	y := proto.Clone(x).(*ReqTranslate)
	for k, v := range y.Translations {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Translations[k] = vv.MaskInLog().(*Translation)
		}
	}

	return y
}

func (x *ReqTranslate) MaskInRpc() any {
	if x == nil {
		return (*ReqTranslate)(nil)
	}

	y := x
	for k, v := range y.Translations {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Translations[k] = vv.MaskInRpc().(*Translation)
		}
	}

	return y
}

func (x *ReqTranslate) MaskInBff() any {
	if x == nil {
		return (*ReqTranslate)(nil)
	}

	y := x
	for k, v := range y.Translations {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Translations[k] = vv.MaskInBff().(*Translation)
		}
	}

	return y
}

func (x *RspTranslate) MaskInLog() any {
	if x == nil {
		return (*RspTranslate)(nil)
	}

	y := proto.Clone(x).(*RspTranslate)
	for k, v := range y.Translations {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Translations[k] = vv.MaskInLog().(*Translation)
		}
	}

	return y
}

func (x *RspTranslate) MaskInRpc() any {
	if x == nil {
		return (*RspTranslate)(nil)
	}

	y := x
	for k, v := range y.Translations {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Translations[k] = vv.MaskInRpc().(*Translation)
		}
	}

	return y
}

func (x *RspTranslate) MaskInBff() any {
	if x == nil {
		return (*RspTranslate)(nil)
	}

	y := x
	for k, v := range y.Translations {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Translations[k] = vv.MaskInBff().(*Translation)
		}
	}

	return y
}

func (x *ReqProxyTencentMap) MaskInLog() any {
	if x == nil {
		return (*ReqProxyTencentMap)(nil)
	}

	y := proto.Clone(x).(*ReqProxyTencentMap)
	if v, ok := any(y.Request).(interface{ MaskInLog() any }); ok {
		y.Request = v.MaskInLog().(*MapRequest)
	}

	return y
}

func (x *ReqProxyTencentMap) MaskInRpc() any {
	if x == nil {
		return (*ReqProxyTencentMap)(nil)
	}

	y := x
	if v, ok := any(y.Request).(interface{ MaskInRpc() any }); ok {
		y.Request = v.MaskInRpc().(*MapRequest)
	}

	return y
}

func (x *ReqProxyTencentMap) MaskInBff() any {
	if x == nil {
		return (*ReqProxyTencentMap)(nil)
	}

	y := x
	if v, ok := any(y.Request).(interface{ MaskInBff() any }); ok {
		y.Request = v.MaskInBff().(*MapRequest)
	}

	return y
}

func (x *RspProxyTencentMap) MaskInLog() any {
	if x == nil {
		return (*RspProxyTencentMap)(nil)
	}

	y := proto.Clone(x).(*RspProxyTencentMap)
	if v, ok := any(y.Response).(interface{ MaskInLog() any }); ok {
		y.Response = v.MaskInLog().(*MapResponse)
	}

	return y
}

func (x *RspProxyTencentMap) MaskInRpc() any {
	if x == nil {
		return (*RspProxyTencentMap)(nil)
	}

	y := x
	if v, ok := any(y.Response).(interface{ MaskInRpc() any }); ok {
		y.Response = v.MaskInRpc().(*MapResponse)
	}

	return y
}

func (x *RspProxyTencentMap) MaskInBff() any {
	if x == nil {
		return (*RspProxyTencentMap)(nil)
	}

	y := x
	if v, ok := any(y.Response).(interface{ MaskInBff() any }); ok {
		y.Response = v.MaskInBff().(*MapResponse)
	}

	return y
}

func (x *ReqProxyGoogleMap) MaskInLog() any {
	if x == nil {
		return (*ReqProxyGoogleMap)(nil)
	}

	y := proto.Clone(x).(*ReqProxyGoogleMap)
	if v, ok := any(y.Request).(interface{ MaskInLog() any }); ok {
		y.Request = v.MaskInLog().(*MapRequest)
	}

	return y
}

func (x *ReqProxyGoogleMap) MaskInRpc() any {
	if x == nil {
		return (*ReqProxyGoogleMap)(nil)
	}

	y := x
	if v, ok := any(y.Request).(interface{ MaskInRpc() any }); ok {
		y.Request = v.MaskInRpc().(*MapRequest)
	}

	return y
}

func (x *ReqProxyGoogleMap) MaskInBff() any {
	if x == nil {
		return (*ReqProxyGoogleMap)(nil)
	}

	y := x
	if v, ok := any(y.Request).(interface{ MaskInBff() any }); ok {
		y.Request = v.MaskInBff().(*MapRequest)
	}

	return y
}

func (x *RspProxyGoogleMap) MaskInLog() any {
	if x == nil {
		return (*RspProxyGoogleMap)(nil)
	}

	y := proto.Clone(x).(*RspProxyGoogleMap)
	if v, ok := any(y.Response).(interface{ MaskInLog() any }); ok {
		y.Response = v.MaskInLog().(*MapResponse)
	}

	return y
}

func (x *RspProxyGoogleMap) MaskInRpc() any {
	if x == nil {
		return (*RspProxyGoogleMap)(nil)
	}

	y := x
	if v, ok := any(y.Response).(interface{ MaskInRpc() any }); ok {
		y.Response = v.MaskInRpc().(*MapResponse)
	}

	return y
}

func (x *RspProxyGoogleMap) MaskInBff() any {
	if x == nil {
		return (*RspProxyGoogleMap)(nil)
	}

	y := x
	if v, ok := any(y.Response).(interface{ MaskInBff() any }); ok {
		y.Response = v.MaskInBff().(*MapResponse)
	}

	return y
}

func (x *ReqGetPlaceSelector) MaskInLog() any {
	if x == nil {
		return (*ReqGetPlaceSelector)(nil)
	}

	y := proto.Clone(x).(*ReqGetPlaceSelector)
	switch v := y.Condition.(type) {
	case *ReqGetPlaceSelector_ByDataType:
		if vv, ok := any(v.ByDataType).(interface{ MaskInLog() any }); ok {
			v.ByDataType = vv.MaskInLog().(*GetPlaceCondByDataType)
		}
	case *ReqGetPlaceSelector_ByPlaceId:
		if vv, ok := any(v.ByPlaceId).(interface{ MaskInLog() any }); ok {
			v.ByPlaceId = vv.MaskInLog().(*GetPlaceCondByPlaceId)
		}
	}

	return y
}

func (x *ReqGetPlaceSelector) MaskInRpc() any {
	if x == nil {
		return (*ReqGetPlaceSelector)(nil)
	}

	y := x
	switch v := y.Condition.(type) {
	case *ReqGetPlaceSelector_ByDataType:
		if vv, ok := any(v.ByDataType).(interface{ MaskInRpc() any }); ok {
			v.ByDataType = vv.MaskInRpc().(*GetPlaceCondByDataType)
		}
	case *ReqGetPlaceSelector_ByPlaceId:
		if vv, ok := any(v.ByPlaceId).(interface{ MaskInRpc() any }); ok {
			v.ByPlaceId = vv.MaskInRpc().(*GetPlaceCondByPlaceId)
		}
	}

	return y
}

func (x *ReqGetPlaceSelector) MaskInBff() any {
	if x == nil {
		return (*ReqGetPlaceSelector)(nil)
	}

	y := x
	switch v := y.Condition.(type) {
	case *ReqGetPlaceSelector_ByDataType:
		if vv, ok := any(v.ByDataType).(interface{ MaskInBff() any }); ok {
			v.ByDataType = vv.MaskInBff().(*GetPlaceCondByDataType)
		}
	case *ReqGetPlaceSelector_ByPlaceId:
		if vv, ok := any(v.ByPlaceId).(interface{ MaskInBff() any }); ok {
			v.ByPlaceId = vv.MaskInBff().(*GetPlaceCondByPlaceId)
		}
	}

	return y
}

func (x *RspGetPlaceSelector) MaskInLog() any {
	if x == nil {
		return (*RspGetPlaceSelector)(nil)
	}

	y := proto.Clone(x).(*RspGetPlaceSelector)
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Places[k] = vv.MaskInLog().(*Place)
		}
	}

	return y
}

func (x *RspGetPlaceSelector) MaskInRpc() any {
	if x == nil {
		return (*RspGetPlaceSelector)(nil)
	}

	y := x
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Places[k] = vv.MaskInRpc().(*Place)
		}
	}

	return y
}

func (x *RspGetPlaceSelector) MaskInBff() any {
	if x == nil {
		return (*RspGetPlaceSelector)(nil)
	}

	y := x
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Places[k] = vv.MaskInBff().(*Place)
		}
	}

	return y
}

func (x *RspSearchPlaces) MaskInLog() any {
	if x == nil {
		return (*RspSearchPlaces)(nil)
	}

	y := proto.Clone(x).(*RspSearchPlaces)
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Places[k] = vv.MaskInLog().(*PlaceWithParents)
		}
	}

	return y
}

func (x *RspSearchPlaces) MaskInRpc() any {
	if x == nil {
		return (*RspSearchPlaces)(nil)
	}

	y := x
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Places[k] = vv.MaskInRpc().(*PlaceWithParents)
		}
	}

	return y
}

func (x *RspSearchPlaces) MaskInBff() any {
	if x == nil {
		return (*RspSearchPlaces)(nil)
	}

	y := x
	for k, v := range y.Places {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Places[k] = vv.MaskInBff().(*PlaceWithParents)
		}
	}

	return y
}

func (x *ReqGeocode) MaskInLog() any {
	if x == nil {
		return (*ReqGeocode)(nil)
	}

	y := proto.Clone(x).(*ReqGeocode)
	for k, v := range y.GeocodingItems {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.GeocodingItems[k] = vv.MaskInLog().(*GeocodingItem)
		}
	}

	return y
}

func (x *ReqGeocode) MaskInRpc() any {
	if x == nil {
		return (*ReqGeocode)(nil)
	}

	y := x
	for k, v := range y.GeocodingItems {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.GeocodingItems[k] = vv.MaskInRpc().(*GeocodingItem)
		}
	}

	return y
}

func (x *ReqGeocode) MaskInBff() any {
	if x == nil {
		return (*ReqGeocode)(nil)
	}

	y := x
	for k, v := range y.GeocodingItems {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.GeocodingItems[k] = vv.MaskInBff().(*GeocodingItem)
		}
	}

	return y
}

func (x *RspGeocode) MaskInLog() any {
	if x == nil {
		return (*RspGeocode)(nil)
	}

	y := proto.Clone(x).(*RspGeocode)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspGeocode_Result)
		}
	}

	return y
}

func (x *RspGeocode) MaskInRpc() any {
	if x == nil {
		return (*RspGeocode)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspGeocode_Result)
		}
	}

	return y
}

func (x *RspGeocode) MaskInBff() any {
	if x == nil {
		return (*RspGeocode)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspGeocode_Result)
		}
	}

	return y
}

func (x *RspGeocode_Result) MaskInLog() any {
	if x == nil {
		return (*RspGeocode_Result)(nil)
	}

	y := proto.Clone(x).(*RspGeocode_Result)
	if v, ok := any(y.Address).(interface{ MaskInLog() any }); ok {
		y.Address = v.MaskInLog().(*StructuredFullAddress)
	}

	return y
}

func (x *RspGeocode_Result) MaskInRpc() any {
	if x == nil {
		return (*RspGeocode_Result)(nil)
	}

	y := x
	if v, ok := any(y.Address).(interface{ MaskInRpc() any }); ok {
		y.Address = v.MaskInRpc().(*StructuredFullAddress)
	}

	return y
}

func (x *RspGeocode_Result) MaskInBff() any {
	if x == nil {
		return (*RspGeocode_Result)(nil)
	}

	y := x
	if v, ok := any(y.Address).(interface{ MaskInBff() any }); ok {
		y.Address = v.MaskInBff().(*StructuredFullAddress)
	}

	return y
}

func (x *RspPreGeocode) MaskInLog() any {
	if x == nil {
		return (*RspPreGeocode)(nil)
	}

	y := proto.Clone(x).(*RspPreGeocode)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspPreGeocode_Result)
		}
	}

	return y
}

func (x *RspPreGeocode) MaskInRpc() any {
	if x == nil {
		return (*RspPreGeocode)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspPreGeocode_Result)
		}
	}

	return y
}

func (x *RspPreGeocode) MaskInBff() any {
	if x == nil {
		return (*RspPreGeocode)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspPreGeocode_Result)
		}
	}

	return y
}

func (x *RspPreGeocode_Result) MaskInLog() any {
	if x == nil {
		return (*RspPreGeocode_Result)(nil)
	}

	y := proto.Clone(x).(*RspPreGeocode_Result)
	switch v := y.Address.(type) {
	case *RspPreGeocode_Result_RawAddress:
		if vv, ok := any(v.RawAddress).(interface{ MaskInLog() any }); ok {
			v.RawAddress = vv.MaskInLog().(*RawAddress)
		}
	case *RspPreGeocode_Result_GeocodingItem:
		if vv, ok := any(v.GeocodingItem).(interface{ MaskInLog() any }); ok {
			v.GeocodingItem = vv.MaskInLog().(*GeocodingItem)
		}
	}

	return y
}

func (x *RspPreGeocode_Result) MaskInRpc() any {
	if x == nil {
		return (*RspPreGeocode_Result)(nil)
	}

	y := x
	switch v := y.Address.(type) {
	case *RspPreGeocode_Result_RawAddress:
		if vv, ok := any(v.RawAddress).(interface{ MaskInRpc() any }); ok {
			v.RawAddress = vv.MaskInRpc().(*RawAddress)
		}
	case *RspPreGeocode_Result_GeocodingItem:
		if vv, ok := any(v.GeocodingItem).(interface{ MaskInRpc() any }); ok {
			v.GeocodingItem = vv.MaskInRpc().(*GeocodingItem)
		}
	}

	return y
}

func (x *RspPreGeocode_Result) MaskInBff() any {
	if x == nil {
		return (*RspPreGeocode_Result)(nil)
	}

	y := x
	switch v := y.Address.(type) {
	case *RspPreGeocode_Result_RawAddress:
		if vv, ok := any(v.RawAddress).(interface{ MaskInBff() any }); ok {
			v.RawAddress = vv.MaskInBff().(*RawAddress)
		}
	case *RspPreGeocode_Result_GeocodingItem:
		if vv, ok := any(v.GeocodingItem).(interface{ MaskInBff() any }); ok {
			v.GeocodingItem = vv.MaskInBff().(*GeocodingItem)
		}
	}

	return y
}

func (x *ReqSyncAddress) MaskInLog() any {
	if x == nil {
		return (*ReqSyncAddress)(nil)
	}

	y := proto.Clone(x).(*ReqSyncAddress)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqSyncAddress_Item)
		}
	}

	return y
}

func (x *ReqSyncAddress) MaskInRpc() any {
	if x == nil {
		return (*ReqSyncAddress)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqSyncAddress_Item)
		}
	}

	return y
}

func (x *ReqSyncAddress) MaskInBff() any {
	if x == nil {
		return (*ReqSyncAddress)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqSyncAddress_Item)
		}
	}

	return y
}

func (x *ReqSyncAddress_Address) MaskInLog() any {
	if x == nil {
		return (*ReqSyncAddress_Address)(nil)
	}

	y := proto.Clone(x).(*ReqSyncAddress_Address)
	switch v := y.Address.(type) {
	case *ReqSyncAddress_Address_RawAddress:
		if vv, ok := any(v.RawAddress).(interface{ MaskInLog() any }); ok {
			v.RawAddress = vv.MaskInLog().(*RawAddress)
		}
	case *ReqSyncAddress_Address_StructuredAddress:
		if vv, ok := any(v.StructuredAddress).(interface{ MaskInLog() any }); ok {
			v.StructuredAddress = vv.MaskInLog().(*StructuredFullAddress)
		}
	}

	return y
}

func (x *ReqSyncAddress_Address) MaskInRpc() any {
	if x == nil {
		return (*ReqSyncAddress_Address)(nil)
	}

	y := x
	switch v := y.Address.(type) {
	case *ReqSyncAddress_Address_RawAddress:
		if vv, ok := any(v.RawAddress).(interface{ MaskInRpc() any }); ok {
			v.RawAddress = vv.MaskInRpc().(*RawAddress)
		}
	case *ReqSyncAddress_Address_StructuredAddress:
		if vv, ok := any(v.StructuredAddress).(interface{ MaskInRpc() any }); ok {
			v.StructuredAddress = vv.MaskInRpc().(*StructuredFullAddress)
		}
	}

	return y
}

func (x *ReqSyncAddress_Address) MaskInBff() any {
	if x == nil {
		return (*ReqSyncAddress_Address)(nil)
	}

	y := x
	switch v := y.Address.(type) {
	case *ReqSyncAddress_Address_RawAddress:
		if vv, ok := any(v.RawAddress).(interface{ MaskInBff() any }); ok {
			v.RawAddress = vv.MaskInBff().(*RawAddress)
		}
	case *ReqSyncAddress_Address_StructuredAddress:
		if vv, ok := any(v.StructuredAddress).(interface{ MaskInBff() any }); ok {
			v.StructuredAddress = vv.MaskInBff().(*StructuredFullAddress)
		}
	}

	return y
}

func (x *ReqSyncAddress_Item) MaskInLog() any {
	if x == nil {
		return (*ReqSyncAddress_Item)(nil)
	}

	y := proto.Clone(x).(*ReqSyncAddress_Item)
	if v, ok := any(y.DataItem).(interface{ MaskInLog() any }); ok {
		y.DataItem = v.MaskInLog().(*base.DataItem)
	}
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Addresses[k] = vv.MaskInLog().(*ReqSyncAddress_Address)
		}
	}

	return y
}

func (x *ReqSyncAddress_Item) MaskInRpc() any {
	if x == nil {
		return (*ReqSyncAddress_Item)(nil)
	}

	y := x
	if v, ok := any(y.DataItem).(interface{ MaskInRpc() any }); ok {
		y.DataItem = v.MaskInRpc().(*base.DataItem)
	}
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Addresses[k] = vv.MaskInRpc().(*ReqSyncAddress_Address)
		}
	}

	return y
}

func (x *ReqSyncAddress_Item) MaskInBff() any {
	if x == nil {
		return (*ReqSyncAddress_Item)(nil)
	}

	y := x
	if v, ok := any(y.DataItem).(interface{ MaskInBff() any }); ok {
		y.DataItem = v.MaskInBff().(*base.DataItem)
	}
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Addresses[k] = vv.MaskInBff().(*ReqSyncAddress_Address)
		}
	}

	return y
}

func (x *ReqDeleteAddress) MaskInLog() any {
	if x == nil {
		return (*ReqDeleteAddress)(nil)
	}

	y := proto.Clone(x).(*ReqDeleteAddress)
	for k, v := range y.DataItems {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.DataItems[k] = vv.MaskInLog().(*base.DataItem)
		}
	}

	return y
}

func (x *ReqDeleteAddress) MaskInRpc() any {
	if x == nil {
		return (*ReqDeleteAddress)(nil)
	}

	y := x
	for k, v := range y.DataItems {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.DataItems[k] = vv.MaskInRpc().(*base.DataItem)
		}
	}

	return y
}

func (x *ReqDeleteAddress) MaskInBff() any {
	if x == nil {
		return (*ReqDeleteAddress)(nil)
	}

	y := x
	for k, v := range y.DataItems {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.DataItems[k] = vv.MaskInBff().(*base.DataItem)
		}
	}

	return y
}

func (x *ReqReplaceAddress) MaskInLog() any {
	if x == nil {
		return (*ReqReplaceAddress)(nil)
	}

	y := proto.Clone(x).(*ReqReplaceAddress)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqReplaceAddress_Item)
		}
	}

	return y
}

func (x *ReqReplaceAddress) MaskInRpc() any {
	if x == nil {
		return (*ReqReplaceAddress)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqReplaceAddress_Item)
		}
	}

	return y
}

func (x *ReqReplaceAddress) MaskInBff() any {
	if x == nil {
		return (*ReqReplaceAddress)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqReplaceAddress_Item)
		}
	}

	return y
}

func (x *ReqReplaceAddress_Item) MaskInLog() any {
	if x == nil {
		return (*ReqReplaceAddress_Item)(nil)
	}

	y := proto.Clone(x).(*ReqReplaceAddress_Item)
	if v, ok := any(y.Source).(interface{ MaskInLog() any }); ok {
		y.Source = v.MaskInLog().(*base.DataItem)
	}
	if v, ok := any(y.Target).(interface{ MaskInLog() any }); ok {
		y.Target = v.MaskInLog().(*base.DataItem)
	}

	return y
}

func (x *ReqReplaceAddress_Item) MaskInRpc() any {
	if x == nil {
		return (*ReqReplaceAddress_Item)(nil)
	}

	y := x
	if v, ok := any(y.Source).(interface{ MaskInRpc() any }); ok {
		y.Source = v.MaskInRpc().(*base.DataItem)
	}
	if v, ok := any(y.Target).(interface{ MaskInRpc() any }); ok {
		y.Target = v.MaskInRpc().(*base.DataItem)
	}

	return y
}

func (x *ReqReplaceAddress_Item) MaskInBff() any {
	if x == nil {
		return (*ReqReplaceAddress_Item)(nil)
	}

	y := x
	if v, ok := any(y.Source).(interface{ MaskInBff() any }); ok {
		y.Source = v.MaskInBff().(*base.DataItem)
	}
	if v, ok := any(y.Target).(interface{ MaskInBff() any }); ok {
		y.Target = v.MaskInBff().(*base.DataItem)
	}

	return y
}

func (x *ReqPublishAddress) MaskInLog() any {
	if x == nil {
		return (*ReqPublishAddress)(nil)
	}

	y := proto.Clone(x).(*ReqPublishAddress)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqPublishAddress_Item)
		}
	}

	return y
}

func (x *ReqPublishAddress) MaskInRpc() any {
	if x == nil {
		return (*ReqPublishAddress)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqPublishAddress_Item)
		}
	}

	return y
}

func (x *ReqPublishAddress) MaskInBff() any {
	if x == nil {
		return (*ReqPublishAddress)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqPublishAddress_Item)
		}
	}

	return y
}

func (x *ReqPublishAddress_Item) MaskInLog() any {
	if x == nil {
		return (*ReqPublishAddress_Item)(nil)
	}

	y := proto.Clone(x).(*ReqPublishAddress_Item)
	if v, ok := any(y.DataItem).(interface{ MaskInLog() any }); ok {
		y.DataItem = v.MaskInLog().(*base.DataItem)
	}

	return y
}

func (x *ReqPublishAddress_Item) MaskInRpc() any {
	if x == nil {
		return (*ReqPublishAddress_Item)(nil)
	}

	y := x
	if v, ok := any(y.DataItem).(interface{ MaskInRpc() any }); ok {
		y.DataItem = v.MaskInRpc().(*base.DataItem)
	}

	return y
}

func (x *ReqPublishAddress_Item) MaskInBff() any {
	if x == nil {
		return (*ReqPublishAddress_Item)(nil)
	}

	y := x
	if v, ok := any(y.DataItem).(interface{ MaskInBff() any }); ok {
		y.DataItem = v.MaskInBff().(*base.DataItem)
	}

	return y
}

func (x *ReqGetAddressesByDataItem) MaskInLog() any {
	if x == nil {
		return (*ReqGetAddressesByDataItem)(nil)
	}

	y := proto.Clone(x).(*ReqGetAddressesByDataItem)
	for k, v := range y.DataItems {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.DataItems[k] = vv.MaskInLog().(*base.DataItem)
		}
	}

	return y
}

func (x *ReqGetAddressesByDataItem) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAddressesByDataItem)(nil)
	}

	y := x
	for k, v := range y.DataItems {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.DataItems[k] = vv.MaskInRpc().(*base.DataItem)
		}
	}

	return y
}

func (x *ReqGetAddressesByDataItem) MaskInBff() any {
	if x == nil {
		return (*ReqGetAddressesByDataItem)(nil)
	}

	y := x
	for k, v := range y.DataItems {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.DataItems[k] = vv.MaskInBff().(*base.DataItem)
		}
	}

	return y
}

func (x *RspGetAddressesByDataItem) MaskInLog() any {
	if x == nil {
		return (*RspGetAddressesByDataItem)(nil)
	}

	y := proto.Clone(x).(*RspGetAddressesByDataItem)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*RspGetAddressesByDataItem_Item)
		}
	}

	return y
}

func (x *RspGetAddressesByDataItem) MaskInRpc() any {
	if x == nil {
		return (*RspGetAddressesByDataItem)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*RspGetAddressesByDataItem_Item)
		}
	}

	return y
}

func (x *RspGetAddressesByDataItem) MaskInBff() any {
	if x == nil {
		return (*RspGetAddressesByDataItem)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*RspGetAddressesByDataItem_Item)
		}
	}

	return y
}

func (x *RspGetAddressesByDataItem_Item) MaskInLog() any {
	if x == nil {
		return (*RspGetAddressesByDataItem_Item)(nil)
	}

	y := proto.Clone(x).(*RspGetAddressesByDataItem_Item)
	if v, ok := any(y.DataItem).(interface{ MaskInLog() any }); ok {
		y.DataItem = v.MaskInLog().(*base.DataItem)
	}
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Addresses[k] = vv.MaskInLog().(*StructuredAddress)
		}
	}

	return y
}

func (x *RspGetAddressesByDataItem_Item) MaskInRpc() any {
	if x == nil {
		return (*RspGetAddressesByDataItem_Item)(nil)
	}

	y := x
	if v, ok := any(y.DataItem).(interface{ MaskInRpc() any }); ok {
		y.DataItem = v.MaskInRpc().(*base.DataItem)
	}
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Addresses[k] = vv.MaskInRpc().(*StructuredAddress)
		}
	}

	return y
}

func (x *RspGetAddressesByDataItem_Item) MaskInBff() any {
	if x == nil {
		return (*RspGetAddressesByDataItem_Item)(nil)
	}

	y := x
	if v, ok := any(y.DataItem).(interface{ MaskInBff() any }); ok {
		y.DataItem = v.MaskInBff().(*base.DataItem)
	}
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Addresses[k] = vv.MaskInBff().(*StructuredAddress)
		}
	}

	return y
}

func (x *ReqGetDataItemByAddresses) MaskInLog() any {
	if x == nil {
		return (*ReqGetDataItemByAddresses)(nil)
	}

	y := proto.Clone(x).(*ReqGetDataItemByAddresses)
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Addresses[k] = vv.MaskInLog().(*ReqGetDataItemByAddresses_Address)
		}
	}

	return y
}

func (x *ReqGetDataItemByAddresses) MaskInRpc() any {
	if x == nil {
		return (*ReqGetDataItemByAddresses)(nil)
	}

	y := x
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Addresses[k] = vv.MaskInRpc().(*ReqGetDataItemByAddresses_Address)
		}
	}

	return y
}

func (x *ReqGetDataItemByAddresses) MaskInBff() any {
	if x == nil {
		return (*ReqGetDataItemByAddresses)(nil)
	}

	y := x
	for k, v := range y.Addresses {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Addresses[k] = vv.MaskInBff().(*ReqGetDataItemByAddresses_Address)
		}
	}

	return y
}

func (x *ReqGetDataItemByAddresses_Address) MaskInLog() any {
	if x == nil {
		return (*ReqGetDataItemByAddresses_Address)(nil)
	}

	y := proto.Clone(x).(*ReqGetDataItemByAddresses_Address)
	if v, ok := any(y.RawAddress).(interface{ MaskInLog() any }); ok {
		y.RawAddress = v.MaskInLog().(*RawAddress)
	}

	return y
}

func (x *ReqGetDataItemByAddresses_Address) MaskInRpc() any {
	if x == nil {
		return (*ReqGetDataItemByAddresses_Address)(nil)
	}

	y := x
	if v, ok := any(y.RawAddress).(interface{ MaskInRpc() any }); ok {
		y.RawAddress = v.MaskInRpc().(*RawAddress)
	}

	return y
}

func (x *ReqGetDataItemByAddresses_Address) MaskInBff() any {
	if x == nil {
		return (*ReqGetDataItemByAddresses_Address)(nil)
	}

	y := x
	if v, ok := any(y.RawAddress).(interface{ MaskInBff() any }); ok {
		y.RawAddress = v.MaskInBff().(*RawAddress)
	}

	return y
}

func (x *ReqCreateMetric) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Metrics {
		if sanitizer, ok := any(x.Metrics[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqTranslate) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Translations {
		if sanitizer, ok := any(x.Translations[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspTranslate) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Translations {
		if sanitizer, ok := any(x.Translations[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqProxyTencentMap) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Request).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspProxyTencentMap) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Response).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqProxyGoogleMap) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Request).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspProxyGoogleMap) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Response).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetPlaceSelector) SanitizeXSS() {
	if x == nil {
		return
	}

	switch oneof := x.Condition.(type) {
	case *ReqGetPlaceSelector_ByDataType:
		if sanitizer, ok := any(oneof.ByDataType).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Condition = oneof
	case *ReqGetPlaceSelector_ByPlaceId:
		if sanitizer, ok := any(oneof.ByPlaceId).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Condition = oneof
	}
}

func (x *RspGetPlaceSelector) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Places {
		if sanitizer, ok := any(x.Places[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspSearchPlaces) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Places {
		if sanitizer, ok := any(x.Places[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGeocode) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.GeocodingItems {
		if sanitizer, ok := any(x.GeocodingItems[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGeocode) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGeocode_Result) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Address).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspPreGeocode) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspPreGeocode_Result) SanitizeXSS() {
	if x == nil {
		return
	}

	switch oneof := x.Address.(type) {
	case *RspPreGeocode_Result_RawAddress:
		if sanitizer, ok := any(oneof.RawAddress).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Address = oneof
	case *RspPreGeocode_Result_GeocodingItem:
		if sanitizer, ok := any(oneof.GeocodingItem).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Address = oneof
	}
}

func (x *ReqSyncAddress) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqSyncAddress_Address) SanitizeXSS() {
	if x == nil {
		return
	}

	switch oneof := x.Address.(type) {
	case *ReqSyncAddress_Address_RawAddress:
		if sanitizer, ok := any(oneof.RawAddress).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Address = oneof
	case *ReqSyncAddress_Address_StructuredAddress:
		if sanitizer, ok := any(oneof.StructuredAddress).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Address = oneof
	}
}

func (x *ReqSyncAddress_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.DataItem).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Addresses {
		if sanitizer, ok := any(x.Addresses[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDeleteAddress) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.DataItems {
		if sanitizer, ok := any(x.DataItems[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqReplaceAddress) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqReplaceAddress_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Source).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Target).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqPublishAddress) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqPublishAddress_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.DataItem).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetAddressesByDataItem) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.DataItems {
		if sanitizer, ok := any(x.DataItems[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAddressesByDataItem) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAddressesByDataItem_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.DataItem).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Addresses {
		if sanitizer, ok := any(x.Addresses[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetDataItemByAddresses) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Addresses {
		if sanitizer, ok := any(x.Addresses[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetDataItemByAddresses_Address) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.RawAddress).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
