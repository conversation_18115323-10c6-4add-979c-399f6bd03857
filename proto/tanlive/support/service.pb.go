// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/support/service.proto

package support

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqCreateMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metrics []*ReqCreateMetric_Metric `protobuf:"bytes,1,rep,name=metrics,proto3" json:"metrics,omitempty"`
	Type    string                    `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *ReqCreateMetric) Reset() {
	*x = ReqCreateMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateMetric) ProtoMessage() {}

func (x *ReqCreateMetric) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateMetric.ProtoReflect.Descriptor instead.
func (*ReqCreateMetric) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqCreateMetric) GetMetrics() []*ReqCreateMetric_Metric {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *ReqCreateMetric) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type ReqTranslate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 翻译列表
	Translations []*Translation `protobuf:"bytes,1,rep,name=translations,proto3" json:"translations,omitempty"`
}

func (x *ReqTranslate) Reset() {
	*x = ReqTranslate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqTranslate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqTranslate) ProtoMessage() {}

func (x *ReqTranslate) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqTranslate.ProtoReflect.Descriptor instead.
func (*ReqTranslate) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{1}
}

func (x *ReqTranslate) GetTranslations() []*Translation {
	if x != nil {
		return x.Translations
	}
	return nil
}

type RspTranslate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 翻译列表
	Translations []*Translation `protobuf:"bytes,1,rep,name=translations,proto3" json:"translations,omitempty"`
}

func (x *RspTranslate) Reset() {
	*x = RspTranslate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspTranslate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspTranslate) ProtoMessage() {}

func (x *RspTranslate) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspTranslate.ProtoReflect.Descriptor instead.
func (*RspTranslate) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{2}
}

func (x *RspTranslate) GetTranslations() []*Translation {
	if x != nil {
		return x.Translations
	}
	return nil
}

type ReqProxyTencentMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求
	Request *MapRequest `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
}

func (x *ReqProxyTencentMap) Reset() {
	*x = ReqProxyTencentMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqProxyTencentMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqProxyTencentMap) ProtoMessage() {}

func (x *ReqProxyTencentMap) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqProxyTencentMap.ProtoReflect.Descriptor instead.
func (*ReqProxyTencentMap) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{3}
}

func (x *ReqProxyTencentMap) GetRequest() *MapRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

type RspProxyTencentMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应
	Response *MapResponse `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *RspProxyTencentMap) Reset() {
	*x = RspProxyTencentMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspProxyTencentMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspProxyTencentMap) ProtoMessage() {}

func (x *RspProxyTencentMap) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspProxyTencentMap.ProtoReflect.Descriptor instead.
func (*RspProxyTencentMap) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{4}
}

func (x *RspProxyTencentMap) GetResponse() *MapResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

type ReqProxyGoogleMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求
	Request *MapRequest `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
	// 客户端
	Client GoogleMapClient `protobuf:"varint,2,opt,name=client,proto3,enum=tanlive.support.GoogleMapClient" json:"client,omitempty"`
}

func (x *ReqProxyGoogleMap) Reset() {
	*x = ReqProxyGoogleMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqProxyGoogleMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqProxyGoogleMap) ProtoMessage() {}

func (x *ReqProxyGoogleMap) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqProxyGoogleMap.ProtoReflect.Descriptor instead.
func (*ReqProxyGoogleMap) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{5}
}

func (x *ReqProxyGoogleMap) GetRequest() *MapRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *ReqProxyGoogleMap) GetClient() GoogleMapClient {
	if x != nil {
		return x.Client
	}
	return GoogleMapClient_GOOGLE_MAP_CLIENT_UNSPECIFIED
}

type RspProxyGoogleMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 响应
	Response *MapResponse `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *RspProxyGoogleMap) Reset() {
	*x = RspProxyGoogleMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspProxyGoogleMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspProxyGoogleMap) ProtoMessage() {}

func (x *RspProxyGoogleMap) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspProxyGoogleMap.ProtoReflect.Descriptor instead.
func (*RspProxyGoogleMap) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{6}
}

func (x *RspProxyGoogleMap) GetResponse() *MapResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

type ReqGetPlaceSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 查询条件
	//
	// Types that are assignable to Condition:
	//
	//	*ReqGetPlaceSelector_ByDataType
	//	*ReqGetPlaceSelector_ByPlaceId
	Condition isReqGetPlaceSelector_Condition `protobuf_oneof:"condition"`
	// 指定语言
	Lang string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqGetPlaceSelector) Reset() {
	*x = ReqGetPlaceSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetPlaceSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetPlaceSelector) ProtoMessage() {}

func (x *ReqGetPlaceSelector) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetPlaceSelector.ProtoReflect.Descriptor instead.
func (*ReqGetPlaceSelector) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{7}
}

func (m *ReqGetPlaceSelector) GetCondition() isReqGetPlaceSelector_Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (x *ReqGetPlaceSelector) GetByDataType() *GetPlaceCondByDataType {
	if x, ok := x.GetCondition().(*ReqGetPlaceSelector_ByDataType); ok {
		return x.ByDataType
	}
	return nil
}

func (x *ReqGetPlaceSelector) GetByPlaceId() *GetPlaceCondByPlaceId {
	if x, ok := x.GetCondition().(*ReqGetPlaceSelector_ByPlaceId); ok {
		return x.ByPlaceId
	}
	return nil
}

func (x *ReqGetPlaceSelector) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type isReqGetPlaceSelector_Condition interface {
	isReqGetPlaceSelector_Condition()
}

type ReqGetPlaceSelector_ByDataType struct {
	// 通过数据类型查询
	ByDataType *GetPlaceCondByDataType `protobuf:"bytes,1,opt,name=by_data_type,json=byDataType,proto3,oneof"`
}

type ReqGetPlaceSelector_ByPlaceId struct {
	// 通过地点ID查询
	ByPlaceId *GetPlaceCondByPlaceId `protobuf:"bytes,2,opt,name=by_place_id,json=byPlaceId,proto3,oneof"`
}

func (*ReqGetPlaceSelector_ByDataType) isReqGetPlaceSelector_Condition() {}

func (*ReqGetPlaceSelector_ByPlaceId) isReqGetPlaceSelector_Condition() {}

type RspGetPlaceSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地点列表
	Places []*Place `protobuf:"bytes,1,rep,name=places,proto3" json:"places,omitempty"`
}

func (x *RspGetPlaceSelector) Reset() {
	*x = RspGetPlaceSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetPlaceSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetPlaceSelector) ProtoMessage() {}

func (x *RspGetPlaceSelector) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetPlaceSelector.ProtoReflect.Descriptor instead.
func (*RspGetPlaceSelector) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{8}
}

func (x *RspGetPlaceSelector) GetPlaces() []*Place {
	if x != nil {
		return x.Places
	}
	return nil
}

type ReqSearchPlaces struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 按名称模糊搜索
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 指定语言
	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
	// 数据类型
	DataType base.DataType `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 数据字段
	// 团队：location、service_region 资源：location、target_regions
	DataField string `protobuf:"bytes,4,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
}

func (x *ReqSearchPlaces) Reset() {
	*x = ReqSearchPlaces{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchPlaces) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchPlaces) ProtoMessage() {}

func (x *ReqSearchPlaces) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchPlaces.ProtoReflect.Descriptor instead.
func (*ReqSearchPlaces) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{9}
}

func (x *ReqSearchPlaces) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReqSearchPlaces) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *ReqSearchPlaces) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqSearchPlaces) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

type RspSearchPlaces struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地点列表
	Places []*PlaceWithParents `protobuf:"bytes,1,rep,name=places,proto3" json:"places,omitempty"`
}

func (x *RspSearchPlaces) Reset() {
	*x = RspSearchPlaces{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSearchPlaces) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSearchPlaces) ProtoMessage() {}

func (x *RspSearchPlaces) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSearchPlaces.ProtoReflect.Descriptor instead.
func (*RspSearchPlaces) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{10}
}

func (x *RspSearchPlaces) GetPlaces() []*PlaceWithParents {
	if x != nil {
		return x.Places
	}
	return nil
}

type ReqGeocode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地址解析项
	GeocodingItems []*GeocodingItem `protobuf:"bytes,1,rep,name=geocoding_items,json=geocodingItems,proto3" json:"geocoding_items,omitempty"`
}

func (x *ReqGeocode) Reset() {
	*x = ReqGeocode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGeocode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGeocode) ProtoMessage() {}

func (x *ReqGeocode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGeocode.ProtoReflect.Descriptor instead.
func (*ReqGeocode) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{11}
}

func (x *ReqGeocode) GetGeocodingItems() []*GeocodingItem {
	if x != nil {
		return x.GeocodingItems
	}
	return nil
}

type RspGeocode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果
	Results []*RspGeocode_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *RspGeocode) Reset() {
	*x = RspGeocode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGeocode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGeocode) ProtoMessage() {}

func (x *RspGeocode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGeocode.ProtoReflect.Descriptor instead.
func (*RspGeocode) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{12}
}

func (x *RspGeocode) GetResults() []*RspGeocode_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type RspPreGeocode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 结果
	Results []*RspPreGeocode_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *RspPreGeocode) Reset() {
	*x = RspPreGeocode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspPreGeocode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspPreGeocode) ProtoMessage() {}

func (x *RspPreGeocode) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspPreGeocode.ProtoReflect.Descriptor instead.
func (*RspPreGeocode) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{13}
}

func (x *RspPreGeocode) GetResults() []*RspPreGeocode_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type ReqSyncAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地址列表
	Items []*ReqSyncAddress_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ReqSyncAddress) Reset() {
	*x = ReqSyncAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSyncAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSyncAddress) ProtoMessage() {}

func (x *ReqSyncAddress) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSyncAddress.ProtoReflect.Descriptor instead.
func (*ReqSyncAddress) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{14}
}

func (x *ReqSyncAddress) GetItems() []*ReqSyncAddress_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type ReqDeleteAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据列表
	DataItems []*base.DataItem `protobuf:"bytes,1,rep,name=data_items,json=dataItems,proto3" json:"data_items,omitempty"`
}

func (x *ReqDeleteAddress) Reset() {
	*x = ReqDeleteAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteAddress) ProtoMessage() {}

func (x *ReqDeleteAddress) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteAddress.ProtoReflect.Descriptor instead.
func (*ReqDeleteAddress) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{15}
}

func (x *ReqDeleteAddress) GetDataItems() []*base.DataItem {
	if x != nil {
		return x.DataItems
	}
	return nil
}

type ReqReplaceAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ReqReplaceAddress_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ReqReplaceAddress) Reset() {
	*x = ReqReplaceAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqReplaceAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqReplaceAddress) ProtoMessage() {}

func (x *ReqReplaceAddress) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqReplaceAddress.ProtoReflect.Descriptor instead.
func (*ReqReplaceAddress) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{16}
}

func (x *ReqReplaceAddress) GetItems() []*ReqReplaceAddress_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type ReqPublishAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ReqPublishAddress_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ReqPublishAddress) Reset() {
	*x = ReqPublishAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqPublishAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqPublishAddress) ProtoMessage() {}

func (x *ReqPublishAddress) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqPublishAddress.ProtoReflect.Descriptor instead.
func (*ReqPublishAddress) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{17}
}

func (x *ReqPublishAddress) GetItems() []*ReqPublishAddress_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type ReqGetAddressesByDataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据项列表
	DataItems []*base.DataItem `protobuf:"bytes,1,rep,name=data_items,json=dataItems,proto3" json:"data_items,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqGetAddressesByDataItem) Reset() {
	*x = ReqGetAddressesByDataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetAddressesByDataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetAddressesByDataItem) ProtoMessage() {}

func (x *ReqGetAddressesByDataItem) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetAddressesByDataItem.ProtoReflect.Descriptor instead.
func (*ReqGetAddressesByDataItem) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{18}
}

func (x *ReqGetAddressesByDataItem) GetDataItems() []*base.DataItem {
	if x != nil {
		return x.DataItems
	}
	return nil
}

func (x *ReqGetAddressesByDataItem) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type RspGetAddressesByDataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*RspGetAddressesByDataItem_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *RspGetAddressesByDataItem) Reset() {
	*x = RspGetAddressesByDataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetAddressesByDataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetAddressesByDataItem) ProtoMessage() {}

func (x *RspGetAddressesByDataItem) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetAddressesByDataItem.ProtoReflect.Descriptor instead.
func (*RspGetAddressesByDataItem) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{19}
}

func (x *RspGetAddressesByDataItem) GetItems() []*RspGetAddressesByDataItem_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type ReqGetDataItemByAddresses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地址详情
	Addresses []*ReqGetDataItemByAddresses_Address `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 数据类型
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 数据字段
	DataField string `protobuf:"bytes,3,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
}

func (x *ReqGetDataItemByAddresses) Reset() {
	*x = ReqGetDataItemByAddresses{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetDataItemByAddresses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetDataItemByAddresses) ProtoMessage() {}

func (x *ReqGetDataItemByAddresses) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetDataItemByAddresses.ProtoReflect.Descriptor instead.
func (*ReqGetDataItemByAddresses) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{20}
}

func (x *ReqGetDataItemByAddresses) GetAddresses() []*ReqGetDataItemByAddresses_Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *ReqGetDataItemByAddresses) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqGetDataItemByAddresses) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

type RspGetDataItemByAddresses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据项列表
	DataIds []uint64 `protobuf:"varint,1,rep,packed,name=data_ids,json=dataIds,proto3" json:"data_ids,omitempty"`
}

func (x *RspGetDataItemByAddresses) Reset() {
	*x = RspGetDataItemByAddresses{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetDataItemByAddresses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetDataItemByAddresses) ProtoMessage() {}

func (x *RspGetDataItemByAddresses) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetDataItemByAddresses.ProtoReflect.Descriptor instead.
func (*RspGetDataItemByAddresses) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{21}
}

func (x *RspGetDataItemByAddresses) GetDataIds() []uint64 {
	if x != nil {
		return x.DataIds
	}
	return nil
}

type ReqCreateMetric_Metric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key             string  `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	ValueInt        int64   `protobuf:"varint,2,opt,name=value_int,json=valueInt,proto3" json:"value_int,omitempty"`
	ValueFloat      float32 `protobuf:"fixed32,3,opt,name=value_float,json=valueFloat,proto3" json:"value_float,omitempty"`
	ValueString     string  `protobuf:"bytes,4,opt,name=value_string,json=valueString,proto3" json:"value_string,omitempty"`
	ValueLongString string  `protobuf:"bytes,5,opt,name=value_long_string,json=valueLongString,proto3" json:"value_long_string,omitempty"`
}

func (x *ReqCreateMetric_Metric) Reset() {
	*x = ReqCreateMetric_Metric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateMetric_Metric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateMetric_Metric) ProtoMessage() {}

func (x *ReqCreateMetric_Metric) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateMetric_Metric.ProtoReflect.Descriptor instead.
func (*ReqCreateMetric_Metric) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ReqCreateMetric_Metric) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ReqCreateMetric_Metric) GetValueInt() int64 {
	if x != nil {
		return x.ValueInt
	}
	return 0
}

func (x *ReqCreateMetric_Metric) GetValueFloat() float32 {
	if x != nil {
		return x.ValueFloat
	}
	return 0
}

func (x *ReqCreateMetric_Metric) GetValueString() string {
	if x != nil {
		return x.ValueString
	}
	return ""
}

func (x *ReqCreateMetric_Metric) GetValueLongString() string {
	if x != nil {
		return x.ValueLongString
	}
	return ""
}

type RspGeocode_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 错误码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 地址详情
	Address *StructuredFullAddress `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *RspGeocode_Result) Reset() {
	*x = RspGeocode_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGeocode_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGeocode_Result) ProtoMessage() {}

func (x *RspGeocode_Result) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGeocode_Result.ProtoReflect.Descriptor instead.
func (*RspGeocode_Result) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{12, 0}
}

func (x *RspGeocode_Result) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RspGeocode_Result) GetAddress() *StructuredFullAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

type RspPreGeocode_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Address:
	//
	//	*RspPreGeocode_Result_RawAddress
	//	*RspPreGeocode_Result_GeocodingItem
	Address isRspPreGeocode_Result_Address `protobuf_oneof:"address"`
}

func (x *RspPreGeocode_Result) Reset() {
	*x = RspPreGeocode_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspPreGeocode_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspPreGeocode_Result) ProtoMessage() {}

func (x *RspPreGeocode_Result) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspPreGeocode_Result.ProtoReflect.Descriptor instead.
func (*RspPreGeocode_Result) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{13, 0}
}

func (m *RspPreGeocode_Result) GetAddress() isRspPreGeocode_Result_Address {
	if m != nil {
		return m.Address
	}
	return nil
}

func (x *RspPreGeocode_Result) GetRawAddress() *RawAddress {
	if x, ok := x.GetAddress().(*RspPreGeocode_Result_RawAddress); ok {
		return x.RawAddress
	}
	return nil
}

func (x *RspPreGeocode_Result) GetGeocodingItem() *GeocodingItem {
	if x, ok := x.GetAddress().(*RspPreGeocode_Result_GeocodingItem); ok {
		return x.GeocodingItem
	}
	return nil
}

type isRspPreGeocode_Result_Address interface {
	isRspPreGeocode_Result_Address()
}

type RspPreGeocode_Result_RawAddress struct {
	// 已解析的原始地址
	RawAddress *RawAddress `protobuf:"bytes,1,opt,name=raw_address,json=rawAddress,proto3,oneof"`
}

type RspPreGeocode_Result_GeocodingItem struct {
	// 未解析的项
	GeocodingItem *GeocodingItem `protobuf:"bytes,2,opt,name=geocoding_item,json=geocodingItem,proto3,oneof"`
}

func (*RspPreGeocode_Result_RawAddress) isRspPreGeocode_Result_Address() {}

func (*RspPreGeocode_Result_GeocodingItem) isRspPreGeocode_Result_Address() {}

type ReqSyncAddress_Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Address:
	//
	//	*ReqSyncAddress_Address_RawAddress
	//	*ReqSyncAddress_Address_StructuredAddress
	Address isReqSyncAddress_Address_Address `protobuf_oneof:"address"`
}

func (x *ReqSyncAddress_Address) Reset() {
	*x = ReqSyncAddress_Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSyncAddress_Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSyncAddress_Address) ProtoMessage() {}

func (x *ReqSyncAddress_Address) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSyncAddress_Address.ProtoReflect.Descriptor instead.
func (*ReqSyncAddress_Address) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{14, 0}
}

func (m *ReqSyncAddress_Address) GetAddress() isReqSyncAddress_Address_Address {
	if m != nil {
		return m.Address
	}
	return nil
}

func (x *ReqSyncAddress_Address) GetRawAddress() *RawAddress {
	if x, ok := x.GetAddress().(*ReqSyncAddress_Address_RawAddress); ok {
		return x.RawAddress
	}
	return nil
}

func (x *ReqSyncAddress_Address) GetStructuredAddress() *StructuredFullAddress {
	if x, ok := x.GetAddress().(*ReqSyncAddress_Address_StructuredAddress); ok {
		return x.StructuredAddress
	}
	return nil
}

type isReqSyncAddress_Address_Address interface {
	isReqSyncAddress_Address_Address()
}

type ReqSyncAddress_Address_RawAddress struct {
	// 原始地址
	RawAddress *RawAddress `protobuf:"bytes,1,opt,name=raw_address,json=rawAddress,proto3,oneof"`
}

type ReqSyncAddress_Address_StructuredAddress struct {
	// 结构化地址
	StructuredAddress *StructuredFullAddress `protobuf:"bytes,2,opt,name=structured_address,json=structuredAddress,proto3,oneof"`
}

func (*ReqSyncAddress_Address_RawAddress) isReqSyncAddress_Address_Address() {}

func (*ReqSyncAddress_Address_StructuredAddress) isReqSyncAddress_Address_Address() {}

type ReqSyncAddress_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据项
	DataItem *base.DataItem `protobuf:"bytes,1,opt,name=data_item,json=dataItem,proto3" json:"data_item,omitempty"`
	// 地址详情
	Addresses []*ReqSyncAddress_Address `protobuf:"bytes,2,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 是否已发布
	IsPublished bool `protobuf:"varint,3,opt,name=is_published,json=isPublished,proto3" json:"is_published,omitempty"`
}

func (x *ReqSyncAddress_Item) Reset() {
	*x = ReqSyncAddress_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSyncAddress_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSyncAddress_Item) ProtoMessage() {}

func (x *ReqSyncAddress_Item) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSyncAddress_Item.ProtoReflect.Descriptor instead.
func (*ReqSyncAddress_Item) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{14, 1}
}

func (x *ReqSyncAddress_Item) GetDataItem() *base.DataItem {
	if x != nil {
		return x.DataItem
	}
	return nil
}

func (x *ReqSyncAddress_Item) GetAddresses() []*ReqSyncAddress_Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *ReqSyncAddress_Item) GetIsPublished() bool {
	if x != nil {
		return x.IsPublished
	}
	return false
}

type ReqReplaceAddress_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 源数据
	Source *base.DataItem `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	// 目标数据
	Target *base.DataItem `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	// 是否已发布
	IsPublished bool `protobuf:"varint,3,opt,name=is_published,json=isPublished,proto3" json:"is_published,omitempty"`
}

func (x *ReqReplaceAddress_Item) Reset() {
	*x = ReqReplaceAddress_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqReplaceAddress_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqReplaceAddress_Item) ProtoMessage() {}

func (x *ReqReplaceAddress_Item) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqReplaceAddress_Item.ProtoReflect.Descriptor instead.
func (*ReqReplaceAddress_Item) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ReqReplaceAddress_Item) GetSource() *base.DataItem {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *ReqReplaceAddress_Item) GetTarget() *base.DataItem {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *ReqReplaceAddress_Item) GetIsPublished() bool {
	if x != nil {
		return x.IsPublished
	}
	return false
}

type ReqPublishAddress_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据想
	DataItem *base.DataItem `protobuf:"bytes,1,opt,name=data_item,json=dataItem,proto3" json:"data_item,omitempty"`
	// 是否已发布
	IsPublished bool `protobuf:"varint,2,opt,name=is_published,json=isPublished,proto3" json:"is_published,omitempty"`
}

func (x *ReqPublishAddress_Item) Reset() {
	*x = ReqPublishAddress_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqPublishAddress_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqPublishAddress_Item) ProtoMessage() {}

func (x *ReqPublishAddress_Item) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqPublishAddress_Item.ProtoReflect.Descriptor instead.
func (*ReqPublishAddress_Item) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{17, 0}
}

func (x *ReqPublishAddress_Item) GetDataItem() *base.DataItem {
	if x != nil {
		return x.DataItem
	}
	return nil
}

func (x *ReqPublishAddress_Item) GetIsPublished() bool {
	if x != nil {
		return x.IsPublished
	}
	return false
}

type RspGetAddressesByDataItem_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据项
	DataItem *base.DataItem `protobuf:"bytes,1,opt,name=data_item,json=dataItem,proto3" json:"data_item,omitempty"`
	// 地址列表
	Addresses []*StructuredAddress `protobuf:"bytes,2,rep,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *RspGetAddressesByDataItem_Item) Reset() {
	*x = RspGetAddressesByDataItem_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetAddressesByDataItem_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetAddressesByDataItem_Item) ProtoMessage() {}

func (x *RspGetAddressesByDataItem_Item) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetAddressesByDataItem_Item.ProtoReflect.Descriptor instead.
func (*RspGetAddressesByDataItem_Item) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{19, 0}
}

func (x *RspGetAddressesByDataItem_Item) GetDataItem() *base.DataItem {
	if x != nil {
		return x.DataItem
	}
	return nil
}

func (x *RspGetAddressesByDataItem_Item) GetAddresses() []*StructuredAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ReqGetDataItemByAddresses_Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 原始地址
	RawAddress *RawAddress `protobuf:"bytes,1,opt,name=raw_address,json=rawAddress,proto3" json:"raw_address,omitempty"`
	// 数据字段
	DataField string `protobuf:"bytes,3,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
}

func (x *ReqGetDataItemByAddresses_Address) Reset() {
	*x = ReqGetDataItemByAddresses_Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_support_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetDataItemByAddresses_Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetDataItemByAddresses_Address) ProtoMessage() {}

func (x *ReqGetDataItemByAddresses_Address) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_support_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetDataItemByAddresses_Address.ProtoReflect.Descriptor instead.
func (*ReqGetDataItemByAddresses_Address) Descriptor() ([]byte, []int) {
	return file_tanlive_support_service_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ReqGetDataItemByAddresses_Address) GetRawAddress() *RawAddress {
	if x != nil {
		return x.RawAddress
	}
	return nil
}

func (x *ReqGetDataItemByAddresses_Address) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

var File_tanlive_support_service_proto protoreflect.FileDescriptor

var file_tanlive_support_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x02, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x41, 0x0a, 0x07, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65,
	0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x1a, 0xa7, 0x01, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1b,
	0x0a, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x2a, 0x0a, 0x11, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x4c, 0x6f, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x50, 0x0a, 0x0c, 0x52,
	0x65, 0x71, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x50, 0x0a,
	0x0c, 0x52, 0x73, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a,
	0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x59, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x54, 0x65, 0x6e, 0x63, 0x65,
	0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x43, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x4e, 0x0a, 0x12, 0x52, 0x73,
	0x70, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70,
	0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x11, 0x52,
	0x65, 0x71, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d, 0x61, 0x70,
	0x12, 0x43, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x0c,
	0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d, 0x61,
	0x70, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x22, 0x4d, 0x0a,
	0x11, 0x52, 0x73, 0x70, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d,
	0x61, 0x70, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf7, 0x01, 0x0a,
	0x13, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x59, 0x0a, 0x0c, 0x62, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x79, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x56, 0x0a, 0x0b, 0x62, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x64, 0x42, 0x79, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x00, 0x52, 0x09, 0x62, 0x79,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x42, 0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x45, 0x0a, 0x13, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2e, 0x0a,
	0x06, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x22, 0xc5, 0x01,
	0x0a, 0x0f, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x63, 0x65,
	0x73, 0x12, 0x20, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x41, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08,
	0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x22, 0x4c, 0x0a, 0x0f, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65,
	0x57, 0x69, 0x74, 0x68, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x06, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x73, 0x22, 0x63, 0x0a, 0x0a, 0x52, 0x65, 0x71, 0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x55, 0x0a, 0x0f, 0x67, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x47, 0x65, 0x6f,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0e, 0x67, 0x65, 0x6f, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xaa, 0x01, 0x0a, 0x0a, 0x52, 0x73, 0x70,
	0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x3c, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65,
	0x6f, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x5e, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x64, 0x46, 0x75, 0x6c, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xef, 0x01, 0x0a, 0x0d, 0x52, 0x73, 0x70, 0x50, 0x72, 0x65,
	0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x50, 0x72,
	0x65, 0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x9c, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x0b, 0x72, 0x61, 0x77, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x61, 0x77, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x61, 0x77, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x47, 0x0a, 0x0e, 0x67, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x47, 0x65,
	0x6f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x0d, 0x67,
	0x65, 0x6f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x09, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xce, 0x03, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x53,
	0x79, 0x6e, 0x63, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x48, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x53,
	0x79, 0x6e, 0x63, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42,
	0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x1a, 0xad, 0x01, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x3e, 0x0a, 0x0b, 0x72, 0x61, 0x77, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x61, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x61, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x57, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x46, 0x75, 0x6c, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x48, 0x00, 0x52, 0x11, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x1a, 0xc1, 0x01, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x41, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x53, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22, 0x49, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x35, 0x0a, 0x0a,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x22, 0x88, 0x02, 0x0a, 0x11, 0x52, 0x65, 0x71, 0x52, 0x65, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4b, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x52, 0x65,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x49, 0x74, 0x65,
	0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0xa5, 0x01, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x3c, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3c, 0x0a,
	0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22, 0xce,
	0x01, 0x0a, 0x11, 0x52, 0x65, 0x71, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x4b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0c, 0x82, 0x88,
	0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x1a, 0x6c, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x41, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x49, 0x74, 0x65, 0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22,
	0x74, 0x0a, 0x19, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x43, 0x0a, 0x0a,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xe1, 0x01, 0x0a, 0x19, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x45, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x7d, 0x0a, 0x04, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x40, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x09,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0xa9, 0x02, 0x0a, 0x19, 0x52, 0x65,
	0x71, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x79, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71,
	0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x79, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x09,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x1a, 0x66, 0x0a,
	0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x72, 0x61, 0x77, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x52, 0x61, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x72, 0x61, 0x77, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x22, 0x36, 0x0a, 0x19, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x32, 0xb4, 0x09,
	0x0a, 0x0e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x49, 0x0a, 0x09, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x52, 0x65, 0x71, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x1a, 0x1d, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52,
	0x73, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x5b, 0x0a, 0x0f, 0x50,
	0x72, 0x6f, 0x78, 0x79, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x23,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x52, 0x65, 0x71, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74,
	0x4d, 0x61, 0x70, 0x1a, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x54, 0x65,
	0x6e, 0x63, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x58, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71,
	0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x22,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x52, 0x73, 0x70, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d,
	0x61, 0x70, 0x12, 0x5e, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x24, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52,
	0x73, 0x70, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x52, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x73, 0x12, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x73, 0x1a, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x07, 0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x1a, 0x1b,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x50,
	0x72, 0x65, 0x47, 0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x1a, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x73, 0x70, 0x50, 0x72, 0x65, 0x47,
	0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x0b, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x79, 0x6e, 0x63, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4a,
	0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x0e, 0x52, 0x65,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x22, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52,
	0x65, 0x71, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x0e, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x70, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x1a, 0x2a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52,
	0x73, 0x70, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x42, 0x79,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x70, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x12, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x74, 0x65, 0x6d, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x1a, 0x2a,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42,
	0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0c, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x20, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x71,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x42, 0x41, 0x5a, 0x3f, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76,
	0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_support_service_proto_rawDescOnce sync.Once
	file_tanlive_support_service_proto_rawDescData = file_tanlive_support_service_proto_rawDesc
)

func file_tanlive_support_service_proto_rawDescGZIP() []byte {
	file_tanlive_support_service_proto_rawDescOnce.Do(func() {
		file_tanlive_support_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_support_service_proto_rawDescData)
	})
	return file_tanlive_support_service_proto_rawDescData
}

var file_tanlive_support_service_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_tanlive_support_service_proto_goTypes = []interface{}{
	(*ReqCreateMetric)(nil),                   // 0: tanlive.support.ReqCreateMetric
	(*ReqTranslate)(nil),                      // 1: tanlive.support.ReqTranslate
	(*RspTranslate)(nil),                      // 2: tanlive.support.RspTranslate
	(*ReqProxyTencentMap)(nil),                // 3: tanlive.support.ReqProxyTencentMap
	(*RspProxyTencentMap)(nil),                // 4: tanlive.support.RspProxyTencentMap
	(*ReqProxyGoogleMap)(nil),                 // 5: tanlive.support.ReqProxyGoogleMap
	(*RspProxyGoogleMap)(nil),                 // 6: tanlive.support.RspProxyGoogleMap
	(*ReqGetPlaceSelector)(nil),               // 7: tanlive.support.ReqGetPlaceSelector
	(*RspGetPlaceSelector)(nil),               // 8: tanlive.support.RspGetPlaceSelector
	(*ReqSearchPlaces)(nil),                   // 9: tanlive.support.ReqSearchPlaces
	(*RspSearchPlaces)(nil),                   // 10: tanlive.support.RspSearchPlaces
	(*ReqGeocode)(nil),                        // 11: tanlive.support.ReqGeocode
	(*RspGeocode)(nil),                        // 12: tanlive.support.RspGeocode
	(*RspPreGeocode)(nil),                     // 13: tanlive.support.RspPreGeocode
	(*ReqSyncAddress)(nil),                    // 14: tanlive.support.ReqSyncAddress
	(*ReqDeleteAddress)(nil),                  // 15: tanlive.support.ReqDeleteAddress
	(*ReqReplaceAddress)(nil),                 // 16: tanlive.support.ReqReplaceAddress
	(*ReqPublishAddress)(nil),                 // 17: tanlive.support.ReqPublishAddress
	(*ReqGetAddressesByDataItem)(nil),         // 18: tanlive.support.ReqGetAddressesByDataItem
	(*RspGetAddressesByDataItem)(nil),         // 19: tanlive.support.RspGetAddressesByDataItem
	(*ReqGetDataItemByAddresses)(nil),         // 20: tanlive.support.ReqGetDataItemByAddresses
	(*RspGetDataItemByAddresses)(nil),         // 21: tanlive.support.RspGetDataItemByAddresses
	(*ReqCreateMetric_Metric)(nil),            // 22: tanlive.support.ReqCreateMetric.Metric
	(*RspGeocode_Result)(nil),                 // 23: tanlive.support.RspGeocode.Result
	(*RspPreGeocode_Result)(nil),              // 24: tanlive.support.RspPreGeocode.Result
	(*ReqSyncAddress_Address)(nil),            // 25: tanlive.support.ReqSyncAddress.Address
	(*ReqSyncAddress_Item)(nil),               // 26: tanlive.support.ReqSyncAddress.Item
	(*ReqReplaceAddress_Item)(nil),            // 27: tanlive.support.ReqReplaceAddress.Item
	(*ReqPublishAddress_Item)(nil),            // 28: tanlive.support.ReqPublishAddress.Item
	(*RspGetAddressesByDataItem_Item)(nil),    // 29: tanlive.support.RspGetAddressesByDataItem.Item
	(*ReqGetDataItemByAddresses_Address)(nil), // 30: tanlive.support.ReqGetDataItemByAddresses.Address
	(*Translation)(nil),                       // 31: tanlive.support.Translation
	(*MapRequest)(nil),                        // 32: tanlive.support.MapRequest
	(*MapResponse)(nil),                       // 33: tanlive.support.MapResponse
	(GoogleMapClient)(0),                      // 34: tanlive.support.GoogleMapClient
	(*GetPlaceCondByDataType)(nil),            // 35: tanlive.support.GetPlaceCondByDataType
	(*GetPlaceCondByPlaceId)(nil),             // 36: tanlive.support.GetPlaceCondByPlaceId
	(*Place)(nil),                             // 37: tanlive.support.Place
	(base.DataType)(0),                        // 38: tanlive.base.DataType
	(*PlaceWithParents)(nil),                  // 39: tanlive.support.PlaceWithParents
	(*GeocodingItem)(nil),                     // 40: tanlive.support.GeocodingItem
	(*base.DataItem)(nil),                     // 41: tanlive.base.DataItem
	(*StructuredFullAddress)(nil),             // 42: tanlive.support.StructuredFullAddress
	(*RawAddress)(nil),                        // 43: tanlive.support.RawAddress
	(*StructuredAddress)(nil),                 // 44: tanlive.support.StructuredAddress
	(*emptypb.Empty)(nil),                     // 45: google.protobuf.Empty
}
var file_tanlive_support_service_proto_depIdxs = []int32{
	22, // 0: tanlive.support.ReqCreateMetric.metrics:type_name -> tanlive.support.ReqCreateMetric.Metric
	31, // 1: tanlive.support.ReqTranslate.translations:type_name -> tanlive.support.Translation
	31, // 2: tanlive.support.RspTranslate.translations:type_name -> tanlive.support.Translation
	32, // 3: tanlive.support.ReqProxyTencentMap.request:type_name -> tanlive.support.MapRequest
	33, // 4: tanlive.support.RspProxyTencentMap.response:type_name -> tanlive.support.MapResponse
	32, // 5: tanlive.support.ReqProxyGoogleMap.request:type_name -> tanlive.support.MapRequest
	34, // 6: tanlive.support.ReqProxyGoogleMap.client:type_name -> tanlive.support.GoogleMapClient
	33, // 7: tanlive.support.RspProxyGoogleMap.response:type_name -> tanlive.support.MapResponse
	35, // 8: tanlive.support.ReqGetPlaceSelector.by_data_type:type_name -> tanlive.support.GetPlaceCondByDataType
	36, // 9: tanlive.support.ReqGetPlaceSelector.by_place_id:type_name -> tanlive.support.GetPlaceCondByPlaceId
	37, // 10: tanlive.support.RspGetPlaceSelector.places:type_name -> tanlive.support.Place
	38, // 11: tanlive.support.ReqSearchPlaces.data_type:type_name -> tanlive.base.DataType
	39, // 12: tanlive.support.RspSearchPlaces.places:type_name -> tanlive.support.PlaceWithParents
	40, // 13: tanlive.support.ReqGeocode.geocoding_items:type_name -> tanlive.support.GeocodingItem
	23, // 14: tanlive.support.RspGeocode.results:type_name -> tanlive.support.RspGeocode.Result
	24, // 15: tanlive.support.RspPreGeocode.results:type_name -> tanlive.support.RspPreGeocode.Result
	26, // 16: tanlive.support.ReqSyncAddress.items:type_name -> tanlive.support.ReqSyncAddress.Item
	41, // 17: tanlive.support.ReqDeleteAddress.data_items:type_name -> tanlive.base.DataItem
	27, // 18: tanlive.support.ReqReplaceAddress.items:type_name -> tanlive.support.ReqReplaceAddress.Item
	28, // 19: tanlive.support.ReqPublishAddress.items:type_name -> tanlive.support.ReqPublishAddress.Item
	41, // 20: tanlive.support.ReqGetAddressesByDataItem.data_items:type_name -> tanlive.base.DataItem
	29, // 21: tanlive.support.RspGetAddressesByDataItem.items:type_name -> tanlive.support.RspGetAddressesByDataItem.Item
	30, // 22: tanlive.support.ReqGetDataItemByAddresses.addresses:type_name -> tanlive.support.ReqGetDataItemByAddresses.Address
	38, // 23: tanlive.support.ReqGetDataItemByAddresses.data_type:type_name -> tanlive.base.DataType
	42, // 24: tanlive.support.RspGeocode.Result.address:type_name -> tanlive.support.StructuredFullAddress
	43, // 25: tanlive.support.RspPreGeocode.Result.raw_address:type_name -> tanlive.support.RawAddress
	40, // 26: tanlive.support.RspPreGeocode.Result.geocoding_item:type_name -> tanlive.support.GeocodingItem
	43, // 27: tanlive.support.ReqSyncAddress.Address.raw_address:type_name -> tanlive.support.RawAddress
	42, // 28: tanlive.support.ReqSyncAddress.Address.structured_address:type_name -> tanlive.support.StructuredFullAddress
	41, // 29: tanlive.support.ReqSyncAddress.Item.data_item:type_name -> tanlive.base.DataItem
	25, // 30: tanlive.support.ReqSyncAddress.Item.addresses:type_name -> tanlive.support.ReqSyncAddress.Address
	41, // 31: tanlive.support.ReqReplaceAddress.Item.source:type_name -> tanlive.base.DataItem
	41, // 32: tanlive.support.ReqReplaceAddress.Item.target:type_name -> tanlive.base.DataItem
	41, // 33: tanlive.support.ReqPublishAddress.Item.data_item:type_name -> tanlive.base.DataItem
	41, // 34: tanlive.support.RspGetAddressesByDataItem.Item.data_item:type_name -> tanlive.base.DataItem
	44, // 35: tanlive.support.RspGetAddressesByDataItem.Item.addresses:type_name -> tanlive.support.StructuredAddress
	43, // 36: tanlive.support.ReqGetDataItemByAddresses.Address.raw_address:type_name -> tanlive.support.RawAddress
	1,  // 37: tanlive.support.SupportService.Translate:input_type -> tanlive.support.ReqTranslate
	3,  // 38: tanlive.support.SupportService.ProxyTencentMap:input_type -> tanlive.support.ReqProxyTencentMap
	5,  // 39: tanlive.support.SupportService.ProxyGoogleMap:input_type -> tanlive.support.ReqProxyGoogleMap
	7,  // 40: tanlive.support.SupportService.GetPlaceSelector:input_type -> tanlive.support.ReqGetPlaceSelector
	9,  // 41: tanlive.support.SupportService.SearchPlaces:input_type -> tanlive.support.ReqSearchPlaces
	11, // 42: tanlive.support.SupportService.Geocode:input_type -> tanlive.support.ReqGeocode
	11, // 43: tanlive.support.SupportService.PreGeocode:input_type -> tanlive.support.ReqGeocode
	14, // 44: tanlive.support.SupportService.SyncAddress:input_type -> tanlive.support.ReqSyncAddress
	15, // 45: tanlive.support.SupportService.DeleteAddress:input_type -> tanlive.support.ReqDeleteAddress
	16, // 46: tanlive.support.SupportService.ReplaceAddress:input_type -> tanlive.support.ReqReplaceAddress
	17, // 47: tanlive.support.SupportService.PublishAddress:input_type -> tanlive.support.ReqPublishAddress
	18, // 48: tanlive.support.SupportService.GetAddressesByDataItem:input_type -> tanlive.support.ReqGetAddressesByDataItem
	20, // 49: tanlive.support.SupportService.GetDataItemByAddresses:input_type -> tanlive.support.ReqGetDataItemByAddresses
	0,  // 50: tanlive.support.SupportService.CreateMetric:input_type -> tanlive.support.ReqCreateMetric
	2,  // 51: tanlive.support.SupportService.Translate:output_type -> tanlive.support.RspTranslate
	4,  // 52: tanlive.support.SupportService.ProxyTencentMap:output_type -> tanlive.support.RspProxyTencentMap
	6,  // 53: tanlive.support.SupportService.ProxyGoogleMap:output_type -> tanlive.support.RspProxyGoogleMap
	8,  // 54: tanlive.support.SupportService.GetPlaceSelector:output_type -> tanlive.support.RspGetPlaceSelector
	10, // 55: tanlive.support.SupportService.SearchPlaces:output_type -> tanlive.support.RspSearchPlaces
	12, // 56: tanlive.support.SupportService.Geocode:output_type -> tanlive.support.RspGeocode
	13, // 57: tanlive.support.SupportService.PreGeocode:output_type -> tanlive.support.RspPreGeocode
	45, // 58: tanlive.support.SupportService.SyncAddress:output_type -> google.protobuf.Empty
	45, // 59: tanlive.support.SupportService.DeleteAddress:output_type -> google.protobuf.Empty
	45, // 60: tanlive.support.SupportService.ReplaceAddress:output_type -> google.protobuf.Empty
	45, // 61: tanlive.support.SupportService.PublishAddress:output_type -> google.protobuf.Empty
	19, // 62: tanlive.support.SupportService.GetAddressesByDataItem:output_type -> tanlive.support.RspGetAddressesByDataItem
	21, // 63: tanlive.support.SupportService.GetDataItemByAddresses:output_type -> tanlive.support.RspGetDataItemByAddresses
	45, // 64: tanlive.support.SupportService.CreateMetric:output_type -> google.protobuf.Empty
	51, // [51:65] is the sub-list for method output_type
	37, // [37:51] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_tanlive_support_service_proto_init() }
func file_tanlive_support_service_proto_init() {
	if File_tanlive_support_service_proto != nil {
		return
	}
	file_tanlive_support_support_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_support_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqTranslate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspTranslate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqProxyTencentMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspProxyTencentMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqProxyGoogleMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspProxyGoogleMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetPlaceSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetPlaceSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchPlaces); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSearchPlaces); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGeocode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGeocode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspPreGeocode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSyncAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqReplaceAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqPublishAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetAddressesByDataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetAddressesByDataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetDataItemByAddresses); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetDataItemByAddresses); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateMetric_Metric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGeocode_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspPreGeocode_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSyncAddress_Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSyncAddress_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqReplaceAddress_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqPublishAddress_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetAddressesByDataItem_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_support_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetDataItemByAddresses_Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tanlive_support_service_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*ReqGetPlaceSelector_ByDataType)(nil),
		(*ReqGetPlaceSelector_ByPlaceId)(nil),
	}
	file_tanlive_support_service_proto_msgTypes[24].OneofWrappers = []interface{}{
		(*RspPreGeocode_Result_RawAddress)(nil),
		(*RspPreGeocode_Result_GeocodingItem)(nil),
	}
	file_tanlive_support_service_proto_msgTypes[25].OneofWrappers = []interface{}{
		(*ReqSyncAddress_Address_RawAddress)(nil),
		(*ReqSyncAddress_Address_StructuredAddress)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_support_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_support_service_proto_goTypes,
		DependencyIndexes: file_tanlive_support_service_proto_depIdxs,
		MessageInfos:      file_tanlive_support_service_proto_msgTypes,
	}.Build()
	File_tanlive_support_service_proto = out.File
	file_tanlive_support_service_proto_rawDesc = nil
	file_tanlive_support_service_proto_goTypes = nil
	file_tanlive_support_service_proto_depIdxs = nil
}
