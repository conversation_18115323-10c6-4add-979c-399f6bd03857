syntax = "proto3";

package tanlive.ai;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai";

import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "tanlive/ai/ai.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/errors/ai.proto";
import "tanlive/options.proto";


message ReqCreateUserChat{
  // 会话标题
  string  title = 1 [(tanlive.validator) = "required"];
  // 创建人
  uint64 create_by = 2;
  // 会话类型
  tanlive.ai.ChatType type = 4;
  // 应用id
  string app_id = 5;
  // 助手id
  uint64 assistant_id = 6 [(validator) = "required"];
  // 创建人国家或地区编码
  string region_code = 7 [(validator) = "required"];
}

message RspReceiveUserChatMessage {
  // 问题的id
  uint64 message_id = 1;
}

message ReqResendUserChatMessage {
  // 问题的id
  uint64 message_id = 1;
  uint64 user_id = 3;
}

message ReqCreateChatMessage{
  tanlive.ai.ChatMessage message = 1;
  // 助手id
  uint64 assistant_id = 2 [(validator) = "required"];
}

message RspCreateChatMessage{
  tanlive.ai.ChatMessage message = 1;
}

message ReqPublishChatMessage{
  tanlive.ai.EventChatMessage message = 1 [(tanlive.validator) = "required"];
  uint64 user_id = 2 [(tanlive.validator) = "required"];
  // 是否是推送运营端
  bool is_op = 3;
  // 是否仅搜索
  bool is_only_search = 4;
}

message ReqSendMessageSync{
  // 问题
  tanlive.ai.ChatMessage message = 1;
  // 助手
  tanlive.ai.AssistantDetail assistant_detail = 2 [(validator) = "required"];
  // 携带的文件文本
  repeated string file_texts = 3;
}

message RspSendMessageSync{
  tanlive.ai.ChatMessage message = 1;
}

message RspSendMessagesSync{
  repeated tanlive.ai.ChatMessage messages = 1;
}

message ReqSendMessageWithoutSaveSync{
  // 消息内容
  string text = 2 [(validator) = "required"];
  // 问题id
  uint64 question_id = 4 [(validator) = "required"];
  // 助手
  tanlive.ai.AssistantDetail assistant_detail = 5 [(validator) = "required"];
  // 推流hash_id
  string hash_id = 6;
  bool wait_answer = 7;
}

message RspSendMessageWithoutSaveSync{
  tanlive.ai.ChatMessage message = 1;
}

message ReqDescribeMessage{
  uint64 message_id = 1 [(validator) = "required"];
  bool with_config = 2;
  bool with_collection = 3;
  bool with_feedback = 4;
  bool with_docs = 5;
}

message ReqDescribeMessageByQuestionId{
  uint64 question_id = 1 [(validator) = "required"];
}

message RspDescribeMessage{
  tanlive.ai.ChatMessage message = 1;
  tanlive.ai.MessageConfig config = 2;
}

message RspDescribeMessageByQuestionId{
  tanlive.ai.ChatMessage message = 1;
}




message RspCreateUserChat{
  // 会话id
  uint64 id = 1;
}

message ReqDescribeChat{
  uint64 chat_id = 1;
  string app_id = 5;
  tanlive.ai.ChatType type = 6;
}

message ReqUpdateTextFileInBulk{
  repeated ReqUpdateTextFile items = 1;
}

message ReqUpdateTextFile{
  uint64 id = 1;
  string name = 2;
  string text = 3;
  string url = 4;
  repeated uint64 scoped_assistant_id = 5;
  repeated DocAssistantState states = 6;
  repeated Contributor contributor = 7;
  Operator update_by = 8;
  uint32 hit_count = 9;
  base.DataType ugc_type = 10;
  uint64 ugc_id = 11;
  string parsed_url = 12;
  google.protobuf.FieldMask mask = 13;
  uint32 show_contributor = 14;
  repeated CustomLabel labels = 15;
  uint64 label_tenant = 16;
  repeated DocReference reference = 17;
  ai.DocFileDownloadAsRef download_as_ref = 18;
}

message ReqDeleteDocInBulk{
  repeated uint64 id = 1;
  Operator operator = 2;
  // scoped_assistant_id 为空时， 从所有助手中完全删除doc
  // 非空时，限定在scoped_assistant_id下删除 doc
  repeated uint64 scoped_assistant_id = 3;
  uint64 query_id = 4;
  // 是否是贡献者删除
  bool is_contributor_delete = 5;
}

message RspDeleteDocInBulk {
  bool async = 1;
}

message ReqReparseTextFiles{
  repeated uint64 ids = 1;
  Operator operator = 2;
  ai.DocParseMode parse_mode = 3;
  uint64 query_id = 4;
}

message RspReparseTextFiles {
  bool async = 1;
}

message ReqOnOffDocInBulk{
  repeated uint64 id = 1;
  Operator operator = 2;
  DocState state = 3;
  repeated uint64 scoped_assistant_id = 4;
  uint64 query_id = 5;
}

message RspOnOffDocInBulk{
  message RepeatCollection{
    uint64 id = 1;
    map<uint64, string> file_name = 2;
  }
  repeated RepeatCollection pre_repeat_collections = 1;
  repeated RepeatCollection repeat_collections = 2;
  message QaContainsMatchCount{
    uint64 assistant_id = 1;
    uint64 qaCnt = 2;
    repeated uint64 doc_ids = 3;
  }
  repeated QaContainsMatchCount exceed_qa_contains_match_limit = 3;
  bool async = 4;
}

message ReqCreateTextFileInBulk{
  repeated TextFile items = 1;
  message Slice{
    repeated uint64 id = 1;
  }
  repeated Slice shared_assistant = 2;
}

message RspCreateTextFileInBulk{
  repeated uint64 id = 1;
}

message ReqListTextFile{
  DocState parse_state = 1;
  DocState state = 2;
  repeated ContributorFilter contributor = 3;
  repeated OperatorFilter update_by = 4;
  repeated base.OrderBy order_by = 5;
  base.Paginator page = 6;
  message Search{
    string text = 1; // 文本内容搜索
    string file_name = 2; // 文件名搜索
    repeated string full_file_name = 3; //文件名全匹配搜索
    // UGC标题模糊搜索
    string ugc_title = 4;
  }
  Search search = 7;
  google.protobuf.FieldMask mask = 8; //需要返回的字段，为空则省略
  repeated DocType doc_type = 9; // 区分文件/文本
  repeated uint64 id = 10;
  message TenantCond{
    repeated ContributorFilter contributor = 1;
    repeated uint64 assistant_id = 2;
  }
  TenantCond tenant_cond = 11;
  repeated uint64 assistant_id = 12;
  uint32 show_contributor = 13;
  // 不在助手中
  repeated uint64 excluded_assistant_id = 14;
  // 重复 doc 紧凑排序
  bool group_repeated = 15;
  // 只获取text片段（300字符），不获取全量的数据，
  bool text_excerpt = 16;
  // 数据来源
  DocDataSource data_source = 17;

  // 是否为系统文档
  // 已废弃，使用 data_source替代
  // bool is_system = 17;
  // 忽略is_system条件
  // 已废弃
  //  bool ignore_is_system = 18;

  // 查询副本
  bool with_copies = 19;
  // 是否为副本
  bool is_copy = 20;
  // 忽略is_copy条件
  bool ignore_is_copy = 21;
  // UGC模块
  repeated base.DataType ugc_type = 22;
  // 内容状态
  repeated ai.DocContentState content_state = 23;
  // 自定义标签
  repeated LabelFilter labels = 24;
  // 分享状态
  ListDocSharedFileter shared_state = 25;
  // 分享筛选，或关系
  DocSharedReceiverFilter shared_receivers = 26;
  // 自定义标签排序，只能当个标签排序
  OrderByLabel order_by_label = 28;
  // 标签的租户
  uint64 label_tenant = 29;
  bool withLabelDetailInfo = 30;
  // tql查询表达式
  string tql = 31;
  ai.DocFileDownloadAsRef download_as_ref = 32;
  repeated OperatorFilter create_by = 33;
  repeated ai.DocParseMode parse_mode = 34;
  // 是否返回知识提示
  bool with_tips = 35;

  // 知识提示过滤条件，用来筛选解析失败或表格过长等问题的记录
  message TipFilter {
    // 警告条件组
    message WarningGroup {
      // 表格超长
      bool table_oversize = 1;
      // 解析失败
      bool parse_failed = 2;
    }
    WarningGroup warning_group = 1;
  }
  TipFilter tip_filter = 36;
  // 外部数据源状态
  uint32 data_source_state = 37;
}

message ListDocSharedFileter{
  ContributorFilter contributor = 1;
  // 分享的状态
  repeated DocSharedState shared_state = 25;
}

message RspListTextFile{
  repeated FullTextFile items = 1;
  uint32 total = 2;
  uint32 fail_parse_count = 3;
}

message ContributorFilter{
  Contributor contributor = 1;
  bool only_match_type = 2;
}

message OperatorFilter{
  Operator operator = 1;
  bool only_match_type = 2;
}

// 分享筛选，或关系
message DocSharedReceiverFilter{
  repeated uint64 assistant_id = 1;
  repeated uint64 user_id = 2;
  repeated uint64 team_id = 3;
}

message ReqListQA{
  DocState state = 2;
  repeated ContributorFilter contributor = 3;
  repeated OperatorFilter update_by = 4;
  repeated base.OrderBy order_by = 5;
  base.Paginator page = 6;
  message Search{
    string qa = 1;
  }
  Search search = 7;
  repeated uint64 id = 8;
  message TenantCond{
    repeated ContributorFilter contributor = 1;
    repeated uint64 assistant_id = 3;
  }
  TenantCond tenant_cond = 9;
  // 助手 id，元素里面可以传 0，0代表没有绑定助手的文档
  repeated uint64 assistant_id = 10;
  uint32 show_contributor = 11;
  // 不在助手中
  repeated uint64 excluded_assistant_id = 12;
  // 重复 doc 紧凑排序
  bool group_repeated = 13;
  // 自定义标签
  repeated LabelFilter labels = 14;
  // 分享状态
  ListDocSharedFileter shared_state = 25;
  // 分享筛选，或关系
  DocSharedReceiverFilter shared_receivers = 26;
  // 自定义标签排序，只能单个标签排序
  OrderByLabel order_by_label = 18;
  // 标签的租户
  uint64 label_tenant = 19;
  bool withLabelDetailInfo = 20;
  // tql查询表达式
  string tql = 31;
  repeated OperatorFilter create_by = 32;
  repeated DocMatchPattern match_patterns = 33;
  bool with_tips = 34;

  // 知识提示过滤条件，用来筛选问题超长等问题的记录
  message TipFilter {
    // 警告条件组
    message WarningGroup {
      // 问题超长
      bool question_oversize = 1;
    }
    WarningGroup warning_group = 1;
  }
  TipFilter tip_filter = 35;
}

message RspListQA{
  repeated QA items = 1;
  uint32 total = 2;
}

message ReqCreateQA{
  QA item = 1;
  repeated uint64 shared_assistant = 2;
}

message RspCreateQA{
  uint64 id = 1;
}

message ReqCreateQAInBulk{
  repeated QA items = 1;
  message Slice{
    repeated uint64 id = 1;
  }
  repeated Slice shared_assistant = 2;
}

message RspCreateQAInBulk{
  repeated uint64 id = 1;
}

message ReqUpdateChatMessage{
  uint64 id = 1 [(tanlive.validator) = "required"];
  string lang = 2;
  uint64 assistant_id = 3;
  string content = 4;
  uint32 state = 6;
  google.protobuf.FieldMask mask = 7;
  MessageCollectionSnapshot collection_snapshot = 8;
  string think = 9;
}

message ReqUpdateChatMessageThink {
  uint64 message_id = 1;
  string content = 2;
  int32 duration = 3;
}

message ReqUpdateQA{
  string question = 1;
  string answer = 2;
  repeated DocReference reference = 3;
  // 限定在以下助手中进行update状态
  repeated uint64 scoped_assistant_id = 4;
  repeated DocAssistantState states = 5;
  uint64 id = 6;
  Operator operator = 7;
  uint32 hit_count = 8;
  google.protobuf.FieldMask mask = 9;
  repeated Contributor contributor = 10;
  uint32 show_contributor = 11;
  repeated CustomLabel labels = 15;
  uint64 label_tenant = 16;
  repeated DocMatchPattern match_patterns = 17;
}

message ReqUpdateQAInBulk{
  repeated ReqUpdateQA items = 1;
}

message ReqDescribeUserChats{
  uint64 user_id = 1 [(tanlive.validator) = "required"];
  uint32 offset = 2;
  uint32 limit = 3;
  repeated uint64 ids = 5;
  uint64 assistant_id = 6;
}

message RspDescribeUserChats{
  repeated Chat chat = 1;
  uint32 total_count = 2;
}

message ReqDeleteUserChat {
  uint64 user_id = 1 [(tanlive.validator) = "required"];
  uint64 chat_id = 2 [(tanlive.validator) = "required"];
}
message ReqDeleteChatMessage {
  uint64 message_id = 2;
  // 删除指定问题答案
  uint64 question_id = 3;
}

message ReqSearchCollectionOneShot{
  string search = 1;
  repeated uint64 assistant_id = 2;
  DocType doc_type = 3;
  uint32 top_n = 4;
  bool time_record = 5;
  float threshold = 6;
  uint32 from = 7;
  bool is_search_chat = 9;
  uint32 text_recall_top_n = 10;
  bool clean_chunks = 11;
  // 关键词召回匹配目标
  tanlive.ai.TextRecallQuery text_recall_query = 12;
  // 关键词召回模式
  tanlive.ai.TextRecallPattern text_recall_pattern = 13;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 14;
  float temperature = 15;
  // 偏移量
  uint32 offset = 16;
  // 页面大小
  uint32 limit = 17;
}


message ReqSearchCollection{
  string search = 1;
  repeated uint64 assistant_id = 2;
  DocType doc_type = 3;
  uint32 top_n = 4;
  bool time_record = 5;
  float threshold = 6;
  uint32 from = 7;
  float text_weight = 8;
  bool is_search_chat = 9;
}

message RspSearchCollectionOneShot{
  repeated SearchCollectionItem search_items = 1;
  repeated SearchCollectionItem search_text_items = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
  uint32 total_count = 5;
}

message RspSearchCollection{
  repeated SearchCollectionItem items = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  uint32 total_count = 4;
}

message ReqListCollection{
  repeated uint64 assistant_id = 1;
}

message RspListCollection{
  repeated Collection collections = 1;
}

message ReqListContributor{
  message OrGroup{
    repeated uint64 scoped_assistant_id = 1; // 绑定了该助手的 doc 下查找贡献值
    Contributor contributor = 2; // 由该贡献者贡献的 doc 下查找贡献者
  }
  repeated uint64 dco_id = 1;
  OrGroup or = 2;
  tanlive.ai.ListDocFilterType type = 3;
  tanlive.ai.DocDataSource data_source = 4;
  // 返回贡献者所在的助手列表
  bool with_assistant_id = 5;
}

message RspListContributor{
  // 列表形式的结果
  repeated Contributor contributors = 1;
  // 每个doc对应的结果，只有在请求中指定doc_id过滤的情况下才会返回
  map<uint64, slice> contributor_map = 2;
  message slice{
    repeated Contributor contributors = 1;
  }
}

message ReqListUpdateBy{
  message OrGroup{
    repeated uint64 scoped_assistant_id = 1; // 绑定了该助手的 doc 下查找更新人
    Contributor contributor = 2; // 由该贡献者贡献的 doc 下查找更新人
  }
  repeated uint64 dco_id = 1;
  OrGroup or = 2;
  tanlive.ai.ListDocFilterType type = 3;
  tanlive.ai.DocDataSource data_source = 4;
}


message RspListUpdateBy{
  repeated Operator operators = 1;
}

message ReqListSharedAssistant{
  string search = 1;
  Contributor contributor = 2; // 由该贡献者贡献的 doc 下查找
  tanlive.ai.ListDocFilterType type = 3;
  tanlive.ai.DocDataSource data_source = 4;
}

message RspListSharedAssistant{
  repeated Assistant assistants = 1;
}

message ReqValidateQAInBulk{
  repeated QA items = 1;
  Contributor contributor = 2;
  repeated uint64 scopedAssistant = 3;
}

message RspValidateQAInBulk{
  message Item {
    tanlive.errors.AiError error = 1;
    uint64 id = 2;
  }
  repeated Item errors = 1;
}

message ReqValidateTextFileInBulk{
  repeated TextFile items = 1;
  Contributor contributor = 2;
  repeated uint64 scopedAssistant = 3;
  ai.DocDataSource data_source = 4;
}

message RspValidateTextFileInBulk{
  message Item {
    tanlive.errors.AiError error = 1;
    uint64 id = 2;
  }
  repeated Item errors = 1;
}

message ReqCloneDocInBulk{
  repeated uint64 id = 1;
  Operator operator = 2;
  repeated uint64 scoped_assistant_id = 3;
  uint64 query_id = 4;
}

message RspCloneDocInBulk{
  repeated uint64 id = 1;
}

message ReqCreateSystemDocCopy {
  /*
  message Para {
    // 文本内容
    string text = 1 [(validator) = "required"];
    // 助手ID
    repeated uint64 assistant_id = 2 [(validator) = "omitempty,dive,required"];
    // 贡献者
    repeated Contributor contributors = 3 [(validator) = "omitempty,dive,required"];
    // 是否显示贡献者
    uint32 show_contributor = 4 [(validator) = "required,oneof=1 2"];
    // 创建人
    base.Identity create_by = 5 [(validator) = "required"];
  }
  */
  // 拷贝自
  uint64 copy_from = 1 [(validator) = "required"];
  // 创建人
  base.Identity create_by = 2 [(validator) = "required"];
  /*
  // 参数
  Para para = 3 [(validator) = "required"];
  */
}

message RspCreateSystemDocCopy {
  // 副本文档ID
  uint64 doc_id = 1;
}

message ReqEnableSystemDoc {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 操作人
  base.Identity operator = 2 [(validator) = "required"];
}

message ReqDisableSystemDoc {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 操作人
  base.Identity operator = 2 [(validator) = "required"];
}

message ReqDeleteSystemDoc {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 操作人
  base.Identity operator = 2 [(validator) = "required"];
}

message ReqRateAiAnswer {
  // 消息ID
  uint64 chat_message_id = 1 [(validator) = "required"];
  // 评价等级
  RatingScale rating_scale = 2 [(validator) = "required"];
  // 更新人
  uint64 update_by = 3 [(validator) = "required"];
}

message ReqUpsertUserFeedback {
  // 创建条件
  oneof cond {
    // 通过助手ID（仅支持创建）
    uint64 by_assistant_id = 1 [(validator) = "required"];
    // 通过答案ID
    uint64 by_answer_id = 2 [(validator) = "required"];
  }
  // 问题
  string question = 3 [(validator) = "required_with=AssistantId"];
  // 答案
  string answer = 4 [(validator) = "required"];
  // 参考文献
  repeated TypedReference references = 5 [(validator) = "omitempty,dive"];
  // 操作人
  base.Identity operator = 6 [(validator) = "required"];
  // 创建人国家或地区编码，如有
  string region_code = 7;
}

message ReqUpsertOpFeedback {
  // AI回答评价
  FeedbackAnswerRating answer_rating = 2 [(validator) = "required"];
  // 是否命中预期知识
  bool hit_expected_doc = 3;
  // 预期命中的知识
  repeated uint64 doc_id = 4 [(validator) = "omitempty,dive,required"];
  // 分析备注
  FeedbackComment op_comment = 5;
  // 操作人
  base.Identity operator = 6 [(validator) = "required"];
  // 操作人管理的助手列表
  repeated uint64 operator_assistant_id = 7 [(validator) = "required"];
  // 更新条件
  oneof cond {
    // 通过答案ID
    uint64 by_answer_id = 8 [(validator) = "required"];
    // 通过反馈ID
    uint64 by_feedback_id = 9 [(validator) = "required"];
  }
}

message ReqUpsertMgmtFeedback {
  // AI回答评价
  FeedbackAnswerRating answer_rating = 2 [(validator) = "required"];
  // 碳LIVE反馈
  FeedbackComment mgmt_feedback = 3 [(validator) = "required"];
  // 碳LIVE内部备注
  FeedbackComment mgmt_comment = 4;
  // 操作人
  base.Identity operator = 5 [(validator) = "required"];
  // 更新条件
  oneof cond {
    // 通过答案ID
    uint64 by_answer_id = 6 [(validator) = "required"];
    // 通过反馈ID
    uint64 by_feedback_id = 7 [(validator) = "required"];
  }
  // 预期命中的知识
  repeated uint64 mgmt_doc_id = 8 [(validator) = "omitempty,dive,required"];
}

message RspUpsertFeedback {
  // 反馈ID
  uint64 feedback_id = 1;
}

message ReqGetFeedbacks {
  // 场景
  enum Scene {
    SCENE_UNSPECIFIED = 0;
    // 在控制台
    SCENE_IN_CONSOLE = 1;
  }
  // 过滤器
  message Filter {
    // 上传用户ID
    repeated uint64 create_by = 1;
    // 创建时间区间
    base.TimeRange create_date_range = 2;
    // 用户反馈ID
    repeated uint64 feedback_id = 3;
    // 助手ID
    repeated uint64 assistant_ids = 4;
    repeated string region_codes = 5;
    // 展示场景
    Scene show_scene = 6;
  }
  // 关系
  message Relation {
    // 查询参考文献
    bool references = 1;
    // 预期命中的知识
    bool expected_docs = 2;
    // 查询原始问题
    bool original_question = 3;
    // 查询原始答案
    bool original_answer = 4;
    // 预期命中的知识（碳LIVE运营）
    bool expected_mgmt_docs = 5;
  }
  // 过滤器
  Filter filter = 1;
  // 分页器
  base.Paginator page = 2;
  // 是否需要总数
  bool with_total_count = 3;
  // 排序
  repeated base.OrderBy order_by = 4;
  // 加载关系
  Relation relation = 5;
}

message RspGetFeedbacks {
  // 反馈列表
  repeated FullFeedback feedbacks = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqReadFeedback {
  // 用户反馈ID
  uint64 feedback_id = 1 [(validator) = "required"];
  oneof userType {
    // 运营端用户ID
    uint64 mgmt_read_by = 2 ;
    // 门户端用户ID
    uint64 portal_handled_by = 3 ;
  }
}

message ReqAcceptFeedback {
  // 用户反馈ID
  uint64 feedback_id = 1 [(validator) = "required"];
  // 操作人
  base.Identity operator = 2 [(validator) = "required"];
}

message ReqGetFeedbackLogs {
  message Filter {
    // 操作类型
    repeated FeedbackAction action = 1;
    // 操作人
    base.Identity create_identity = 2;
    // 反馈ID
    uint64 feedback_id = 3;
    // 操作时间范围
    base.TimeRange create_date_range = 4;
    // 助手ID
    repeated uint64 assistant_id = 5;
  }
  message Relation {
    // 反馈
    bool feedback = 1;
  }
  // 过滤器
  Filter filter = 1;
  // 分页
  base.Paginator page = 2;
  // 排序
  repeated base.OrderBy order_by = 3;
  // 关系
  Relation relation = 4;
  // 返回总数
  bool with_total_count = 5;
}

message RspGetFeedbackLogs {
  // 日志列表
  repeated FullFeedbackLog logs = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqGetDocs {
  // 文档ID
  repeated uint64 doc_id = 1 [(validator) = "required,dive,required"];
}

message RspGetDocs {
  // 文档列表
  repeated ChatMessageDoc docs = 1;
}

message ReqGetAssistantChatCreators {
  // 助手id
  repeated uint64 assistant_ids = 1 [(validator) = "required,dive,required"];
  repeated uint64 user_ids = 2;
}

message RspGetAssistantChatCreators {
  // 创建者用户ID列表
  repeated uint64 user_id = 1;
}

message ReqListChat {
  message Filter {
    // 用户id
    repeated uint64 user_ids = 1;
    // 对话内容
    repeated string chat_titles = 2;
    ChatType chat_type = 3;
    repeated string nicknames = 4;
    // 筛选审核 1 违规 2 敏感 3 正常
    uint32 reject_job_result = 5;
    // 国家或地区编码
    repeated string region_codes = 6;
    // 是否转过人工服务 1 否 2 是
    int32 is_manual = 9;
  }
  uint32 offset = 1;
  uint32 limit = 2;
  repeated tanlive.base.OrderBy order_by = 3;
  Filter filter = 4 ;
  // 创建时间区间
  tanlive.base.TimeRange create_date_range = 5;
  // 处理时间区间
  tanlive.base.TimeRange update_date_range = 6;
  // 来源
  ChatOrigin origin = 7;
  // 自定义标签kv对
  repeated tanlive.ai.LabelFilter labels = 8;
  // 助手id
  repeated uint64 assistant_ids = 9;
  uint64 label_tenant = 10;
  // 自定义标签排序，只能当个标签排序
  OrderByLabel order_by_label = 11;
  // 筛选ids
  repeated uint64 ids = 12;
}

message RspListChat {
  repeated Chat chats = 1;
  uint32 total_count = 2;
}

message ReqGetChatDetail {
  // chat_id
  uint64 id = 1;
  // 来源
  ChatOrigin origin = 2;
  // 助手id
  repeated uint64 assistant_id = 3;
}

message RspGetChatDetail {
  ChatDetail chat_detail = 1;
}


message ReqSyncHelpCenterDoc{
  string zh_cos_prefix = 1;
  string en_cos_prefix = 2;
  string suffix = 3;
  // [zh, en]
  repeated string languages = 4;
}

message ReqStopAnswerReply{
  uint64 message_id = 1 [(validator) = "required"];
  string stop_text = 2;
  string stop_think = 3;
  int32 stop_chunk_state = 4;
  string hash_id = 5 [(validator) = "required"];
}

message RspStopAnswerReply{
  uint64 id = 1;
}

message ReqDescribeMessageDocs{
  string doc_names = 1 [(validator) = "required"];
  // 是否计数命中次数
  bool hit_count = 2;
}

message RspDescribeMessageDocs{
  repeated  ChatMessageDoc docs = 1;
}

message ReqDescribeMessageLog{
  uint64 message_id = 1 [(validator) = "required"];
  tanlive.ai.AssistantDetail assistant_detail = 3;
  // 运营端显示所有字段，传true
  bool ignore_chain_visible = 4;
}

message RspDescribeMessageLog{
  repeated  ChatMessageLog message_logs = 1;
}

message ReqDescribeSuggestLog{
  uint64 message_id = 1 [(validator) = "required"];
}

message RspDescribeSuggestLog{
  repeated  ChatSuggestLog logs = 1;
}

message ReqListDocByRef{
  repeated string ref = 1;
}

message ReqCreateAllDocMd5{}

message RspListDocByRef{
  message Doc {
    string text = 1;
    string question = 2;
    string file_name = 4;
    string url = 5;
    repeated tanlive.ai.Contributor contributor = 6;
    Operator update_by = 7;
    string ref = 8;
  }
  repeated Doc docs = 1;
}

message ReqCreateDocShareConfigSender{
  uint64 user_id = 1;
  base.IdentityType admin_type = 2;
  repeated uint64 share_assistant_id = 3;
  repeated uint64 share_user_id = 4;
  repeated uint64 share_team_id = 5;
}

message ReqListCustomLabel{
  // ids主键id。 非空时，指定id查询， 为空是，查询所有
  repeated uint64 ids = 1;
  uint64 tenant_id = 2;
  base.Paginator page = 3;
  tanlive.ai.CustomLabelObjectType object_type = 4;
}

message ReqListeDocShareConfigSender{
  uint64 create_by = 1;
  base.IdentityType admin_type = 2;
}

message RspListeDocShareConfigSender{
  repeated uint64 share_assistant_id = 1;
  repeated uint64 my_admin_assistant_id = 2;
  repeated uint64 share_user_id = 3;
  repeated uint64 share_team_id = 4;
}

message ReqListAssistantCanShareDoc{
  uint64 create_by = 1;
  base.IdentityType admin_type = 2;
  uint64 doc_id = 3;
  // 模糊匹配助手名称
  string name = 4;
  // 语言
  string language = 5;
}

message RspListAssistantCanShareDoc{
  message SharedAssistant{
    uint64 id = 1;
    string name = 2;
    string name_en = 3;
    bool is_selected = 4;
  }

  repeated SharedAssistant assistants = 1;
}

message ReqListTeamCanShareDoc{
  uint64 create_by = 1;
  base.IdentityType admin_type = 2;
  string name = 3;
  uint64 offset = 4;
  uint64 limit = 5;
  uint64 doc_id = 6;
}

message RspListTeamCanShareDoc{
  message Teams{
    uint64 id = 1;
    string name = 2;
    string name_en = 3;
    bool is_selected = 4;
  }

  repeated Teams teams = 1;
}

message ReqListUserCanShareDoc{
  uint64 create_by = 1;
  base.IdentityType admin_type = 2;
  // 用户只能显示判断某个人
  uint64 user_id = 3;
  uint64 doc_id = 4;
}

message RspListUserCanShareDoc{
  message Users{
    uint64 id = 1;
    string name = 2;
    bool is_selected = 4;
  }

  repeated Users users = 1;
}

message ReqListSharedTeam{
  Contributor contributor = 1; // 由该贡献者贡献的 doc 下查找已分享的团队
  tanlive.ai.ListDocFilterType type = 2;
  tanlive.ai.DocDataSource data_source = 3;
}

message RspListSharedTeam{
  repeated uint64 ids = 1;
}

message ReqListSharedUser{
  Contributor contributor = 1; // 由该贡献者贡献的 doc 下查找已分享的团队
  tanlive.ai.ListDocFilterType type = 2;
  tanlive.ai.DocDataSource data_source = 3;
}

message RspListSharedUser{
  repeated uint64 ids = 1;
}

message ReqListMyAssistantIds{
  uint64 create_by = 1;
  base.IdentityType admin_type = 2;
}

message RspListMyAssistantIds{
  repeated uint64 share_assistant_ids = 1;
}

// 对特定用户/团队分享的知识，按照特定 state 接收
message DocReceiveConfigBySender{
  repeated uint64 user_id = 1;
  repeated uint64 team_id = 2;
  tanlive.ai.DocShareState state = 3;
}

message ReqCreateDocShareConfigReceiverAssistant{
  uint64 assistant_id = 1 [(validator) = "required"];
  tanlive.ai.DocShareAcceptState  receiver_state = 2 [(validator) = "required"];
  repeated DocReceiveConfigBySender user_shares = 3;
  tanlive.ai.DocShareState   other_state = 4;

  base.IdentityType admin_type = 5;
  uint64 create_by = 6;
}

message ReqCreateDocShareConfigReceiverUserTeam{
  tanlive.ai.DocShareAcceptState receiver_state = 1;
  tanlive.ai.DocShareState  other_state = 2;
  repeated DocReceiveConfigBySender user_shares = 3;
  base.IdentityType admin_type = 4;
  uint64 create_by = 5;
}

message ReqListDocShareConfigReceiverAssistant{
  uint64 assistant_id = 1;
  uint64 create_by = 3;
  base.IdentityType admin_type = 4;
}

message RspListDocShareConfigReceiverAssistant{
  message UserShare {
    repeated uint64 user_id = 1;
    repeated uint64 team_id = 2;
    tanlive.ai.DocShareState state = 3;
    uint64 group_id = 4;
  }

  uint64 assistant_id = 1;
  tanlive.ai.DocShareAcceptState receiver_state = 2;
  repeated UserShare user_shares = 3;
  tanlive.ai.DocShareState  other_state = 4;
}

message ReqListDocShareConfigReceiverUserTeam{
  uint64 create_by = 3;
  base.IdentityType admin_type = 4;
}

message RspListDocShareConfigReceiverUserTeam{
  message UserShare {
    repeated uint64 user_id = 1;
    repeated uint64 team_id = 2;
    tanlive.ai.DocShareState state = 3;
    uint64 group_id = 4;
  }

  tanlive.ai.DocShareAcceptState receiver_state = 2;
  repeated UserShare user_shares = 3;
  tanlive.ai.DocShareState  other_state = 4;
}

message ReqCreateDocShare{
  repeated uint64 assistant_id = 1;
  uint64 doc_id = 2;
  // 批量操作doc
  repeated uint64 doc_ids = 3;
  base.IdentityType admin_type = 5;
  uint64 create_by = 6;
  bool is_contributor = 8;
  uint64 query_id = 9;
  Operator operator = 10;
  // 分享给个人的ID列表
  repeated uint64 user_id = 11;
  // 分享给团队的ID列表
  repeated uint64 team_id = 12;
}

message RspCreateDocShare{
  bool async = 1;
}

message ReqCreateOpenWechat{
  string permanent_code = 1;
  string corp_id = 2;
  string action = 3;
  string open_kfid = 4;
}

message ReqDescribeOpenWechat{
  string corp_id = 1;
}

message RspDescribeOpenWechat{
  string permanent_code = 2;
}

message ReqAuthTencentCode{
  string code = 1  [(validator) = "required"];
  string path = 2;
  uint64 user_id = 4;
}

message RspAuthTencentCode{
}

message ReqDescribeTokenIsEmpty{
  uint64 user_id = 2;
  string encrypted_openid = 3;
}

message RspDescribeTokenIsEmpty{
  bool is_empty = 1;
}

message ReqListExternalSourceUser{
  // tanlive 用户ID
  uint64 user_id = 1;
}

message RspListExternalSourceUser{
  repeated ExternalSourceUser users = 1;
}

message ReqCreateGTBText{
  bool is_pull_all = 1;
  uint32 admin_type = 6;
  uint64 account_id = 7;
  uint64 user_id = 8;
}

message RspCreateGTBText{
  string uuid = 1;
}

message ReqDescribeTencentDocTask{
  uint64 user_id = 1;
}

message RspDescribeTencentDocTask{
  bool is_running = 1;
}

message ReqDelTencentDocAuth{
  uint64 user_id = 1;
  string encrypted_openid = 2;
}

message ReqDescribeGTBText{
  string uuid = 1;
}

message RspDescribeGTBText{
}

message ReqCreateTencentDocAuthUrl{
}

message RspCreateTencentDocAuthUrl{
  string url = 1;
}

message ReqDescribeTencentDocList{
  string state = 2;
  string search = 3;
  uint32 start = 4;
  string folder_id = 5;
  uint32 admin_type = 6;
  uint64 account_id = 7;
  uint64 user_id = 8;
  string encrypted_openid = 9;
  string title_name_sort = 10; // asc desc
}

message RspDescribeTencentDocList{
  repeated TencentDoc docs = 1;
  uint32 next = 2;
  bool has_expired = 3;
}

message ReqImportTencentDoc{
  repeated string file_ids = 1;
  uint32 admin_type = 6;
  uint64 account_id = 7;
  uint64 user_id = 8;
  string encrypted_openid = 9;
  uint64 custom_label_tenant_id = 10;
  string language = 11;
  string file_path = 12;
}

message RspImportTencentDoc{
  string uuid = 1;
}

// 重新导入腾讯文档(doc_id 知识库的tdoc.id)
message ReqReimportTencentDoc{
  repeated uint64 doc_ids = 1;
  uint32 admin_type = 6;
  uint64 account_id = 7;
  uint64 user_id = 8;
  uint64 custom_label_tenant_id = 9;
  string language = 10;
}

message RspReimportTencentDoc{
  message FailInfo{
    uint64 doc_id = 1;
    string file_name = 2;
    tanlive.ai.ExternalSourceUser user = 3;
  }
  repeated FailInfo failed = 2;
}

message ReqImportTencentDocWebClip{
  uint32 admin_type = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string encrypted_openid = 4;
  google.protobuf.Timestamp after_time = 5;
  uint64 custom_label_tenant_id = 6;
  string language = 7;
}

message RspImportTencentDocWebClip{
  repeated TencentDoc docs = 1;
}

message ReqModifyDocTab{
  uint64 id = 1;
  string name = 2;
  uint32 admin_type = 3;
  uint64 account_id = 4;
  uint32 type = 5;
}

message ReqDescribeDocTab{
  uint32 type = 1;
  uint32 admin_type = 3;
  uint64 account_id = 4;
  uint64 user_id = 5;
  uint64 team_id = 6;
}

message RspDescribeDocTab{
  message DocTab {
    uint64 id = 1;
    string name = 2;
    uint32 type = 3;
    bool is_show = 4;
    bool has_expired = 5;
  }
  repeated DocTab tabs = 1;
}

message ReqDescribeMyDoc{
  uint32 admin_type = 3;
  uint64 account_id = 4;
}

message RspDescribeMyDoc{
  repeated string file_ids = 1;
}

message ReqCreateNebulaTask{
  string lang = 2;
  uint64 user_id = 3;
  uint64 team_id = 4;
  repeated uint64 assistant_id = 5;
  repeated uint64 query_assistant_id = 6;
  string simplify_lang = 7;
  uint64 collection_id = 8;
  string filter_hash = 9;
  string filter_json = 10;
  bool as_team = 11;
  uint64 admin_type = 12;
  string filter_text = 13;
  int32 auto_create = 14;
  string load_embedding = 15;
  bool select_all = 16;
  string clustering_method = 17;
  repeated int32 min_samples_range = 18;
  repeated float eps_range = 19;
  repeated int32 n_clusters_range = 20;
}

message RspCreateNebulaTask{
  string uuid = 1;
}

message ReqDescribeNebulaTask{
  string uuid = 1;
  uint64 user_id = 3;
  uint64 team_id = 4;
  repeated uint64 assistant_id = 5;
  repeated uint64 query_assistant_id = 6;
  string lang = 7;
  bool as_team = 11;
}

message RspDescribeNebulaTask{
  string uuid = 1;
  uint32 state = 2;
  string create_date = 3;
  string end_date = 4;
  repeated string calcu_result = 5;
  string filter_text = 6;
  string cluster_list = 7;
  string connect_info = 8;
}

message ReqDescribeNebulaTaskList{
  uint64 user_id = 1;
  uint64 team_id = 2;
  base.Paginator page = 4;
  bool as_team = 11;
  repeated string uuid = 12;
}

message RspDescribeNebulaTaskList{
  message Task {
    string uuid = 1;
    uint32 state = 2;
    string create_date = 3;
    string end_date = 4;
    string lang = 5;
    string filter_text = 6;
    uint32 is_read = 7;
  }

  repeated Task tasks = 1;
  uint32 total = 2;
  uint32 unread_num = 3;
}

message ReqDescribeNebulaProjection{
  string uuid = 1;
  uint64 user_id = 2;
  uint64 team_id = 3;
  string query = 4;
}

message RspDescribeNebulaProjection{
  repeated double projection = 1;
}

message ReqDescribeNebulaData{
  repeated string content_hash = 1;
  uint64 id = 2;
  uint64 user_id = 3;
  uint64 team_id = 4;
}

message RspDescribeNebulaData{
  repeated string content = 1;
  string query_name = 2;
}

message ReqBatchCreateNebulaTasks{
  uint64 user_id = 1;
  uint64 team_id = 2;
  bool as_team = 3;
  uint64 admin_type = 4;
  repeated uint64 assistant_id = 5;
  repeated uint64 query_assistant_id = 6;
  string lang = 7;
  bool order_by = 8;
  string load_embedding = 9;
}

message RspBatchCreateNebulaTasks{}

message ReqCreateAssistantCollectionInit{
  uint64 user_id = 1;
  uint64 team_id = 2;
  bool as_team = 3;
  uint64 admin_type = 4;
  uint64 assistant_id = 5;
}

message RspCreateAssistantCollectionInit{}

message RspListCustomLabel{
  repeated CustomLabel labels = 1;
  uint32 total = 2;
}

message ReqUpsertCustomLabels{
  repeated CustomLabel labels = 1;
  uint64 user_id = 2;
  uint64 tenant_id = 3;
  ai.CustomLabelObjectType object_type = 4;
}

message RspUpsertCustomLabels{
  repeated uint64 ids = 1;
}

message ReqDeleteCustomLabels{
  repeated uint64 ids = 1;
}

message ReqListAssistant{
  repeated uint64 ids = 1;
  base.Paginator page = 2;
  repeated AssistantAdmin admins = 3;
  bool with_collection = 4;
  ChatType type = 5;
  base.OrderBy order = 6;
  // 搜索名称关键词
  string name = 7;
  // 语言
  string language = 8;
}

message RspListAssistant{
  repeated Assistant assistants = 1;
  uint32 total = 2;
}

message ReqGetAssistant {
  uint64 id = 1 ;
  string app_code = 2;
  string corp_id = 3;
  string language = 4;
}

message RspGetAssistant{
  tanlive.ai.AssistantDetail assistant_detail = 1;
}

message ReqGetAssistantInfoMap {
  repeated uint64 ids = 1;
}

message RspGetAssistantInfoMap {
  map<uint64, Info> info_map = 1;
  message Info {
    string name = 1;
    string name_en = 2;
    int64 chat_idle_duration = 3;
    google.protobuf.Timestamp create_date = 4;
    // 创建人
    base.Identity create_by = 5;
    // 渠道
    AssistantChannel channel = 6;
    // 是否启用
    bool enabled = 7;
    // 是否草稿
    bool is_draft = 8;
    uint64 id = 9;
  }
}

message ReqUpdateCustomChatLabels{
  repeated uint64 object_id = 1;
  repeated CustomLabel labels = 2;
  CustomLabelObjectType object_type = 3;
}

message ReqListChatLiveAgent {
  repeated uint64 assistantIds = 1;
  string live_agent_name = 2;
  string live_agent_full_name = 3;
  // 1:接待中,2:停止接待。
  ChatLiveAgentStatus status = 4;
  base.Paginator page = 5;
  repeated uint64 live_agent_ids = 6;
}

message RspListChatLiveAgent {
  repeated ChatLiveAgent chat_live_agents = 1;
}

message ReqSwitchChatLiveAgent {
  // 人工客服名称
  string live_agent_name = 1;
  // 会话id
  uint64 chat_id = 2;
  uint64 update_by = 3;
  ChatSupportType support_type = 4;
  bool update_default_answer_record = 5;
  uint32 default_answer_record = 6;
}

message ReqLiveAgentStatusChange {
  uint64 assistant_id = 1;
  string user_name = 2;
  ChatLiveAgentStatus status = 3;
}

message ReqRateAiAnswerWechat {
  uint64 chat_id = 1;
  uint64 answer_id = 2;
  RatingScale rating_scale = 3;
}

message ReqGetAnswerWechat {
  // 消息内容
  ChatMessage message = 1 [(validator) = "required"];
  // 问题的唯一标识
  string third_record_uuid = 2;
  // 用户id
  string external_user_id = 3 ;
  // 设置的会话固定回答
  string set_chat_answer = 4;
  // 当前问题游标
  string current_cursor = 5;
  // 下一个问题的游标
  string next_cursor = 6;
  // 超时默认回答
  string timeout_default_answer = 7;
  // 助手
  tanlive.ai.AssistantDetail assistant_detail = 8 [(validator) = "required"];
  // 文件处理命令
  AIFileProcessing file_cmd = 9;
  uint64 record_id = 10;
  repeated tanlive.ai.ChatMessageDoc docs = 11;
  DocMatchPattern match_pattern = 12;
  google.protobuf.Timestamp answer_start_time = 13;
  google.protobuf.Timestamp answer_end_time = 14;
  tanlive.ai.MessageCollectionSnapshot collection_snapshot = 15;
}

message RspGetAnswerWechat {
  // 答案
  repeated ChatMessage answers = 1;
  // 是否重复问题
  QuestionType question_type = 2;
  // 当前询问的问题的id
  uint64 question_id = 3;
  ChatSupportType support_type = 4;
  // 处理的图片问题的id（用户如果是先发送的图片然后给的非指令回复，此时并非每个图片都有一个回答，但是要从默认回复状态更新为已处理状态）
  repeated uint64 file_question_ids = 5;
  enum QuestionType {
    // 正常的数据
    QUESTION_TYPE_NORMAL = 0;
    // 重新回答
    QUESTION_TYPE_REPETITION = 1;
    // 问题ID重复，忽略
    QUESTION_TYPE_ID_REPETITIVE = 2;
    // 清除文件agent处理标识缓存
    QUESTION_TYPE_CLEAR_FILE_CACHE = 3;
  }
  repeated tanlive.ai.AiSendResultRecord answer_sharding = 6;
}

message ReqFinishChatWechat {
  // 微信external_user_id
  string external_user_id = 1;
  // 微信union_id
  //  string union_id = 1;
  // 助手id
  uint64 assistant_id = 2;
  ChatType chat_type = 3;
}

message ReqGetWhiteListWechat {
  uint64 assistant_id = 1;
}

message RspGetWhiteListWechat {
  repeated string username = 1;
}

message ReqUpdateChatMessageStateWechat {
  repeated Info infos = 1;
  // 判断是否推送人工提醒的次数记录
  uint32 default_answer_record = 2;
  uint64 chat_id = 3;
  // 是否正常回复
  bool normal_reply = 4;
  // 当前询问的主问题的id，可能会没有答案
  uint64 question_id = 5;
  // 处理的图片问题的id（用户如果是先发送的图片然后给的非指令回复，此时并非每个图片都有一个回答，但是要从默认回复状态更新为已处理状态）
  repeated uint64 file_question_ids = 6;
  message Info {
    uint64 question_id = 1;
    uint64 answer_id = 2;
    // 审核驳回原因
    string reject_reason = 3;
    // 每个答案的记录信息
    repeated AiSendResultRecord records = 4;
  }
}

message ReqGetMessageCursorWechat {
  uint64 assistant_id = 1;
}

message RspGetMessageCursorWechat {
  string cursor = 1;
}

message ReqGetChatWechat {
  uint64 assistant_id = 1;
  tanlive.ai.ChatType chat_type = 2;
  repeated string external_user_ids = 3;
  int64 chat_idle_duration = 4;
}

message RspGetChatWechat {
  // key:external_user_id ; value:chat
  map<string, ChatWeChat> chat_map = 1;
  // 存在人工坐席但是已经结束的会话 map key:external_user_id ; value: live_agent_name
  map<string, string> finished_chat_live_agent_map = 2;
}

message ReqCreateChatWechat {
  repeated ChatWeChat chats = 1;
  uint64 assistant_id = 2;
}

message RspCreateChatWechat {
  // key:external_user_id ; value:chat_id
  map<string, uint64> chat_map = 1;
}

message ReqGetLastQuestionStateWechat {
  uint64 chat_id = 1;
  uint64 question_id = 2;
}

message RspGetLastQuestionStateWechat {
  int32 state = 1;
}

message ReqUpdateChatMessageRecordWechat {
  uint64 chat_id = 1;
  uint64 question_id = 2;
  string answer_third_record_uuid = 3;
  string answer_text = 4;
  uint32 default_answer_record = 5;
  uint64 assistant_id = 6;
  string external_user_id = 7;
  AiRecordType send_record_type = 8;
}

message RspUpdateChatMessageRecordWechat {
  uint64 answer_id = 1;
  uint64 record_id = 2;
  // 是否追加继续回答
  bool continue_answering = 3;
}

message ReqCreateSendRecord {
  uint64 chat_id = 1;
  uint64 message_id = 2;
  repeated Piece pieces = 3;
  tanlive.ai.ChatType chat_type = 4;
  AiSendResultRecord whats_app_record = 5;
  message Piece {// 微信直接发text的消息限制为1024，发菜单消息的限制为2048，但是此时并不能确认是发菜单消息还是text消息，所以都先限制为1024，分为两条
    string one = 1;
    string two = 2;
    AiRecordType type = 3;
    tanlive.ai.AiRecordMessageType message_type = 4;
    // 图片地址，如果存在，则说明当前消息为图片消息，没有实际的文本，微信不允许图片和文字同时发送
    string url = 5;
    // 扩展信息，现阶段用于 在发送媒体消息之前，获取的mediaId
    string extra_id = 6;
    // 扩展信息，现阶段用于 在发送媒体消息之前，获取mediaId时的记录信息
    string extra_info = 7;
  }
  // 后续还有消息
  bool more_to_come = 6;
}

message RspCreateSendRecord {
  repeated RecordInfo records = 1;
  // 是否追加继续回答
  bool continue_answering = 2;
  message RecordInfo {
    uint64 record_id = 1;
    uint64 message_id = 2;
    AiRecordType type = 3;
    string content = 4;
    // 扩展信息，现阶段用于 在发送媒体消息之前，获取的mediaId
    string extra_id = 5;
  }
}

message ReqUpdateSendRecord {
  repeated AiSendResultRecord records = 1;
  // 更新用户撤回的消息
  string user_recall_msg_uuid = 2;
}

message ReqDescribeUserChatRecords {
  // chat_id
  uint64 chat_id = 1 [(validator) = "min=1"];
  base.Paginator page = 2;
  // 消息内容搜索关键词
  string keyword = 3;
  // 发送时间区间
  tanlive.base.TimeRange send_range = 4;
}

message RspDescribeUserChatRecords {
  repeated ChatSendRecordInfo records = 1;
  uint32 total_count = 2;
}

message ReqDescribeAssistantMessage {
  uint64 assistant_id = 1 [(validator) = "required"];
  base.Paginator page = 2;
  tanlive.ai.ChatType chat_type = 3;
}

message RspDescribeAssistantMessage {
  repeated tanlive.ai.ChatMessage message = 1;
}

message ReqInsertAssistantMessageRecord {
  repeated AiChatSendRecord records = 1;
}

message ReqMigrationChatMessageInfo {
  uint64 old = 1;
  uint64 new = 2;
}

message ReqDescribeChatRegionCode {
  repeated uint64 assistant_ids = 1;
}

message RspDescribeChatRegionCode {
  repeated string region_codes = 1;
}

message ReqDescribeFeedbackRegionCode {
}

message RspDescribeFeedbackRegionCode {
  repeated string region_codes = 1;
}

message ReqSyncFixChatMessageCollection{
  uint64 chat_id = 1;
}

message ReqDescribeChatLogAuthItem{
  uint64 assistant_id = 1;
}

message RspDescribeChatLogAuthItem{
  repeated string auth_fields = 1;
}

message ReqFetchHtmlTitles {
  repeated string urls = 1;
}

message RspFetchHtmlTitles {
  map<string, string> url_with_titles = 1;
}

message ReqCreateMessageSuggestQuestion{
  // 答案 id
  uint64 message_id = 1 [(validator) = "required"];
  string question_text = 2 [(validator) = "required"];
  string answer_text = 3 [(validator) = "required"];
  // 助手
  tanlive.ai.AssistantDetail assistant_detail = 4 [(validator) = "required"];
  tanlive.ai.AiRecordType record_type = 5;
  string record_info = 6;
  uint64 chat_id = 7;
  uint64 create_by = 8;
}

message RspCreateMessageSuggestQuestion {
  repeated string suggest_questions = 1;
  uint64 record_id = 2;
  // 是否追加继续回答
  bool continue_answering = 3;
}

message ReqCreateExportTask{
  uint64 user_id = 1;
  tanlive.ai.ExportTaskType type = 2;
  tanlive.ai.TaskOperationType operation_type = 3;
  string fields_snapshot = 4;
  string filter_snapshot = 5;
}

message RspCreateExportTask{
  tanlive.ai.ExportTask task = 1;
}

message ReqUpdateExportTask{
  uint64 id = 1;
  tanlive.ai.ExportTaskState state = 2;
  string url = 3;
  string extra_info = 4;
  repeated string paths = 5;
  string filter_snapshot = 6;
  string fields_snapshot = 7;
}

message ReqDescribeExportTasks{
  uint64 user_id = 1;
  repeated tanlive.ai.ExportTaskType type = 2;
  tanlive.ai.TaskOperationType operation_type = 3;
  repeated uint64 ids = 4;
}

message RspDescribeExportTasks{
  repeated tanlive.ai.ExportTask tasks = 1;
}

message ReqConvertCustomLabel{
  uint64 id = 1;
  CustomLabel target = 2;
  bool dry_run = 3;
}

message RspConvertCustomLabel{
  uint32 reserved = 1;
  uint32 deleted = 2;
}

message ReqGetCustomLabelValueTopN{
  int32 top_n = 1;
  uint64 id = 2;
}

message RspGetCustomLabelValueTopN{
  repeated LabelValue values = 1;
}

message ReqDescribeExportChatMessages {
  message ExportChat {
    string assistant_name = 1;
    string user_name = 2;
    string region_code = 3;
    uint64 id = 4;
  }
  repeated ExportChat export_chats = 1;
}

message RspDescribeExportChatMessages {
  repeated ExportMessage export_messages = 1;
}

message ReqGetAssistants {
  // 可见场景
  enum VisibleScene {
    VISIBLE_SCENE_UNSPECIFIED = 0;
    // 在用户后台列表
    VISIBLE_SCENE_IN_CONSOLE_LIST = 1;
    // 在WEB中
    VISIBLE_SCENE_IN_WEB = 2;
    // 在用户后台详情
    VISIBLE_SCENE_IN_CONSOLE_DETAIL = 3;
    // 在知识库管理
    VISIBLE_SCENE_IN_COLLECTION_MANAGE = 4;
    // 在知识星云，知识星云会有特殊排序逻辑
    // 排序逻辑：AI助手选择collection最后更新过的助手，如果有多个助手的collection最后更新时间一致，则选C端最后对话过的助手。
    VISIBLE_SCENE_IN_NEBULA = 5;
  }
  message Filter {
    // 所属用户ID
    repeated uint64 user_id = 1;
    // 所属团队ID
    repeated uint64 team_id = 2;
    // 创建时间范围
    base.TimeRange create_date = 3;
    // 渠道
    repeated AssistantChannel channel = 4;
    // 是否启用
    base.BoolEnum enabled = 5;
    // 知识库语言
    repeated string collection_lang = 6;
    // 是否确认协议
    base.BoolEnum terms_confirmed = 7;
    // 助手ID
    repeated uint64 assistant_id = 8;
    // 路由
    string route_path = 9;
    // 助手名称
    string assistant_name = 10;
    // 模型
    repeated string model = 11;
    // 搜索引擎
    repeated string search_engine = 12;
    // 是否为草稿
    base.BoolEnum is_draft = 13;
    // 应用ID
    string app_id = 14;
    // 批次号
    string batch_no = 15;
    // 文档ID
    uint64 doc_id = 16;
  }
  message Relation {
    // 归属
    bool admin = 1;
    // 知识库
    bool collection = 2;
    // 人工坐席
    bool live_agent = 3;
    // 协议确认
    bool terms_confirmation = 4;
    // 白名单
    bool allowlist = 5;
    // agent 流程
    bool pipelines = 6;
  }
  // 过滤器
  Filter filter = 1;
  // 排序
  repeated base.OrderBy order_by = 2;
  // 关系
  Relation relation = 3;
  // 分页
  base.Paginator page = 4;
  // 总数
  bool with_total_count = 5;
  // 可见场景（不同的场景可见字段不同）
  VisibleScene visible_scene = 9;
}

message RspGetAssistants {
  // 完整助手信息
  repeated FullAssistant assistants = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqBatchCreateAssistant {
  // 配置列表
  repeated AssistantConfig configs = 1 [(validator) = "required"];
  // 创建人
  base.Identity create_by = 2 [(validator) = "required"];
  // 是否保存为草稿
  bool is_draft = 3;
}

message RspBatchCreateAssistant {
  // 批次号
  string batch_no = 1;
  // 助手ID列表
  repeated uint64 assistant_id = 2;
}

message ReqBatchUpdateAssistant {
  message Item {
    // 助手ID
    uint64 assistant_id = 1;
    // 配置详情
    AssistantConfig config = 2 [(validator) = "required"];
    // Assistant mask
    google.protobuf.FieldMask mask = 3 [(validator) = "required"];
    // 修改前的用户标签
    AssistantUserLabelConfig old_user_label_config = 42;
  }
  // 助手列表
  repeated Item items = 1 [(validator) = "required,dive,required"];
  // 更新人
  base.Identity update_by = 2 [(validator) = "required"];
  // 是否保存为草稿（已发布的助手忽略该参数）
  bool is_draft = 3;
  // 批次号
  // 如果指定了批次号，items里的助手必须都属于该批次号，且批次号内的助手必须为草稿，允许新增、删除；未指定批次号时items里的助手必须为非草稿，仅允许更新
  string batch_no = 4;
}

message ReqDeleteAssistant {
  message AssistantId {
    // 助手ID
    repeated uint64 assistant_id = 1 [(validator) = "required,dive,required"];
  }
  // 更新人
  base.Identity update_by = 1 [(validator) = "required"];
  // 删除条件
  oneof condition {
    // 通过助手ID
    AssistantId by_assistant_id = 2 [(validator) = "required"];
    // 通过批次号
    string by_batch_no = 3 [(validator) = "required"];
  }
}

message ReqGetAssistantLogs {
  // 过滤器
  message Filter {
    // 日志ID
    repeated uint64 id = 1 [(validator) = "omitempty,dive,required"];
    // 助手ID
    repeated uint64 assistant_id = 2 [(validator) = "omitempty,dive,required"];
    // 操作类型
    repeated AssistantAction action = 3 [(validator) = "omitempty,dive,required"];
    // 不查询管理员的日志
    bool except_mgmt = 4;
    // 操作时间范围
    base.TimeRange create_date = 5;
  }
  // 过滤器
  Filter filter = 1;
  // 分页
  base.Paginator page = 2;
  // 总数
  bool with_total_count = 3;
}

message RspGetAssistantLogs {
  // 日志列表
  repeated AssistantLog logs = 1;
  // 总数
  uint32 total_count = 2;
}

message RspGetAssistantOptions {
  // 对话模型
  repeated string chat_model = 1;
  // ChatOrSql模型
  repeated string chat_or_sql_model = 2;
  // 解析图谱模型
  repeated string graph_parse_mode = 3;
  // 搜索引擎
  repeated string search_engine = 4;
  // 互动暗号
  repeated InteractiveCodeOption interactive_code = 5;
  // 链路查询
  repeated VisibleChainOption visible_chain = 6;
  // 问题建议模型
  repeated string ask_suggestion_model = 7;
  // 向量化模型
  repeated EmbeddingModelOption embedding_model = 8;
  // 对话模型v2
  repeated ChatModelOption chat_model_v2 = 9;
  // 搜索引擎v2
  repeated SearchEngineOption search_engine_v2 = 10;
  // 快捷命令
  repeated QuickAction quick_actions = 11;
  // 小程序URL白名单
  repeated string mini_white_url = 12;
}

message ReqCheckAssistantAllowlist {
  // 手机白名单参数
  message PhonePara {
    // 手机号
    string phone = 1 [(validator) = "phone"];
  }
  // 助手ID
  uint64 assistant_id = 1 [(validator) = "required"];
  // 类型参数
  oneof type_para {
    // 手机类型参数
    PhonePara phone_type_para = 2;
  }
}

message RspCheckAssistantAllowlist {
  // 是否允许
  bool allowed = 1;
}

message ReqGetDocChunks {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 助手ID
  repeated uint64 assistant_id = 2;
  // 所属管理员
  base.Identity admin = 3;
}

message RspGetDocChunks {
  // 助手的分段列表
  repeated AssistantChunks assistant_chunks = 1;
}

message ReqChunkDoc {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 新文本（如果文本未改动不需要传值）
  string new_text = 2;
  // 创建人
  base.Identity create_by = 3;
  // 分段参数
  oneof chunk_para {
    // 自动分段参数
    AutoChunkPara auto_para = 4 [(validator) = "required"];
    // 手动分段参数
    ManualChunkPara manual_para = 5 [(validator) = "required"];
  }
}

message RspChunkDoc {
  // 分段列表
  repeated ChunkItem chunks = 1;
  // 任务ID
  uint64 task_id = 2;
}

message ReqGetChunkDocTasks {
  // 文档ID
  repeated uint64 doc_id = 1 [(validator) = "required,dive,required"];
}

message RspGetChunkDocTasks {
  // 任务列表
  repeated DocChunkTask tasks = 1;
}

message ReqGetDocEmbeddingModels {
  // 文档ID
  uint64 doc_id = 1 [(validator) = "required"];
  // 所属用户ID
  repeated uint64 admin_user_id = 2;
  // 所属团队ID
  repeated uint64 admin_team_id = 3;
}

message RspGetDocEmbeddingModels {
  // 向量化模型列表
  repeated EmbeddingModelCount embedding_models = 1;
}

message ReqResendMessageSync {
  uint64 question_id = 1 [(validator) = "required"];
  tanlive.ai.AssistantDetail assistant_detail = 2 [(validator) = "required"];
  bool clear_answer = 3;
  string publish_hash_id = 4;
}

message RspResendMessageSync {
  repeated tanlive.ai.ChatMessage message = 1;
}

message ReqUpdateDocAttrInBulk{
  repeated uint64 id = 1 [(validator) = "required"];
  google.protobuf.FieldMask mask = 2 [(validator) = "required"];
  // 贡献者
  repeated Contributor contributor = 4;
  // 是否显示贡献者
  uint32 show_contributor = 5;
  // 参考资料下载方式
  ai.DocFileDownloadAsRef download_as_ref = 6;

  // 关联的助手，scoped_assistant_id 限定在这个范围内修改
  repeated uint64 scoped_assistant_id = 3;
  repeated uint64 assistant_id = 7;

  Operator update_by = 8;

  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 9;
  // 参考资料
  repeated tanlive.ai.DocReference reference = 10;
  // 自定义列
  repeated CustomLabel labels = 11;
  uint64 query_id = 12;
}

message RspUpdateDocAttrInBulk{
  bool async = 1;
}

message ReqDescribeMessageMatchQa {
  string text = 1;
  uint64 assistant_id = 2;
  // 分页
  base.Paginator page = 3;
  // 用来筛选的匹配模式
  MessageMatchQaFilter filter = 4;
  // 指定匹配模式
  DocMatchPattern match_pattern = 5;
}

message RspDescribeMessageMatchQa {
  repeated tanlive.ai.ChatMessageDoc docs = 1;
  DocMatchPattern match_pattern = 2;
  // 总数
  uint32 total_count = 3;
}


message ReqCreateQaMatchMessage {
  tanlive.ai.ChatMessage question = 1;
  repeated tanlive.ai.ChatMessageDoc docs = 2 [(validator) = "required"];
  DocMatchPattern match_pattern = 3;
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
  MessageCollectionSnapshot collection_snapshot = 6;
  ChatMessageTask task = 7;
}

message RspCreateQaMatchMessage{
  tanlive.ai.ChatMessage message = 1;
}

message ReqUpdateMessageText{
  uint64 id = 1;
  string text = 2;
  string think = 3;
}

message ReqGetRecentlyUsedAssistantIds {
  uint64 user_id = 1 [(validator) = "required"];
  ChatType chat_type = 2 [(validator) = "required"];
}

message RspGetRecentlyUsedAssistantIds {
  repeated uint64 ids = 1;
}

message ReqGetRecentlyUsedAssistants {
  // 最近使用过的id
  repeated uint64 use_assistant_ids = 1;
  // 分页
  base.Paginator page = 2;
  // 是否展示在助手列表（为true时则是在查询小程序的助手列表，其他情况时则是在查询小程序的导航列表）
  base.BoolEnum show_in_list = 3;
  // 是否只查询最近使用过的助手
  bool only_recently_use = 4;
  // 搜索助手昵称的中英文
  string search = 5;
  // 所属用户ID
  repeated uint64 user_ids = 6;
  // 所属团队ID
  repeated uint64 team_ids = 7;
  AssistantChannel channel = 8;
}

message RspGetRecentlyUsedAssistants {
  // 完整助手信息
  repeated FullAssistant assistants = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqGetAssistantAdmin {
  base.Paginator page = 1;
  repeated uint64 assistant_ids = 2;
}

message RspGetAssistantAdmin {
  message Info {
    // 助手管理员
    repeated base.Identity admins = 1;
    uint64 assistant_id = 2;
  }
  repeated Info admins = 1;
  // 总数
  uint32 total_count = 2;
}

message ReqGetAssistantChatUser {
  repeated uint64 assistant_ids = 1;
}

message RspGetAssistantChatUser {
  message Info {
    // 用户id
    repeated uint64 user_id = 1;
    uint64 assistant_id = 2;
  }
  repeated Info userInfo = 1;
}

message ReqGetTextFileTip{
  uint64 id = 1;
  // 贡献者，与管理的助手是或者关系，用于限定重复的文本文件的查询范围
  Contributor contributor = 2;
  // 管理的助手
  repeated uint64 assistant_id = 3;
}

message RspGetTextFileTip{
  // 表头过长的表格
  repeated TextFileTipTableOverSize table_over_size = 1;
  // 解析状态
  DocState state = 2;
  // 内容重复信息
  repeated string repeated = 3;
}

message ReqCreateDocQuery {
  ReqListTextFile doc = 1;
  ReqListQA qa = 2;
  ai.Operator create_by = 3;
}

message RspCreateDocQuery {
  uint64 query_id = 1;
  bool is_empty = 2;
  uint32 total_count = 3;
}

// QA提示请求
message ReqGetQaTip{
  uint64 id = 1;
  // 贡献者，与管理的助手是或者关系，用于限定重复的QA的查询范围
  Contributor contributor = 2;
  // 管理的助手
  repeated uint64 assistant_id = 3;
}

// QA提示响应
message RspGetQaTip{
  // 问题超长提示
  bool question_over_size = 1;
  // 内容重复信息
  repeated string repeated = 2;
}

message ReqParseChatDoc{
  repeated ChatMessageFile files = 1;
  uint64 message_id = 2;
}

message RspParseChatDoc {
  repeated uint64 ids = 1;
}

message ReqCreateChatOperation {
  uint64 question_id = 1;
  string hash_id = 2 [(validator) = "required"];
  ChatOperationType operation_type = 3;
  int32 stop_chunk_state = 4;
  string stop_text = 5;
  string stop_think = 6;
  uint64 message_id = 7 [(validator) = "required"];
  uint64 chat_id = 8 [(validator) = "required"];
}

message ReqDescribeChatQuestionAnswersByPage{
  uint64 chat_id = 1 [(tanlive.validator) = "required"];
  uint64 user_id = 2;
  uint32 offset = 3;
  uint32 limit = 4;
  bool is_reverse = 5;
  uint64 question_id = 6;
}

message RspDescribeChatQuestionAnswersByPage {
  repeated ChatMessage questions = 1;
  repeated ChatMessage answers = 2;
  uint32 total_count = 3;
}

message ReqCheckChatPermission{
  uint64 chat_id = 1;
  uint64 user_id = 2;
}

message ReqCreateChatTaskMessage{
  // 助手
  tanlive.ai.AssistantDetail assistant_detail = 1 [(validator) = "required"];
  tanlive.ai.ChatMessage message = 2;
  // 携带的文件文本
  repeated string file_texts = 3;
  // 携带的agent tasks
  repeated tanlive.ai.ChatAgentTask tasks = 4;
}

message RspCreateChatTaskMessage{
  repeated tanlive.ai.ChatMessage messages = 1;
}

message ReqDescribeChatAgentTask {
  uint64 assistant_id = 1;
  string prompt = 2;
  bool no_next_order_task = 3;
  bool no_next_task = 4;
}

message RspDescribeChatAgentTask {
  repeated tanlive.ai.ChatAgentTask tasks = 1;
}

message ReqFixSearchCollectionItems{
  repeated tanlive.ai.SearchCollectionItem items = 1;
}

message RspFixSearchCollectionItems{
  repeated tanlive.ai.SearchCollectionItem items = 1;
}

message ReqUpdateChatMessageCollections{
  google.protobuf.Timestamp start_time = 1 [(validator) = "required"];
  google.protobuf.Timestamp end_time = 2;
  repeated SearchCollectionItem items = 3;
  bool clean_chunks = 4;
  uint64 message_id = 5;
}

message ReqDescribeChatAgentTaskByPreTask {
  uint64 pre_task_id = 1;
}

message RspDescribeChatAgentTaskByPreTask {
  repeated tanlive.ai.ChatAgentTask tasks = 1;
}

// 请求消息
message ReqDescribeChatMessageFileState {
  uint64 message_id = 1 [(validator) = "required"];
}

// 响应消息
message RspDescribeChatMessageFileState {
  repeated ChatMessageFile files = 1;  // 文件信息列表
}

message ReqDescribePreMessageTask {
  uint64 message_id = 1;
  uint64 chat_id = 2;
}

message RspReqDescribePreMessageTask {
  uint64 task_id = 1;
}

message ReqStopQuestionReply {
  uint64 message_id = 1 [(validator) = "required"];
}

message RspStopQuestionReply {
  ChatMessage message = 1;
}

// 获取分享详情请求
message ReqGetChatShareMessages {
  uint64 chat_id = 1;
  repeated uint64 message_ids = 2;
}

// 获取分享详情响应
message RspGetChatShareMessages {
  repeated ChatMessage messages = 11;
}

message ReqGetChatShareRecord{
  // 分享ID
  string share_id = 1 [(validator) = "required"];
  // 是否记录访问
  bool record_access = 2;
  // 访问者ID
  uint64 user_id = 3;
}

message RspGetChatShareRecord {
  // chat id
  uint64 chat_id = 1;
  // message ids
  repeated uint64 message_ids = 2;
  // 分享类型
  ShareType share_type = 3;
  // 助手ID
  uint64 assistant_id = 4;
  // 分享状态
  ShareStatus share_status = 6;
  // 分享者ID
  uint64 shared_by = 7;
  // 访问次数
  int32 access_count = 8;
  // 分享创建时间
  google.protobuf.Timestamp share_date = 9;
  // 分享过期时间
  google.protobuf.Timestamp expire_date = 10;
  // 最后访问时间
  google.protobuf.Timestamp last_access_time = 11;
}

// 创建聊天分享请求
message ReqCreateChatShare {
  // 会话ID
  uint64 chat_id = 1 [(validator) = "required"];
  // 消息ID列表
  repeated uint64 message_ids = 2 [(validator) = "required,min=1"];
  // 用户ID
  uint64 user_id = 3 [(validator) = "required"];
  // 分享类型
  ShareType share_type = 4;
  // 过期天数（0表示永久有效）
  int32 expire_days = 5;
  uint64 assistant_id = 6;
}

// 创建聊天分享响应
message RspCreateChatShare {
  // 分享ID
  string share_id = 1;
}

// 从分享继续聊天请求
message ReqContinueChatFromShare {
  repeated ChatMessage messages = 1 [(validator) = "required"];
  uint64 user_id = 2 [(validator) = "required"];
  string share_id = 4 [(validator) = "required"];

  ChatType chat_type = 5;
  uint64 assistant_id = 6 [(validator) = "required"];
  string region_code = 7 [(validator) = "required"];
}

message RspContinueChatFromShare {
  uint64 chat_id = 1; // 新会话ID
  string title = 2; // 会话标题
}

// 获取用户分享列表请求
message ReqListChatShares {
  // 用户ID
  uint64 user_id = 1;
  // 会话ID
  uint64 chat_id = 2;
  // 分享状态
  ShareStatus share_status = 3;
  // 分页偏移
  uint32 offset = 4;
  // 分页限制
  uint32 limit = 5;
}

// 获取用户分享列表响应
message RspListChatShares {
  // 分享列表
  repeated ChatShare shares = 1;
  // 总数
  uint32 total_count = 2;
}

// 更新分享状态请求
message ReqUpdateChatShareStatus {
  // 分享ID
  string share_id = 1 [(validator) = "required"];
  // 用户ID
  uint64 user_id = 2;
  // 新状态
  ShareStatus status = 3 [(validator) = "required"];
}

// 获取分享访问记录请求
message ReqListChatShareAccesses {
  // 分享ID
  string share_id = 1 [(validator) = "required"];
  // 用户ID
  uint64 user_id = 2;
  // 分页偏移
  uint32 offset = 3;
  // 分页限制
  uint32 limit = 4;
}

// 获取分享访问记录响应
message RspListChatShareAccesses {
  // 访问记录列表
  repeated ChatShareAccess accesses = 1;
  // 总数
  uint32 total_count = 2;
}

// 记录分享访问请求
message ReqRecordChatShareAccess {
  // 分享ID
  string share_id = 1 [(validator) = "required"];
  // 访问者用户ID
  uint64 user_id = 2;
  // 是否继续聊天
  bool is_continued = 3;
  // 新会话ID（如果继续聊天）
  uint64 new_chat_id = 4;
}

// 记录分享访问响应
message RspRecordChatShareAccess {
  // 访问记录ID
  uint64 access_id = 1;
}

// AI服务
service AiService {
  // 停止消息发送
  rpc StopAnswerReply(ReqStopAnswerReply) returns (RspStopAnswerReply);
  // 停止消息发送
  rpc StopQuestionReply(ReqStopQuestionReply) returns (RspStopQuestionReply);
  // 查询前置消息
  rpc DescribePreMessageTask(ReqDescribePreMessageTask) returns (RspReqDescribePreMessageTask);
  // 查询文件解析状态
  rpc DescribeChatMessageFileState(ReqDescribeChatMessageFileState) returns (RspDescribeChatMessageFileState) {}
  // 查询当前chat需要执行的tasks
  rpc DescribeChatAgentTaskByPreTask(ReqDescribeChatAgentTaskByPreTask) returns (RspDescribeChatAgentTaskByPreTask);
  // 更新message collections
  rpc UpdateChatMessageCollections(ReqUpdateChatMessageCollections) returns (google.protobuf.Empty);
  // 修复search collection items
  rpc FixSearchCollectionItems(ReqFixSearchCollectionItems) returns (RspFixSearchCollectionItems);
  // 查询agent任务
  rpc DescribeChatAgentTask(ReqDescribeChatAgentTask) returns (RspDescribeChatAgentTask);
  // 检查会话权限
  rpc CheckChatPermission(ReqCheckChatPermission) returns (google.protobuf.Empty);
  // 获取会话聊天记录
  rpc DescribeChatQuestionAnswersByPage(ReqDescribeChatQuestionAnswersByPage) returns (RspDescribeChatQuestionAnswersByPage);
  // 创建更新
  rpc CreateChatOperation(ReqCreateChatOperation) returns (google.protobuf.Empty);
  // 将对话中的文件转换成文本
  rpc ParseChatDoc(ReqParseChatDoc) returns (RspParseChatDoc);
  // 更新message 文本
  rpc UpdateMessageText(ReqUpdateMessageText) returns (google.protobuf.Empty);
  // 创建QA匹配模式命中回答
  rpc CreateQaMatchMessage(ReqCreateQaMatchMessage) returns (RspCreateQaMatchMessage);
  // 查询qa模式匹配
  rpc DescribeMessageMatchQa(ReqDescribeMessageMatchQa) returns (RspDescribeMessageMatchQa);
  // 重新发送
  rpc ResendMessageSync(ReqResendMessageSync) returns (RspResendMessageSync);
  // 获取导出消息体
  rpc DescribeExportChatMessages(ReqDescribeExportChatMessages) returns (RspDescribeExportChatMessages);
  // 创建消息建议问题
  rpc CreateMessageSuggestQuestion(ReqCreateMessageSuggestQuestion) returns (RspCreateMessageSuggestQuestion);
  // 查询导出任务列表
  rpc DescribeExportTasks(ReqDescribeExportTasks) returns (RspDescribeExportTasks);
  // 创建导出任务
  rpc CreateExportTask(ReqCreateExportTask) returns (RspCreateExportTask);
  // 更新导出任务
  rpc UpdateExportTask(ReqUpdateExportTask) returns (google.protobuf.Empty);
  // 获取QA列表
  rpc ListQA(ReqListQA) returns (RspListQA);
  // 批量创建QA
  rpc CreateQAInBulk(ReqCreateQAInBulk) returns (RspCreateQAInBulk);
  // 创建QA
  rpc CreateQA(ReqCreateQA) returns (RspCreateQA);
  // 更新QA
  rpc UpdateQA(ReqUpdateQA) returns (google.protobuf.Empty);
  // 批量更新QA
  rpc UpdateQAInBulk(ReqUpdateQAInBulk) returns (google.protobuf.Empty);
  // 获取文本/文件列表
  rpc ListTextFile(ReqListTextFile) returns (RspListTextFile);
  // 创建文本/文科
  rpc CreateTextFileInBulk(ReqCreateTextFileInBulk) returns (RspCreateTextFileInBulk);
  // 更新文本/文件
  rpc UpdateTextFile(ReqUpdateTextFile) returns (google.protobuf.Empty);
  // 批量更新文本/文件
  rpc UpdateTextFileInBulk(ReqUpdateTextFileInBulk) returns (google.protobuf.Empty);
  // 删除QA/文本/文件
  rpc DeleteDocInBulk(ReqDeleteDocInBulk) returns (RspDeleteDocInBulk);
  // 向量搜索带命中
  rpc SearchCollectionOneShot(ReqSearchCollectionOneShot) returns (RspSearchCollectionOneShot);
  // 重新解析doc文件
  rpc ReparseTextFiles(ReqReparseTextFiles) returns (RspReparseTextFiles);
  // 向量搜索
  rpc SearchCollection(ReqSearchCollection) returns (RspSearchCollection);
  // deprecated 获取collection列表
  rpc ListCollection(ReqListCollection) returns (RspListCollection);
  // 获取贡献者列表
  rpc ListContributor(ReqListContributor) returns (RspListContributor);
  // 获取最近更新人列表
  rpc ListUpdateBy(ReqListUpdateBy) returns (RspListUpdateBy);
  // 获取最近创建人人列表
  rpc ListCreateBy(ReqListUpdateBy) returns (RspListUpdateBy);
  // 获取已分享的助手列表，用于表头筛选
  rpc ListSharedAssistant(ReqListSharedAssistant) returns (RspListSharedAssistant);
  // 校验QA
  rpc ValidateQAInBulk(ReqValidateQAInBulk)returns (RspValidateQAInBulk);
  // 校验文本/文件
  rpc ValidateTextFileInBulk(ReqValidateTextFileInBulk)returns (RspValidateTextFileInBulk);
  // 克隆QA/文本/文件
  rpc CloneDocInBulk(ReqCloneDocInBulk) returns (RspCloneDocInBulk);
  // 启用/禁用doc
  rpc OnOffDocInBulk(ReqOnOffDocInBulk) returns (RspOnOffDocInBulk);
  // 根据文档ref_id查询文档
  rpc ListDocByRef(ReqListDocByRef) returns (RspListDocByRef);
  // 更新doc标签
  rpc UpdateDocLabels(ReqUpdateCustomChatLabels) returns (google.protobuf.Empty);
  // 转换标签
  rpc ConvertCustomLabel(ReqConvertCustomLabel) returns (RspConvertCustomLabel);
  // 获取标签topn值
  rpc GetCustomLabelValueTopN(ReqGetCustomLabelValueTopN) returns (RspGetCustomLabelValueTopN);
  // 批量更新doc的特定字段值
  rpc UpdateDocAttrInBulk(ReqUpdateDocAttrInBulk) returns (RspUpdateDocAttrInBulk);
  // 查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
  rpc GetTextFileTip(ReqGetTextFileTip) returns (RspGetTextFileTip);
  // 创建doc查询
  rpc CreateDocQuery(ReqCreateDocQuery) returns (RspCreateDocQuery);

  // 查询QA的知识提示（问题超长，内容重复）等信息
  rpc GetQaTip(ReqGetQaTip) returns (RspGetQaTip);

  // 创建全部文档md5
  rpc CreateAllDocMd5(ReqCreateAllDocMd5) returns (google.protobuf.Empty);
  //  创建助手发送方设置
  rpc CreateDocShareConfigSender(ReqCreateDocShareConfigSender) returns (google.protobuf.Empty);
  //  查看创建知识库接收者设置
  rpc ListeDocShareConfigSender(ReqListeDocShareConfigSender) returns (RspListeDocShareConfigSender);
  //  查询可分享的助手列表
  rpc ListAssistantCanShareDoc(ReqListAssistantCanShareDoc) returns (RspListAssistantCanShareDoc);
  // 查询可分享的团队列表
  rpc ListTeamCanShareDoc(ReqListTeamCanShareDoc) returns (RspListTeamCanShareDoc);
  // 查询可分享的用户列表
  rpc ListUserCanShareDoc(ReqListUserCanShareDoc) returns (RspListUserCanShareDoc);
  // 查询已分享的团队列表
  rpc ListSharedTeam(ReqListSharedTeam) returns (RspListSharedTeam);
  // 查询已分享的用户列表
  rpc ListSharedUser(ReqListSharedUser) returns (RspListSharedUser);
  // 查询我设置的助手
  rpc ListMyAssistantIds(ReqListMyAssistantIds) returns (RspListMyAssistantIds);
  // 创建助手接收方设置
  rpc CreateDocShareConfigReceiverAssistant(ReqCreateDocShareConfigReceiverAssistant) returns (google.protobuf.Empty);
  // 创建个人/团队接收方设置
  rpc CreateDocShareConfigReceiverUserTeam(ReqCreateDocShareConfigReceiverUserTeam) returns (google.protobuf.Empty);
  // 查询助手接收方设置
  rpc ListDocShareConfigReceiverAssistant(ReqListDocShareConfigReceiverAssistant) returns (RspListDocShareConfigReceiverAssistant);
  // 查询个人/团队接收方设置
  rpc ListDocShareConfigReceiverUserTeam(ReqListDocShareConfigReceiverUserTeam) returns (RspListDocShareConfigReceiverUserTeam);
  // 创建知识库分享至助手、个人、团队
  rpc CreateDocShare(ReqCreateDocShare) returns (RspCreateDocShare);
  // 创建知识库同步or取消至助手（已废弃，请使用CreateDocShare）
  rpc CreateDocShareAssistant(ReqCreateDocShare) returns (RspCreateDocShare);
  // 创建企微服务商租户
  rpc CreateOpenWechat(ReqCreateOpenWechat) returns (google.protobuf.Empty);
  // 查询企微服务商租户
  rpc DescribeOpenWechat(ReqDescribeOpenWechat) returns (RspDescribeOpenWechat);

  // 腾讯文档授权code处理
  rpc AuthTencentCode(ReqAuthTencentCode) returns (RspAuthTencentCode);
  // 查询腾讯文档缓存是否为空
  rpc DescribeTokenIsEmpty(ReqDescribeTokenIsEmpty) returns (RspDescribeTokenIsEmpty);
  // 查询腾讯文档外部用户列表
  rpc ListExternalSourceUser(ReqListExternalSourceUser) returns (RspListExternalSourceUser);
  // 批量导入绿技行
  rpc CreateGTBText(ReqCreateGTBText) returns (RspCreateGTBText);
  // 查询腾讯文档任务状态
  rpc DescribeTencentDocTask (ReqDescribeTencentDocTask) returns (RspDescribeTencentDocTask);
  // 删除腾讯文档授权
  rpc DelTencentDocAuth (ReqDelTencentDocAuth) returns (google.protobuf.Empty);
  // 查询绿技行状态
  rpc DescribeGTBText(ReqDescribeGTBText) returns (RspDescribeGTBText);
  // 创建腾讯文档AuthURL
  rpc CreateTencentDocAuthUrl(ReqCreateTencentDocAuthUrl) returns (RspCreateTencentDocAuthUrl);
  // 查询腾讯文档列表
  rpc DescribeDocList(ReqDescribeTencentDocList) returns (RspDescribeTencentDocList);
  // 腾讯文档导入
  rpc ImportTencentDoc(ReqImportTencentDoc) returns (RspImportTencentDoc);
  // 重新导入腾讯文档
  rpc ReimportTencentDoc(ReqReimportTencentDoc) returns (RspReimportTencentDoc);
  // 导入腾讯文档网页剪存文档
  rpc ImportTencentDocWebClip(ReqImportTencentDocWebClip) returns (RspImportTencentDocWebClip);
  // 知识库文档标签修改
  rpc ModifyDocTab(ReqModifyDocTab) returns (google.protobuf.Empty);
  // 查询知识库文档标签
  rpc DescribeDocTab(ReqDescribeDocTab) returns (RspDescribeDocTab);
  // 查询我的知识库文档
  rpc DescribeMyDoc(ReqDescribeMyDoc) returns (RspDescribeMyDoc);

  // 创建星云任务
  rpc CreateNebulaTask(ReqCreateNebulaTask) returns (RspCreateNebulaTask);
  // 查询星云任务
  rpc DescribeNebulaTask(ReqDescribeNebulaTask) returns (RspDescribeNebulaTask);
  // 查询星云任务列表
  rpc DescribeNebulaTaskList(ReqDescribeNebulaTaskList) returns (RspDescribeNebulaTaskList);
  //  查看投影坐标
  rpc DescribeNebulaProjection(ReqDescribeNebulaProjection) returns (RspDescribeNebulaProjection);
  //  查看投影元数据
  rpc DescribeNebulaData(ReqDescribeNebulaData) returns (RspDescribeNebulaData);
  // 批量创建星云任务，自动化任务
  rpc BatchCreateNebulaTasks (ReqBatchCreateNebulaTasks) returns (RspBatchCreateNebulaTasks);
  // 创建助手已有collection数据初始化处理
  rpc CreateAssistantCollectionInit(ReqCreateAssistantCollectionInit) returns (RspCreateAssistantCollectionInit);

  // 创建会话消息
  rpc CreateChatMessage(ReqCreateChatMessage) returns (RspCreateChatMessage);
  // 更新会话消息
  rpc UpdateChatMessageThink(ReqUpdateChatMessageThink) returns (google.protobuf.Empty);
  // 更新会话消息
  rpc UpdateChatMessage(ReqUpdateChatMessage) returns (google.protobuf.Empty);
  // 推送消息
  rpc PublishChatMessage(ReqPublishChatMessage) returns (google.protobuf.Empty);
  // 创建AI Agent任务
  rpc CreateChatTaskMessage(ReqCreateChatTaskMessage) returns (RspCreateChatTaskMessage);
  // 发送请求AI消息同步
  rpc SendMessageWithoutSaveSync(ReqSendMessageWithoutSaveSync) returns (RspSendMessageWithoutSaveSync);
  // 获取消息
  rpc DescribeMessage(ReqDescribeMessage) returns (RspDescribeMessage);
  // 获取答案
  rpc DescribeMessageByQuestionId(ReqDescribeMessageByQuestionId) returns (RspDescribeMessageByQuestionId);
  // 创建会话
  rpc CreateUserChat(ReqCreateUserChat) returns (RspCreateUserChat);
  // 更新chat标签
  rpc UpdateUserChatLabels(ReqUpdateCustomChatLabels) returns (google.protobuf.Empty);
  // 获取会话列表
  rpc DescribeUserChats(ReqDescribeUserChats) returns (RspDescribeUserChats);
  // 删除会话
  rpc DeleteUserChat(ReqDeleteUserChat) returns (google.protobuf.Empty);
  // 删除消息
  rpc DeleteChatMessage(ReqDeleteChatMessage) returns (google.protobuf.Empty);
  // 创建系统文档副本
  rpc CreateSystemDocCopy(ReqCreateSystemDocCopy) returns (RspCreateSystemDocCopy);
  // 启用系统文档
  rpc EnableSystemDoc(ReqEnableSystemDoc) returns (google.protobuf.Empty);
  // 停用系统文档
  rpc DisableSystemDoc(ReqDisableSystemDoc) returns (google.protobuf.Empty);
  // 删除系统文档
  rpc DeleteSystemDoc(ReqDeleteSystemDoc) returns (google.protobuf.Empty);
  // 评价AI回答
  rpc RateAiAnswer(ReqRateAiAnswer) returns (google.protobuf.Empty);
  // 更新/创建用户反馈
  rpc UpsertUserFeedback(ReqUpsertUserFeedback) returns (RspUpsertFeedback);
  // 更新/创建运营反馈
  rpc UpsertOpFeedback(ReqUpsertOpFeedback) returns (RspUpsertFeedback);
  // 更新碳LIVE反馈
  rpc UpsertMgmtFeedback(ReqUpsertMgmtFeedback) returns (RspUpsertFeedback);
  // 查询用户反馈列表
  rpc GetFeedbacks(ReqGetFeedbacks) returns (RspGetFeedbacks);
  // 已读用户反馈
  rpc ReadFeedback(ReqReadFeedback) returns (google.protobuf.Empty);
  // 采用用户反馈
  rpc AcceptFeedback(ReqAcceptFeedback) returns (google.protobuf.Empty);
  // 查询用户反馈操作日志
  rpc GetFeedbackLogs(ReqGetFeedbackLogs) returns (RspGetFeedbackLogs);
  // 查询文档列表
  rpc GetDocs(ReqGetDocs) returns (RspGetDocs);
  // 获取消息doc
  rpc DescribeMessageDocs(ReqDescribeMessageDocs) returns (RspDescribeMessageDocs);
  // 获取Suggest log
  rpc DescribeSuggestLogs(ReqDescribeSuggestLog) returns (RspDescribeSuggestLog);
  // 获取消息log
  rpc DescribeMessageLogs(ReqDescribeMessageLog) returns (RspDescribeMessageLog);
  // 修复更新collection
  rpc SyncFixChatMessageCollection(ReqSyncFixChatMessageCollection) returns (google.protobuf.Empty);

  // 获取助手的聊天创建者列表
  rpc GetAssistantChatCreators(ReqGetAssistantChatCreators) returns (RspGetAssistantChatCreators);
  // 运营端获取AI对话管理列表
  rpc ListChat(ReqListChat) returns (RspListChat);
  // 运营端获取AI对话详情
  rpc GetChatDetail(ReqGetChatDetail) returns (RspGetChatDetail);
  // 获取AI对话自定义标签
  rpc ListCustomLabel(ReqListCustomLabel) returns (RspListCustomLabel);
  // 插入/更新对话标签
  rpc UpsertCustomLabels(ReqUpsertCustomLabels) returns (RspUpsertCustomLabels);
  // 删除对话标签
  rpc DeleteCustomLabels(ReqDeleteCustomLabels) returns (google.protobuf.Empty);
  // 查询ai助手列表
  rpc ListAssistant(ReqListAssistant) returns (RspListAssistant);
  // 获取ai助手详情
  rpc GetAssistant(ReqGetAssistant) returns (RspGetAssistant);
  // 获取小助手信息map
  rpc GetAssistantInfoMap(ReqGetAssistantInfoMap) returns (RspGetAssistantInfoMap);

  // 获取人工坐席列表
  rpc ListChatLiveAgent(ReqListChatLiveAgent) returns (RspListChatLiveAgent);
  // 切换人工坐席
  rpc SwitchChatLiveAgent(ReqSwitchChatLiveAgent) returns (google.protobuf.Empty);
  // 人工坐席状态变更
  rpc LiveAgentStatusChange(ReqLiveAgentStatusChange) returns (google.protobuf.Empty);
  // 发送ai公众号用到的rpc接口
  // 微信-评价微信公众号ai回答
  rpc RateAiAnswerWechat(ReqRateAiAnswerWechat) returns (google.protobuf.Empty);
  // 微信-获取问题答案（重复的问题则会忽略）
  rpc GetAnswerWechat(ReqGetAnswerWechat) returns (RspGetAnswerWechat);
  // 微信-获取图片提问的答案
  rpc GetAnswerWechatForFile(ReqGetAnswerWechat) returns (RspGetAnswerWechat);
  // 微信-获取继续回答的答案
  rpc GetAnswerWechatForContinue(ReqGetAnswerWechat) returns (RspGetAnswerWechat);
  // 微信-结束微信会话
  rpc FinishChatWechat(ReqFinishChatWechat) returns (google.protobuf.Empty);
  // 微信-获取微信访问白名单
  rpc GetWhiteListWechat(ReqGetWhiteListWechat) returns (RspGetWhiteListWechat);
  // 微信-更新微信问答message状态及答案记录信息
  rpc UpdateChatMessageStateWechat(ReqUpdateChatMessageStateWechat) returns (google.protobuf.Empty);
  // 微信-获取聊天记录游标
  rpc GetMessageCursorWechat(ReqGetMessageCursorWechat) returns (RspGetMessageCursorWechat);
  // 微信-获取用户会话信息
  rpc GetChatWechat(ReqGetChatWechat) returns (RspGetChatWechat);
  // 微信-创建用户会话
  rpc CreateChatWechat(ReqCreateChatWechat) returns (RspCreateChatWechat);
  // 微信-获取会话上一个问题的状态（是否已回复）
  rpc GetLastQuestionStateWechat(ReqGetLastQuestionStateWechat) returns (RspGetLastQuestionStateWechat);
  // 微信-更新发送的默认消息记录
  rpc UpdateChatMessageRecordWechat(ReqUpdateChatMessageRecordWechat) returns (RspUpdateChatMessageRecordWechat);
  // 创建发送记录
  rpc CreateSendRecord(ReqCreateSendRecord) returns (RspCreateSendRecord);
  // 更新发送记录
  rpc UpdateSendRecord(ReqUpdateSendRecord) returns (google.protobuf.Empty);
  // 获取助手的聊天记录(主要用于从t_chat_message 到 t_chat_send_record 的数据迁移)
  rpc DescribeAssistantMessage(ReqDescribeAssistantMessage) returns (RspDescribeAssistantMessage);
  // 插入助手的聊天记录(主要用于从t_chat_message 到 t_chat_send_record 的数据迁移)
  rpc InsertAssistantMessageRecord(ReqInsertAssistantMessageRecord) returns (google.protobuf.Empty);
  // 迁移用户会话记录信息
  rpc MigrationChatMessageInfo(ReqMigrationChatMessageInfo) returns (google.protobuf.Empty);
  // 获取有会话的地区编码
  rpc DescribeChatRegionCode(ReqDescribeChatRegionCode) returns (RspDescribeChatRegionCode);
  // 获取有教学反馈的地区编码
  rpc DescribeFeedbackRegionCode(ReqDescribeFeedbackRegionCode) returns (RspDescribeFeedbackRegionCode);

  // 查询用户会话记录
  rpc DescribeUserChatRecords(ReqDescribeUserChatRecords) returns (RspDescribeUserChatRecords);
  // 获取chat log权限
  rpc DescribeChatLogAuthItem(ReqDescribeChatLogAuthItem) returns (RspDescribeChatLogAuthItem);
  // 获取网页title
  rpc FetchHtmlTitles(ReqFetchHtmlTitles) returns (RspFetchHtmlTitles);

  // 查询助手
  rpc GetAssistants(ReqGetAssistants) returns (RspGetAssistants);
  // 批量创建助手
  rpc BatchCreateAssistant(ReqBatchCreateAssistant) returns (RspBatchCreateAssistant);
  // 批量更新助手
  rpc BatchUpdateAssistant(ReqBatchUpdateAssistant) returns (google.protobuf.Empty);
  // 删除助手
  rpc DeleteAssistant(ReqDeleteAssistant) returns (google.protobuf.Empty);
  // 查询助手日志
  rpc GetAssistantLogs(ReqGetAssistantLogs) returns (RspGetAssistantLogs);
  // 获取助手下拉选项
  rpc GetAssistantOptions(google.protobuf.Empty) returns (RspGetAssistantOptions);
  // 查询文档分段信息
  rpc GetDocChunks(ReqGetDocChunks) returns (RspGetDocChunks);
  // 文档分段
  rpc ChunkDoc(ReqChunkDoc) returns (RspChunkDoc);
  // 查询文档分段任务列表
  rpc GetChunkDocTasks(ReqGetChunkDocTasks) returns (RspGetChunkDocTasks);
  // 查询文档的向量化模型
  rpc GetDocEmbeddingModels(ReqGetDocEmbeddingModels) returns (RspGetDocEmbeddingModels);
  // 检查助手白名单
  rpc CheckAssistantAllowlist(ReqCheckAssistantAllowlist) returns (RspCheckAssistantAllowlist);
  // 获取最近使用过的助手Id
  rpc GetRecentlyUsedAssistantIds(ReqGetRecentlyUsedAssistantIds) returns(RspGetRecentlyUsedAssistantIds);
  // 获取最近使用过的助手
  rpc GetRecentlyUsedAssistants(ReqGetRecentlyUsedAssistants) returns(RspGetRecentlyUsedAssistants);
  // 获取所有助手的管理员信息
  rpc GetAssistantAdmin(ReqGetAssistantAdmin) returns (RspGetAssistantAdmin);
  // GetAssistantChatUser 获取助手会话的用户
  rpc GetAssistantChatUser(ReqGetAssistantChatUser) returns (RspGetAssistantChatUser);

  // 创建聊天分享
  rpc CreateChatShare(ReqCreateChatShare) returns (RspCreateChatShare);

  // 从分享继续聊天
  rpc ContinueChatFromShare(ReqContinueChatFromShare) returns (RspContinueChatFromShare);

  // 获取用户分享列表
  rpc ListChatShares(ReqListChatShares) returns (RspListChatShares);

  // 更新分享状态
  rpc UpdateChatShareStatus(ReqUpdateChatShareStatus) returns (google.protobuf.Empty);

  // 获取分享访问记录
  rpc ListChatShareAccesses(ReqListChatShareAccesses) returns (RspListChatShareAccesses);

  // 获取分享详情
  rpc GetChatShareMessages(ReqGetChatShareMessages) returns (RspGetChatShareMessages);

  // 查询share记录
  rpc GetChatShareRecord(ReqGetChatShareRecord) returns (RspGetChatShareRecord);

  // 记录分享访问
  rpc RecordChatShareAccess(ReqRecordChatShareAccess) returns (RspRecordChatShareAccess);
}
