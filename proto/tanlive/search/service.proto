syntax = "proto3";

package tanlive.search;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/search";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/options.proto";
import "tanlive/search/search.proto";

// 搜索服务
service SearchService {
  // 更新或创建产品文档
  rpc UpsertProductIndexDocs(ReqUpsertProductIndexDocs) returns (google.protobuf.Empty);
  // 删除产品文档
  rpc DeleteProductIndexDocs(ReqDeleteProductIndexDocs) returns (google.protobuf.Empty);
  // 搜索产品
  rpc SearchProductDocs(ReqSearchProductDocs) returns (RspSearchProductDocs);

  // 更新或创建团队文档
  rpc UpsertTeamIndexDocs(ReqUpsertTeamIndexDocs) returns (google.protobuf.Empty);
  // 删除团队文档
  rpc DeleteTeamIndexDocs(ReqDeleteTeamIndexDocs) returns (google.protobuf.Empty);
  // 搜索团队文档
  rpc SearchTeamDocs(ReqSearchTeamDocs) returns (RspSearchTeamDocs);

  // 编辑筛选项
  rpc ModifySearchOption(ReqModifySearchOption)returns (RspModifySearchOption);
  // 筛选项查询
  rpc DescribeSearchOptions(ReqDescribeSearchOptions)returns(RspDescribeSearchOptions);
  // 编辑搜索提示语
  rpc ModifySearchPrompt(ReqModifySearchPrompt)returns(google.protobuf.Empty);
  // 查询搜索提示语
  rpc DescribeSearchPrompts(ReqDescribeSearchPrompts)returns(RspDescribeSearchPrompts);
  // 通过引用对象，修改筛选项
  rpc ModifySearchOptionsByRefer(ReqModifySearchOptionsByRefer)returns(google.protobuf.Empty);

  // 更新或创建资源文档
  rpc UpsertResourceIndexDocs(ReqUpsertResourceIndexDocs) returns (google.protobuf.Empty);
  // 删除团队文档
  rpc DeleteResourceIndexDocs(ReqDeleteResourceIndexDocs) returns (google.protobuf.Empty);
  // 搜索团队文档
  rpc SearchResourceDocs(ReqSearchResourceDocs) returns (RspSearchResourceDocs);
}

message ReqUpsertProductIndexDocs {
  // 产品文档
  repeated ProductDoc product_docs = 1 [(validator)="required,min=1"];
}

message ReqDeleteProductIndexDocs {
  message ProductId {
    // 产品ID
    repeated uint64 product_id = 1 [(validator)="required,min=1,dive,min=1"];
  }
  oneof condition {
    // 通过团队ID
    uint64 by_team_id = 1 [(validator)="min=1"];
    // 通过产品ID
    ProductId by_product_id = 2;
  };
}

message ReqSearchProductDocs {
  // 过滤器
  message Filter {
    // 产品类型
    repeated uint64 product_type_tag_id = 1;
    // 产品行业认可
    repeated uint64 product_industry_recognition_tag_id = 2;
    // 图谱
    repeated uint64 graph_id = 3;
    // 源语言
    repeated string source_lang = 4;
    // 可见类型
    repeated int32 visible_type = 5;
    // 产品ID
    repeated uint64 id = 6;
  }
  // 关键词
  string keyword = 1;
  // 过滤器
  Filter filter = 2;
  // 分页器
  base.Paginator page = 3;
  // 返回字段
  repeated string fields = 4;
  // 仅查询ID
  bool only_id = 5;
}

message RspSearchProductDocs {
  // 产品文档列表
  repeated ProductDoc product_docs = 1;
  // 总数
  uint32 total_count = 2;
  // 产品ID列表（仅查询ID时返回）
  repeated uint64 product_id = 3;
}

message ReqUpsertTeamIndexDocs {
  // 团队文档
  repeated TeamDoc team_docs = 1 [(validator)="required,min=1"];
}

message ReqDeleteTeamIndexDocs {
  // 团队ID
  repeated uint64 team_id = 1 [(validator)="required,min=1,dive,min=1"];
}

message ReqSearchTeamDocs {
  // 过滤器
  message Filter {
    // 团队类型
    repeated uint64 team_type_tag_id = 1;
    // 团队行业
    repeated uint64 team_industry_tag_id = 2;
    // 图谱
    repeated uint64 graph_id = 3;
    // 团队行业认可
    repeated uint64 team_industry_recognition_tag_id = 4;
    // 团队发展阶段
    repeated uint64 team_financing_stage_tag_id = 5;
    // 团队所在地ID
    repeated uint64 team_location_place_id = 6;
    // 团队服务地区ID
    repeated uint64 team_service_region_place_id = 7;
    // 只看已认证
    bool only_verified = 8;
    // 只看有相关产品
    bool only_has_product = 9;
    // 只看有相关资源
    bool only_has_resource = 10;
    // 只看有相关资源
    repeated uint32 team_level = 11;
    // 源语言
    repeated string source_lang = 12;
    // 团队ID
    repeated uint64 id = 13;
    // 仅看有所在地的
    bool only_has_location = 14;
    // 仅看有服务地区的
    bool only_has_service_region = 15;
    // 团队所在地名
    repeated string team_location_place_name = 16;
    // 团队服务地区名
    repeated string team_service_region_place_name = 17;
  }
  // 关键词
  string keyword = 1;
  // 过滤器
  Filter filter = 2;
  // 分页器
  base.Paginator page = 3;
  // 返回字段
  repeated string fields = 4;
  // 仅查询ID
  bool only_id = 5;
  // 系统语言
  string system_lang = 6;
  // UGC语言
  string ugc_lang = 7;
}

message RspSearchTeamDocs {
  // 团队列表
  repeated TeamDoc team_docs = 1;
  // 总数
  uint32 total_count = 2;
  // 团队ID列表（仅查询ID时返回）
  repeated uint64 team_id = 3;
}

message ReqModifySearchOption{
  SearchOption option = 1;
  base.CrudType operation = 2;
  uint64 user_id = 3;
}

message RspModifySearchOption{
  uint64 id = 1;
}

message ReqDescribeSearchOptions{
  uint32 offset = 1;
  uint32 limit = 2;
  repeated base.OrderBy order_by = 4;
  message Filter{
    string language = 1;
    base.DisableState status = 2;
    SearchOptionReferType refer_type = 3;
    SearchOptionTargetType target_type = 4;
  }
  Filter filter = 3;
}

message RspDescribeSearchOptions{
  uint64 total = 1;
  message OptionEx{
    SearchOption option = 1;
    string refer_title = 2;
    int32 refer_bind_count = 3;
    bool is_delete = 4;
    base.UgcState refer_state = 5;
    uint64 refer_draft_id = 6;
  }
  repeated OptionEx options = 2;
}

message ReqModifySearchPrompt{
  uint64 id = 1;
  string prompt = 2;
  uint64 user_id = 3;
}


message ReqDescribeSearchPrompts{
  string language = 1;
  SearchOptionReferType refer_type = 2;
  SearchOptionTargetType target_type = 3;
}

message RspDescribeSearchPrompts{
  repeated SearchPrompt prompts = 1;
}

message ReqModifySearchOptionsByRefer{
  SearchOption option = 1;
  uint64 user_id = 3;
}

message ReqUpsertResourceIndexDocs {
  repeated ResourceDoc resource_docs = 1 [(validator)="required,min=1"];
}

message ReqDeleteResourceIndexDocs {
  repeated uint64 resource_id = 1 [(validator)="required,min=1,dive,min=1"];
}

message ReqSearchResourceDocs {
  // 过滤器
  message Filter {
    // id过滤
    repeated string ids = 1;
    // 资源类型
    repeated uint64 resource_type_tag_ids = 2;
    // 截止日期
    google.protobuf.Timestamp deadline = 3;
    // 出资规模-最小值 单位固定百万人民币
    uint64 investment_scale_min = 4;
    // 出资规模-最大值 单位固定百万人民币
    uint64 investment_scale_max = 5;
    // 排序 最新更新(last_update_date desc) 金额最高(provide_funds_avg desc) 截止日期最早(end_time desc) 截止日期最晚(end_time asc)
    repeated tanlive.base.OrderBy orders = 7;
    // 面向行业
    repeated uint64 industry_tag_ids = 8;
    // 团队阶段
    repeated uint64 team_stage_tag_ids = 10;
    // 产品成熟度-下限 不搜索默认-1
    int32 product_maturity_min = 11;
    // 产品成熟-上限 不搜索默认-1
    int32 product_maturity_max = 12;
    // 团队id
    uint64  team_id = 14;
    // 仅看长期资源
    uint32 is_long =27;
    // 不看过期资源
    uint32 is_no_past = 28;
    string source_lang = 29;
    // 资源系列
    uint64 series_tag_id = 30;
    // 团队行业认可标签
    repeated uint64 team_industry_recognition_tag_id = 31;
    // 产品行业认可标签
    repeated uint64 product_industry_recognition_tag_id = 32;
    // 日历开始时间
    google.protobuf.Timestamp start_time = 33;
    // 日历结束时间
    google.protobuf.Timestamp end_time = 34;
    // 举办地点
    repeated uint64 location_place_id = 35;
    // 受众地点
    repeated uint64 target_regions_place_id = 36;
  }
  // 关键词     // 搜索 资源标题，简介，发起方、行业
  string keyword = 1;
  // 过滤器
  Filter filter = 2;
  // 分页器
  base.Paginator page = 3;
  // 返回字段
  repeated string fields = 4;
  // 仅查询ID
  bool only_id = 5;
  // 系统语言
  string system_lang = 6;
  // UGC语言
  string ugc_lang = 7;
}

message RspSearchResourceDocs {
  // 资源列表
  repeated ResourceDoc resource_docs = 1;
  // 总数
  uint32 total_count = 2;
  // 资源ID列表（仅查询ID时返回）
  repeated uint64 resource_id = 3;
}