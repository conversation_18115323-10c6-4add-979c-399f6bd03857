// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/search/service.proto

package search

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqUpsertProductIndexDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 产品文档
	ProductDocs []*ProductDoc `protobuf:"bytes,1,rep,name=product_docs,json=productDocs,proto3" json:"product_docs,omitempty"`
}

func (x *ReqUpsertProductIndexDocs) Reset() {
	*x = ReqUpsertProductIndexDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpsertProductIndexDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpsertProductIndexDocs) ProtoMessage() {}

func (x *ReqUpsertProductIndexDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpsertProductIndexDocs.ProtoReflect.Descriptor instead.
func (*ReqUpsertProductIndexDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqUpsertProductIndexDocs) GetProductDocs() []*ProductDoc {
	if x != nil {
		return x.ProductDocs
	}
	return nil
}

type ReqDeleteProductIndexDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Condition:
	//
	//	*ReqDeleteProductIndexDocs_ByTeamId
	//	*ReqDeleteProductIndexDocs_ByProductId
	Condition isReqDeleteProductIndexDocs_Condition `protobuf_oneof:"condition"`
}

func (x *ReqDeleteProductIndexDocs) Reset() {
	*x = ReqDeleteProductIndexDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteProductIndexDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteProductIndexDocs) ProtoMessage() {}

func (x *ReqDeleteProductIndexDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteProductIndexDocs.ProtoReflect.Descriptor instead.
func (*ReqDeleteProductIndexDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{1}
}

func (m *ReqDeleteProductIndexDocs) GetCondition() isReqDeleteProductIndexDocs_Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (x *ReqDeleteProductIndexDocs) GetByTeamId() uint64 {
	if x, ok := x.GetCondition().(*ReqDeleteProductIndexDocs_ByTeamId); ok {
		return x.ByTeamId
	}
	return 0
}

func (x *ReqDeleteProductIndexDocs) GetByProductId() *ReqDeleteProductIndexDocs_ProductId {
	if x, ok := x.GetCondition().(*ReqDeleteProductIndexDocs_ByProductId); ok {
		return x.ByProductId
	}
	return nil
}

type isReqDeleteProductIndexDocs_Condition interface {
	isReqDeleteProductIndexDocs_Condition()
}

type ReqDeleteProductIndexDocs_ByTeamId struct {
	// 通过团队ID
	ByTeamId uint64 `protobuf:"varint,1,opt,name=by_team_id,json=byTeamId,proto3,oneof"`
}

type ReqDeleteProductIndexDocs_ByProductId struct {
	// 通过产品ID
	ByProductId *ReqDeleteProductIndexDocs_ProductId `protobuf:"bytes,2,opt,name=by_product_id,json=byProductId,proto3,oneof"`
}

func (*ReqDeleteProductIndexDocs_ByTeamId) isReqDeleteProductIndexDocs_Condition() {}

func (*ReqDeleteProductIndexDocs_ByProductId) isReqDeleteProductIndexDocs_Condition() {}

type ReqSearchProductDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 关键词
	Keyword string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// 过滤器
	Filter *ReqSearchProductDocs_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// 分页器
	Page *base.Paginator `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	// 返回字段
	Fields []string `protobuf:"bytes,4,rep,name=fields,proto3" json:"fields,omitempty"`
	// 仅查询ID
	OnlyId bool `protobuf:"varint,5,opt,name=only_id,json=onlyId,proto3" json:"only_id,omitempty"`
}

func (x *ReqSearchProductDocs) Reset() {
	*x = ReqSearchProductDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchProductDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchProductDocs) ProtoMessage() {}

func (x *ReqSearchProductDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchProductDocs.ProtoReflect.Descriptor instead.
func (*ReqSearchProductDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReqSearchProductDocs) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ReqSearchProductDocs) GetFilter() *ReqSearchProductDocs_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqSearchProductDocs) GetPage() *base.Paginator {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ReqSearchProductDocs) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *ReqSearchProductDocs) GetOnlyId() bool {
	if x != nil {
		return x.OnlyId
	}
	return false
}

type RspSearchProductDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 产品文档列表
	ProductDocs []*ProductDoc `protobuf:"bytes,1,rep,name=product_docs,json=productDocs,proto3" json:"product_docs,omitempty"`
	// 总数
	TotalCount uint32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// 产品ID列表（仅查询ID时返回）
	ProductId []uint64 `protobuf:"varint,3,rep,packed,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
}

func (x *RspSearchProductDocs) Reset() {
	*x = RspSearchProductDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSearchProductDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSearchProductDocs) ProtoMessage() {}

func (x *RspSearchProductDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSearchProductDocs.ProtoReflect.Descriptor instead.
func (*RspSearchProductDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{3}
}

func (x *RspSearchProductDocs) GetProductDocs() []*ProductDoc {
	if x != nil {
		return x.ProductDocs
	}
	return nil
}

func (x *RspSearchProductDocs) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *RspSearchProductDocs) GetProductId() []uint64 {
	if x != nil {
		return x.ProductId
	}
	return nil
}

type ReqUpsertTeamIndexDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队文档
	TeamDocs []*TeamDoc `protobuf:"bytes,1,rep,name=team_docs,json=teamDocs,proto3" json:"team_docs,omitempty"`
}

func (x *ReqUpsertTeamIndexDocs) Reset() {
	*x = ReqUpsertTeamIndexDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpsertTeamIndexDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpsertTeamIndexDocs) ProtoMessage() {}

func (x *ReqUpsertTeamIndexDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpsertTeamIndexDocs.ProtoReflect.Descriptor instead.
func (*ReqUpsertTeamIndexDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{4}
}

func (x *ReqUpsertTeamIndexDocs) GetTeamDocs() []*TeamDoc {
	if x != nil {
		return x.TeamDocs
	}
	return nil
}

type ReqDeleteTeamIndexDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队ID
	TeamId []uint64 `protobuf:"varint,1,rep,packed,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
}

func (x *ReqDeleteTeamIndexDocs) Reset() {
	*x = ReqDeleteTeamIndexDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteTeamIndexDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteTeamIndexDocs) ProtoMessage() {}

func (x *ReqDeleteTeamIndexDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteTeamIndexDocs.ProtoReflect.Descriptor instead.
func (*ReqDeleteTeamIndexDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{5}
}

func (x *ReqDeleteTeamIndexDocs) GetTeamId() []uint64 {
	if x != nil {
		return x.TeamId
	}
	return nil
}

type ReqSearchTeamDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 关键词
	Keyword string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// 过滤器
	Filter *ReqSearchTeamDocs_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// 分页器
	Page *base.Paginator `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	// 返回字段
	Fields []string `protobuf:"bytes,4,rep,name=fields,proto3" json:"fields,omitempty"`
	// 仅查询ID
	OnlyId bool `protobuf:"varint,5,opt,name=only_id,json=onlyId,proto3" json:"only_id,omitempty"`
	// 系统语言
	SystemLang string `protobuf:"bytes,6,opt,name=system_lang,json=systemLang,proto3" json:"system_lang,omitempty"`
	// UGC语言
	UgcLang string `protobuf:"bytes,7,opt,name=ugc_lang,json=ugcLang,proto3" json:"ugc_lang,omitempty"`
}

func (x *ReqSearchTeamDocs) Reset() {
	*x = ReqSearchTeamDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchTeamDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchTeamDocs) ProtoMessage() {}

func (x *ReqSearchTeamDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchTeamDocs.ProtoReflect.Descriptor instead.
func (*ReqSearchTeamDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{6}
}

func (x *ReqSearchTeamDocs) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ReqSearchTeamDocs) GetFilter() *ReqSearchTeamDocs_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqSearchTeamDocs) GetPage() *base.Paginator {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ReqSearchTeamDocs) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *ReqSearchTeamDocs) GetOnlyId() bool {
	if x != nil {
		return x.OnlyId
	}
	return false
}

func (x *ReqSearchTeamDocs) GetSystemLang() string {
	if x != nil {
		return x.SystemLang
	}
	return ""
}

func (x *ReqSearchTeamDocs) GetUgcLang() string {
	if x != nil {
		return x.UgcLang
	}
	return ""
}

type RspSearchTeamDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队列表
	TeamDocs []*TeamDoc `protobuf:"bytes,1,rep,name=team_docs,json=teamDocs,proto3" json:"team_docs,omitempty"`
	// 总数
	TotalCount uint32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// 团队ID列表（仅查询ID时返回）
	TeamId []uint64 `protobuf:"varint,3,rep,packed,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
}

func (x *RspSearchTeamDocs) Reset() {
	*x = RspSearchTeamDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSearchTeamDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSearchTeamDocs) ProtoMessage() {}

func (x *RspSearchTeamDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSearchTeamDocs.ProtoReflect.Descriptor instead.
func (*RspSearchTeamDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{7}
}

func (x *RspSearchTeamDocs) GetTeamDocs() []*TeamDoc {
	if x != nil {
		return x.TeamDocs
	}
	return nil
}

func (x *RspSearchTeamDocs) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *RspSearchTeamDocs) GetTeamId() []uint64 {
	if x != nil {
		return x.TeamId
	}
	return nil
}

type ReqModifySearchOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Option    *SearchOption `protobuf:"bytes,1,opt,name=option,proto3" json:"option,omitempty"`
	Operation base.CrudType `protobuf:"varint,2,opt,name=operation,proto3,enum=tanlive.base.CrudType" json:"operation,omitempty"`
	UserId    uint64        `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqModifySearchOption) Reset() {
	*x = ReqModifySearchOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifySearchOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifySearchOption) ProtoMessage() {}

func (x *ReqModifySearchOption) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifySearchOption.ProtoReflect.Descriptor instead.
func (*ReqModifySearchOption) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{8}
}

func (x *ReqModifySearchOption) GetOption() *SearchOption {
	if x != nil {
		return x.Option
	}
	return nil
}

func (x *ReqModifySearchOption) GetOperation() base.CrudType {
	if x != nil {
		return x.Operation
	}
	return base.CrudType(0)
}

func (x *ReqModifySearchOption) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type RspModifySearchOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RspModifySearchOption) Reset() {
	*x = RspModifySearchOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspModifySearchOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspModifySearchOption) ProtoMessage() {}

func (x *RspModifySearchOption) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspModifySearchOption.ProtoReflect.Descriptor instead.
func (*RspModifySearchOption) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{9}
}

func (x *RspModifySearchOption) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ReqDescribeSearchOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset  uint32                           `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit   uint32                           `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	OrderBy []*base.OrderBy                  `protobuf:"bytes,4,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Filter  *ReqDescribeSearchOptions_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeSearchOptions) Reset() {
	*x = ReqDescribeSearchOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSearchOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSearchOptions) ProtoMessage() {}

func (x *ReqDescribeSearchOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSearchOptions.ProtoReflect.Descriptor instead.
func (*ReqDescribeSearchOptions) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{10}
}

func (x *ReqDescribeSearchOptions) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeSearchOptions) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeSearchOptions) GetOrderBy() []*base.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ReqDescribeSearchOptions) GetFilter() *ReqDescribeSearchOptions_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type RspDescribeSearchOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   uint64                               `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Options []*RspDescribeSearchOptions_OptionEx `protobuf:"bytes,2,rep,name=options,proto3" json:"options,omitempty"`
}

func (x *RspDescribeSearchOptions) Reset() {
	*x = RspDescribeSearchOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSearchOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSearchOptions) ProtoMessage() {}

func (x *RspDescribeSearchOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSearchOptions.ProtoReflect.Descriptor instead.
func (*RspDescribeSearchOptions) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{11}
}

func (x *RspDescribeSearchOptions) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RspDescribeSearchOptions) GetOptions() []*RspDescribeSearchOptions_OptionEx {
	if x != nil {
		return x.Options
	}
	return nil
}

type ReqModifySearchPrompt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Prompt string `protobuf:"bytes,2,opt,name=prompt,proto3" json:"prompt,omitempty"`
	UserId uint64 `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqModifySearchPrompt) Reset() {
	*x = ReqModifySearchPrompt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifySearchPrompt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifySearchPrompt) ProtoMessage() {}

func (x *ReqModifySearchPrompt) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifySearchPrompt.ProtoReflect.Descriptor instead.
func (*ReqModifySearchPrompt) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{12}
}

func (x *ReqModifySearchPrompt) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReqModifySearchPrompt) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *ReqModifySearchPrompt) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ReqDescribeSearchPrompts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language   string                 `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
	ReferType  SearchOptionReferType  `protobuf:"varint,2,opt,name=refer_type,json=referType,proto3,enum=tanlive.search.SearchOptionReferType" json:"refer_type,omitempty"`
	TargetType SearchOptionTargetType `protobuf:"varint,3,opt,name=target_type,json=targetType,proto3,enum=tanlive.search.SearchOptionTargetType" json:"target_type,omitempty"`
}

func (x *ReqDescribeSearchPrompts) Reset() {
	*x = ReqDescribeSearchPrompts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSearchPrompts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSearchPrompts) ProtoMessage() {}

func (x *ReqDescribeSearchPrompts) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSearchPrompts.ProtoReflect.Descriptor instead.
func (*ReqDescribeSearchPrompts) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{13}
}

func (x *ReqDescribeSearchPrompts) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqDescribeSearchPrompts) GetReferType() SearchOptionReferType {
	if x != nil {
		return x.ReferType
	}
	return SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_UNSPECIFIED
}

func (x *ReqDescribeSearchPrompts) GetTargetType() SearchOptionTargetType {
	if x != nil {
		return x.TargetType
	}
	return SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_UNSPECIFIED
}

type RspDescribeSearchPrompts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prompts []*SearchPrompt `protobuf:"bytes,1,rep,name=prompts,proto3" json:"prompts,omitempty"`
}

func (x *RspDescribeSearchPrompts) Reset() {
	*x = RspDescribeSearchPrompts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSearchPrompts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSearchPrompts) ProtoMessage() {}

func (x *RspDescribeSearchPrompts) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSearchPrompts.ProtoReflect.Descriptor instead.
func (*RspDescribeSearchPrompts) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{14}
}

func (x *RspDescribeSearchPrompts) GetPrompts() []*SearchPrompt {
	if x != nil {
		return x.Prompts
	}
	return nil
}

type ReqModifySearchOptionsByRefer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Option *SearchOption `protobuf:"bytes,1,opt,name=option,proto3" json:"option,omitempty"`
	UserId uint64        `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqModifySearchOptionsByRefer) Reset() {
	*x = ReqModifySearchOptionsByRefer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifySearchOptionsByRefer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifySearchOptionsByRefer) ProtoMessage() {}

func (x *ReqModifySearchOptionsByRefer) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifySearchOptionsByRefer.ProtoReflect.Descriptor instead.
func (*ReqModifySearchOptionsByRefer) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{15}
}

func (x *ReqModifySearchOptionsByRefer) GetOption() *SearchOption {
	if x != nil {
		return x.Option
	}
	return nil
}

func (x *ReqModifySearchOptionsByRefer) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ReqUpsertResourceIndexDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceDocs []*ResourceDoc `protobuf:"bytes,1,rep,name=resource_docs,json=resourceDocs,proto3" json:"resource_docs,omitempty"`
}

func (x *ReqUpsertResourceIndexDocs) Reset() {
	*x = ReqUpsertResourceIndexDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpsertResourceIndexDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpsertResourceIndexDocs) ProtoMessage() {}

func (x *ReqUpsertResourceIndexDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpsertResourceIndexDocs.ProtoReflect.Descriptor instead.
func (*ReqUpsertResourceIndexDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{16}
}

func (x *ReqUpsertResourceIndexDocs) GetResourceDocs() []*ResourceDoc {
	if x != nil {
		return x.ResourceDocs
	}
	return nil
}

type ReqDeleteResourceIndexDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId []uint64 `protobuf:"varint,1,rep,packed,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
}

func (x *ReqDeleteResourceIndexDocs) Reset() {
	*x = ReqDeleteResourceIndexDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteResourceIndexDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteResourceIndexDocs) ProtoMessage() {}

func (x *ReqDeleteResourceIndexDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteResourceIndexDocs.ProtoReflect.Descriptor instead.
func (*ReqDeleteResourceIndexDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{17}
}

func (x *ReqDeleteResourceIndexDocs) GetResourceId() []uint64 {
	if x != nil {
		return x.ResourceId
	}
	return nil
}

type ReqSearchResourceDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 关键词     // 搜索 资源标题，简介，发起方、行业
	Keyword string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// 过滤器
	Filter *ReqSearchResourceDocs_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// 分页器
	Page *base.Paginator `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	// 返回字段
	Fields []string `protobuf:"bytes,4,rep,name=fields,proto3" json:"fields,omitempty"`
	// 仅查询ID
	OnlyId bool `protobuf:"varint,5,opt,name=only_id,json=onlyId,proto3" json:"only_id,omitempty"`
	// 系统语言
	SystemLang string `protobuf:"bytes,6,opt,name=system_lang,json=systemLang,proto3" json:"system_lang,omitempty"`
	// UGC语言
	UgcLang string `protobuf:"bytes,7,opt,name=ugc_lang,json=ugcLang,proto3" json:"ugc_lang,omitempty"`
}

func (x *ReqSearchResourceDocs) Reset() {
	*x = ReqSearchResourceDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchResourceDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchResourceDocs) ProtoMessage() {}

func (x *ReqSearchResourceDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchResourceDocs.ProtoReflect.Descriptor instead.
func (*ReqSearchResourceDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{18}
}

func (x *ReqSearchResourceDocs) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ReqSearchResourceDocs) GetFilter() *ReqSearchResourceDocs_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqSearchResourceDocs) GetPage() *base.Paginator {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ReqSearchResourceDocs) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *ReqSearchResourceDocs) GetOnlyId() bool {
	if x != nil {
		return x.OnlyId
	}
	return false
}

func (x *ReqSearchResourceDocs) GetSystemLang() string {
	if x != nil {
		return x.SystemLang
	}
	return ""
}

func (x *ReqSearchResourceDocs) GetUgcLang() string {
	if x != nil {
		return x.UgcLang
	}
	return ""
}

type RspSearchResourceDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 资源列表
	ResourceDocs []*ResourceDoc `protobuf:"bytes,1,rep,name=resource_docs,json=resourceDocs,proto3" json:"resource_docs,omitempty"`
	// 总数
	TotalCount uint32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// 资源ID列表（仅查询ID时返回）
	ResourceId []uint64 `protobuf:"varint,3,rep,packed,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
}

func (x *RspSearchResourceDocs) Reset() {
	*x = RspSearchResourceDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspSearchResourceDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspSearchResourceDocs) ProtoMessage() {}

func (x *RspSearchResourceDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspSearchResourceDocs.ProtoReflect.Descriptor instead.
func (*RspSearchResourceDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{19}
}

func (x *RspSearchResourceDocs) GetResourceDocs() []*ResourceDoc {
	if x != nil {
		return x.ResourceDocs
	}
	return nil
}

func (x *RspSearchResourceDocs) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *RspSearchResourceDocs) GetResourceId() []uint64 {
	if x != nil {
		return x.ResourceId
	}
	return nil
}

type ReqDeleteProductIndexDocs_ProductId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 产品ID
	ProductId []uint64 `protobuf:"varint,1,rep,packed,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
}

func (x *ReqDeleteProductIndexDocs_ProductId) Reset() {
	*x = ReqDeleteProductIndexDocs_ProductId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteProductIndexDocs_ProductId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteProductIndexDocs_ProductId) ProtoMessage() {}

func (x *ReqDeleteProductIndexDocs_ProductId) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteProductIndexDocs_ProductId.ProtoReflect.Descriptor instead.
func (*ReqDeleteProductIndexDocs_ProductId) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ReqDeleteProductIndexDocs_ProductId) GetProductId() []uint64 {
	if x != nil {
		return x.ProductId
	}
	return nil
}

// 过滤器
type ReqSearchProductDocs_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 产品类型
	ProductTypeTagId []uint64 `protobuf:"varint,1,rep,packed,name=product_type_tag_id,json=productTypeTagId,proto3" json:"product_type_tag_id,omitempty"`
	// 产品行业认可
	ProductIndustryRecognitionTagId []uint64 `protobuf:"varint,2,rep,packed,name=product_industry_recognition_tag_id,json=productIndustryRecognitionTagId,proto3" json:"product_industry_recognition_tag_id,omitempty"`
	// 图谱
	GraphId []uint64 `protobuf:"varint,3,rep,packed,name=graph_id,json=graphId,proto3" json:"graph_id,omitempty"`
	// 源语言
	SourceLang []string `protobuf:"bytes,4,rep,name=source_lang,json=sourceLang,proto3" json:"source_lang,omitempty"`
	// 可见类型
	VisibleType []int32 `protobuf:"varint,5,rep,packed,name=visible_type,json=visibleType,proto3" json:"visible_type,omitempty"`
	// 产品ID
	Id []uint64 `protobuf:"varint,6,rep,packed,name=id,proto3" json:"id,omitempty"`
}

func (x *ReqSearchProductDocs_Filter) Reset() {
	*x = ReqSearchProductDocs_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchProductDocs_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchProductDocs_Filter) ProtoMessage() {}

func (x *ReqSearchProductDocs_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchProductDocs_Filter.ProtoReflect.Descriptor instead.
func (*ReqSearchProductDocs_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ReqSearchProductDocs_Filter) GetProductTypeTagId() []uint64 {
	if x != nil {
		return x.ProductTypeTagId
	}
	return nil
}

func (x *ReqSearchProductDocs_Filter) GetProductIndustryRecognitionTagId() []uint64 {
	if x != nil {
		return x.ProductIndustryRecognitionTagId
	}
	return nil
}

func (x *ReqSearchProductDocs_Filter) GetGraphId() []uint64 {
	if x != nil {
		return x.GraphId
	}
	return nil
}

func (x *ReqSearchProductDocs_Filter) GetSourceLang() []string {
	if x != nil {
		return x.SourceLang
	}
	return nil
}

func (x *ReqSearchProductDocs_Filter) GetVisibleType() []int32 {
	if x != nil {
		return x.VisibleType
	}
	return nil
}

func (x *ReqSearchProductDocs_Filter) GetId() []uint64 {
	if x != nil {
		return x.Id
	}
	return nil
}

// 过滤器
type ReqSearchTeamDocs_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队类型
	TeamTypeTagId []uint64 `protobuf:"varint,1,rep,packed,name=team_type_tag_id,json=teamTypeTagId,proto3" json:"team_type_tag_id,omitempty"`
	// 团队行业
	TeamIndustryTagId []uint64 `protobuf:"varint,2,rep,packed,name=team_industry_tag_id,json=teamIndustryTagId,proto3" json:"team_industry_tag_id,omitempty"`
	// 图谱
	GraphId []uint64 `protobuf:"varint,3,rep,packed,name=graph_id,json=graphId,proto3" json:"graph_id,omitempty"`
	// 团队行业认可
	TeamIndustryRecognitionTagId []uint64 `protobuf:"varint,4,rep,packed,name=team_industry_recognition_tag_id,json=teamIndustryRecognitionTagId,proto3" json:"team_industry_recognition_tag_id,omitempty"`
	// 团队发展阶段
	TeamFinancingStageTagId []uint64 `protobuf:"varint,5,rep,packed,name=team_financing_stage_tag_id,json=teamFinancingStageTagId,proto3" json:"team_financing_stage_tag_id,omitempty"`
	// 团队所在地ID
	TeamLocationPlaceId []uint64 `protobuf:"varint,6,rep,packed,name=team_location_place_id,json=teamLocationPlaceId,proto3" json:"team_location_place_id,omitempty"`
	// 团队服务地区ID
	TeamServiceRegionPlaceId []uint64 `protobuf:"varint,7,rep,packed,name=team_service_region_place_id,json=teamServiceRegionPlaceId,proto3" json:"team_service_region_place_id,omitempty"`
	// 只看已认证
	OnlyVerified bool `protobuf:"varint,8,opt,name=only_verified,json=onlyVerified,proto3" json:"only_verified,omitempty"`
	// 只看有相关产品
	OnlyHasProduct bool `protobuf:"varint,9,opt,name=only_has_product,json=onlyHasProduct,proto3" json:"only_has_product,omitempty"`
	// 只看有相关资源
	OnlyHasResource bool `protobuf:"varint,10,opt,name=only_has_resource,json=onlyHasResource,proto3" json:"only_has_resource,omitempty"`
	// 只看有相关资源
	TeamLevel []uint32 `protobuf:"varint,11,rep,packed,name=team_level,json=teamLevel,proto3" json:"team_level,omitempty"`
	// 源语言
	SourceLang []string `protobuf:"bytes,12,rep,name=source_lang,json=sourceLang,proto3" json:"source_lang,omitempty"`
	// 团队ID
	Id []uint64 `protobuf:"varint,13,rep,packed,name=id,proto3" json:"id,omitempty"`
	// 仅看有所在地的
	OnlyHasLocation bool `protobuf:"varint,14,opt,name=only_has_location,json=onlyHasLocation,proto3" json:"only_has_location,omitempty"`
	// 仅看有服务地区的
	OnlyHasServiceRegion bool `protobuf:"varint,15,opt,name=only_has_service_region,json=onlyHasServiceRegion,proto3" json:"only_has_service_region,omitempty"`
	// 团队所在地名
	TeamLocationPlaceName []string `protobuf:"bytes,16,rep,name=team_location_place_name,json=teamLocationPlaceName,proto3" json:"team_location_place_name,omitempty"`
	// 团队服务地区名
	TeamServiceRegionPlaceName []string `protobuf:"bytes,17,rep,name=team_service_region_place_name,json=teamServiceRegionPlaceName,proto3" json:"team_service_region_place_name,omitempty"`
}

func (x *ReqSearchTeamDocs_Filter) Reset() {
	*x = ReqSearchTeamDocs_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchTeamDocs_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchTeamDocs_Filter) ProtoMessage() {}

func (x *ReqSearchTeamDocs_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchTeamDocs_Filter.ProtoReflect.Descriptor instead.
func (*ReqSearchTeamDocs_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ReqSearchTeamDocs_Filter) GetTeamTypeTagId() []uint64 {
	if x != nil {
		return x.TeamTypeTagId
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetTeamIndustryTagId() []uint64 {
	if x != nil {
		return x.TeamIndustryTagId
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetGraphId() []uint64 {
	if x != nil {
		return x.GraphId
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetTeamIndustryRecognitionTagId() []uint64 {
	if x != nil {
		return x.TeamIndustryRecognitionTagId
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetTeamFinancingStageTagId() []uint64 {
	if x != nil {
		return x.TeamFinancingStageTagId
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetTeamLocationPlaceId() []uint64 {
	if x != nil {
		return x.TeamLocationPlaceId
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetTeamServiceRegionPlaceId() []uint64 {
	if x != nil {
		return x.TeamServiceRegionPlaceId
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetOnlyVerified() bool {
	if x != nil {
		return x.OnlyVerified
	}
	return false
}

func (x *ReqSearchTeamDocs_Filter) GetOnlyHasProduct() bool {
	if x != nil {
		return x.OnlyHasProduct
	}
	return false
}

func (x *ReqSearchTeamDocs_Filter) GetOnlyHasResource() bool {
	if x != nil {
		return x.OnlyHasResource
	}
	return false
}

func (x *ReqSearchTeamDocs_Filter) GetTeamLevel() []uint32 {
	if x != nil {
		return x.TeamLevel
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetSourceLang() []string {
	if x != nil {
		return x.SourceLang
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetId() []uint64 {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetOnlyHasLocation() bool {
	if x != nil {
		return x.OnlyHasLocation
	}
	return false
}

func (x *ReqSearchTeamDocs_Filter) GetOnlyHasServiceRegion() bool {
	if x != nil {
		return x.OnlyHasServiceRegion
	}
	return false
}

func (x *ReqSearchTeamDocs_Filter) GetTeamLocationPlaceName() []string {
	if x != nil {
		return x.TeamLocationPlaceName
	}
	return nil
}

func (x *ReqSearchTeamDocs_Filter) GetTeamServiceRegionPlaceName() []string {
	if x != nil {
		return x.TeamServiceRegionPlaceName
	}
	return nil
}

type ReqDescribeSearchOptions_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language   string                 `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
	Status     base.DisableState      `protobuf:"varint,2,opt,name=status,proto3,enum=tanlive.base.DisableState" json:"status,omitempty"`
	ReferType  SearchOptionReferType  `protobuf:"varint,3,opt,name=refer_type,json=referType,proto3,enum=tanlive.search.SearchOptionReferType" json:"refer_type,omitempty"`
	TargetType SearchOptionTargetType `protobuf:"varint,4,opt,name=target_type,json=targetType,proto3,enum=tanlive.search.SearchOptionTargetType" json:"target_type,omitempty"`
}

func (x *ReqDescribeSearchOptions_Filter) Reset() {
	*x = ReqDescribeSearchOptions_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSearchOptions_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSearchOptions_Filter) ProtoMessage() {}

func (x *ReqDescribeSearchOptions_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSearchOptions_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeSearchOptions_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ReqDescribeSearchOptions_Filter) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqDescribeSearchOptions_Filter) GetStatus() base.DisableState {
	if x != nil {
		return x.Status
	}
	return base.DisableState(0)
}

func (x *ReqDescribeSearchOptions_Filter) GetReferType() SearchOptionReferType {
	if x != nil {
		return x.ReferType
	}
	return SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_UNSPECIFIED
}

func (x *ReqDescribeSearchOptions_Filter) GetTargetType() SearchOptionTargetType {
	if x != nil {
		return x.TargetType
	}
	return SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_UNSPECIFIED
}

type RspDescribeSearchOptions_OptionEx struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Option         *SearchOption `protobuf:"bytes,1,opt,name=option,proto3" json:"option,omitempty"`
	ReferTitle     string        `protobuf:"bytes,2,opt,name=refer_title,json=referTitle,proto3" json:"refer_title,omitempty"`
	ReferBindCount int32         `protobuf:"varint,3,opt,name=refer_bind_count,json=referBindCount,proto3" json:"refer_bind_count,omitempty"`
	IsDelete       bool          `protobuf:"varint,4,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	ReferState     base.UgcState `protobuf:"varint,5,opt,name=refer_state,json=referState,proto3,enum=tanlive.base.UgcState" json:"refer_state,omitempty"`
	ReferDraftId   uint64        `protobuf:"varint,6,opt,name=refer_draft_id,json=referDraftId,proto3" json:"refer_draft_id,omitempty"`
}

func (x *RspDescribeSearchOptions_OptionEx) Reset() {
	*x = RspDescribeSearchOptions_OptionEx{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSearchOptions_OptionEx) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSearchOptions_OptionEx) ProtoMessage() {}

func (x *RspDescribeSearchOptions_OptionEx) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSearchOptions_OptionEx.ProtoReflect.Descriptor instead.
func (*RspDescribeSearchOptions_OptionEx) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *RspDescribeSearchOptions_OptionEx) GetOption() *SearchOption {
	if x != nil {
		return x.Option
	}
	return nil
}

func (x *RspDescribeSearchOptions_OptionEx) GetReferTitle() string {
	if x != nil {
		return x.ReferTitle
	}
	return ""
}

func (x *RspDescribeSearchOptions_OptionEx) GetReferBindCount() int32 {
	if x != nil {
		return x.ReferBindCount
	}
	return 0
}

func (x *RspDescribeSearchOptions_OptionEx) GetIsDelete() bool {
	if x != nil {
		return x.IsDelete
	}
	return false
}

func (x *RspDescribeSearchOptions_OptionEx) GetReferState() base.UgcState {
	if x != nil {
		return x.ReferState
	}
	return base.UgcState(0)
}

func (x *RspDescribeSearchOptions_OptionEx) GetReferDraftId() uint64 {
	if x != nil {
		return x.ReferDraftId
	}
	return 0
}

// 过滤器
type ReqSearchResourceDocs_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id过滤
	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	// 资源类型
	ResourceTypeTagIds []uint64 `protobuf:"varint,2,rep,packed,name=resource_type_tag_ids,json=resourceTypeTagIds,proto3" json:"resource_type_tag_ids,omitempty"`
	// 截止日期
	Deadline *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=deadline,proto3" json:"deadline,omitempty"`
	// 出资规模-最小值 单位固定百万人民币
	InvestmentScaleMin uint64 `protobuf:"varint,4,opt,name=investment_scale_min,json=investmentScaleMin,proto3" json:"investment_scale_min,omitempty"`
	// 出资规模-最大值 单位固定百万人民币
	InvestmentScaleMax uint64 `protobuf:"varint,5,opt,name=investment_scale_max,json=investmentScaleMax,proto3" json:"investment_scale_max,omitempty"`
	// 排序 最新更新(last_update_date desc) 金额最高(provide_funds_avg desc) 截止日期最早(end_time desc) 截止日期最晚(end_time asc)
	Orders []*base.OrderBy `protobuf:"bytes,7,rep,name=orders,proto3" json:"orders,omitempty"`
	// 面向行业
	IndustryTagIds []uint64 `protobuf:"varint,8,rep,packed,name=industry_tag_ids,json=industryTagIds,proto3" json:"industry_tag_ids,omitempty"`
	// 团队阶段
	TeamStageTagIds []uint64 `protobuf:"varint,10,rep,packed,name=team_stage_tag_ids,json=teamStageTagIds,proto3" json:"team_stage_tag_ids,omitempty"`
	// 产品成熟度-下限 不搜索默认-1
	ProductMaturityMin int32 `protobuf:"varint,11,opt,name=product_maturity_min,json=productMaturityMin,proto3" json:"product_maturity_min,omitempty"`
	// 产品成熟-上限 不搜索默认-1
	ProductMaturityMax int32 `protobuf:"varint,12,opt,name=product_maturity_max,json=productMaturityMax,proto3" json:"product_maturity_max,omitempty"`
	// 团队id
	TeamId uint64 `protobuf:"varint,14,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// 仅看长期资源
	IsLong uint32 `protobuf:"varint,27,opt,name=is_long,json=isLong,proto3" json:"is_long,omitempty"`
	// 不看过期资源
	IsNoPast   uint32 `protobuf:"varint,28,opt,name=is_no_past,json=isNoPast,proto3" json:"is_no_past,omitempty"`
	SourceLang string `protobuf:"bytes,29,opt,name=source_lang,json=sourceLang,proto3" json:"source_lang,omitempty"`
	// 资源系列
	SeriesTagId uint64 `protobuf:"varint,30,opt,name=series_tag_id,json=seriesTagId,proto3" json:"series_tag_id,omitempty"`
	// 团队行业认可标签
	TeamIndustryRecognitionTagId []uint64 `protobuf:"varint,31,rep,packed,name=team_industry_recognition_tag_id,json=teamIndustryRecognitionTagId,proto3" json:"team_industry_recognition_tag_id,omitempty"`
	// 产品行业认可标签
	ProductIndustryRecognitionTagId []uint64 `protobuf:"varint,32,rep,packed,name=product_industry_recognition_tag_id,json=productIndustryRecognitionTagId,proto3" json:"product_industry_recognition_tag_id,omitempty"`
	// 日历开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,33,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 日历结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,34,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 举办地点
	LocationPlaceId []uint64 `protobuf:"varint,35,rep,packed,name=location_place_id,json=locationPlaceId,proto3" json:"location_place_id,omitempty"`
	// 受众地点
	TargetRegionsPlaceId []uint64 `protobuf:"varint,36,rep,packed,name=target_regions_place_id,json=targetRegionsPlaceId,proto3" json:"target_regions_place_id,omitempty"`
}

func (x *ReqSearchResourceDocs_Filter) Reset() {
	*x = ReqSearchResourceDocs_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSearchResourceDocs_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSearchResourceDocs_Filter) ProtoMessage() {}

func (x *ReqSearchResourceDocs_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSearchResourceDocs_Filter.ProtoReflect.Descriptor instead.
func (*ReqSearchResourceDocs_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_search_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *ReqSearchResourceDocs_Filter) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetResourceTypeTagIds() []uint64 {
	if x != nil {
		return x.ResourceTypeTagIds
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetDeadline() *timestamppb.Timestamp {
	if x != nil {
		return x.Deadline
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetInvestmentScaleMin() uint64 {
	if x != nil {
		return x.InvestmentScaleMin
	}
	return 0
}

func (x *ReqSearchResourceDocs_Filter) GetInvestmentScaleMax() uint64 {
	if x != nil {
		return x.InvestmentScaleMax
	}
	return 0
}

func (x *ReqSearchResourceDocs_Filter) GetOrders() []*base.OrderBy {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetIndustryTagIds() []uint64 {
	if x != nil {
		return x.IndustryTagIds
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetTeamStageTagIds() []uint64 {
	if x != nil {
		return x.TeamStageTagIds
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetProductMaturityMin() int32 {
	if x != nil {
		return x.ProductMaturityMin
	}
	return 0
}

func (x *ReqSearchResourceDocs_Filter) GetProductMaturityMax() int32 {
	if x != nil {
		return x.ProductMaturityMax
	}
	return 0
}

func (x *ReqSearchResourceDocs_Filter) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *ReqSearchResourceDocs_Filter) GetIsLong() uint32 {
	if x != nil {
		return x.IsLong
	}
	return 0
}

func (x *ReqSearchResourceDocs_Filter) GetIsNoPast() uint32 {
	if x != nil {
		return x.IsNoPast
	}
	return 0
}

func (x *ReqSearchResourceDocs_Filter) GetSourceLang() string {
	if x != nil {
		return x.SourceLang
	}
	return ""
}

func (x *ReqSearchResourceDocs_Filter) GetSeriesTagId() uint64 {
	if x != nil {
		return x.SeriesTagId
	}
	return 0
}

func (x *ReqSearchResourceDocs_Filter) GetTeamIndustryRecognitionTagId() []uint64 {
	if x != nil {
		return x.TeamIndustryRecognitionTagId
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetProductIndustryRecognitionTagId() []uint64 {
	if x != nil {
		return x.ProductIndustryRecognitionTagId
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetLocationPlaceId() []uint64 {
	if x != nil {
		return x.LocationPlaceId
	}
	return nil
}

func (x *ReqSearchResourceDocs_Filter) GetTargetRegionsPlaceId() []uint64 {
	if x != nil {
		return x.TargetRegionsPlaceId
	}
	return nil
}

var File_tanlive_search_service_proto protoreflect.FileDescriptor

var file_tanlive_search_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62,
	0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x6e, 0x0a, 0x19, 0x52, 0x65, 0x71, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x51,
	0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x6f, 0x63,
	0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d,
	0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x6f, 0x63,
	0x73, 0x22, 0xf9, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12,
	0x29, 0x0a, 0x0a, 0x62, 0x79, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x09, 0x82, 0x88, 0x27, 0x05, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x48, 0x00,
	0x52, 0x08, 0x62, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x0d, 0x62, 0x79,
	0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x2e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x48, 0x00, 0x52, 0x0b, 0x62, 0x79, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x49, 0x64, 0x1a, 0x49, 0x0a, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x1d, 0x82, 0x88, 0x27, 0x19, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c,
	0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x42, 0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xca, 0x03,
	0x0a, 0x14, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x43, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x44, 0x6f, 0x63, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x6e,
	0x6c, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6f, 0x6e, 0x6c,
	0x79, 0x49, 0x64, 0x1a, 0xf4, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2d,
	0x0a, 0x13, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74,
	0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x10, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x4c, 0x0a,
	0x23, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72,
	0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x1f, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x67,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x69, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x95, 0x01, 0x0a, 0x14, 0x52,
	0x73, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44,
	0x6f, 0x63, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x64,
	0x6f, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x6f,
	0x63, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x22, 0x62, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54,
	0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x48, 0x0a, 0x09,
	0x74, 0x65, 0x61, 0x6d, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x44, 0x6f, 0x63, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x08, 0x74, 0x65,
	0x61, 0x6d, 0x44, 0x6f, 0x63, 0x73, 0x22, 0x50, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73,
	0x12, 0x36, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x04, 0x42, 0x1d, 0x82, 0x88, 0x27, 0x19, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c,
	0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31,
	0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0xaf, 0x08, 0x0a, 0x11, 0x52, 0x65, 0x71,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x44, 0x6f, 0x63, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x67, 0x63,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x67, 0x63,
	0x4c, 0x61, 0x6e, 0x67, 0x1a, 0xa3, 0x06, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x27, 0x0a, 0x10, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x61, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x65, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x74, 0x65, 0x61, 0x6d,
	0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x11, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x75,
	0x73, 0x74, 0x72, 0x79, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x20, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x1c,
	0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x1b,
	0x74, 0x65, 0x61, 0x6d, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x17, 0x74, 0x65, 0x61, 0x6d, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x16, 0x74, 0x65,
	0x61, 0x6d, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x04, 0x52, 0x13, 0x74, 0x65, 0x61, 0x6d,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x3e, 0x0a, 0x1c, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x04, 0x52, 0x18, 0x74, 0x65, 0x61, 0x6d, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x79, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x68, 0x61, 0x73,
	0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x6f, 0x6e, 0x6c, 0x79, 0x48, 0x61, 0x73, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6f, 0x6e, 0x6c, 0x79, 0x48,
	0x61, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65,
	0x61, 0x6d, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09,
	0x74, 0x65, 0x61, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x6e,
	0x6c, 0x79, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6f, 0x6e, 0x6c, 0x79, 0x48, 0x61, 0x73, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x17, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x68,
	0x61, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x6f, 0x6e, 0x6c, 0x79, 0x48, 0x61, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a,
	0x18, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x15, 0x74, 0x65, 0x61, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x1e, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1a,
	0x74, 0x65, 0x61, 0x6d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x11, 0x52,
	0x73, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x44, 0x6f, 0x63, 0x73,
	0x12, 0x34, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x44, 0x6f, 0x63, 0x52, 0x08, 0x74, 0x65,
	0x61, 0x6d, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64,
	0x22, 0x9c, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x34, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x43, 0x72, 0x75, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x27, 0x0a, 0x15, 0x52, 0x73, 0x70, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0xad, 0x03, 0x0a, 0x18, 0x52, 0x65, 0x71,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x30, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x47, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xe7,
	0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x0a, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x47, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x87, 0x03, 0x0a, 0x18, 0x52, 0x73, 0x70,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x4b, 0x0a, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73,
	0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x52,
	0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x87, 0x02, 0x0a, 0x08, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x78, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x42, 0x69, 0x6e,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x67, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x72, 0x61, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x44, 0x72, 0x61, 0x66, 0x74,
	0x49, 0x64, 0x22, 0x58, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xc5, 0x01, 0x0a,
	0x18, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0b, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x52, 0x0a, 0x18, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73,
	0x12, 0x36, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x52,
	0x07, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x22, 0x6e, 0x0a, 0x1d, 0x52, 0x65, 0x71, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x42, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x1a, 0x52, 0x65, 0x71, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x42, 0x12, 0x82, 0x88, 0x27, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x73, 0x22, 0x5c, 0x0a, 0x1a,
	0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x3e, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42,
	0x1d, 0x82, 0x88, 0x27, 0x19, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x6d, 0x69,
	0x6e, 0x3d, 0x31, 0x2c, 0x64, 0x69, 0x76, 0x65, 0x2c, 0x6d, 0x69, 0x6e, 0x3d, 0x31, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x22, 0xe7, 0x09, 0x0a, 0x15, 0x52,
	0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x44, 0x6f, 0x63, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x44,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x44, 0x6f, 0x63, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x6e, 0x6c,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x79,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x61, 0x6e,
	0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4c,
	0x61, 0x6e, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x67, 0x63, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x67, 0x63, 0x4c, 0x61, 0x6e, 0x67, 0x1a, 0xd3,
	0x07, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x61, 0x67,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x12, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x36,
	0x0a, 0x08, 0x64, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x64, 0x65,
	0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x63, 0x61, 0x6c, 0x65, 0x4d, 0x69, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x6d, 0x61, 0x78,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x4d, 0x61, 0x78, 0x12, 0x2d, 0x0a, 0x06, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x0e, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x54, 0x61, 0x67,
	0x49, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x12, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x0f, 0x74, 0x65, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x49, 0x64, 0x73,
	0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6d, 0x61, 0x74, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4d,
	0x69, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6d, 0x61,
	0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x4d, 0x61, 0x78, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x69, 0x73, 0x4c, 0x6f, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6e, 0x6f, 0x5f,
	0x70, 0x61, 0x73, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73, 0x4e, 0x6f,
	0x50, 0x61, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c,
	0x61, 0x6e, 0x67, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x5f,
	0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x20, 0x74, 0x65, 0x61,
	0x6d, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x67,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x1f, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x1c, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x49,
	0x64, 0x12, 0x4c, 0x0a, 0x23, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x20, 0x20, 0x03, 0x28, 0x04, 0x52, 0x1f,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12,
	0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x23, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x35, 0x0a,
	0x17, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x5f,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x24, 0x20, 0x03, 0x28, 0x04, 0x52, 0x14,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x49, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x15, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x40,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44,
	0x6f, 0x63, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x32, 0xca, 0x0a, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x5b, 0x0a, 0x16, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x29,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x52, 0x65, 0x71, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x5b, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x29, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5f,
	0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44,
	0x6f, 0x63, 0x73, 0x12, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x6f, 0x63, 0x73, 0x1a, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x6f, 0x63, 0x73, 0x12,
	0x55, 0x0a, 0x13, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x55, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x26, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52,
	0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x44, 0x6f, 0x63, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x56, 0x0a,
	0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x44, 0x6f, 0x63, 0x73, 0x12,
	0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x44, 0x6f,
	0x63, 0x73, 0x1a, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61,
	0x6d, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x62, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6b, 0x0a, 0x15, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x28, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73,
	0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x53, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x25, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65,
	0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6b, 0x0a, 0x15, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x73, 0x12, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x1a, 0x28,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x63, 0x0a, 0x1a, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42,
	0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x12, 0x2d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5d, 0x0a,
	0x17, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x44, 0x6f, 0x63, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5d, 0x0a, 0x17,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x2a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x44,
	0x6f, 0x63, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x62, 0x0a, 0x12, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x63,
	0x73, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x73, 0x1a, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x73, 0x42,
	0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f,
	0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_search_service_proto_rawDescOnce sync.Once
	file_tanlive_search_service_proto_rawDescData = file_tanlive_search_service_proto_rawDesc
)

func file_tanlive_search_service_proto_rawDescGZIP() []byte {
	file_tanlive_search_service_proto_rawDescOnce.Do(func() {
		file_tanlive_search_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_search_service_proto_rawDescData)
	})
	return file_tanlive_search_service_proto_rawDescData
}

var file_tanlive_search_service_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_tanlive_search_service_proto_goTypes = []interface{}{
	(*ReqUpsertProductIndexDocs)(nil),           // 0: tanlive.search.ReqUpsertProductIndexDocs
	(*ReqDeleteProductIndexDocs)(nil),           // 1: tanlive.search.ReqDeleteProductIndexDocs
	(*ReqSearchProductDocs)(nil),                // 2: tanlive.search.ReqSearchProductDocs
	(*RspSearchProductDocs)(nil),                // 3: tanlive.search.RspSearchProductDocs
	(*ReqUpsertTeamIndexDocs)(nil),              // 4: tanlive.search.ReqUpsertTeamIndexDocs
	(*ReqDeleteTeamIndexDocs)(nil),              // 5: tanlive.search.ReqDeleteTeamIndexDocs
	(*ReqSearchTeamDocs)(nil),                   // 6: tanlive.search.ReqSearchTeamDocs
	(*RspSearchTeamDocs)(nil),                   // 7: tanlive.search.RspSearchTeamDocs
	(*ReqModifySearchOption)(nil),               // 8: tanlive.search.ReqModifySearchOption
	(*RspModifySearchOption)(nil),               // 9: tanlive.search.RspModifySearchOption
	(*ReqDescribeSearchOptions)(nil),            // 10: tanlive.search.ReqDescribeSearchOptions
	(*RspDescribeSearchOptions)(nil),            // 11: tanlive.search.RspDescribeSearchOptions
	(*ReqModifySearchPrompt)(nil),               // 12: tanlive.search.ReqModifySearchPrompt
	(*ReqDescribeSearchPrompts)(nil),            // 13: tanlive.search.ReqDescribeSearchPrompts
	(*RspDescribeSearchPrompts)(nil),            // 14: tanlive.search.RspDescribeSearchPrompts
	(*ReqModifySearchOptionsByRefer)(nil),       // 15: tanlive.search.ReqModifySearchOptionsByRefer
	(*ReqUpsertResourceIndexDocs)(nil),          // 16: tanlive.search.ReqUpsertResourceIndexDocs
	(*ReqDeleteResourceIndexDocs)(nil),          // 17: tanlive.search.ReqDeleteResourceIndexDocs
	(*ReqSearchResourceDocs)(nil),               // 18: tanlive.search.ReqSearchResourceDocs
	(*RspSearchResourceDocs)(nil),               // 19: tanlive.search.RspSearchResourceDocs
	(*ReqDeleteProductIndexDocs_ProductId)(nil), // 20: tanlive.search.ReqDeleteProductIndexDocs.ProductId
	(*ReqSearchProductDocs_Filter)(nil),         // 21: tanlive.search.ReqSearchProductDocs.Filter
	(*ReqSearchTeamDocs_Filter)(nil),            // 22: tanlive.search.ReqSearchTeamDocs.Filter
	(*ReqDescribeSearchOptions_Filter)(nil),     // 23: tanlive.search.ReqDescribeSearchOptions.Filter
	(*RspDescribeSearchOptions_OptionEx)(nil),   // 24: tanlive.search.RspDescribeSearchOptions.OptionEx
	(*ReqSearchResourceDocs_Filter)(nil),        // 25: tanlive.search.ReqSearchResourceDocs.Filter
	(*ProductDoc)(nil),                          // 26: tanlive.search.ProductDoc
	(*base.Paginator)(nil),                      // 27: tanlive.base.Paginator
	(*TeamDoc)(nil),                             // 28: tanlive.search.TeamDoc
	(*SearchOption)(nil),                        // 29: tanlive.search.SearchOption
	(base.CrudType)(0),                          // 30: tanlive.base.CrudType
	(*base.OrderBy)(nil),                        // 31: tanlive.base.OrderBy
	(SearchOptionReferType)(0),                  // 32: tanlive.search.SearchOptionReferType
	(SearchOptionTargetType)(0),                 // 33: tanlive.search.SearchOptionTargetType
	(*SearchPrompt)(nil),                        // 34: tanlive.search.SearchPrompt
	(*ResourceDoc)(nil),                         // 35: tanlive.search.ResourceDoc
	(base.DisableState)(0),                      // 36: tanlive.base.DisableState
	(base.UgcState)(0),                          // 37: tanlive.base.UgcState
	(*timestamppb.Timestamp)(nil),               // 38: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                       // 39: google.protobuf.Empty
}
var file_tanlive_search_service_proto_depIdxs = []int32{
	26, // 0: tanlive.search.ReqUpsertProductIndexDocs.product_docs:type_name -> tanlive.search.ProductDoc
	20, // 1: tanlive.search.ReqDeleteProductIndexDocs.by_product_id:type_name -> tanlive.search.ReqDeleteProductIndexDocs.ProductId
	21, // 2: tanlive.search.ReqSearchProductDocs.filter:type_name -> tanlive.search.ReqSearchProductDocs.Filter
	27, // 3: tanlive.search.ReqSearchProductDocs.page:type_name -> tanlive.base.Paginator
	26, // 4: tanlive.search.RspSearchProductDocs.product_docs:type_name -> tanlive.search.ProductDoc
	28, // 5: tanlive.search.ReqUpsertTeamIndexDocs.team_docs:type_name -> tanlive.search.TeamDoc
	22, // 6: tanlive.search.ReqSearchTeamDocs.filter:type_name -> tanlive.search.ReqSearchTeamDocs.Filter
	27, // 7: tanlive.search.ReqSearchTeamDocs.page:type_name -> tanlive.base.Paginator
	28, // 8: tanlive.search.RspSearchTeamDocs.team_docs:type_name -> tanlive.search.TeamDoc
	29, // 9: tanlive.search.ReqModifySearchOption.option:type_name -> tanlive.search.SearchOption
	30, // 10: tanlive.search.ReqModifySearchOption.operation:type_name -> tanlive.base.CrudType
	31, // 11: tanlive.search.ReqDescribeSearchOptions.order_by:type_name -> tanlive.base.OrderBy
	23, // 12: tanlive.search.ReqDescribeSearchOptions.filter:type_name -> tanlive.search.ReqDescribeSearchOptions.Filter
	24, // 13: tanlive.search.RspDescribeSearchOptions.options:type_name -> tanlive.search.RspDescribeSearchOptions.OptionEx
	32, // 14: tanlive.search.ReqDescribeSearchPrompts.refer_type:type_name -> tanlive.search.SearchOptionReferType
	33, // 15: tanlive.search.ReqDescribeSearchPrompts.target_type:type_name -> tanlive.search.SearchOptionTargetType
	34, // 16: tanlive.search.RspDescribeSearchPrompts.prompts:type_name -> tanlive.search.SearchPrompt
	29, // 17: tanlive.search.ReqModifySearchOptionsByRefer.option:type_name -> tanlive.search.SearchOption
	35, // 18: tanlive.search.ReqUpsertResourceIndexDocs.resource_docs:type_name -> tanlive.search.ResourceDoc
	25, // 19: tanlive.search.ReqSearchResourceDocs.filter:type_name -> tanlive.search.ReqSearchResourceDocs.Filter
	27, // 20: tanlive.search.ReqSearchResourceDocs.page:type_name -> tanlive.base.Paginator
	35, // 21: tanlive.search.RspSearchResourceDocs.resource_docs:type_name -> tanlive.search.ResourceDoc
	36, // 22: tanlive.search.ReqDescribeSearchOptions.Filter.status:type_name -> tanlive.base.DisableState
	32, // 23: tanlive.search.ReqDescribeSearchOptions.Filter.refer_type:type_name -> tanlive.search.SearchOptionReferType
	33, // 24: tanlive.search.ReqDescribeSearchOptions.Filter.target_type:type_name -> tanlive.search.SearchOptionTargetType
	29, // 25: tanlive.search.RspDescribeSearchOptions.OptionEx.option:type_name -> tanlive.search.SearchOption
	37, // 26: tanlive.search.RspDescribeSearchOptions.OptionEx.refer_state:type_name -> tanlive.base.UgcState
	38, // 27: tanlive.search.ReqSearchResourceDocs.Filter.deadline:type_name -> google.protobuf.Timestamp
	31, // 28: tanlive.search.ReqSearchResourceDocs.Filter.orders:type_name -> tanlive.base.OrderBy
	38, // 29: tanlive.search.ReqSearchResourceDocs.Filter.start_time:type_name -> google.protobuf.Timestamp
	38, // 30: tanlive.search.ReqSearchResourceDocs.Filter.end_time:type_name -> google.protobuf.Timestamp
	0,  // 31: tanlive.search.SearchService.UpsertProductIndexDocs:input_type -> tanlive.search.ReqUpsertProductIndexDocs
	1,  // 32: tanlive.search.SearchService.DeleteProductIndexDocs:input_type -> tanlive.search.ReqDeleteProductIndexDocs
	2,  // 33: tanlive.search.SearchService.SearchProductDocs:input_type -> tanlive.search.ReqSearchProductDocs
	4,  // 34: tanlive.search.SearchService.UpsertTeamIndexDocs:input_type -> tanlive.search.ReqUpsertTeamIndexDocs
	5,  // 35: tanlive.search.SearchService.DeleteTeamIndexDocs:input_type -> tanlive.search.ReqDeleteTeamIndexDocs
	6,  // 36: tanlive.search.SearchService.SearchTeamDocs:input_type -> tanlive.search.ReqSearchTeamDocs
	8,  // 37: tanlive.search.SearchService.ModifySearchOption:input_type -> tanlive.search.ReqModifySearchOption
	10, // 38: tanlive.search.SearchService.DescribeSearchOptions:input_type -> tanlive.search.ReqDescribeSearchOptions
	12, // 39: tanlive.search.SearchService.ModifySearchPrompt:input_type -> tanlive.search.ReqModifySearchPrompt
	13, // 40: tanlive.search.SearchService.DescribeSearchPrompts:input_type -> tanlive.search.ReqDescribeSearchPrompts
	15, // 41: tanlive.search.SearchService.ModifySearchOptionsByRefer:input_type -> tanlive.search.ReqModifySearchOptionsByRefer
	16, // 42: tanlive.search.SearchService.UpsertResourceIndexDocs:input_type -> tanlive.search.ReqUpsertResourceIndexDocs
	17, // 43: tanlive.search.SearchService.DeleteResourceIndexDocs:input_type -> tanlive.search.ReqDeleteResourceIndexDocs
	18, // 44: tanlive.search.SearchService.SearchResourceDocs:input_type -> tanlive.search.ReqSearchResourceDocs
	39, // 45: tanlive.search.SearchService.UpsertProductIndexDocs:output_type -> google.protobuf.Empty
	39, // 46: tanlive.search.SearchService.DeleteProductIndexDocs:output_type -> google.protobuf.Empty
	3,  // 47: tanlive.search.SearchService.SearchProductDocs:output_type -> tanlive.search.RspSearchProductDocs
	39, // 48: tanlive.search.SearchService.UpsertTeamIndexDocs:output_type -> google.protobuf.Empty
	39, // 49: tanlive.search.SearchService.DeleteTeamIndexDocs:output_type -> google.protobuf.Empty
	7,  // 50: tanlive.search.SearchService.SearchTeamDocs:output_type -> tanlive.search.RspSearchTeamDocs
	9,  // 51: tanlive.search.SearchService.ModifySearchOption:output_type -> tanlive.search.RspModifySearchOption
	11, // 52: tanlive.search.SearchService.DescribeSearchOptions:output_type -> tanlive.search.RspDescribeSearchOptions
	39, // 53: tanlive.search.SearchService.ModifySearchPrompt:output_type -> google.protobuf.Empty
	14, // 54: tanlive.search.SearchService.DescribeSearchPrompts:output_type -> tanlive.search.RspDescribeSearchPrompts
	39, // 55: tanlive.search.SearchService.ModifySearchOptionsByRefer:output_type -> google.protobuf.Empty
	39, // 56: tanlive.search.SearchService.UpsertResourceIndexDocs:output_type -> google.protobuf.Empty
	39, // 57: tanlive.search.SearchService.DeleteResourceIndexDocs:output_type -> google.protobuf.Empty
	19, // 58: tanlive.search.SearchService.SearchResourceDocs:output_type -> tanlive.search.RspSearchResourceDocs
	45, // [45:59] is the sub-list for method output_type
	31, // [31:45] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_tanlive_search_service_proto_init() }
func file_tanlive_search_service_proto_init() {
	if File_tanlive_search_service_proto != nil {
		return
	}
	file_tanlive_search_search_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_search_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpsertProductIndexDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteProductIndexDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchProductDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSearchProductDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpsertTeamIndexDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteTeamIndexDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchTeamDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSearchTeamDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifySearchOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspModifySearchOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSearchOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSearchOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifySearchPrompt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSearchPrompts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSearchPrompts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifySearchOptionsByRefer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpsertResourceIndexDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteResourceIndexDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchResourceDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspSearchResourceDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteProductIndexDocs_ProductId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchProductDocs_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchTeamDocs_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSearchOptions_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSearchOptions_OptionEx); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSearchResourceDocs_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tanlive_search_service_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*ReqDeleteProductIndexDocs_ByTeamId)(nil),
		(*ReqDeleteProductIndexDocs_ByProductId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_search_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_search_service_proto_goTypes,
		DependencyIndexes: file_tanlive_search_service_proto_depIdxs,
		MessageInfos:      file_tanlive_search_service_proto_msgTypes,
	}.Build()
	File_tanlive_search_service_proto = out.File
	file_tanlive_search_service_proto_rawDesc = nil
	file_tanlive_search_service_proto_goTypes = nil
	file_tanlive_search_service_proto_depIdxs = nil
}
