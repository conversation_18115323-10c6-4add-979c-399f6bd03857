syntax = "proto3";

package tanlive.search;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/search";

import "google/protobuf/timestamp.proto";
import "tanlive/options.proto";
import "tanlive/base/base.proto";

// 文本文档
message TextDoc {
  // 文本内容
  string text = 1;
  // 语言
  string lang = 2;
}

// 标签文档
message TagDoc {
  // 标签ID
  uint64 id = 1;
  // 标签名
  string name = 2;
  // 语言
  string lang = 3;
}

// 产品文档
message ProductDoc {
  // 产品ID
  uint64 id = 1;
  // 产品名称
  repeated TextDoc name = 2;
  // 简介
  repeated TextDoc brief_intro = 3;
  // 面向用户
  repeated TagDoc user_oriented = 4;
  // 类型
  repeated TagDoc type = 5;
  // 行业认可
  repeated TagDoc industry_recognition = 6;
  // 置顶
  uint32 top = 7;
  // 最后更新时间
  google.protobuf.Timestamp last_update_date = 8;
  // 关联图谱
  repeated uint64 graph = 9;
  // 源语言
  string source_lang = 10;
  // 可见类型
  int32 visible_type = 11;

  // 团队ID
  uint64 team_id = 12;
  // 团队全称
  repeated TextDoc team_full_name = 13;
  // 团队全称
  repeated TextDoc team_short_name = 14;
  // 团队所属行业
  repeated TagDoc team_industry = 15;
}

// 团队文档
message TeamDoc {
  // 团队ID
  uint64 id = 1;
  // 全称
  repeated TextDoc full_name = 2;
  // 简称
  repeated TextDoc short_name = 3;
  // 简介
  repeated TextDoc brief_intro = 4;
  // 行业
  repeated TagDoc industry = 5;
  // 行业认可
  repeated TagDoc industry_recognition = 6;
  // 类型
  repeated TagDoc type = 7;
  // 关联图谱
  repeated uint64 graph = 8;
  // 发展阶段
  repeated TagDoc financing_stage = 9;
  // 所在地
  repeated AddressDoc location = 10;
  // 服务地区
  repeated AddressDoc service_region = 11;
  // 是否已认证
  bool is_verified = 12;
  // 是否发布产品
  bool has_product = 13;
  // 是否发布资源
  bool has_resource = 14;
  // 共创等级
  uint32 level = 15;
  // 推荐值
  uint32 recommend = 16;
  // 更新时间
  google.protobuf.Timestamp last_update_date = 17;
  // 源语言
  string source_lang = 18;
  // 团队LOGO
  string logo_url = 19;
  // 置顶值
  int32 top = 20;
}

// 地点文档
message PlaceDoc {
  // 地点ID
  uint64 id = 1;
  // 地点名称
  string name = 2;
  // 详细地址
  string address = 3;
  // 纬度
  double lat = 4;
  // 经度
  double lng = 5;
}

// 地址文档
message AddressDoc {
  // 大洲
  PlaceDoc continent = 1;
  // 国家
  PlaceDoc country = 2;
  // 一级行政区划
  PlaceDoc level1 = 3;
  // 二级行政区划
  PlaceDoc level2 = 4;
  // 详细地址
  PlaceDoc detail = 5;
  // 语言
  string lang = 6;
}

// 搜索对应的筛选项
enum SearchOptionReferType{
  SEARCH_OPTION_REFER_TYPE_UNSPECIFIED = 0;
  SEARCH_OPTION_REFER_TYPE_ATLAS = 1; // 图谱
  SEARCH_OPTION_REFER_TYPE_INDUSTRY_RECOGNITION = 2; // 行业认可
  SEARCH_OPTION_REFER_TYPE_RESOURCE_SERIES = 3; // 资源系列
}

// 搜索对应的模块
enum SearchOptionTargetType{
  SEARCH_OPTION_TARGET_TYPE_UNSPECIFIED = 0;
  SEARCH_OPTION_TARGET_TYPE_TEAM = 1; // 团队
  SEARCH_OPTION_TARGET_TYPE_PRODUCT = 2; // 产品
  SEARCH_OPTION_TARGET_TYPE_RESOURCE = 3; // 资源
}

// 搜索筛选项
message SearchOption{
  SearchOptionTargetType target_type = 1;
  SearchOptionReferType refer_type = 2;
  string language = 3;
  string name = 4;
  uint32 weight = 5;
  int64 refer_id = 6;
  base.DisableState status = 8;
  int64 id = 9;
}

// 搜索提示语
message SearchPrompt{
  SearchOptionTargetType target_type = 1;
  SearchOptionReferType refer_type = 2;
  string language = 3;
  int64 id = 4;
  string content =  5;
}

// ResourceDoc 资源文档
message ResourceDoc {
  // 搜索 资源标题，简介，发起方、行业
  // 资源标题
  repeated TextDoc name = 1;
  // 资源简述
  repeated TextDoc introduction = 2;
  // 资源类型
  repeated TagDoc type = 3;
  // 主办方
  repeated TextDoc OriginatorName = 4;
  // 出资规模-最小值 单位固定百万人民币
  float provide_funds_min = 5;
  // 出资规模-最大值 单位固定百万人民币
  float provide_funds_max = 6;
  // 排序 最新更新(last_update_date desc) 金额最高(provide_funds_avg desc) 截止日期最早(end_time desc) 截止日期最晚(end_time asc)
  repeated string orders = 7;
  // 面向行业
  repeated TagDoc industry_tag = 8;
  // 团队阶段
  repeated TagDoc team_stage_tag = 10;
  // 产品成熟度-下限 不搜索默认-1
  int32 product_maturity_min = 11;
  // 产品成熟-上限 不搜索默认-1
  int32 product_maturity_max = 12;
  uint64 id = 14;
  // 头像
  string res_image_url = 15;
  // 关联方
  repeated TextDoc RelaterName = 16;
  // 置顶位置
  int32 top_pos = 17;
  // 排序权重
  int32 order_by = 18;
  // 资源权限 1全网公开 2用户
  uint64	show_scope = 19;
  // 共享编辑团队
  repeated uint64 share_edit_team = 20;
  // 共享编辑人
  repeated uint64 share_edit_user = 21;
  // 有效期类型 ONE单次资源 LONG长期有效 CYCLE周期
  string time_validity_type = 22;
  // 源语言
  string source_lang = 29;
  // 资源系列
  repeated TagDoc series_tag = 30;
  // 团队行业认可标签
  repeated TagDoc team_industry_recognition_tag = 31;
  // 产品行业认可标签
  repeated TagDoc product_industry_recognition_tag = 32;
  // 日历开始时间
  google.protobuf.Timestamp start_time = 33;
  // 日历结束时间
  google.protobuf.Timestamp end_time = 34;
  // 举办地点
  repeated AddressDoc location = 35;
  // 受众地点
  repeated AddressDoc target_regions = 36;
  // 最后更新时间
  google.protobuf.Timestamp last_update_date = 37;
}