// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package search

import (
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func (x *ProductDoc) MaskInLog() any {
	if x == nil {
		return (*ProductDoc)(nil)
	}

	y := proto.Clone(x).(*ProductDoc)
	for k, v := range y.Name {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Name[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.BriefIntro {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BriefIntro[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.UserOriented {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserOriented[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Type[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.IndustryRecognition {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.IndustryRecognition[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.TeamFullName {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamFullName[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.TeamShortName {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamShortName[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.TeamIndustry {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamIndustry[k] = vv.MaskInLog().(*TagDoc)
		}
	}

	return y
}

func (x *ProductDoc) MaskInRpc() any {
	if x == nil {
		return (*ProductDoc)(nil)
	}

	y := x
	for k, v := range y.Name {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Name[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.BriefIntro {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BriefIntro[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.UserOriented {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserOriented[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Type[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.IndustryRecognition {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.IndustryRecognition[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.TeamFullName {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamFullName[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.TeamShortName {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamShortName[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.TeamIndustry {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamIndustry[k] = vv.MaskInRpc().(*TagDoc)
		}
	}

	return y
}

func (x *ProductDoc) MaskInBff() any {
	if x == nil {
		return (*ProductDoc)(nil)
	}

	y := x
	for k, v := range y.Name {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Name[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.BriefIntro {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BriefIntro[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.UserOriented {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserOriented[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Type[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.IndustryRecognition {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.IndustryRecognition[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.TeamFullName {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamFullName[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.TeamShortName {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamShortName[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.TeamIndustry {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamIndustry[k] = vv.MaskInBff().(*TagDoc)
		}
	}

	return y
}

func (x *TeamDoc) MaskInLog() any {
	if x == nil {
		return (*TeamDoc)(nil)
	}

	y := proto.Clone(x).(*TeamDoc)
	for k, v := range y.FullName {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.FullName[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.ShortName {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ShortName[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.BriefIntro {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BriefIntro[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.Industry {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Industry[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.IndustryRecognition {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.IndustryRecognition[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Type[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.FinancingStage {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.FinancingStage[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.Location {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Location[k] = vv.MaskInLog().(*AddressDoc)
		}
	}
	for k, v := range y.ServiceRegion {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ServiceRegion[k] = vv.MaskInLog().(*AddressDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TeamDoc) MaskInRpc() any {
	if x == nil {
		return (*TeamDoc)(nil)
	}

	y := x
	for k, v := range y.FullName {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.FullName[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.ShortName {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ShortName[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.BriefIntro {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BriefIntro[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.Industry {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Industry[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.IndustryRecognition {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.IndustryRecognition[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Type[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.FinancingStage {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.FinancingStage[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.Location {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Location[k] = vv.MaskInRpc().(*AddressDoc)
		}
	}
	for k, v := range y.ServiceRegion {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ServiceRegion[k] = vv.MaskInRpc().(*AddressDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TeamDoc) MaskInBff() any {
	if x == nil {
		return (*TeamDoc)(nil)
	}

	y := x
	for k, v := range y.FullName {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.FullName[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.ShortName {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ShortName[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.BriefIntro {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BriefIntro[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.Industry {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Industry[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.IndustryRecognition {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.IndustryRecognition[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Type[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.FinancingStage {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.FinancingStage[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.Location {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Location[k] = vv.MaskInBff().(*AddressDoc)
		}
	}
	for k, v := range y.ServiceRegion {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ServiceRegion[k] = vv.MaskInBff().(*AddressDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *AddressDoc) MaskInLog() any {
	if x == nil {
		return (*AddressDoc)(nil)
	}

	y := proto.Clone(x).(*AddressDoc)
	if v, ok := any(y.Continent).(interface{ MaskInLog() any }); ok {
		y.Continent = v.MaskInLog().(*PlaceDoc)
	}
	if v, ok := any(y.Country).(interface{ MaskInLog() any }); ok {
		y.Country = v.MaskInLog().(*PlaceDoc)
	}
	if v, ok := any(y.Level1).(interface{ MaskInLog() any }); ok {
		y.Level1 = v.MaskInLog().(*PlaceDoc)
	}
	if v, ok := any(y.Level2).(interface{ MaskInLog() any }); ok {
		y.Level2 = v.MaskInLog().(*PlaceDoc)
	}
	if v, ok := any(y.Detail).(interface{ MaskInLog() any }); ok {
		y.Detail = v.MaskInLog().(*PlaceDoc)
	}

	return y
}

func (x *AddressDoc) MaskInRpc() any {
	if x == nil {
		return (*AddressDoc)(nil)
	}

	y := x
	if v, ok := any(y.Continent).(interface{ MaskInRpc() any }); ok {
		y.Continent = v.MaskInRpc().(*PlaceDoc)
	}
	if v, ok := any(y.Country).(interface{ MaskInRpc() any }); ok {
		y.Country = v.MaskInRpc().(*PlaceDoc)
	}
	if v, ok := any(y.Level1).(interface{ MaskInRpc() any }); ok {
		y.Level1 = v.MaskInRpc().(*PlaceDoc)
	}
	if v, ok := any(y.Level2).(interface{ MaskInRpc() any }); ok {
		y.Level2 = v.MaskInRpc().(*PlaceDoc)
	}
	if v, ok := any(y.Detail).(interface{ MaskInRpc() any }); ok {
		y.Detail = v.MaskInRpc().(*PlaceDoc)
	}

	return y
}

func (x *AddressDoc) MaskInBff() any {
	if x == nil {
		return (*AddressDoc)(nil)
	}

	y := x
	if v, ok := any(y.Continent).(interface{ MaskInBff() any }); ok {
		y.Continent = v.MaskInBff().(*PlaceDoc)
	}
	if v, ok := any(y.Country).(interface{ MaskInBff() any }); ok {
		y.Country = v.MaskInBff().(*PlaceDoc)
	}
	if v, ok := any(y.Level1).(interface{ MaskInBff() any }); ok {
		y.Level1 = v.MaskInBff().(*PlaceDoc)
	}
	if v, ok := any(y.Level2).(interface{ MaskInBff() any }); ok {
		y.Level2 = v.MaskInBff().(*PlaceDoc)
	}
	if v, ok := any(y.Detail).(interface{ MaskInBff() any }); ok {
		y.Detail = v.MaskInBff().(*PlaceDoc)
	}

	return y
}

func (x *ResourceDoc) MaskInLog() any {
	if x == nil {
		return (*ResourceDoc)(nil)
	}

	y := proto.Clone(x).(*ResourceDoc)
	for k, v := range y.Name {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Name[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.Introduction {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Introduction[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Type[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.OriginatorName {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OriginatorName[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.IndustryTag {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.IndustryTag[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.TeamStageTag {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamStageTag[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.RelaterName {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.RelaterName[k] = vv.MaskInLog().(*TextDoc)
		}
	}
	for k, v := range y.SeriesTag {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SeriesTag[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.TeamIndustryRecognitionTag {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamIndustryRecognitionTag[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	for k, v := range y.ProductIndustryRecognitionTag {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ProductIndustryRecognitionTag[k] = vv.MaskInLog().(*TagDoc)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Location {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Location[k] = vv.MaskInLog().(*AddressDoc)
		}
	}
	for k, v := range y.TargetRegions {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TargetRegions[k] = vv.MaskInLog().(*AddressDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ResourceDoc) MaskInRpc() any {
	if x == nil {
		return (*ResourceDoc)(nil)
	}

	y := x
	for k, v := range y.Name {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Name[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.Introduction {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Introduction[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Type[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.OriginatorName {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OriginatorName[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.IndustryTag {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.IndustryTag[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.TeamStageTag {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamStageTag[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.RelaterName {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.RelaterName[k] = vv.MaskInRpc().(*TextDoc)
		}
	}
	for k, v := range y.SeriesTag {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SeriesTag[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.TeamIndustryRecognitionTag {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamIndustryRecognitionTag[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	for k, v := range y.ProductIndustryRecognitionTag {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ProductIndustryRecognitionTag[k] = vv.MaskInRpc().(*TagDoc)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Location {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Location[k] = vv.MaskInRpc().(*AddressDoc)
		}
	}
	for k, v := range y.TargetRegions {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TargetRegions[k] = vv.MaskInRpc().(*AddressDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ResourceDoc) MaskInBff() any {
	if x == nil {
		return (*ResourceDoc)(nil)
	}

	y := x
	for k, v := range y.Name {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Name[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.Introduction {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Introduction[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.Type {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Type[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.OriginatorName {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OriginatorName[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.IndustryTag {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.IndustryTag[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.TeamStageTag {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamStageTag[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.RelaterName {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.RelaterName[k] = vv.MaskInBff().(*TextDoc)
		}
	}
	for k, v := range y.SeriesTag {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SeriesTag[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.TeamIndustryRecognitionTag {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamIndustryRecognitionTag[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	for k, v := range y.ProductIndustryRecognitionTag {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ProductIndustryRecognitionTag[k] = vv.MaskInBff().(*TagDoc)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Location {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Location[k] = vv.MaskInBff().(*AddressDoc)
		}
	}
	for k, v := range y.TargetRegions {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TargetRegions[k] = vv.MaskInBff().(*AddressDoc)
		}
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ProductDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Name {
		if sanitizer, ok := any(x.Name[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.BriefIntro {
		if sanitizer, ok := any(x.BriefIntro[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserOriented {
		if sanitizer, ok := any(x.UserOriented[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Type {
		if sanitizer, ok := any(x.Type[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.IndustryRecognition {
		if sanitizer, ok := any(x.IndustryRecognition[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.TeamFullName {
		if sanitizer, ok := any(x.TeamFullName[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamShortName {
		if sanitizer, ok := any(x.TeamShortName[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamIndustry {
		if sanitizer, ok := any(x.TeamIndustry[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *TeamDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.FullName {
		if sanitizer, ok := any(x.FullName[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ShortName {
		if sanitizer, ok := any(x.ShortName[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.BriefIntro {
		if sanitizer, ok := any(x.BriefIntro[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Industry {
		if sanitizer, ok := any(x.Industry[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.IndustryRecognition {
		if sanitizer, ok := any(x.IndustryRecognition[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Type {
		if sanitizer, ok := any(x.Type[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.FinancingStage {
		if sanitizer, ok := any(x.FinancingStage[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Location {
		if sanitizer, ok := any(x.Location[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ServiceRegion {
		if sanitizer, ok := any(x.ServiceRegion[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AddressDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Continent).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Country).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Level1).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Level2).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Detail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ResourceDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Name {
		if sanitizer, ok := any(x.Name[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Introduction {
		if sanitizer, ok := any(x.Introduction[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Type {
		if sanitizer, ok := any(x.Type[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.OriginatorName {
		if sanitizer, ok := any(x.OriginatorName[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.IndustryTag {
		if sanitizer, ok := any(x.IndustryTag[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamStageTag {
		if sanitizer, ok := any(x.TeamStageTag[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.RelaterName {
		if sanitizer, ok := any(x.RelaterName[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SeriesTag {
		if sanitizer, ok := any(x.SeriesTag[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamIndustryRecognitionTag {
		if sanitizer, ok := any(x.TeamIndustryRecognitionTag[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ProductIndustryRecognitionTag {
		if sanitizer, ok := any(x.ProductIndustryRecognitionTag[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Location {
		if sanitizer, ok := any(x.Location[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TargetRegions {
		if sanitizer, ok := any(x.TargetRegions[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
