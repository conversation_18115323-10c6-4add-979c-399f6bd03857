// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/search/service.proto

package search

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for SearchService service

func NewSearchServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for SearchService service

type SearchService interface {
	// 更新或创建产品文档
	UpsertProductIndexDocs(ctx context.Context, in *ReqUpsertProductIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除产品文档
	DeleteProductIndexDocs(ctx context.Context, in *ReqDeleteProductIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error)
	// 搜索产品
	SearchProductDocs(ctx context.Context, in *ReqSearchProductDocs, opts ...client.CallOption) (*RspSearchProductDocs, error)
	// 更新或创建团队文档
	UpsertTeamIndexDocs(ctx context.Context, in *ReqUpsertTeamIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除团队文档
	DeleteTeamIndexDocs(ctx context.Context, in *ReqDeleteTeamIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error)
	// 搜索团队文档
	SearchTeamDocs(ctx context.Context, in *ReqSearchTeamDocs, opts ...client.CallOption) (*RspSearchTeamDocs, error)
	// 编辑筛选项
	ModifySearchOption(ctx context.Context, in *ReqModifySearchOption, opts ...client.CallOption) (*RspModifySearchOption, error)
	// 筛选项查询
	DescribeSearchOptions(ctx context.Context, in *ReqDescribeSearchOptions, opts ...client.CallOption) (*RspDescribeSearchOptions, error)
	// 编辑搜索提示语
	ModifySearchPrompt(ctx context.Context, in *ReqModifySearchPrompt, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询搜索提示语
	DescribeSearchPrompts(ctx context.Context, in *ReqDescribeSearchPrompts, opts ...client.CallOption) (*RspDescribeSearchPrompts, error)
	// 通过引用对象，修改筛选项
	ModifySearchOptionsByRefer(ctx context.Context, in *ReqModifySearchOptionsByRefer, opts ...client.CallOption) (*emptypb.Empty, error)
	// 更新或创建资源文档
	UpsertResourceIndexDocs(ctx context.Context, in *ReqUpsertResourceIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除团队文档
	DeleteResourceIndexDocs(ctx context.Context, in *ReqDeleteResourceIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error)
	// 搜索团队文档
	SearchResourceDocs(ctx context.Context, in *ReqSearchResourceDocs, opts ...client.CallOption) (*RspSearchResourceDocs, error)
}

type searchService struct {
	c    client.Client
	name string
}

func NewSearchService(name string, c client.Client) SearchService {
	return &searchService{
		c:    c,
		name: name,
	}
}

func (c *searchService) UpsertProductIndexDocs(ctx context.Context, in *ReqUpsertProductIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SearchService.UpsertProductIndexDocs", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) DeleteProductIndexDocs(ctx context.Context, in *ReqDeleteProductIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SearchService.DeleteProductIndexDocs", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) SearchProductDocs(ctx context.Context, in *ReqSearchProductDocs, opts ...client.CallOption) (*RspSearchProductDocs, error) {
	req := c.c.NewRequest(c.name, "SearchService.SearchProductDocs", in)
	out := new(RspSearchProductDocs)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) UpsertTeamIndexDocs(ctx context.Context, in *ReqUpsertTeamIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SearchService.UpsertTeamIndexDocs", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) DeleteTeamIndexDocs(ctx context.Context, in *ReqDeleteTeamIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SearchService.DeleteTeamIndexDocs", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) SearchTeamDocs(ctx context.Context, in *ReqSearchTeamDocs, opts ...client.CallOption) (*RspSearchTeamDocs, error) {
	req := c.c.NewRequest(c.name, "SearchService.SearchTeamDocs", in)
	out := new(RspSearchTeamDocs)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) ModifySearchOption(ctx context.Context, in *ReqModifySearchOption, opts ...client.CallOption) (*RspModifySearchOption, error) {
	req := c.c.NewRequest(c.name, "SearchService.ModifySearchOption", in)
	out := new(RspModifySearchOption)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) DescribeSearchOptions(ctx context.Context, in *ReqDescribeSearchOptions, opts ...client.CallOption) (*RspDescribeSearchOptions, error) {
	req := c.c.NewRequest(c.name, "SearchService.DescribeSearchOptions", in)
	out := new(RspDescribeSearchOptions)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) ModifySearchPrompt(ctx context.Context, in *ReqModifySearchPrompt, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SearchService.ModifySearchPrompt", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) DescribeSearchPrompts(ctx context.Context, in *ReqDescribeSearchPrompts, opts ...client.CallOption) (*RspDescribeSearchPrompts, error) {
	req := c.c.NewRequest(c.name, "SearchService.DescribeSearchPrompts", in)
	out := new(RspDescribeSearchPrompts)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) ModifySearchOptionsByRefer(ctx context.Context, in *ReqModifySearchOptionsByRefer, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SearchService.ModifySearchOptionsByRefer", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) UpsertResourceIndexDocs(ctx context.Context, in *ReqUpsertResourceIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SearchService.UpsertResourceIndexDocs", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) DeleteResourceIndexDocs(ctx context.Context, in *ReqDeleteResourceIndexDocs, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "SearchService.DeleteResourceIndexDocs", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchService) SearchResourceDocs(ctx context.Context, in *ReqSearchResourceDocs, opts ...client.CallOption) (*RspSearchResourceDocs, error) {
	req := c.c.NewRequest(c.name, "SearchService.SearchResourceDocs", in)
	out := new(RspSearchResourceDocs)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for SearchService service

type SearchServiceHandler interface {
	// 更新或创建产品文档
	UpsertProductIndexDocs(context.Context, *ReqUpsertProductIndexDocs, *emptypb.Empty) error
	// 删除产品文档
	DeleteProductIndexDocs(context.Context, *ReqDeleteProductIndexDocs, *emptypb.Empty) error
	// 搜索产品
	SearchProductDocs(context.Context, *ReqSearchProductDocs, *RspSearchProductDocs) error
	// 更新或创建团队文档
	UpsertTeamIndexDocs(context.Context, *ReqUpsertTeamIndexDocs, *emptypb.Empty) error
	// 删除团队文档
	DeleteTeamIndexDocs(context.Context, *ReqDeleteTeamIndexDocs, *emptypb.Empty) error
	// 搜索团队文档
	SearchTeamDocs(context.Context, *ReqSearchTeamDocs, *RspSearchTeamDocs) error
	// 编辑筛选项
	ModifySearchOption(context.Context, *ReqModifySearchOption, *RspModifySearchOption) error
	// 筛选项查询
	DescribeSearchOptions(context.Context, *ReqDescribeSearchOptions, *RspDescribeSearchOptions) error
	// 编辑搜索提示语
	ModifySearchPrompt(context.Context, *ReqModifySearchPrompt, *emptypb.Empty) error
	// 查询搜索提示语
	DescribeSearchPrompts(context.Context, *ReqDescribeSearchPrompts, *RspDescribeSearchPrompts) error
	// 通过引用对象，修改筛选项
	ModifySearchOptionsByRefer(context.Context, *ReqModifySearchOptionsByRefer, *emptypb.Empty) error
	// 更新或创建资源文档
	UpsertResourceIndexDocs(context.Context, *ReqUpsertResourceIndexDocs, *emptypb.Empty) error
	// 删除团队文档
	DeleteResourceIndexDocs(context.Context, *ReqDeleteResourceIndexDocs, *emptypb.Empty) error
	// 搜索团队文档
	SearchResourceDocs(context.Context, *ReqSearchResourceDocs, *RspSearchResourceDocs) error
}

func RegisterSearchServiceHandler(s server.Server, hdlr SearchServiceHandler, opts ...server.HandlerOption) error {
	type searchService interface {
		UpsertProductIndexDocs(ctx context.Context, in *ReqUpsertProductIndexDocs, out *emptypb.Empty) error
		DeleteProductIndexDocs(ctx context.Context, in *ReqDeleteProductIndexDocs, out *emptypb.Empty) error
		SearchProductDocs(ctx context.Context, in *ReqSearchProductDocs, out *RspSearchProductDocs) error
		UpsertTeamIndexDocs(ctx context.Context, in *ReqUpsertTeamIndexDocs, out *emptypb.Empty) error
		DeleteTeamIndexDocs(ctx context.Context, in *ReqDeleteTeamIndexDocs, out *emptypb.Empty) error
		SearchTeamDocs(ctx context.Context, in *ReqSearchTeamDocs, out *RspSearchTeamDocs) error
		ModifySearchOption(ctx context.Context, in *ReqModifySearchOption, out *RspModifySearchOption) error
		DescribeSearchOptions(ctx context.Context, in *ReqDescribeSearchOptions, out *RspDescribeSearchOptions) error
		ModifySearchPrompt(ctx context.Context, in *ReqModifySearchPrompt, out *emptypb.Empty) error
		DescribeSearchPrompts(ctx context.Context, in *ReqDescribeSearchPrompts, out *RspDescribeSearchPrompts) error
		ModifySearchOptionsByRefer(ctx context.Context, in *ReqModifySearchOptionsByRefer, out *emptypb.Empty) error
		UpsertResourceIndexDocs(ctx context.Context, in *ReqUpsertResourceIndexDocs, out *emptypb.Empty) error
		DeleteResourceIndexDocs(ctx context.Context, in *ReqDeleteResourceIndexDocs, out *emptypb.Empty) error
		SearchResourceDocs(ctx context.Context, in *ReqSearchResourceDocs, out *RspSearchResourceDocs) error
	}
	type SearchService struct {
		searchService
	}
	h := &searchServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&SearchService{h}, opts...))
}

type searchServiceHandler struct {
	SearchServiceHandler
}

func (h *searchServiceHandler) UpsertProductIndexDocs(ctx context.Context, in *ReqUpsertProductIndexDocs, out *emptypb.Empty) error {
	return h.SearchServiceHandler.UpsertProductIndexDocs(ctx, in, out)
}

func (h *searchServiceHandler) DeleteProductIndexDocs(ctx context.Context, in *ReqDeleteProductIndexDocs, out *emptypb.Empty) error {
	return h.SearchServiceHandler.DeleteProductIndexDocs(ctx, in, out)
}

func (h *searchServiceHandler) SearchProductDocs(ctx context.Context, in *ReqSearchProductDocs, out *RspSearchProductDocs) error {
	return h.SearchServiceHandler.SearchProductDocs(ctx, in, out)
}

func (h *searchServiceHandler) UpsertTeamIndexDocs(ctx context.Context, in *ReqUpsertTeamIndexDocs, out *emptypb.Empty) error {
	return h.SearchServiceHandler.UpsertTeamIndexDocs(ctx, in, out)
}

func (h *searchServiceHandler) DeleteTeamIndexDocs(ctx context.Context, in *ReqDeleteTeamIndexDocs, out *emptypb.Empty) error {
	return h.SearchServiceHandler.DeleteTeamIndexDocs(ctx, in, out)
}

func (h *searchServiceHandler) SearchTeamDocs(ctx context.Context, in *ReqSearchTeamDocs, out *RspSearchTeamDocs) error {
	return h.SearchServiceHandler.SearchTeamDocs(ctx, in, out)
}

func (h *searchServiceHandler) ModifySearchOption(ctx context.Context, in *ReqModifySearchOption, out *RspModifySearchOption) error {
	return h.SearchServiceHandler.ModifySearchOption(ctx, in, out)
}

func (h *searchServiceHandler) DescribeSearchOptions(ctx context.Context, in *ReqDescribeSearchOptions, out *RspDescribeSearchOptions) error {
	return h.SearchServiceHandler.DescribeSearchOptions(ctx, in, out)
}

func (h *searchServiceHandler) ModifySearchPrompt(ctx context.Context, in *ReqModifySearchPrompt, out *emptypb.Empty) error {
	return h.SearchServiceHandler.ModifySearchPrompt(ctx, in, out)
}

func (h *searchServiceHandler) DescribeSearchPrompts(ctx context.Context, in *ReqDescribeSearchPrompts, out *RspDescribeSearchPrompts) error {
	return h.SearchServiceHandler.DescribeSearchPrompts(ctx, in, out)
}

func (h *searchServiceHandler) ModifySearchOptionsByRefer(ctx context.Context, in *ReqModifySearchOptionsByRefer, out *emptypb.Empty) error {
	return h.SearchServiceHandler.ModifySearchOptionsByRefer(ctx, in, out)
}

func (h *searchServiceHandler) UpsertResourceIndexDocs(ctx context.Context, in *ReqUpsertResourceIndexDocs, out *emptypb.Empty) error {
	return h.SearchServiceHandler.UpsertResourceIndexDocs(ctx, in, out)
}

func (h *searchServiceHandler) DeleteResourceIndexDocs(ctx context.Context, in *ReqDeleteResourceIndexDocs, out *emptypb.Empty) error {
	return h.SearchServiceHandler.DeleteResourceIndexDocs(ctx, in, out)
}

func (h *searchServiceHandler) SearchResourceDocs(ctx context.Context, in *ReqSearchResourceDocs, out *RspSearchResourceDocs) error {
	return h.SearchServiceHandler.SearchResourceDocs(ctx, in, out)
}
