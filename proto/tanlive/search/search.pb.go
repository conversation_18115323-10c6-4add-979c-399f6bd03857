// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/search/search.proto

package search

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 搜索对应的筛选项
type SearchOptionReferType int32

const (
	SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_UNSPECIFIED          SearchOptionReferType = 0
	SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_ATLAS                SearchOptionReferType = 1 // 图谱
	SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_INDUSTRY_RECOGNITION SearchOptionReferType = 2 // 行业认可
	SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_RESOURCE_SERIES      SearchOptionReferType = 3 // 资源系列
)

// Enum value maps for SearchOptionReferType.
var (
	SearchOptionReferType_name = map[int32]string{
		0: "SEARCH_OPTION_REFER_TYPE_UNSPECIFIED",
		1: "SEARCH_OPTION_REFER_TYPE_ATLAS",
		2: "SEARCH_OPTION_REFER_TYPE_INDUSTRY_RECOGNITION",
		3: "SEARCH_OPTION_REFER_TYPE_RESOURCE_SERIES",
	}
	SearchOptionReferType_value = map[string]int32{
		"SEARCH_OPTION_REFER_TYPE_UNSPECIFIED":          0,
		"SEARCH_OPTION_REFER_TYPE_ATLAS":                1,
		"SEARCH_OPTION_REFER_TYPE_INDUSTRY_RECOGNITION": 2,
		"SEARCH_OPTION_REFER_TYPE_RESOURCE_SERIES":      3,
	}
)

func (x SearchOptionReferType) Enum() *SearchOptionReferType {
	p := new(SearchOptionReferType)
	*p = x
	return p
}

func (x SearchOptionReferType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchOptionReferType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_search_search_proto_enumTypes[0].Descriptor()
}

func (SearchOptionReferType) Type() protoreflect.EnumType {
	return &file_tanlive_search_search_proto_enumTypes[0]
}

func (x SearchOptionReferType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchOptionReferType.Descriptor instead.
func (SearchOptionReferType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{0}
}

// 搜索对应的模块
type SearchOptionTargetType int32

const (
	SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_UNSPECIFIED SearchOptionTargetType = 0
	SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_TEAM        SearchOptionTargetType = 1 // 团队
	SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_PRODUCT     SearchOptionTargetType = 2 // 产品
	SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_RESOURCE    SearchOptionTargetType = 3 // 资源
)

// Enum value maps for SearchOptionTargetType.
var (
	SearchOptionTargetType_name = map[int32]string{
		0: "SEARCH_OPTION_TARGET_TYPE_UNSPECIFIED",
		1: "SEARCH_OPTION_TARGET_TYPE_TEAM",
		2: "SEARCH_OPTION_TARGET_TYPE_PRODUCT",
		3: "SEARCH_OPTION_TARGET_TYPE_RESOURCE",
	}
	SearchOptionTargetType_value = map[string]int32{
		"SEARCH_OPTION_TARGET_TYPE_UNSPECIFIED": 0,
		"SEARCH_OPTION_TARGET_TYPE_TEAM":        1,
		"SEARCH_OPTION_TARGET_TYPE_PRODUCT":     2,
		"SEARCH_OPTION_TARGET_TYPE_RESOURCE":    3,
	}
)

func (x SearchOptionTargetType) Enum() *SearchOptionTargetType {
	p := new(SearchOptionTargetType)
	*p = x
	return p
}

func (x SearchOptionTargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchOptionTargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_search_search_proto_enumTypes[1].Descriptor()
}

func (SearchOptionTargetType) Type() protoreflect.EnumType {
	return &file_tanlive_search_search_proto_enumTypes[1]
}

func (x SearchOptionTargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchOptionTargetType.Descriptor instead.
func (SearchOptionTargetType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{1}
}

// 文本文档
type TextDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文本内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *TextDoc) Reset() {
	*x = TextDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextDoc) ProtoMessage() {}

func (x *TextDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextDoc.ProtoReflect.Descriptor instead.
func (*TextDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{0}
}

func (x *TextDoc) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TextDoc) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

// 标签文档
type TagDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标签名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *TagDoc) Reset() {
	*x = TagDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagDoc) ProtoMessage() {}

func (x *TagDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagDoc.ProtoReflect.Descriptor instead.
func (*TagDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{1}
}

func (x *TagDoc) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TagDoc) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TagDoc) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

// 产品文档
type ProductDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 产品ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 产品名称
	Name []*TextDoc `protobuf:"bytes,2,rep,name=name,proto3" json:"name,omitempty"`
	// 简介
	BriefIntro []*TextDoc `protobuf:"bytes,3,rep,name=brief_intro,json=briefIntro,proto3" json:"brief_intro,omitempty"`
	// 面向用户
	UserOriented []*TagDoc `protobuf:"bytes,4,rep,name=user_oriented,json=userOriented,proto3" json:"user_oriented,omitempty"`
	// 类型
	Type []*TagDoc `protobuf:"bytes,5,rep,name=type,proto3" json:"type,omitempty"`
	// 行业认可
	IndustryRecognition []*TagDoc `protobuf:"bytes,6,rep,name=industry_recognition,json=industryRecognition,proto3" json:"industry_recognition,omitempty"`
	// 置顶
	Top uint32 `protobuf:"varint,7,opt,name=top,proto3" json:"top,omitempty"`
	// 最后更新时间
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
	// 关联图谱
	Graph []uint64 `protobuf:"varint,9,rep,packed,name=graph,proto3" json:"graph,omitempty"`
	// 源语言
	SourceLang string `protobuf:"bytes,10,opt,name=source_lang,json=sourceLang,proto3" json:"source_lang,omitempty"`
	// 可见类型
	VisibleType int32 `protobuf:"varint,11,opt,name=visible_type,json=visibleType,proto3" json:"visible_type,omitempty"`
	// 团队ID
	TeamId uint64 `protobuf:"varint,12,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// 团队全称
	TeamFullName []*TextDoc `protobuf:"bytes,13,rep,name=team_full_name,json=teamFullName,proto3" json:"team_full_name,omitempty"`
	// 团队全称
	TeamShortName []*TextDoc `protobuf:"bytes,14,rep,name=team_short_name,json=teamShortName,proto3" json:"team_short_name,omitempty"`
	// 团队所属行业
	TeamIndustry []*TagDoc `protobuf:"bytes,15,rep,name=team_industry,json=teamIndustry,proto3" json:"team_industry,omitempty"`
}

func (x *ProductDoc) Reset() {
	*x = ProductDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductDoc) ProtoMessage() {}

func (x *ProductDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductDoc.ProtoReflect.Descriptor instead.
func (*ProductDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{2}
}

func (x *ProductDoc) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProductDoc) GetName() []*TextDoc {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *ProductDoc) GetBriefIntro() []*TextDoc {
	if x != nil {
		return x.BriefIntro
	}
	return nil
}

func (x *ProductDoc) GetUserOriented() []*TagDoc {
	if x != nil {
		return x.UserOriented
	}
	return nil
}

func (x *ProductDoc) GetType() []*TagDoc {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *ProductDoc) GetIndustryRecognition() []*TagDoc {
	if x != nil {
		return x.IndustryRecognition
	}
	return nil
}

func (x *ProductDoc) GetTop() uint32 {
	if x != nil {
		return x.Top
	}
	return 0
}

func (x *ProductDoc) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

func (x *ProductDoc) GetGraph() []uint64 {
	if x != nil {
		return x.Graph
	}
	return nil
}

func (x *ProductDoc) GetSourceLang() string {
	if x != nil {
		return x.SourceLang
	}
	return ""
}

func (x *ProductDoc) GetVisibleType() int32 {
	if x != nil {
		return x.VisibleType
	}
	return 0
}

func (x *ProductDoc) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *ProductDoc) GetTeamFullName() []*TextDoc {
	if x != nil {
		return x.TeamFullName
	}
	return nil
}

func (x *ProductDoc) GetTeamShortName() []*TextDoc {
	if x != nil {
		return x.TeamShortName
	}
	return nil
}

func (x *ProductDoc) GetTeamIndustry() []*TagDoc {
	if x != nil {
		return x.TeamIndustry
	}
	return nil
}

// 团队文档
type TeamDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 团队ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 全称
	FullName []*TextDoc `protobuf:"bytes,2,rep,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	// 简称
	ShortName []*TextDoc `protobuf:"bytes,3,rep,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	// 简介
	BriefIntro []*TextDoc `protobuf:"bytes,4,rep,name=brief_intro,json=briefIntro,proto3" json:"brief_intro,omitempty"`
	// 行业
	Industry []*TagDoc `protobuf:"bytes,5,rep,name=industry,proto3" json:"industry,omitempty"`
	// 行业认可
	IndustryRecognition []*TagDoc `protobuf:"bytes,6,rep,name=industry_recognition,json=industryRecognition,proto3" json:"industry_recognition,omitempty"`
	// 类型
	Type []*TagDoc `protobuf:"bytes,7,rep,name=type,proto3" json:"type,omitempty"`
	// 关联图谱
	Graph []uint64 `protobuf:"varint,8,rep,packed,name=graph,proto3" json:"graph,omitempty"`
	// 发展阶段
	FinancingStage []*TagDoc `protobuf:"bytes,9,rep,name=financing_stage,json=financingStage,proto3" json:"financing_stage,omitempty"`
	// 所在地
	Location []*AddressDoc `protobuf:"bytes,10,rep,name=location,proto3" json:"location,omitempty"`
	// 服务地区
	ServiceRegion []*AddressDoc `protobuf:"bytes,11,rep,name=service_region,json=serviceRegion,proto3" json:"service_region,omitempty"`
	// 是否已认证
	IsVerified bool `protobuf:"varint,12,opt,name=is_verified,json=isVerified,proto3" json:"is_verified,omitempty"`
	// 是否发布产品
	HasProduct bool `protobuf:"varint,13,opt,name=has_product,json=hasProduct,proto3" json:"has_product,omitempty"`
	// 是否发布资源
	HasResource bool `protobuf:"varint,14,opt,name=has_resource,json=hasResource,proto3" json:"has_resource,omitempty"`
	// 共创等级
	Level uint32 `protobuf:"varint,15,opt,name=level,proto3" json:"level,omitempty"`
	// 推荐值
	Recommend uint32 `protobuf:"varint,16,opt,name=recommend,proto3" json:"recommend,omitempty"`
	// 更新时间
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
	// 源语言
	SourceLang string `protobuf:"bytes,18,opt,name=source_lang,json=sourceLang,proto3" json:"source_lang,omitempty"`
	// 团队LOGO
	LogoUrl string `protobuf:"bytes,19,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 置顶值
	Top int32 `protobuf:"varint,20,opt,name=top,proto3" json:"top,omitempty"`
}

func (x *TeamDoc) Reset() {
	*x = TeamDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamDoc) ProtoMessage() {}

func (x *TeamDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamDoc.ProtoReflect.Descriptor instead.
func (*TeamDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{3}
}

func (x *TeamDoc) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeamDoc) GetFullName() []*TextDoc {
	if x != nil {
		return x.FullName
	}
	return nil
}

func (x *TeamDoc) GetShortName() []*TextDoc {
	if x != nil {
		return x.ShortName
	}
	return nil
}

func (x *TeamDoc) GetBriefIntro() []*TextDoc {
	if x != nil {
		return x.BriefIntro
	}
	return nil
}

func (x *TeamDoc) GetIndustry() []*TagDoc {
	if x != nil {
		return x.Industry
	}
	return nil
}

func (x *TeamDoc) GetIndustryRecognition() []*TagDoc {
	if x != nil {
		return x.IndustryRecognition
	}
	return nil
}

func (x *TeamDoc) GetType() []*TagDoc {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *TeamDoc) GetGraph() []uint64 {
	if x != nil {
		return x.Graph
	}
	return nil
}

func (x *TeamDoc) GetFinancingStage() []*TagDoc {
	if x != nil {
		return x.FinancingStage
	}
	return nil
}

func (x *TeamDoc) GetLocation() []*AddressDoc {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *TeamDoc) GetServiceRegion() []*AddressDoc {
	if x != nil {
		return x.ServiceRegion
	}
	return nil
}

func (x *TeamDoc) GetIsVerified() bool {
	if x != nil {
		return x.IsVerified
	}
	return false
}

func (x *TeamDoc) GetHasProduct() bool {
	if x != nil {
		return x.HasProduct
	}
	return false
}

func (x *TeamDoc) GetHasResource() bool {
	if x != nil {
		return x.HasResource
	}
	return false
}

func (x *TeamDoc) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *TeamDoc) GetRecommend() uint32 {
	if x != nil {
		return x.Recommend
	}
	return 0
}

func (x *TeamDoc) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

func (x *TeamDoc) GetSourceLang() string {
	if x != nil {
		return x.SourceLang
	}
	return ""
}

func (x *TeamDoc) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *TeamDoc) GetTop() int32 {
	if x != nil {
		return x.Top
	}
	return 0
}

// 地点文档
type PlaceDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地点ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 地点名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 详细地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// 纬度
	Lat float64 `protobuf:"fixed64,4,opt,name=lat,proto3" json:"lat,omitempty"`
	// 经度
	Lng float64 `protobuf:"fixed64,5,opt,name=lng,proto3" json:"lng,omitempty"`
}

func (x *PlaceDoc) Reset() {
	*x = PlaceDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceDoc) ProtoMessage() {}

func (x *PlaceDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceDoc.ProtoReflect.Descriptor instead.
func (*PlaceDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{4}
}

func (x *PlaceDoc) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PlaceDoc) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlaceDoc) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *PlaceDoc) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *PlaceDoc) GetLng() float64 {
	if x != nil {
		return x.Lng
	}
	return 0
}

// 地址文档
type AddressDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 大洲
	Continent *PlaceDoc `protobuf:"bytes,1,opt,name=continent,proto3" json:"continent,omitempty"`
	// 国家
	Country *PlaceDoc `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	// 一级行政区划
	Level1 *PlaceDoc `protobuf:"bytes,3,opt,name=level1,proto3" json:"level1,omitempty"`
	// 二级行政区划
	Level2 *PlaceDoc `protobuf:"bytes,4,opt,name=level2,proto3" json:"level2,omitempty"`
	// 详细地址
	Detail *PlaceDoc `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail,omitempty"`
	// 语言
	Lang string `protobuf:"bytes,6,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *AddressDoc) Reset() {
	*x = AddressDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressDoc) ProtoMessage() {}

func (x *AddressDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressDoc.ProtoReflect.Descriptor instead.
func (*AddressDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{5}
}

func (x *AddressDoc) GetContinent() *PlaceDoc {
	if x != nil {
		return x.Continent
	}
	return nil
}

func (x *AddressDoc) GetCountry() *PlaceDoc {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *AddressDoc) GetLevel1() *PlaceDoc {
	if x != nil {
		return x.Level1
	}
	return nil
}

func (x *AddressDoc) GetLevel2() *PlaceDoc {
	if x != nil {
		return x.Level2
	}
	return nil
}

func (x *AddressDoc) GetDetail() *PlaceDoc {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *AddressDoc) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

// 搜索筛选项
type SearchOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetType SearchOptionTargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=tanlive.search.SearchOptionTargetType" json:"target_type,omitempty"`
	ReferType  SearchOptionReferType  `protobuf:"varint,2,opt,name=refer_type,json=referType,proto3,enum=tanlive.search.SearchOptionReferType" json:"refer_type,omitempty"`
	Language   string                 `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	Name       string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Weight     uint32                 `protobuf:"varint,5,opt,name=weight,proto3" json:"weight,omitempty"`
	ReferId    int64                  `protobuf:"varint,6,opt,name=refer_id,json=referId,proto3" json:"refer_id,omitempty"`
	Status     base.DisableState      `protobuf:"varint,8,opt,name=status,proto3,enum=tanlive.base.DisableState" json:"status,omitempty"`
	Id         int64                  `protobuf:"varint,9,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SearchOption) Reset() {
	*x = SearchOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchOption) ProtoMessage() {}

func (x *SearchOption) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchOption.ProtoReflect.Descriptor instead.
func (*SearchOption) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{6}
}

func (x *SearchOption) GetTargetType() SearchOptionTargetType {
	if x != nil {
		return x.TargetType
	}
	return SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_UNSPECIFIED
}

func (x *SearchOption) GetReferType() SearchOptionReferType {
	if x != nil {
		return x.ReferType
	}
	return SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_UNSPECIFIED
}

func (x *SearchOption) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *SearchOption) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchOption) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *SearchOption) GetReferId() int64 {
	if x != nil {
		return x.ReferId
	}
	return 0
}

func (x *SearchOption) GetStatus() base.DisableState {
	if x != nil {
		return x.Status
	}
	return base.DisableState(0)
}

func (x *SearchOption) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 搜索提示语
type SearchPrompt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetType SearchOptionTargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=tanlive.search.SearchOptionTargetType" json:"target_type,omitempty"`
	ReferType  SearchOptionReferType  `protobuf:"varint,2,opt,name=refer_type,json=referType,proto3,enum=tanlive.search.SearchOptionReferType" json:"refer_type,omitempty"`
	Language   string                 `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	Id         int64                  `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	Content    string                 `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *SearchPrompt) Reset() {
	*x = SearchPrompt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPrompt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPrompt) ProtoMessage() {}

func (x *SearchPrompt) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPrompt.ProtoReflect.Descriptor instead.
func (*SearchPrompt) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{7}
}

func (x *SearchPrompt) GetTargetType() SearchOptionTargetType {
	if x != nil {
		return x.TargetType
	}
	return SearchOptionTargetType_SEARCH_OPTION_TARGET_TYPE_UNSPECIFIED
}

func (x *SearchPrompt) GetReferType() SearchOptionReferType {
	if x != nil {
		return x.ReferType
	}
	return SearchOptionReferType_SEARCH_OPTION_REFER_TYPE_UNSPECIFIED
}

func (x *SearchPrompt) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *SearchPrompt) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchPrompt) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// ResourceDoc 资源文档
type ResourceDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索 资源标题，简介，发起方、行业
	// 资源标题
	Name []*TextDoc `protobuf:"bytes,1,rep,name=name,proto3" json:"name,omitempty"`
	// 资源简述
	Introduction []*TextDoc `protobuf:"bytes,2,rep,name=introduction,proto3" json:"introduction,omitempty"`
	// 资源类型
	Type []*TagDoc `protobuf:"bytes,3,rep,name=type,proto3" json:"type,omitempty"`
	// 主办方
	OriginatorName []*TextDoc `protobuf:"bytes,4,rep,name=OriginatorName,proto3" json:"OriginatorName,omitempty"`
	// 出资规模-最小值 单位固定百万人民币
	ProvideFundsMin float32 `protobuf:"fixed32,5,opt,name=provide_funds_min,json=provideFundsMin,proto3" json:"provide_funds_min,omitempty"`
	// 出资规模-最大值 单位固定百万人民币
	ProvideFundsMax float32 `protobuf:"fixed32,6,opt,name=provide_funds_max,json=provideFundsMax,proto3" json:"provide_funds_max,omitempty"`
	// 排序 最新更新(last_update_date desc) 金额最高(provide_funds_avg desc) 截止日期最早(end_time desc) 截止日期最晚(end_time asc)
	Orders []string `protobuf:"bytes,7,rep,name=orders,proto3" json:"orders,omitempty"`
	// 面向行业
	IndustryTag []*TagDoc `protobuf:"bytes,8,rep,name=industry_tag,json=industryTag,proto3" json:"industry_tag,omitempty"`
	// 团队阶段
	TeamStageTag []*TagDoc `protobuf:"bytes,10,rep,name=team_stage_tag,json=teamStageTag,proto3" json:"team_stage_tag,omitempty"`
	// 产品成熟度-下限 不搜索默认-1
	ProductMaturityMin int32 `protobuf:"varint,11,opt,name=product_maturity_min,json=productMaturityMin,proto3" json:"product_maturity_min,omitempty"`
	// 产品成熟-上限 不搜索默认-1
	ProductMaturityMax int32  `protobuf:"varint,12,opt,name=product_maturity_max,json=productMaturityMax,proto3" json:"product_maturity_max,omitempty"`
	Id                 uint64 `protobuf:"varint,14,opt,name=id,proto3" json:"id,omitempty"`
	// 头像
	ResImageUrl string `protobuf:"bytes,15,opt,name=res_image_url,json=resImageUrl,proto3" json:"res_image_url,omitempty"`
	// 关联方
	RelaterName []*TextDoc `protobuf:"bytes,16,rep,name=RelaterName,proto3" json:"RelaterName,omitempty"`
	// 置顶位置
	TopPos int32 `protobuf:"varint,17,opt,name=top_pos,json=topPos,proto3" json:"top_pos,omitempty"`
	// 排序权重
	OrderBy int32 `protobuf:"varint,18,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// 资源权限 1全网公开 2用户
	ShowScope uint64 `protobuf:"varint,19,opt,name=show_scope,json=showScope,proto3" json:"show_scope,omitempty"`
	// 共享编辑团队
	ShareEditTeam []uint64 `protobuf:"varint,20,rep,packed,name=share_edit_team,json=shareEditTeam,proto3" json:"share_edit_team,omitempty"`
	// 共享编辑人
	ShareEditUser []uint64 `protobuf:"varint,21,rep,packed,name=share_edit_user,json=shareEditUser,proto3" json:"share_edit_user,omitempty"`
	// 有效期类型 ONE单次资源 LONG长期有效 CYCLE周期
	TimeValidityType string `protobuf:"bytes,22,opt,name=time_validity_type,json=timeValidityType,proto3" json:"time_validity_type,omitempty"`
	// 源语言
	SourceLang string `protobuf:"bytes,29,opt,name=source_lang,json=sourceLang,proto3" json:"source_lang,omitempty"`
	// 资源系列
	SeriesTag []*TagDoc `protobuf:"bytes,30,rep,name=series_tag,json=seriesTag,proto3" json:"series_tag,omitempty"`
	// 团队行业认可标签
	TeamIndustryRecognitionTag []*TagDoc `protobuf:"bytes,31,rep,name=team_industry_recognition_tag,json=teamIndustryRecognitionTag,proto3" json:"team_industry_recognition_tag,omitempty"`
	// 产品行业认可标签
	ProductIndustryRecognitionTag []*TagDoc `protobuf:"bytes,32,rep,name=product_industry_recognition_tag,json=productIndustryRecognitionTag,proto3" json:"product_industry_recognition_tag,omitempty"`
	// 日历开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,33,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 日历结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,34,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 举办地点
	Location []*AddressDoc `protobuf:"bytes,35,rep,name=location,proto3" json:"location,omitempty"`
	// 受众地点
	TargetRegions []*AddressDoc `protobuf:"bytes,36,rep,name=target_regions,json=targetRegions,proto3" json:"target_regions,omitempty"`
	// 最后更新时间
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,37,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
}

func (x *ResourceDoc) Reset() {
	*x = ResourceDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_search_search_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDoc) ProtoMessage() {}

func (x *ResourceDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_search_search_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDoc.ProtoReflect.Descriptor instead.
func (*ResourceDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_search_search_proto_rawDescGZIP(), []int{8}
}

func (x *ResourceDoc) GetName() []*TextDoc {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *ResourceDoc) GetIntroduction() []*TextDoc {
	if x != nil {
		return x.Introduction
	}
	return nil
}

func (x *ResourceDoc) GetType() []*TagDoc {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *ResourceDoc) GetOriginatorName() []*TextDoc {
	if x != nil {
		return x.OriginatorName
	}
	return nil
}

func (x *ResourceDoc) GetProvideFundsMin() float32 {
	if x != nil {
		return x.ProvideFundsMin
	}
	return 0
}

func (x *ResourceDoc) GetProvideFundsMax() float32 {
	if x != nil {
		return x.ProvideFundsMax
	}
	return 0
}

func (x *ResourceDoc) GetOrders() []string {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ResourceDoc) GetIndustryTag() []*TagDoc {
	if x != nil {
		return x.IndustryTag
	}
	return nil
}

func (x *ResourceDoc) GetTeamStageTag() []*TagDoc {
	if x != nil {
		return x.TeamStageTag
	}
	return nil
}

func (x *ResourceDoc) GetProductMaturityMin() int32 {
	if x != nil {
		return x.ProductMaturityMin
	}
	return 0
}

func (x *ResourceDoc) GetProductMaturityMax() int32 {
	if x != nil {
		return x.ProductMaturityMax
	}
	return 0
}

func (x *ResourceDoc) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResourceDoc) GetResImageUrl() string {
	if x != nil {
		return x.ResImageUrl
	}
	return ""
}

func (x *ResourceDoc) GetRelaterName() []*TextDoc {
	if x != nil {
		return x.RelaterName
	}
	return nil
}

func (x *ResourceDoc) GetTopPos() int32 {
	if x != nil {
		return x.TopPos
	}
	return 0
}

func (x *ResourceDoc) GetOrderBy() int32 {
	if x != nil {
		return x.OrderBy
	}
	return 0
}

func (x *ResourceDoc) GetShowScope() uint64 {
	if x != nil {
		return x.ShowScope
	}
	return 0
}

func (x *ResourceDoc) GetShareEditTeam() []uint64 {
	if x != nil {
		return x.ShareEditTeam
	}
	return nil
}

func (x *ResourceDoc) GetShareEditUser() []uint64 {
	if x != nil {
		return x.ShareEditUser
	}
	return nil
}

func (x *ResourceDoc) GetTimeValidityType() string {
	if x != nil {
		return x.TimeValidityType
	}
	return ""
}

func (x *ResourceDoc) GetSourceLang() string {
	if x != nil {
		return x.SourceLang
	}
	return ""
}

func (x *ResourceDoc) GetSeriesTag() []*TagDoc {
	if x != nil {
		return x.SeriesTag
	}
	return nil
}

func (x *ResourceDoc) GetTeamIndustryRecognitionTag() []*TagDoc {
	if x != nil {
		return x.TeamIndustryRecognitionTag
	}
	return nil
}

func (x *ResourceDoc) GetProductIndustryRecognitionTag() []*TagDoc {
	if x != nil {
		return x.ProductIndustryRecognitionTag
	}
	return nil
}

func (x *ResourceDoc) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ResourceDoc) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ResourceDoc) GetLocation() []*AddressDoc {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *ResourceDoc) GetTargetRegions() []*AddressDoc {
	if x != nil {
		return x.TargetRegions
	}
	return nil
}

func (x *ResourceDoc) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

var File_tanlive_search_search_proto protoreflect.FileDescriptor

var file_tanlive_search_search_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62,
	0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x31,
	0x0a, 0x07, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x22, 0x40, 0x0a, 0x06, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c,
	0x61, 0x6e, 0x67, 0x22, 0xbf, 0x05, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44,
	0x6f, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x38, 0x0a, 0x0b, 0x62, 0x72, 0x69, 0x65, 0x66, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x0a, 0x62,
	0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x12, 0x3b, 0x0a, 0x0d, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x4f, 0x72,
	0x69, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x49, 0x0a, 0x14, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x13, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74,
	0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x74, 0x6f, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x6f, 0x70, 0x12,
	0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c,
	0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x74, 0x65, 0x61, 0x6d,
	0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x0c, 0x74, 0x65, 0x61, 0x6d, 0x46,
	0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0f, 0x74, 0x65, 0x61, 0x6d, 0x5f,
	0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x0d, 0x74, 0x65, 0x61, 0x6d, 0x53,
	0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x74, 0x65, 0x61, 0x6d,
	0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x0c, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x22, 0xeb, 0x06, 0x0a, 0x07, 0x54, 0x65, 0x61, 0x6d, 0x44, 0x6f,
	0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x34, 0x0a, 0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x08, 0x66,
	0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x44, 0x6f, 0x63, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x38, 0x0a, 0x0b, 0x62, 0x72, 0x69, 0x65, 0x66, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x0a, 0x62,
	0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x12, 0x32, 0x0a, 0x08, 0x69, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61, 0x67,
	0x44, 0x6f, 0x63, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12, 0x49, 0x0a,
	0x14, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61, 0x67,
	0x44, 0x6f, 0x63, 0x52, 0x13, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x52, 0x65, 0x63,
	0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x05, 0x67, 0x72, 0x61, 0x70, 0x68, 0x12, 0x3f, 0x0a, 0x0f, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x0e, 0x66, 0x69, 0x6e,
	0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x6f, 0x63, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x44, 0x6f, 0x63, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x61, 0x73, 0x5f, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x68, 0x61,
	0x73, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x68, 0x61, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12,
	0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6c, 0x61, 0x6e, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72,
	0x6c, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x6f, 0x70, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x74, 0x6f, 0x70, 0x22, 0x6c, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x6c, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6e,
	0x67, 0x22, 0xa2, 0x02, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x6f, 0x63,
	0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65,
	0x44, 0x6f, 0x63, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x30, 0x0a, 0x06,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x12, 0x30,
	0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32,
	0x12, 0x30, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xc4, 0x02, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x44, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x72, 0x65, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xe3, 0x01,
	0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x47,
	0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x22, 0xb8, 0x0b, 0x0a, 0x0b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x44, 0x6f, 0x63, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x3b, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52,
	0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61, 0x67,
	0x44, 0x6f, 0x63, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0e, 0x4f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x0e, 0x4f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x5f, 0x6d, 0x69, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x46, 0x75,
	0x6e, 0x64, 0x73, 0x4d, 0x69, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x4d,
	0x61, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x69, 0x6e,
	0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x0b, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74,
	0x72, 0x79, 0x54, 0x61, 0x67, 0x12, 0x3c, 0x0a, 0x0e, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54,
	0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x0c, 0x74, 0x65, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x54, 0x61, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6d,
	0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4d, 0x61, 0x74, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x4d, 0x69, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4d, 0x61, 0x74, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x73, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x39, 0x0a, 0x0b, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x6f, 0x63, 0x52, 0x0b, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x6f, 0x70, 0x5f, 0x70, 0x6f,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x50, 0x6f, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x73, 0x68, 0x6f, 0x77, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x14, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x45, 0x64, 0x69, 0x74, 0x54, 0x65, 0x61,
	0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x15, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0d, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61,
	0x67, 0x44, 0x6f, 0x63, 0x52, 0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x61, 0x67, 0x12,
	0x59, 0x0a, 0x1d, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x67,
	0x18, 0x1f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x1a,
	0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x12, 0x5f, 0x0a, 0x20, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x20,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x54, 0x61, 0x67, 0x44, 0x6f, 0x63, 0x52, 0x1d, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x52, 0x65, 0x63,
	0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x12, 0x39, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x36, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x23, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x6f, 0x63, 0x52, 0x08, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x24, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x6f, 0x63, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e,
	0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x2a, 0xc6,
	0x01, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x45, 0x41, 0x52,
	0x43, 0x48, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x54, 0x4c, 0x41, 0x53, 0x10, 0x01, 0x12, 0x31, 0x0a, 0x2d, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48,
	0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x47, 0x4e, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x45, 0x41,
	0x52, 0x43, 0x48, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53,
	0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x03, 0x2a, 0xb6, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a,
	0x1e, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x10,
	0x01, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x45, 0x41, 0x52,
	0x43, 0x48, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x03,
	0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74,
	0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_search_search_proto_rawDescOnce sync.Once
	file_tanlive_search_search_proto_rawDescData = file_tanlive_search_search_proto_rawDesc
)

func file_tanlive_search_search_proto_rawDescGZIP() []byte {
	file_tanlive_search_search_proto_rawDescOnce.Do(func() {
		file_tanlive_search_search_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_search_search_proto_rawDescData)
	})
	return file_tanlive_search_search_proto_rawDescData
}

var file_tanlive_search_search_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_tanlive_search_search_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_tanlive_search_search_proto_goTypes = []interface{}{
	(SearchOptionReferType)(0),    // 0: tanlive.search.SearchOptionReferType
	(SearchOptionTargetType)(0),   // 1: tanlive.search.SearchOptionTargetType
	(*TextDoc)(nil),               // 2: tanlive.search.TextDoc
	(*TagDoc)(nil),                // 3: tanlive.search.TagDoc
	(*ProductDoc)(nil),            // 4: tanlive.search.ProductDoc
	(*TeamDoc)(nil),               // 5: tanlive.search.TeamDoc
	(*PlaceDoc)(nil),              // 6: tanlive.search.PlaceDoc
	(*AddressDoc)(nil),            // 7: tanlive.search.AddressDoc
	(*SearchOption)(nil),          // 8: tanlive.search.SearchOption
	(*SearchPrompt)(nil),          // 9: tanlive.search.SearchPrompt
	(*ResourceDoc)(nil),           // 10: tanlive.search.ResourceDoc
	(*timestamppb.Timestamp)(nil), // 11: google.protobuf.Timestamp
	(base.DisableState)(0),        // 12: tanlive.base.DisableState
}
var file_tanlive_search_search_proto_depIdxs = []int32{
	2,  // 0: tanlive.search.ProductDoc.name:type_name -> tanlive.search.TextDoc
	2,  // 1: tanlive.search.ProductDoc.brief_intro:type_name -> tanlive.search.TextDoc
	3,  // 2: tanlive.search.ProductDoc.user_oriented:type_name -> tanlive.search.TagDoc
	3,  // 3: tanlive.search.ProductDoc.type:type_name -> tanlive.search.TagDoc
	3,  // 4: tanlive.search.ProductDoc.industry_recognition:type_name -> tanlive.search.TagDoc
	11, // 5: tanlive.search.ProductDoc.last_update_date:type_name -> google.protobuf.Timestamp
	2,  // 6: tanlive.search.ProductDoc.team_full_name:type_name -> tanlive.search.TextDoc
	2,  // 7: tanlive.search.ProductDoc.team_short_name:type_name -> tanlive.search.TextDoc
	3,  // 8: tanlive.search.ProductDoc.team_industry:type_name -> tanlive.search.TagDoc
	2,  // 9: tanlive.search.TeamDoc.full_name:type_name -> tanlive.search.TextDoc
	2,  // 10: tanlive.search.TeamDoc.short_name:type_name -> tanlive.search.TextDoc
	2,  // 11: tanlive.search.TeamDoc.brief_intro:type_name -> tanlive.search.TextDoc
	3,  // 12: tanlive.search.TeamDoc.industry:type_name -> tanlive.search.TagDoc
	3,  // 13: tanlive.search.TeamDoc.industry_recognition:type_name -> tanlive.search.TagDoc
	3,  // 14: tanlive.search.TeamDoc.type:type_name -> tanlive.search.TagDoc
	3,  // 15: tanlive.search.TeamDoc.financing_stage:type_name -> tanlive.search.TagDoc
	7,  // 16: tanlive.search.TeamDoc.location:type_name -> tanlive.search.AddressDoc
	7,  // 17: tanlive.search.TeamDoc.service_region:type_name -> tanlive.search.AddressDoc
	11, // 18: tanlive.search.TeamDoc.last_update_date:type_name -> google.protobuf.Timestamp
	6,  // 19: tanlive.search.AddressDoc.continent:type_name -> tanlive.search.PlaceDoc
	6,  // 20: tanlive.search.AddressDoc.country:type_name -> tanlive.search.PlaceDoc
	6,  // 21: tanlive.search.AddressDoc.level1:type_name -> tanlive.search.PlaceDoc
	6,  // 22: tanlive.search.AddressDoc.level2:type_name -> tanlive.search.PlaceDoc
	6,  // 23: tanlive.search.AddressDoc.detail:type_name -> tanlive.search.PlaceDoc
	1,  // 24: tanlive.search.SearchOption.target_type:type_name -> tanlive.search.SearchOptionTargetType
	0,  // 25: tanlive.search.SearchOption.refer_type:type_name -> tanlive.search.SearchOptionReferType
	12, // 26: tanlive.search.SearchOption.status:type_name -> tanlive.base.DisableState
	1,  // 27: tanlive.search.SearchPrompt.target_type:type_name -> tanlive.search.SearchOptionTargetType
	0,  // 28: tanlive.search.SearchPrompt.refer_type:type_name -> tanlive.search.SearchOptionReferType
	2,  // 29: tanlive.search.ResourceDoc.name:type_name -> tanlive.search.TextDoc
	2,  // 30: tanlive.search.ResourceDoc.introduction:type_name -> tanlive.search.TextDoc
	3,  // 31: tanlive.search.ResourceDoc.type:type_name -> tanlive.search.TagDoc
	2,  // 32: tanlive.search.ResourceDoc.OriginatorName:type_name -> tanlive.search.TextDoc
	3,  // 33: tanlive.search.ResourceDoc.industry_tag:type_name -> tanlive.search.TagDoc
	3,  // 34: tanlive.search.ResourceDoc.team_stage_tag:type_name -> tanlive.search.TagDoc
	2,  // 35: tanlive.search.ResourceDoc.RelaterName:type_name -> tanlive.search.TextDoc
	3,  // 36: tanlive.search.ResourceDoc.series_tag:type_name -> tanlive.search.TagDoc
	3,  // 37: tanlive.search.ResourceDoc.team_industry_recognition_tag:type_name -> tanlive.search.TagDoc
	3,  // 38: tanlive.search.ResourceDoc.product_industry_recognition_tag:type_name -> tanlive.search.TagDoc
	11, // 39: tanlive.search.ResourceDoc.start_time:type_name -> google.protobuf.Timestamp
	11, // 40: tanlive.search.ResourceDoc.end_time:type_name -> google.protobuf.Timestamp
	7,  // 41: tanlive.search.ResourceDoc.location:type_name -> tanlive.search.AddressDoc
	7,  // 42: tanlive.search.ResourceDoc.target_regions:type_name -> tanlive.search.AddressDoc
	11, // 43: tanlive.search.ResourceDoc.last_update_date:type_name -> google.protobuf.Timestamp
	44, // [44:44] is the sub-list for method output_type
	44, // [44:44] is the sub-list for method input_type
	44, // [44:44] is the sub-list for extension type_name
	44, // [44:44] is the sub-list for extension extendee
	0,  // [0:44] is the sub-list for field type_name
}

func init() { file_tanlive_search_search_proto_init() }
func file_tanlive_search_search_proto_init() {
	if File_tanlive_search_search_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_search_search_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_search_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_search_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_search_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_search_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_search_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_search_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_search_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPrompt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_search_search_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_search_search_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_search_search_proto_goTypes,
		DependencyIndexes: file_tanlive_search_search_proto_depIdxs,
		EnumInfos:         file_tanlive_search_search_proto_enumTypes,
		MessageInfos:      file_tanlive_search_search_proto_msgTypes,
	}.Build()
	File_tanlive_search_search_proto = out.File
	file_tanlive_search_search_proto_rawDesc = nil
	file_tanlive_search_search_proto_goTypes = nil
	file_tanlive_search_search_proto_depIdxs = nil
}
