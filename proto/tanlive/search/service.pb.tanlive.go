// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package search

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ProductDocs": "required,min=1",
	}, &ReqUpsertProductIndexDocs{})
}

func (x *ReqUpsertProductIndexDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ByTeamId": "min=1",
	}, &ReqDeleteProductIndexDocs{})
}

func (x *ReqDeleteProductIndexDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ProductId": "required,min=1,dive,min=1",
	}, &ReqDeleteProductIndexDocs_ProductId{})
}

func (x *ReqDeleteProductIndexDocs_ProductId) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TeamDocs": "required,min=1",
	}, &ReqUpsertTeamIndexDocs{})
}

func (x *ReqUpsertTeamIndexDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TeamId": "required,min=1,dive,min=1",
	}, &ReqDeleteTeamIndexDocs{})
}

func (x *ReqDeleteTeamIndexDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ResourceDocs": "required,min=1",
	}, &ReqUpsertResourceIndexDocs{})
}

func (x *ReqUpsertResourceIndexDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ResourceId": "required,min=1,dive,min=1",
	}, &ReqDeleteResourceIndexDocs{})
}

func (x *ReqDeleteResourceIndexDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqUpsertProductIndexDocs) MaskInLog() any {
	if x == nil {
		return (*ReqUpsertProductIndexDocs)(nil)
	}

	y := proto.Clone(x).(*ReqUpsertProductIndexDocs)
	for k, v := range y.ProductDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ProductDocs[k] = vv.MaskInLog().(*ProductDoc)
		}
	}

	return y
}

func (x *ReqUpsertProductIndexDocs) MaskInRpc() any {
	if x == nil {
		return (*ReqUpsertProductIndexDocs)(nil)
	}

	y := x
	for k, v := range y.ProductDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ProductDocs[k] = vv.MaskInRpc().(*ProductDoc)
		}
	}

	return y
}

func (x *ReqUpsertProductIndexDocs) MaskInBff() any {
	if x == nil {
		return (*ReqUpsertProductIndexDocs)(nil)
	}

	y := x
	for k, v := range y.ProductDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ProductDocs[k] = vv.MaskInBff().(*ProductDoc)
		}
	}

	return y
}

func (x *ReqDeleteProductIndexDocs) MaskInLog() any {
	if x == nil {
		return (*ReqDeleteProductIndexDocs)(nil)
	}

	y := proto.Clone(x).(*ReqDeleteProductIndexDocs)
	switch v := y.Condition.(type) {
	case *ReqDeleteProductIndexDocs_ByProductId:
		if vv, ok := any(v.ByProductId).(interface{ MaskInLog() any }); ok {
			v.ByProductId = vv.MaskInLog().(*ReqDeleteProductIndexDocs_ProductId)
		}
	}

	return y
}

func (x *ReqDeleteProductIndexDocs) MaskInRpc() any {
	if x == nil {
		return (*ReqDeleteProductIndexDocs)(nil)
	}

	y := x
	switch v := y.Condition.(type) {
	case *ReqDeleteProductIndexDocs_ByProductId:
		if vv, ok := any(v.ByProductId).(interface{ MaskInRpc() any }); ok {
			v.ByProductId = vv.MaskInRpc().(*ReqDeleteProductIndexDocs_ProductId)
		}
	}

	return y
}

func (x *ReqDeleteProductIndexDocs) MaskInBff() any {
	if x == nil {
		return (*ReqDeleteProductIndexDocs)(nil)
	}

	y := x
	switch v := y.Condition.(type) {
	case *ReqDeleteProductIndexDocs_ByProductId:
		if vv, ok := any(v.ByProductId).(interface{ MaskInBff() any }); ok {
			v.ByProductId = vv.MaskInBff().(*ReqDeleteProductIndexDocs_ProductId)
		}
	}

	return y
}

func (x *ReqSearchProductDocs) MaskInLog() any {
	if x == nil {
		return (*ReqSearchProductDocs)(nil)
	}

	y := proto.Clone(x).(*ReqSearchProductDocs)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqSearchProductDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqSearchProductDocs) MaskInRpc() any {
	if x == nil {
		return (*ReqSearchProductDocs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqSearchProductDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqSearchProductDocs) MaskInBff() any {
	if x == nil {
		return (*ReqSearchProductDocs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqSearchProductDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspSearchProductDocs) MaskInLog() any {
	if x == nil {
		return (*RspSearchProductDocs)(nil)
	}

	y := proto.Clone(x).(*RspSearchProductDocs)
	for k, v := range y.ProductDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ProductDocs[k] = vv.MaskInLog().(*ProductDoc)
		}
	}

	return y
}

func (x *RspSearchProductDocs) MaskInRpc() any {
	if x == nil {
		return (*RspSearchProductDocs)(nil)
	}

	y := x
	for k, v := range y.ProductDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ProductDocs[k] = vv.MaskInRpc().(*ProductDoc)
		}
	}

	return y
}

func (x *RspSearchProductDocs) MaskInBff() any {
	if x == nil {
		return (*RspSearchProductDocs)(nil)
	}

	y := x
	for k, v := range y.ProductDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ProductDocs[k] = vv.MaskInBff().(*ProductDoc)
		}
	}

	return y
}

func (x *ReqUpsertTeamIndexDocs) MaskInLog() any {
	if x == nil {
		return (*ReqUpsertTeamIndexDocs)(nil)
	}

	y := proto.Clone(x).(*ReqUpsertTeamIndexDocs)
	for k, v := range y.TeamDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamDocs[k] = vv.MaskInLog().(*TeamDoc)
		}
	}

	return y
}

func (x *ReqUpsertTeamIndexDocs) MaskInRpc() any {
	if x == nil {
		return (*ReqUpsertTeamIndexDocs)(nil)
	}

	y := x
	for k, v := range y.TeamDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamDocs[k] = vv.MaskInRpc().(*TeamDoc)
		}
	}

	return y
}

func (x *ReqUpsertTeamIndexDocs) MaskInBff() any {
	if x == nil {
		return (*ReqUpsertTeamIndexDocs)(nil)
	}

	y := x
	for k, v := range y.TeamDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamDocs[k] = vv.MaskInBff().(*TeamDoc)
		}
	}

	return y
}

func (x *ReqSearchTeamDocs) MaskInLog() any {
	if x == nil {
		return (*ReqSearchTeamDocs)(nil)
	}

	y := proto.Clone(x).(*ReqSearchTeamDocs)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqSearchTeamDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqSearchTeamDocs) MaskInRpc() any {
	if x == nil {
		return (*ReqSearchTeamDocs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqSearchTeamDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqSearchTeamDocs) MaskInBff() any {
	if x == nil {
		return (*ReqSearchTeamDocs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqSearchTeamDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspSearchTeamDocs) MaskInLog() any {
	if x == nil {
		return (*RspSearchTeamDocs)(nil)
	}

	y := proto.Clone(x).(*RspSearchTeamDocs)
	for k, v := range y.TeamDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamDocs[k] = vv.MaskInLog().(*TeamDoc)
		}
	}

	return y
}

func (x *RspSearchTeamDocs) MaskInRpc() any {
	if x == nil {
		return (*RspSearchTeamDocs)(nil)
	}

	y := x
	for k, v := range y.TeamDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamDocs[k] = vv.MaskInRpc().(*TeamDoc)
		}
	}

	return y
}

func (x *RspSearchTeamDocs) MaskInBff() any {
	if x == nil {
		return (*RspSearchTeamDocs)(nil)
	}

	y := x
	for k, v := range y.TeamDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamDocs[k] = vv.MaskInBff().(*TeamDoc)
		}
	}

	return y
}

func (x *ReqModifySearchOption) MaskInLog() any {
	if x == nil {
		return (*ReqModifySearchOption)(nil)
	}

	y := proto.Clone(x).(*ReqModifySearchOption)
	if v, ok := any(y.Option).(interface{ MaskInLog() any }); ok {
		y.Option = v.MaskInLog().(*SearchOption)
	}

	return y
}

func (x *ReqModifySearchOption) MaskInRpc() any {
	if x == nil {
		return (*ReqModifySearchOption)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInRpc() any }); ok {
		y.Option = v.MaskInRpc().(*SearchOption)
	}

	return y
}

func (x *ReqModifySearchOption) MaskInBff() any {
	if x == nil {
		return (*ReqModifySearchOption)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInBff() any }); ok {
		y.Option = v.MaskInBff().(*SearchOption)
	}

	return y
}

func (x *ReqDescribeSearchOptions) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeSearchOptions)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeSearchOptions)
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeSearchOptions_Filter)
	}

	return y
}

func (x *ReqDescribeSearchOptions) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeSearchOptions)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeSearchOptions_Filter)
	}

	return y
}

func (x *ReqDescribeSearchOptions) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeSearchOptions)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeSearchOptions_Filter)
	}

	return y
}

func (x *RspDescribeSearchOptions) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSearchOptions)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSearchOptions)
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Options[k] = vv.MaskInLog().(*RspDescribeSearchOptions_OptionEx)
		}
	}

	return y
}

func (x *RspDescribeSearchOptions) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSearchOptions)(nil)
	}

	y := x
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Options[k] = vv.MaskInRpc().(*RspDescribeSearchOptions_OptionEx)
		}
	}

	return y
}

func (x *RspDescribeSearchOptions) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSearchOptions)(nil)
	}

	y := x
	for k, v := range y.Options {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Options[k] = vv.MaskInBff().(*RspDescribeSearchOptions_OptionEx)
		}
	}

	return y
}

func (x *RspDescribeSearchOptions_OptionEx) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSearchOptions_OptionEx)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSearchOptions_OptionEx)
	if v, ok := any(y.Option).(interface{ MaskInLog() any }); ok {
		y.Option = v.MaskInLog().(*SearchOption)
	}

	return y
}

func (x *RspDescribeSearchOptions_OptionEx) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSearchOptions_OptionEx)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInRpc() any }); ok {
		y.Option = v.MaskInRpc().(*SearchOption)
	}

	return y
}

func (x *RspDescribeSearchOptions_OptionEx) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSearchOptions_OptionEx)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInBff() any }); ok {
		y.Option = v.MaskInBff().(*SearchOption)
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSearchPrompts)
	for k, v := range y.Prompts {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Prompts[k] = vv.MaskInLog().(*SearchPrompt)
		}
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := x
	for k, v := range y.Prompts {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Prompts[k] = vv.MaskInRpc().(*SearchPrompt)
		}
	}

	return y
}

func (x *RspDescribeSearchPrompts) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSearchPrompts)(nil)
	}

	y := x
	for k, v := range y.Prompts {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Prompts[k] = vv.MaskInBff().(*SearchPrompt)
		}
	}

	return y
}

func (x *ReqModifySearchOptionsByRefer) MaskInLog() any {
	if x == nil {
		return (*ReqModifySearchOptionsByRefer)(nil)
	}

	y := proto.Clone(x).(*ReqModifySearchOptionsByRefer)
	if v, ok := any(y.Option).(interface{ MaskInLog() any }); ok {
		y.Option = v.MaskInLog().(*SearchOption)
	}

	return y
}

func (x *ReqModifySearchOptionsByRefer) MaskInRpc() any {
	if x == nil {
		return (*ReqModifySearchOptionsByRefer)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInRpc() any }); ok {
		y.Option = v.MaskInRpc().(*SearchOption)
	}

	return y
}

func (x *ReqModifySearchOptionsByRefer) MaskInBff() any {
	if x == nil {
		return (*ReqModifySearchOptionsByRefer)(nil)
	}

	y := x
	if v, ok := any(y.Option).(interface{ MaskInBff() any }); ok {
		y.Option = v.MaskInBff().(*SearchOption)
	}

	return y
}

func (x *ReqUpsertResourceIndexDocs) MaskInLog() any {
	if x == nil {
		return (*ReqUpsertResourceIndexDocs)(nil)
	}

	y := proto.Clone(x).(*ReqUpsertResourceIndexDocs)
	for k, v := range y.ResourceDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ResourceDocs[k] = vv.MaskInLog().(*ResourceDoc)
		}
	}

	return y
}

func (x *ReqUpsertResourceIndexDocs) MaskInRpc() any {
	if x == nil {
		return (*ReqUpsertResourceIndexDocs)(nil)
	}

	y := x
	for k, v := range y.ResourceDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ResourceDocs[k] = vv.MaskInRpc().(*ResourceDoc)
		}
	}

	return y
}

func (x *ReqUpsertResourceIndexDocs) MaskInBff() any {
	if x == nil {
		return (*ReqUpsertResourceIndexDocs)(nil)
	}

	y := x
	for k, v := range y.ResourceDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ResourceDocs[k] = vv.MaskInBff().(*ResourceDoc)
		}
	}

	return y
}

func (x *ReqSearchResourceDocs) MaskInLog() any {
	if x == nil {
		return (*ReqSearchResourceDocs)(nil)
	}

	y := proto.Clone(x).(*ReqSearchResourceDocs)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqSearchResourceDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqSearchResourceDocs) MaskInRpc() any {
	if x == nil {
		return (*ReqSearchResourceDocs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqSearchResourceDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqSearchResourceDocs) MaskInBff() any {
	if x == nil {
		return (*ReqSearchResourceDocs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqSearchResourceDocs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *ReqSearchResourceDocs_Filter) MaskInLog() any {
	if x == nil {
		return (*ReqSearchResourceDocs_Filter)(nil)
	}

	y := proto.Clone(x).(*ReqSearchResourceDocs_Filter)
	if v, ok := any(y.Deadline).(interface{ MaskInLog() any }); ok {
		y.Deadline = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Orders {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Orders[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ReqSearchResourceDocs_Filter) MaskInRpc() any {
	if x == nil {
		return (*ReqSearchResourceDocs_Filter)(nil)
	}

	y := x
	if v, ok := any(y.Deadline).(interface{ MaskInRpc() any }); ok {
		y.Deadline = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Orders {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Orders[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ReqSearchResourceDocs_Filter) MaskInBff() any {
	if x == nil {
		return (*ReqSearchResourceDocs_Filter)(nil)
	}

	y := x
	if v, ok := any(y.Deadline).(interface{ MaskInBff() any }); ok {
		y.Deadline = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Orders {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Orders[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspSearchResourceDocs) MaskInLog() any {
	if x == nil {
		return (*RspSearchResourceDocs)(nil)
	}

	y := proto.Clone(x).(*RspSearchResourceDocs)
	for k, v := range y.ResourceDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ResourceDocs[k] = vv.MaskInLog().(*ResourceDoc)
		}
	}

	return y
}

func (x *RspSearchResourceDocs) MaskInRpc() any {
	if x == nil {
		return (*RspSearchResourceDocs)(nil)
	}

	y := x
	for k, v := range y.ResourceDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ResourceDocs[k] = vv.MaskInRpc().(*ResourceDoc)
		}
	}

	return y
}

func (x *RspSearchResourceDocs) MaskInBff() any {
	if x == nil {
		return (*RspSearchResourceDocs)(nil)
	}

	y := x
	for k, v := range y.ResourceDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ResourceDocs[k] = vv.MaskInBff().(*ResourceDoc)
		}
	}

	return y
}

func (x *ReqUpsertProductIndexDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ProductDocs {
		if sanitizer, ok := any(x.ProductDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDeleteProductIndexDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	switch oneof := x.Condition.(type) {
	case *ReqDeleteProductIndexDocs_ByProductId:
		if sanitizer, ok := any(oneof.ByProductId).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Condition = oneof
	}
}

func (x *ReqSearchProductDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSearchProductDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ProductDocs {
		if sanitizer, ok := any(x.ProductDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpsertTeamIndexDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TeamDocs {
		if sanitizer, ok := any(x.TeamDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqSearchTeamDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSearchTeamDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TeamDocs {
		if sanitizer, ok := any(x.TeamDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqModifySearchOption) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Option).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqDescribeSearchOptions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeSearchOptions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Options {
		if sanitizer, ok := any(x.Options[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeSearchOptions_OptionEx) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Option).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeSearchPrompts) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Prompts {
		if sanitizer, ok := any(x.Prompts[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqModifySearchOptionsByRefer) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Option).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpsertResourceIndexDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ResourceDocs {
		if sanitizer, ok := any(x.ResourceDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqSearchResourceDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqSearchResourceDocs_Filter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Deadline).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Orders {
		if sanitizer, ok := any(x.Orders[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSearchResourceDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ResourceDocs {
		if sanitizer, ok := any(x.ResourceDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
