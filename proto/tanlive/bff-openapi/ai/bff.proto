syntax = "proto3";

package tanlive.bff_openapi.ai;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-openapi/ai";

import "tanlive/options.proto";

// 请求示例（需删除）
message ReqUploadDocs {
  // 文件名
  string filename = 1;
  // 文档内容
  string content = 2;
}

// 请求示例（需删除）
message RspUploadDocs {
  // 文档ID
  uint64 doc_id = 1;
}

// AI接口
service AiCapi {
  option (tanlive.capi) = true;

  // 上传文档（请求示例，需删除）
  rpc UploadDocs(ReqUploadDocs) returns (RspUploadDocs);
}
