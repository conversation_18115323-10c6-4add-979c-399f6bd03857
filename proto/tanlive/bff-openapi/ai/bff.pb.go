// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-openapi/ai/bff.proto

package ai

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求示例（需删除）
type ReqUploadDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文件名
	Filename string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	// 文档内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *ReqUploadDocs) Reset() {
	*x = ReqUploadDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_openapi_ai_bff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUploadDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUploadDocs) ProtoMessage() {}

func (x *ReqUploadDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_openapi_ai_bff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUploadDocs.ProtoReflect.Descriptor instead.
func (*ReqUploadDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_openapi_ai_bff_proto_rawDescGZIP(), []int{0}
}

func (x *ReqUploadDocs) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *ReqUploadDocs) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// 请求示例（需删除）
type RspUploadDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档ID
	DocId uint64 `protobuf:"varint,1,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
}

func (x *RspUploadDocs) Reset() {
	*x = RspUploadDocs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_openapi_ai_bff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspUploadDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspUploadDocs) ProtoMessage() {}

func (x *RspUploadDocs) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_openapi_ai_bff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspUploadDocs.ProtoReflect.Descriptor instead.
func (*RspUploadDocs) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_openapi_ai_bff_proto_rawDescGZIP(), []int{1}
}

func (x *RspUploadDocs) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

var File_tanlive_bff_openapi_ai_bff_proto protoreflect.FileDescriptor

var file_tanlive_bff_openapi_ai_bff_proto_rawDesc = []byte{
	0x0a, 0x20, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x62, 0x66, 0x66, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x45, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f,
	0x63, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x26, 0x0a, 0x0d, 0x52, 0x73, 0x70, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64,
	0x32, 0x6a, 0x0a, 0x06, 0x41, 0x69, 0x43, 0x61, 0x70, 0x69, 0x12, 0x5a, 0x0a, 0x0a, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x69, 0x2e, 0x52, 0x65, 0x71, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x73, 0x1a,
	0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x52, 0x73, 0x70, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x44, 0x6f, 0x63, 0x73, 0x1a, 0x04, 0xd8, 0xc6, 0x27, 0x01, 0x42, 0x40, 0x5a, 0x3e,
	0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x62, 0x66, 0x66, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_openapi_ai_bff_proto_rawDescOnce sync.Once
	file_tanlive_bff_openapi_ai_bff_proto_rawDescData = file_tanlive_bff_openapi_ai_bff_proto_rawDesc
)

func file_tanlive_bff_openapi_ai_bff_proto_rawDescGZIP() []byte {
	file_tanlive_bff_openapi_ai_bff_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_openapi_ai_bff_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_openapi_ai_bff_proto_rawDescData)
	})
	return file_tanlive_bff_openapi_ai_bff_proto_rawDescData
}

var file_tanlive_bff_openapi_ai_bff_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tanlive_bff_openapi_ai_bff_proto_goTypes = []interface{}{
	(*ReqUploadDocs)(nil), // 0: tanlive.bff_openapi.ai.ReqUploadDocs
	(*RspUploadDocs)(nil), // 1: tanlive.bff_openapi.ai.RspUploadDocs
}
var file_tanlive_bff_openapi_ai_bff_proto_depIdxs = []int32{
	0, // 0: tanlive.bff_openapi.ai.AiCapi.UploadDocs:input_type -> tanlive.bff_openapi.ai.ReqUploadDocs
	1, // 1: tanlive.bff_openapi.ai.AiCapi.UploadDocs:output_type -> tanlive.bff_openapi.ai.RspUploadDocs
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_bff_openapi_ai_bff_proto_init() }
func file_tanlive_bff_openapi_ai_bff_proto_init() {
	if File_tanlive_bff_openapi_ai_bff_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_openapi_ai_bff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUploadDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_openapi_ai_bff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspUploadDocs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_openapi_ai_bff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_bff_openapi_ai_bff_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_openapi_ai_bff_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_openapi_ai_bff_proto_msgTypes,
	}.Build()
	File_tanlive_bff_openapi_ai_bff_proto = out.File
	file_tanlive_bff_openapi_ai_bff_proto_rawDesc = nil
	file_tanlive_bff_openapi_ai_bff_proto_goTypes = nil
	file_tanlive_bff_openapi_ai_bff_proto_depIdxs = nil
}
