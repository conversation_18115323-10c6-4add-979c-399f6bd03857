// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package base

import (
	mask "e.coding.net/tencent-ssv/tanlive/gokits/mask"
	proto "google.golang.org/protobuf/proto"
)

func (x *TcloudCaptcha) MaskInLog() any {
	if x == nil {
		return (*TcloudCaptcha)(nil)
	}

	y := proto.Clone(x).(*TcloudCaptcha)
	y.Ticket = mask.Mask(y.Ticket, "secret")

	return y
}

func (x *TcloudCaptcha) MaskInRpc() any {
	if x == nil {
		return (*TcloudCaptcha)(nil)
	}

	y := x
	y.Ticket = mask.Mask(y.Ticket, "secret")

	return y
}

func (x *TcloudCaptcha) MaskInBff() any {
	if x == nil {
		return (*TcloudCaptcha)(nil)
	}

	y := x
	y.Ticket = mask.Mask(y.Ticket, "secret")

	return y
}
