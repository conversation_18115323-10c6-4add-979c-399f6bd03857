syntax = "proto3";

package tanlive.base;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base";

import "tanlive/options.proto";

// 驳回原因类型
enum RejectReasonType {
  // 其它
  REJECT_REASON_TYPE_OTHER = 0;
  // 内容违反使用条款
  REJECT_REASON_TYPE_VIOLATE_TERMS = 1;
  // 内容含不支持的语言
  REJECT_REASON_TYPE_UNSUPPORTED_LANG = 2;
  // 消息信息缺失
  REJECT_REASON_TYPE_NOTIFY_INFO_MISSING = 3;
  // 消息中有错别字或标点使用不当
  REJECT_REASON_TYPE_NOTIFY_HAS_TYPO = 4;
  // 消息模版选择错误
  REJECT_REASON_TYPE_NOTIFY_WRONG_TMPL = 5;
}

// 驳回原因
message RejectReason {
  // 原因类型
  RejectReasonType reason_type = 1 [(tanlive.validator) = "min=0,max=5"];
  // 其他原因
  string other_reason = 2 [(tanlive.validator) = "required_if=ReasonType 0"];
  // 自动任务结果
  repeated AutoJobResult auto_job_results = 3;
}

// 自动任务结果
message AutoJobResult {
  // 数据字段
  string data_field = 1;
  // 任务结果
  int32 job_result = 2;
  // 任务标签
  string job_label = 3;
}
