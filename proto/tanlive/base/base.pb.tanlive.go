// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package base

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	proto "google.golang.org/protobuf/proto"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"IdentityType": "required",
		"IdentityId":   "required_if=IdentityType 1,required_if=IdentityType 2,required_if=IdentityType 3",
		"Name":         "required_if=IdentityType 4",
	}, &Identity{})
}

func (x *Identity) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RateLimitResult) MaskInLog() any {
	if x == nil {
		return (*RateLimitResult)(nil)
	}

	y := proto.Clone(x).(*RateLimitResult)
	if v, ok := any(y.ResetAfter).(interface{ MaskInLog() any }); ok {
		y.ResetAfter = v.MaskInLog().(*durationpb.Duration)
	}
	if v, ok := any(y.RetryAfter).(interface{ MaskInLog() any }); ok {
		y.RetryAfter = v.MaskInLog().(*durationpb.Duration)
	}

	return y
}

func (x *RateLimitResult) MaskInRpc() any {
	if x == nil {
		return (*RateLimitResult)(nil)
	}

	y := x
	if v, ok := any(y.ResetAfter).(interface{ MaskInRpc() any }); ok {
		y.ResetAfter = v.MaskInRpc().(*durationpb.Duration)
	}
	if v, ok := any(y.RetryAfter).(interface{ MaskInRpc() any }); ok {
		y.RetryAfter = v.MaskInRpc().(*durationpb.Duration)
	}

	return y
}

func (x *RateLimitResult) MaskInBff() any {
	if x == nil {
		return (*RateLimitResult)(nil)
	}

	y := x
	if v, ok := any(y.ResetAfter).(interface{ MaskInBff() any }); ok {
		y.ResetAfter = v.MaskInBff().(*durationpb.Duration)
	}
	if v, ok := any(y.RetryAfter).(interface{ MaskInBff() any }); ok {
		y.RetryAfter = v.MaskInBff().(*durationpb.Duration)
	}

	return y
}

func (x *TimeRange) MaskInLog() any {
	if x == nil {
		return (*TimeRange)(nil)
	}

	y := proto.Clone(x).(*TimeRange)
	if v, ok := any(y.Start).(interface{ MaskInLog() any }); ok {
		y.Start = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInLog() any }); ok {
		y.End = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TimeRange) MaskInRpc() any {
	if x == nil {
		return (*TimeRange)(nil)
	}

	y := x
	if v, ok := any(y.Start).(interface{ MaskInRpc() any }); ok {
		y.Start = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInRpc() any }); ok {
		y.End = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TimeRange) MaskInBff() any {
	if x == nil {
		return (*TimeRange)(nil)
	}

	y := x
	if v, ok := any(y.Start).(interface{ MaskInBff() any }); ok {
		y.Start = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInBff() any }); ok {
		y.End = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RateLimitResult) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ResetAfter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RetryAfter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TimeRange) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Start).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.End).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
