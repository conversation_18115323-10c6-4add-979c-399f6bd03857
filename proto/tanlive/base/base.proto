syntax = "proto3";

package tanlive.base;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base";

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "tanlive/options.proto";

// 禁用状态
enum DisableState {
  DISABLE_STATE_UNSPECIFIED = 0;
  // 启用
  ENABLED = 1;
  // 禁用
  DISABLED = 2;
}

// 限流结果
message RateLimitResult {
  // 是否允许访问
  bool allowed = 1;
  // 总限制次数
  uint32 limit = 2;
  // 剩余访问次数
  uint32 remaining = 3;
  // 重置间隔时间
  google.protobuf.Duration reset_after = 4;
  // 重试间隔时间
  google.protobuf.Duration retry_after = 5;
}

// 分页器
message Paginator {
  // 便宜量
  uint32 offset = 1;
  // 页面大小
  uint32 limit = 2;
}

// 时间范围
message TimeRange {
  // 开始时间
  google.protobuf.Timestamp start = 1;
  // 结束时间
  google.protobuf.Timestamp end = 2;
}

// 排序
message OrderBy {
  // 列名
  string column = 1;
  // 是否倒序
  bool desc = 2;
}

// 排序类型
enum OrderType {
  ORDER_TYPE_UNSPECIFIED = 0;
  // 正序
  ORDER_TYPE_ASC = 1;
  // 倒序
  ORDER_TYPE_DESC = 2;
}

// bool值的枚举
enum BoolEnum {
  BOOL_ENUM_UNSPECIFIED = 0;
  // false
  BOOL_ENUM_FALSE = 1;
  // true
  BOOL_ENUM_TRUE = 2;
}

// 数据操作类型
enum CrudType{
  // 未定义
  CRUD_TYPE_UNSPECIFIED = 0;
  // 创建
  CRUD_TYPE_CREATE = 1;
  // 读取
  CRUD_TYPE_READ = 2;
  // 更新
  CRUD_TYPE_UPDATE = 3;
  // 删除
  CRUD_TYPE_DELETE = 4;
}

// 身份类型
enum IdentityType {
  IDENTITY_TYPE_UNSPECIFIED = 0;
  // 门户端用户
  IDENTITY_TYPE_USER = 1;
  // 团队
  IDENTITY_TYPE_TEAM = 2;
  // 运营端用户
  IDENTITY_TYPE_MGMT = 3;
  // 自定义
  IDENTITY_TYPE_CUSTOM = 4;
}

// 身份
message Identity {
  // 身份类型
  IdentityType identity_type = 1 [(validator) = "required"];
  // 身份ID
  uint64 identity_id = 2 [(validator) = "required_if=IdentityType 1,required_if=IdentityType 2,required_if=IdentityType 3"];
  // 名字
  string name = 3 [(validator) = "required_if=IdentityType 4"];
  // 额外ID（团队类型表示用户ID）
  uint64 extra_id = 4;
}
