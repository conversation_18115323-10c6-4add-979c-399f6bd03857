// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/base/ai.proto

package base

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AI事件源
type AIEventSource int32

const (
	// 图谱通知
	AIEventSource_ATLAS_NOTIFY AIEventSource = 0
	// AI对话
	AIEventSource_AI_CHAT AIEventSource = 1
)

// Enum value maps for AIEventSource.
var (
	AIEventSource_name = map[int32]string{
		0: "ATLAS_NOTIFY",
		1: "AI_CHAT",
	}
	AIEventSource_value = map[string]int32{
		"ATLAS_NOTIFY": 0,
		"AI_CHAT":      1,
	}
)

func (x AIEventSource) Enum() *AIEventSource {
	p := new(AIEventSource)
	*p = x
	return p
}

func (x AIEventSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AIEventSource) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_ai_proto_enumTypes[0].Descriptor()
}

func (AIEventSource) Type() protoreflect.EnumType {
	return &file_tanlive_base_ai_proto_enumTypes[0]
}

func (x AIEventSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AIEventSource.Descriptor instead.
func (AIEventSource) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_ai_proto_rawDescGZIP(), []int{0}
}

var File_tanlive_base_ai_proto protoreflect.FileDescriptor

var file_tanlive_base_ai_proto_rawDesc = []byte{
	0x0a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x61,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0x2e, 0x0a, 0x0d, 0x41, 0x49, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x54, 0x4c, 0x41, 0x53, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x49, 0x5f, 0x43,
	0x48, 0x41, 0x54, 0x10, 0x01, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73,
	0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x62, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_base_ai_proto_rawDescOnce sync.Once
	file_tanlive_base_ai_proto_rawDescData = file_tanlive_base_ai_proto_rawDesc
)

func file_tanlive_base_ai_proto_rawDescGZIP() []byte {
	file_tanlive_base_ai_proto_rawDescOnce.Do(func() {
		file_tanlive_base_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_base_ai_proto_rawDescData)
	})
	return file_tanlive_base_ai_proto_rawDescData
}

var file_tanlive_base_ai_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_base_ai_proto_goTypes = []interface{}{
	(AIEventSource)(0), // 0: tanlive.base.AIEventSource
}
var file_tanlive_base_ai_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tanlive_base_ai_proto_init() }
func file_tanlive_base_ai_proto_init() {
	if File_tanlive_base_ai_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_base_ai_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_base_ai_proto_goTypes,
		DependencyIndexes: file_tanlive_base_ai_proto_depIdxs,
		EnumInfos:         file_tanlive_base_ai_proto_enumTypes,
	}.Build()
	File_tanlive_base_ai_proto = out.File
	file_tanlive_base_ai_proto_rawDesc = nil
	file_tanlive_base_ai_proto_goTypes = nil
	file_tanlive_base_ai_proto_depIdxs = nil
}
