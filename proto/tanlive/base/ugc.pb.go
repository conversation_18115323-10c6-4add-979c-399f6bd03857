// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/base/ugc.proto

package base

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UGC状态
type UgcState int32

const (
	UgcState_UGC_STATE_UNSPECIFIED UgcState = 0
	// 草稿。用户前台：草稿；运营后台：草稿
	UgcState_UGC_STATE_DRAFTING UgcState = 1
	// 自动审核中。用户前台：处理中；运营后台：处理中
	UgcState_UGC_STATE_AUTO_REVIEWING UgcState = 2
	// 人工审核中（用户前台：处理中；运营后台：待处理）
	UgcState_UGC_STATE_MANUAL_REVIEWING UgcState = 3
	// 人工审核中-可疑（用户前台：处理中；运营后台：可疑，待处理）
	UgcState_UGC_STATE_MANUAL_REVIEWING_SUSPICIOUS UgcState = 4
	// 审核已通过（用户前台：下架存档；运营后台：下架存档）
	UgcState_UGC_STATE_PASSED UgcState = 5
	// 已发布（用户前台：已发布；运营后台：已发布）
	UgcState_UGC_STATE_PUBLISHED UgcState = 6
	// 审核已驳回（用户前台：已驳回；运营后台：已驳回）
	UgcState_UGC_STATE_REJECTED UgcState = 7
	// 申诉中（用户前台：申诉中；运营后台：申诉待处理）
	UgcState_UGC_STATE_APPEALING UgcState = 8
)

// Enum value maps for UgcState.
var (
	UgcState_name = map[int32]string{
		0: "UGC_STATE_UNSPECIFIED",
		1: "UGC_STATE_DRAFTING",
		2: "UGC_STATE_AUTO_REVIEWING",
		3: "UGC_STATE_MANUAL_REVIEWING",
		4: "UGC_STATE_MANUAL_REVIEWING_SUSPICIOUS",
		5: "UGC_STATE_PASSED",
		6: "UGC_STATE_PUBLISHED",
		7: "UGC_STATE_REJECTED",
		8: "UGC_STATE_APPEALING",
	}
	UgcState_value = map[string]int32{
		"UGC_STATE_UNSPECIFIED":                 0,
		"UGC_STATE_DRAFTING":                    1,
		"UGC_STATE_AUTO_REVIEWING":              2,
		"UGC_STATE_MANUAL_REVIEWING":            3,
		"UGC_STATE_MANUAL_REVIEWING_SUSPICIOUS": 4,
		"UGC_STATE_PASSED":                      5,
		"UGC_STATE_PUBLISHED":                   6,
		"UGC_STATE_REJECTED":                    7,
		"UGC_STATE_APPEALING":                   8,
	}
)

func (x UgcState) Enum() *UgcState {
	p := new(UgcState)
	*p = x
	return p
}

func (x UgcState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UgcState) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_ugc_proto_enumTypes[0].Descriptor()
}

func (UgcState) Type() protoreflect.EnumType {
	return &file_tanlive_base_ugc_proto_enumTypes[0]
}

func (x UgcState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UgcState.Descriptor instead.
func (UgcState) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_ugc_proto_rawDescGZIP(), []int{0}
}

// 可见类型
type VisibleType int32

const (
	// 全网可见
	VisibleType_VISIBLE_TYPE_PUBLIC VisibleType = 0
	// 不公开
	VisibleType_VISIBLE_TYPE_PRIVATE VisibleType = 1
	// 仅注册用户
	VisibleType_VISIBLE_TYPE_REGISTERED_USER VisibleType = 2
	// 仅认证团队
	VisibleType_VISIBLE_TYPE_VERIFIED_TEAM VisibleType = 3
)

// Enum value maps for VisibleType.
var (
	VisibleType_name = map[int32]string{
		0: "VISIBLE_TYPE_PUBLIC",
		1: "VISIBLE_TYPE_PRIVATE",
		2: "VISIBLE_TYPE_REGISTERED_USER",
		3: "VISIBLE_TYPE_VERIFIED_TEAM",
	}
	VisibleType_value = map[string]int32{
		"VISIBLE_TYPE_PUBLIC":          0,
		"VISIBLE_TYPE_PRIVATE":         1,
		"VISIBLE_TYPE_REGISTERED_USER": 2,
		"VISIBLE_TYPE_VERIFIED_TEAM":   3,
	}
)

func (x VisibleType) Enum() *VisibleType {
	p := new(VisibleType)
	*p = x
	return p
}

func (x VisibleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VisibleType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_ugc_proto_enumTypes[1].Descriptor()
}

func (VisibleType) Type() protoreflect.EnumType {
	return &file_tanlive_base_ugc_proto_enumTypes[1]
}

func (x VisibleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VisibleType.Descriptor instead.
func (VisibleType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_ugc_proto_rawDescGZIP(), []int{1}
}

// 贡献者类型
type ContributorType int32

const (
	ContributorType_CONTRIBUTOR_TYPE_UNSPECIFIED ContributorType = 0
	// 团队
	ContributorType_CONTRIBUTOR_TYPE_TEAM ContributorType = 1
	// 个人
	ContributorType_CONTRIBUTOR_TYPE_USER ContributorType = 2
)

// Enum value maps for ContributorType.
var (
	ContributorType_name = map[int32]string{
		0: "CONTRIBUTOR_TYPE_UNSPECIFIED",
		1: "CONTRIBUTOR_TYPE_TEAM",
		2: "CONTRIBUTOR_TYPE_USER",
	}
	ContributorType_value = map[string]int32{
		"CONTRIBUTOR_TYPE_UNSPECIFIED": 0,
		"CONTRIBUTOR_TYPE_TEAM":        1,
		"CONTRIBUTOR_TYPE_USER":        2,
	}
)

func (x ContributorType) Enum() *ContributorType {
	p := new(ContributorType)
	*p = x
	return p
}

func (x ContributorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContributorType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_ugc_proto_enumTypes[2].Descriptor()
}

func (ContributorType) Type() protoreflect.EnumType {
	return &file_tanlive_base_ugc_proto_enumTypes[2]
}

func (x ContributorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContributorType.Descriptor instead.
func (ContributorType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_ugc_proto_rawDescGZIP(), []int{2}
}

// 编辑者类型
type EditorType int32

const (
	EditorType_EDITOR_TYPE_UNSPECIFIED EditorType = 0
	// 团队
	EditorType_EDITOR_TYPE_TEAM EditorType = 1
	// 个人
	EditorType_EDITOR_TYPE_USER EditorType = 2
)

// Enum value maps for EditorType.
var (
	EditorType_name = map[int32]string{
		0: "EDITOR_TYPE_UNSPECIFIED",
		1: "EDITOR_TYPE_TEAM",
		2: "EDITOR_TYPE_USER",
	}
	EditorType_value = map[string]int32{
		"EDITOR_TYPE_UNSPECIFIED": 0,
		"EDITOR_TYPE_TEAM":        1,
		"EDITOR_TYPE_USER":        2,
	}
)

func (x EditorType) Enum() *EditorType {
	p := new(EditorType)
	*p = x
	return p
}

func (x EditorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EditorType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_ugc_proto_enumTypes[3].Descriptor()
}

func (EditorType) Type() protoreflect.EnumType {
	return &file_tanlive_base_ugc_proto_enumTypes[3]
}

func (x EditorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EditorType.Descriptor instead.
func (EditorType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_ugc_proto_rawDescGZIP(), []int{3}
}

// 数据类型
type DataType int32

const (
	DataType_DATA_TYPE_UNSPECIFIED DataType = 0
	// 团队
	DataType_DATA_TYPE_TEAM DataType = 1
	// 产品
	DataType_DATA_TYPE_PRODUCT DataType = 2
	// 资源
	DataType_DATA_TYPE_RESOURCE DataType = 3
	// 图谱
	DataType_DATA_TYPE_GRAPH DataType = 4
	// 定向推送
	DataType_DATA_TYPE_NOTIFY DataType = 5
	// 用户个人
	DataType_DATA_TYPE_USER DataType = 6
	// 图谱AI
	DataType_DATA_TYPE_GRAPH_AI DataType = 7
	// 帮助中心文档
	DataType_DATA_TYPE_HELP_CENTER DataType = 8
	// AI助手
	DataType_DATA_TYPE_AI_ASSISTANT DataType = 9
)

// Enum value maps for DataType.
var (
	DataType_name = map[int32]string{
		0: "DATA_TYPE_UNSPECIFIED",
		1: "DATA_TYPE_TEAM",
		2: "DATA_TYPE_PRODUCT",
		3: "DATA_TYPE_RESOURCE",
		4: "DATA_TYPE_GRAPH",
		5: "DATA_TYPE_NOTIFY",
		6: "DATA_TYPE_USER",
		7: "DATA_TYPE_GRAPH_AI",
		8: "DATA_TYPE_HELP_CENTER",
		9: "DATA_TYPE_AI_ASSISTANT",
	}
	DataType_value = map[string]int32{
		"DATA_TYPE_UNSPECIFIED":  0,
		"DATA_TYPE_TEAM":         1,
		"DATA_TYPE_PRODUCT":      2,
		"DATA_TYPE_RESOURCE":     3,
		"DATA_TYPE_GRAPH":        4,
		"DATA_TYPE_NOTIFY":       5,
		"DATA_TYPE_USER":         6,
		"DATA_TYPE_GRAPH_AI":     7,
		"DATA_TYPE_HELP_CENTER":  8,
		"DATA_TYPE_AI_ASSISTANT": 9,
	}
)

func (x DataType) Enum() *DataType {
	p := new(DataType)
	*p = x
	return p
}

func (x DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_ugc_proto_enumTypes[4].Descriptor()
}

func (DataType) Type() protoreflect.EnumType {
	return &file_tanlive_base_ugc_proto_enumTypes[4]
}

func (x DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataType.Descriptor instead.
func (DataType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_ugc_proto_rawDescGZIP(), []int{4}
}

// 地域
type Region int32

const (
	Region_REGION_UNSPECIFIED Region = 0
	// 国内
	Region_REGION_NATIONAL Region = 1
	// 海外
	Region_REGION_INTERNATIONAL Region = 2
)

// Enum value maps for Region.
var (
	Region_name = map[int32]string{
		0: "REGION_UNSPECIFIED",
		1: "REGION_NATIONAL",
		2: "REGION_INTERNATIONAL",
	}
	Region_value = map[string]int32{
		"REGION_UNSPECIFIED":   0,
		"REGION_NATIONAL":      1,
		"REGION_INTERNATIONAL": 2,
	}
)

func (x Region) Enum() *Region {
	p := new(Region)
	*p = x
	return p
}

func (x Region) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Region) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_ugc_proto_enumTypes[5].Descriptor()
}

func (Region) Type() protoreflect.EnumType {
	return &file_tanlive_base_ugc_proto_enumTypes[5]
}

func (x Region) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Region.Descriptor instead.
func (Region) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_ugc_proto_rawDescGZIP(), []int{5}
}

// 可见性规则
type VisibleRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 可见类型
	VisibleType VisibleType `protobuf:"varint,1,opt,name=visible_type,json=visibleType,proto3,enum=tanlive.base.VisibleType" json:"visible_type,omitempty"`
	// 是否全选
	SelectAll bool `protobuf:"varint,2,opt,name=select_all,json=selectAll,proto3" json:"select_all,omitempty"`
	// 可见标签
	Tags []string `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty"`
	// 是否包含自定义标签
	HasCustomTag bool `protobuf:"varint,4,opt,name=has_custom_tag,json=hasCustomTag,proto3" json:"has_custom_tag,omitempty"`
}

func (x *VisibleRule) Reset() {
	*x = VisibleRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_ugc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisibleRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisibleRule) ProtoMessage() {}

func (x *VisibleRule) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_ugc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisibleRule.ProtoReflect.Descriptor instead.
func (*VisibleRule) Descriptor() ([]byte, []int) {
	return file_tanlive_base_ugc_proto_rawDescGZIP(), []int{0}
}

func (x *VisibleRule) GetVisibleType() VisibleType {
	if x != nil {
		return x.VisibleType
	}
	return VisibleType_VISIBLE_TYPE_PUBLIC
}

func (x *VisibleRule) GetSelectAll() bool {
	if x != nil {
		return x.SelectAll
	}
	return false
}

func (x *VisibleRule) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *VisibleRule) GetHasCustomTag() bool {
	if x != nil {
		return x.HasCustomTag
	}
	return false
}

// 数据项
type DataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据ID
	DataId uint64 `protobuf:"varint,1,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	// 数据类型
	DataType DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 数据字段
	DataField string `protobuf:"bytes,3,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
}

func (x *DataItem) Reset() {
	*x = DataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_ugc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataItem) ProtoMessage() {}

func (x *DataItem) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_ugc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataItem.ProtoReflect.Descriptor instead.
func (*DataItem) Descriptor() ([]byte, []int) {
	return file_tanlive_base_ugc_proto_rawDescGZIP(), []int{1}
}

func (x *DataItem) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *DataItem) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_DATA_TYPE_UNSPECIFIED
}

func (x *DataItem) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

var File_tanlive_base_ugc_proto protoreflect.FileDescriptor

var file_tanlive_base_ugc_proto_rawDesc = []byte{
	0x0a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75,
	0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x22, 0xa4, 0x01, 0x0a, 0x0b, 0x56, 0x69, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x56, 0x69, 0x73, 0x69,
	0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x61,
	0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x41, 0x6c, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x68, 0x61, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x68, 0x61, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x22, 0x77, 0x0a,
	0x08, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61,
	0x49, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x2a, 0x86, 0x02, 0x0a, 0x08, 0x55, 0x67, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16,
	0x0a, 0x12, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x52, 0x41, 0x46,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x49,
	0x4e, 0x47, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x49, 0x43, 0x49, 0x4f, 0x55, 0x53, 0x10, 0x04, 0x12,
	0x14, 0x0a, 0x10, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x53,
	0x53, 0x45, 0x44, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x06, 0x12, 0x16,
	0x0a, 0x12, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45,
	0x43, 0x54, 0x45, 0x44, 0x10, 0x07, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x47, 0x43, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x45, 0x41, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x08, 0x2a,
	0x82, 0x01, 0x0a, 0x0b, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x17, 0x0a, 0x13, 0x56, 0x49, 0x53, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x49, 0x53, 0x49,
	0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45,
	0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x56, 0x49, 0x53, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x49, 0x53, 0x49, 0x42, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x54, 0x45,
	0x41, 0x4d, 0x10, 0x03, 0x2a, 0x69, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4e, 0x54, 0x52,
	0x49, 0x42, 0x55, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4f, 0x4e,
	0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45,
	0x41, 0x4d, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x49, 0x42, 0x55,
	0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x02, 0x2a,
	0x55, 0x0a, 0x0a, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x45, 0x44, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x44,
	0x49, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x45, 0x44, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x10, 0x02, 0x2a, 0xf6, 0x01, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12,
	0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d,
	0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10,
	0x03, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47,
	0x52, 0x41, 0x50, 0x48, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x06,
	0x12, 0x16, 0x0a, 0x12, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x52,
	0x41, 0x50, 0x48, 0x5f, 0x41, 0x49, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x45,
	0x52, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x49, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x10, 0x09, 0x2a,
	0x4f, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x47,
	0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x47, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x47, 0x49, 0x4f, 0x4e,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x02,
	0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74,
	0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_base_ugc_proto_rawDescOnce sync.Once
	file_tanlive_base_ugc_proto_rawDescData = file_tanlive_base_ugc_proto_rawDesc
)

func file_tanlive_base_ugc_proto_rawDescGZIP() []byte {
	file_tanlive_base_ugc_proto_rawDescOnce.Do(func() {
		file_tanlive_base_ugc_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_base_ugc_proto_rawDescData)
	})
	return file_tanlive_base_ugc_proto_rawDescData
}

var file_tanlive_base_ugc_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_tanlive_base_ugc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tanlive_base_ugc_proto_goTypes = []interface{}{
	(UgcState)(0),        // 0: tanlive.base.UgcState
	(VisibleType)(0),     // 1: tanlive.base.VisibleType
	(ContributorType)(0), // 2: tanlive.base.ContributorType
	(EditorType)(0),      // 3: tanlive.base.EditorType
	(DataType)(0),        // 4: tanlive.base.DataType
	(Region)(0),          // 5: tanlive.base.Region
	(*VisibleRule)(nil),  // 6: tanlive.base.VisibleRule
	(*DataItem)(nil),     // 7: tanlive.base.DataItem
}
var file_tanlive_base_ugc_proto_depIdxs = []int32{
	1, // 0: tanlive.base.VisibleRule.visible_type:type_name -> tanlive.base.VisibleType
	4, // 1: tanlive.base.DataItem.data_type:type_name -> tanlive.base.DataType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_tanlive_base_ugc_proto_init() }
func file_tanlive_base_ugc_proto_init() {
	if File_tanlive_base_ugc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_base_ugc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisibleRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_base_ugc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_base_ugc_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_base_ugc_proto_goTypes,
		DependencyIndexes: file_tanlive_base_ugc_proto_depIdxs,
		EnumInfos:         file_tanlive_base_ugc_proto_enumTypes,
		MessageInfos:      file_tanlive_base_ugc_proto_msgTypes,
	}.Build()
	File_tanlive_base_ugc_proto = out.File
	file_tanlive_base_ugc_proto_rawDesc = nil
	file_tanlive_base_ugc_proto_goTypes = nil
	file_tanlive_base_ugc_proto_depIdxs = nil
}
