// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package base

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	proto "google.golang.org/protobuf/proto"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ReasonType":  "min=0,max=5",
		"OtherReason": "required_if=ReasonType 0",
	}, &RejectReason{})
}

func (x *RejectReason) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RejectReason) MaskInLog() any {
	if x == nil {
		return (*RejectReason)(nil)
	}

	y := proto.Clone(x).(*RejectReason)
	for k, v := range y.AutoJobResults {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.AutoJobResults[k] = vv.MaskInLog().(*AutoJobResult)
		}
	}

	return y
}

func (x *RejectReason) MaskInRpc() any {
	if x == nil {
		return (*RejectReason)(nil)
	}

	y := x
	for k, v := range y.AutoJobResults {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.AutoJobResults[k] = vv.MaskInRpc().(*AutoJobResult)
		}
	}

	return y
}

func (x *RejectReason) MaskInBff() any {
	if x == nil {
		return (*RejectReason)(nil)
	}

	y := x
	for k, v := range y.AutoJobResults {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.AutoJobResults[k] = vv.MaskInBff().(*AutoJobResult)
		}
	}

	return y
}

func (x *RejectReason) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.AutoJobResults {
		if sanitizer, ok := any(x.AutoJobResults[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
