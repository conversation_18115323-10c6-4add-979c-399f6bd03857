// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/base/base.proto

package base

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 禁用状态
type DisableState int32

const (
	DisableState_DISABLE_STATE_UNSPECIFIED DisableState = 0
	// 启用
	DisableState_ENABLED DisableState = 1
	// 禁用
	DisableState_DISABLED DisableState = 2
)

// Enum value maps for DisableState.
var (
	DisableState_name = map[int32]string{
		0: "DISABLE_STATE_UNSPECIFIED",
		1: "ENABLED",
		2: "DISABLED",
	}
	DisableState_value = map[string]int32{
		"DISABLE_STATE_UNSPECIFIED": 0,
		"ENABLED":                   1,
		"DISABLED":                  2,
	}
)

func (x DisableState) Enum() *DisableState {
	p := new(DisableState)
	*p = x
	return p
}

func (x DisableState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisableState) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_base_proto_enumTypes[0].Descriptor()
}

func (DisableState) Type() protoreflect.EnumType {
	return &file_tanlive_base_base_proto_enumTypes[0]
}

func (x DisableState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisableState.Descriptor instead.
func (DisableState) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{0}
}

// 排序类型
type OrderType int32

const (
	OrderType_ORDER_TYPE_UNSPECIFIED OrderType = 0
	// 正序
	OrderType_ORDER_TYPE_ASC OrderType = 1
	// 倒序
	OrderType_ORDER_TYPE_DESC OrderType = 2
)

// Enum value maps for OrderType.
var (
	OrderType_name = map[int32]string{
		0: "ORDER_TYPE_UNSPECIFIED",
		1: "ORDER_TYPE_ASC",
		2: "ORDER_TYPE_DESC",
	}
	OrderType_value = map[string]int32{
		"ORDER_TYPE_UNSPECIFIED": 0,
		"ORDER_TYPE_ASC":         1,
		"ORDER_TYPE_DESC":        2,
	}
)

func (x OrderType) Enum() *OrderType {
	p := new(OrderType)
	*p = x
	return p
}

func (x OrderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_base_proto_enumTypes[1].Descriptor()
}

func (OrderType) Type() protoreflect.EnumType {
	return &file_tanlive_base_base_proto_enumTypes[1]
}

func (x OrderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderType.Descriptor instead.
func (OrderType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{1}
}

// bool值的枚举
type BoolEnum int32

const (
	BoolEnum_BOOL_ENUM_UNSPECIFIED BoolEnum = 0
	// false
	BoolEnum_BOOL_ENUM_FALSE BoolEnum = 1
	// true
	BoolEnum_BOOL_ENUM_TRUE BoolEnum = 2
)

// Enum value maps for BoolEnum.
var (
	BoolEnum_name = map[int32]string{
		0: "BOOL_ENUM_UNSPECIFIED",
		1: "BOOL_ENUM_FALSE",
		2: "BOOL_ENUM_TRUE",
	}
	BoolEnum_value = map[string]int32{
		"BOOL_ENUM_UNSPECIFIED": 0,
		"BOOL_ENUM_FALSE":       1,
		"BOOL_ENUM_TRUE":        2,
	}
)

func (x BoolEnum) Enum() *BoolEnum {
	p := new(BoolEnum)
	*p = x
	return p
}

func (x BoolEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BoolEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_base_proto_enumTypes[2].Descriptor()
}

func (BoolEnum) Type() protoreflect.EnumType {
	return &file_tanlive_base_base_proto_enumTypes[2]
}

func (x BoolEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BoolEnum.Descriptor instead.
func (BoolEnum) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{2}
}

// 数据操作类型
type CrudType int32

const (
	// 未定义
	CrudType_CRUD_TYPE_UNSPECIFIED CrudType = 0
	// 创建
	CrudType_CRUD_TYPE_CREATE CrudType = 1
	// 读取
	CrudType_CRUD_TYPE_READ CrudType = 2
	// 更新
	CrudType_CRUD_TYPE_UPDATE CrudType = 3
	// 删除
	CrudType_CRUD_TYPE_DELETE CrudType = 4
)

// Enum value maps for CrudType.
var (
	CrudType_name = map[int32]string{
		0: "CRUD_TYPE_UNSPECIFIED",
		1: "CRUD_TYPE_CREATE",
		2: "CRUD_TYPE_READ",
		3: "CRUD_TYPE_UPDATE",
		4: "CRUD_TYPE_DELETE",
	}
	CrudType_value = map[string]int32{
		"CRUD_TYPE_UNSPECIFIED": 0,
		"CRUD_TYPE_CREATE":      1,
		"CRUD_TYPE_READ":        2,
		"CRUD_TYPE_UPDATE":      3,
		"CRUD_TYPE_DELETE":      4,
	}
)

func (x CrudType) Enum() *CrudType {
	p := new(CrudType)
	*p = x
	return p
}

func (x CrudType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CrudType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_base_proto_enumTypes[3].Descriptor()
}

func (CrudType) Type() protoreflect.EnumType {
	return &file_tanlive_base_base_proto_enumTypes[3]
}

func (x CrudType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CrudType.Descriptor instead.
func (CrudType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{3}
}

// 身份类型
type IdentityType int32

const (
	IdentityType_IDENTITY_TYPE_UNSPECIFIED IdentityType = 0
	// 门户端用户
	IdentityType_IDENTITY_TYPE_USER IdentityType = 1
	// 团队
	IdentityType_IDENTITY_TYPE_TEAM IdentityType = 2
	// 运营端用户
	IdentityType_IDENTITY_TYPE_MGMT IdentityType = 3
	// 自定义
	IdentityType_IDENTITY_TYPE_CUSTOM IdentityType = 4
)

// Enum value maps for IdentityType.
var (
	IdentityType_name = map[int32]string{
		0: "IDENTITY_TYPE_UNSPECIFIED",
		1: "IDENTITY_TYPE_USER",
		2: "IDENTITY_TYPE_TEAM",
		3: "IDENTITY_TYPE_MGMT",
		4: "IDENTITY_TYPE_CUSTOM",
	}
	IdentityType_value = map[string]int32{
		"IDENTITY_TYPE_UNSPECIFIED": 0,
		"IDENTITY_TYPE_USER":        1,
		"IDENTITY_TYPE_TEAM":        2,
		"IDENTITY_TYPE_MGMT":        3,
		"IDENTITY_TYPE_CUSTOM":      4,
	}
)

func (x IdentityType) Enum() *IdentityType {
	p := new(IdentityType)
	*p = x
	return p
}

func (x IdentityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentityType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_base_proto_enumTypes[4].Descriptor()
}

func (IdentityType) Type() protoreflect.EnumType {
	return &file_tanlive_base_base_proto_enumTypes[4]
}

func (x IdentityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentityType.Descriptor instead.
func (IdentityType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{4}
}

// 限流结果
type RateLimitResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否允许访问
	Allowed bool `protobuf:"varint,1,opt,name=allowed,proto3" json:"allowed,omitempty"`
	// 总限制次数
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 剩余访问次数
	Remaining uint32 `protobuf:"varint,3,opt,name=remaining,proto3" json:"remaining,omitempty"`
	// 重置间隔时间
	ResetAfter *durationpb.Duration `protobuf:"bytes,4,opt,name=reset_after,json=resetAfter,proto3" json:"reset_after,omitempty"`
	// 重试间隔时间
	RetryAfter *durationpb.Duration `protobuf:"bytes,5,opt,name=retry_after,json=retryAfter,proto3" json:"retry_after,omitempty"`
}

func (x *RateLimitResult) Reset() {
	*x = RateLimitResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_base_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitResult) ProtoMessage() {}

func (x *RateLimitResult) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_base_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitResult.ProtoReflect.Descriptor instead.
func (*RateLimitResult) Descriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{0}
}

func (x *RateLimitResult) GetAllowed() bool {
	if x != nil {
		return x.Allowed
	}
	return false
}

func (x *RateLimitResult) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *RateLimitResult) GetRemaining() uint32 {
	if x != nil {
		return x.Remaining
	}
	return 0
}

func (x *RateLimitResult) GetResetAfter() *durationpb.Duration {
	if x != nil {
		return x.ResetAfter
	}
	return nil
}

func (x *RateLimitResult) GetRetryAfter() *durationpb.Duration {
	if x != nil {
		return x.RetryAfter
	}
	return nil
}

// 分页器
type Paginator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 便宜量
	Offset uint32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// 页面大小
	Limit uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *Paginator) Reset() {
	*x = Paginator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_base_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Paginator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Paginator) ProtoMessage() {}

func (x *Paginator) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_base_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Paginator.ProtoReflect.Descriptor instead.
func (*Paginator) Descriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{1}
}

func (x *Paginator) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *Paginator) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// 时间范围
type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 开始时间
	Start *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	// 结束时间
	End *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_base_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_base_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{2}
}

func (x *TimeRange) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *TimeRange) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

// 排序
type OrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 列名
	Column string `protobuf:"bytes,1,opt,name=column,proto3" json:"column,omitempty"`
	// 是否倒序
	Desc bool `protobuf:"varint,2,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *OrderBy) Reset() {
	*x = OrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_base_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderBy) ProtoMessage() {}

func (x *OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_base_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderBy.ProtoReflect.Descriptor instead.
func (*OrderBy) Descriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{3}
}

func (x *OrderBy) GetColumn() string {
	if x != nil {
		return x.Column
	}
	return ""
}

func (x *OrderBy) GetDesc() bool {
	if x != nil {
		return x.Desc
	}
	return false
}

// 身份
type Identity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 身份类型
	IdentityType IdentityType `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3,enum=tanlive.base.IdentityType" json:"identity_type,omitempty"`
	// 身份ID
	IdentityId uint64 `protobuf:"varint,2,opt,name=identity_id,json=identityId,proto3" json:"identity_id,omitempty"`
	// 名字
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 额外ID（团队类型表示用户ID）
	ExtraId uint64 `protobuf:"varint,4,opt,name=extra_id,json=extraId,proto3" json:"extra_id,omitempty"`
}

func (x *Identity) Reset() {
	*x = Identity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_base_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Identity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identity) ProtoMessage() {}

func (x *Identity) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_base_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identity.ProtoReflect.Descriptor instead.
func (*Identity) Descriptor() ([]byte, []int) {
	return file_tanlive_base_base_proto_rawDescGZIP(), []int{4}
}

func (x *Identity) GetIdentityType() IdentityType {
	if x != nil {
		return x.IdentityType
	}
	return IdentityType_IDENTITY_TYPE_UNSPECIFIED
}

func (x *Identity) GetIdentityId() uint64 {
	if x != nil {
		return x.IdentityId
	}
	return 0
}

func (x *Identity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Identity) GetExtraId() uint64 {
	if x != nil {
		return x.ExtraId
	}
	return 0
}

var File_tanlive_base_base_proto protoreflect.FileDescriptor

var file_tanlive_base_base_proto_rawDesc = []byte{
	0x0a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xd7, 0x01, 0x0a, 0x0f, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x12, 0x3a, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x65, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x3a, 0x0a,
	0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x41, 0x66, 0x74, 0x65, 0x72, 0x22, 0x39, 0x0a, 0x09, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x22, 0x6b, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x65, 0x6e,
	0x64, 0x22, 0x35, 0x0a, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x9f, 0x02, 0x0a, 0x08, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x4d, 0x0a, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0c, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x75, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x54, 0x82, 0x88, 0x27, 0x50, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x20, 0x31, 0x2c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x20, 0x32, 0x2c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x66,
	0x3d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x20, 0x33, 0x52,
	0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0x82, 0x88, 0x27, 0x1a, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x20, 0x34, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x64, 0x2a, 0x48, 0x0a, 0x0c, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49,
	0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x4e, 0x41,
	0x42, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c,
	0x45, 0x44, 0x10, 0x02, 0x2a, 0x50, 0x0a, 0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a,
	0x0e, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x43, 0x10,
	0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x44, 0x45, 0x53, 0x43, 0x10, 0x02, 0x2a, 0x4e, 0x0a, 0x08, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x19, 0x0a, 0x15, 0x42, 0x4f, 0x4f, 0x4c, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x42, 0x4f, 0x4f, 0x4c, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x46, 0x41, 0x4c, 0x53, 0x45,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x4f, 0x4f, 0x4c, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f,
	0x54, 0x52, 0x55, 0x45, 0x10, 0x02, 0x2a, 0x7b, 0x0a, 0x08, 0x43, 0x72, 0x75, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x52, 0x55, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x43, 0x52, 0x55, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x52, 0x55, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x52, 0x55, 0x44, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x14, 0x0a,
	0x10, 0x43, 0x52, 0x55, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54,
	0x45, 0x10, 0x04, 0x2a, 0x8f, 0x01, 0x0a, 0x0c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x41,
	0x4d, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x47, 0x4d, 0x54, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x10, 0x04, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73,
	0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x62, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_base_base_proto_rawDescOnce sync.Once
	file_tanlive_base_base_proto_rawDescData = file_tanlive_base_base_proto_rawDesc
)

func file_tanlive_base_base_proto_rawDescGZIP() []byte {
	file_tanlive_base_base_proto_rawDescOnce.Do(func() {
		file_tanlive_base_base_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_base_base_proto_rawDescData)
	})
	return file_tanlive_base_base_proto_rawDescData
}

var file_tanlive_base_base_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_tanlive_base_base_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_tanlive_base_base_proto_goTypes = []interface{}{
	(DisableState)(0),             // 0: tanlive.base.DisableState
	(OrderType)(0),                // 1: tanlive.base.OrderType
	(BoolEnum)(0),                 // 2: tanlive.base.BoolEnum
	(CrudType)(0),                 // 3: tanlive.base.CrudType
	(IdentityType)(0),             // 4: tanlive.base.IdentityType
	(*RateLimitResult)(nil),       // 5: tanlive.base.RateLimitResult
	(*Paginator)(nil),             // 6: tanlive.base.Paginator
	(*TimeRange)(nil),             // 7: tanlive.base.TimeRange
	(*OrderBy)(nil),               // 8: tanlive.base.OrderBy
	(*Identity)(nil),              // 9: tanlive.base.Identity
	(*durationpb.Duration)(nil),   // 10: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil), // 11: google.protobuf.Timestamp
}
var file_tanlive_base_base_proto_depIdxs = []int32{
	10, // 0: tanlive.base.RateLimitResult.reset_after:type_name -> google.protobuf.Duration
	10, // 1: tanlive.base.RateLimitResult.retry_after:type_name -> google.protobuf.Duration
	11, // 2: tanlive.base.TimeRange.start:type_name -> google.protobuf.Timestamp
	11, // 3: tanlive.base.TimeRange.end:type_name -> google.protobuf.Timestamp
	4,  // 4: tanlive.base.Identity.identity_type:type_name -> tanlive.base.IdentityType
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_tanlive_base_base_proto_init() }
func file_tanlive_base_base_proto_init() {
	if File_tanlive_base_base_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_base_base_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_base_base_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Paginator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_base_base_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_base_base_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_base_base_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Identity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_base_base_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_base_base_proto_goTypes,
		DependencyIndexes: file_tanlive_base_base_proto_depIdxs,
		EnumInfos:         file_tanlive_base_base_proto_enumTypes,
		MessageInfos:      file_tanlive_base_base_proto_msgTypes,
	}.Build()
	File_tanlive_base_base_proto = out.File
	file_tanlive_base_base_proto_rawDesc = nil
	file_tanlive_base_base_proto_goTypes = nil
	file_tanlive_base_base_proto_depIdxs = nil
}
