syntax = "proto3";

package tanlive.base;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base";

// UGC状态
enum UgcState {
  UGC_STATE_UNSPECIFIED = 0;
  // 草稿。用户前台：草稿；运营后台：草稿
  UGC_STATE_DRAFTING = 1;
  // 自动审核中。用户前台：处理中；运营后台：处理中
  UGC_STATE_AUTO_REVIEWING = 2;
  // 人工审核中（用户前台：处理中；运营后台：待处理）
  UGC_STATE_MANUAL_REVIEWING = 3;
  // 人工审核中-可疑（用户前台：处理中；运营后台：可疑，待处理）
  UGC_STATE_MANUAL_REVIEWING_SUSPICIOUS = 4;
  // 审核已通过（用户前台：下架存档；运营后台：下架存档）
  UGC_STATE_PASSED = 5;
  // 已发布（用户前台：已发布；运营后台：已发布）
  UGC_STATE_PUBLISHED = 6;
  // 审核已驳回（用户前台：已驳回；运营后台：已驳回）
  UGC_STATE_REJECTED = 7;
  // 申诉中（用户前台：申诉中；运营后台：申诉待处理）
  UGC_STATE_APPEALING = 8;
}

// 可见类型
enum VisibleType {
  // 全网可见
  VISIBLE_TYPE_PUBLIC = 0;
  // 不公开
  VISIBLE_TYPE_PRIVATE = 1;
  // 仅注册用户
  VISIBLE_TYPE_REGISTERED_USER = 2;
  // 仅认证团队
  VISIBLE_TYPE_VERIFIED_TEAM = 3;
}

// 可见性规则
message VisibleRule {
  // 可见类型
  VisibleType visible_type = 1;
  // 是否全选
  bool select_all = 2;
  // 可见标签
  repeated string tags = 3;
  // 是否包含自定义标签
  bool has_custom_tag = 4;
}

// 贡献者类型
enum ContributorType {
  CONTRIBUTOR_TYPE_UNSPECIFIED = 0;
  // 团队
  CONTRIBUTOR_TYPE_TEAM = 1;
  // 个人
  CONTRIBUTOR_TYPE_USER = 2;
}

// 编辑者类型
enum EditorType {
  EDITOR_TYPE_UNSPECIFIED = 0;
  // 团队
  EDITOR_TYPE_TEAM = 1;
  // 个人
  EDITOR_TYPE_USER = 2;
}

// 数据类型
enum DataType {
  DATA_TYPE_UNSPECIFIED = 0;
  // 团队
  DATA_TYPE_TEAM = 1;
  // 产品
  DATA_TYPE_PRODUCT = 2;
  // 资源
  DATA_TYPE_RESOURCE = 3;
  // 图谱
  DATA_TYPE_GRAPH = 4;
  // 定向推送
  DATA_TYPE_NOTIFY = 5;
  // 用户个人
  DATA_TYPE_USER = 6;
  // 图谱AI
  DATA_TYPE_GRAPH_AI = 7;
  // 帮助中心文档
  DATA_TYPE_HELP_CENTER = 8;
  // AI助手
  DATA_TYPE_AI_ASSISTANT = 9;
}

// 数据项
message DataItem {
  // 数据ID
  uint64 data_id = 1;
  // 数据类型
  DataType data_type = 2;
  // 数据字段
  string data_field = 3;
}

// 地域
enum Region {
  REGION_UNSPECIFIED = 0;
  // 国内
  REGION_NATIONAL = 1;
  // 海外
  REGION_INTERNATIONAL = 2;
}
