// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/base/review.proto

package base

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 驳回原因类型
type RejectReasonType int32

const (
	// 其它
	RejectReasonType_REJECT_REASON_TYPE_OTHER RejectReasonType = 0
	// 内容违反使用条款
	RejectReasonType_REJECT_REASON_TYPE_VIOLATE_TERMS RejectReasonType = 1
	// 内容含不支持的语言
	RejectReasonType_REJECT_REASON_TYPE_UNSUPPORTED_LANG RejectReasonType = 2
	// 消息信息缺失
	RejectReasonType_REJECT_REASON_TYPE_NOTIFY_INFO_MISSING RejectReasonType = 3
	// 消息中有错别字或标点使用不当
	RejectReasonType_REJECT_REASON_TYPE_NOTIFY_HAS_TYPO RejectReasonType = 4
	// 消息模版选择错误
	RejectReasonType_REJECT_REASON_TYPE_NOTIFY_WRONG_TMPL RejectReasonType = 5
)

// Enum value maps for RejectReasonType.
var (
	RejectReasonType_name = map[int32]string{
		0: "REJECT_REASON_TYPE_OTHER",
		1: "REJECT_REASON_TYPE_VIOLATE_TERMS",
		2: "REJECT_REASON_TYPE_UNSUPPORTED_LANG",
		3: "REJECT_REASON_TYPE_NOTIFY_INFO_MISSING",
		4: "REJECT_REASON_TYPE_NOTIFY_HAS_TYPO",
		5: "REJECT_REASON_TYPE_NOTIFY_WRONG_TMPL",
	}
	RejectReasonType_value = map[string]int32{
		"REJECT_REASON_TYPE_OTHER":               0,
		"REJECT_REASON_TYPE_VIOLATE_TERMS":       1,
		"REJECT_REASON_TYPE_UNSUPPORTED_LANG":    2,
		"REJECT_REASON_TYPE_NOTIFY_INFO_MISSING": 3,
		"REJECT_REASON_TYPE_NOTIFY_HAS_TYPO":     4,
		"REJECT_REASON_TYPE_NOTIFY_WRONG_TMPL":   5,
	}
)

func (x RejectReasonType) Enum() *RejectReasonType {
	p := new(RejectReasonType)
	*p = x
	return p
}

func (x RejectReasonType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RejectReasonType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_base_review_proto_enumTypes[0].Descriptor()
}

func (RejectReasonType) Type() protoreflect.EnumType {
	return &file_tanlive_base_review_proto_enumTypes[0]
}

func (x RejectReasonType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RejectReasonType.Descriptor instead.
func (RejectReasonType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_base_review_proto_rawDescGZIP(), []int{0}
}

// 驳回原因
type RejectReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 原因类型
	ReasonType RejectReasonType `protobuf:"varint,1,opt,name=reason_type,json=reasonType,proto3,enum=tanlive.base.RejectReasonType" json:"reason_type,omitempty"`
	// 其他原因
	OtherReason string `protobuf:"bytes,2,opt,name=other_reason,json=otherReason,proto3" json:"other_reason,omitempty"`
	// 自动任务结果
	AutoJobResults []*AutoJobResult `protobuf:"bytes,3,rep,name=auto_job_results,json=autoJobResults,proto3" json:"auto_job_results,omitempty"`
}

func (x *RejectReason) Reset() {
	*x = RejectReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_review_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RejectReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectReason) ProtoMessage() {}

func (x *RejectReason) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_review_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectReason.ProtoReflect.Descriptor instead.
func (*RejectReason) Descriptor() ([]byte, []int) {
	return file_tanlive_base_review_proto_rawDescGZIP(), []int{0}
}

func (x *RejectReason) GetReasonType() RejectReasonType {
	if x != nil {
		return x.ReasonType
	}
	return RejectReasonType_REJECT_REASON_TYPE_OTHER
}

func (x *RejectReason) GetOtherReason() string {
	if x != nil {
		return x.OtherReason
	}
	return ""
}

func (x *RejectReason) GetAutoJobResults() []*AutoJobResult {
	if x != nil {
		return x.AutoJobResults
	}
	return nil
}

// 自动任务结果
type AutoJobResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据字段
	DataField string `protobuf:"bytes,1,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
	// 任务结果
	JobResult int32 `protobuf:"varint,2,opt,name=job_result,json=jobResult,proto3" json:"job_result,omitempty"`
	// 任务标签
	JobLabel string `protobuf:"bytes,3,opt,name=job_label,json=jobLabel,proto3" json:"job_label,omitempty"`
}

func (x *AutoJobResult) Reset() {
	*x = AutoJobResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_base_review_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoJobResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoJobResult) ProtoMessage() {}

func (x *AutoJobResult) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_base_review_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoJobResult.ProtoReflect.Descriptor instead.
func (*AutoJobResult) Descriptor() ([]byte, []int) {
	return file_tanlive_base_review_proto_rawDescGZIP(), []int{1}
}

func (x *AutoJobResult) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

func (x *AutoJobResult) GetJobResult() int32 {
	if x != nil {
		return x.JobResult
	}
	return 0
}

func (x *AutoJobResult) GetJobLabel() string {
	if x != nil {
		return x.JobLabel
	}
	return ""
}

var File_tanlive_base_review_proto protoreflect.FileDescriptor

var file_tanlive_base_review_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe8, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x50, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x82, 0x88, 0x27, 0x0b, 0x6d, 0x69, 0x6e, 0x3d,
	0x30, 0x2c, 0x6d, 0x61, 0x78, 0x3d, 0x35, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0x82, 0x88, 0x27, 0x18, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x66, 0x3d, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x20, 0x30, 0x52, 0x0b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6a, 0x6f, 0x62,
	0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0e, 0x61, 0x75, 0x74,
	0x6f, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x6a, 0x0a, 0x0d, 0x41,
	0x75, 0x74, 0x6f, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6a,
	0x6f, 0x62, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x6a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f,
	0x62, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a,
	0x6f, 0x62, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x2a, 0xfd, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x45,
	0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x56, 0x49, 0x4f, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x53, 0x10, 0x01,
	0x12, 0x27, 0x0a, 0x23, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54,
	0x45, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x52, 0x45, 0x4a,
	0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x4d, 0x49, 0x53, 0x53,
	0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x59, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x4f, 0x10, 0x04, 0x12, 0x28, 0x0a,
	0x24, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47,
	0x5f, 0x54, 0x4d, 0x50, 0x4c, 0x10, 0x05, 0x42, 0x3e, 0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_base_review_proto_rawDescOnce sync.Once
	file_tanlive_base_review_proto_rawDescData = file_tanlive_base_review_proto_rawDesc
)

func file_tanlive_base_review_proto_rawDescGZIP() []byte {
	file_tanlive_base_review_proto_rawDescOnce.Do(func() {
		file_tanlive_base_review_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_base_review_proto_rawDescData)
	})
	return file_tanlive_base_review_proto_rawDescData
}

var file_tanlive_base_review_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_base_review_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tanlive_base_review_proto_goTypes = []interface{}{
	(RejectReasonType)(0), // 0: tanlive.base.RejectReasonType
	(*RejectReason)(nil),  // 1: tanlive.base.RejectReason
	(*AutoJobResult)(nil), // 2: tanlive.base.AutoJobResult
}
var file_tanlive_base_review_proto_depIdxs = []int32{
	0, // 0: tanlive.base.RejectReason.reason_type:type_name -> tanlive.base.RejectReasonType
	2, // 1: tanlive.base.RejectReason.auto_job_results:type_name -> tanlive.base.AutoJobResult
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_tanlive_base_review_proto_init() }
func file_tanlive_base_review_proto_init() {
	if File_tanlive_base_review_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_base_review_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RejectReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_base_review_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoJobResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_base_review_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_base_review_proto_goTypes,
		DependencyIndexes: file_tanlive_base_review_proto_depIdxs,
		EnumInfos:         file_tanlive_base_review_proto_enumTypes,
		MessageInfos:      file_tanlive_base_review_proto_msgTypes,
	}.Build()
	File_tanlive_base_review_proto = out.File
	file_tanlive_base_review_proto_rawDesc = nil
	file_tanlive_base_review_proto_goTypes = nil
	file_tanlive_base_review_proto_depIdxs = nil
}
