// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/graph/service.proto

package graph

import (
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for GraphService service

func NewGraphServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for GraphService service

type GraphService interface {
	// 流程配置列表
	DescribeListProcess(ctx context.Context, in *ReqDescribeListProcess, opts ...client.CallOption) (*RspDescribeListProcess, error)
	// 流程引擎添加
	CreateProcessEngine(ctx context.Context, in *ReqCreateProcessEngine, opts ...client.CallOption) (*ReqCreateProcessEngine, error)
	// 流程引擎编辑
	ModifyProcessEngine(ctx context.Context, in *ReqModifyProcessEngine, opts ...client.CallOption) (*RspEmpty, error)
	// 版本启用
	ModifyEnableProcessEngine(ctx context.Context, in *ReqModifyEnableProcessEngine, opts ...client.CallOption) (*RspEmpty, error)
	// 流程引擎删除
	DeleteProcessEngine(ctx context.Context, in *ReqDeleteProcessEngine, opts ...client.CallOption) (*RspEmpty, error)
}

type graphService struct {
	c    client.Client
	name string
}

func NewGraphService(name string, c client.Client) GraphService {
	return &graphService{
		c:    c,
		name: name,
	}
}

func (c *graphService) DescribeListProcess(ctx context.Context, in *ReqDescribeListProcess, opts ...client.CallOption) (*RspDescribeListProcess, error) {
	req := c.c.NewRequest(c.name, "GraphService.DescribeListProcess", in)
	out := new(RspDescribeListProcess)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *graphService) CreateProcessEngine(ctx context.Context, in *ReqCreateProcessEngine, opts ...client.CallOption) (*ReqCreateProcessEngine, error) {
	req := c.c.NewRequest(c.name, "GraphService.CreateProcessEngine", in)
	out := new(ReqCreateProcessEngine)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *graphService) ModifyProcessEngine(ctx context.Context, in *ReqModifyProcessEngine, opts ...client.CallOption) (*RspEmpty, error) {
	req := c.c.NewRequest(c.name, "GraphService.ModifyProcessEngine", in)
	out := new(RspEmpty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *graphService) ModifyEnableProcessEngine(ctx context.Context, in *ReqModifyEnableProcessEngine, opts ...client.CallOption) (*RspEmpty, error) {
	req := c.c.NewRequest(c.name, "GraphService.ModifyEnableProcessEngine", in)
	out := new(RspEmpty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *graphService) DeleteProcessEngine(ctx context.Context, in *ReqDeleteProcessEngine, opts ...client.CallOption) (*RspEmpty, error) {
	req := c.c.NewRequest(c.name, "GraphService.DeleteProcessEngine", in)
	out := new(RspEmpty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for GraphService service

type GraphServiceHandler interface {
	// 流程配置列表
	DescribeListProcess(context.Context, *ReqDescribeListProcess, *RspDescribeListProcess) error
	// 流程引擎添加
	CreateProcessEngine(context.Context, *ReqCreateProcessEngine, *ReqCreateProcessEngine) error
	// 流程引擎编辑
	ModifyProcessEngine(context.Context, *ReqModifyProcessEngine, *RspEmpty) error
	// 版本启用
	ModifyEnableProcessEngine(context.Context, *ReqModifyEnableProcessEngine, *RspEmpty) error
	// 流程引擎删除
	DeleteProcessEngine(context.Context, *ReqDeleteProcessEngine, *RspEmpty) error
}

func RegisterGraphServiceHandler(s server.Server, hdlr GraphServiceHandler, opts ...server.HandlerOption) error {
	type graphService interface {
		DescribeListProcess(ctx context.Context, in *ReqDescribeListProcess, out *RspDescribeListProcess) error
		CreateProcessEngine(ctx context.Context, in *ReqCreateProcessEngine, out *ReqCreateProcessEngine) error
		ModifyProcessEngine(ctx context.Context, in *ReqModifyProcessEngine, out *RspEmpty) error
		ModifyEnableProcessEngine(ctx context.Context, in *ReqModifyEnableProcessEngine, out *RspEmpty) error
		DeleteProcessEngine(ctx context.Context, in *ReqDeleteProcessEngine, out *RspEmpty) error
	}
	type GraphService struct {
		graphService
	}
	h := &graphServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&GraphService{h}, opts...))
}

type graphServiceHandler struct {
	GraphServiceHandler
}

func (h *graphServiceHandler) DescribeListProcess(ctx context.Context, in *ReqDescribeListProcess, out *RspDescribeListProcess) error {
	return h.GraphServiceHandler.DescribeListProcess(ctx, in, out)
}

func (h *graphServiceHandler) CreateProcessEngine(ctx context.Context, in *ReqCreateProcessEngine, out *ReqCreateProcessEngine) error {
	return h.GraphServiceHandler.CreateProcessEngine(ctx, in, out)
}

func (h *graphServiceHandler) ModifyProcessEngine(ctx context.Context, in *ReqModifyProcessEngine, out *RspEmpty) error {
	return h.GraphServiceHandler.ModifyProcessEngine(ctx, in, out)
}

func (h *graphServiceHandler) ModifyEnableProcessEngine(ctx context.Context, in *ReqModifyEnableProcessEngine, out *RspEmpty) error {
	return h.GraphServiceHandler.ModifyEnableProcessEngine(ctx, in, out)
}

func (h *graphServiceHandler) DeleteProcessEngine(ctx context.Context, in *ReqDeleteProcessEngine, out *RspEmpty) error {
	return h.GraphServiceHandler.DeleteProcessEngine(ctx, in, out)
}
