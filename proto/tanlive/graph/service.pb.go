// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/graph/service.proto

package graph

import (
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqDescribeListProcess struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchName  string          `protobuf:"bytes,1,opt,name=search_name,json=searchName,proto3" json:"search_name,omitempty"`
	Offset      uint32          `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit       uint32          `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	OrderBy     []*base.OrderBy `protobuf:"bytes,4,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Id          uint64          `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	IsGraphOpen bool            `protobuf:"varint,6,opt,name=is_graph_open,json=isGraphOpen,proto3" json:"is_graph_open,omitempty"`
	Lang        uint32          `protobuf:"varint,7,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqDescribeListProcess) Reset() {
	*x = ReqDescribeListProcess{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeListProcess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeListProcess) ProtoMessage() {}

func (x *ReqDescribeListProcess) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeListProcess.ProtoReflect.Descriptor instead.
func (*ReqDescribeListProcess) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqDescribeListProcess) GetSearchName() string {
	if x != nil {
		return x.SearchName
	}
	return ""
}

func (x *ReqDescribeListProcess) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeListProcess) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeListProcess) GetOrderBy() []*base.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ReqDescribeListProcess) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReqDescribeListProcess) GetIsGraphOpen() bool {
	if x != nil {
		return x.IsGraphOpen
	}
	return false
}

func (x *ReqDescribeListProcess) GetLang() uint32 {
	if x != nil {
		return x.Lang
	}
	return 0
}

type RspDescribeListProcess struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCount uint32         `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	Processes  []*ProcessInfo `protobuf:"bytes,2,rep,name=processes,proto3" json:"processes,omitempty"`
}

func (x *RspDescribeListProcess) Reset() {
	*x = RspDescribeListProcess{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeListProcess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeListProcess) ProtoMessage() {}

func (x *RspDescribeListProcess) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeListProcess.ProtoReflect.Descriptor instead.
func (*RspDescribeListProcess) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{1}
}

func (x *RspDescribeListProcess) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *RspDescribeListProcess) GetProcesses() []*ProcessInfo {
	if x != nil {
		return x.Processes
	}
	return nil
}

type ReqCreateProcessEngine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessName  string `protobuf:"bytes,1,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`
	Category     uint32 `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"`
	YamlConfig   string `protobuf:"bytes,3,opt,name=yaml_config,json=yamlConfig,proto3" json:"yaml_config,omitempty"`
	Remark       string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	CreateBy     uint64 `protobuf:"varint,6,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	LastUpdateBy uint64 `protobuf:"varint,7,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	Lang         uint32 `protobuf:"varint,8,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqCreateProcessEngine) Reset() {
	*x = ReqCreateProcessEngine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateProcessEngine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateProcessEngine) ProtoMessage() {}

func (x *ReqCreateProcessEngine) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateProcessEngine.ProtoReflect.Descriptor instead.
func (*ReqCreateProcessEngine) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReqCreateProcessEngine) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *ReqCreateProcessEngine) GetCategory() uint32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *ReqCreateProcessEngine) GetYamlConfig() string {
	if x != nil {
		return x.YamlConfig
	}
	return ""
}

func (x *ReqCreateProcessEngine) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *ReqCreateProcessEngine) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *ReqCreateProcessEngine) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *ReqCreateProcessEngine) GetLang() uint32 {
	if x != nil {
		return x.Lang
	}
	return 0
}

type ReqDescribeListVersions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchName string   `protobuf:"bytes,1,opt,name=search_name,json=searchName,proto3" json:"search_name,omitempty"`
	Offset     uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit      uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	OrderBy    []string `protobuf:"bytes,4,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Id         uint64   `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ReqDescribeListVersions) Reset() {
	*x = ReqDescribeListVersions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeListVersions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeListVersions) ProtoMessage() {}

func (x *ReqDescribeListVersions) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeListVersions.ProtoReflect.Descriptor instead.
func (*ReqDescribeListVersions) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{3}
}

func (x *ReqDescribeListVersions) GetSearchName() string {
	if x != nil {
		return x.SearchName
	}
	return ""
}

func (x *ReqDescribeListVersions) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeListVersions) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeListVersions) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ReqDescribeListVersions) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type RspDescribeListVersions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCount uint32         `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	Versions   []*VersionInfo `protobuf:"bytes,2,rep,name=versions,proto3" json:"versions,omitempty"`
}

func (x *RspDescribeListVersions) Reset() {
	*x = RspDescribeListVersions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeListVersions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeListVersions) ProtoMessage() {}

func (x *RspDescribeListVersions) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeListVersions.ProtoReflect.Descriptor instead.
func (*RspDescribeListVersions) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{4}
}

func (x *RspDescribeListVersions) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *RspDescribeListVersions) GetVersions() []*VersionInfo {
	if x != nil {
		return x.Versions
	}
	return nil
}

type ReqModifyProcessEngine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessName  string `protobuf:"bytes,1,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`
	Category     uint32 `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"`
	YamlConfig   string `protobuf:"bytes,3,opt,name=yaml_config,json=yamlConfig,proto3" json:"yaml_config,omitempty"`
	Remark       string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	Id           uint64 `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	LastUpdateBy uint64 `protobuf:"varint,7,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	Lang         uint32 `protobuf:"varint,8,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ReqModifyProcessEngine) Reset() {
	*x = ReqModifyProcessEngine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyProcessEngine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyProcessEngine) ProtoMessage() {}

func (x *ReqModifyProcessEngine) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyProcessEngine.ProtoReflect.Descriptor instead.
func (*ReqModifyProcessEngine) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{5}
}

func (x *ReqModifyProcessEngine) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *ReqModifyProcessEngine) GetCategory() uint32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *ReqModifyProcessEngine) GetYamlConfig() string {
	if x != nil {
		return x.YamlConfig
	}
	return ""
}

func (x *ReqModifyProcessEngine) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *ReqModifyProcessEngine) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReqModifyProcessEngine) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *ReqModifyProcessEngine) GetLang() uint32 {
	if x != nil {
		return x.Lang
	}
	return 0
}

type ReqModifyEnableProcessEngine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 1启用 2禁用
	IsOnlineVersion bool   `protobuf:"varint,2,opt,name=is_online_version,json=isOnlineVersion,proto3" json:"is_online_version,omitempty"`
	LastUpdateBy    uint64 `protobuf:"varint,3,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
}

func (x *ReqModifyEnableProcessEngine) Reset() {
	*x = ReqModifyEnableProcessEngine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqModifyEnableProcessEngine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqModifyEnableProcessEngine) ProtoMessage() {}

func (x *ReqModifyEnableProcessEngine) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqModifyEnableProcessEngine.ProtoReflect.Descriptor instead.
func (*ReqModifyEnableProcessEngine) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{6}
}

func (x *ReqModifyEnableProcessEngine) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReqModifyEnableProcessEngine) GetIsOnlineVersion() bool {
	if x != nil {
		return x.IsOnlineVersion
	}
	return false
}

func (x *ReqModifyEnableProcessEngine) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

type ReqDeleteProcessEngine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ReqDeleteProcessEngine) Reset() {
	*x = ReqDeleteProcessEngine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteProcessEngine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteProcessEngine) ProtoMessage() {}

func (x *ReqDeleteProcessEngine) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteProcessEngine.ProtoReflect.Descriptor instead.
func (*ReqDeleteProcessEngine) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{7}
}

func (x *ReqDeleteProcessEngine) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ReqDeleteListVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ReqDeleteListVersion) Reset() {
	*x = ReqDeleteListVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteListVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteListVersion) ProtoMessage() {}

func (x *ReqDeleteListVersion) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteListVersion.ProtoReflect.Descriptor instead.
func (*ReqDeleteListVersion) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_service_proto_rawDescGZIP(), []int{8}
}

func (x *ReqDeleteListVersion) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_tanlive_graph_service_proto protoreflect.FileDescriptor

var file_tanlive_graph_service_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x1a, 0x19, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2f, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe1, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x30, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d,
	0x69, 0x73, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x47, 0x72, 0x61, 0x70, 0x68, 0x4f, 0x70, 0x65, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x22, 0x73, 0x0a, 0x16, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x38, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0xe7, 0x01, 0x0a, 0x16, 0x52, 0x65,
	0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x79, 0x61, 0x6d, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x79, 0x61, 0x6d, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x6c,
	0x61, 0x6e, 0x67, 0x22, 0x93, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x72, 0x0a, 0x17, 0x52, 0x73, 0x70,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xda, 0x01,
	0x0a, 0x16, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x79, 0x61, 0x6d, 0x6c, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x79, 0x61,
	0x6d, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x80, 0x01, 0x0a, 0x1c, 0x52,
	0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x69,
	0x73, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x22, 0x28, 0x0a,
	0x16, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x26, 0x0a, 0x14, 0x52, 0x65, 0x71, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x32,
	0xe9, 0x03, 0x0a, 0x0c, 0x47, 0x72, 0x61, 0x70, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x63, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x1a, 0x25,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x52,
	0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x63, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x25, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x52, 0x65, 0x71,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x1a, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x55, 0x0a, 0x13, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x12, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x2e, 0x52, 0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x1a, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x61, 0x0a, 0x19, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x2b,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x52,
	0x65, 0x71, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x1a, 0x17, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x55, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x25, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x1a, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x2e, 0x52, 0x73, 0x70, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x3f, 0x5a, 0x3d, 0x65,
	0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_graph_service_proto_rawDescOnce sync.Once
	file_tanlive_graph_service_proto_rawDescData = file_tanlive_graph_service_proto_rawDesc
)

func file_tanlive_graph_service_proto_rawDescGZIP() []byte {
	file_tanlive_graph_service_proto_rawDescOnce.Do(func() {
		file_tanlive_graph_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_graph_service_proto_rawDescData)
	})
	return file_tanlive_graph_service_proto_rawDescData
}

var file_tanlive_graph_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_tanlive_graph_service_proto_goTypes = []interface{}{
	(*ReqDescribeListProcess)(nil),       // 0: tanlive.graph.ReqDescribeListProcess
	(*RspDescribeListProcess)(nil),       // 1: tanlive.graph.RspDescribeListProcess
	(*ReqCreateProcessEngine)(nil),       // 2: tanlive.graph.ReqCreateProcessEngine
	(*ReqDescribeListVersions)(nil),      // 3: tanlive.graph.ReqDescribeListVersions
	(*RspDescribeListVersions)(nil),      // 4: tanlive.graph.RspDescribeListVersions
	(*ReqModifyProcessEngine)(nil),       // 5: tanlive.graph.ReqModifyProcessEngine
	(*ReqModifyEnableProcessEngine)(nil), // 6: tanlive.graph.ReqModifyEnableProcessEngine
	(*ReqDeleteProcessEngine)(nil),       // 7: tanlive.graph.ReqDeleteProcessEngine
	(*ReqDeleteListVersion)(nil),         // 8: tanlive.graph.ReqDeleteListVersion
	(*base.OrderBy)(nil),                 // 9: tanlive.base.OrderBy
	(*ProcessInfo)(nil),                  // 10: tanlive.graph.ProcessInfo
	(*VersionInfo)(nil),                  // 11: tanlive.graph.VersionInfo
	(*RspEmpty)(nil),                     // 12: tanlive.graph.RspEmpty
}
var file_tanlive_graph_service_proto_depIdxs = []int32{
	9,  // 0: tanlive.graph.ReqDescribeListProcess.order_by:type_name -> tanlive.base.OrderBy
	10, // 1: tanlive.graph.RspDescribeListProcess.processes:type_name -> tanlive.graph.ProcessInfo
	11, // 2: tanlive.graph.RspDescribeListVersions.versions:type_name -> tanlive.graph.VersionInfo
	0,  // 3: tanlive.graph.GraphService.DescribeListProcess:input_type -> tanlive.graph.ReqDescribeListProcess
	2,  // 4: tanlive.graph.GraphService.CreateProcessEngine:input_type -> tanlive.graph.ReqCreateProcessEngine
	5,  // 5: tanlive.graph.GraphService.ModifyProcessEngine:input_type -> tanlive.graph.ReqModifyProcessEngine
	6,  // 6: tanlive.graph.GraphService.ModifyEnableProcessEngine:input_type -> tanlive.graph.ReqModifyEnableProcessEngine
	7,  // 7: tanlive.graph.GraphService.DeleteProcessEngine:input_type -> tanlive.graph.ReqDeleteProcessEngine
	1,  // 8: tanlive.graph.GraphService.DescribeListProcess:output_type -> tanlive.graph.RspDescribeListProcess
	2,  // 9: tanlive.graph.GraphService.CreateProcessEngine:output_type -> tanlive.graph.ReqCreateProcessEngine
	12, // 10: tanlive.graph.GraphService.ModifyProcessEngine:output_type -> tanlive.graph.RspEmpty
	12, // 11: tanlive.graph.GraphService.ModifyEnableProcessEngine:output_type -> tanlive.graph.RspEmpty
	12, // 12: tanlive.graph.GraphService.DeleteProcessEngine:output_type -> tanlive.graph.RspEmpty
	8,  // [8:13] is the sub-list for method output_type
	3,  // [3:8] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_tanlive_graph_service_proto_init() }
func file_tanlive_graph_service_proto_init() {
	if File_tanlive_graph_service_proto != nil {
		return
	}
	file_tanlive_graph_graph_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_graph_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeListProcess); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeListProcess); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateProcessEngine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeListVersions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeListVersions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyProcessEngine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqModifyEnableProcessEngine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteProcessEngine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteListVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_graph_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_graph_service_proto_goTypes,
		DependencyIndexes: file_tanlive_graph_service_proto_depIdxs,
		MessageInfos:      file_tanlive_graph_service_proto_msgTypes,
	}.Build()
	File_tanlive_graph_service_proto = out.File
	file_tanlive_graph_service_proto_rawDesc = nil
	file_tanlive_graph_service_proto_goTypes = nil
	file_tanlive_graph_service_proto_depIdxs = nil
}
