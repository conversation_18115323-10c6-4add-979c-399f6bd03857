// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package graph

import (
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
)

func (x *ReqDescribeListProcess) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeListProcess)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeListProcess)
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqDescribeListProcess) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeListProcess)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqDescribeListProcess) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeListProcess)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}

	return y
}

func (x *RspDescribeListProcess) MaskInLog() any {
	if x == nil {
		return (*RspDescribeListProcess)(nil)
	}

	y := proto.Clone(x).(*RspDescribeListProcess)
	for k, v := range y.Processes {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Processes[k] = vv.MaskInLog().(*ProcessInfo)
		}
	}

	return y
}

func (x *RspDescribeListProcess) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeListProcess)(nil)
	}

	y := x
	for k, v := range y.Processes {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Processes[k] = vv.MaskInRpc().(*ProcessInfo)
		}
	}

	return y
}

func (x *RspDescribeListProcess) MaskInBff() any {
	if x == nil {
		return (*RspDescribeListProcess)(nil)
	}

	y := x
	for k, v := range y.Processes {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Processes[k] = vv.MaskInBff().(*ProcessInfo)
		}
	}

	return y
}

func (x *RspDescribeListVersions) MaskInLog() any {
	if x == nil {
		return (*RspDescribeListVersions)(nil)
	}

	y := proto.Clone(x).(*RspDescribeListVersions)
	for k, v := range y.Versions {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Versions[k] = vv.MaskInLog().(*VersionInfo)
		}
	}

	return y
}

func (x *RspDescribeListVersions) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeListVersions)(nil)
	}

	y := x
	for k, v := range y.Versions {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Versions[k] = vv.MaskInRpc().(*VersionInfo)
		}
	}

	return y
}

func (x *RspDescribeListVersions) MaskInBff() any {
	if x == nil {
		return (*RspDescribeListVersions)(nil)
	}

	y := x
	for k, v := range y.Versions {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Versions[k] = vv.MaskInBff().(*VersionInfo)
		}
	}

	return y
}

func (x *ReqDescribeListProcess) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeListProcess) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Processes {
		if sanitizer, ok := any(x.Processes[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeListVersions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Versions {
		if sanitizer, ok := any(x.Versions[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
