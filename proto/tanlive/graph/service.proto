syntax = "proto3";

package tanlive.graph;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/graph";
import "tanlive/graph/graph.proto";
import "tanlive/base/base.proto";

message ReqDescribeListProcess {
  string search_name = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated tanlive.base.OrderBy order_by = 4;
  uint64 id = 5;
  bool is_graph_open = 6;
  uint32 lang = 7;
}

message RspDescribeListProcess {
  uint32 total_count = 1;
  repeated ProcessInfo processes = 2;
}

message ReqCreateProcessEngine {
  string process_name = 1;
  uint32 category = 2;
  string yaml_config = 3;
  string remark = 4;
  uint64 create_by = 6;
  uint64 last_update_by = 7;
  uint32 lang = 8;
}

message ReqDescribeListVersions {
  string search_name = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated string order_by = 4;
  uint64 id = 5;
}

message RspDescribeListVersions {
  uint32 total_count = 1;
  repeated VersionInfo versions = 2;
}

message ReqModifyProcessEngine {
  string process_name = 1;
  uint32 category = 2;
  string yaml_config = 3;
  string remark = 4;
  uint64 id = 5;
  uint64 last_update_by = 7;
  uint32 lang = 8;
}

message ReqModifyEnableProcessEngine {
  uint64 id = 1;
  // 1启用 2禁用
  bool is_online_version = 2;
  uint64 last_update_by = 3;
}

message ReqDeleteProcessEngine {
  uint64 id = 1;
}

message ReqDeleteListVersion {
  uint64 id = 1;
}

// 图谱服务
service GraphService {

  // 流程配置列表
  rpc DescribeListProcess(ReqDescribeListProcess) returns (RspDescribeListProcess);
  // 流程引擎添加
  rpc CreateProcessEngine(ReqCreateProcessEngine) returns (ReqCreateProcessEngine);
  // 流程引擎编辑
  rpc ModifyProcessEngine(ReqModifyProcessEngine) returns (RspEmpty);
  // 版本启用
  rpc ModifyEnableProcessEngine(ReqModifyEnableProcessEngine) returns (RspEmpty);
  // 流程引擎删除
  rpc DeleteProcessEngine(ReqDeleteProcessEngine) returns (RspEmpty);

}
