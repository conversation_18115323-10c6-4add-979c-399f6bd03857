// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/graph/graph.proto

package graph

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 流程名称
	ProcessName string `protobuf:"bytes,1,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`
	// 流程类别 1图谱 2资源 3团队 4产品
	Category uint32 `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"`
	// 备注
	Remark string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	// 创建人
	CreateBy uint64 `protobuf:"varint,4,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// yaml配置
	YamlConfig string `protobuf:"bytes,5,opt,name=yaml_config,json=yamlConfig,proto3" json:"yaml_config,omitempty"`
	// 创建时间
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 修改时间
	LastUpdateDate  *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
	Id              uint64                 `protobuf:"varint,8,opt,name=id,proto3" json:"id,omitempty"`
	LastUpdateBy    uint64                 `protobuf:"varint,9,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	IsOnlineVersion uint32                 `protobuf:"varint,10,opt,name=is_online_version,json=isOnlineVersion,proto3" json:"is_online_version,omitempty"`
	Lang            uint32                 `protobuf:"varint,11,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ProcessInfo) Reset() {
	*x = ProcessInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_graph_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInfo) ProtoMessage() {}

func (x *ProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_graph_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInfo.ProtoReflect.Descriptor instead.
func (*ProcessInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_graph_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessInfo) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *ProcessInfo) GetCategory() uint32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *ProcessInfo) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *ProcessInfo) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *ProcessInfo) GetYamlConfig() string {
	if x != nil {
		return x.YamlConfig
	}
	return ""
}

func (x *ProcessInfo) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *ProcessInfo) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

func (x *ProcessInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProcessInfo) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *ProcessInfo) GetIsOnlineVersion() uint32 {
	if x != nil {
		return x.IsOnlineVersion
	}
	return 0
}

func (x *ProcessInfo) GetLang() uint32 {
	if x != nil {
		return x.Lang
	}
	return 0
}

type VersionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 流程名称
	ProcessName string `protobuf:"bytes,1,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`
	// 流程类别
	Category uint32 `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"`
	// 备注
	Remark string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	// 版本号
	VersionNumber string `protobuf:"bytes,4,opt,name=version_number,json=versionNumber,proto3" json:"version_number,omitempty"`
	// 是否线上版本
	IsOnlineVersion bool `protobuf:"varint,5,opt,name=is_online_version,json=isOnlineVersion,proto3" json:"is_online_version,omitempty"`
	// 创建人
	Creator string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	// 创建时间
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 修改时间
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
	Id             uint64                 `protobuf:"varint,9,opt,name=id,proto3" json:"id,omitempty"`
	Lang           uint32                 `protobuf:"varint,10,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *VersionInfo) Reset() {
	*x = VersionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_graph_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionInfo) ProtoMessage() {}

func (x *VersionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_graph_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionInfo.ProtoReflect.Descriptor instead.
func (*VersionInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_graph_proto_rawDescGZIP(), []int{1}
}

func (x *VersionInfo) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *VersionInfo) GetCategory() uint32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *VersionInfo) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *VersionInfo) GetVersionNumber() string {
	if x != nil {
		return x.VersionNumber
	}
	return ""
}

func (x *VersionInfo) GetIsOnlineVersion() bool {
	if x != nil {
		return x.IsOnlineVersion
	}
	return false
}

func (x *VersionInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *VersionInfo) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *VersionInfo) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

func (x *VersionInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VersionInfo) GetLang() uint32 {
	if x != nil {
		return x.Lang
	}
	return 0
}

type RspEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RspEmpty) Reset() {
	*x = RspEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_graph_graph_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspEmpty) ProtoMessage() {}

func (x *RspEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_graph_graph_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspEmpty.ProtoReflect.Descriptor instead.
func (*RspEmpty) Descriptor() ([]byte, []int) {
	return file_tanlive_graph_graph_proto_rawDescGZIP(), []int{2}
}

var File_tanlive_graph_graph_proto protoreflect.FileDescriptor

var file_tanlive_graph_graph_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2f,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b, 0x03, 0x0a, 0x0b,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x79, 0x61, 0x6d, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x79, 0x61, 0x6d, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a,
	0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6c, 0x61, 0x73,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x69, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xf8, 0x02, 0x0a, 0x0b, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x12, 0x25, 0x0a, 0x0e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x22, 0x0a, 0x0a, 0x08, 0x52, 0x73, 0x70, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x42, 0x3f, 0x5a, 0x3d, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74,
	0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_graph_graph_proto_rawDescOnce sync.Once
	file_tanlive_graph_graph_proto_rawDescData = file_tanlive_graph_graph_proto_rawDesc
)

func file_tanlive_graph_graph_proto_rawDescGZIP() []byte {
	file_tanlive_graph_graph_proto_rawDescOnce.Do(func() {
		file_tanlive_graph_graph_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_graph_graph_proto_rawDescData)
	})
	return file_tanlive_graph_graph_proto_rawDescData
}

var file_tanlive_graph_graph_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_tanlive_graph_graph_proto_goTypes = []interface{}{
	(*ProcessInfo)(nil),           // 0: tanlive.graph.ProcessInfo
	(*VersionInfo)(nil),           // 1: tanlive.graph.VersionInfo
	(*RspEmpty)(nil),              // 2: tanlive.graph.RspEmpty
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_tanlive_graph_graph_proto_depIdxs = []int32{
	3, // 0: tanlive.graph.ProcessInfo.create_date:type_name -> google.protobuf.Timestamp
	3, // 1: tanlive.graph.ProcessInfo.last_update_date:type_name -> google.protobuf.Timestamp
	3, // 2: tanlive.graph.VersionInfo.create_date:type_name -> google.protobuf.Timestamp
	3, // 3: tanlive.graph.VersionInfo.last_update_date:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_tanlive_graph_graph_proto_init() }
func file_tanlive_graph_graph_proto_init() {
	if File_tanlive_graph_graph_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_graph_graph_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_graph_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_graph_graph_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_graph_graph_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_graph_graph_proto_goTypes,
		DependencyIndexes: file_tanlive_graph_graph_proto_depIdxs,
		MessageInfos:      file_tanlive_graph_graph_proto_msgTypes,
	}.Build()
	File_tanlive_graph_graph_proto = out.File
	file_tanlive_graph_graph_proto_rawDesc = nil
	file_tanlive_graph_graph_proto_goTypes = nil
	file_tanlive_graph_graph_proto_depIdxs = nil
}
