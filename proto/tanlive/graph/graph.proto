syntax = "proto3";

package tanlive.graph;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/graph";
import "google/protobuf/timestamp.proto";

message ProcessInfo {
  // 流程名称
  string process_name = 1;
  // 流程类别 1图谱 2资源 3团队 4产品
  uint32 category = 2;
  // 备注
  string remark = 3;
  // 创建人
  uint64 create_by = 4;
  // yaml配置
  string yaml_config = 5;
  // 创建时间
  google.protobuf.Timestamp create_date = 6;
  // 修改时间
  google.protobuf.Timestamp last_update_date = 7;
  uint64 id = 8;
  uint64 last_update_by = 9;
  uint32 is_online_version = 10;
  uint32 lang = 11;
}

message VersionInfo {
  // 流程名称
  string process_name = 1;
  // 流程类别
  uint32 category = 2;
  // 备注
  string remark = 3;
  // 版本号
  string version_number = 4;
  // 是否线上版本
  bool is_online_version = 5;
  // 创建人
  string creator = 6;
  // 创建时间
  google.protobuf.Timestamp create_date = 7;
  // 修改时间
  google.protobuf.Timestamp last_update_date = 8;
  uint64 id = 9;
  uint32 lang = 10;
}

message RspEmpty {
}