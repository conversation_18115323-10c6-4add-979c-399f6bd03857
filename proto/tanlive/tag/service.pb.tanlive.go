// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package tag

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TaggableType": "required",
	}, &ReqDeleteSystemTag{})
}

func (x *ReqDeleteSystemTag) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":       "required",
		"DataInfo":     "required",
		"TaggableType": "required",
		"Action":       "required",
	}, &ReqCreateTagBinding{})
}

func (x *ReqCreateTagBinding) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DataInfo":  "required",
		"LabelInfo": "required",
	}, &ReqAutoBindingAITag{})
}

func (x *ReqAutoBindingAITag) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Old":      "required",
		"New":      "required",
		"DataType": "required",
	}, &ReqBindingTagChangeData{})
}

func (x *ReqBindingTagChangeData) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TaggableType": "required",
		"TagIds":       "required",
		"DataType":     "required",
	}, &ReqGetDataByTag{})
}

func (x *ReqGetDataByTag) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqEditSystemTag) MaskInLog() any {
	if x == nil {
		return (*ReqEditSystemTag)(nil)
	}

	y := proto.Clone(x).(*ReqEditSystemTag)
	if v, ok := any(y.ZhTag).(interface{ MaskInLog() any }); ok {
		y.ZhTag = v.MaskInLog().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInLog() any }); ok {
		y.EnTag = v.MaskInLog().(*Tag)
	}

	return y
}

func (x *ReqEditSystemTag) MaskInRpc() any {
	if x == nil {
		return (*ReqEditSystemTag)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInRpc() any }); ok {
		y.ZhTag = v.MaskInRpc().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInRpc() any }); ok {
		y.EnTag = v.MaskInRpc().(*Tag)
	}

	return y
}

func (x *ReqEditSystemTag) MaskInBff() any {
	if x == nil {
		return (*ReqEditSystemTag)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInBff() any }); ok {
		y.ZhTag = v.MaskInBff().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInBff() any }); ok {
		y.EnTag = v.MaskInBff().(*Tag)
	}

	return y
}

func (x *ReqMergeSystemTags) MaskInLog() any {
	if x == nil {
		return (*ReqMergeSystemTags)(nil)
	}

	y := proto.Clone(x).(*ReqMergeSystemTags)
	if v, ok := any(y.ZhTag).(interface{ MaskInLog() any }); ok {
		y.ZhTag = v.MaskInLog().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInLog() any }); ok {
		y.EnTag = v.MaskInLog().(*Tag)
	}

	return y
}

func (x *ReqMergeSystemTags) MaskInRpc() any {
	if x == nil {
		return (*ReqMergeSystemTags)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInRpc() any }); ok {
		y.ZhTag = v.MaskInRpc().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInRpc() any }); ok {
		y.EnTag = v.MaskInRpc().(*Tag)
	}

	return y
}

func (x *ReqMergeSystemTags) MaskInBff() any {
	if x == nil {
		return (*ReqMergeSystemTags)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInBff() any }); ok {
		y.ZhTag = v.MaskInBff().(*Tag)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInBff() any }); ok {
		y.EnTag = v.MaskInBff().(*Tag)
	}

	return y
}

func (x *ReqDescribeSystemTags) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeSystemTags)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeSystemTags)
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeSystemTags_Filter)
	}

	return y
}

func (x *ReqDescribeSystemTags) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeSystemTags)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeSystemTags_Filter)
	}

	return y
}

func (x *ReqDescribeSystemTags) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeSystemTags)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeSystemTags_Filter)
	}

	return y
}

func (x *RspDescribeSystemTags) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSystemTags)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSystemTags)
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagSet[k] = vv.MaskInLog().(*RspDescribeSystemTags_TagInfo)
		}
	}

	return y
}

func (x *RspDescribeSystemTags) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSystemTags)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagSet[k] = vv.MaskInRpc().(*RspDescribeSystemTags_TagInfo)
		}
	}

	return y
}

func (x *RspDescribeSystemTags) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSystemTags)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagSet[k] = vv.MaskInBff().(*RspDescribeSystemTags_TagInfo)
		}
	}

	return y
}

func (x *RspDescribeSystemTags_TagInfo) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSystemTags_TagInfo)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSystemTags_TagInfo)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspDescribeSystemTags_TagInfo) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSystemTags_TagInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspDescribeSystemTags_TagInfo) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSystemTags_TagInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ReqDescribeSystemTagsByIndex) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeSystemTagsByIndex)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeSystemTagsByIndex)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeSystemTagsByIndex_Filter)
	}
	if v, ok := any(y.OrderBy).(interface{ MaskInLog() any }); ok {
		y.OrderBy = v.MaskInLog().(*base.OrderBy)
	}

	return y
}

func (x *ReqDescribeSystemTagsByIndex) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeSystemTagsByIndex)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeSystemTagsByIndex_Filter)
	}
	if v, ok := any(y.OrderBy).(interface{ MaskInRpc() any }); ok {
		y.OrderBy = v.MaskInRpc().(*base.OrderBy)
	}

	return y
}

func (x *ReqDescribeSystemTagsByIndex) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeSystemTagsByIndex)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeSystemTagsByIndex_Filter)
	}
	if v, ok := any(y.OrderBy).(interface{ MaskInBff() any }); ok {
		y.OrderBy = v.MaskInBff().(*base.OrderBy)
	}

	return y
}

func (x *RspDescribeSystemTagsByIndex) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSystemTagsByIndex)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSystemTagsByIndex)
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagSet[k] = vv.MaskInLog().(*TagIndex)
		}
	}

	return y
}

func (x *RspDescribeSystemTagsByIndex) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSystemTagsByIndex)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagSet[k] = vv.MaskInRpc().(*TagIndex)
		}
	}

	return y
}

func (x *RspDescribeSystemTagsByIndex) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSystemTagsByIndex)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagSet[k] = vv.MaskInBff().(*TagIndex)
		}
	}

	return y
}

func (x *ReqDescribeTeamTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeTeamTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeTeamTagBindingInfos)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeTeamTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeTeamTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeTeamTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeTeamTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeTeamTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeTeamTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeTeamTagBindingInfos_Filter)
	}

	return y
}

func (x *RspDescribeTeamTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeTeamTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeTeamTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeTeamTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeTeamTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeTeamTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeTeamTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeTeamTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeTeamTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeTeamTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *ReqDescribeResourceTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeResourceTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeResourceTagBindingInfos)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeResourceTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeResourceTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeResourceTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeResourceTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeResourceTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeResourceTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeResourceTagBindingInfos_Filter)
	}

	return y
}

func (x *RspDescribeResourceTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeResourceTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeResourceTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeResourceTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeResourceTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeResourceTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeResourceTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeResourceTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeResourceTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeResourceTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *ReqDescribeAtlasTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeAtlasTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeAtlasTagBindingInfos)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeAtlasTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeAtlasTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeAtlasTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeAtlasTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeAtlasTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeAtlasTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeAtlasTagBindingInfos_Filter)
	}

	return y
}

func (x *RspDescribeAtlasTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeAtlasTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeAtlasTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeAtlasTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeAtlasTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeAtlasTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeAtlasTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeAtlasTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeAtlasTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeAtlasTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *ReqDescribeProductTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeProductTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeProductTagBindingInfos)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqDescribeProductTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeProductTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeProductTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqDescribeProductTagBindingInfos_Filter)
	}

	return y
}

func (x *ReqDescribeProductTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeProductTagBindingInfos)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqDescribeProductTagBindingInfos_Filter)
	}

	return y
}

func (x *RspDescribeProductTagBindingInfos) MaskInLog() any {
	if x == nil {
		return (*RspDescribeProductTagBindingInfos)(nil)
	}

	y := proto.Clone(x).(*RspDescribeProductTagBindingInfos)
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.BindingObjects[k] = vv.MaskInLog().(*RspDescribeProductTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeProductTagBindingInfos) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeProductTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.BindingObjects[k] = vv.MaskInRpc().(*RspDescribeProductTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *RspDescribeProductTagBindingInfos) MaskInBff() any {
	if x == nil {
		return (*RspDescribeProductTagBindingInfos)(nil)
	}

	y := x
	for k, v := range y.BindingObjects {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.BindingObjects[k] = vv.MaskInBff().(*RspDescribeProductTagBindingInfos_BindingObject)
		}
	}

	return y
}

func (x *ReqBatchImportSystemTag) MaskInLog() any {
	if x == nil {
		return (*ReqBatchImportSystemTag)(nil)
	}

	y := proto.Clone(x).(*ReqBatchImportSystemTag)
	for k, v := range y.TagIndex {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagIndex[k] = vv.MaskInLog().(*ReqBatchImportSystemTag_TagIndex)
		}
	}

	return y
}

func (x *ReqBatchImportSystemTag) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchImportSystemTag)(nil)
	}

	y := x
	for k, v := range y.TagIndex {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagIndex[k] = vv.MaskInRpc().(*ReqBatchImportSystemTag_TagIndex)
		}
	}

	return y
}

func (x *ReqBatchImportSystemTag) MaskInBff() any {
	if x == nil {
		return (*ReqBatchImportSystemTag)(nil)
	}

	y := x
	for k, v := range y.TagIndex {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagIndex[k] = vv.MaskInBff().(*ReqBatchImportSystemTag_TagIndex)
		}
	}

	return y
}

func (x *ReqGetSystemTags) MaskInLog() any {
	if x == nil {
		return (*ReqGetSystemTags)(nil)
	}

	y := proto.Clone(x).(*ReqGetSystemTags)
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetSystemTags) MaskInRpc() any {
	if x == nil {
		return (*ReqGetSystemTags)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetSystemTags) MaskInBff() any {
	if x == nil {
		return (*ReqGetSystemTags)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}

	return y
}

func (x *RspGetSystemTags) MaskInLog() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := proto.Clone(x).(*RspGetSystemTags)
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagSet[k] = vv.MaskInLog().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *RspGetSystemTags) MaskInRpc() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagSet[k] = vv.MaskInRpc().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *RspGetSystemTags) MaskInBff() any {
	if x == nil {
		return (*RspGetSystemTags)(nil)
	}

	y := x
	for k, v := range y.TagSet {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagSet[k] = vv.MaskInBff().(*RspGetSystemTags_Tag)
		}
	}

	return y
}

func (x *ReqCreateUserTag) MaskInLog() any {
	if x == nil {
		return (*ReqCreateUserTag)(nil)
	}

	y := proto.Clone(x).(*ReqCreateUserTag)
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tags[k] = vv.MaskInLog().(*CreateTag)
		}
	}

	return y
}

func (x *ReqCreateUserTag) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateUserTag)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tags[k] = vv.MaskInRpc().(*CreateTag)
		}
	}

	return y
}

func (x *ReqCreateUserTag) MaskInBff() any {
	if x == nil {
		return (*ReqCreateUserTag)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tags[k] = vv.MaskInBff().(*CreateTag)
		}
	}

	return y
}

func (x *ReqCreateTagBinding) MaskInLog() any {
	if x == nil {
		return (*ReqCreateTagBinding)(nil)
	}

	y := proto.Clone(x).(*ReqCreateTagBinding)
	if v, ok := any(y.DataInfo).(interface{ MaskInLog() any }); ok {
		y.DataInfo = v.MaskInLog().(*DataInfo)
	}

	return y
}

func (x *ReqCreateTagBinding) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateTagBinding)(nil)
	}

	y := x
	if v, ok := any(y.DataInfo).(interface{ MaskInRpc() any }); ok {
		y.DataInfo = v.MaskInRpc().(*DataInfo)
	}

	return y
}

func (x *ReqCreateTagBinding) MaskInBff() any {
	if x == nil {
		return (*ReqCreateTagBinding)(nil)
	}

	y := x
	if v, ok := any(y.DataInfo).(interface{ MaskInBff() any }); ok {
		y.DataInfo = v.MaskInBff().(*DataInfo)
	}

	return y
}

func (x *ReqAutoBindingAITag) MaskInLog() any {
	if x == nil {
		return (*ReqAutoBindingAITag)(nil)
	}

	y := proto.Clone(x).(*ReqAutoBindingAITag)
	for k, v := range y.DataInfo {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.DataInfo[k] = vv.MaskInLog().(*DataInfo)
		}
	}
	if v, ok := any(y.LabelInfo).(interface{ MaskInLog() any }); ok {
		y.LabelInfo = v.MaskInLog().(*LabelInfo)
	}

	return y
}

func (x *ReqAutoBindingAITag) MaskInRpc() any {
	if x == nil {
		return (*ReqAutoBindingAITag)(nil)
	}

	y := x
	for k, v := range y.DataInfo {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.DataInfo[k] = vv.MaskInRpc().(*DataInfo)
		}
	}
	if v, ok := any(y.LabelInfo).(interface{ MaskInRpc() any }); ok {
		y.LabelInfo = v.MaskInRpc().(*LabelInfo)
	}

	return y
}

func (x *ReqAutoBindingAITag) MaskInBff() any {
	if x == nil {
		return (*ReqAutoBindingAITag)(nil)
	}

	y := x
	for k, v := range y.DataInfo {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.DataInfo[k] = vv.MaskInBff().(*DataInfo)
		}
	}
	if v, ok := any(y.LabelInfo).(interface{ MaskInBff() any }); ok {
		y.LabelInfo = v.MaskInBff().(*LabelInfo)
	}

	return y
}

func (x *RspGetTagBinding) MaskInLog() any {
	if x == nil {
		return (*RspGetTagBinding)(nil)
	}

	y := proto.Clone(x).(*RspGetTagBinding)
	for k, v := range y.TagBinding {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagBinding[k] = vv.MaskInLog().(*TagBinding)
		}
	}

	return y
}

func (x *RspGetTagBinding) MaskInRpc() any {
	if x == nil {
		return (*RspGetTagBinding)(nil)
	}

	y := x
	for k, v := range y.TagBinding {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagBinding[k] = vv.MaskInRpc().(*TagBinding)
		}
	}

	return y
}

func (x *RspGetTagBinding) MaskInBff() any {
	if x == nil {
		return (*RspGetTagBinding)(nil)
	}

	y := x
	for k, v := range y.TagBinding {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagBinding[k] = vv.MaskInBff().(*TagBinding)
		}
	}

	return y
}

func (x *RspGetTagInfo) MaskInLog() any {
	if x == nil {
		return (*RspGetTagInfo)(nil)
	}

	y := proto.Clone(x).(*RspGetTagInfo)
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tags[k] = vv.MaskInLog().(*ComTag)
		}
	}

	return y
}

func (x *RspGetTagInfo) MaskInRpc() any {
	if x == nil {
		return (*RspGetTagInfo)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tags[k] = vv.MaskInRpc().(*ComTag)
		}
	}

	return y
}

func (x *RspGetTagInfo) MaskInBff() any {
	if x == nil {
		return (*RspGetTagInfo)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tags[k] = vv.MaskInBff().(*ComTag)
		}
	}

	return y
}

func (x *RspGetTagInfoByLanguage) MaskInLog() any {
	if x == nil {
		return (*RspGetTagInfoByLanguage)(nil)
	}

	y := proto.Clone(x).(*RspGetTagInfoByLanguage)
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tags[k] = vv.MaskInLog().(*ComTag)
		}
	}

	return y
}

func (x *RspGetTagInfoByLanguage) MaskInRpc() any {
	if x == nil {
		return (*RspGetTagInfoByLanguage)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tags[k] = vv.MaskInRpc().(*ComTag)
		}
	}

	return y
}

func (x *RspGetTagInfoByLanguage) MaskInBff() any {
	if x == nil {
		return (*RspGetTagInfoByLanguage)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tags[k] = vv.MaskInBff().(*ComTag)
		}
	}

	return y
}

func (x *RspGetAllTagInfoByData) MaskInLog() any {
	if x == nil {
		return (*RspGetAllTagInfoByData)(nil)
	}

	y := proto.Clone(x).(*RspGetAllTagInfoByData)
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tags[k] = vv.MaskInLog().(*ComTag)
		}
	}

	return y
}

func (x *RspGetAllTagInfoByData) MaskInRpc() any {
	if x == nil {
		return (*RspGetAllTagInfoByData)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tags[k] = vv.MaskInRpc().(*ComTag)
		}
	}

	return y
}

func (x *RspGetAllTagInfoByData) MaskInBff() any {
	if x == nil {
		return (*RspGetAllTagInfoByData)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tags[k] = vv.MaskInBff().(*ComTag)
		}
	}

	return y
}

func (x *RspGetDataWithTagByTag) MaskInLog() any {
	if x == nil {
		return (*RspGetDataWithTagByTag)(nil)
	}

	y := proto.Clone(x).(*RspGetDataWithTagByTag)
	for k, v := range y.DataTags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.DataTags[k] = vv.MaskInLog().(*RspGetDataWithTagByTag_DataTag)
		}
	}

	return y
}

func (x *RspGetDataWithTagByTag) MaskInRpc() any {
	if x == nil {
		return (*RspGetDataWithTagByTag)(nil)
	}

	y := x
	for k, v := range y.DataTags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.DataTags[k] = vv.MaskInRpc().(*RspGetDataWithTagByTag_DataTag)
		}
	}

	return y
}

func (x *RspGetDataWithTagByTag) MaskInBff() any {
	if x == nil {
		return (*RspGetDataWithTagByTag)(nil)
	}

	y := x
	for k, v := range y.DataTags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.DataTags[k] = vv.MaskInBff().(*RspGetDataWithTagByTag_DataTag)
		}
	}

	return y
}

func (x *RspGetDataWithTagByTag_DataTag) MaskInLog() any {
	if x == nil {
		return (*RspGetDataWithTagByTag_DataTag)(nil)
	}

	y := proto.Clone(x).(*RspGetDataWithTagByTag_DataTag)
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tags[k] = vv.MaskInLog().(*ComTag)
		}
	}

	return y
}

func (x *RspGetDataWithTagByTag_DataTag) MaskInRpc() any {
	if x == nil {
		return (*RspGetDataWithTagByTag_DataTag)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tags[k] = vv.MaskInRpc().(*ComTag)
		}
	}

	return y
}

func (x *RspGetDataWithTagByTag_DataTag) MaskInBff() any {
	if x == nil {
		return (*RspGetDataWithTagByTag_DataTag)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tags[k] = vv.MaskInBff().(*ComTag)
		}
	}

	return y
}

func (x *ReqGetDataByTag) MaskInLog() any {
	if x == nil {
		return (*ReqGetDataByTag)(nil)
	}

	y := proto.Clone(x).(*ReqGetDataByTag)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqGetDataByTag) MaskInRpc() any {
	if x == nil {
		return (*ReqGetDataByTag)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqGetDataByTag) MaskInBff() any {
	if x == nil {
		return (*ReqGetDataByTag)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *ReqEditSystemTag) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ZhTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EnTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqMergeSystemTags) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ZhTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EnTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqDescribeSystemTags) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeSystemTags) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagSet {
		if sanitizer, ok := any(x.TagSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeSystemTags_TagInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqDescribeSystemTagsByIndex) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OrderBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeSystemTagsByIndex) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagSet {
		if sanitizer, ok := any(x.TagSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeTeamTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeTeamTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeResourceTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeResourceTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeAtlasTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeAtlasTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeProductTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeProductTagBindingInfos) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.BindingObjects {
		if sanitizer, ok := any(x.BindingObjects[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqBatchImportSystemTag) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagIndex {
		if sanitizer, ok := any(x.TagIndex[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetSystemTags) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetSystemTags) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagSet {
		if sanitizer, ok := any(x.TagSet[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateUserTag) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tags {
		if sanitizer, ok := any(x.Tags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateTagBinding) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.DataInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqAutoBindingAITag) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.DataInfo {
		if sanitizer, ok := any(x.DataInfo[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.LabelInfo).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetTagBinding) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagBinding {
		if sanitizer, ok := any(x.TagBinding[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetTagInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tags {
		if sanitizer, ok := any(x.Tags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetTagInfoByLanguage) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tags {
		if sanitizer, ok := any(x.Tags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAllTagInfoByData) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tags {
		if sanitizer, ok := any(x.Tags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetDataWithTagByTag) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.DataTags {
		if sanitizer, ok := any(x.DataTags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetDataWithTagByTag_DataTag) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tags {
		if sanitizer, ok := any(x.Tags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetDataByTag) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
