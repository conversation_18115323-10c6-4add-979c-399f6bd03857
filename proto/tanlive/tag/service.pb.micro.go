// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/tag/service.proto

package tag

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for TagService service

func NewTagServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for TagService service

type TagService interface {
	// 更新系统标签
	UpdateSystemTag(ctx context.Context, in *ReqUpdateSystemTag, opts ...client.CallOption) (*emptypb.Empty, error)
	// 编辑系统标签
	EditSystemTag(ctx context.Context, in *ReqEditSystemTag, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除系统标签
	DeleteSystemTag(ctx context.Context, in *ReqDeleteSystemTag, opts ...client.CallOption) (*emptypb.Empty, error)
	// 合并系统标签
	MergeSystemTags(ctx context.Context, in *ReqMergeSystemTags, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取系统标签列表
	DescribeSystemTags(ctx context.Context, in *ReqDescribeSystemTags, opts ...client.CallOption) (*RspDescribeSystemTags, error)
	// 获取系统索引标签列表
	DescribeSystemTagsByIndex(ctx context.Context, in *ReqDescribeSystemTagsByIndex, opts ...client.CallOption) (*RspDescribeSystemTagsByIndex, error)
	// 查询团队标签关联详情
	DescribeTeamTagBindingInfos(ctx context.Context, in *ReqDescribeTeamTagBindingInfos, opts ...client.CallOption) (*RspDescribeTeamTagBindingInfos, error)
	// 查询资源标签关联详情
	DescribeResourceTagBindingInfos(ctx context.Context, in *ReqDescribeResourceTagBindingInfos, opts ...client.CallOption) (*RspDescribeResourceTagBindingInfos, error)
	// 查询图谱标签关联详情
	DescribeAtlasTagBindingInfos(ctx context.Context, in *ReqDescribeAtlasTagBindingInfos, opts ...client.CallOption) (*RspDescribeAtlasTagBindingInfos, error)
	// 查询产品标签关联详情
	DescribeProductTagBindingInfos(ctx context.Context, in *ReqDescribeProductTagBindingInfos, opts ...client.CallOption) (*RspDescribeProductTagBindingInfos, error)
	// 批量导入标签
	BatchImportSystemTag(ctx context.Context, in *ReqBatchImportSystemTag, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取全量系统标签
	GetSystemTags(ctx context.Context, in *ReqGetSystemTags, opts ...client.CallOption) (*RspGetSystemTags, error)
	// 创建用户自创标签
	CreateUserTag(ctx context.Context, in *ReqCreateUserTag, opts ...client.CallOption) (*RspCreateUserTag, error)
	// 创建标签绑定，适用于不需要审核的场景
	CreateTagBinding(ctx context.Context, in *ReqCreateTagBinding, opts ...client.CallOption) (*emptypb.Empty, error)
	// 自动绑定标签
	AutoBindingAITag(ctx context.Context, in *ReqAutoBindingAITag, opts ...client.CallOption) (*emptypb.Empty, error)
	// 切换标签的绑定者，目前适用:从小程序虚拟用户切换为正式用户
	BindingTagChangeData(ctx context.Context, in *ReqBindingTagChangeData, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取标签绑定
	GetTagBinding(ctx context.Context, in *ReqGetTagBinding, opts ...client.CallOption) (*RspGetTagBinding, error)
	// 清除标签绑定
	CleanTagBinding(ctx context.Context, in *ReqCleanTagBinding, opts ...client.CallOption) (*emptypb.Empty, error)
	// 数据标签审核通过
	ApprovedDataTag(ctx context.Context, in *ReqApprovedDataTag, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取被标签绑定的数据（返回各个标签绑定ugc的并集）
	GetDataBoundByTag(ctx context.Context, in *ReqGetDataBoundByTag, opts ...client.CallOption) (*RspGetDataBoundByTag, error)
	// 获取标签信息
	GetTagInfo(ctx context.Context, in *ReqGetTagInfo, opts ...client.CallOption) (*RspGetTagInfo, error)
	// 获取标签多语言信息
	GetTagInfoByLanguage(ctx context.Context, in *ReqGetTagInfoByLanguage, opts ...client.CallOption) (*RspGetTagInfoByLanguage, error)
	// 同步ugc内容绑定的标签信息
	SyncDataTag(ctx context.Context, in *ReqSyncDataTag, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取ugc数据绑定的全语言标签信息
	GetAllTagInfoByData(ctx context.Context, in *ReqGetAllTagInfoByData, opts ...client.CallOption) (*RspGetAllTagInfoByData, error)
	// 获取通过标签获取数据集标签信息，运用于首页标签列表查询关联已上线的ugc的标签
	GetDataWithTagByTag(ctx context.Context, in *ReqGetDataWithTagByTag, opts ...client.CallOption) (*RspGetDataWithTagByTag, error)
	// 获取被用户标签绑定的数据信息
	GetDataByTag(ctx context.Context, in *ReqGetDataByTag, opts ...client.CallOption) (*RspGetDataByTag, error)
}

type tagService struct {
	c    client.Client
	name string
}

func NewTagService(name string, c client.Client) TagService {
	return &tagService{
		c:    c,
		name: name,
	}
}

func (c *tagService) UpdateSystemTag(ctx context.Context, in *ReqUpdateSystemTag, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.UpdateSystemTag", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) EditSystemTag(ctx context.Context, in *ReqEditSystemTag, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.EditSystemTag", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) DeleteSystemTag(ctx context.Context, in *ReqDeleteSystemTag, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.DeleteSystemTag", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) MergeSystemTags(ctx context.Context, in *ReqMergeSystemTags, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.MergeSystemTags", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) DescribeSystemTags(ctx context.Context, in *ReqDescribeSystemTags, opts ...client.CallOption) (*RspDescribeSystemTags, error) {
	req := c.c.NewRequest(c.name, "TagService.DescribeSystemTags", in)
	out := new(RspDescribeSystemTags)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) DescribeSystemTagsByIndex(ctx context.Context, in *ReqDescribeSystemTagsByIndex, opts ...client.CallOption) (*RspDescribeSystemTagsByIndex, error) {
	req := c.c.NewRequest(c.name, "TagService.DescribeSystemTagsByIndex", in)
	out := new(RspDescribeSystemTagsByIndex)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) DescribeTeamTagBindingInfos(ctx context.Context, in *ReqDescribeTeamTagBindingInfos, opts ...client.CallOption) (*RspDescribeTeamTagBindingInfos, error) {
	req := c.c.NewRequest(c.name, "TagService.DescribeTeamTagBindingInfos", in)
	out := new(RspDescribeTeamTagBindingInfos)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) DescribeResourceTagBindingInfos(ctx context.Context, in *ReqDescribeResourceTagBindingInfos, opts ...client.CallOption) (*RspDescribeResourceTagBindingInfos, error) {
	req := c.c.NewRequest(c.name, "TagService.DescribeResourceTagBindingInfos", in)
	out := new(RspDescribeResourceTagBindingInfos)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) DescribeAtlasTagBindingInfos(ctx context.Context, in *ReqDescribeAtlasTagBindingInfos, opts ...client.CallOption) (*RspDescribeAtlasTagBindingInfos, error) {
	req := c.c.NewRequest(c.name, "TagService.DescribeAtlasTagBindingInfos", in)
	out := new(RspDescribeAtlasTagBindingInfos)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) DescribeProductTagBindingInfos(ctx context.Context, in *ReqDescribeProductTagBindingInfos, opts ...client.CallOption) (*RspDescribeProductTagBindingInfos, error) {
	req := c.c.NewRequest(c.name, "TagService.DescribeProductTagBindingInfos", in)
	out := new(RspDescribeProductTagBindingInfos)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) BatchImportSystemTag(ctx context.Context, in *ReqBatchImportSystemTag, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.BatchImportSystemTag", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) GetSystemTags(ctx context.Context, in *ReqGetSystemTags, opts ...client.CallOption) (*RspGetSystemTags, error) {
	req := c.c.NewRequest(c.name, "TagService.GetSystemTags", in)
	out := new(RspGetSystemTags)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) CreateUserTag(ctx context.Context, in *ReqCreateUserTag, opts ...client.CallOption) (*RspCreateUserTag, error) {
	req := c.c.NewRequest(c.name, "TagService.CreateUserTag", in)
	out := new(RspCreateUserTag)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) CreateTagBinding(ctx context.Context, in *ReqCreateTagBinding, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.CreateTagBinding", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) AutoBindingAITag(ctx context.Context, in *ReqAutoBindingAITag, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.AutoBindingAITag", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) BindingTagChangeData(ctx context.Context, in *ReqBindingTagChangeData, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.BindingTagChangeData", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) GetTagBinding(ctx context.Context, in *ReqGetTagBinding, opts ...client.CallOption) (*RspGetTagBinding, error) {
	req := c.c.NewRequest(c.name, "TagService.GetTagBinding", in)
	out := new(RspGetTagBinding)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) CleanTagBinding(ctx context.Context, in *ReqCleanTagBinding, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.CleanTagBinding", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) ApprovedDataTag(ctx context.Context, in *ReqApprovedDataTag, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.ApprovedDataTag", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) GetDataBoundByTag(ctx context.Context, in *ReqGetDataBoundByTag, opts ...client.CallOption) (*RspGetDataBoundByTag, error) {
	req := c.c.NewRequest(c.name, "TagService.GetDataBoundByTag", in)
	out := new(RspGetDataBoundByTag)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) GetTagInfo(ctx context.Context, in *ReqGetTagInfo, opts ...client.CallOption) (*RspGetTagInfo, error) {
	req := c.c.NewRequest(c.name, "TagService.GetTagInfo", in)
	out := new(RspGetTagInfo)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) GetTagInfoByLanguage(ctx context.Context, in *ReqGetTagInfoByLanguage, opts ...client.CallOption) (*RspGetTagInfoByLanguage, error) {
	req := c.c.NewRequest(c.name, "TagService.GetTagInfoByLanguage", in)
	out := new(RspGetTagInfoByLanguage)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) SyncDataTag(ctx context.Context, in *ReqSyncDataTag, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "TagService.SyncDataTag", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) GetAllTagInfoByData(ctx context.Context, in *ReqGetAllTagInfoByData, opts ...client.CallOption) (*RspGetAllTagInfoByData, error) {
	req := c.c.NewRequest(c.name, "TagService.GetAllTagInfoByData", in)
	out := new(RspGetAllTagInfoByData)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) GetDataWithTagByTag(ctx context.Context, in *ReqGetDataWithTagByTag, opts ...client.CallOption) (*RspGetDataWithTagByTag, error) {
	req := c.c.NewRequest(c.name, "TagService.GetDataWithTagByTag", in)
	out := new(RspGetDataWithTagByTag)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagService) GetDataByTag(ctx context.Context, in *ReqGetDataByTag, opts ...client.CallOption) (*RspGetDataByTag, error) {
	req := c.c.NewRequest(c.name, "TagService.GetDataByTag", in)
	out := new(RspGetDataByTag)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for TagService service

type TagServiceHandler interface {
	// 更新系统标签
	UpdateSystemTag(context.Context, *ReqUpdateSystemTag, *emptypb.Empty) error
	// 编辑系统标签
	EditSystemTag(context.Context, *ReqEditSystemTag, *emptypb.Empty) error
	// 删除系统标签
	DeleteSystemTag(context.Context, *ReqDeleteSystemTag, *emptypb.Empty) error
	// 合并系统标签
	MergeSystemTags(context.Context, *ReqMergeSystemTags, *emptypb.Empty) error
	// 获取系统标签列表
	DescribeSystemTags(context.Context, *ReqDescribeSystemTags, *RspDescribeSystemTags) error
	// 获取系统索引标签列表
	DescribeSystemTagsByIndex(context.Context, *ReqDescribeSystemTagsByIndex, *RspDescribeSystemTagsByIndex) error
	// 查询团队标签关联详情
	DescribeTeamTagBindingInfos(context.Context, *ReqDescribeTeamTagBindingInfos, *RspDescribeTeamTagBindingInfos) error
	// 查询资源标签关联详情
	DescribeResourceTagBindingInfos(context.Context, *ReqDescribeResourceTagBindingInfos, *RspDescribeResourceTagBindingInfos) error
	// 查询图谱标签关联详情
	DescribeAtlasTagBindingInfos(context.Context, *ReqDescribeAtlasTagBindingInfos, *RspDescribeAtlasTagBindingInfos) error
	// 查询产品标签关联详情
	DescribeProductTagBindingInfos(context.Context, *ReqDescribeProductTagBindingInfos, *RspDescribeProductTagBindingInfos) error
	// 批量导入标签
	BatchImportSystemTag(context.Context, *ReqBatchImportSystemTag, *emptypb.Empty) error
	// 获取全量系统标签
	GetSystemTags(context.Context, *ReqGetSystemTags, *RspGetSystemTags) error
	// 创建用户自创标签
	CreateUserTag(context.Context, *ReqCreateUserTag, *RspCreateUserTag) error
	// 创建标签绑定，适用于不需要审核的场景
	CreateTagBinding(context.Context, *ReqCreateTagBinding, *emptypb.Empty) error
	// 自动绑定标签
	AutoBindingAITag(context.Context, *ReqAutoBindingAITag, *emptypb.Empty) error
	// 切换标签的绑定者，目前适用:从小程序虚拟用户切换为正式用户
	BindingTagChangeData(context.Context, *ReqBindingTagChangeData, *emptypb.Empty) error
	// 获取标签绑定
	GetTagBinding(context.Context, *ReqGetTagBinding, *RspGetTagBinding) error
	// 清除标签绑定
	CleanTagBinding(context.Context, *ReqCleanTagBinding, *emptypb.Empty) error
	// 数据标签审核通过
	ApprovedDataTag(context.Context, *ReqApprovedDataTag, *emptypb.Empty) error
	// 获取被标签绑定的数据（返回各个标签绑定ugc的并集）
	GetDataBoundByTag(context.Context, *ReqGetDataBoundByTag, *RspGetDataBoundByTag) error
	// 获取标签信息
	GetTagInfo(context.Context, *ReqGetTagInfo, *RspGetTagInfo) error
	// 获取标签多语言信息
	GetTagInfoByLanguage(context.Context, *ReqGetTagInfoByLanguage, *RspGetTagInfoByLanguage) error
	// 同步ugc内容绑定的标签信息
	SyncDataTag(context.Context, *ReqSyncDataTag, *emptypb.Empty) error
	// 获取ugc数据绑定的全语言标签信息
	GetAllTagInfoByData(context.Context, *ReqGetAllTagInfoByData, *RspGetAllTagInfoByData) error
	// 获取通过标签获取数据集标签信息，运用于首页标签列表查询关联已上线的ugc的标签
	GetDataWithTagByTag(context.Context, *ReqGetDataWithTagByTag, *RspGetDataWithTagByTag) error
	// 获取被用户标签绑定的数据信息
	GetDataByTag(context.Context, *ReqGetDataByTag, *RspGetDataByTag) error
}

func RegisterTagServiceHandler(s server.Server, hdlr TagServiceHandler, opts ...server.HandlerOption) error {
	type tagService interface {
		UpdateSystemTag(ctx context.Context, in *ReqUpdateSystemTag, out *emptypb.Empty) error
		EditSystemTag(ctx context.Context, in *ReqEditSystemTag, out *emptypb.Empty) error
		DeleteSystemTag(ctx context.Context, in *ReqDeleteSystemTag, out *emptypb.Empty) error
		MergeSystemTags(ctx context.Context, in *ReqMergeSystemTags, out *emptypb.Empty) error
		DescribeSystemTags(ctx context.Context, in *ReqDescribeSystemTags, out *RspDescribeSystemTags) error
		DescribeSystemTagsByIndex(ctx context.Context, in *ReqDescribeSystemTagsByIndex, out *RspDescribeSystemTagsByIndex) error
		DescribeTeamTagBindingInfos(ctx context.Context, in *ReqDescribeTeamTagBindingInfos, out *RspDescribeTeamTagBindingInfos) error
		DescribeResourceTagBindingInfos(ctx context.Context, in *ReqDescribeResourceTagBindingInfos, out *RspDescribeResourceTagBindingInfos) error
		DescribeAtlasTagBindingInfos(ctx context.Context, in *ReqDescribeAtlasTagBindingInfos, out *RspDescribeAtlasTagBindingInfos) error
		DescribeProductTagBindingInfos(ctx context.Context, in *ReqDescribeProductTagBindingInfos, out *RspDescribeProductTagBindingInfos) error
		BatchImportSystemTag(ctx context.Context, in *ReqBatchImportSystemTag, out *emptypb.Empty) error
		GetSystemTags(ctx context.Context, in *ReqGetSystemTags, out *RspGetSystemTags) error
		CreateUserTag(ctx context.Context, in *ReqCreateUserTag, out *RspCreateUserTag) error
		CreateTagBinding(ctx context.Context, in *ReqCreateTagBinding, out *emptypb.Empty) error
		AutoBindingAITag(ctx context.Context, in *ReqAutoBindingAITag, out *emptypb.Empty) error
		BindingTagChangeData(ctx context.Context, in *ReqBindingTagChangeData, out *emptypb.Empty) error
		GetTagBinding(ctx context.Context, in *ReqGetTagBinding, out *RspGetTagBinding) error
		CleanTagBinding(ctx context.Context, in *ReqCleanTagBinding, out *emptypb.Empty) error
		ApprovedDataTag(ctx context.Context, in *ReqApprovedDataTag, out *emptypb.Empty) error
		GetDataBoundByTag(ctx context.Context, in *ReqGetDataBoundByTag, out *RspGetDataBoundByTag) error
		GetTagInfo(ctx context.Context, in *ReqGetTagInfo, out *RspGetTagInfo) error
		GetTagInfoByLanguage(ctx context.Context, in *ReqGetTagInfoByLanguage, out *RspGetTagInfoByLanguage) error
		SyncDataTag(ctx context.Context, in *ReqSyncDataTag, out *emptypb.Empty) error
		GetAllTagInfoByData(ctx context.Context, in *ReqGetAllTagInfoByData, out *RspGetAllTagInfoByData) error
		GetDataWithTagByTag(ctx context.Context, in *ReqGetDataWithTagByTag, out *RspGetDataWithTagByTag) error
		GetDataByTag(ctx context.Context, in *ReqGetDataByTag, out *RspGetDataByTag) error
	}
	type TagService struct {
		tagService
	}
	h := &tagServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&TagService{h}, opts...))
}

type tagServiceHandler struct {
	TagServiceHandler
}

func (h *tagServiceHandler) UpdateSystemTag(ctx context.Context, in *ReqUpdateSystemTag, out *emptypb.Empty) error {
	return h.TagServiceHandler.UpdateSystemTag(ctx, in, out)
}

func (h *tagServiceHandler) EditSystemTag(ctx context.Context, in *ReqEditSystemTag, out *emptypb.Empty) error {
	return h.TagServiceHandler.EditSystemTag(ctx, in, out)
}

func (h *tagServiceHandler) DeleteSystemTag(ctx context.Context, in *ReqDeleteSystemTag, out *emptypb.Empty) error {
	return h.TagServiceHandler.DeleteSystemTag(ctx, in, out)
}

func (h *tagServiceHandler) MergeSystemTags(ctx context.Context, in *ReqMergeSystemTags, out *emptypb.Empty) error {
	return h.TagServiceHandler.MergeSystemTags(ctx, in, out)
}

func (h *tagServiceHandler) DescribeSystemTags(ctx context.Context, in *ReqDescribeSystemTags, out *RspDescribeSystemTags) error {
	return h.TagServiceHandler.DescribeSystemTags(ctx, in, out)
}

func (h *tagServiceHandler) DescribeSystemTagsByIndex(ctx context.Context, in *ReqDescribeSystemTagsByIndex, out *RspDescribeSystemTagsByIndex) error {
	return h.TagServiceHandler.DescribeSystemTagsByIndex(ctx, in, out)
}

func (h *tagServiceHandler) DescribeTeamTagBindingInfos(ctx context.Context, in *ReqDescribeTeamTagBindingInfos, out *RspDescribeTeamTagBindingInfos) error {
	return h.TagServiceHandler.DescribeTeamTagBindingInfos(ctx, in, out)
}

func (h *tagServiceHandler) DescribeResourceTagBindingInfos(ctx context.Context, in *ReqDescribeResourceTagBindingInfos, out *RspDescribeResourceTagBindingInfos) error {
	return h.TagServiceHandler.DescribeResourceTagBindingInfos(ctx, in, out)
}

func (h *tagServiceHandler) DescribeAtlasTagBindingInfos(ctx context.Context, in *ReqDescribeAtlasTagBindingInfos, out *RspDescribeAtlasTagBindingInfos) error {
	return h.TagServiceHandler.DescribeAtlasTagBindingInfos(ctx, in, out)
}

func (h *tagServiceHandler) DescribeProductTagBindingInfos(ctx context.Context, in *ReqDescribeProductTagBindingInfos, out *RspDescribeProductTagBindingInfos) error {
	return h.TagServiceHandler.DescribeProductTagBindingInfos(ctx, in, out)
}

func (h *tagServiceHandler) BatchImportSystemTag(ctx context.Context, in *ReqBatchImportSystemTag, out *emptypb.Empty) error {
	return h.TagServiceHandler.BatchImportSystemTag(ctx, in, out)
}

func (h *tagServiceHandler) GetSystemTags(ctx context.Context, in *ReqGetSystemTags, out *RspGetSystemTags) error {
	return h.TagServiceHandler.GetSystemTags(ctx, in, out)
}

func (h *tagServiceHandler) CreateUserTag(ctx context.Context, in *ReqCreateUserTag, out *RspCreateUserTag) error {
	return h.TagServiceHandler.CreateUserTag(ctx, in, out)
}

func (h *tagServiceHandler) CreateTagBinding(ctx context.Context, in *ReqCreateTagBinding, out *emptypb.Empty) error {
	return h.TagServiceHandler.CreateTagBinding(ctx, in, out)
}

func (h *tagServiceHandler) AutoBindingAITag(ctx context.Context, in *ReqAutoBindingAITag, out *emptypb.Empty) error {
	return h.TagServiceHandler.AutoBindingAITag(ctx, in, out)
}

func (h *tagServiceHandler) BindingTagChangeData(ctx context.Context, in *ReqBindingTagChangeData, out *emptypb.Empty) error {
	return h.TagServiceHandler.BindingTagChangeData(ctx, in, out)
}

func (h *tagServiceHandler) GetTagBinding(ctx context.Context, in *ReqGetTagBinding, out *RspGetTagBinding) error {
	return h.TagServiceHandler.GetTagBinding(ctx, in, out)
}

func (h *tagServiceHandler) CleanTagBinding(ctx context.Context, in *ReqCleanTagBinding, out *emptypb.Empty) error {
	return h.TagServiceHandler.CleanTagBinding(ctx, in, out)
}

func (h *tagServiceHandler) ApprovedDataTag(ctx context.Context, in *ReqApprovedDataTag, out *emptypb.Empty) error {
	return h.TagServiceHandler.ApprovedDataTag(ctx, in, out)
}

func (h *tagServiceHandler) GetDataBoundByTag(ctx context.Context, in *ReqGetDataBoundByTag, out *RspGetDataBoundByTag) error {
	return h.TagServiceHandler.GetDataBoundByTag(ctx, in, out)
}

func (h *tagServiceHandler) GetTagInfo(ctx context.Context, in *ReqGetTagInfo, out *RspGetTagInfo) error {
	return h.TagServiceHandler.GetTagInfo(ctx, in, out)
}

func (h *tagServiceHandler) GetTagInfoByLanguage(ctx context.Context, in *ReqGetTagInfoByLanguage, out *RspGetTagInfoByLanguage) error {
	return h.TagServiceHandler.GetTagInfoByLanguage(ctx, in, out)
}

func (h *tagServiceHandler) SyncDataTag(ctx context.Context, in *ReqSyncDataTag, out *emptypb.Empty) error {
	return h.TagServiceHandler.SyncDataTag(ctx, in, out)
}

func (h *tagServiceHandler) GetAllTagInfoByData(ctx context.Context, in *ReqGetAllTagInfoByData, out *RspGetAllTagInfoByData) error {
	return h.TagServiceHandler.GetAllTagInfoByData(ctx, in, out)
}

func (h *tagServiceHandler) GetDataWithTagByTag(ctx context.Context, in *ReqGetDataWithTagByTag, out *RspGetDataWithTagByTag) error {
	return h.TagServiceHandler.GetDataWithTagByTag(ctx, in, out)
}

func (h *tagServiceHandler) GetDataByTag(ctx context.Context, in *ReqGetDataByTag, out *RspGetDataByTag) error {
	return h.TagServiceHandler.GetDataByTag(ctx, in, out)
}
