// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/tag/tag.proto

package tag

import (
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TagTmtType 标签翻译类型枚举
type TagTmtType int32

const (
	TagTmtType_TAG_TMT_TYPE_UNSPECIFIED TagTmtType = 0
	// 已编辑
	TagTmtType_TAG_TMT_TYPE_EDITED TagTmtType = 1
	// 已翻译
	TagTmtType_TAG_TMT_TYPE_TRANSLATED TagTmtType = 2
	// 待翻译
	TagTmtType_TAG_TMT_TYPE_UNTRANSLATED TagTmtType = 3
)

// Enum value maps for TagTmtType.
var (
	TagTmtType_name = map[int32]string{
		0: "TAG_TMT_TYPE_UNSPECIFIED",
		1: "TAG_TMT_TYPE_EDITED",
		2: "TAG_TMT_TYPE_TRANSLATED",
		3: "TAG_TMT_TYPE_UNTRANSLATED",
	}
	TagTmtType_value = map[string]int32{
		"TAG_TMT_TYPE_UNSPECIFIED":  0,
		"TAG_TMT_TYPE_EDITED":       1,
		"TAG_TMT_TYPE_TRANSLATED":   2,
		"TAG_TMT_TYPE_UNTRANSLATED": 3,
	}
)

func (x TagTmtType) Enum() *TagTmtType {
	p := new(TagTmtType)
	*p = x
	return p
}

func (x TagTmtType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TagTmtType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_tag_tag_proto_enumTypes[0].Descriptor()
}

func (TagTmtType) Type() protoreflect.EnumType {
	return &file_tanlive_tag_tag_proto_enumTypes[0]
}

func (x TagTmtType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TagTmtType.Descriptor instead.
func (TagTmtType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{0}
}

// TagCreateType 标签创建类型枚举
type TagCreateType int32

const (
	TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED TagCreateType = 0
	// 系统标签
	TagCreateType_TAG_CREATE_TYPE_SYSTEM TagCreateType = 1
	// 自创标签
	TagCreateType_TAG_CREATE_TYPE_USER TagCreateType = 2
)

// Enum value maps for TagCreateType.
var (
	TagCreateType_name = map[int32]string{
		0: "TAG_CREATE_TYPE_UNSPECIFIED",
		1: "TAG_CREATE_TYPE_SYSTEM",
		2: "TAG_CREATE_TYPE_USER",
	}
	TagCreateType_value = map[string]int32{
		"TAG_CREATE_TYPE_UNSPECIFIED": 0,
		"TAG_CREATE_TYPE_SYSTEM":      1,
		"TAG_CREATE_TYPE_USER":        2,
	}
)

func (x TagCreateType) Enum() *TagCreateType {
	p := new(TagCreateType)
	*p = x
	return p
}

func (x TagCreateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TagCreateType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_tag_tag_proto_enumTypes[1].Descriptor()
}

func (TagCreateType) Type() protoreflect.EnumType {
	return &file_tanlive_tag_tag_proto_enumTypes[1]
}

func (x TagCreateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TagCreateType.Descriptor instead.
func (TagCreateType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{1}
}

// TagActionType 标签操作类型
type TagActionType int32

const (
	TagActionType_TAG_ACTION_TYPE_UNSPECIFIED TagActionType = 0
	// 正常
	TagActionType_TAG_ACTION_TYPE_NORMAL TagActionType = 1
	// 禁止删除
	TagActionType_TAG_ACTION_TYPE_NO_DELETION TagActionType = 2
	// 及联删除
	TagActionType_TAG_ACTION_TYPE_RELATION_DELETION TagActionType = 3
)

// Enum value maps for TagActionType.
var (
	TagActionType_name = map[int32]string{
		0: "TAG_ACTION_TYPE_UNSPECIFIED",
		1: "TAG_ACTION_TYPE_NORMAL",
		2: "TAG_ACTION_TYPE_NO_DELETION",
		3: "TAG_ACTION_TYPE_RELATION_DELETION",
	}
	TagActionType_value = map[string]int32{
		"TAG_ACTION_TYPE_UNSPECIFIED":       0,
		"TAG_ACTION_TYPE_NORMAL":            1,
		"TAG_ACTION_TYPE_NO_DELETION":       2,
		"TAG_ACTION_TYPE_RELATION_DELETION": 3,
	}
)

func (x TagActionType) Enum() *TagActionType {
	p := new(TagActionType)
	*p = x
	return p
}

func (x TagActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TagActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_tag_tag_proto_enumTypes[2].Descriptor()
}

func (TagActionType) Type() protoreflect.EnumType {
	return &file_tanlive_tag_tag_proto_enumTypes[2]
}

func (x TagActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TagActionType.Descriptor instead.
func (TagActionType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{2}
}

// TaggableActionType 标签的绑定类型
type TaggableActionType int32

const (
	TaggableActionType_TAGGABLE_ACTION_TYPE_UNSPECIFIED TaggableActionType = 0
	// 用户的普通绑定
	TaggableActionType_TAGGABLE_ACTION_TYPE_NORMAL TaggableActionType = 1
	// 运营人员绑定
	TaggableActionType_TAGGABLE_ACTION_TYPE_MGMT TaggableActionType = 2
	// 自动打标
	TaggableActionType_TAGGABLE_ACTION_TYPE_AUTO TaggableActionType = 3
	// 自动打标的一次性绑定
	TaggableActionType_TAGGABLE_ACTION_TYPE_AUTO_DISPOSABLE TaggableActionType = 4
	// 及联绑定
	TaggableActionType_TAGGABLE_ACTION_TYPE_RELATION TaggableActionType = 5
)

// Enum value maps for TaggableActionType.
var (
	TaggableActionType_name = map[int32]string{
		0: "TAGGABLE_ACTION_TYPE_UNSPECIFIED",
		1: "TAGGABLE_ACTION_TYPE_NORMAL",
		2: "TAGGABLE_ACTION_TYPE_MGMT",
		3: "TAGGABLE_ACTION_TYPE_AUTO",
		4: "TAGGABLE_ACTION_TYPE_AUTO_DISPOSABLE",
		5: "TAGGABLE_ACTION_TYPE_RELATION",
	}
	TaggableActionType_value = map[string]int32{
		"TAGGABLE_ACTION_TYPE_UNSPECIFIED":     0,
		"TAGGABLE_ACTION_TYPE_NORMAL":          1,
		"TAGGABLE_ACTION_TYPE_MGMT":            2,
		"TAGGABLE_ACTION_TYPE_AUTO":            3,
		"TAGGABLE_ACTION_TYPE_AUTO_DISPOSABLE": 4,
		"TAGGABLE_ACTION_TYPE_RELATION":        5,
	}
)

func (x TaggableActionType) Enum() *TaggableActionType {
	p := new(TaggableActionType)
	*p = x
	return p
}

func (x TaggableActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaggableActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_tag_tag_proto_enumTypes[3].Descriptor()
}

func (TaggableActionType) Type() protoreflect.EnumType {
	return &file_tanlive_tag_tag_proto_enumTypes[3]
}

func (x TaggableActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaggableActionType.Descriptor instead.
func (TaggableActionType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{3}
}

type AutoLabelType int32

const (
	AutoLabelType_AUTO_LABEL_TYPE_UNSPECIFIED AutoLabelType = 0
	// 默认行为
	AutoLabelType_AUTO_LABEL_TYPE_DEFAULT AutoLabelType = 1
	// ai助手管理员
	AutoLabelType_AUTO_LABEL_TYPE_AI_ASSISTANT_ADMIN AutoLabelType = 2
	// 加入ai助手团队
	AutoLabelType_AUTO_LABEL_TYPE_ASSISTANT_TEAM AutoLabelType = 3
	// ai对话
	AutoLabelType_AUTO_LABEL_TYPE_AI_CHAT AutoLabelType = 4
	// ai图谱
	AutoLabelType_AUTO_LABEL_TYPE_AI_ATLAS AutoLabelType = 5
)

// Enum value maps for AutoLabelType.
var (
	AutoLabelType_name = map[int32]string{
		0: "AUTO_LABEL_TYPE_UNSPECIFIED",
		1: "AUTO_LABEL_TYPE_DEFAULT",
		2: "AUTO_LABEL_TYPE_AI_ASSISTANT_ADMIN",
		3: "AUTO_LABEL_TYPE_ASSISTANT_TEAM",
		4: "AUTO_LABEL_TYPE_AI_CHAT",
		5: "AUTO_LABEL_TYPE_AI_ATLAS",
	}
	AutoLabelType_value = map[string]int32{
		"AUTO_LABEL_TYPE_UNSPECIFIED":        0,
		"AUTO_LABEL_TYPE_DEFAULT":            1,
		"AUTO_LABEL_TYPE_AI_ASSISTANT_ADMIN": 2,
		"AUTO_LABEL_TYPE_ASSISTANT_TEAM":     3,
		"AUTO_LABEL_TYPE_AI_CHAT":            4,
		"AUTO_LABEL_TYPE_AI_ATLAS":           5,
	}
)

func (x AutoLabelType) Enum() *AutoLabelType {
	p := new(AutoLabelType)
	*p = x
	return p
}

func (x AutoLabelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AutoLabelType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_tag_tag_proto_enumTypes[4].Descriptor()
}

func (AutoLabelType) Type() protoreflect.EnumType {
	return &file_tanlive_tag_tag_proto_enumTypes[4]
}

func (x AutoLabelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AutoLabelType.Descriptor instead.
func (AutoLabelType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{4}
}

// 标签类型
type TaggableType int32

const (
	TaggableType_TAGGABLE_TYPE_UNSPECIFIED TaggableType = 0
	// 1 个人信息-我来自
	TaggableType_TAGGABLE_TYPE_PERSONAL_INFORMATION TaggableType = 1
	// 2 团队类型
	TaggableType_TAGGABLE_TYPE_TEAM_TYPE TaggableType = 2
	// 3 团队属性
	TaggableType_TAGGABLE_TYPE_TEAM_ATTRIBUTES TaggableType = 3
	// 4 团队行业
	TaggableType_TAGGABLE_TYPE_TEAM_INDUSTRY TaggableType = 4
	// 5 资源受众行业
	TaggableType_TAGGABLE_TYPE_RESOURCE_AUDIENCE_INDUSTRY TaggableType = 5
	// 6 产品技术-面向用户（场景）
	TaggableType_TAGGABLE_TYPE_PRODUCT_TECHNOLOGY_USER_ORIENTED TaggableType = 6
	// 7 资源类型
	TaggableType_TAGGABLE_TYPE_RESOURCE_TYPE TaggableType = 7
	// 8 图谱-适用行业
	TaggableType_TAGGABLE_TYPE_SUIT_INDUSTRY TaggableType = 8
	// 9 图谱-适用场景
	TaggableType_TAGGABLE_TYPE_SUIT_SCENE TaggableType = 9
	// 10 图谱类型
	TaggableType_TAGGABLE_ATLAS_TYPE TaggableType = 10
	// 11 产品行业认可 --2.2迭代将 21资源-产品行业认可合并入11
	TaggableType_TAGGABLE_TYPE_PRODUCT_INDUSTRY_RECOGNITION TaggableType = 11
	// 12 团队行业认可 --2.2迭代将 22资源-团队行业认可合并入12
	TaggableType_TAGGABLE_TYPE_TEAM_INDUSTRY_RECOGNITION TaggableType = 12
	// 14 产品适用行业
	TaggableType_TAGGABLE_TYPE_PRODUCT_APPLICABLE_INDUSTRY TaggableType = 14
	// 23 资源系列
	TaggableType_TAGGABLE_TYPE_RESOURCE_SERIES TaggableType = 23
	// 101 团队发展阶段
	TaggableType_TAGGABLE_TYPE_TEAM_DEVELOPMENT_STAGE TaggableType = 101
	// 102 团队产品类型
	TaggableType_TAGGABLE_TYPE_TEAM_PRODUCT_TYPE TaggableType = 102
	// 103 受众团队融资阶段
	TaggableType_TAGGABLE_TYPE_AUDIENCE_TEAM_FINANCING_STAGE TaggableType = 103
	// 200 后端用于业务及联的特殊类型，前端忽略
	TaggableType_TAGGABLE_TYPE_AI_ASSISTANT TaggableType = 200
	// 201 个人用户-用户标签
	TaggableType_TAGGABLE_TYPE_PERSONAL_USER_LABEL TaggableType = 201
	// 202 团队用户-用户标签
	TaggableType_TAGGABLE_TYPE_TEAM_USER_LABEL TaggableType = 202
	// 203 AI助手-给用户打标
	TaggableType_TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL TaggableType = 203
)

// Enum value maps for TaggableType.
var (
	TaggableType_name = map[int32]string{
		0:   "TAGGABLE_TYPE_UNSPECIFIED",
		1:   "TAGGABLE_TYPE_PERSONAL_INFORMATION",
		2:   "TAGGABLE_TYPE_TEAM_TYPE",
		3:   "TAGGABLE_TYPE_TEAM_ATTRIBUTES",
		4:   "TAGGABLE_TYPE_TEAM_INDUSTRY",
		5:   "TAGGABLE_TYPE_RESOURCE_AUDIENCE_INDUSTRY",
		6:   "TAGGABLE_TYPE_PRODUCT_TECHNOLOGY_USER_ORIENTED",
		7:   "TAGGABLE_TYPE_RESOURCE_TYPE",
		8:   "TAGGABLE_TYPE_SUIT_INDUSTRY",
		9:   "TAGGABLE_TYPE_SUIT_SCENE",
		10:  "TAGGABLE_ATLAS_TYPE",
		11:  "TAGGABLE_TYPE_PRODUCT_INDUSTRY_RECOGNITION",
		12:  "TAGGABLE_TYPE_TEAM_INDUSTRY_RECOGNITION",
		14:  "TAGGABLE_TYPE_PRODUCT_APPLICABLE_INDUSTRY",
		23:  "TAGGABLE_TYPE_RESOURCE_SERIES",
		101: "TAGGABLE_TYPE_TEAM_DEVELOPMENT_STAGE",
		102: "TAGGABLE_TYPE_TEAM_PRODUCT_TYPE",
		103: "TAGGABLE_TYPE_AUDIENCE_TEAM_FINANCING_STAGE",
		200: "TAGGABLE_TYPE_AI_ASSISTANT",
		201: "TAGGABLE_TYPE_PERSONAL_USER_LABEL",
		202: "TAGGABLE_TYPE_TEAM_USER_LABEL",
		203: "TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL",
	}
	TaggableType_value = map[string]int32{
		"TAGGABLE_TYPE_UNSPECIFIED":                      0,
		"TAGGABLE_TYPE_PERSONAL_INFORMATION":             1,
		"TAGGABLE_TYPE_TEAM_TYPE":                        2,
		"TAGGABLE_TYPE_TEAM_ATTRIBUTES":                  3,
		"TAGGABLE_TYPE_TEAM_INDUSTRY":                    4,
		"TAGGABLE_TYPE_RESOURCE_AUDIENCE_INDUSTRY":       5,
		"TAGGABLE_TYPE_PRODUCT_TECHNOLOGY_USER_ORIENTED": 6,
		"TAGGABLE_TYPE_RESOURCE_TYPE":                    7,
		"TAGGABLE_TYPE_SUIT_INDUSTRY":                    8,
		"TAGGABLE_TYPE_SUIT_SCENE":                       9,
		"TAGGABLE_ATLAS_TYPE":                            10,
		"TAGGABLE_TYPE_PRODUCT_INDUSTRY_RECOGNITION":     11,
		"TAGGABLE_TYPE_TEAM_INDUSTRY_RECOGNITION":        12,
		"TAGGABLE_TYPE_PRODUCT_APPLICABLE_INDUSTRY":      14,
		"TAGGABLE_TYPE_RESOURCE_SERIES":                  23,
		"TAGGABLE_TYPE_TEAM_DEVELOPMENT_STAGE":           101,
		"TAGGABLE_TYPE_TEAM_PRODUCT_TYPE":                102,
		"TAGGABLE_TYPE_AUDIENCE_TEAM_FINANCING_STAGE":    103,
		"TAGGABLE_TYPE_AI_ASSISTANT":                     200,
		"TAGGABLE_TYPE_PERSONAL_USER_LABEL":              201,
		"TAGGABLE_TYPE_TEAM_USER_LABEL":                  202,
		"TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL":          203,
	}
)

func (x TaggableType) Enum() *TaggableType {
	p := new(TaggableType)
	*p = x
	return p
}

func (x TaggableType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaggableType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_tag_tag_proto_enumTypes[5].Descriptor()
}

func (TaggableType) Type() protoreflect.EnumType {
	return &file_tanlive_tag_tag_proto_enumTypes[5]
}

func (x TaggableType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaggableType.Descriptor instead.
func (TaggableType) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{5}
}

type Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	//
	//	uint64 id = 1 ;
	//
	// 标签权重
	Weight uint32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	//	// 语言
	//	string language = 3 ;
	//
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
	Type TagCreateType `protobuf:"varint,4,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
	NotifyType TagCreateType `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// 是否tmt
	IsTmt bool `protobuf:"varint,7,opt,name=is_tmt,json=isTmt,proto3" json:"is_tmt,omitempty"`
}

func (x *Tag) Reset() {
	*x = Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tag) ProtoMessage() {}

func (x *Tag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tag.ProtoReflect.Descriptor instead.
func (*Tag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{0}
}

func (x *Tag) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Tag) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *Tag) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tag) GetIsTmt() bool {
	if x != nil {
		return x.IsTmt
	}
	return false
}

type TagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 语言类型
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	// 是否tmt
	IsTmt bool `protobuf:"varint,4,opt,name=is_tmt,json=isTmt,proto3" json:"is_tmt,omitempty"`
	// 引用数量
	CitationNum uint32 `protobuf:"varint,5,opt,name=citation_num,json=citationNum,proto3" json:"citation_num,omitempty"`
	// 标签权重
	Weight uint32 `protobuf:"varint,6,opt,name=weight,proto3" json:"weight,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创
	Type TagCreateType `protobuf:"varint,7,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创
	NotifyType     TagCreateType          `protobuf:"varint,8,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	CreateBy       uint64                 `protobuf:"varint,9,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	CreateDate     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	LastUpdateBy   uint64                 `protobuf:"varint,11,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
	// 是否允许删除
	AllowDelete bool `protobuf:"varint,13,opt,name=allow_delete,json=allowDelete,proto3" json:"allow_delete,omitempty"`
}

func (x *TagInfo) Reset() {
	*x = TagInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagInfo) ProtoMessage() {}

func (x *TagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagInfo.ProtoReflect.Descriptor instead.
func (*TagInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{1}
}

func (x *TagInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TagInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TagInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *TagInfo) GetIsTmt() bool {
	if x != nil {
		return x.IsTmt
	}
	return false
}

func (x *TagInfo) GetCitationNum() uint32 {
	if x != nil {
		return x.CitationNum
	}
	return 0
}

func (x *TagInfo) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *TagInfo) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *TagInfo) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *TagInfo) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *TagInfo) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *TagInfo) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *TagInfo) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

func (x *TagInfo) GetAllowDelete() bool {
	if x != nil {
		return x.AllowDelete
	}
	return false
}

type TagIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签索引id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标签索引名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 中文标签信息
	ZhTag *TagInfo `protobuf:"bytes,13,opt,name=zh_tag,json=zhTag,proto3" json:"zh_tag,omitempty"`
	// 英文标签信息
	EnTag *TagInfo `protobuf:"bytes,14,opt,name=en_tag,json=enTag,proto3" json:"en_tag,omitempty"`
}

func (x *TagIndex) Reset() {
	*x = TagIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagIndex) ProtoMessage() {}

func (x *TagIndex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagIndex.ProtoReflect.Descriptor instead.
func (*TagIndex) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{2}
}

func (x *TagIndex) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TagIndex) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TagIndex) GetZhTag() *TagInfo {
	if x != nil {
		return x.ZhTag
	}
	return nil
}

func (x *TagIndex) GetEnTag() *TagInfo {
	if x != nil {
		return x.EnTag
	}
	return nil
}

type TagBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key:TaggableType
	TagBindingMap map[uint64]*BindingTag `protobuf:"bytes,1,rep,name=tag_binding_map,json=tagBindingMap,proto3" json:"tag_binding_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TagBinding) Reset() {
	*x = TagBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagBinding) ProtoMessage() {}

func (x *TagBinding) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagBinding.ProtoReflect.Descriptor instead.
func (*TagBinding) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{3}
}

func (x *TagBinding) GetTagBindingMap() map[uint64]*BindingTag {
	if x != nil {
		return x.TagBindingMap
	}
	return nil
}

type BindingTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tags []*BindingTag_Tag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *BindingTag) Reset() {
	*x = BindingTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingTag) ProtoMessage() {}

func (x *BindingTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingTag.ProtoReflect.Descriptor instead.
func (*BindingTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{4}
}

func (x *BindingTag) GetTags() []*BindingTag_Tag {
	if x != nil {
		return x.Tags
	}
	return nil
}

type CreateTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string       `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TaggableType TaggableType `protobuf:"varint,2,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
}

func (x *CreateTag) Reset() {
	*x = CreateTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTag) ProtoMessage() {}

func (x *CreateTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTag.ProtoReflect.Descriptor instead.
func (*CreateTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{5}
}

func (x *CreateTag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTag) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

type ComTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TaggableType TaggableType `protobuf:"varint,3,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
	Type TagCreateType `protobuf:"varint,4,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
	NotifyType TagCreateType `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 标签语言 en / zh
	Language           string             `protobuf:"bytes,6,opt,name=language,proto3" json:"language,omitempty"`
	TaggableActionType TaggableActionType `protobuf:"varint,7,opt,name=taggable_action_type,json=taggableActionType,proto3,enum=tanlive.tag.TaggableActionType" json:"taggable_action_type,omitempty"`
}

func (x *ComTag) Reset() {
	*x = ComTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComTag) ProtoMessage() {}

func (x *ComTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComTag.ProtoReflect.Descriptor instead.
func (*ComTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{6}
}

func (x *ComTag) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ComTag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ComTag) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ComTag) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ComTag) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ComTag) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ComTag) GetTaggableActionType() TaggableActionType {
	if x != nil {
		return x.TaggableActionType
	}
	return TaggableActionType_TAGGABLE_ACTION_TYPE_UNSPECIFIED
}

type LabelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LabelType   AutoLabelType `protobuf:"varint,1,opt,name=labelType,proto3,enum=tanlive.tag.AutoLabelType" json:"labelType,omitempty"`
	TeamId      uint64        `protobuf:"varint,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	AssistantId uint64        `protobuf:"varint,3,opt,name=assistant_id,json=assistantId,proto3" json:"assistant_id,omitempty"`
}

func (x *LabelInfo) Reset() {
	*x = LabelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelInfo) ProtoMessage() {}

func (x *LabelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelInfo.ProtoReflect.Descriptor instead.
func (*LabelInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{7}
}

func (x *LabelInfo) GetLabelType() AutoLabelType {
	if x != nil {
		return x.LabelType
	}
	return AutoLabelType_AUTO_LABEL_TYPE_UNSPECIFIED
}

func (x *LabelInfo) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *LabelInfo) GetAssistantId() uint64 {
	if x != nil {
		return x.AssistantId
	}
	return 0
}

type DataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataId   uint64        `protobuf:"varint,1,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
}

func (x *DataInfo) Reset() {
	*x = DataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataInfo) ProtoMessage() {}

func (x *DataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataInfo.ProtoReflect.Descriptor instead.
func (*DataInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{8}
}

func (x *DataInfo) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *DataInfo) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

type BindingTag_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tag id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// tag 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
	Type TagCreateType `protobuf:"varint,3,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
	NotifyType         TagCreateType      `protobuf:"varint,4,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	TaggableActionType TaggableActionType `protobuf:"varint,5,opt,name=taggable_action_type,json=taggableActionType,proto3,enum=tanlive.tag.TaggableActionType" json:"taggable_action_type,omitempty"`
}

func (x *BindingTag_Tag) Reset() {
	*x = BindingTag_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_tag_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingTag_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingTag_Tag) ProtoMessage() {}

func (x *BindingTag_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_tag_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingTag_Tag.ProtoReflect.Descriptor instead.
func (*BindingTag_Tag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_tag_proto_rawDescGZIP(), []int{4, 0}
}

func (x *BindingTag_Tag) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BindingTag_Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BindingTag_Tag) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *BindingTag_Tag) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *BindingTag_Tag) GetTaggableActionType() TaggableActionType {
	if x != nil {
		return x.TaggableActionType
	}
	return TaggableActionType_TAGGABLE_ACTION_TYPE_UNSPECIFIED
}

var File_tanlive_tag_tag_proto protoreflect.FileDescriptor

var file_tanlive_tag_tag_proto_rawDesc = []byte{
	0x0a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x74, 0x61,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x61, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62,
	0x61, 0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x01,
	0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2e, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a,
	0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x73, 0x5f, 0x74, 0x6d, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x69, 0x73, 0x54, 0x6d, 0x74, 0x22, 0xf1, 0x03, 0x0a, 0x07, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x74, 0x6d, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x69, 0x73, 0x54, 0x6d, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x63, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79,
	0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x08, 0x54, 0x61,
	0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x7a, 0x68,
	0x5f, 0x74, 0x61, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x7a, 0x68, 0x54, 0x61, 0x67, 0x12, 0x2b, 0x0a, 0x06, 0x65, 0x6e, 0x5f, 0x74, 0x61,
	0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x65,
	0x6e, 0x54, 0x61, 0x67, 0x22, 0xbb, 0x01, 0x0a, 0x0a, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x52, 0x0a, 0x0f, 0x74, 0x61, 0x67, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x74, 0x61, 0x67, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x70, 0x1a, 0x59, 0x0a, 0x12, 0x54, 0x61, 0x67, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xa9, 0x02, 0x0a, 0x0a, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61,
	0x67, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x1a, 0xe9, 0x01, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b,
	0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x14, 0x74,
	0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x74, 0x61, 0x67, 0x67,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5f,
	0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x3e, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xc8, 0x02, 0x0a, 0x06, 0x43, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3e,
	0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b,
	0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x14, 0x74, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x09, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x09, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x58,
	0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x64, 0x61, 0x74,
	0x61, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x7f, 0x0a, 0x0a, 0x54, 0x61, 0x67, 0x54,
	0x6d, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x41, 0x47, 0x5f, 0x54, 0x4d,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x41, 0x47, 0x5f, 0x54, 0x4d, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1b, 0x0a,
	0x17, 0x54, 0x41, 0x47, 0x5f, 0x54, 0x4d, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x41,
	0x47, 0x5f, 0x54, 0x4d, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x66, 0x0a, 0x0d, 0x54, 0x61, 0x67,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41,
	0x47, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x54,
	0x41, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x59, 0x53, 0x54, 0x45, 0x4d, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x41, 0x47, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10,
	0x02, 0x2a, 0x94, 0x01, 0x0a, 0x0d, 0x54, 0x61, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x41, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01,
	0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x02, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x41, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x2a, 0xe6, 0x01, 0x0a, 0x12, 0x54, 0x61, 0x67,
	0x67, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x24, 0x0a, 0x20, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f,
	0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x47, 0x4d, 0x54, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55,
	0x54, 0x4f, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54,
	0x4f, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x21,
	0x0a, 0x1d, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x05, 0x2a, 0xd4, 0x01, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x6f, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4c, 0x41, 0x42, 0x45,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4c, 0x41, 0x42,
	0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10,
	0x01, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x49, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x53, 0x54, 0x41, 0x4e,
	0x54, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x55, 0x54,
	0x4f, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x53,
	0x49, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x10, 0x03, 0x12, 0x1b, 0x0a,
	0x17, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x49, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x55,
	0x54, 0x4f, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x49,
	0x5f, 0x41, 0x54, 0x4c, 0x41, 0x53, 0x10, 0x05, 0x2a, 0xd9, 0x06, 0x0a, 0x0c, 0x54, 0x61, 0x67,
	0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x41, 0x47,
	0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x41, 0x47, 0x47,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01,
	0x12, 0x1b, 0x0a, 0x17, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x02, 0x12, 0x21, 0x0a,
	0x1d, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54,
	0x45, 0x41, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x53, 0x10, 0x03,
	0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x10,
	0x04, 0x12, 0x2c, 0x0a, 0x28, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49,
	0x45, 0x4e, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x10, 0x05, 0x12,
	0x32, 0x0a, 0x2e, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x45, 0x43, 0x48, 0x4e, 0x4f, 0x4c,
	0x4f, 0x47, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4f, 0x52, 0x49, 0x45, 0x4e, 0x54, 0x45,
	0x44, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x5f, 0x49, 0x4e, 0x44, 0x55, 0x53,
	0x54, 0x52, 0x59, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x45, 0x4e,
	0x45, 0x10, 0x09, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x41, 0x54, 0x4c, 0x41, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x0a, 0x12, 0x2e, 0x0a, 0x2a,
	0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x52,
	0x45, 0x43, 0x4f, 0x47, 0x4e, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x2b, 0x0a, 0x27,
	0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45,
	0x41, 0x4d, 0x5f, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x47, 0x4e, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0c, 0x12, 0x2d, 0x0a, 0x29, 0x54, 0x41, 0x47,
	0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x49, 0x4e,
	0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x10, 0x0e, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x41, 0x47, 0x47,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x17, 0x12, 0x28, 0x0a, 0x24, 0x54,
	0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x41,
	0x4d, 0x5f, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x10, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x50, 0x52, 0x4f, 0x44,
	0x55, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x66, 0x12, 0x2f, 0x0a, 0x2b, 0x54, 0x41,
	0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49,
	0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x10, 0x67, 0x12, 0x1f, 0x0a, 0x1a, 0x54,
	0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x49, 0x5f,
	0x41, 0x53, 0x53, 0x49, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x10, 0xc8, 0x01, 0x12, 0x26, 0x0a, 0x21,
	0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45,
	0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x42, 0x45,
	0x4c, 0x10, 0xc9, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x54, 0x41, 0x47, 0x47, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x4c, 0x41, 0x42, 0x45, 0x4c, 0x10, 0xca, 0x01, 0x12, 0x2a, 0x0a, 0x25, 0x54, 0x41, 0x47, 0x47,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x49, 0x5f, 0x41, 0x53, 0x53,
	0x49, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x42, 0x45,
	0x4c, 0x10, 0xcb, 0x01, 0x42, 0x3d, 0x5a, 0x3b, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76,
	0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x74, 0x61, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_tag_tag_proto_rawDescOnce sync.Once
	file_tanlive_tag_tag_proto_rawDescData = file_tanlive_tag_tag_proto_rawDesc
)

func file_tanlive_tag_tag_proto_rawDescGZIP() []byte {
	file_tanlive_tag_tag_proto_rawDescOnce.Do(func() {
		file_tanlive_tag_tag_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_tag_tag_proto_rawDescData)
	})
	return file_tanlive_tag_tag_proto_rawDescData
}

var file_tanlive_tag_tag_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_tanlive_tag_tag_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_tanlive_tag_tag_proto_goTypes = []interface{}{
	(TagTmtType)(0),               // 0: tanlive.tag.TagTmtType
	(TagCreateType)(0),            // 1: tanlive.tag.TagCreateType
	(TagActionType)(0),            // 2: tanlive.tag.TagActionType
	(TaggableActionType)(0),       // 3: tanlive.tag.TaggableActionType
	(AutoLabelType)(0),            // 4: tanlive.tag.AutoLabelType
	(TaggableType)(0),             // 5: tanlive.tag.TaggableType
	(*Tag)(nil),                   // 6: tanlive.tag.Tag
	(*TagInfo)(nil),               // 7: tanlive.tag.TagInfo
	(*TagIndex)(nil),              // 8: tanlive.tag.TagIndex
	(*TagBinding)(nil),            // 9: tanlive.tag.TagBinding
	(*BindingTag)(nil),            // 10: tanlive.tag.BindingTag
	(*CreateTag)(nil),             // 11: tanlive.tag.CreateTag
	(*ComTag)(nil),                // 12: tanlive.tag.ComTag
	(*LabelInfo)(nil),             // 13: tanlive.tag.LabelInfo
	(*DataInfo)(nil),              // 14: tanlive.tag.DataInfo
	nil,                           // 15: tanlive.tag.TagBinding.TagBindingMapEntry
	(*BindingTag_Tag)(nil),        // 16: tanlive.tag.BindingTag.Tag
	(*timestamppb.Timestamp)(nil), // 17: google.protobuf.Timestamp
	(base.DataType)(0),            // 18: tanlive.base.DataType
}
var file_tanlive_tag_tag_proto_depIdxs = []int32{
	1,  // 0: tanlive.tag.Tag.type:type_name -> tanlive.tag.TagCreateType
	1,  // 1: tanlive.tag.Tag.notify_type:type_name -> tanlive.tag.TagCreateType
	1,  // 2: tanlive.tag.TagInfo.type:type_name -> tanlive.tag.TagCreateType
	1,  // 3: tanlive.tag.TagInfo.notify_type:type_name -> tanlive.tag.TagCreateType
	17, // 4: tanlive.tag.TagInfo.create_date:type_name -> google.protobuf.Timestamp
	17, // 5: tanlive.tag.TagInfo.last_update_date:type_name -> google.protobuf.Timestamp
	7,  // 6: tanlive.tag.TagIndex.zh_tag:type_name -> tanlive.tag.TagInfo
	7,  // 7: tanlive.tag.TagIndex.en_tag:type_name -> tanlive.tag.TagInfo
	15, // 8: tanlive.tag.TagBinding.tag_binding_map:type_name -> tanlive.tag.TagBinding.TagBindingMapEntry
	16, // 9: tanlive.tag.BindingTag.tags:type_name -> tanlive.tag.BindingTag.Tag
	5,  // 10: tanlive.tag.CreateTag.taggable_type:type_name -> tanlive.tag.TaggableType
	5,  // 11: tanlive.tag.ComTag.taggable_type:type_name -> tanlive.tag.TaggableType
	1,  // 12: tanlive.tag.ComTag.type:type_name -> tanlive.tag.TagCreateType
	1,  // 13: tanlive.tag.ComTag.notify_type:type_name -> tanlive.tag.TagCreateType
	3,  // 14: tanlive.tag.ComTag.taggable_action_type:type_name -> tanlive.tag.TaggableActionType
	4,  // 15: tanlive.tag.LabelInfo.labelType:type_name -> tanlive.tag.AutoLabelType
	18, // 16: tanlive.tag.DataInfo.data_type:type_name -> tanlive.base.DataType
	10, // 17: tanlive.tag.TagBinding.TagBindingMapEntry.value:type_name -> tanlive.tag.BindingTag
	1,  // 18: tanlive.tag.BindingTag.Tag.type:type_name -> tanlive.tag.TagCreateType
	1,  // 19: tanlive.tag.BindingTag.Tag.notify_type:type_name -> tanlive.tag.TagCreateType
	3,  // 20: tanlive.tag.BindingTag.Tag.taggable_action_type:type_name -> tanlive.tag.TaggableActionType
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_tanlive_tag_tag_proto_init() }
func file_tanlive_tag_tag_proto_init() {
	if File_tanlive_tag_tag_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_tag_tag_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindingTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_tag_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindingTag_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_tag_tag_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_tag_tag_proto_goTypes,
		DependencyIndexes: file_tanlive_tag_tag_proto_depIdxs,
		EnumInfos:         file_tanlive_tag_tag_proto_enumTypes,
		MessageInfos:      file_tanlive_tag_tag_proto_msgTypes,
	}.Build()
	File_tanlive_tag_tag_proto = out.File
	file_tanlive_tag_tag_proto_rawDesc = nil
	file_tanlive_tag_tag_proto_goTypes = nil
	file_tanlive_tag_tag_proto_depIdxs = nil
}
