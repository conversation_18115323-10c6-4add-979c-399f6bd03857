syntax = "proto3";

package tanlive.tag;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag";

import "google/protobuf/timestamp.proto";
import "tanlive/base/ugc.proto";

// TagTmtType 标签翻译类型枚举
enum TagTmtType {
  TAG_TMT_TYPE_UNSPECIFIED = 0;
  // 已编辑
  TAG_TMT_TYPE_EDITED = 1;
  // 已翻译
  TAG_TMT_TYPE_TRANSLATED = 2;
  // 待翻译
  TAG_TMT_TYPE_UNTRANSLATED = 3;
}

// TagCreateType 标签创建类型枚举
enum TagCreateType {
  TAG_CREATE_TYPE_UNSPECIFIED = 0;
  // 系统标签
  TAG_CREATE_TYPE_SYSTEM = 1;
  // 自创标签
  TAG_CREATE_TYPE_USER = 2;
}

// TagActionType 标签操作类型
enum TagActionType {
  TAG_ACTION_TYPE_UNSPECIFIED = 0;
  // 正常
  TAG_ACTION_TYPE_NORMAL = 1;
  // 禁止删除
  TAG_ACTION_TYPE_NO_DELETION = 2;
  // 及联删除
  TAG_ACTION_TYPE_RELATION_DELETION = 3;
}

// TaggableActionType 标签的绑定类型
enum TaggableActionType {
  TAGGABLE_ACTION_TYPE_UNSPECIFIED = 0;
  // 用户的普通绑定
  TAGGABLE_ACTION_TYPE_NORMAL = 1;
  // 运营人员绑定
  TAGGABLE_ACTION_TYPE_MGMT = 2;
  // 自动打标
  TAGGABLE_ACTION_TYPE_AUTO = 3;
  // 自动打标的一次性绑定
  TAGGABLE_ACTION_TYPE_AUTO_DISPOSABLE = 4;
  // 及联绑定
  TAGGABLE_ACTION_TYPE_RELATION = 5;
}

enum AutoLabelType {
  AUTO_LABEL_TYPE_UNSPECIFIED = 0;
  // 默认行为
  AUTO_LABEL_TYPE_DEFAULT = 1;
  // ai助手管理员
  AUTO_LABEL_TYPE_AI_ASSISTANT_ADMIN = 2;
  // 加入ai助手团队
  AUTO_LABEL_TYPE_ASSISTANT_TEAM = 3;
  // ai对话
  AUTO_LABEL_TYPE_AI_CHAT= 4;
  // ai图谱
  AUTO_LABEL_TYPE_AI_ATLAS = 5;
}

// 标签类型
enum TaggableType {
  TAGGABLE_TYPE_UNSPECIFIED = 0;
  // 1 个人信息-我来自
  TAGGABLE_TYPE_PERSONAL_INFORMATION = 1;
  // 2 团队类型
  TAGGABLE_TYPE_TEAM_TYPE = 2;
  // 3 团队属性
  TAGGABLE_TYPE_TEAM_ATTRIBUTES = 3;
  // 4 团队行业
  TAGGABLE_TYPE_TEAM_INDUSTRY = 4;
  // 5 资源受众行业
  TAGGABLE_TYPE_RESOURCE_AUDIENCE_INDUSTRY = 5;
  // 6 产品技术-面向用户（场景）
  TAGGABLE_TYPE_PRODUCT_TECHNOLOGY_USER_ORIENTED = 6;
  // 7 资源类型
  TAGGABLE_TYPE_RESOURCE_TYPE = 7;
  // 8 图谱-适用行业
  TAGGABLE_TYPE_SUIT_INDUSTRY = 8;
  // 9 图谱-适用场景
  TAGGABLE_TYPE_SUIT_SCENE = 9;
  // 10 图谱类型
  TAGGABLE_ATLAS_TYPE = 10;
  // 11 产品行业认可 --2.2迭代将 21资源-产品行业认可合并入11
  TAGGABLE_TYPE_PRODUCT_INDUSTRY_RECOGNITION = 11;
  // 12 团队行业认可 --2.2迭代将 22资源-团队行业认可合并入12
  TAGGABLE_TYPE_TEAM_INDUSTRY_RECOGNITION = 12;
  // 14 产品适用行业
  TAGGABLE_TYPE_PRODUCT_APPLICABLE_INDUSTRY = 14;
  // 23 资源系列
  TAGGABLE_TYPE_RESOURCE_SERIES = 23;
  // 101 团队发展阶段
  TAGGABLE_TYPE_TEAM_DEVELOPMENT_STAGE = 101;
  // 102 团队产品类型
  TAGGABLE_TYPE_TEAM_PRODUCT_TYPE = 102;
  // 103 受众团队融资阶段
  TAGGABLE_TYPE_AUDIENCE_TEAM_FINANCING_STAGE = 103;
  // 200 后端用于业务及联的特殊类型，前端忽略
  TAGGABLE_TYPE_AI_ASSISTANT = 200;
  // 201 个人用户-用户标签
  TAGGABLE_TYPE_PERSONAL_USER_LABEL = 201;
  // 202 团队用户-用户标签
  TAGGABLE_TYPE_TEAM_USER_LABEL = 202;
  // 203 AI助手-给用户打标
  TAGGABLE_TYPE_AI_ASSISTANT_USER_LABEL = 203;
}

message Tag {
  // 标签id
  //  uint64 id = 1 ;
  // 标签权重
  uint32 weight = 2 ;
  //  // 语言
  //  string language = 3 ;
  // 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
  TagCreateType type = 4;
  // 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
  TagCreateType notify_type = 5;
  // 标签名称
  string name = 6;
  // 是否tmt
  bool is_tmt = 7;
}

message TagInfo {
  // 标签id
  uint64 id = 1;
  // 标签名称
  string name = 2;
  // 语言类型
  string language = 3;
  // 是否tmt
  bool is_tmt = 4;
  // 引用数量
  uint32 citation_num = 5;
  // 标签权重
  uint32 weight = 6;
  // 标签类型（多语言选项或注册用） 1 系统 ；2 自创
  TagCreateType type = 7;
  // 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创
  TagCreateType notify_type = 8;
  uint64 create_by = 9;
  google.protobuf.Timestamp create_date = 10;
  uint64 last_update_by = 11;
  google.protobuf.Timestamp last_update_date = 12;
  // 是否允许删除
  bool allow_delete = 13;
}

message TagIndex {
  // 标签索引id
  uint64 id = 1;
  // 标签索引名称
  string name = 2;
  // 中文标签信息
  TagInfo zh_tag = 13;
  // 英文标签信息
  TagInfo en_tag = 14;
}

message TagBinding {
  // key:TaggableType
  map<uint64, BindingTag> tag_binding_map = 1;
}

message BindingTag {
  repeated Tag tags = 1;
  message Tag {
    // tag id
    uint64 id = 1;
    // tag 名称
    string name = 2;
    // 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
    TagCreateType type = 3;
    // 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
    TagCreateType notify_type = 4;
    TaggableActionType taggable_action_type = 5;
  }
}

message CreateTag {
  string name = 1;
  TaggableType taggable_type = 2;
}

message ComTag {
  uint64 id = 1;
  string name = 2;
  TaggableType taggable_type = 3;
  // 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
  TagCreateType type = 4;
  // 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
  TagCreateType notify_type = 5;
  // 标签语言 en / zh
  string language = 6;
  TaggableActionType taggable_action_type = 7;
}

message LabelInfo {
  AutoLabelType labelType = 1 ;
  uint64 team_id = 2;
  uint64 assistant_id = 3;
}

message DataInfo {
  uint64 data_id = 1 ;
  tanlive.base.DataType data_type = 2 ;
}