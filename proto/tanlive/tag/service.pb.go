// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/tag/service.proto

package tag

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqUpdateSystemTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	// 标签权重
	Weight uint32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创
	Type TagCreateType `protobuf:"varint,3,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 标签索引类型
	TaggableType TaggableType `protobuf:"varint,4,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 定向推送标签类型（我来自标签时生效） 1 系统 ；2 自创
	NotifyType TagCreateType `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	UserId     uint64        `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqUpdateSystemTag) Reset() {
	*x = ReqUpdateSystemTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqUpdateSystemTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqUpdateSystemTag) ProtoMessage() {}

func (x *ReqUpdateSystemTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqUpdateSystemTag.ProtoReflect.Descriptor instead.
func (*ReqUpdateSystemTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReqUpdateSystemTag) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

func (x *ReqUpdateSystemTag) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *ReqUpdateSystemTag) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqUpdateSystemTag) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqUpdateSystemTag) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqUpdateSystemTag) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ReqEditSystemTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签索引id 为0 则创建，否则更新对应数据
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 标签索引名称
	IndexName string `protobuf:"bytes,2,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	// 中文标签信息
	ZhTag *Tag `protobuf:"bytes,3,opt,name=zh_tag,json=zhTag,proto3" json:"zh_tag,omitempty"`
	// 英文标签信息
	EnTag *Tag `protobuf:"bytes,4,opt,name=en_tag,json=enTag,proto3" json:"en_tag,omitempty"`
	// 标签索引类型
	TaggableType TaggableType `protobuf:"varint,5,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	UserId       uint64       `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqEditSystemTag) Reset() {
	*x = ReqEditSystemTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqEditSystemTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqEditSystemTag) ProtoMessage() {}

func (x *ReqEditSystemTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqEditSystemTag.ProtoReflect.Descriptor instead.
func (*ReqEditSystemTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{1}
}

func (x *ReqEditSystemTag) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *ReqEditSystemTag) GetIndexName() string {
	if x != nil {
		return x.IndexName
	}
	return ""
}

func (x *ReqEditSystemTag) GetZhTag() *Tag {
	if x != nil {
		return x.ZhTag
	}
	return nil
}

func (x *ReqEditSystemTag) GetEnTag() *Tag {
	if x != nil {
		return x.EnTag
	}
	return nil
}

func (x *ReqEditSystemTag) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqEditSystemTag) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ReqDeleteSystemTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签索引数组，会删除索引下的所有标签
	TagIndexIds []uint64 `protobuf:"varint,1,rep,packed,name=tag_index_ids,json=tagIndexIds,proto3" json:"tag_index_ids,omitempty"`
	// 标签索引类型
	TaggableType TaggableType  `protobuf:"varint,2,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	ActionType   TagActionType `protobuf:"varint,3,opt,name=action_type,json=actionType,proto3,enum=tanlive.tag.TagActionType" json:"action_type,omitempty"`
	AssistantIds []uint64      `protobuf:"varint,4,rep,packed,name=assistant_ids,json=assistantIds,proto3" json:"assistant_ids,omitempty"`
}

func (x *ReqDeleteSystemTag) Reset() {
	*x = ReqDeleteSystemTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDeleteSystemTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDeleteSystemTag) ProtoMessage() {}

func (x *ReqDeleteSystemTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDeleteSystemTag.ProtoReflect.Descriptor instead.
func (*ReqDeleteSystemTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReqDeleteSystemTag) GetTagIndexIds() []uint64 {
	if x != nil {
		return x.TagIndexIds
	}
	return nil
}

func (x *ReqDeleteSystemTag) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqDeleteSystemTag) GetActionType() TagActionType {
	if x != nil {
		return x.ActionType
	}
	return TagActionType_TAG_ACTION_TYPE_UNSPECIFIED
}

func (x *ReqDeleteSystemTag) GetAssistantIds() []uint64 {
	if x != nil {
		return x.AssistantIds
	}
	return nil
}

type ReqMergeSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 合并后的标签索引id
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 需要合并标签索引数组
	MergeIndexIds []uint64 `protobuf:"varint,2,rep,packed,name=merge_index_ids,json=mergeIndexIds,proto3" json:"merge_index_ids,omitempty"`
	// 中文标签信息
	ZhTag *Tag `protobuf:"bytes,3,opt,name=zh_tag,json=zhTag,proto3" json:"zh_tag,omitempty"`
	// 英文标签信息
	EnTag *Tag `protobuf:"bytes,4,opt,name=en_tag,json=enTag,proto3" json:"en_tag,omitempty"`
	// 合并的标签类型
	TaggableType TaggableType `protobuf:"varint,5,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	UserId       uint64       `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqMergeSystemTags) Reset() {
	*x = ReqMergeSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqMergeSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqMergeSystemTags) ProtoMessage() {}

func (x *ReqMergeSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqMergeSystemTags.ProtoReflect.Descriptor instead.
func (*ReqMergeSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{3}
}

func (x *ReqMergeSystemTags) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *ReqMergeSystemTags) GetMergeIndexIds() []uint64 {
	if x != nil {
		return x.MergeIndexIds
	}
	return nil
}

func (x *ReqMergeSystemTags) GetZhTag() *Tag {
	if x != nil {
		return x.ZhTag
	}
	return nil
}

func (x *ReqMergeSystemTags) GetEnTag() *Tag {
	if x != nil {
		return x.EnTag
	}
	return nil
}

func (x *ReqMergeSystemTags) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqMergeSystemTags) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ReqDescribeSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset  uint32                        `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit   uint32                        `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	OrderBy []*base.OrderBy               `protobuf:"bytes,3,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Filter  *ReqDescribeSystemTags_Filter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeSystemTags) Reset() {
	*x = ReqDescribeSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSystemTags) ProtoMessage() {}

func (x *ReqDescribeSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSystemTags.ProtoReflect.Descriptor instead.
func (*ReqDescribeSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{4}
}

func (x *ReqDescribeSystemTags) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeSystemTags) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeSystemTags) GetOrderBy() []*base.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ReqDescribeSystemTags) GetFilter() *ReqDescribeSystemTags_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type RspDescribeSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagSet     []*RspDescribeSystemTags_TagInfo `protobuf:"bytes,1,rep,name=tag_set,json=tagSet,proto3" json:"tag_set,omitempty"`
	TotalCount uint32                           `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeSystemTags) Reset() {
	*x = RspDescribeSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSystemTags) ProtoMessage() {}

func (x *RspDescribeSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSystemTags.ProtoReflect.Descriptor instead.
func (*RspDescribeSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{5}
}

func (x *RspDescribeSystemTags) GetTagSet() []*RspDescribeSystemTags_TagInfo {
	if x != nil {
		return x.TagSet
	}
	return nil
}

func (x *RspDescribeSystemTags) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqDescribeSystemTagsByIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset  uint32                               `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit   uint32                               `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter  *ReqDescribeSystemTagsByIndex_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy *base.OrderBy                        `protobuf:"bytes,4,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
}

func (x *ReqDescribeSystemTagsByIndex) Reset() {
	*x = ReqDescribeSystemTagsByIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSystemTagsByIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSystemTagsByIndex) ProtoMessage() {}

func (x *ReqDescribeSystemTagsByIndex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSystemTagsByIndex.ProtoReflect.Descriptor instead.
func (*ReqDescribeSystemTagsByIndex) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{6}
}

func (x *ReqDescribeSystemTagsByIndex) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeSystemTagsByIndex) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeSystemTagsByIndex) GetFilter() *ReqDescribeSystemTagsByIndex_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReqDescribeSystemTagsByIndex) GetOrderBy() *base.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

type RspDescribeSystemTagsByIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagSet     []*TagIndex `protobuf:"bytes,1,rep,name=tag_set,json=tagSet,proto3" json:"tag_set,omitempty"`
	TotalCount uint32      `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeSystemTagsByIndex) Reset() {
	*x = RspDescribeSystemTagsByIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSystemTagsByIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSystemTagsByIndex) ProtoMessage() {}

func (x *RspDescribeSystemTagsByIndex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSystemTagsByIndex.ProtoReflect.Descriptor instead.
func (*RspDescribeSystemTagsByIndex) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{7}
}

func (x *RspDescribeSystemTagsByIndex) GetTagSet() []*TagIndex {
	if x != nil {
		return x.TagSet
	}
	return nil
}

func (x *RspDescribeSystemTagsByIndex) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// ReqDescribeTeamTagBindingInfos 查询团队类型标签关联详情
type ReqDescribeTeamTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint32                                 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32                                 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter *ReqDescribeTeamTagBindingInfos_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeTeamTagBindingInfos) Reset() {
	*x = ReqDescribeTeamTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeTeamTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeTeamTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeTeamTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeTeamTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeTeamTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{8}
}

func (x *ReqDescribeTeamTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeTeamTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeTeamTagBindingInfos) GetFilter() *ReqDescribeTeamTagBindingInfos_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// RspDescribeTeamTagBindingInfos 查询团队类型标签关联详情回复
type RspDescribeTeamTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeTeamTagBindingInfos_BindingObject `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                          `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeTeamTagBindingInfos) Reset() {
	*x = RspDescribeTeamTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeTeamTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeTeamTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeTeamTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeTeamTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeTeamTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{9}
}

func (x *RspDescribeTeamTagBindingInfos) GetBindingObjects() []*RspDescribeTeamTagBindingInfos_BindingObject {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeTeamTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// ReqDescribeResourceTagBindingInfos 查询资源相关标签关联详情
type ReqDescribeResourceTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint32                                     `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32                                     `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter *ReqDescribeResourceTagBindingInfos_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeResourceTagBindingInfos) Reset() {
	*x = ReqDescribeResourceTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeResourceTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeResourceTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeResourceTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeResourceTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeResourceTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{10}
}

func (x *ReqDescribeResourceTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeResourceTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeResourceTagBindingInfos) GetFilter() *ReqDescribeResourceTagBindingInfos_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// RspDescribeResourceTagBindingInfos 查询资源相关标签关联详情回复
type RspDescribeResourceTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeResourceTagBindingInfos_BindingObject `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                              `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeResourceTagBindingInfos) Reset() {
	*x = RspDescribeResourceTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeResourceTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeResourceTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeResourceTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeResourceTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeResourceTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{11}
}

func (x *RspDescribeResourceTagBindingInfos) GetBindingObjects() []*RspDescribeResourceTagBindingInfos_BindingObject {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeResourceTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqDescribeAtlasTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint32                                  `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32                                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter *ReqDescribeAtlasTagBindingInfos_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeAtlasTagBindingInfos) Reset() {
	*x = ReqDescribeAtlasTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeAtlasTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeAtlasTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeAtlasTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeAtlasTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeAtlasTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{12}
}

func (x *ReqDescribeAtlasTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeAtlasTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeAtlasTagBindingInfos) GetFilter() *ReqDescribeAtlasTagBindingInfos_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type RspDescribeAtlasTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeAtlasTagBindingInfos_BindingObject `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                           `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeAtlasTagBindingInfos) Reset() {
	*x = RspDescribeAtlasTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeAtlasTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeAtlasTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeAtlasTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeAtlasTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeAtlasTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{13}
}

func (x *RspDescribeAtlasTagBindingInfos) GetBindingObjects() []*RspDescribeAtlasTagBindingInfos_BindingObject {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeAtlasTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// ReqDescribeProductTagBindingInfos 查询产品技术-面向用户标签关联详情
type ReqDescribeProductTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint32                                    `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32                                    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter *ReqDescribeProductTagBindingInfos_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ReqDescribeProductTagBindingInfos) Reset() {
	*x = ReqDescribeProductTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeProductTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeProductTagBindingInfos) ProtoMessage() {}

func (x *ReqDescribeProductTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeProductTagBindingInfos.ProtoReflect.Descriptor instead.
func (*ReqDescribeProductTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{14}
}

func (x *ReqDescribeProductTagBindingInfos) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqDescribeProductTagBindingInfos) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqDescribeProductTagBindingInfos) GetFilter() *ReqDescribeProductTagBindingInfos_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// RspDescribeProductTagBindingInfos 查询产品技术-面向用户类型标签关联详情回复
type RspDescribeProductTagBindingInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingObjects []*RspDescribeProductTagBindingInfos_BindingObject `protobuf:"bytes,1,rep,name=binding_objects,json=bindingObjects,proto3" json:"binding_objects,omitempty"`
	TotalCount     uint32                                             `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspDescribeProductTagBindingInfos) Reset() {
	*x = RspDescribeProductTagBindingInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeProductTagBindingInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeProductTagBindingInfos) ProtoMessage() {}

func (x *RspDescribeProductTagBindingInfos) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeProductTagBindingInfos.ProtoReflect.Descriptor instead.
func (*RspDescribeProductTagBindingInfos) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{15}
}

func (x *RspDescribeProductTagBindingInfos) GetBindingObjects() []*RspDescribeProductTagBindingInfos_BindingObject {
	if x != nil {
		return x.BindingObjects
	}
	return nil
}

func (x *RspDescribeProductTagBindingInfos) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqBatchImportSystemTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 导入标签索引数组
	TagIndex []*ReqBatchImportSystemTag_TagIndex `protobuf:"bytes,1,rep,name=tag_index,json=tagIndex,proto3" json:"tag_index,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
	Type TagCreateType `protobuf:"varint,2,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
	NotifyType TagCreateType `protobuf:"varint,3,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 标签类型
	TaggableType TaggableType `protobuf:"varint,4,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 备注
	Remarks string `protobuf:"bytes,5,opt,name=remarks,proto3" json:"remarks,omitempty"`
	UserId  uint64 `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ReqBatchImportSystemTag) Reset() {
	*x = ReqBatchImportSystemTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBatchImportSystemTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBatchImportSystemTag) ProtoMessage() {}

func (x *ReqBatchImportSystemTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBatchImportSystemTag.ProtoReflect.Descriptor instead.
func (*ReqBatchImportSystemTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{16}
}

func (x *ReqBatchImportSystemTag) GetTagIndex() []*ReqBatchImportSystemTag_TagIndex {
	if x != nil {
		return x.TagIndex
	}
	return nil
}

func (x *ReqBatchImportSystemTag) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqBatchImportSystemTag) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqBatchImportSystemTag) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqBatchImportSystemTag) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *ReqBatchImportSystemTag) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ReqGetSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签索引类型
	TaggableType TaggableType `protobuf:"varint,1,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 语言类型zh、en
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	// 排序
	OrderBy []*base.OrderBy `protobuf:"bytes,3,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// 定向推送用的筛选条件 1 系统标签
	NotifyType TagCreateType `protobuf:"varint,4,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 标签名称模糊搜索
	TagName string `protobuf:"bytes,5,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 是否直接显示标签id，不会被编码为hashId
	IsDirectDisplayId bool `protobuf:"varint,6,opt,name=is_direct_display_id,json=isDirectDisplayId,proto3" json:"is_direct_display_id,omitempty"`
	// 是否包含 助手用户标签
	WithAssistantUserTag bool     `protobuf:"varint,7,opt,name=with_assistant_user_tag,json=withAssistantUserTag,proto3" json:"with_assistant_user_tag,omitempty"`
	TagIds               []uint64 `protobuf:"varint,8,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
}

func (x *ReqGetSystemTags) Reset() {
	*x = ReqGetSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetSystemTags) ProtoMessage() {}

func (x *ReqGetSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetSystemTags.ProtoReflect.Descriptor instead.
func (*ReqGetSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{17}
}

func (x *ReqGetSystemTags) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqGetSystemTags) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqGetSystemTags) GetOrderBy() []*base.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ReqGetSystemTags) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqGetSystemTags) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *ReqGetSystemTags) GetIsDirectDisplayId() bool {
	if x != nil {
		return x.IsDirectDisplayId
	}
	return false
}

func (x *ReqGetSystemTags) GetWithAssistantUserTag() bool {
	if x != nil {
		return x.WithAssistantUserTag
	}
	return false
}

func (x *ReqGetSystemTags) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

type RspGetSystemTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagSet []*RspGetSystemTags_Tag `protobuf:"bytes,1,rep,name=tag_set,json=tagSet,proto3" json:"tag_set,omitempty"`
}

func (x *RspGetSystemTags) Reset() {
	*x = RspGetSystemTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetSystemTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetSystemTags) ProtoMessage() {}

func (x *RspGetSystemTags) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetSystemTags.ProtoReflect.Descriptor instead.
func (*RspGetSystemTags) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{18}
}

func (x *RspGetSystemTags) GetTagSet() []*RspGetSystemTags_Tag {
	if x != nil {
		return x.TagSet
	}
	return nil
}

type ReqCreateUserTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateBy uint64       `protobuf:"varint,1,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	Tags     []*CreateTag `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *ReqCreateUserTag) Reset() {
	*x = ReqCreateUserTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateUserTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateUserTag) ProtoMessage() {}

func (x *ReqCreateUserTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateUserTag.ProtoReflect.Descriptor instead.
func (*ReqCreateUserTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{19}
}

func (x *ReqCreateUserTag) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *ReqCreateUserTag) GetTags() []*CreateTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

type RspCreateUserTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagIds []uint64 `protobuf:"varint,1,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
}

func (x *RspCreateUserTag) Reset() {
	*x = RspCreateUserTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspCreateUserTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspCreateUserTag) ProtoMessage() {}

func (x *RspCreateUserTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspCreateUserTag.ProtoReflect.Descriptor instead.
func (*RspCreateUserTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{20}
}

func (x *RspCreateUserTag) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

type ReqCreateTagBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint64    `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DataInfo *DataInfo `protobuf:"bytes,2,opt,name=data_info,json=dataInfo,proto3" json:"data_info,omitempty"`
	// 绑定现有的标签
	TagIds []uint64 `protobuf:"varint,3,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// 新增的自创标签
	TagNames     []string           `protobuf:"bytes,4,rep,name=tag_names,json=tagNames,proto3" json:"tag_names,omitempty"`
	TaggableType TaggableType       `protobuf:"varint,5,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	Action       TaggableActionType `protobuf:"varint,6,opt,name=action,proto3,enum=tanlive.tag.TaggableActionType" json:"action,omitempty"`
}

func (x *ReqCreateTagBinding) Reset() {
	*x = ReqCreateTagBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCreateTagBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCreateTagBinding) ProtoMessage() {}

func (x *ReqCreateTagBinding) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCreateTagBinding.ProtoReflect.Descriptor instead.
func (*ReqCreateTagBinding) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{21}
}

func (x *ReqCreateTagBinding) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqCreateTagBinding) GetDataInfo() *DataInfo {
	if x != nil {
		return x.DataInfo
	}
	return nil
}

func (x *ReqCreateTagBinding) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqCreateTagBinding) GetTagNames() []string {
	if x != nil {
		return x.TagNames
	}
	return nil
}

func (x *ReqCreateTagBinding) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqCreateTagBinding) GetAction() TaggableActionType {
	if x != nil {
		return x.Action
	}
	return TaggableActionType_TAGGABLE_ACTION_TYPE_UNSPECIFIED
}

type ReqAutoBindingAITag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    uint64      `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DataInfo  []*DataInfo `protobuf:"bytes,2,rep,name=data_info,json=dataInfo,proto3" json:"data_info,omitempty"`
	LabelInfo *LabelInfo  `protobuf:"bytes,3,opt,name=label_info,json=labelInfo,proto3" json:"label_info,omitempty"`
	Unbind    bool        `protobuf:"varint,4,opt,name=unbind,proto3" json:"unbind,omitempty"`
}

func (x *ReqAutoBindingAITag) Reset() {
	*x = ReqAutoBindingAITag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqAutoBindingAITag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqAutoBindingAITag) ProtoMessage() {}

func (x *ReqAutoBindingAITag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqAutoBindingAITag.ProtoReflect.Descriptor instead.
func (*ReqAutoBindingAITag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{22}
}

func (x *ReqAutoBindingAITag) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqAutoBindingAITag) GetDataInfo() []*DataInfo {
	if x != nil {
		return x.DataInfo
	}
	return nil
}

func (x *ReqAutoBindingAITag) GetLabelInfo() *LabelInfo {
	if x != nil {
		return x.LabelInfo
	}
	return nil
}

func (x *ReqAutoBindingAITag) GetUnbind() bool {
	if x != nil {
		return x.Unbind
	}
	return false
}

type ReqBindingTagChangeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Old uint64 `protobuf:"varint,1,opt,name=old,proto3" json:"old,omitempty"`
	// 从old 迁移到 new上
	New      uint64        `protobuf:"varint,2,opt,name=new,proto3" json:"new,omitempty"`
	DataType base.DataType `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
}

func (x *ReqBindingTagChangeData) Reset() {
	*x = ReqBindingTagChangeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBindingTagChangeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBindingTagChangeData) ProtoMessage() {}

func (x *ReqBindingTagChangeData) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBindingTagChangeData.ProtoReflect.Descriptor instead.
func (*ReqBindingTagChangeData) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{23}
}

func (x *ReqBindingTagChangeData) GetOld() uint64 {
	if x != nil {
		return x.Old
	}
	return 0
}

func (x *ReqBindingTagChangeData) GetNew() uint64 {
	if x != nil {
		return x.New
	}
	return 0
}

func (x *ReqBindingTagChangeData) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

type ReqGetTagBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataIds  []uint64      `protobuf:"varint,1,rep,packed,name=data_ids,json=dataIds,proto3" json:"data_ids,omitempty"`
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 标签类型：重要！如果要查询是因为助手自动给用户打标的标签，一定要增加 TAGGABLE_TYPE_AI_ASSISTANT 这个类型
	TaggableTypes []TaggableType `protobuf:"varint,3,rep,packed,name=taggable_types,json=taggableTypes,proto3,enum=tanlive.tag.TaggableType" json:"taggable_types,omitempty"`
	// 语言类型 en zh
	Language string `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *ReqGetTagBinding) Reset() {
	*x = ReqGetTagBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTagBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTagBinding) ProtoMessage() {}

func (x *ReqGetTagBinding) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTagBinding.ProtoReflect.Descriptor instead.
func (*ReqGetTagBinding) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{24}
}

func (x *ReqGetTagBinding) GetDataIds() []uint64 {
	if x != nil {
		return x.DataIds
	}
	return nil
}

func (x *ReqGetTagBinding) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqGetTagBinding) GetTaggableTypes() []TaggableType {
	if x != nil {
		return x.TaggableTypes
	}
	return nil
}

func (x *ReqGetTagBinding) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type RspGetTagBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key:data_id
	TagBinding map[uint64]*TagBinding `protobuf:"bytes,1,rep,name=tag_binding,json=tagBinding,proto3" json:"tag_binding,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RspGetTagBinding) Reset() {
	*x = RspGetTagBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTagBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTagBinding) ProtoMessage() {}

func (x *RspGetTagBinding) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTagBinding.ProtoReflect.Descriptor instead.
func (*RspGetTagBinding) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{25}
}

func (x *RspGetTagBinding) GetTagBinding() map[uint64]*TagBinding {
	if x != nil {
		return x.TagBinding
	}
	return nil
}

type ReqCleanTagBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataIds  []uint64      `protobuf:"varint,1,rep,packed,name=data_ids,json=dataIds,proto3" json:"data_ids,omitempty"`
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
}

func (x *ReqCleanTagBinding) Reset() {
	*x = ReqCleanTagBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqCleanTagBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqCleanTagBinding) ProtoMessage() {}

func (x *ReqCleanTagBinding) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqCleanTagBinding.ProtoReflect.Descriptor instead.
func (*ReqCleanTagBinding) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{26}
}

func (x *ReqCleanTagBinding) GetDataIds() []uint64 {
	if x != nil {
		return x.DataIds
	}
	return nil
}

func (x *ReqCleanTagBinding) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

type ReqApprovedDataTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataId   uint64        `protobuf:"varint,1,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	UserId   uint64        `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 语言类型 en zh
	Language string `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *ReqApprovedDataTag) Reset() {
	*x = ReqApprovedDataTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqApprovedDataTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqApprovedDataTag) ProtoMessage() {}

func (x *ReqApprovedDataTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqApprovedDataTag.ProtoReflect.Descriptor instead.
func (*ReqApprovedDataTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{27}
}

func (x *ReqApprovedDataTag) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *ReqApprovedDataTag) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqApprovedDataTag) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReqApprovedDataTag) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type ReqGetDataBoundByTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagIds   []uint64      `protobuf:"varint,1,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 标签名称（标签名称和标签类型必须共存，名称和tag_id 取交集）
	TagNames []string `protobuf:"bytes,3,rep,name=tag_names,json=tagNames,proto3" json:"tag_names,omitempty"`
	// 标签类型
	TaggableTypes []TaggableType `protobuf:"varint,4,rep,packed,name=taggable_types,json=taggableTypes,proto3,enum=tanlive.tag.TaggableType" json:"taggable_types,omitempty"`
	Offset        uint32         `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	// 如果limit= 0 则是查询查询全量, 最大 200
	Limit uint32 `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	// 标签创建类型（多语言选项或注册用） 1 系统 ；2 自创，默认0
	Type TagCreateType `protobuf:"varint,7,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送创建标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认0
	NotifyType TagCreateType `protobuf:"varint,8,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	// 是否多语言查询,为true时 会匹配出tag_ids的多语言数据
	Multilingual bool `protobuf:"varint,9,opt,name=multilingual,proto3" json:"multilingual,omitempty"`
	// 是否对传的标签ID 只返回绑定了所有tag_ids的data_ids
	TagIdIntersection bool               `protobuf:"varint,10,opt,name=tag_id_intersection,json=tagIdIntersection,proto3" json:"tag_id_intersection,omitempty"`
	ActionType        TaggableActionType `protobuf:"varint,11,opt,name=action_type,json=actionType,proto3,enum=tanlive.tag.TaggableActionType" json:"action_type,omitempty"`
}

func (x *ReqGetDataBoundByTag) Reset() {
	*x = ReqGetDataBoundByTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetDataBoundByTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetDataBoundByTag) ProtoMessage() {}

func (x *ReqGetDataBoundByTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetDataBoundByTag.ProtoReflect.Descriptor instead.
func (*ReqGetDataBoundByTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{28}
}

func (x *ReqGetDataBoundByTag) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqGetDataBoundByTag) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqGetDataBoundByTag) GetTagNames() []string {
	if x != nil {
		return x.TagNames
	}
	return nil
}

func (x *ReqGetDataBoundByTag) GetTaggableTypes() []TaggableType {
	if x != nil {
		return x.TaggableTypes
	}
	return nil
}

func (x *ReqGetDataBoundByTag) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ReqGetDataBoundByTag) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ReqGetDataBoundByTag) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqGetDataBoundByTag) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqGetDataBoundByTag) GetMultilingual() bool {
	if x != nil {
		return x.Multilingual
	}
	return false
}

func (x *ReqGetDataBoundByTag) GetTagIdIntersection() bool {
	if x != nil {
		return x.TagIdIntersection
	}
	return false
}

func (x *ReqGetDataBoundByTag) GetActionType() TaggableActionType {
	if x != nil {
		return x.ActionType
	}
	return TaggableActionType_TAGGABLE_ACTION_TYPE_UNSPECIFIED
}

type RspGetDataBoundByTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataIds    []uint64 `protobuf:"varint,1,rep,packed,name=data_ids,json=dataIds,proto3" json:"data_ids,omitempty"`
	TotalCount uint32   `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *RspGetDataBoundByTag) Reset() {
	*x = RspGetDataBoundByTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetDataBoundByTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetDataBoundByTag) ProtoMessage() {}

func (x *RspGetDataBoundByTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetDataBoundByTag.ProtoReflect.Descriptor instead.
func (*RspGetDataBoundByTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{29}
}

func (x *RspGetDataBoundByTag) GetDataIds() []uint64 {
	if x != nil {
		return x.DataIds
	}
	return nil
}

func (x *RspGetDataBoundByTag) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ReqGetTagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagIds []uint64 `protobuf:"varint,1,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
}

func (x *ReqGetTagInfo) Reset() {
	*x = ReqGetTagInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTagInfo) ProtoMessage() {}

func (x *ReqGetTagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTagInfo.ProtoReflect.Descriptor instead.
func (*ReqGetTagInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{30}
}

func (x *ReqGetTagInfo) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

type RspGetTagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tags []*ComTag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *RspGetTagInfo) Reset() {
	*x = RspGetTagInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTagInfo) ProtoMessage() {}

func (x *RspGetTagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTagInfo.ProtoReflect.Descriptor instead.
func (*RspGetTagInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{31}
}

func (x *RspGetTagInfo) GetTags() []*ComTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

type ReqGetTagInfoByLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagIds      []uint64 `protobuf:"varint,1,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	Language    string   `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	WithEditing bool     `protobuf:"varint,3,opt,name=with_editing,json=withEditing,proto3" json:"with_editing,omitempty"` // true 则会在显示自定义标签时忽略language
}

func (x *ReqGetTagInfoByLanguage) Reset() {
	*x = ReqGetTagInfoByLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetTagInfoByLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetTagInfoByLanguage) ProtoMessage() {}

func (x *ReqGetTagInfoByLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetTagInfoByLanguage.ProtoReflect.Descriptor instead.
func (*ReqGetTagInfoByLanguage) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{32}
}

func (x *ReqGetTagInfoByLanguage) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqGetTagInfoByLanguage) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqGetTagInfoByLanguage) GetWithEditing() bool {
	if x != nil {
		return x.WithEditing
	}
	return false
}

type RspGetTagInfoByLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key 原tagID  value 多语言标签信息
	Tags map[uint64]*ComTag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RspGetTagInfoByLanguage) Reset() {
	*x = RspGetTagInfoByLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetTagInfoByLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetTagInfoByLanguage) ProtoMessage() {}

func (x *RspGetTagInfoByLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetTagInfoByLanguage.ProtoReflect.Descriptor instead.
func (*RspGetTagInfoByLanguage) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{33}
}

func (x *RspGetTagInfoByLanguage) GetTags() map[uint64]*ComTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

type ReqSyncDataTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ugc id
	DataId uint64 `protobuf:"varint,1,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	// 同步数据更新人 userid
	UpdateBy uint64 `protobuf:"varint,2,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 同步的全量标签id
	TagIds   []uint64      `protobuf:"varint,3,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	DataType base.DataType `protobuf:"varint,4,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
}

func (x *ReqSyncDataTag) Reset() {
	*x = ReqSyncDataTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqSyncDataTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqSyncDataTag) ProtoMessage() {}

func (x *ReqSyncDataTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqSyncDataTag.ProtoReflect.Descriptor instead.
func (*ReqSyncDataTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{34}
}

func (x *ReqSyncDataTag) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *ReqSyncDataTag) GetUpdateBy() uint64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *ReqSyncDataTag) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqSyncDataTag) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

type ReqGetAllTagInfoByData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ugc id
	DataId uint64 `protobuf:"varint,1,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	// ugc type
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// union_data_ids 不为空时，会返回对应ugc绑定的所有指定类型的标签，标签类型为空时则返回全部标签，返回之前会取标签结果并集
	UnionDataIds []uint64 `protobuf:"varint,3,rep,packed,name=union_data_ids,json=unionDataIds,proto3" json:"union_data_ids,omitempty"`
	// 标签类型 如果为空则获取ugc绑定的所有标签信息
	TaggableTypes []TaggableType `protobuf:"varint,4,rep,packed,name=taggable_types,json=taggableTypes,proto3,enum=tanlive.tag.TaggableType" json:"taggable_types,omitempty"`
}

func (x *ReqGetAllTagInfoByData) Reset() {
	*x = ReqGetAllTagInfoByData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetAllTagInfoByData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetAllTagInfoByData) ProtoMessage() {}

func (x *ReqGetAllTagInfoByData) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetAllTagInfoByData.ProtoReflect.Descriptor instead.
func (*ReqGetAllTagInfoByData) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{35}
}

func (x *ReqGetAllTagInfoByData) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *ReqGetAllTagInfoByData) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqGetAllTagInfoByData) GetUnionDataIds() []uint64 {
	if x != nil {
		return x.UnionDataIds
	}
	return nil
}

func (x *ReqGetAllTagInfoByData) GetTaggableTypes() []TaggableType {
	if x != nil {
		return x.TaggableTypes
	}
	return nil
}

type RspGetAllTagInfoByData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tags []*ComTag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *RspGetAllTagInfoByData) Reset() {
	*x = RspGetAllTagInfoByData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetAllTagInfoByData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetAllTagInfoByData) ProtoMessage() {}

func (x *RspGetAllTagInfoByData) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetAllTagInfoByData.ProtoReflect.Descriptor instead.
func (*RspGetAllTagInfoByData) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{36}
}

func (x *RspGetAllTagInfoByData) GetTags() []*ComTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

type ReqGetDataWithTagByTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagIds []uint64 `protobuf:"varint,1,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// ugc type
	DataType base.DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
	// 标签类型，指定返回ugc绑定的标签类型
	TaggableTypes []TaggableType `protobuf:"varint,3,rep,packed,name=taggable_types,json=taggableTypes,proto3,enum=tanlive.tag.TaggableType" json:"taggable_types,omitempty"`
	// 是否多语言查询,true 会根据tagIds标签多语言数据匹配ugc；false 只会匹配与tagIds关联的ugc
	Multilingual bool `protobuf:"varint,4,opt,name=multilingual,proto3" json:"multilingual,omitempty"`
	// 指定返回ugc绑定的标签的语言类型
	Language string `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *ReqGetDataWithTagByTag) Reset() {
	*x = ReqGetDataWithTagByTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetDataWithTagByTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetDataWithTagByTag) ProtoMessage() {}

func (x *ReqGetDataWithTagByTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetDataWithTagByTag.ProtoReflect.Descriptor instead.
func (*ReqGetDataWithTagByTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{37}
}

func (x *ReqGetDataWithTagByTag) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqGetDataWithTagByTag) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

func (x *ReqGetDataWithTagByTag) GetTaggableTypes() []TaggableType {
	if x != nil {
		return x.TaggableTypes
	}
	return nil
}

func (x *ReqGetDataWithTagByTag) GetMultilingual() bool {
	if x != nil {
		return x.Multilingual
	}
	return false
}

func (x *ReqGetDataWithTagByTag) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type RspGetDataWithTagByTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataTags []*RspGetDataWithTagByTag_DataTag `protobuf:"bytes,1,rep,name=data_tags,json=dataTags,proto3" json:"data_tags,omitempty"`
}

func (x *RspGetDataWithTagByTag) Reset() {
	*x = RspGetDataWithTagByTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetDataWithTagByTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetDataWithTagByTag) ProtoMessage() {}

func (x *RspGetDataWithTagByTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetDataWithTagByTag.ProtoReflect.Descriptor instead.
func (*RspGetDataWithTagByTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{38}
}

func (x *RspGetDataWithTagByTag) GetDataTags() []*RspGetDataWithTagByTag_DataTag {
	if x != nil {
		return x.DataTags
	}
	return nil
}

type ReqGetDataByTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaggableType TaggableType    `protobuf:"varint,1,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	TagIds       []uint64        `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	Page         *base.Paginator `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	DataType     base.DataType   `protobuf:"varint,4,opt,name=data_type,json=dataType,proto3,enum=tanlive.base.DataType" json:"data_type,omitempty"`
}

func (x *ReqGetDataByTag) Reset() {
	*x = ReqGetDataByTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqGetDataByTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqGetDataByTag) ProtoMessage() {}

func (x *ReqGetDataByTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqGetDataByTag.ProtoReflect.Descriptor instead.
func (*ReqGetDataByTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{39}
}

func (x *ReqGetDataByTag) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqGetDataByTag) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqGetDataByTag) GetPage() *base.Paginator {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ReqGetDataByTag) GetDataType() base.DataType {
	if x != nil {
		return x.DataType
	}
	return base.DataType(0)
}

type RspGetDataByTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []uint64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *RspGetDataByTag) Reset() {
	*x = RspGetDataByTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetDataByTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetDataByTag) ProtoMessage() {}

func (x *RspGetDataByTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetDataByTag.ProtoReflect.Descriptor instead.
func (*RspGetDataByTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{40}
}

func (x *RspGetDataByTag) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ReqDescribeSystemTags_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签名称
	TagName string `protobuf:"bytes,1,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 标签索引类型
	TaggableType TaggableType `protobuf:"varint,2,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 标签id
	TagIds []uint64 `protobuf:"varint,3,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// 语言类型
	Language string `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创
	Type TagCreateType `protobuf:"varint,5,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（我来自标签时） 1 系统 ；2 自创
	NotifyType TagCreateType `protobuf:"varint,6,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
}

func (x *ReqDescribeSystemTags_Filter) Reset() {
	*x = ReqDescribeSystemTags_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSystemTags_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSystemTags_Filter) ProtoMessage() {}

func (x *ReqDescribeSystemTags_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSystemTags_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeSystemTags_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ReqDescribeSystemTags_Filter) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *ReqDescribeSystemTags_Filter) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqDescribeSystemTags_Filter) GetTagIds() []uint64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ReqDescribeSystemTags_Filter) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqDescribeSystemTags_Filter) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqDescribeSystemTags_Filter) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

type RspDescribeSystemTags_TagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	// 标签名称
	TagName string `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 语言类型
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	// 是否tmt
	IsTmt bool `protobuf:"varint,4,opt,name=is_tmt,json=isTmt,proto3" json:"is_tmt,omitempty"`
	// 引用数量
	CitationNum uint32 `protobuf:"varint,5,opt,name=citation_num,json=citationNum,proto3" json:"citation_num,omitempty"`
	// 标签权重
	Weight uint32 `protobuf:"varint,6,opt,name=weight,proto3" json:"weight,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创
	Type TagCreateType `protobuf:"varint,7,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创
	NotifyType     TagCreateType          `protobuf:"varint,8,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
	CreateBy       uint64                 `protobuf:"varint,9,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	CreateDate     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	LastUpdateBy   uint64                 `protobuf:"varint,11,opt,name=last_update_by,json=lastUpdateBy,proto3" json:"last_update_by,omitempty"`
	LastUpdateDate *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=last_update_date,json=lastUpdateDate,proto3" json:"last_update_date,omitempty"`
	// 标签索引名称
	IndexName string `protobuf:"bytes,13,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	// 索引id
	IndexId uint64 `protobuf:"varint,14,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
}

func (x *RspDescribeSystemTags_TagInfo) Reset() {
	*x = RspDescribeSystemTags_TagInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeSystemTags_TagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeSystemTags_TagInfo) ProtoMessage() {}

func (x *RspDescribeSystemTags_TagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeSystemTags_TagInfo.ProtoReflect.Descriptor instead.
func (*RspDescribeSystemTags_TagInfo) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{5, 0}
}

func (x *RspDescribeSystemTags_TagInfo) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

func (x *RspDescribeSystemTags_TagInfo) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *RspDescribeSystemTags_TagInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *RspDescribeSystemTags_TagInfo) GetIsTmt() bool {
	if x != nil {
		return x.IsTmt
	}
	return false
}

func (x *RspDescribeSystemTags_TagInfo) GetCitationNum() uint32 {
	if x != nil {
		return x.CitationNum
	}
	return 0
}

func (x *RspDescribeSystemTags_TagInfo) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *RspDescribeSystemTags_TagInfo) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *RspDescribeSystemTags_TagInfo) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *RspDescribeSystemTags_TagInfo) GetCreateBy() uint64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *RspDescribeSystemTags_TagInfo) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *RspDescribeSystemTags_TagInfo) GetLastUpdateBy() uint64 {
	if x != nil {
		return x.LastUpdateBy
	}
	return 0
}

func (x *RspDescribeSystemTags_TagInfo) GetLastUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateDate
	}
	return nil
}

func (x *RspDescribeSystemTags_TagInfo) GetIndexName() string {
	if x != nil {
		return x.IndexName
	}
	return ""
}

func (x *RspDescribeSystemTags_TagInfo) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

type ReqDescribeSystemTagsByIndex_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签名称
	TagName string `protobuf:"bytes,1,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 标签索引类型
	TaggableType TaggableType `protobuf:"varint,2,opt,name=taggable_type,json=taggableType,proto3,enum=tanlive.tag.TaggableType" json:"taggable_type,omitempty"`
	// 标签索引id
	IndexIds []uint64 `protobuf:"varint,3,rep,packed,name=index_ids,json=indexIds,proto3" json:"index_ids,omitempty"`
	// 语言类型
	Language string `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	// 标签类型（多语言选项或注册用） 1 系统 ；2 自创
	Type TagCreateType `protobuf:"varint,5,opt,name=type,proto3,enum=tanlive.tag.TagCreateType" json:"type,omitempty"`
	// 定向推送标签类型（我来自标签时） 1 系统 ；2 自创
	NotifyType TagCreateType `protobuf:"varint,6,opt,name=notify_type,json=notifyType,proto3,enum=tanlive.tag.TagCreateType" json:"notify_type,omitempty"`
}

func (x *ReqDescribeSystemTagsByIndex_Filter) Reset() {
	*x = ReqDescribeSystemTagsByIndex_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeSystemTagsByIndex_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeSystemTagsByIndex_Filter) ProtoMessage() {}

func (x *ReqDescribeSystemTagsByIndex_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeSystemTagsByIndex_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeSystemTagsByIndex_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetTaggableType() TaggableType {
	if x != nil {
		return x.TaggableType
	}
	return TaggableType_TAGGABLE_TYPE_UNSPECIFIED
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetIndexIds() []uint64 {
	if x != nil {
		return x.IndexIds
	}
	return nil
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetType() TagCreateType {
	if x != nil {
		return x.Type
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

func (x *ReqDescribeSystemTagsByIndex_Filter) GetNotifyType() TagCreateType {
	if x != nil {
		return x.NotifyType
	}
	return TagCreateType_TAG_CREATE_TYPE_UNSPECIFIED
}

type ReqDescribeTeamTagBindingInfos_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (x *ReqDescribeTeamTagBindingInfos_Filter) Reset() {
	*x = ReqDescribeTeamTagBindingInfos_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeTeamTagBindingInfos_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeTeamTagBindingInfos_Filter) ProtoMessage() {}

func (x *ReqDescribeTeamTagBindingInfos_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeTeamTagBindingInfos_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeTeamTagBindingInfos_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ReqDescribeTeamTagBindingInfos_Filter) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

type RspDescribeTeamTagBindingInfos_BindingObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 团队id
	TeamId uint64 `protobuf:"varint,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// 团队名称
	TeamName string `protobuf:"bytes,3,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
	// 团队简称
	TeamShortName string `protobuf:"bytes,4,opt,name=team_short_name,json=teamShortName,proto3" json:"team_short_name,omitempty"`
	// 创建时间
	CreateDate string `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 团队属性
	TeamNature string `protobuf:"bytes,6,opt,name=team_nature,json=teamNature,proto3" json:"team_nature,omitempty"`
	// 认证状态
	VerifyStatus string `protobuf:"bytes,7,opt,name=verify_status,json=verifyStatus,proto3" json:"verify_status,omitempty"`
	// 团队持有人id
	HolderId uint64 `protobuf:"varint,8,opt,name=holder_id,json=holderId,proto3" json:"holder_id,omitempty"`
	// 团队持有人
	HolderName string `protobuf:"bytes,9,opt,name=holder_name,json=holderName,proto3" json:"holder_name,omitempty"`
	// 团队类型
	TeamTypes []string `protobuf:"bytes,10,rep,name=team_types,json=teamTypes,proto3" json:"team_types,omitempty"`
	// 贡献者
	Contributors []string `protobuf:"bytes,11,rep,name=contributors,proto3" json:"contributors,omitempty"`
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) Reset() {
	*x = RspDescribeTeamTagBindingInfos_BindingObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeTeamTagBindingInfos_BindingObject) ProtoMessage() {}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeTeamTagBindingInfos_BindingObject.ProtoReflect.Descriptor instead.
func (*RspDescribeTeamTagBindingInfos_BindingObject) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamShortName() string {
	if x != nil {
		return x.TeamShortName
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamNature() string {
	if x != nil {
		return x.TeamNature
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetVerifyStatus() string {
	if x != nil {
		return x.VerifyStatus
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetHolderId() uint64 {
	if x != nil {
		return x.HolderId
	}
	return 0
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetHolderName() string {
	if x != nil {
		return x.HolderName
	}
	return ""
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetTeamTypes() []string {
	if x != nil {
		return x.TeamTypes
	}
	return nil
}

func (x *RspDescribeTeamTagBindingInfos_BindingObject) GetContributors() []string {
	if x != nil {
		return x.Contributors
	}
	return nil
}

type ReqDescribeResourceTagBindingInfos_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (x *ReqDescribeResourceTagBindingInfos_Filter) Reset() {
	*x = ReqDescribeResourceTagBindingInfos_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeResourceTagBindingInfos_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeResourceTagBindingInfos_Filter) ProtoMessage() {}

func (x *ReqDescribeResourceTagBindingInfos_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeResourceTagBindingInfos_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeResourceTagBindingInfos_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ReqDescribeResourceTagBindingInfos_Filter) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

type RspDescribeResourceTagBindingInfos_BindingObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 资源标题
	ResName string `protobuf:"bytes,2,opt,name=res_name,json=resName,proto3" json:"res_name,omitempty"`
	// 资源简述
	ResIntroduction string `protobuf:"bytes,3,opt,name=res_introduction,json=resIntroduction,proto3" json:"res_introduction,omitempty"`
	// 资源类型
	ResTypes []string `protobuf:"bytes,4,rep,name=res_types,json=resTypes,proto3" json:"res_types,omitempty"`
	// 创建时间
	CreateDate string `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 主办方
	OriginatorName []string `protobuf:"bytes,6,rep,name=originator_name,json=originatorName,proto3" json:"originator_name,omitempty"`
	// 资源id
	ResId uint64 `protobuf:"varint,7,opt,name=res_id,json=resId,proto3" json:"res_id,omitempty"`
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) Reset() {
	*x = RspDescribeResourceTagBindingInfos_BindingObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeResourceTagBindingInfos_BindingObject) ProtoMessage() {}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeResourceTagBindingInfos_BindingObject.ProtoReflect.Descriptor instead.
func (*RspDescribeResourceTagBindingInfos_BindingObject) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetResName() string {
	if x != nil {
		return x.ResName
	}
	return ""
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetResIntroduction() string {
	if x != nil {
		return x.ResIntroduction
	}
	return ""
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetResTypes() []string {
	if x != nil {
		return x.ResTypes
	}
	return nil
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetOriginatorName() []string {
	if x != nil {
		return x.OriginatorName
	}
	return nil
}

func (x *RspDescribeResourceTagBindingInfos_BindingObject) GetResId() uint64 {
	if x != nil {
		return x.ResId
	}
	return 0
}

type ReqDescribeAtlasTagBindingInfos_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (x *ReqDescribeAtlasTagBindingInfos_Filter) Reset() {
	*x = ReqDescribeAtlasTagBindingInfos_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeAtlasTagBindingInfos_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeAtlasTagBindingInfos_Filter) ProtoMessage() {}

func (x *ReqDescribeAtlasTagBindingInfos_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeAtlasTagBindingInfos_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeAtlasTagBindingInfos_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ReqDescribeAtlasTagBindingInfos_Filter) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

type RspDescribeAtlasTagBindingInfos_BindingObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标题
	AtlasName string `protobuf:"bytes,2,opt,name=atlas_name,json=atlasName,proto3" json:"atlas_name,omitempty"`
	// 简述
	AtlasIntroduction string `protobuf:"bytes,3,opt,name=atlas_introduction,json=atlasIntroduction,proto3" json:"atlas_introduction,omitempty"`
	// 图谱类型
	AtlasTypes []string `protobuf:"bytes,4,rep,name=atlas_types,json=atlasTypes,proto3" json:"atlas_types,omitempty"`
	// 创建时间
	CreateDate string `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 发布方
	PubName []string `protobuf:"bytes,6,rep,name=pub_name,json=pubName,proto3" json:"pub_name,omitempty"`
	// 图谱id
	AtlasId uint64 `protobuf:"varint,7,opt,name=atlas_id,json=atlasId,proto3" json:"atlas_id,omitempty"`
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) Reset() {
	*x = RspDescribeAtlasTagBindingInfos_BindingObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeAtlasTagBindingInfos_BindingObject) ProtoMessage() {}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeAtlasTagBindingInfos_BindingObject.ProtoReflect.Descriptor instead.
func (*RspDescribeAtlasTagBindingInfos_BindingObject) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{13, 0}
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetAtlasName() string {
	if x != nil {
		return x.AtlasName
	}
	return ""
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetAtlasIntroduction() string {
	if x != nil {
		return x.AtlasIntroduction
	}
	return ""
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetAtlasTypes() []string {
	if x != nil {
		return x.AtlasTypes
	}
	return nil
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetPubName() []string {
	if x != nil {
		return x.PubName
	}
	return nil
}

func (x *RspDescribeAtlasTagBindingInfos_BindingObject) GetAtlasId() uint64 {
	if x != nil {
		return x.AtlasId
	}
	return 0
}

type ReqDescribeProductTagBindingInfos_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId uint64 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (x *ReqDescribeProductTagBindingInfos_Filter) Reset() {
	*x = ReqDescribeProductTagBindingInfos_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqDescribeProductTagBindingInfos_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqDescribeProductTagBindingInfos_Filter) ProtoMessage() {}

func (x *ReqDescribeProductTagBindingInfos_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqDescribeProductTagBindingInfos_Filter.ProtoReflect.Descriptor instead.
func (*ReqDescribeProductTagBindingInfos_Filter) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ReqDescribeProductTagBindingInfos_Filter) GetTagId() uint64 {
	if x != nil {
		return x.TagId
	}
	return 0
}

type RspDescribeProductTagBindingInfos_BindingObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 产品名称
	ProductName string `protobuf:"bytes,2,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"`
	// 一句话简介
	BriefIntro string `protobuf:"bytes,3,opt,name=brief_intro,json=briefIntro,proto3" json:"brief_intro,omitempty"`
	// 产品类型
	ProductTypes []string `protobuf:"bytes,4,rep,name=product_types,json=productTypes,proto3" json:"product_types,omitempty"`
	// 团队简称
	TeamShortName string `protobuf:"bytes,5,opt,name=team_short_name,json=teamShortName,proto3" json:"team_short_name,omitempty"`
	// 创建时间
	CreateDate string `protobuf:"bytes,6,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 产品id
	ProductId uint64 `protobuf:"varint,7,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) Reset() {
	*x = RspDescribeProductTagBindingInfos_BindingObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspDescribeProductTagBindingInfos_BindingObject) ProtoMessage() {}

func (x *RspDescribeProductTagBindingInfos_BindingObject) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspDescribeProductTagBindingInfos_BindingObject.ProtoReflect.Descriptor instead.
func (*RspDescribeProductTagBindingInfos_BindingObject) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{15, 0}
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetBriefIntro() string {
	if x != nil {
		return x.BriefIntro
	}
	return ""
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetProductTypes() []string {
	if x != nil {
		return x.ProductTypes
	}
	return nil
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetTeamShortName() string {
	if x != nil {
		return x.TeamShortName
	}
	return ""
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *RspDescribeProductTagBindingInfos_BindingObject) GetProductId() uint64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

type ReqBatchImportSystemTag_TagIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引名称
	IndexName string `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	// 标签名称
	TagName string `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
}

func (x *ReqBatchImportSystemTag_TagIndex) Reset() {
	*x = ReqBatchImportSystemTag_TagIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBatchImportSystemTag_TagIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBatchImportSystemTag_TagIndex) ProtoMessage() {}

func (x *ReqBatchImportSystemTag_TagIndex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBatchImportSystemTag_TagIndex.ProtoReflect.Descriptor instead.
func (*ReqBatchImportSystemTag_TagIndex) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ReqBatchImportSystemTag_TagIndex) GetIndexName() string {
	if x != nil {
		return x.IndexName
	}
	return ""
}

func (x *ReqBatchImportSystemTag_TagIndex) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

type RspGetSystemTags_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 直接显示的标签id,is_direct_display_id为false则默认为0
	DirectDisplayId uint64 `protobuf:"varint,6,opt,name=direct_display_id,json=directDisplayId,proto3" json:"direct_display_id,omitempty"`
}

func (x *RspGetSystemTags_Tag) Reset() {
	*x = RspGetSystemTags_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetSystemTags_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetSystemTags_Tag) ProtoMessage() {}

func (x *RspGetSystemTags_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetSystemTags_Tag.ProtoReflect.Descriptor instead.
func (*RspGetSystemTags_Tag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *RspGetSystemTags_Tag) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RspGetSystemTags_Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RspGetSystemTags_Tag) GetDirectDisplayId() uint64 {
	if x != nil {
		return x.DirectDisplayId
	}
	return 0
}

type RspGetDataWithTagByTag_DataTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ugc id
	DataId uint64 `protobuf:"varint,1,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	// tag_id
	Tags []*ComTag `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *RspGetDataWithTagByTag_DataTag) Reset() {
	*x = RspGetDataWithTagByTag_DataTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_tag_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspGetDataWithTagByTag_DataTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspGetDataWithTagByTag_DataTag) ProtoMessage() {}

func (x *RspGetDataWithTagByTag_DataTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_tag_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspGetDataWithTagByTag_DataTag.ProtoReflect.Descriptor instead.
func (*RspGetDataWithTagByTag_DataTag) Descriptor() ([]byte, []int) {
	return file_tanlive_tag_service_proto_rawDescGZIP(), []int{38, 0}
}

func (x *RspGetDataWithTagByTag_DataTag) GetDataId() uint64 {
	if x != nil {
		return x.DataId
	}
	return 0
}

func (x *RspGetDataWithTagByTag_DataTag) GetTags() []*ComTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

var File_tanlive_tag_service_proto protoreflect.FileDescriptor

var file_tanlive_tag_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65,
	0x2f, 0x75, 0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x02, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x15, 0x0a, 0x06,
	0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61,
	0x67, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x74,
	0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74,
	0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0xf7, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x45, 0x64, 0x69, 0x74, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x27, 0x0a, 0x06, 0x7a, 0x68, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x52, 0x05, 0x7a, 0x68, 0x54, 0x61, 0x67, 0x12, 0x27, 0x0a, 0x06, 0x65, 0x6e, 0x5f,
	0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x05, 0x65, 0x6e, 0x54,
	0x61, 0x67, 0x12, 0x3e, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xe8, 0x01, 0x0a, 0x12,
	0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54,
	0x61, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x61, 0x67, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x49, 0x64, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67,
	0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x82, 0x02, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x4d, 0x65,
	0x72, 0x67, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x0d, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x73,
	0x12, 0x27, 0x0a, 0x06, 0x7a, 0x68, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x52, 0x05, 0x7a, 0x68, 0x54, 0x61, 0x67, 0x12, 0x27, 0x0a, 0x06, 0x65, 0x6e, 0x5f,
	0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x05, 0x65, 0x6e, 0x54,
	0x61, 0x67, 0x12, 0x3e, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xc2, 0x03, 0x0a, 0x15,
	0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x30, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x41, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x85, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e,
	0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x96, 0x05, 0x0a, 0x15, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x43, 0x0a, 0x07, 0x74, 0x61,
	0x67, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x2e,
	0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x74, 0x61, 0x67, 0x53, 0x65, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x1a, 0x96, 0x04, 0x0a, 0x07, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06,
	0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61,
	0x67, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73,
	0x5f, 0x74, 0x6d, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x54, 0x6d,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2e, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6c, 0x61, 0x73,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x22, 0xd4, 0x03, 0x0a, 0x1c, 0x52, 0x65,
	0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54,
	0x61, 0x67, 0x73, 0x42, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x48, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x42, 0x79, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x30, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x1a, 0x89, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x74, 0x61,
	0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x61,
	0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x6f, 0x0a, 0x1c, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x42, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x2e, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x54, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x06, 0x74, 0x61, 0x67, 0x53, 0x65, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xbb, 0x01, 0x0a, 0x1e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x4a, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d,
	0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x1f,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x22,
	0x8d, 0x04, 0x0a, 0x1e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54,
	0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x12, 0x62, 0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe5, 0x02, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x0f, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x65, 0x61, 0x6d, 0x53, 0x68,
	0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x61, 0x6d,
	0x5f, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74,
	0x65, 0x61, 0x6d, 0x4e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x65, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x22,
	0xc3, 0x01, 0x0a, 0x22, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x4e, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74,
	0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x1a, 0x1f, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x15,
	0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x74, 0x61, 0x67, 0x49, 0x64, 0x22, 0x93, 0x03, 0x0a, 0x22, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x66, 0x0a, 0x0f,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe3, 0x01, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65,
	0x73, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x72, 0x65, 0x73, 0x49, 0x64, 0x22, 0xbd, 0x01, 0x0a, 0x1f,
	0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73,
	0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x4b, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x1f, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x22, 0x8f, 0x03, 0x0a, 0x1f,
	0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73,
	0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x63, 0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe5, 0x01, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x6c,
	0x61, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f,
	0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x74, 0x6c, 0x61,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x75, 0x62, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x49, 0x64, 0x22, 0xc1, 0x01,
	0x0a, 0x21, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x4d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x1a, 0x1f, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49,
	0x64, 0x22, 0x9e, 0x03, 0x0a, 0x21, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x65, 0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52,
	0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a,
	0xf0, 0x01, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x69, 0x65, 0x66, 0x5f, 0x69, 0x6e,
	0x74, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x72, 0x69, 0x65, 0x66,
	0x49, 0x6e, 0x74, 0x72, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x65,
	0x61, 0x6d, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x65, 0x61, 0x6d, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x22, 0x8b, 0x03, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x4a,
	0x0a, 0x09, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x52, 0x08, 0x74, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61,
	0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67,
	0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x73, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x1a, 0x44, 0x0a, 0x08, 0x54, 0x61,
	0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xf9, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x30, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x69,
	0x73, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x17,
	0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x77,
	0x69, 0x74, 0x68, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x22, 0xa5, 0x01, 0x0a,
	0x10, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67,
	0x73, 0x12, 0x3a, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67,
	0x73, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x06, 0x74, 0x61, 0x67, 0x53, 0x65, 0x74, 0x1a, 0x55, 0x0a,
	0x03, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x10, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x22, 0x2b, 0x0a, 0x10, 0x52, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x22, 0xc9,
	0x02, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x67, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x67, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xcd, 0x01, 0x0a, 0x13, 0x52,
	0x65, 0x71, 0x41, 0x75, 0x74, 0x6f, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x49, 0x54,
	0x61, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x09, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43, 0x0a,
	0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x75, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x17, 0x52,
	0x65, 0x71, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x03, 0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x03, 0x6f, 0x6c, 0x64, 0x12, 0x1e, 0x0a, 0x03, 0x6e, 0x65, 0x77, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x52, 0x03, 0x6e, 0x65, 0x77, 0x12, 0x41, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x10, 0x52, 0x65,
	0x71, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x19,
	0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40,
	0x0a, 0x0e, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0xba, 0x01, 0x0a,
	0x10, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x4e, 0x0a, 0x0b, 0x74, 0x61, 0x67, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x74, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x1a, 0x56, 0x0a, 0x0f, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x64, 0x0a, 0x12, 0x52, 0x65, 0x71,
	0x43, 0x6c, 0x65, 0x61, 0x6e, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x97, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x61, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0xf4, 0x03, 0x0a, 0x14, 0x52, 0x65,
	0x71, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x54,
	0x61, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x40, 0x0a,
	0x0e, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x2e, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a,
	0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67,
	0x2e, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x12, 0x2e,
	0x0a, 0x13, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x74, 0x61, 0x67,
	0x49, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40,
	0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61,
	0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x52, 0x0a, 0x14, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x6f,
	0x75, 0x6e, 0x64, 0x42, 0x79, 0x54, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61,
	0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x28, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x22, 0x38,
	0x0a, 0x0d, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x27, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x54,
	0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x71, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x47,
	0x65, 0x74, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x77, 0x69, 0x74, 0x68, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xab, 0x01, 0x0a, 0x17,
	0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x61, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x1a, 0x4c, 0x0a, 0x09, 0x54,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x94, 0x01, 0x0a, 0x0e, 0x52, 0x65,
	0x71, 0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x54, 0x61, 0x67, 0x12, 0x17, 0x0a, 0x07,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x64,
	0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xce, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x61,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x64, 0x61,
	0x74, 0x61, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x6e, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x0c, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x12,
	0x40, 0x0a, 0x0e, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x22, 0x41, 0x0a, 0x16, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x61,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x22, 0xe8, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x42, 0x79, 0x54, 0x61, 0x67, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a,
	0x0e, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x22, 0x0a, 0x0c, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67,
	0x75, 0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22,
	0xaf, 0x01, 0x0a, 0x16, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x57, 0x69,
	0x74, 0x68, 0x54, 0x61, 0x67, 0x42, 0x79, 0x54, 0x61, 0x67, 0x12, 0x48, 0x0a, 0x09, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x42, 0x79, 0x54,
	0x61, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x61, 0x67, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x61, 0x67, 0x73, 0x1a, 0x4b, 0x0a, 0x07, 0x44, 0x61, 0x74, 0x61, 0x54, 0x61, 0x67, 0x12,
	0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x61, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x22, 0xf6, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x42, 0x79, 0x54, 0x61, 0x67, 0x12, 0x4c, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x54, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x0c, 0x74, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x04, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0c, 0x82, 0x88, 0x27, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x22, 0x23, 0x0a, 0x0f, 0x52, 0x73,
	0x70, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x54, 0x61, 0x67, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x03, 0x69, 0x64, 0x73, 0x32,
	0xab, 0x12, 0x0a, 0x0a, 0x54, 0x61, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4a,
	0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61,
	0x67, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54,
	0x61, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x46, 0x0a, 0x0d, 0x45, 0x64,
	0x69, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x1d, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x45, 0x64, 0x69,
	0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x4a, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4a,
	0x0a, 0x0f, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67,
	0x73, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61,
	0x67, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5c, 0x0a, 0x12, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73,
	0x12, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52,
	0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x54, 0x61, 0x67, 0x73, 0x1a, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74,
	0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x71, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x42, 0x79,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x29, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x42, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x1a, 0x29, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52,
	0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x54, 0x61, 0x67, 0x73, 0x42, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x77, 0x0a, 0x1b, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x2b, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x2b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x12, 0x83, 0x01, 0x0a, 0x1f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x2f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x2f, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x7a, 0x0a, 0x1c, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x2c, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x2c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x1e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x2e, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x2e, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x54, 0x0a, 0x14, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61,
	0x67, 0x12, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x4d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73,
	0x12, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52,
	0x65, 0x71, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x1a,
	0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73,
	0x70, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x12, 0x4d,
	0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12,
	0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65,
	0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x1a, 0x1d,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x4c, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x10, 0x41,
	0x75, 0x74, 0x6f, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x49, 0x54, 0x61, 0x67, 0x12,
	0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65,
	0x71, 0x41, 0x75, 0x74, 0x6f, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x49, 0x54, 0x61,
	0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x54, 0x0a, 0x14, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x4d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52,
	0x65, 0x71, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x1a,
	0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73,
	0x70, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x4a,
	0x0a, 0x0f, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x54, 0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4a, 0x0a, 0x0f, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x61, 0x67, 0x12, 0x1f, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x41,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x61, 0x67, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x59, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x54, 0x61, 0x67, 0x12, 0x21, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x54, 0x61, 0x67, 0x1a, 0x21,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70,
	0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x54, 0x61,
	0x67, 0x12, 0x44, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65,
	0x71, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x1a, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74,
	0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x62, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65,
	0x71, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x1a, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x53,
	0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x54, 0x61, 0x67, 0x12, 0x1b, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x53, 0x79, 0x6e, 0x63,
	0x44, 0x61, 0x74, 0x61, 0x54, 0x61, 0x67, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x5f, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x61,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x23, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x5f, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x57, 0x69, 0x74, 0x68, 0x54,
	0x61, 0x67, 0x42, 0x79, 0x54, 0x61, 0x67, 0x12, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x42, 0x79, 0x54, 0x61, 0x67, 0x1a, 0x23, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73, 0x70, 0x47, 0x65,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x42, 0x79, 0x54, 0x61,
	0x67, 0x12, 0x4a, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x54, 0x61,
	0x67, 0x12, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x54, 0x61, 0x67, 0x1a,
	0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x74, 0x61, 0x67, 0x2e, 0x52, 0x73,
	0x70, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x54, 0x61, 0x67, 0x42, 0x3d, 0x5a,
	0x3b, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65,
	0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_tag_service_proto_rawDescOnce sync.Once
	file_tanlive_tag_service_proto_rawDescData = file_tanlive_tag_service_proto_rawDesc
)

func file_tanlive_tag_service_proto_rawDescGZIP() []byte {
	file_tanlive_tag_service_proto_rawDescOnce.Do(func() {
		file_tanlive_tag_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_tag_service_proto_rawDescData)
	})
	return file_tanlive_tag_service_proto_rawDescData
}

var file_tanlive_tag_service_proto_msgTypes = make([]protoimpl.MessageInfo, 57)
var file_tanlive_tag_service_proto_goTypes = []interface{}{
	(*ReqUpdateSystemTag)(nil),                               // 0: tanlive.tag.ReqUpdateSystemTag
	(*ReqEditSystemTag)(nil),                                 // 1: tanlive.tag.ReqEditSystemTag
	(*ReqDeleteSystemTag)(nil),                               // 2: tanlive.tag.ReqDeleteSystemTag
	(*ReqMergeSystemTags)(nil),                               // 3: tanlive.tag.ReqMergeSystemTags
	(*ReqDescribeSystemTags)(nil),                            // 4: tanlive.tag.ReqDescribeSystemTags
	(*RspDescribeSystemTags)(nil),                            // 5: tanlive.tag.RspDescribeSystemTags
	(*ReqDescribeSystemTagsByIndex)(nil),                     // 6: tanlive.tag.ReqDescribeSystemTagsByIndex
	(*RspDescribeSystemTagsByIndex)(nil),                     // 7: tanlive.tag.RspDescribeSystemTagsByIndex
	(*ReqDescribeTeamTagBindingInfos)(nil),                   // 8: tanlive.tag.ReqDescribeTeamTagBindingInfos
	(*RspDescribeTeamTagBindingInfos)(nil),                   // 9: tanlive.tag.RspDescribeTeamTagBindingInfos
	(*ReqDescribeResourceTagBindingInfos)(nil),               // 10: tanlive.tag.ReqDescribeResourceTagBindingInfos
	(*RspDescribeResourceTagBindingInfos)(nil),               // 11: tanlive.tag.RspDescribeResourceTagBindingInfos
	(*ReqDescribeAtlasTagBindingInfos)(nil),                  // 12: tanlive.tag.ReqDescribeAtlasTagBindingInfos
	(*RspDescribeAtlasTagBindingInfos)(nil),                  // 13: tanlive.tag.RspDescribeAtlasTagBindingInfos
	(*ReqDescribeProductTagBindingInfos)(nil),                // 14: tanlive.tag.ReqDescribeProductTagBindingInfos
	(*RspDescribeProductTagBindingInfos)(nil),                // 15: tanlive.tag.RspDescribeProductTagBindingInfos
	(*ReqBatchImportSystemTag)(nil),                          // 16: tanlive.tag.ReqBatchImportSystemTag
	(*ReqGetSystemTags)(nil),                                 // 17: tanlive.tag.ReqGetSystemTags
	(*RspGetSystemTags)(nil),                                 // 18: tanlive.tag.RspGetSystemTags
	(*ReqCreateUserTag)(nil),                                 // 19: tanlive.tag.ReqCreateUserTag
	(*RspCreateUserTag)(nil),                                 // 20: tanlive.tag.RspCreateUserTag
	(*ReqCreateTagBinding)(nil),                              // 21: tanlive.tag.ReqCreateTagBinding
	(*ReqAutoBindingAITag)(nil),                              // 22: tanlive.tag.ReqAutoBindingAITag
	(*ReqBindingTagChangeData)(nil),                          // 23: tanlive.tag.ReqBindingTagChangeData
	(*ReqGetTagBinding)(nil),                                 // 24: tanlive.tag.ReqGetTagBinding
	(*RspGetTagBinding)(nil),                                 // 25: tanlive.tag.RspGetTagBinding
	(*ReqCleanTagBinding)(nil),                               // 26: tanlive.tag.ReqCleanTagBinding
	(*ReqApprovedDataTag)(nil),                               // 27: tanlive.tag.ReqApprovedDataTag
	(*ReqGetDataBoundByTag)(nil),                             // 28: tanlive.tag.ReqGetDataBoundByTag
	(*RspGetDataBoundByTag)(nil),                             // 29: tanlive.tag.RspGetDataBoundByTag
	(*ReqGetTagInfo)(nil),                                    // 30: tanlive.tag.ReqGetTagInfo
	(*RspGetTagInfo)(nil),                                    // 31: tanlive.tag.RspGetTagInfo
	(*ReqGetTagInfoByLanguage)(nil),                          // 32: tanlive.tag.ReqGetTagInfoByLanguage
	(*RspGetTagInfoByLanguage)(nil),                          // 33: tanlive.tag.RspGetTagInfoByLanguage
	(*ReqSyncDataTag)(nil),                                   // 34: tanlive.tag.ReqSyncDataTag
	(*ReqGetAllTagInfoByData)(nil),                           // 35: tanlive.tag.ReqGetAllTagInfoByData
	(*RspGetAllTagInfoByData)(nil),                           // 36: tanlive.tag.RspGetAllTagInfoByData
	(*ReqGetDataWithTagByTag)(nil),                           // 37: tanlive.tag.ReqGetDataWithTagByTag
	(*RspGetDataWithTagByTag)(nil),                           // 38: tanlive.tag.RspGetDataWithTagByTag
	(*ReqGetDataByTag)(nil),                                  // 39: tanlive.tag.ReqGetDataByTag
	(*RspGetDataByTag)(nil),                                  // 40: tanlive.tag.RspGetDataByTag
	(*ReqDescribeSystemTags_Filter)(nil),                     // 41: tanlive.tag.ReqDescribeSystemTags.Filter
	(*RspDescribeSystemTags_TagInfo)(nil),                    // 42: tanlive.tag.RspDescribeSystemTags.TagInfo
	(*ReqDescribeSystemTagsByIndex_Filter)(nil),              // 43: tanlive.tag.ReqDescribeSystemTagsByIndex.Filter
	(*ReqDescribeTeamTagBindingInfos_Filter)(nil),            // 44: tanlive.tag.ReqDescribeTeamTagBindingInfos.Filter
	(*RspDescribeTeamTagBindingInfos_BindingObject)(nil),     // 45: tanlive.tag.RspDescribeTeamTagBindingInfos.BindingObject
	(*ReqDescribeResourceTagBindingInfos_Filter)(nil),        // 46: tanlive.tag.ReqDescribeResourceTagBindingInfos.Filter
	(*RspDescribeResourceTagBindingInfos_BindingObject)(nil), // 47: tanlive.tag.RspDescribeResourceTagBindingInfos.BindingObject
	(*ReqDescribeAtlasTagBindingInfos_Filter)(nil),           // 48: tanlive.tag.ReqDescribeAtlasTagBindingInfos.Filter
	(*RspDescribeAtlasTagBindingInfos_BindingObject)(nil),    // 49: tanlive.tag.RspDescribeAtlasTagBindingInfos.BindingObject
	(*ReqDescribeProductTagBindingInfos_Filter)(nil),         // 50: tanlive.tag.ReqDescribeProductTagBindingInfos.Filter
	(*RspDescribeProductTagBindingInfos_BindingObject)(nil),  // 51: tanlive.tag.RspDescribeProductTagBindingInfos.BindingObject
	(*ReqBatchImportSystemTag_TagIndex)(nil),                 // 52: tanlive.tag.ReqBatchImportSystemTag.TagIndex
	(*RspGetSystemTags_Tag)(nil),                             // 53: tanlive.tag.RspGetSystemTags.Tag
	nil,                                                      // 54: tanlive.tag.RspGetTagBinding.TagBindingEntry
	nil,                                                      // 55: tanlive.tag.RspGetTagInfoByLanguage.TagsEntry
	(*RspGetDataWithTagByTag_DataTag)(nil),                   // 56: tanlive.tag.RspGetDataWithTagByTag.DataTag
	(TagCreateType)(0),                                       // 57: tanlive.tag.TagCreateType
	(TaggableType)(0),                                        // 58: tanlive.tag.TaggableType
	(*Tag)(nil),                                              // 59: tanlive.tag.Tag
	(TagActionType)(0),                                       // 60: tanlive.tag.TagActionType
	(*base.OrderBy)(nil),                                     // 61: tanlive.base.OrderBy
	(*TagIndex)(nil),                                         // 62: tanlive.tag.TagIndex
	(*CreateTag)(nil),                                        // 63: tanlive.tag.CreateTag
	(*DataInfo)(nil),                                         // 64: tanlive.tag.DataInfo
	(TaggableActionType)(0),                                  // 65: tanlive.tag.TaggableActionType
	(*LabelInfo)(nil),                                        // 66: tanlive.tag.LabelInfo
	(base.DataType)(0),                                       // 67: tanlive.base.DataType
	(*ComTag)(nil),                                           // 68: tanlive.tag.ComTag
	(*base.Paginator)(nil),                                   // 69: tanlive.base.Paginator
	(*timestamppb.Timestamp)(nil),                            // 70: google.protobuf.Timestamp
	(*TagBinding)(nil),                                       // 71: tanlive.tag.TagBinding
	(*emptypb.Empty)(nil),                                    // 72: google.protobuf.Empty
}
var file_tanlive_tag_service_proto_depIdxs = []int32{
	57,  // 0: tanlive.tag.ReqUpdateSystemTag.type:type_name -> tanlive.tag.TagCreateType
	58,  // 1: tanlive.tag.ReqUpdateSystemTag.taggable_type:type_name -> tanlive.tag.TaggableType
	57,  // 2: tanlive.tag.ReqUpdateSystemTag.notify_type:type_name -> tanlive.tag.TagCreateType
	59,  // 3: tanlive.tag.ReqEditSystemTag.zh_tag:type_name -> tanlive.tag.Tag
	59,  // 4: tanlive.tag.ReqEditSystemTag.en_tag:type_name -> tanlive.tag.Tag
	58,  // 5: tanlive.tag.ReqEditSystemTag.taggable_type:type_name -> tanlive.tag.TaggableType
	58,  // 6: tanlive.tag.ReqDeleteSystemTag.taggable_type:type_name -> tanlive.tag.TaggableType
	60,  // 7: tanlive.tag.ReqDeleteSystemTag.action_type:type_name -> tanlive.tag.TagActionType
	59,  // 8: tanlive.tag.ReqMergeSystemTags.zh_tag:type_name -> tanlive.tag.Tag
	59,  // 9: tanlive.tag.ReqMergeSystemTags.en_tag:type_name -> tanlive.tag.Tag
	58,  // 10: tanlive.tag.ReqMergeSystemTags.taggable_type:type_name -> tanlive.tag.TaggableType
	61,  // 11: tanlive.tag.ReqDescribeSystemTags.order_by:type_name -> tanlive.base.OrderBy
	41,  // 12: tanlive.tag.ReqDescribeSystemTags.filter:type_name -> tanlive.tag.ReqDescribeSystemTags.Filter
	42,  // 13: tanlive.tag.RspDescribeSystemTags.tag_set:type_name -> tanlive.tag.RspDescribeSystemTags.TagInfo
	43,  // 14: tanlive.tag.ReqDescribeSystemTagsByIndex.filter:type_name -> tanlive.tag.ReqDescribeSystemTagsByIndex.Filter
	61,  // 15: tanlive.tag.ReqDescribeSystemTagsByIndex.order_by:type_name -> tanlive.base.OrderBy
	62,  // 16: tanlive.tag.RspDescribeSystemTagsByIndex.tag_set:type_name -> tanlive.tag.TagIndex
	44,  // 17: tanlive.tag.ReqDescribeTeamTagBindingInfos.filter:type_name -> tanlive.tag.ReqDescribeTeamTagBindingInfos.Filter
	45,  // 18: tanlive.tag.RspDescribeTeamTagBindingInfos.binding_objects:type_name -> tanlive.tag.RspDescribeTeamTagBindingInfos.BindingObject
	46,  // 19: tanlive.tag.ReqDescribeResourceTagBindingInfos.filter:type_name -> tanlive.tag.ReqDescribeResourceTagBindingInfos.Filter
	47,  // 20: tanlive.tag.RspDescribeResourceTagBindingInfos.binding_objects:type_name -> tanlive.tag.RspDescribeResourceTagBindingInfos.BindingObject
	48,  // 21: tanlive.tag.ReqDescribeAtlasTagBindingInfos.filter:type_name -> tanlive.tag.ReqDescribeAtlasTagBindingInfos.Filter
	49,  // 22: tanlive.tag.RspDescribeAtlasTagBindingInfos.binding_objects:type_name -> tanlive.tag.RspDescribeAtlasTagBindingInfos.BindingObject
	50,  // 23: tanlive.tag.ReqDescribeProductTagBindingInfos.filter:type_name -> tanlive.tag.ReqDescribeProductTagBindingInfos.Filter
	51,  // 24: tanlive.tag.RspDescribeProductTagBindingInfos.binding_objects:type_name -> tanlive.tag.RspDescribeProductTagBindingInfos.BindingObject
	52,  // 25: tanlive.tag.ReqBatchImportSystemTag.tag_index:type_name -> tanlive.tag.ReqBatchImportSystemTag.TagIndex
	57,  // 26: tanlive.tag.ReqBatchImportSystemTag.type:type_name -> tanlive.tag.TagCreateType
	57,  // 27: tanlive.tag.ReqBatchImportSystemTag.notify_type:type_name -> tanlive.tag.TagCreateType
	58,  // 28: tanlive.tag.ReqBatchImportSystemTag.taggable_type:type_name -> tanlive.tag.TaggableType
	58,  // 29: tanlive.tag.ReqGetSystemTags.taggable_type:type_name -> tanlive.tag.TaggableType
	61,  // 30: tanlive.tag.ReqGetSystemTags.order_by:type_name -> tanlive.base.OrderBy
	57,  // 31: tanlive.tag.ReqGetSystemTags.notify_type:type_name -> tanlive.tag.TagCreateType
	53,  // 32: tanlive.tag.RspGetSystemTags.tag_set:type_name -> tanlive.tag.RspGetSystemTags.Tag
	63,  // 33: tanlive.tag.ReqCreateUserTag.tags:type_name -> tanlive.tag.CreateTag
	64,  // 34: tanlive.tag.ReqCreateTagBinding.data_info:type_name -> tanlive.tag.DataInfo
	58,  // 35: tanlive.tag.ReqCreateTagBinding.taggable_type:type_name -> tanlive.tag.TaggableType
	65,  // 36: tanlive.tag.ReqCreateTagBinding.action:type_name -> tanlive.tag.TaggableActionType
	64,  // 37: tanlive.tag.ReqAutoBindingAITag.data_info:type_name -> tanlive.tag.DataInfo
	66,  // 38: tanlive.tag.ReqAutoBindingAITag.label_info:type_name -> tanlive.tag.LabelInfo
	67,  // 39: tanlive.tag.ReqBindingTagChangeData.data_type:type_name -> tanlive.base.DataType
	67,  // 40: tanlive.tag.ReqGetTagBinding.data_type:type_name -> tanlive.base.DataType
	58,  // 41: tanlive.tag.ReqGetTagBinding.taggable_types:type_name -> tanlive.tag.TaggableType
	54,  // 42: tanlive.tag.RspGetTagBinding.tag_binding:type_name -> tanlive.tag.RspGetTagBinding.TagBindingEntry
	67,  // 43: tanlive.tag.ReqCleanTagBinding.data_type:type_name -> tanlive.base.DataType
	67,  // 44: tanlive.tag.ReqApprovedDataTag.data_type:type_name -> tanlive.base.DataType
	67,  // 45: tanlive.tag.ReqGetDataBoundByTag.data_type:type_name -> tanlive.base.DataType
	58,  // 46: tanlive.tag.ReqGetDataBoundByTag.taggable_types:type_name -> tanlive.tag.TaggableType
	57,  // 47: tanlive.tag.ReqGetDataBoundByTag.type:type_name -> tanlive.tag.TagCreateType
	57,  // 48: tanlive.tag.ReqGetDataBoundByTag.notify_type:type_name -> tanlive.tag.TagCreateType
	65,  // 49: tanlive.tag.ReqGetDataBoundByTag.action_type:type_name -> tanlive.tag.TaggableActionType
	68,  // 50: tanlive.tag.RspGetTagInfo.tags:type_name -> tanlive.tag.ComTag
	55,  // 51: tanlive.tag.RspGetTagInfoByLanguage.tags:type_name -> tanlive.tag.RspGetTagInfoByLanguage.TagsEntry
	67,  // 52: tanlive.tag.ReqSyncDataTag.data_type:type_name -> tanlive.base.DataType
	67,  // 53: tanlive.tag.ReqGetAllTagInfoByData.data_type:type_name -> tanlive.base.DataType
	58,  // 54: tanlive.tag.ReqGetAllTagInfoByData.taggable_types:type_name -> tanlive.tag.TaggableType
	68,  // 55: tanlive.tag.RspGetAllTagInfoByData.tags:type_name -> tanlive.tag.ComTag
	67,  // 56: tanlive.tag.ReqGetDataWithTagByTag.data_type:type_name -> tanlive.base.DataType
	58,  // 57: tanlive.tag.ReqGetDataWithTagByTag.taggable_types:type_name -> tanlive.tag.TaggableType
	56,  // 58: tanlive.tag.RspGetDataWithTagByTag.data_tags:type_name -> tanlive.tag.RspGetDataWithTagByTag.DataTag
	58,  // 59: tanlive.tag.ReqGetDataByTag.taggable_type:type_name -> tanlive.tag.TaggableType
	69,  // 60: tanlive.tag.ReqGetDataByTag.page:type_name -> tanlive.base.Paginator
	67,  // 61: tanlive.tag.ReqGetDataByTag.data_type:type_name -> tanlive.base.DataType
	58,  // 62: tanlive.tag.ReqDescribeSystemTags.Filter.taggable_type:type_name -> tanlive.tag.TaggableType
	57,  // 63: tanlive.tag.ReqDescribeSystemTags.Filter.type:type_name -> tanlive.tag.TagCreateType
	57,  // 64: tanlive.tag.ReqDescribeSystemTags.Filter.notify_type:type_name -> tanlive.tag.TagCreateType
	57,  // 65: tanlive.tag.RspDescribeSystemTags.TagInfo.type:type_name -> tanlive.tag.TagCreateType
	57,  // 66: tanlive.tag.RspDescribeSystemTags.TagInfo.notify_type:type_name -> tanlive.tag.TagCreateType
	70,  // 67: tanlive.tag.RspDescribeSystemTags.TagInfo.create_date:type_name -> google.protobuf.Timestamp
	70,  // 68: tanlive.tag.RspDescribeSystemTags.TagInfo.last_update_date:type_name -> google.protobuf.Timestamp
	58,  // 69: tanlive.tag.ReqDescribeSystemTagsByIndex.Filter.taggable_type:type_name -> tanlive.tag.TaggableType
	57,  // 70: tanlive.tag.ReqDescribeSystemTagsByIndex.Filter.type:type_name -> tanlive.tag.TagCreateType
	57,  // 71: tanlive.tag.ReqDescribeSystemTagsByIndex.Filter.notify_type:type_name -> tanlive.tag.TagCreateType
	71,  // 72: tanlive.tag.RspGetTagBinding.TagBindingEntry.value:type_name -> tanlive.tag.TagBinding
	68,  // 73: tanlive.tag.RspGetTagInfoByLanguage.TagsEntry.value:type_name -> tanlive.tag.ComTag
	68,  // 74: tanlive.tag.RspGetDataWithTagByTag.DataTag.tags:type_name -> tanlive.tag.ComTag
	0,   // 75: tanlive.tag.TagService.UpdateSystemTag:input_type -> tanlive.tag.ReqUpdateSystemTag
	1,   // 76: tanlive.tag.TagService.EditSystemTag:input_type -> tanlive.tag.ReqEditSystemTag
	2,   // 77: tanlive.tag.TagService.DeleteSystemTag:input_type -> tanlive.tag.ReqDeleteSystemTag
	3,   // 78: tanlive.tag.TagService.MergeSystemTags:input_type -> tanlive.tag.ReqMergeSystemTags
	4,   // 79: tanlive.tag.TagService.DescribeSystemTags:input_type -> tanlive.tag.ReqDescribeSystemTags
	6,   // 80: tanlive.tag.TagService.DescribeSystemTagsByIndex:input_type -> tanlive.tag.ReqDescribeSystemTagsByIndex
	8,   // 81: tanlive.tag.TagService.DescribeTeamTagBindingInfos:input_type -> tanlive.tag.ReqDescribeTeamTagBindingInfos
	10,  // 82: tanlive.tag.TagService.DescribeResourceTagBindingInfos:input_type -> tanlive.tag.ReqDescribeResourceTagBindingInfos
	12,  // 83: tanlive.tag.TagService.DescribeAtlasTagBindingInfos:input_type -> tanlive.tag.ReqDescribeAtlasTagBindingInfos
	14,  // 84: tanlive.tag.TagService.DescribeProductTagBindingInfos:input_type -> tanlive.tag.ReqDescribeProductTagBindingInfos
	16,  // 85: tanlive.tag.TagService.BatchImportSystemTag:input_type -> tanlive.tag.ReqBatchImportSystemTag
	17,  // 86: tanlive.tag.TagService.GetSystemTags:input_type -> tanlive.tag.ReqGetSystemTags
	19,  // 87: tanlive.tag.TagService.CreateUserTag:input_type -> tanlive.tag.ReqCreateUserTag
	21,  // 88: tanlive.tag.TagService.CreateTagBinding:input_type -> tanlive.tag.ReqCreateTagBinding
	22,  // 89: tanlive.tag.TagService.AutoBindingAITag:input_type -> tanlive.tag.ReqAutoBindingAITag
	23,  // 90: tanlive.tag.TagService.BindingTagChangeData:input_type -> tanlive.tag.ReqBindingTagChangeData
	24,  // 91: tanlive.tag.TagService.GetTagBinding:input_type -> tanlive.tag.ReqGetTagBinding
	26,  // 92: tanlive.tag.TagService.CleanTagBinding:input_type -> tanlive.tag.ReqCleanTagBinding
	27,  // 93: tanlive.tag.TagService.ApprovedDataTag:input_type -> tanlive.tag.ReqApprovedDataTag
	28,  // 94: tanlive.tag.TagService.GetDataBoundByTag:input_type -> tanlive.tag.ReqGetDataBoundByTag
	30,  // 95: tanlive.tag.TagService.GetTagInfo:input_type -> tanlive.tag.ReqGetTagInfo
	32,  // 96: tanlive.tag.TagService.GetTagInfoByLanguage:input_type -> tanlive.tag.ReqGetTagInfoByLanguage
	34,  // 97: tanlive.tag.TagService.SyncDataTag:input_type -> tanlive.tag.ReqSyncDataTag
	35,  // 98: tanlive.tag.TagService.GetAllTagInfoByData:input_type -> tanlive.tag.ReqGetAllTagInfoByData
	37,  // 99: tanlive.tag.TagService.GetDataWithTagByTag:input_type -> tanlive.tag.ReqGetDataWithTagByTag
	39,  // 100: tanlive.tag.TagService.GetDataByTag:input_type -> tanlive.tag.ReqGetDataByTag
	72,  // 101: tanlive.tag.TagService.UpdateSystemTag:output_type -> google.protobuf.Empty
	72,  // 102: tanlive.tag.TagService.EditSystemTag:output_type -> google.protobuf.Empty
	72,  // 103: tanlive.tag.TagService.DeleteSystemTag:output_type -> google.protobuf.Empty
	72,  // 104: tanlive.tag.TagService.MergeSystemTags:output_type -> google.protobuf.Empty
	5,   // 105: tanlive.tag.TagService.DescribeSystemTags:output_type -> tanlive.tag.RspDescribeSystemTags
	7,   // 106: tanlive.tag.TagService.DescribeSystemTagsByIndex:output_type -> tanlive.tag.RspDescribeSystemTagsByIndex
	9,   // 107: tanlive.tag.TagService.DescribeTeamTagBindingInfos:output_type -> tanlive.tag.RspDescribeTeamTagBindingInfos
	11,  // 108: tanlive.tag.TagService.DescribeResourceTagBindingInfos:output_type -> tanlive.tag.RspDescribeResourceTagBindingInfos
	13,  // 109: tanlive.tag.TagService.DescribeAtlasTagBindingInfos:output_type -> tanlive.tag.RspDescribeAtlasTagBindingInfos
	15,  // 110: tanlive.tag.TagService.DescribeProductTagBindingInfos:output_type -> tanlive.tag.RspDescribeProductTagBindingInfos
	72,  // 111: tanlive.tag.TagService.BatchImportSystemTag:output_type -> google.protobuf.Empty
	18,  // 112: tanlive.tag.TagService.GetSystemTags:output_type -> tanlive.tag.RspGetSystemTags
	20,  // 113: tanlive.tag.TagService.CreateUserTag:output_type -> tanlive.tag.RspCreateUserTag
	72,  // 114: tanlive.tag.TagService.CreateTagBinding:output_type -> google.protobuf.Empty
	72,  // 115: tanlive.tag.TagService.AutoBindingAITag:output_type -> google.protobuf.Empty
	72,  // 116: tanlive.tag.TagService.BindingTagChangeData:output_type -> google.protobuf.Empty
	25,  // 117: tanlive.tag.TagService.GetTagBinding:output_type -> tanlive.tag.RspGetTagBinding
	72,  // 118: tanlive.tag.TagService.CleanTagBinding:output_type -> google.protobuf.Empty
	72,  // 119: tanlive.tag.TagService.ApprovedDataTag:output_type -> google.protobuf.Empty
	29,  // 120: tanlive.tag.TagService.GetDataBoundByTag:output_type -> tanlive.tag.RspGetDataBoundByTag
	31,  // 121: tanlive.tag.TagService.GetTagInfo:output_type -> tanlive.tag.RspGetTagInfo
	33,  // 122: tanlive.tag.TagService.GetTagInfoByLanguage:output_type -> tanlive.tag.RspGetTagInfoByLanguage
	72,  // 123: tanlive.tag.TagService.SyncDataTag:output_type -> google.protobuf.Empty
	36,  // 124: tanlive.tag.TagService.GetAllTagInfoByData:output_type -> tanlive.tag.RspGetAllTagInfoByData
	38,  // 125: tanlive.tag.TagService.GetDataWithTagByTag:output_type -> tanlive.tag.RspGetDataWithTagByTag
	40,  // 126: tanlive.tag.TagService.GetDataByTag:output_type -> tanlive.tag.RspGetDataByTag
	101, // [101:127] is the sub-list for method output_type
	75,  // [75:101] is the sub-list for method input_type
	75,  // [75:75] is the sub-list for extension type_name
	75,  // [75:75] is the sub-list for extension extendee
	0,   // [0:75] is the sub-list for field type_name
}

func init() { file_tanlive_tag_service_proto_init() }
func file_tanlive_tag_service_proto_init() {
	if File_tanlive_tag_service_proto != nil {
		return
	}
	file_tanlive_tag_tag_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_tag_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqUpdateSystemTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqEditSystemTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDeleteSystemTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqMergeSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSystemTagsByIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSystemTagsByIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeTeamTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeTeamTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeResourceTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeResourceTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeAtlasTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeAtlasTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeProductTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeProductTagBindingInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBatchImportSystemTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetSystemTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateUserTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspCreateUserTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCreateTagBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqAutoBindingAITag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBindingTagChangeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTagBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTagBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqCleanTagBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqApprovedDataTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetDataBoundByTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetDataBoundByTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTagInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTagInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetTagInfoByLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetTagInfoByLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqSyncDataTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetAllTagInfoByData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetAllTagInfoByData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetDataWithTagByTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetDataWithTagByTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqGetDataByTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetDataByTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSystemTags_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeSystemTags_TagInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeSystemTagsByIndex_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeTeamTagBindingInfos_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeTeamTagBindingInfos_BindingObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeResourceTagBindingInfos_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeResourceTagBindingInfos_BindingObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeAtlasTagBindingInfos_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeAtlasTagBindingInfos_BindingObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqDescribeProductTagBindingInfos_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspDescribeProductTagBindingInfos_BindingObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBatchImportSystemTag_TagIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetSystemTags_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_tag_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspGetDataWithTagByTag_DataTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_tag_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   57,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanlive_tag_service_proto_goTypes,
		DependencyIndexes: file_tanlive_tag_service_proto_depIdxs,
		MessageInfos:      file_tanlive_tag_service_proto_msgTypes,
	}.Build()
	File_tanlive_tag_service_proto = out.File
	file_tanlive_tag_service_proto_rawDesc = nil
	file_tanlive_tag_service_proto_goTypes = nil
	file_tanlive_tag_service_proto_depIdxs = nil
}
