syntax = "proto3";

package tanlive.tag;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/tag";

import "google/protobuf/timestamp.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/tag/tag.proto";
import "tanlive/options.proto";
import "google/protobuf/empty.proto";

message ReqUpdateSystemTag {
  // 标签id
  uint64 tag_id = 1 ;
  // 标签权重
  uint32 weight = 2;
  // 标签类型（多语言选项或注册用） 1 系统 ；2 自创
  TagCreateType type = 3;
  // 标签索引类型
  TaggableType taggable_type = 4;
  // 定向推送标签类型（我来自标签时生效） 1 系统 ；2 自创
  TagCreateType notify_type = 5;
  uint64 user_id = 6;
}

message ReqEditSystemTag {
  // 标签索引id 为0 则创建，否则更新对应数据
  uint64 index_id = 1 ;
  // 标签索引名称
  string index_name = 2 ;
  // 中文标签信息
  Tag zh_tag = 3 ;
  // 英文标签信息
  Tag en_tag = 4 ;
  // 标签索引类型
  TaggableType taggable_type = 5;
  uint64 user_id = 6;
}

message ReqDeleteSystemTag {
  // 标签索引数组，会删除索引下的所有标签
  repeated uint64 tag_index_ids = 1;
  // 标签索引类型
  TaggableType taggable_type = 2 [(tanlive.validator) = "required"];
  TagActionType action_type = 3;
  repeated uint64 assistant_ids = 4;
}

message ReqMergeSystemTags {
  // 合并后的标签索引id
  uint64 index_id = 1;
  // 需要合并标签索引数组
  repeated uint64 merge_index_ids = 2;
  // 中文标签信息
  Tag zh_tag = 3;
  // 英文标签信息
  Tag en_tag = 4;
  // 合并的标签类型
  TaggableType taggable_type = 5;
  uint64 user_id = 6;
}

message ReqDescribeSystemTags {
  message Filter {
    // 标签名称
    string tag_name = 1;
    // 标签索引类型
    TaggableType taggable_type = 2 ;
    // 标签id
    repeated uint64 tag_ids = 3;
    // 语言类型
    string language = 4;
    // 标签类型（多语言选项或注册用） 1 系统 ；2 自创
    TagCreateType type = 5 ;
    // 定向推送标签类型（我来自标签时） 1 系统 ；2 自创
    TagCreateType notify_type = 6 ;
  }
  uint32 offset = 1;
  uint32 limit = 2;
  repeated tanlive.base.OrderBy order_by = 3;
  Filter filter = 4;
}

message RspDescribeSystemTags {
  repeated TagInfo tag_set = 1;
  uint32 total_count = 2;
  message TagInfo {
    // 标签id
    uint64 tag_id = 1;
    // 标签名称
    string tag_name = 2;
    // 语言类型
    string language = 3;
    // 是否tmt
    bool is_tmt = 4;
    // 引用数量
    uint32 citation_num = 5;
    // 标签权重
    uint32 weight = 6;
    // 标签类型（多语言选项或注册用） 1 系统 ；2 自创
    TagCreateType type = 7;
    // 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创
    TagCreateType notify_type = 8;
    uint64 create_by = 9;
    google.protobuf.Timestamp create_date = 10;
    uint64 last_update_by = 11;
    google.protobuf.Timestamp last_update_date = 12;
    // 标签索引名称
    string index_name = 13;
    // 索引id
    uint64 index_id = 14;
  }
}

message ReqDescribeSystemTagsByIndex {
  message Filter {
    // 标签名称
    string tag_name = 1;
    // 标签索引类型
    TaggableType taggable_type = 2 ;
    // 标签索引id
    repeated uint64 index_ids = 3;
    // 语言类型
    string language = 4;
    // 标签类型（多语言选项或注册用） 1 系统 ；2 自创
    TagCreateType type = 5 ;
    // 定向推送标签类型（我来自标签时） 1 系统 ；2 自创
    TagCreateType notify_type = 6 ;
  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
  tanlive.base.OrderBy order_by = 4;
}

message RspDescribeSystemTagsByIndex {
  repeated TagIndex tag_set = 1;
  uint32 total_count = 2;
}

// ReqDescribeTeamTagBindingInfos 查询团队类型标签关联详情
message ReqDescribeTeamTagBindingInfos {
  message Filter {
    uint64 tag_id = 1;
  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
}

// RspDescribeTeamTagBindingInfos 查询团队类型标签关联详情回复
message RspDescribeTeamTagBindingInfos {
  message BindingObject {
    uint64 id = 1;
    // 团队id
    uint64 team_id = 2;
    // 团队名称
    string team_name = 3;
    // 团队简称
    string team_short_name = 4;
    // 创建时间
    string create_date = 5;
    // 团队属性
    string team_nature = 6;
    // 认证状态
    string verify_status = 7;
    // 团队持有人id
    uint64 holder_id = 8;
    // 团队持有人
    string holder_name = 9;
    // 团队类型
    repeated string team_types = 10;
    // 贡献者
    repeated string contributors = 11;
  }
  repeated BindingObject binding_objects = 1;
  uint32 total_count = 2;

}

// ReqDescribeResourceTagBindingInfos 查询资源相关标签关联详情
message ReqDescribeResourceTagBindingInfos {
  message Filter {
    uint64 tag_id = 1;

  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
}

// RspDescribeResourceTagBindingInfos 查询资源相关标签关联详情回复
message RspDescribeResourceTagBindingInfos {
  message BindingObject {
    uint64 id = 1;
    // 资源标题
    string res_name = 2;
    // 资源简述
    string res_introduction = 3;
    // 资源类型
    repeated string res_types = 4;
    // 创建时间
    string create_date = 5;
    // 主办方
    repeated string originator_name = 6;
    // 资源id
    uint64 res_id = 7;
  }
  repeated BindingObject binding_objects = 1;
  uint32 total_count = 2;
}

message ReqDescribeAtlasTagBindingInfos {
  message Filter {
    uint64 tag_id = 1;

  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
}

message RspDescribeAtlasTagBindingInfos {
  message BindingObject {
    uint64 id = 1;
    // 标题
    string atlas_name = 2;
    // 简述
    string atlas_introduction = 3;
    // 图谱类型
    repeated string atlas_types = 4;
    // 创建时间
    string create_date = 5;
    // 发布方
    repeated string pub_name = 6;
    // 图谱id
    uint64 atlas_id = 7;
  }
  repeated BindingObject binding_objects = 1;
  uint32 total_count = 2;
}


// ReqDescribeProductTagBindingInfos 查询产品技术-面向用户标签关联详情
message ReqDescribeProductTagBindingInfos {
  message Filter {
    uint64 tag_id = 1;

  }
  uint32 offset = 1;
  uint32 limit = 2;
  Filter filter = 3;
}

// RspDescribeProductTagBindingInfos 查询产品技术-面向用户类型标签关联详情回复
message RspDescribeProductTagBindingInfos {
  message BindingObject {
    uint64 id = 1;
    // 产品名称
    string product_name = 2;
    // 一句话简介
    string brief_intro = 3;
    // 产品类型
    repeated string product_types = 4;
    // 团队简称
    string team_short_name = 5;
    // 创建时间
    string create_date = 6;
    // 产品id
    uint64 product_id = 7;
  }
  repeated BindingObject binding_objects = 1;
  uint32 total_count = 2;
}

message ReqBatchImportSystemTag {
  //导入标签索引数组
  repeated TagIndex tag_index = 1 ;
  // 标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1
  TagCreateType type = 2;
  // 定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1
  TagCreateType notify_type = 3;
  // 标签类型
  TaggableType taggable_type = 4 ;
  // 备注
  string remarks = 5 ;
  uint64 user_id = 6 ;
  message TagIndex {
    //索引名称
    string index_name = 1 ;
    //标签名称
    string tag_name = 2 ;
//    //索引名称
//    string index_name = 1;
//    //英文标签名称
//    string en_tag_name = 2;
//    //中文标签名称
//    string zh_tag_name = 3;
  }
}

message ReqGetSystemTags{
  // 标签索引类型
  TaggableType taggable_type = 1 ;
  // 语言类型zh、en
  string language = 2 ;
  // 排序
  repeated tanlive.base.OrderBy order_by = 3;
  // 定向推送用的筛选条件 1 系统标签
  TagCreateType notify_type = 4;
  // 标签名称模糊搜索
  string tag_name = 5;
  // 是否直接显示标签id，不会被编码为hashId
  bool is_direct_display_id = 6;
  // 是否包含 助手用户标签
  bool with_assistant_user_tag = 7;
  repeated uint64 tag_ids = 8;
}

message RspGetSystemTags{
  repeated Tag tag_set = 1;
  message Tag {
    // 标签id
    uint64 id = 1;
    // 标签名称
    string name = 2;
    // 直接显示的标签id,is_direct_display_id为false则默认为0
    uint64 direct_display_id = 6;
  }
}

message ReqCreateUserTag {
  uint64 create_by = 1;
  repeated CreateTag tags = 2;
}

message RspCreateUserTag {
  repeated uint64 tag_ids = 1;
}

message ReqCreateTagBinding {
  uint64 user_id = 1 [(tanlive.validator) = "required"];
  DataInfo data_info = 2 [(tanlive.validator) = "required"];
  // 绑定现有的标签
  repeated uint64 tag_ids = 3;
  // 新增的自创标签
  repeated string tag_names = 4;
  TaggableType taggable_type = 5 [(tanlive.validator) = "required"];
  TaggableActionType action = 6 [(tanlive.validator) = "required"];
}

message ReqAutoBindingAITag {
  uint64 user_id = 1 ;
  repeated DataInfo data_info = 2 [(tanlive.validator) = "required"];
  LabelInfo label_info = 3 [(tanlive.validator) = "required"];
  bool unbind = 4 ;
}

message ReqBindingTagChangeData {
  uint64 old = 1 [(tanlive.validator) = "required"];
  // 从old 迁移到 new上
  uint64 new = 2 [(tanlive.validator) = "required"];
  base.DataType data_type = 3 [(tanlive.validator) = "required"];
}

message ReqGetTagBinding {
  repeated uint64 data_ids = 1;
  tanlive.base.DataType data_type = 2;
  // 标签类型：重要！如果要查询是因为助手自动给用户打标的标签，一定要增加 TAGGABLE_TYPE_AI_ASSISTANT 这个类型
  repeated TaggableType taggable_types = 3 ;
  // 语言类型 en zh
  string language = 4;
}

message RspGetTagBinding {
  // key:data_id
  map<uint64,TagBinding> tag_binding = 1;
}

message ReqCleanTagBinding {
  repeated uint64 data_ids = 1;
  tanlive.base.DataType data_type = 2;
}

message ReqApprovedDataTag {
  uint64 data_id = 1;
  tanlive.base.DataType data_type = 2;
  uint64 user_id = 3;
  // 语言类型 en zh
  string language = 4;
}

message ReqGetDataBoundByTag {
  repeated uint64 tag_ids = 1;
  tanlive.base.DataType data_type = 2;
  // 标签名称（标签名称和标签类型必须共存，名称和tag_id 取交集）
  repeated string tag_names = 3;
  // 标签类型
  repeated TaggableType taggable_types = 4 ;
  uint32 offset = 5;
  // 如果limit= 0 则是查询查询全量, 最大 200
  uint32 limit = 6;
  // 标签创建类型（多语言选项或注册用） 1 系统 ；2 自创，默认0
  TagCreateType type = 7;
  // 定向推送创建标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认0
  TagCreateType notify_type = 8;
  // 是否多语言查询,为true时 会匹配出tag_ids的多语言数据
  bool multilingual = 9;
  // 是否对传的标签ID 只返回绑定了所有tag_ids的data_ids
  bool tag_id_intersection = 10;
  TaggableActionType action_type = 11;
}

message RspGetDataBoundByTag {
  repeated uint64 data_ids = 1;
  uint32 total_count = 2;
}

message ReqGetTagInfo {
  repeated uint64 tag_ids = 1;
}

message RspGetTagInfo {
  repeated ComTag tags =1;
}

message ReqGetTagInfoByLanguage {
  repeated uint64 tag_ids = 1;
  string language = 2;
  bool with_editing = 3; // true 则会在显示自定义标签时忽略language
}

message RspGetTagInfoByLanguage {
  // key 原tagID  value 多语言标签信息
  map<uint64,ComTag> tags = 1;
}

message ReqSyncDataTag {
  // ugc id
  uint64 data_id = 1;
  // 同步数据更新人 userid
  uint64 update_by = 2;
  // 同步的全量标签id
  repeated uint64 tag_ids = 3;
  tanlive.base.DataType data_type = 4;
}

message ReqGetAllTagInfoByData {
  // ugc id
  uint64 data_id = 1;
  // ugc type
  tanlive.base.DataType data_type = 2;
  // union_data_ids 不为空时，会返回对应ugc绑定的所有指定类型的标签，标签类型为空时则返回全部标签，返回之前会取标签结果并集
  repeated uint64 union_data_ids = 3;
  // 标签类型 如果为空则获取ugc绑定的所有标签信息
  repeated TaggableType taggable_types = 4;
}

message RspGetAllTagInfoByData {
  repeated ComTag tags = 1;
}

message ReqGetDataWithTagByTag {
  repeated uint64 tag_ids = 1;
  // ugc type
  tanlive.base.DataType data_type = 2;
  // 标签类型，指定返回ugc绑定的标签类型
  repeated TaggableType taggable_types = 3 ;
  // 是否多语言查询,true 会根据tagIds标签多语言数据匹配ugc；false 只会匹配与tagIds关联的ugc
  bool multilingual = 4;
  // 指定返回ugc绑定的标签的语言类型
  string language = 5;
}

message RspGetDataWithTagByTag {
  repeated DataTag data_tags = 1;
  message DataTag {
    // ugc id
    uint64 data_id = 1;
    // tag_id
    repeated ComTag tags = 2;
  }
}

message ReqGetDataByTag {
  tanlive.tag.TaggableType taggable_type = 1 [(tanlive.validator) = "required"];
  repeated uint64 tag_ids = 2 [(tanlive.validator) = "required"];
  base.Paginator page = 3;
  base.DataType data_type = 4 [(tanlive.validator) = "required"];
}

message RspGetDataByTag {
  repeated uint64 ids = 1;
}

// 标签服务
service TagService {

  // 更新系统标签
  rpc UpdateSystemTag(ReqUpdateSystemTag) returns (google.protobuf.Empty);

  // 编辑系统标签
  rpc EditSystemTag(ReqEditSystemTag) returns (google.protobuf.Empty);

  // 删除系统标签
  rpc DeleteSystemTag(ReqDeleteSystemTag) returns (google.protobuf.Empty);

  // 合并系统标签
  rpc MergeSystemTags(ReqMergeSystemTags) returns (google.protobuf.Empty);

  // 获取系统标签列表
  rpc DescribeSystemTags(ReqDescribeSystemTags) returns (RspDescribeSystemTags);

  // 获取系统索引标签列表
  rpc DescribeSystemTagsByIndex(ReqDescribeSystemTagsByIndex) returns (RspDescribeSystemTagsByIndex);

  // 查询团队标签关联详情
  rpc DescribeTeamTagBindingInfos(ReqDescribeTeamTagBindingInfos) returns (RspDescribeTeamTagBindingInfos);

  // 查询资源标签关联详情
  rpc DescribeResourceTagBindingInfos(ReqDescribeResourceTagBindingInfos) returns (RspDescribeResourceTagBindingInfos);

  // 查询图谱标签关联详情
  rpc DescribeAtlasTagBindingInfos(ReqDescribeAtlasTagBindingInfos) returns (RspDescribeAtlasTagBindingInfos);

  // 查询产品标签关联详情
  rpc DescribeProductTagBindingInfos(ReqDescribeProductTagBindingInfos) returns (RspDescribeProductTagBindingInfos);

  // 批量导入标签
  rpc BatchImportSystemTag(ReqBatchImportSystemTag) returns (google.protobuf.Empty);

  // 获取全量系统标签
  rpc GetSystemTags(ReqGetSystemTags) returns (RspGetSystemTags);

  // 创建用户自创标签
  rpc CreateUserTag(ReqCreateUserTag) returns (RspCreateUserTag);

  // 创建标签绑定，适用于不需要审核的场景
  rpc CreateTagBinding(ReqCreateTagBinding) returns (google.protobuf.Empty);

  // 自动绑定标签
  rpc AutoBindingAITag(ReqAutoBindingAITag) returns (google.protobuf.Empty);

  // 切换标签的绑定者，目前适用:从小程序虚拟用户切换为正式用户
  rpc BindingTagChangeData(ReqBindingTagChangeData) returns (google.protobuf.Empty);

  // 获取标签绑定
  rpc GetTagBinding(ReqGetTagBinding) returns (RspGetTagBinding);

  // 清除标签绑定
  rpc CleanTagBinding(ReqCleanTagBinding) returns (google.protobuf.Empty);

  // 数据标签审核通过
  rpc ApprovedDataTag(ReqApprovedDataTag) returns (google.protobuf.Empty);

  // 获取被标签绑定的数据（返回各个标签绑定ugc的并集）
  rpc GetDataBoundByTag(ReqGetDataBoundByTag) returns (RspGetDataBoundByTag);

  // 获取标签信息
  rpc GetTagInfo(ReqGetTagInfo) returns (RspGetTagInfo);

  // 获取标签多语言信息
  rpc GetTagInfoByLanguage(ReqGetTagInfoByLanguage) returns (RspGetTagInfoByLanguage);

  // 同步ugc内容绑定的标签信息
  rpc SyncDataTag(ReqSyncDataTag) returns (google.protobuf.Empty);

  // 获取ugc数据绑定的全语言标签信息
  rpc GetAllTagInfoByData(ReqGetAllTagInfoByData) returns (RspGetAllTagInfoByData);

  // 获取通过标签获取数据集标签信息，运用于首页标签列表查询关联已上线的ugc的标签
  rpc GetDataWithTagByTag(ReqGetDataWithTagByTag) returns (RspGetDataWithTagByTag);

  // 获取被用户标签绑定的数据信息
  rpc GetDataByTag(ReqGetDataByTag) returns (RspGetDataByTag);
}
