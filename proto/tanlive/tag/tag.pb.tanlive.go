// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package tag

import (
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func (x *TagInfo) MaskInLog() any {
	if x == nil {
		return (*TagInfo)(nil)
	}

	y := proto.Clone(x).(*TagInfo)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TagInfo) MaskInRpc() any {
	if x == nil {
		return (*TagInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TagInfo) MaskInBff() any {
	if x == nil {
		return (*TagInfo)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TagIndex) MaskInLog() any {
	if x == nil {
		return (*TagIndex)(nil)
	}

	y := proto.Clone(x).(*TagIndex)
	if v, ok := any(y.ZhTag).(interface{ MaskInLog() any }); ok {
		y.ZhTag = v.MaskInLog().(*TagInfo)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInLog() any }); ok {
		y.EnTag = v.MaskInLog().(*TagInfo)
	}

	return y
}

func (x *TagIndex) MaskInRpc() any {
	if x == nil {
		return (*TagIndex)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInRpc() any }); ok {
		y.ZhTag = v.MaskInRpc().(*TagInfo)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInRpc() any }); ok {
		y.EnTag = v.MaskInRpc().(*TagInfo)
	}

	return y
}

func (x *TagIndex) MaskInBff() any {
	if x == nil {
		return (*TagIndex)(nil)
	}

	y := x
	if v, ok := any(y.ZhTag).(interface{ MaskInBff() any }); ok {
		y.ZhTag = v.MaskInBff().(*TagInfo)
	}
	if v, ok := any(y.EnTag).(interface{ MaskInBff() any }); ok {
		y.EnTag = v.MaskInBff().(*TagInfo)
	}

	return y
}

func (x *TagBinding) MaskInLog() any {
	if x == nil {
		return (*TagBinding)(nil)
	}

	y := proto.Clone(x).(*TagBinding)
	for k, v := range y.TagBindingMap {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TagBindingMap[k] = vv.MaskInLog().(*BindingTag)
		}
	}

	return y
}

func (x *TagBinding) MaskInRpc() any {
	if x == nil {
		return (*TagBinding)(nil)
	}

	y := x
	for k, v := range y.TagBindingMap {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TagBindingMap[k] = vv.MaskInRpc().(*BindingTag)
		}
	}

	return y
}

func (x *TagBinding) MaskInBff() any {
	if x == nil {
		return (*TagBinding)(nil)
	}

	y := x
	for k, v := range y.TagBindingMap {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TagBindingMap[k] = vv.MaskInBff().(*BindingTag)
		}
	}

	return y
}

func (x *BindingTag) MaskInLog() any {
	if x == nil {
		return (*BindingTag)(nil)
	}

	y := proto.Clone(x).(*BindingTag)
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tags[k] = vv.MaskInLog().(*BindingTag_Tag)
		}
	}

	return y
}

func (x *BindingTag) MaskInRpc() any {
	if x == nil {
		return (*BindingTag)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tags[k] = vv.MaskInRpc().(*BindingTag_Tag)
		}
	}

	return y
}

func (x *BindingTag) MaskInBff() any {
	if x == nil {
		return (*BindingTag)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tags[k] = vv.MaskInBff().(*BindingTag_Tag)
		}
	}

	return y
}

func (x *TagInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TagIndex) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ZhTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EnTag).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TagBinding) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TagBindingMap {
		if sanitizer, ok := any(x.TagBindingMap[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *BindingTag) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tags {
		if sanitizer, ok := any(x.Tags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
